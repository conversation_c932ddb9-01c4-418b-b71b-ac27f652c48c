import React, { useState, useEffect } from 'react';
import {
  VideoScriptRequest,
  VideoScriptResponse,
  VideoType,
  VideoPurpose,
  VideoTargetAudience,
  VideoPlatform,
  SavedVideoScript
} from '../types/videoScript';
import { VideoScriptFormData, addVideoScriptForm } from '../types/videoScriptForm';
import { generateVideoScript } from '../services/videoScriptService';
import { saveVideoScript, updateSavedVideoScript } from '../services/savedVideoScriptService';
import { fetchProducts } from '../services/productService';
import { Product } from '../types';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './ui/select';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import {
  Loader2,
  FileText,
  Download,
  Edit,
  Copy,
  Save,
  Check,
  Search,
  Database,
  PenLine
} from 'lucide-react';
import { useToast } from './ui/use-toast';
import jsPDF from 'jspdf';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "../components/ui/command";

interface VideoScriptGeneratorProps {
  initialScript?: SavedVideoScript;
  relatedContentId?: string;
  videoType?: VideoType;
  purpose?: VideoPurpose;
  targetAudience?: VideoTargetAudience;
  platform?: VideoPlatform;
  productName?: string;
  additionalInfo?: string;
  onScriptSaved?: (script: SavedVideoScript) => void;
  onScriptUpdated?: (script: SavedVideoScript) => void;
  onResetEditor?: () => void;
}

const VideoScriptGenerator: React.FC<VideoScriptGeneratorProps> = ({
  initialScript,
  relatedContentId,
  videoType: initialVideoType,
  purpose: initialPurpose,
  targetAudience: initialTargetAudience,
  platform: initialPlatform,
  productName: initialProductName,
  additionalInfo: initialAdditionalInfo,
  onScriptSaved,
  onScriptUpdated,
  onResetEditor
}) => {
  const [videoType, setVideoType] = useState<VideoType>(initialVideoType || VideoType.MARKETING_PRODUCT);
  const [purpose, setPurpose] = useState<VideoPurpose>(initialPurpose || VideoPurpose.PROMOTION);
  const [targetAudience, setTargetAudience] = useState<VideoTargetAudience>(initialTargetAudience || VideoTargetAudience.CUSTOMER);
  const [platform, setPlatform] = useState<VideoPlatform>(initialPlatform || VideoPlatform.INSTAGRAM);
  const [productName, setProductName] = useState<string>(initialProductName || '');
  const [duration, setDuration] = useState<string>('');
  const [additionalInfo, setAdditionalInfo] = useState<string>(initialAdditionalInfo || '');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [scriptResponse, setScriptResponse] = useState<VideoScriptResponse | null>(null);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editedScript, setEditedScript] = useState<VideoScriptResponse | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [savedScript, setSavedScript] = useState<SavedVideoScript | null>(null);

  // Product lookup state
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState<boolean>(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [isProductPopoverOpen, setIsProductPopoverOpen] = useState<boolean>(false);
  const [productInputMode, setProductInputMode] = useState<'manual' | 'lookup'>('manual');

  const { toast } = useToast();

  // Initialize form with initial script if provided
  useEffect(() => {
    if (initialScript) {
      setVideoType(initialScript.videoType);
      setPurpose(initialScript.purpose);
      setTargetAudience(initialScript.targetAudience);
      setPlatform(initialScript.platform);
      setProductName(initialScript.productName || '');
      setDuration(initialScript.durationInSeconds ? initialScript.durationInSeconds.toString() : '');
      setScriptResponse(initialScript);
      setEditedScript(initialScript);
      setSavedScript(initialScript);
    }
  }, [initialScript]);

  // Initialize form with props if provided
  useEffect(() => {
    if (!initialScript) {
      if (initialVideoType) setVideoType(initialVideoType);
      if (initialPurpose) setPurpose(initialPurpose);
      if (initialTargetAudience) setTargetAudience(initialTargetAudience);
      if (initialPlatform) setPlatform(initialPlatform);
      if (initialProductName) setProductName(initialProductName);
      if (initialAdditionalInfo) setAdditionalInfo(initialAdditionalInfo);
    }
  }, [initialVideoType, initialPurpose, initialTargetAudience, initialPlatform, initialProductName, initialAdditionalInfo, initialScript]);

  // Fetch products for lookup
  useEffect(() => {
    const loadProducts = async () => {
      try {
        // Only load products if we don't already have them
        if (products.length === 0) {
          setIsLoadingProducts(true);
          console.log('Loading products from storage...');
          const productData = await fetchProducts();
          setProducts(productData);
          console.log(`Loaded ${productData.length} products`);
        }
      } catch (error) {
        console.error('Error loading products:', error);
        toast({
          title: 'Error',
          description: 'Gagal memuat data produk',
          variant: 'destructive',
        });
      } finally {
        setIsLoadingProducts(false);
      }
    };

    loadProducts();

    // Add click outside handler to close dropdown
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      // Check if the click is outside the dropdown
      if (isProductPopoverOpen && !target.closest('.product-dropdown')) {
        setIsProductPopoverOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [toast, products.length, isProductPopoverOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setScriptResponse(null);
    setEditedScript(null);
    setIsEditing(false);
    setSavedScript(null);

    try {
      const request: VideoScriptRequest = {
        videoType,
        purpose,
        targetAudience,
        platform,
        productName: productName || undefined,
        duration: duration ? parseInt(duration) : undefined,
        additionalInfo: additionalInfo || undefined
      };

      const response = await generateVideoScript(request);
      setScriptResponse(response);
      setEditedScript(response);

      toast({
        title: 'Script berhasil dibuat',
        description: 'Script video telah berhasil dibuat',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error generating script:', error);
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat membuat script video',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle save script
  const handleSaveScript = () => {
    if (!scriptResponse) return;

    // Set saving state immediately for UI feedback
    setIsSaving(true);

    // Show toast immediately for better user experience
    toast({
      title: 'Menyimpan script...',
      description: 'Script video sedang disimpan',
      variant: 'default',
    });

    try {
      const request: VideoScriptRequest = {
        videoType,
        purpose,
        targetAudience,
        platform,
        productName: productName || undefined,
        duration: duration ? parseInt(duration) : undefined,
        additionalInfo: additionalInfo || undefined
      };

      // If we're editing an existing script, update it
      if (savedScript) {
        const updatedScript = updateSavedVideoScript({
          ...savedScript,
          ...scriptResponse,
          videoType,
          purpose,
          targetAudience,
          platform,
          productName: productName || undefined,
          durationInSeconds: duration ? parseInt(duration) : undefined,
        });

        // Update state immediately
        setSavedScript(updatedScript);

        // Show success toast after a short delay to ensure UI feels responsive
        setTimeout(() => {
          toast({
            title: 'Script berhasil diperbarui',
            description: 'Script video telah berhasil diperbarui',
            variant: 'default',
          });

          if (onScriptUpdated) {
            onScriptUpdated(updatedScript);
          }

          setIsSaving(false);
        }, 300);
      } else {
        // Otherwise, save a new script
        const newScript = saveVideoScript(scriptResponse, request, relatedContentId);

        // Also save the form data for lightweight storage
        const formData: Omit<VideoScriptFormData, 'id' | 'createdAt'> = {
          title: scriptResponse.title,
          videoType,
          purpose,
          targetAudience,
          platform,
          productName: productName || undefined,
          duration: duration || undefined,
          additionalInfo: additionalInfo || undefined
        };
        addVideoScriptForm(formData);

        // Update state immediately
        setSavedScript(newScript);

        // Show success toast after a short delay to ensure UI feels responsive
        setTimeout(() => {
          toast({
            title: 'Script berhasil disimpan',
            description: 'Script video telah berhasil disimpan',
            variant: 'default',
          });

          if (onScriptSaved) {
            // Pastikan callback ini dijalankan dengan benar
            try {
              onScriptSaved(newScript);
              console.log('Script saved callback executed successfully');
            } catch (callbackError) {
              console.error('Error in onScriptSaved callback:', callbackError);
            }
          } else {
            console.log('No onScriptSaved callback provided');
          }

          setIsSaving(false);
        }, 300);
      }
    } catch (error) {
      console.error('Error saving script:', error);
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat menyimpan script video',
        variant: 'destructive',
      });
      setIsSaving(false);
    }
  };

  // Reset the editor
  // Handle product selection from lookup
  const handleProductSelect = (value: string) => {
    // Find the product that matches the selected value
    const selectedProductId = value.split(' ')[0]; // Extract the product ID from the value
    const product = products.find(p => p.oldMaterialNo === selectedProductId);

    if (product) {
      setSelectedProduct(product);
      setProductName(product.materialDescription);
      setIsProductPopoverOpen(false);

      // Add product details to additional info if it's empty
      if (!additionalInfo) {
        const productDetails = `Produk: ${product.materialDescription}\nKode: ${product.oldMaterialNo}\nHarga: ${product.price.toLocaleString('id-ID')} IDR`;
        setAdditionalInfo(productDetails);
      }

      toast({
        title: 'Produk dipilih',
        description: `${product.materialDescription} telah dipilih`,
        variant: 'default',
      });
    } else {
      console.error('Product not found for value:', value);
    }
  };

  // Toggle between manual input and product lookup
  const toggleProductInputMode = () => {
    const newMode = productInputMode === 'manual' ? 'lookup' : 'manual';
    setProductInputMode(newMode);

    if (newMode === 'lookup') {
      // When switching to lookup mode, open the popover
      setTimeout(() => {
        setIsProductPopoverOpen(true);
      }, 100);

      // Load products if not already loaded
      if (products.length === 0 && !isLoadingProducts) {
        fetchProducts().then(data => {
          setProducts(data);
        }).catch(error => {
          console.error('Error loading products:', error);
          toast({
            title: 'Error',
            description: 'Gagal memuat data produk',
            variant: 'destructive',
          });
        });
      }
    } else {
      // When switching to manual, clear selected product but keep the name
      setSelectedProduct(null);
      setIsProductPopoverOpen(false);
    }
  };

  const handleReset = () => {
    // Reset form fields to default values
    setVideoType(VideoType.MARKETING_PRODUCT);
    setPurpose(VideoPurpose.PROMOTION);
    setTargetAudience(VideoTargetAudience.CUSTOMER);
    setPlatform(VideoPlatform.INSTAGRAM);
    setProductName('');
    setDuration('');
    setAdditionalInfo('');

    // Reset product selection
    setSelectedProduct(null);
    setProductInputMode('manual');

    // Reset script state
    setScriptResponse(null);
    setEditedScript(null);
    setIsEditing(false);
    setSavedScript(null);
    setIsSaving(false);

    // Notify parent component
    if (onResetEditor) {
      try {
        onResetEditor();
        console.log('Reset editor callback executed successfully');
      } catch (error) {
        console.error('Error in onResetEditor callback:', error);
      }
    }

    // Show toast notification
    toast({
      title: 'Editor direset',
      description: 'Form telah dikembalikan ke nilai default',
      variant: 'default',
    });
  };

  const handleEditScript = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    if (editedScript) {
      setScriptResponse(editedScript);
      setIsEditing(false);

      toast({
        title: 'Script berhasil disimpan',
        description: 'Perubahan pada script telah disimpan',
        variant: 'default',
      });
    }
  };

  const handleCancelEdit = () => {
    setEditedScript(scriptResponse);
    setIsEditing(false);
  };

  const handleCopyToClipboard = () => {
    if (!scriptResponse) return;

    // Create a more detailed script text with timeline
    let scriptText = `
JUDUL: ${scriptResponse.title}

`;

    // Add sections with timeline
    scriptResponse.sections.forEach(section => {
      scriptText += `${section.name.toUpperCase()} (${section.duration}):\n${section.content}\n\n`;

      // Add timeline for this section if available
      if (section.timeline && section.timeline.length > 0) {
        scriptText += `TIMELINE DETAIL:\n`;
        section.timeline.forEach(segment => {
          scriptText += `[${segment.startTime} - ${segment.endTime}] ${segment.shotType}\n`;

          if (segment.dialogue) {
            scriptText += `  DIALOG (${segment.dialogue.speaker}): "${segment.dialogue.text}"\n`;
            if (segment.dialogue.tone) {
              scriptText += `  TONE: ${segment.dialogue.tone}\n`;
            }
          }

          scriptText += `  VISUAL: ${segment.visual.description}\n`;

          if (segment.music) {
            scriptText += `  MUSIK: ${segment.music.type} - ${segment.music.description}\n`;
          }

          if (segment.location) {
            scriptText += `  LOKASI: ${segment.location}\n`;
          }

          if (segment.visualObjects && segment.visualObjects.length > 0) {
            scriptText += `  OBJEK: ${segment.visualObjects.join(', ')}\n`;
          }

          if (segment.notes) {
            scriptText += `  CATATAN: ${segment.notes}\n`;
          }

          scriptText += `\n`;
        });
      }
    });

    // Add overall timeline if available
    if (scriptResponse.timeline && scriptResponse.timeline.length > 0) {
      scriptText += `TIMELINE KESELURUHAN:\n`;
      scriptResponse.timeline.forEach(segment => {
        scriptText += `[${segment.startTime} - ${segment.endTime}] ${segment.shotType}\n`;

        if (segment.dialogue) {
          scriptText += `  DIALOG (${segment.dialogue.speaker}): "${segment.dialogue.text}"\n`;
          if (segment.dialogue.tone) {
            scriptText += `  TONE: ${segment.dialogue.tone}\n`;
          }
        }

        scriptText += `  VISUAL: ${segment.visual.description}\n`;

        if (segment.music) {
          scriptText += `  MUSIK: ${segment.music.type} - ${segment.music.description}\n`;
        }

        if (segment.location) {
          scriptText += `  LOKASI: ${segment.location}\n`;
        }

        if (segment.visualObjects && segment.visualObjects.length > 0) {
          scriptText += `  OBJEK: ${segment.visualObjects.join(', ')}\n`;
        }

        if (segment.notes) {
          scriptText += `  CATATAN: ${segment.notes}\n`;
        }

        scriptText += `\n`;
      });
    }

    // Add shot recommendations
    scriptText += `REKOMENDASI SHOT:\n`;
    scriptResponse.shotRecommendations.forEach(shot => {
      scriptText += `- ${shot.shotType}: ${shot.description} (${shot.duration || 'N/A'})\n`;
      if (shot.cameraMovement) {
        scriptText += `  PERGERAKAN KAMERA: ${shot.cameraMovement}\n`;
      }
      if (shot.angle) {
        scriptText += `  SUDUT: ${shot.angle}\n`;
      }
      if (shot.lighting) {
        scriptText += `  PENCAHAYAAN: ${shot.lighting}\n`;
      }
    });

    // Add location recommendations
    scriptText += `\nREKOMENDASI LOKASI:\n`;
    scriptResponse.locationRecommendations.forEach(location => {
      scriptText += `- ${location.name}: ${location.description}\n`;
      if (location.lighting) {
        scriptText += `  PENCAHAYAAN: ${location.lighting}\n`;
      }
      if (location.timeOfDay) {
        scriptText += `  WAKTU: ${location.timeOfDay}\n`;
      }
      if (location.weatherCondition) {
        scriptText += `  CUACA: ${location.weatherCondition}\n`;
      }
    });

    // Add visual object recommendations
    scriptText += `\nREKOMENDASI OBJEK VISUAL:\n`;
    scriptResponse.visualObjectRecommendations.forEach(obj => {
      scriptText += `- ${obj.name}: ${obj.purpose}\n`;
      if (obj.placement) {
        scriptText += `  PENEMPATAN: ${obj.placement}\n`;
      }
      if (obj.interaction) {
        scriptText += `  INTERAKSI: ${obj.interaction}\n`;
      }
    });

    // Add music recommendations if available
    if (scriptResponse.musicRecommendations && scriptResponse.musicRecommendations.length > 0) {
      scriptText += `\nREKOMENDASI MUSIK:\n`;
      scriptResponse.musicRecommendations.forEach(music => {
        scriptText += `- ${music.type}: ${music.description}\n`;
        if (music.startTime && music.endTime) {
          scriptText += `  WAKTU: ${music.startTime} - ${music.endTime}\n`;
        }
        if (music.mood) {
          scriptText += `  MOOD: ${music.mood}\n`;
        }
        if (music.tempo) {
          scriptText += `  TEMPO: ${music.tempo}\n`;
        }
        if (music.volume) {
          scriptText += `  VOLUME: ${music.volume}\n`;
        }
      });
    }

    // Add additional notes if available
    if (scriptResponse.additionalNotes) {
      scriptText += `\nCATATAN TAMBAHAN:\n${scriptResponse.additionalNotes}\n`;
    }

    // Add total duration
    scriptText += `\nTotal Durasi: ${scriptResponse.totalDuration || 'N/A'}\n`;

    navigator.clipboard.writeText(scriptText);

    toast({
      title: 'Disalin ke clipboard',
      description: 'Script detail telah disalin ke clipboard',
      variant: 'default',
    });
  };

  const handleExportPDF = () => {
    if (!scriptResponse) return;

    const doc = new jsPDF();

    // Add title
    doc.setFontSize(18);
    doc.text(scriptResponse.title, 20, 20);

    // Add sections
    let y = 30;
    doc.setFontSize(12);

    scriptResponse.sections.forEach(section => {
      doc.setFont(undefined, 'bold');
      doc.text(`${section.name.toUpperCase()} (${section.duration})`, 20, y);
      y += 7;

      doc.setFont(undefined, 'normal');
      const contentLines = doc.splitTextToSize(section.content, 170);
      doc.text(contentLines, 20, y);
      y += contentLines.length * 7 + 5;

      // Add timeline for this section if available
      if (section.timeline && section.timeline.length > 0) {
        doc.setFont(undefined, 'bold');
        doc.text('TIMELINE DETAIL:', 20, y);
        y += 7;

        doc.setFont(undefined, 'normal');
        section.timeline.forEach(segment => {
          // Check if we need a new page
          if (y > 250) {
            doc.addPage();
            y = 20;
          }

          doc.setFont(undefined, 'bold');
          doc.text(`[${segment.startTime} - ${segment.endTime}] ${segment.shotType}`, 20, y);
          y += 7;

          doc.setFont(undefined, 'normal');

          if (segment.dialogue) {
            const dialogText = `DIALOG (${segment.dialogue.speaker}): "${segment.dialogue.text}"`;
            const dialogLines = doc.splitTextToSize(dialogText, 160);
            doc.text(dialogLines, 25, y);
            y += dialogLines.length * 5;

            if (segment.dialogue.tone) {
              doc.text(`TONE: ${segment.dialogue.tone}`, 25, y);
              y += 5;
            }
          }

          const visualText = `VISUAL: ${segment.visual.description}`;
          const visualLines = doc.splitTextToSize(visualText, 160);
          doc.text(visualLines, 25, y);
          y += visualLines.length * 5;

          if (segment.music) {
            const musicText = `MUSIK: ${segment.music.type} - ${segment.music.description}`;
            const musicLines = doc.splitTextToSize(musicText, 160);
            doc.text(musicLines, 25, y);
            y += musicLines.length * 5;
          }

          if (segment.location) {
            doc.text(`LOKASI: ${segment.location}`, 25, y);
            y += 5;
          }

          if (segment.visualObjects && segment.visualObjects.length > 0) {
            const objText = `OBJEK: ${segment.visualObjects.join(', ')}`;
            const objLines = doc.splitTextToSize(objText, 160);
            doc.text(objLines, 25, y);
            y += objLines.length * 5;
          }

          if (segment.notes) {
            const notesText = `CATATAN: ${segment.notes}`;
            const notesLines = doc.splitTextToSize(notesText, 160);
            doc.text(notesLines, 25, y);
            y += notesLines.length * 5;
          }

          y += 5;
        });
      }

      // Add a new page if we're running out of space
      if (y > 270) {
        doc.addPage();
        y = 20;
      }
    });

    // Add overall timeline if available
    if (scriptResponse.timeline && scriptResponse.timeline.length > 0) {
      doc.addPage();
      y = 20;

      doc.setFont(undefined, 'bold');
      doc.text('TIMELINE KESELURUHAN:', 20, y);
      y += 10;

      doc.setFont(undefined, 'normal');
      scriptResponse.timeline.forEach(segment => {
        // Check if we need a new page
        if (y > 250) {
          doc.addPage();
          y = 20;
        }

        doc.setFont(undefined, 'bold');
        doc.text(`[${segment.startTime} - ${segment.endTime}] ${segment.shotType}`, 20, y);
        y += 7;

        doc.setFont(undefined, 'normal');

        if (segment.dialogue) {
          const dialogText = `DIALOG (${segment.dialogue.speaker}): "${segment.dialogue.text}"`;
          const dialogLines = doc.splitTextToSize(dialogText, 160);
          doc.text(dialogLines, 25, y);
          y += dialogLines.length * 5;

          if (segment.dialogue.tone) {
            doc.text(`TONE: ${segment.dialogue.tone}`, 25, y);
            y += 5;
          }
        }

        const visualText = `VISUAL: ${segment.visual.description}`;
        const visualLines = doc.splitTextToSize(visualText, 160);
        doc.text(visualLines, 25, y);
        y += visualLines.length * 5;

        if (segment.music) {
          const musicText = `MUSIK: ${segment.music.type} - ${segment.music.description}`;
          const musicLines = doc.splitTextToSize(musicText, 160);
          doc.text(musicLines, 25, y);
          y += musicLines.length * 5;
        }

        if (segment.location) {
          doc.text(`LOKASI: ${segment.location}`, 25, y);
          y += 5;
        }

        if (segment.visualObjects && segment.visualObjects.length > 0) {
          const objText = `OBJEK: ${segment.visualObjects.join(', ')}`;
          const objLines = doc.splitTextToSize(objText, 160);
          doc.text(objLines, 25, y);
          y += objLines.length * 5;
        }

        if (segment.notes) {
          const notesText = `CATATAN: ${segment.notes}`;
          const notesLines = doc.splitTextToSize(notesText, 160);
          doc.text(notesLines, 25, y);
          y += notesLines.length * 5;
        }

        y += 5;
      });
    }

    // Add a new page for recommendations
    doc.addPage();
    y = 20;

    // Add shot recommendations
    doc.setFont(undefined, 'bold');
    doc.text('REKOMENDASI SHOT:', 20, y);
    y += 7;

    doc.setFont(undefined, 'normal');
    scriptResponse.shotRecommendations.forEach(shot => {
      const shotText = `- ${shot.shotType}: ${shot.description} (${shot.duration || 'N/A'})`;
      const shotLines = doc.splitTextToSize(shotText, 170);
      doc.text(shotLines, 20, y);
      y += shotLines.length * 5;

      if (shot.cameraMovement) {
        doc.text(`  PERGERAKAN KAMERA: ${shot.cameraMovement}`, 20, y);
        y += 5;
      }

      if (shot.angle) {
        doc.text(`  SUDUT: ${shot.angle}`, 20, y);
        y += 5;
      }

      if (shot.lighting) {
        doc.text(`  PENCAHAYAAN: ${shot.lighting}`, 20, y);
        y += 5;
      }

      y += 2;

      if (y > 270) {
        doc.addPage();
        y = 20;
      }
    });

    y += 5;

    // Add location recommendations
    doc.setFont(undefined, 'bold');
    doc.text('REKOMENDASI LOKASI:', 20, y);
    y += 7;

    doc.setFont(undefined, 'normal');
    scriptResponse.locationRecommendations.forEach(location => {
      const locationText = `- ${location.name}: ${location.description}`;
      const locationLines = doc.splitTextToSize(locationText, 170);
      doc.text(locationLines, 20, y);
      y += locationLines.length * 5;

      if (location.lighting) {
        doc.text(`  PENCAHAYAAN: ${location.lighting}`, 20, y);
        y += 5;
      }

      if (location.timeOfDay) {
        doc.text(`  WAKTU: ${location.timeOfDay}`, 20, y);
        y += 5;
      }

      if (location.weatherCondition) {
        doc.text(`  CUACA: ${location.weatherCondition}`, 20, y);
        y += 5;
      }

      y += 2;

      if (y > 270) {
        doc.addPage();
        y = 20;
      }
    });

    y += 5;

    // Add visual object recommendations
    doc.setFont(undefined, 'bold');
    doc.text('REKOMENDASI OBJEK VISUAL:', 20, y);
    y += 7;

    doc.setFont(undefined, 'normal');
    scriptResponse.visualObjectRecommendations.forEach(obj => {
      const objText = `- ${obj.name}: ${obj.purpose}`;
      const objLines = doc.splitTextToSize(objText, 170);
      doc.text(objLines, 20, y);
      y += objLines.length * 5;

      if (obj.placement) {
        doc.text(`  PENEMPATAN: ${obj.placement}`, 20, y);
        y += 5;
      }

      if (obj.interaction) {
        doc.text(`  INTERAKSI: ${obj.interaction}`, 20, y);
        y += 5;
      }

      y += 2;

      if (y > 270) {
        doc.addPage();
        y = 20;
      }
    });

    // Add music recommendations if available
    if (scriptResponse.musicRecommendations && scriptResponse.musicRecommendations.length > 0) {
      y += 5;

      doc.setFont(undefined, 'bold');
      doc.text('REKOMENDASI MUSIK:', 20, y);
      y += 7;

      doc.setFont(undefined, 'normal');
      scriptResponse.musicRecommendations.forEach(music => {
        const musicText = `- ${music.type}: ${music.description}`;
        const musicLines = doc.splitTextToSize(musicText, 170);
        doc.text(musicLines, 20, y);
        y += musicLines.length * 5;

        if (music.startTime && music.endTime) {
          doc.text(`  WAKTU: ${music.startTime} - ${music.endTime}`, 20, y);
          y += 5;
        }

        if (music.mood) {
          doc.text(`  MOOD: ${music.mood}`, 20, y);
          y += 5;
        }

        if (music.tempo) {
          doc.text(`  TEMPO: ${music.tempo}`, 20, y);
          y += 5;
        }

        if (music.volume) {
          doc.text(`  VOLUME: ${music.volume}`, 20, y);
          y += 5;
        }

        y += 2;

        if (y > 270) {
          doc.addPage();
          y = 20;
        }
      });
    }

    y += 5;

    // Add additional notes if any
    if (scriptResponse.additionalNotes) {
      doc.setFont(undefined, 'bold');
      doc.text('CATATAN TAMBAHAN:', 20, y);
      y += 7;

      doc.setFont(undefined, 'normal');
      const notesLines = doc.splitTextToSize(scriptResponse.additionalNotes, 170);
      doc.text(notesLines, 20, y);
      y += notesLines.length * 5;
    }

    y += 5;

    // Add total duration
    doc.setFont(undefined, 'bold');
    doc.text(`Total Durasi: ${scriptResponse.totalDuration || 'N/A'}`, 20, y);

    // Save the PDF
    doc.save(`video-script-${new Date().toISOString().slice(0, 10)}.pdf`);

    toast({
      title: 'PDF berhasil dibuat',
      description: 'Script detail telah diekspor ke PDF',
      variant: 'default',
    });
  };

  return (
    <div className="container mx-auto p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Form Section */}
        <div>
          <form onSubmit={handleSubmit} className="space-y-4 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Generator Script Video</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="flex items-center gap-1"
                type="button"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                  <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
                  <path d="M3 3v5h5"></path>
                </svg>
                Reset Form
              </Button>
            </div>

            <div className="space-y-2">
              <Label htmlFor="videoType">Jenis Video</Label>
              <Select
                value={videoType}
                onValueChange={(value) => setVideoType(value as VideoType)}
              >
                <SelectTrigger id="videoType">
                  <SelectValue placeholder="Pilih jenis video" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={VideoType.MARKETING_PRODUCT}>Marketing Produk</SelectItem>
                  <SelectItem value={VideoType.DOCUMENTATION}>Dokumentasi</SelectItem>
                  <SelectItem value={VideoType.REELS}>Reels</SelectItem>
                  <SelectItem value={VideoType.JOKES}>Jokes/Humor</SelectItem>
                  <SelectItem value={VideoType.REVIEW}>Review</SelectItem>
                  <SelectItem value={VideoType.TESTIMONIAL}>Testimoni</SelectItem>
                  <SelectItem value={VideoType.EDUCATIONAL}>Edukasi</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="purpose">Tujuan Video</Label>
              <Select
                value={purpose}
                onValueChange={(value) => setPurpose(value as VideoPurpose)}
              >
                <SelectTrigger id="purpose">
                  <SelectValue placeholder="Pilih tujuan video" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={VideoPurpose.PROMOTION}>Promosi</SelectItem>
                  <SelectItem value={VideoPurpose.EDUCATION}>Edukasi</SelectItem>
                  <SelectItem value={VideoPurpose.VIRAL}>Viral</SelectItem>
                  <SelectItem value={VideoPurpose.DOCUMENTATION}>Dokumentasi</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="targetAudience">Target Audiens</Label>
              <Select
                value={targetAudience}
                onValueChange={(value) => setTargetAudience(value as VideoTargetAudience)}
              >
                <SelectTrigger id="targetAudience">
                  <SelectValue placeholder="Pilih target audiens" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={VideoTargetAudience.CUSTOMER}>Customer</SelectItem>
                  <SelectItem value={VideoTargetAudience.INTERNAL}>Internal</SelectItem>
                  <SelectItem value={VideoTargetAudience.GENERAL}>Umum</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="platform">Platform</Label>
              <Select
                value={platform}
                onValueChange={(value) => setPlatform(value as VideoPlatform)}
              >
                <SelectTrigger id="platform">
                  <SelectValue placeholder="Pilih platform" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={VideoPlatform.INSTAGRAM}>Instagram</SelectItem>
                  <SelectItem value={VideoPlatform.TIKTOK}>TikTok</SelectItem>
                  <SelectItem value={VideoPlatform.YOUTUBE}>YouTube</SelectItem>
                  <SelectItem value={VideoPlatform.WHATSAPP}>WhatsApp</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="productName">Nama Produk (Opsional)</Label>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={toggleProductInputMode}
                  className="h-8 px-2 text-xs"
                >
                  {productInputMode === 'manual' ? (
                    <>
                      <Database className="h-3.5 w-3.5 mr-1" />
                      Pilih dari Database
                    </>
                  ) : (
                    <>
                      <PenLine className="h-3.5 w-3.5 mr-1" />
                      Input Manual
                    </>
                  )}
                </Button>
              </div>

              {productInputMode === 'manual' ? (
                <Input
                  id="productName"
                  value={productName}
                  onChange={(e) => setProductName(e.target.value)}
                  placeholder="Masukkan nama produk"
                />
              ) : (
                <div className="relative product-dropdown">
                  <Button
                    type="button"
                    variant="outline"
                    role="combobox"
                    onClick={() => setIsProductPopoverOpen(!isProductPopoverOpen)}
                    className="w-full justify-between bg-white"
                  >
                    {selectedProduct ? selectedProduct.materialDescription : "Pilih produk..."}
                    <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>

                  {isProductPopoverOpen && (
                    <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-md product-dropdown">
                      <div className="p-2 border-b border-gray-200">
                        <input
                          type="text"
                          placeholder="Cari produk..."
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 no-navigation"
                          onChange={(e) => {
                            try {
                              e.preventDefault();
                              e.stopPropagation();

                              // Simple filtering logic with error handling
                              const searchTerm = e.target.value.toLowerCase();
                              if (searchTerm === '') {
                                // If search is empty, just fetch all products
                                fetchProducts()
                                  .then(setProducts)
                                  .catch(err => {
                                    console.error('Error fetching products:', err);
                                    toast({
                                      title: 'Error',
                                      description: 'Gagal memuat data produk',
                                      variant: 'destructive',
                                    });
                                  });
                              } else {
                                // Filter products based on search term
                                fetchProducts()
                                  .then(allProducts => {
                                    try {
                                      const filtered = allProducts.filter(product => {
                                        try {
                                          return (
                                            ((product.materialDescription || '').toLowerCase()).includes(searchTerm) ||
                                            ((product.oldMaterialNo || '').toLowerCase()).includes(searchTerm)
                                          );
                                        } catch (filterErr) {
                                          console.error('Error filtering product:', product, filterErr);
                                          return false;
                                        }
                                      });
                                      setProducts(filtered);
                                    } catch (filterErr) {
                                      console.error('Error filtering products:', filterErr);
                                      setProducts(allProducts); // Return all products if there's an error
                                    }
                                  })
                                  .catch(err => {
                                    console.error('Error fetching products for filtering:', err);
                                    toast({
                                      title: 'Error',
                                      description: 'Gagal memuat data produk',
                                      variant: 'destructive',
                                    });
                                  });
                              }
                            } catch (err) {
                              console.error('Error in search input change:', err);
                            }
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              e.stopPropagation();
                            }
                          }}
                        />
                      </div>

                      <div className="max-h-[300px] overflow-y-auto p-1">
                        {isLoadingProducts ? (
                          <div className="flex items-center justify-center p-4">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            <span>Memuat produk...</span>
                          </div>
                        ) : products.length === 0 ? (
                          <div className="py-6 text-center text-sm text-gray-500">
                            Produk tidak ditemukan
                          </div>
                        ) : (
                          <div className="space-y-1">
                            {products.map((product) => (
                              <div
                                key={product.id}
                                onClick={(e) => {
                                  try {
                                    e.preventDefault();
                                    e.stopPropagation();

                                    setSelectedProduct(product);
                                    setProductName(product.materialDescription || 'No Name');
                                    setIsProductPopoverOpen(false);

                                    // Add product details to additional info if it's empty
                                    if (!additionalInfo) {
                                      const productDetails = `Produk: ${product.materialDescription || 'No Name'}\nKode: ${product.oldMaterialNo || 'N/A'}\nHarga: ${(product.price || 0).toLocaleString('id-ID')} IDR`;
                                      setAdditionalInfo(productDetails);
                                    }

                                    toast({
                                      title: 'Produk dipilih',
                                      description: `${product.materialDescription || 'No Name'} telah dipilih`,
                                      variant: 'default',
                                    });
                                  } catch (err) {
                                    console.error('Error selecting product:', err);
                                    toast({
                                      title: 'Error',
                                      description: 'Gagal memilih produk',
                                      variant: 'destructive',
                                    });
                                  }
                                }}
                                className="px-4 py-2 hover:bg-blue-50 cursor-pointer rounded-md"
                              >
                                <div className="flex flex-col w-full">
                                  <span className="font-medium">{product.materialDescription || 'No Name'}</span>
                                  <span className="text-xs text-gray-500">
                                    {product.oldMaterialNo || 'N/A'} - {(product.price || 0).toLocaleString('id-ID')} IDR
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {selectedProduct && (
                <div className="text-xs text-gray-500 mt-1">
                  Kode: {selectedProduct.oldMaterialNo} |
                  Harga: {selectedProduct.price.toLocaleString('id-ID')} IDR
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="duration">Durasi (detik, Opsional)</Label>
              <Input
                id="duration"
                type="number"
                value={duration}
                onChange={(e) => setDuration(e.target.value)}
                placeholder="Masukkan durasi dalam detik"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="additionalInfo">Informasi Tambahan (Opsional)</Label>
              <Textarea
                id="additionalInfo"
                value={additionalInfo}
                onChange={(e) => setAdditionalInfo(e.target.value)}
                placeholder="Masukkan informasi tambahan"
                rows={4}
              />
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Membuat Script...
                </>
              ) : (
                'Buat Script Video'
              )}
            </Button>
          </form>
        </div>

        {/* Result Section */}
        <div>
          {scriptResponse && !isEditing && (
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">{scriptResponse.title}</h2>
                <div className="flex space-x-2">
                  <Button
                    variant={savedScript ? "default" : "outline"}
                    size="sm"
                    onClick={handleSaveScript}
                    disabled={isSaving}
                    className={`transition-all duration-200 ${isSaving ? 'bg-green-100 text-green-800' : ''}`}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        Menyimpan...
                      </>
                    ) : savedScript ? (
                      <>
                        <Check className="h-4 w-4 mr-1" />
                        Tersimpan
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-1" />
                        Simpan
                      </>
                    )}
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleEditScript}>
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleCopyToClipboard}>
                    <Copy className="h-4 w-4 mr-1" />
                    Copy
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleExportPDF}>
                    <Download className="h-4 w-4 mr-1" />
                    PDF
                  </Button>
                  {initialScript && (
                    <Button variant="outline" size="sm" onClick={handleReset}>
                      <FileText className="h-4 w-4 mr-1" />
                      Baru
                    </Button>
                  )}
                </div>
              </div>

              <div className="space-y-6">
                {/* Sections with Timeline */}
                {scriptResponse.sections.map((section, index) => (
                  <div key={index} className="border-b pb-4">
                    <h3 className="font-medium text-lg">{section.name} <span className="text-sm text-gray-500">({section.duration})</span></h3>
                    <p className="whitespace-pre-line mb-3">{section.content}</p>

                    {/* Timeline for this section */}
                    {section.timeline && section.timeline.length > 0 && (
                      <div className="mt-3 bg-gray-50 p-3 rounded-md">
                        <h4 className="font-medium text-md mb-2">Timeline Detail:</h4>
                        <div className="space-y-4">
                          {section.timeline.map((segment, segIndex) => (
                            <div key={segIndex} className="border-l-2 border-blue-400 pl-3 py-1">
                              <div className="flex items-center mb-1">
                                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded">
                                  {segment.startTime} - {segment.endTime}
                                </span>
                                <span className="ml-2 bg-gray-100 text-gray-800 text-xs font-medium px-2 py-0.5 rounded">
                                  {segment.shotType}
                                </span>
                              </div>

                              {segment.dialogue && (
                                <div className="mb-1 pl-2">
                                  <p className="text-sm">
                                    <span className="font-medium">Dialog ({segment.dialogue.speaker}):</span> "{segment.dialogue.text}"
                                    {segment.dialogue.tone && <span className="text-gray-500 text-xs ml-1"> - Tone: {segment.dialogue.tone}</span>}
                                  </p>
                                </div>
                              )}

                              <div className="mb-1 pl-2">
                                <p className="text-sm">
                                  <span className="font-medium">Visual:</span> {segment.visual.description}
                                  {segment.visual.transition && <span className="text-gray-500 text-xs ml-1"> - Transition: {segment.visual.transition}</span>}
                                  {segment.visual.effects && <span className="text-gray-500 text-xs ml-1"> - Effects: {segment.visual.effects}</span>}
                                </p>
                              </div>

                              {segment.music && (
                                <div className="mb-1 pl-2">
                                  <p className="text-sm">
                                    <span className="font-medium">Music:</span> {segment.music.type} - {segment.music.description}
                                    {segment.music.volume && <span className="text-gray-500 text-xs ml-1"> - Volume: {segment.music.volume}</span>}
                                  </p>
                                </div>
                              )}

                              {segment.location && (
                                <div className="mb-1 pl-2">
                                  <p className="text-sm">
                                    <span className="font-medium">Location:</span> {segment.location}
                                  </p>
                                </div>
                              )}

                              {segment.visualObjects && segment.visualObjects.length > 0 && (
                                <div className="mb-1 pl-2">
                                  <p className="text-sm">
                                    <span className="font-medium">Objects:</span> {segment.visualObjects.join(', ')}
                                  </p>
                                </div>
                              )}

                              {segment.notes && (
                                <div className="mb-1 pl-2">
                                  <p className="text-sm">
                                    <span className="font-medium">Notes:</span> {segment.notes}
                                  </p>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {/* Overall Timeline */}
                {scriptResponse.timeline && scriptResponse.timeline.length > 0 && (
                  <div className="border-b pb-4">
                    <h3 className="font-medium text-lg mb-3">Timeline Keseluruhan</h3>
                    <div className="bg-gray-50 p-3 rounded-md">
                      <div className="space-y-4">
                        {scriptResponse.timeline.map((segment, segIndex) => (
                          <div key={segIndex} className="border-l-2 border-green-400 pl-3 py-1">
                            <div className="flex items-center mb-1">
                              <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded">
                                {segment.startTime} - {segment.endTime}
                              </span>
                              <span className="ml-2 bg-gray-100 text-gray-800 text-xs font-medium px-2 py-0.5 rounded">
                                {segment.shotType}
                              </span>
                            </div>

                            {segment.dialogue && (
                              <div className="mb-1 pl-2">
                                <p className="text-sm">
                                  <span className="font-medium">Dialog ({segment.dialogue.speaker}):</span> "{segment.dialogue.text}"
                                  {segment.dialogue.tone && <span className="text-gray-500 text-xs ml-1"> - Tone: {segment.dialogue.tone}</span>}
                                </p>
                              </div>
                            )}

                            <div className="mb-1 pl-2">
                              <p className="text-sm">
                                <span className="font-medium">Visual:</span> {segment.visual.description}
                                {segment.visual.transition && <span className="text-gray-500 text-xs ml-1"> - Transition: {segment.visual.transition}</span>}
                                {segment.visual.effects && <span className="text-gray-500 text-xs ml-1"> - Effects: {segment.visual.effects}</span>}
                              </p>
                            </div>

                            {segment.music && (
                              <div className="mb-1 pl-2">
                                <p className="text-sm">
                                  <span className="font-medium">Music:</span> {segment.music.type} - {segment.music.description}
                                  {segment.music.volume && <span className="text-gray-500 text-xs ml-1"> - Volume: {segment.music.volume}</span>}
                                </p>
                              </div>
                            )}

                            {segment.location && (
                              <div className="mb-1 pl-2">
                                <p className="text-sm">
                                  <span className="font-medium">Location:</span> {segment.location}
                                </p>
                              </div>
                            )}

                            {segment.visualObjects && segment.visualObjects.length > 0 && (
                              <div className="mb-1 pl-2">
                                <p className="text-sm">
                                  <span className="font-medium">Objects:</span> {segment.visualObjects.join(', ')}
                                </p>
                              </div>
                            )}

                            {segment.notes && (
                              <div className="mb-1 pl-2">
                                <p className="text-sm">
                                  <span className="font-medium">Notes:</span> {segment.notes}
                                </p>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Music Recommendations */}
                {scriptResponse.musicRecommendations && scriptResponse.musicRecommendations.length > 0 && (
                  <div>
                    <h3 className="font-medium text-lg mb-2">Rekomendasi Musik</h3>
                    <ul className="list-disc pl-5 space-y-2">
                      {scriptResponse.musicRecommendations.map((music, index) => (
                        <li key={index} className="pb-1">
                          <div>
                            <span className="font-medium">{music.type}</span>: {music.description}
                          </div>
                          {(music.startTime || music.endTime) && (
                            <div className="text-sm text-gray-600 ml-1">
                              Waktu: {music.startTime || '0:00'} - {music.endTime || 'end'}
                            </div>
                          )}
                          {music.mood && (
                            <div className="text-sm text-gray-600 ml-1">
                              Mood: {music.mood}
                            </div>
                          )}
                          {music.tempo && (
                            <div className="text-sm text-gray-600 ml-1">
                              Tempo: {music.tempo}
                            </div>
                          )}
                          {music.volume && (
                            <div className="text-sm text-gray-600 ml-1">
                              Volume: {music.volume}
                            </div>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Shot Recommendations */}
                <div>
                  <h3 className="font-medium text-lg mb-2">Rekomendasi Shot</h3>
                  <ul className="list-disc pl-5 space-y-2">
                    {scriptResponse.shotRecommendations.map((shot, index) => (
                      <li key={index} className="pb-1">
                        <div>
                          <span className="font-medium">{shot.shotType}</span>: {shot.description} {shot.duration && `(${shot.duration})`}
                        </div>
                        {shot.cameraMovement && (
                          <div className="text-sm text-gray-600 ml-1">
                            Pergerakan Kamera: {shot.cameraMovement}
                          </div>
                        )}
                        {shot.angle && (
                          <div className="text-sm text-gray-600 ml-1">
                            Sudut: {shot.angle}
                          </div>
                        )}
                        {shot.lighting && (
                          <div className="text-sm text-gray-600 ml-1">
                            Pencahayaan: {shot.lighting}
                          </div>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Location Recommendations */}
                <div>
                  <h3 className="font-medium text-lg mb-2">Rekomendasi Lokasi</h3>
                  <ul className="list-disc pl-5 space-y-2">
                    {scriptResponse.locationRecommendations.map((location, index) => (
                      <li key={index} className="pb-1">
                        <div>
                          <span className="font-medium">{location.name}</span>: {location.description}
                        </div>
                        {location.lighting && (
                          <div className="text-sm text-gray-600 ml-1">
                            Pencahayaan: {location.lighting}
                          </div>
                        )}
                        {location.timeOfDay && (
                          <div className="text-sm text-gray-600 ml-1">
                            Waktu: {location.timeOfDay}
                          </div>
                        )}
                        {location.weatherCondition && (
                          <div className="text-sm text-gray-600 ml-1">
                            Cuaca: {location.weatherCondition}
                          </div>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Visual Object Recommendations */}
                <div>
                  <h3 className="font-medium text-lg mb-2">Rekomendasi Objek Visual</h3>
                  <ul className="list-disc pl-5 space-y-2">
                    {scriptResponse.visualObjectRecommendations.map((obj, index) => (
                      <li key={index} className="pb-1">
                        <div>
                          <span className="font-medium">{obj.name}</span>: {obj.purpose}
                        </div>
                        {obj.placement && (
                          <div className="text-sm text-gray-600 ml-1">
                            Penempatan: {obj.placement}
                          </div>
                        )}
                        {obj.interaction && (
                          <div className="text-sm text-gray-600 ml-1">
                            Interaksi: {obj.interaction}
                          </div>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Additional Notes */}
                {scriptResponse.additionalNotes && (
                  <div>
                    <h3 className="font-medium text-lg mb-2">Catatan Tambahan</h3>
                    <p className="whitespace-pre-line">{scriptResponse.additionalNotes}</p>
                  </div>
                )}

                {/* Total Duration */}
                <div className="pt-2">
                  <p className="text-sm font-medium">Total Durasi: {scriptResponse.totalDuration || 'N/A'}</p>
                </div>
              </div>
            </div>
          )}

          {isEditing && editedScript && (
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Edit Script</h2>
                <div className="flex space-x-2">
                  <Button variant="default" size="sm" onClick={handleSaveEdit}>
                    Simpan
                  </Button>
                  <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                    Batal
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="editTitle">Judul</Label>
                  <Input
                    id="editTitle"
                    value={editedScript.title}
                    onChange={(e) => setEditedScript({...editedScript, title: e.target.value})}
                  />
                </div>

                <h3 className="font-medium">Bagian Script</h3>
                {editedScript.sections.map((section, index) => (
                  <div key={index} className="space-y-2 border p-3 rounded">
                    <div className="flex justify-between">
                      <Label htmlFor={`editSectionName${index}`}>Nama Bagian</Label>
                      <Label htmlFor={`editSectionDuration${index}`}>Durasi</Label>
                    </div>
                    <div className="flex gap-2">
                      <Input
                        id={`editSectionName${index}`}
                        value={section.name}
                        onChange={(e) => {
                          const newSections = [...editedScript.sections];
                          newSections[index].name = e.target.value;
                          setEditedScript({...editedScript, sections: newSections});
                        }}
                      />
                      <Input
                        id={`editSectionDuration${index}`}
                        value={section.duration || ''}
                        onChange={(e) => {
                          const newSections = [...editedScript.sections];
                          newSections[index].duration = e.target.value;
                          setEditedScript({...editedScript, sections: newSections});
                        }}
                      />
                    </div>
                    <Label htmlFor={`editSectionContent${index}`}>Konten</Label>
                    <Textarea
                      id={`editSectionContent${index}`}
                      value={section.content}
                      onChange={(e) => {
                        const newSections = [...editedScript.sections];
                        newSections[index].content = e.target.value;
                        setEditedScript({...editedScript, sections: newSections});
                      }}
                      rows={4}
                    />
                  </div>
                ))}

                <div className="space-y-2">
                  <Label htmlFor="editTotalDuration">Total Durasi</Label>
                  <Input
                    id="editTotalDuration"
                    value={editedScript.totalDuration || ''}
                    onChange={(e) => setEditedScript({...editedScript, totalDuration: e.target.value})}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="editAdditionalNotes">Catatan Tambahan</Label>
                  <Textarea
                    id="editAdditionalNotes"
                    value={editedScript.additionalNotes || ''}
                    onChange={(e) => setEditedScript({...editedScript, additionalNotes: e.target.value})}
                    rows={3}
                  />
                </div>
              </div>
            </div>
          )}

          {!scriptResponse && !isLoading && (
            <Card>
              <CardContent className="p-6 flex flex-col items-center justify-center min-h-[400px] text-center">
                <FileText className="h-16 w-16 text-gray-300 mb-4" />
                <h3 className="text-lg font-medium mb-2">Belum Ada Script</h3>
                <p className="text-gray-500 mb-4">Isi form di sebelah kiri dan klik "Buat Script Video" untuk membuat script video baru.</p>
              </CardContent>
            </Card>
          )}

          {isLoading && (
            <Card>
              <CardContent className="p-6 flex flex-col items-center justify-center min-h-[400px]">
                <Loader2 className="h-16 w-16 text-blue-500 animate-spin mb-4" />
                <h3 className="text-lg font-medium mb-2">Membuat Script Video...</h3>
                <p className="text-gray-500">Mohon tunggu sebentar, kami sedang membuat script video sesuai dengan permintaan Anda.</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default VideoScriptGenerator;
