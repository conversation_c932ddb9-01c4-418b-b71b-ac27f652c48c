<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Proposal Builder</h1>
                    <p class="mt-2 text-gray-600">Create professional tire sales proposals with automated calculations</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="saveTemplate"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center"
                    >
                        <Save class="h-4 w-4 mr-2" />
                        Save Template
                    </button>
                    <button
                        @click="previewProposal"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <Eye class="h-4 w-4 mr-2" />
                        Preview
                    </button>
                    <button
                        @click="exportPDF"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export PDF
                    </button>
                </div>
            </div>

            <!-- Proposal Builder Steps -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <nav class="flex space-x-8">
                        <button
                            v-for="step in steps"
                            :key="step.id"
                            @click="currentStep = step.id"
                            :class="[
                                'py-2 px-1 border-b-2 font-medium text-sm',
                                currentStep === step.id
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            ]"
                        >
                            <component :is="step.icon" class="h-4 w-4 mr-2 inline" />
                            {{ step.name }}
                        </button>
                    </nav>
                </div>

                <div class="p-6">
                    <!-- Step 1: Customer Information -->
                    <div v-if="currentStep === 'customer'" class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-900">Customer Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                                <input 
                                    v-model="proposal.customer.companyName" 
                                    type="text" 
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="PT Mining Sejahtera"
                                />
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Contact Person</label>
                                <input 
                                    v-model="proposal.customer.contactPerson" 
                                    type="text" 
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Ahmad Wijaya"
                                />
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input 
                                    v-model="proposal.customer.email" 
                                    type="email" 
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="<EMAIL>"
                                />
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                <input 
                                    v-model="proposal.customer.phone" 
                                    type="tel" 
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="+62 812 3456 7890"
                                />
                            </div>
                            
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                                <textarea 
                                    v-model="proposal.customer.address" 
                                    rows="3"
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Jl. Industri No. 123, Jakarta"
                                ></textarea>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Industry</label>
                                <select 
                                    v-model="proposal.customer.industry" 
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="">Select Industry</option>
                                    <option value="mining">Mining & Quarrying</option>
                                    <option value="construction">Construction</option>
                                    <option value="logistics">Logistics & Transportation</option>
                                    <option value="agriculture">Agriculture</option>
                                    <option value="manufacturing">Manufacturing</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Fleet Size</label>
                                <input 
                                    v-model.number="proposal.customer.fleetSize" 
                                    type="number" 
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="50"
                                />
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Product Selection -->
                    <div v-if="currentStep === 'products'" class="space-y-6">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold text-gray-900">Product Selection</h3>
                            <button
                                @click="addProduct"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                            >
                                <Plus class="h-4 w-4 mr-2" />
                                Add Product
                            </button>
                        </div>

                        <div class="space-y-4">
                            <div 
                                v-for="(product, index) in proposal.products" 
                                :key="index"
                                class="p-4 border border-gray-200 rounded-lg"
                            >
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="font-medium text-gray-900">Product {{ index + 1 }}</h4>
                                    <button
                                        @click="removeProduct(index)"
                                        class="text-red-600 hover:text-red-800"
                                    >
                                        <Trash2 class="h-4 w-4" />
                                    </button>
                                </div>
                                
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Product</label>
                                        <select 
                                            v-model="product.productId" 
                                            @change="updateProductDetails(index)"
                                            class="w-full p-2 border border-gray-300 rounded text-sm"
                                        >
                                            <option value="">Select product...</option>
                                            <option 
                                                v-for="p in availableProducts" 
                                                :key="p.id" 
                                                :value="p.id"
                                            >
                                                {{ p.name }}
                                            </option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                                        <input 
                                            v-model.number="product.quantity" 
                                            type="number" 
                                            min="1"
                                            @input="calculateProductTotal(index)"
                                            class="w-full p-2 border border-gray-300 rounded text-sm"
                                        />
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Unit Price</label>
                                        <input 
                                            v-model.number="product.unitPrice" 
                                            type="number"
                                            @input="calculateProductTotal(index)"
                                            class="w-full p-2 border border-gray-300 rounded text-sm"
                                        />
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Total</label>
                                        <input 
                                            :value="formatCurrency(product.total || 0)" 
                                            readonly
                                            class="w-full p-2 border border-gray-300 rounded text-sm bg-gray-50"
                                        />
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                    <textarea 
                                        v-model="product.description" 
                                        rows="2"
                                        class="w-full p-2 border border-gray-300 rounded text-sm"
                                        placeholder="Product specifications and features..."
                                    ></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Product Summary -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="font-medium text-gray-900">Subtotal:</span>
                                <span class="text-lg font-bold text-gray-900">{{ formatCurrency(subtotal) }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Pricing & Terms -->
                    <div v-if="currentStep === 'pricing'" class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-900">Pricing & Terms</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Discount (%)</label>
                                <input 
                                    v-model.number="proposal.pricing.discount" 
                                    type="number" 
                                    min="0" 
                                    max="100"
                                    @input="calculateTotals"
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tax (%)</label>
                                <input 
                                    v-model.number="proposal.pricing.tax" 
                                    type="number" 
                                    min="0"
                                    @input="calculateTotals"
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Payment Terms</label>
                                <select 
                                    v-model="proposal.terms.paymentTerms" 
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="net30">Net 30 Days</option>
                                    <option value="net60">Net 60 Days</option>
                                    <option value="advance">50% Advance, 50% on Delivery</option>
                                    <option value="cod">Cash on Delivery</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Delivery Terms</label>
                                <select 
                                    v-model="proposal.terms.deliveryTerms" 
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="fob">FOB Origin</option>
                                    <option value="delivered">Delivered to Site</option>
                                    <option value="pickup">Customer Pickup</option>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Validity (Days)</label>
                                <input 
                                    v-model.number="proposal.terms.validity" 
                                    type="number" 
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Warranty (Months)</label>
                                <input 
                                    v-model.number="proposal.terms.warranty" 
                                    type="number" 
                                    class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                        </div>

                        <!-- Pricing Summary -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-4">Pricing Summary</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span>Subtotal:</span>
                                    <span>{{ formatCurrency(subtotal) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Discount ({{ proposal.pricing.discount }}%):</span>
                                    <span class="text-red-600">-{{ formatCurrency(discountAmount) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>After Discount:</span>
                                    <span>{{ formatCurrency(afterDiscount) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Tax ({{ proposal.pricing.tax }}%):</span>
                                    <span>{{ formatCurrency(taxAmount) }}</span>
                                </div>
                                <div class="flex justify-between border-t pt-2 font-bold text-lg">
                                    <span>Total:</span>
                                    <span>{{ formatCurrency(grandTotal) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Review -->
                    <div v-if="currentStep === 'review'" class="space-y-6">
                        <h3 class="text-lg font-semibold text-gray-900">Review Proposal</h3>
                        
                        <!-- Customer Summary -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium text-gray-900 mb-2">Customer Information</h4>
                            <p><strong>Company:</strong> {{ proposal.customer.companyName }}</p>
                            <p><strong>Contact:</strong> {{ proposal.customer.contactPerson }}</p>
                            <p><strong>Industry:</strong> {{ proposal.customer.industry }}</p>
                        </div>

                        <!-- Products Summary -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium text-gray-900 mb-2">Products ({{ proposal.products.length }} items)</h4>
                            <div class="space-y-2">
                                <div v-for="(product, index) in proposal.products" :key="index" class="flex justify-between">
                                    <span>{{ getProductName(product.productId) }} ({{ product.quantity }}x)</span>
                                    <span>{{ formatCurrency(product.total || 0) }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Final Total -->
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-semibold text-blue-900">Grand Total:</span>
                                <span class="text-2xl font-bold text-blue-900">{{ formatCurrency(grandTotal) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="px-6 py-4 border-t border-gray-200 flex justify-between">
                    <button
                        @click="previousStep"
                        :disabled="currentStep === 'customer'"
                        class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Previous
                    </button>
                    <button
                        @click="nextStep"
                        :disabled="currentStep === 'review'"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Next
                    </button>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-purple-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-purple-900">Advanced Features Coming Soon</h3>
                        <p class="text-purple-700 mt-1">
                            Enhanced proposal features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-purple-700 mt-2 space-y-1">
                            <li>Professional PDF generation with company branding</li>
                            <li>Digital signature integration</li>
                            <li>Proposal tracking and analytics</li>
                            <li>Automated follow-up reminders</li>
                            <li>Integration with CRM systems</li>
                            <li>Multi-language proposal templates</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Save,
    Eye,
    Download,
    User,
    Package,
    DollarSign,
    FileCheck,
    Plus,
    Trash2,
    Info
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Types
interface Customer {
    companyName: string;
    contactPerson: string;
    email: string;
    phone: string;
    address: string;
    industry: string;
    fleetSize: number;
}

interface Product {
    productId: string;
    quantity: number;
    unitPrice: number;
    total?: number;
    description: string;
}

interface Pricing {
    discount: number;
    tax: number;
}

interface Terms {
    paymentTerms: string;
    deliveryTerms: string;
    validity: number;
    warranty: number;
}

interface Proposal {
    customer: Customer;
    products: Product[];
    pricing: Pricing;
    terms: Terms;
}

// Reactive state
const currentStep = ref('customer');

const steps = [
    { id: 'customer', name: 'Customer Info', icon: User },
    { id: 'products', name: 'Products', icon: Package },
    { id: 'pricing', name: 'Pricing & Terms', icon: DollarSign },
    { id: 'review', name: 'Review', icon: FileCheck }
];

const proposal = ref<Proposal>({
    customer: {
        companyName: '',
        contactPerson: '',
        email: '',
        phone: '',
        address: '',
        industry: '',
        fleetSize: 0
    },
    products: [],
    pricing: {
        discount: 0,
        tax: 11
    },
    terms: {
        paymentTerms: 'net30',
        deliveryTerms: 'delivered',
        validity: 30,
        warranty: 12
    }
});

const availableProducts = ref([
    { id: '1', name: '27.00 R 49 XD GRIP B E4T TL', price: ********* },
    { id: '2', name: '24.00 R 35 XD GRIP B E4T TL', price: ********* },
    { id: '3', name: '21.00 R 33 XD GRIP B E4T TL', price: ********* },
    { id: '4', name: 'Engine Oil SAE 15W-40 (20L)', price: 850000 },
    { id: '5', name: 'Air Filter Heavy Duty', price: 450000 },
    { id: '6', name: 'Oil Filter Premium', price: 320000 },
    { id: '7', name: 'Coolant Radiator (5L)', price: 275000 },
    { id: '8', name: 'Brake Fluid DOT 4 (1L)', price: 125000 }
]);

// Computed properties
const subtotal = computed(() => {
    return proposal.value.products.reduce((sum, product) => sum + (product.total || 0), 0);
});

const discountAmount = computed(() => {
    return subtotal.value * (proposal.value.pricing.discount / 100);
});

const afterDiscount = computed(() => {
    return subtotal.value - discountAmount.value;
});

const taxAmount = computed(() => {
    return afterDiscount.value * (proposal.value.pricing.tax / 100);
});

const grandTotal = computed(() => {
    return afterDiscount.value + taxAmount.value;
});

// Utility functions
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const getProductName = (productId: string): string => {
    const product = availableProducts.value.find(p => p.id === productId);
    return product ? product.name : 'Unknown Product';
};

// Product management
const addProduct = () => {
    proposal.value.products.push({
        productId: '',
        quantity: 1,
        unitPrice: 0,
        total: 0,
        description: ''
    });
};

const removeProduct = (index: number) => {
    proposal.value.products.splice(index, 1);
};

const updateProductDetails = (index: number) => {
    const product = proposal.value.products[index];
    const selectedProduct = availableProducts.value.find(p => p.id === product.productId);

    if (selectedProduct) {
        product.unitPrice = selectedProduct.price;
        calculateProductTotal(index);
    }
};

const calculateProductTotal = (index: number) => {
    const product = proposal.value.products[index];
    product.total = product.quantity * product.unitPrice;
};

const calculateTotals = () => {
    // Recalculate all totals when pricing changes
    proposal.value.products.forEach((_, index) => {
        calculateProductTotal(index);
    });
};

// Navigation
const nextStep = () => {
    const stepOrder = ['customer', 'products', 'pricing', 'review'];
    const currentIndex = stepOrder.indexOf(currentStep.value);
    if (currentIndex < stepOrder.length - 1) {
        currentStep.value = stepOrder[currentIndex + 1];
    }
};

const previousStep = () => {
    const stepOrder = ['customer', 'products', 'pricing', 'review'];
    const currentIndex = stepOrder.indexOf(currentStep.value);
    if (currentIndex > 0) {
        currentStep.value = stepOrder[currentIndex - 1];
    }
};

// Actions
const saveTemplate = () => {
    try {
        const templateData = JSON.stringify(proposal.value, null, 2);
        localStorage.setItem('proposal_template', templateData);
        alert('Template saved successfully!');
    } catch (error) {
        console.error('Error saving template:', error);
        alert('Failed to save template.');
    }
};

const previewProposal = () => {
    alert('Preview functionality will open proposal in new window (coming soon)');
};

const exportPDF = () => {
    alert('PDF export functionality coming soon. Will generate professional proposal PDF.');
};

// Initialize
onMounted(() => {
    // Load saved template if exists
    try {
        const savedTemplate = localStorage.getItem('proposal_template');
        if (savedTemplate) {
            const parsedTemplate = JSON.parse(savedTemplate);
            proposal.value = { ...proposal.value, ...parsedTemplate };
        }
    } catch (error) {
        console.error('Error loading saved template:', error);
    }

    // Add initial product
    if (proposal.value.products.length === 0) {
        addProduct();
    }
});
</script>
