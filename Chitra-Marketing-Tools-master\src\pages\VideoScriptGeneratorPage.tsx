import React, { useState, useEffect } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Video, Info, List } from 'lucide-react';
import VideoScriptGenerator from '../components/VideoScriptGenerator';
import SimpleVideoScriptList from '../components/SimpleVideoScriptList';
import { SavedVideoScript } from '../types/videoScript';
import { VideoScriptFormData } from '../types/videoScriptForm';

export default function VideoScriptGeneratorPage() {
  const [activeTab, setActiveTab] = useState('generator');
  const [editingScript, setEditingScript] = useState<SavedVideoScript | undefined>(undefined);
  const [editingForm, setEditingForm] = useState<VideoScriptFormData | undefined>(undefined);
  const [contentData, setContentData] = useState<any>(null);

  // Handle direct navigation to specific tab
  const navigateToTab = (tabId: string) => {
    console.log(`Navigating to tab: ${tabId}`);

    // First update the state
    setActiveTab(tabId);

    // Then force the UI to reflect the change by clicking the tab
    setTimeout(() => {
      const tabElement = document.querySelector(`[value="${tabId}"]`) as HTMLElement;
      if (tabElement) {
        console.log(`Clicking ${tabId} tab programmatically`);
        tabElement.click();
      } else {
        console.warn(`Tab element with value="${tabId}" not found`);
      }
    }, 50);
  };

  // Check for content data from Monthly Content Plan
  useEffect(() => {
    const contentToScript = sessionStorage.getItem('content_to_script');
    if (contentToScript) {
      try {
        const data = JSON.parse(contentToScript);
        setContentData(data);
        // Clear the session storage
        sessionStorage.removeItem('content_to_script');
      } catch (error) {
        console.error('Error parsing content data:', error);
      }
    }
  }, []);

  // Handle script saved
  const handleScriptSaved = (script: SavedVideoScript) => {
    // Switch to the list tab after saving
    console.log('Script saved, switching to list tab');
    try {
      // Force a re-render first
      setEditingScript(undefined);

      // Use a longer delay to ensure UI updates properly
      setTimeout(() => {
        // Force update the active tab state
        setActiveTab('list');
        console.log('Tab switched to list');

        // Force another re-render to ensure the tab change is applied
        setTimeout(() => {
          const listTab = document.querySelector('[value="list"]') as HTMLElement;
          if (listTab) {
            console.log('Clicking list tab programmatically');
            listTab.click();
          }
        }, 50);
      }, 200);
    } catch (error) {
      console.error('Error switching tabs:', error);
    }
  };

  // Handle script edit
  const handleEditScript = (script: SavedVideoScript) => {
    setEditingScript(script);
    setActiveTab('generator');
  };

  // Handle reset editor
  const handleResetEditor = () => {
    console.log('Resetting editor state');
    try {
      // Reset all state
      setEditingScript(undefined);
      setEditingForm(undefined);
      setContentData(null);

      // Make sure we stay on the generator tab
      setActiveTab('generator');

      console.log('Editor state reset successfully');
    } catch (error) {
      console.error('Error resetting editor:', error);
    }
  };

  // Handle form selected from list
  const handleFormSelected = (form: VideoScriptFormData) => {
    console.log('Form selected:', form);
    try {
      setEditingForm(form);
      setActiveTab('generator');
    } catch (error) {
      console.error('Error selecting form:', error);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Video className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-bold">Video Script Generator</h1>
        </div>
        <div className="flex space-x-2">
          <Button
            variant={activeTab === 'generator' ? "default" : "outline"}
            size="sm"
            onClick={() => navigateToTab('generator')}
            className="flex items-center gap-1 font-medium"
            id="generator-tab-button"
          >
            <Video className="h-4 w-4" />
            Generator
          </Button>
          <Button
            variant={activeTab === 'list' ? "default" : "outline"}
            size="sm"
            onClick={() => navigateToTab('list')}
            className="flex items-center gap-1 font-medium"
            id="list-tab-button"
          >
            <List className="h-4 w-4" />
            Daftar Video
          </Button>
          <Button
            variant={activeTab === 'about' ? "default" : "outline"}
            size="sm"
            onClick={() => navigateToTab('about')}
            className="flex items-center gap-1 font-medium"
            id="about-tab-button"
          >
            <Info className="h-4 w-4" />
            Tentang
          </Button>
        </div>
      </div>

      {/* Use a controlled Tabs component with explicit state management */}
      <Tabs
        value={activeTab}
        onValueChange={(value) => {
          console.log(`Tab changed to: ${value}`);
          setActiveTab(value);
        }}
        className="w-full"
        defaultValue="generator"
      >
        <TabsList className="mb-4">
          <TabsTrigger
            value="generator"
            className="flex items-center gap-1"
            onClick={() => {
              console.log("Generator tab clicked");
              setActiveTab("generator");
            }}
          >
            <Video className="h-4 w-4" />
            Generator Script
          </TabsTrigger>
          <TabsTrigger
            value="list"
            className="flex items-center gap-1"
            onClick={() => {
              console.log("List tab clicked");
              setActiveTab("list");
            }}
          >
            <List className="h-4 w-4" />
            Daftar Video
          </TabsTrigger>
          <TabsTrigger
            value="about"
            className="flex items-center gap-1"
            onClick={() => {
              console.log("About tab clicked");
              setActiveTab("about");
            }}
          >
            <Info className="h-4 w-4" />
            Tentang Fitur
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generator">
          <VideoScriptGenerator
            initialScript={editingScript}
            relatedContentId={contentData?.relatedContentId}
            onScriptSaved={handleScriptSaved}
            onResetEditor={handleResetEditor}
            {...(contentData && {
              videoType: contentData.videoType,
              purpose: contentData.purpose,
              targetAudience: contentData.targetAudience,
              platform: contentData.platform,
              productName: contentData.productName,
              additionalInfo: contentData.additionalInfo
            })}
            {...(editingForm && {
              videoType: editingForm.videoType,
              purpose: editingForm.purpose,
              targetAudience: editingForm.targetAudience,
              platform: editingForm.platform,
              productName: editingForm.productName,
              additionalInfo: editingForm.additionalInfo
            })}
          />
        </TabsContent>

        <TabsContent value="list">
          <Card>
            <CardHeader>
              <CardTitle>Daftar Form Video Script</CardTitle>
              <CardDescription>
                Kelola form video script yang telah dibuat
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SimpleVideoScriptList
                onNavigateToGenerator={() => navigateToTab('generator')}
                onLoadForm={handleFormSelected}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="about">
          <Card>
            <CardHeader>
              <CardTitle>Tentang Video Script Generator</CardTitle>
              <CardDescription>
                Fitur ini membantu Anda membuat script video profesional untuk berbagai kebutuhan marketing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Apa itu Video Script Generator?</h3>
                <p>
                  Video Script Generator adalah fitur yang membantu Anda membuat script video profesional untuk berbagai jenis video seperti marketing produk, dokumentasi, reels, jokes, review, testimoni, dan edukasi. Fitur ini menggunakan AI untuk menghasilkan script yang sesuai dengan tujuan, target audiens, dan platform yang Anda pilih.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Manfaat Menggunakan Fitur Ini</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Menghemat waktu dalam pembuatan script video</li>
                  <li>Mendapatkan struktur script yang profesional dan terorganisir</li>
                  <li>Mendapatkan rekomendasi shot, lokasi, dan objek visual yang sesuai</li>
                  <li>Meningkatkan kualitas konten video marketing</li>
                  <li>Mempercepat proses produksi video</li>
                  <li>Memudahkan komunikasi dengan tim produksi video</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Cara Menggunakan</h3>
                <ol className="list-decimal pl-5 space-y-1">
                  <li>Pilih jenis video yang ingin dibuat (Marketing Produk, Dokumentasi, Reels, dll)</li>
                  <li>Tentukan tujuan video (Promosi, Edukasi, Viral, Dokumentasi)</li>
                  <li>Pilih target audiens (Customer, Internal, Umum)</li>
                  <li>Pilih platform tujuan (Instagram, TikTok, YouTube, WhatsApp)</li>
                  <li>Masukkan nama produk jika relevan (opsional)</li>
                  <li>Tentukan durasi video dalam detik (opsional)</li>
                  <li>Tambahkan informasi tambahan yang relevan (opsional)</li>
                  <li>Klik "Buat Script Video" untuk menghasilkan script</li>
                  <li>Lihat hasil script yang terdiri dari judul, bagian-bagian script, rekomendasi shot, lokasi, dan objek visual</li>
                  <li>Edit script jika diperlukan dengan mengklik tombol "Edit"</li>
                  <li>Salin script ke clipboard atau ekspor ke PDF untuk dibagikan dengan tim produksi</li>
                </ol>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Jenis Video yang Didukung</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li><strong>Marketing Produk</strong>: Video untuk mempromosikan produk ban dan otomotif</li>
                  <li><strong>Dokumentasi</strong>: Video dokumentasi kegiatan, event, atau proses</li>
                  <li><strong>Reels</strong>: Video pendek untuk Instagram Reels atau TikTok</li>
                  <li><strong>Jokes/Humor</strong>: Video lucu atau menghibur</li>
                  <li><strong>Review</strong>: Video review produk atau layanan</li>
                  <li><strong>Testimoni</strong>: Video testimoni pelanggan</li>
                  <li><strong>Edukasi</strong>: Video edukasi tentang produk, industri, atau tips</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Struktur Script</h3>
                <p>Script yang dihasilkan akan terdiri dari beberapa bagian utama:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li><strong>Opening</strong>: Bagian pembuka untuk menarik perhatian penonton</li>
                  <li><strong>Masalah</strong>: Menjelaskan masalah atau kebutuhan yang dihadapi target audiens</li>
                  <li><strong>Solusi</strong>: Menjelaskan solusi yang ditawarkan oleh produk atau layanan</li>
                  <li><strong>CTA (Call to Action)</strong>: Mengajak penonton untuk melakukan tindakan tertentu</li>
                </ul>
                <p className="mt-2">Selain itu, script juga akan menyertakan:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li><strong>Rekomendasi Shot</strong>: Jenis shot yang disarankan untuk setiap bagian script</li>
                  <li><strong>Rekomendasi Lokasi</strong>: Lokasi yang disarankan untuk pengambilan gambar</li>
                  <li><strong>Rekomendasi Objek Visual</strong>: Objek yang disarankan untuk ditampilkan dalam video</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
