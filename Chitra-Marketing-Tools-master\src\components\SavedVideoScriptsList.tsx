import React, { useState, useEffect, useMemo } from 'react';
import { SavedVideoScript } from '../types/videoScript';
import {
  getAllSavedVideoScripts,
  deleteSavedVideoScript,
  getVideoTypeName,
  getPurposeName,
  getTargetAudienceName,
  getPlatformName
} from '../services/savedVideoScriptService';
import { useDebounce } from '../hooks/useDebounce';
import ScriptItem from './optimized/ScriptItem';
import { Button } from './ui/button';
import { Input } from './ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from './ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from './ui/dialog';
import {
  Eye,
  Edit,
  Trash2,
  MoreVertical,
  Download,
  Search,
  FileText,
  Calendar
} from 'lucide-react';
import { Badge } from './ui/badge';
import { useToast } from './ui/use-toast';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import jsPDF from 'jspdf';

interface SavedVideoScriptsListProps {
  onViewScript: (script: SavedVideoScript) => void;
  onEditScript: (script: SavedVideoScript) => void;
  onNavigateToGenerator?: () => void;
}

const SavedVideoScriptsList: React.FC<SavedVideoScriptsListProps> = ({
  onViewScript,
  onEditScript,
  onNavigateToGenerator
}) => {
  const [scripts, setScripts] = useState<SavedVideoScript[]>([]);
  const [filteredScripts, setFilteredScripts] = useState<SavedVideoScript[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [scriptToDelete, setScriptToDelete] = useState<SavedVideoScript | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [selectedScript, setSelectedScript] = useState<SavedVideoScript | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const { toast } = useToast();

  // Load saved scripts
  useEffect(() => {
    const loadScripts = async () => {
      try {
        const savedScripts = await getAllSavedVideoScripts();
        setScripts(savedScripts);
        setFilteredScripts(savedScripts);
      } catch (error) {
        console.error('Error loading saved scripts:', error);
        toast({
          title: 'Error',
          description: 'Gagal memuat daftar script video',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadScripts();
  }, [toast]);

  // Use debounced search term to prevent excessive filtering
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  // Filter scripts when debounced search term changes
  useEffect(() => {
    if (!debouncedSearchTerm.trim()) {
      setFilteredScripts(scripts);
      return;
    }

    const lowerSearchTerm = debouncedSearchTerm.toLowerCase();
    const filtered = scripts.filter(script =>
      script.title.toLowerCase().includes(lowerSearchTerm) ||
      getVideoTypeName(script.videoType).toLowerCase().includes(lowerSearchTerm) ||
      getPurposeName(script.purpose).toLowerCase().includes(lowerSearchTerm) ||
      (script.productName && script.productName.toLowerCase().includes(lowerSearchTerm))
    );

    setFilteredScripts(filtered);
    // Reset to first page when filtering
    setCurrentPage(1);
  }, [debouncedSearchTerm, scripts]);

  // Calculate pagination values
  const totalPages = Math.ceil(filteredScripts.length / itemsPerPage);

  // Get current page items
  const currentItems = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredScripts.slice(startIndex, endIndex);
  }, [filteredScripts, currentPage, itemsPerPage]);

  // Handle view script
  const handleViewScript = (script: SavedVideoScript) => {
    setSelectedScript(script);
    setViewDialogOpen(true);
  };

  // Handle edit script
  const handleEditScript = (script: SavedVideoScript) => {
    onEditScript(script);
  };

  // Handle delete script
  const handleDeleteClick = (script: SavedVideoScript) => {
    setScriptToDelete(script);
    setDeleteDialogOpen(true);
  };

  // Confirm delete script
  const handleConfirmDelete = async () => {
    if (!scriptToDelete) return;

    try {
      await deleteSavedVideoScript(scriptToDelete.id);

      // Update the scripts list
      const updatedScripts = scripts.filter(script => script.id !== scriptToDelete.id);
      setScripts(updatedScripts);
      setFilteredScripts(updatedScripts);

      toast({
        title: 'Script berhasil dihapus',
        description: `Script "${scriptToDelete.title}" telah dihapus`,
        variant: 'default',
      });
    } catch (error) {
      console.error('Error deleting script:', error);
      toast({
        title: 'Error',
        description: 'Gagal menghapus script video',
        variant: 'destructive',
      });
    } finally {
      setDeleteDialogOpen(false);
      setScriptToDelete(null);
    }
  };

  // Export script to PDF
  const handleExportPDF = (script: SavedVideoScript) => {
    const doc = new jsPDF();

    // Add title
    doc.setFontSize(18);
    doc.text(script.title, 20, 20);

    // Add metadata
    doc.setFontSize(10);
    doc.text(`Jenis: ${getVideoTypeName(script.videoType)} | Tujuan: ${getPurposeName(script.purpose)} | Platform: ${getPlatformName(script.platform)}`, 20, 30);
    if (script.productName) {
      doc.text(`Produk: ${script.productName}`, 20, 35);
    }

    // Add sections
    let y = 40;
    doc.setFontSize(12);

    script.sections.forEach(section => {
      doc.setFont(undefined, 'bold');
      doc.text(`${section.name.toUpperCase()} (${section.duration})`, 20, y);
      y += 7;

      doc.setFont(undefined, 'normal');
      const contentLines = doc.splitTextToSize(section.content, 170);
      doc.text(contentLines, 20, y);
      y += contentLines.length * 7 + 5;

      // Add timeline for this section if available
      if (section.timeline && section.timeline.length > 0) {
        doc.setFont(undefined, 'bold');
        doc.text('TIMELINE DETAIL:', 20, y);
        y += 7;

        doc.setFont(undefined, 'normal');
        section.timeline.forEach(segment => {
          // Check if we need a new page
          if (y > 250) {
            doc.addPage();
            y = 20;
          }

          doc.setFont(undefined, 'bold');
          doc.text(`[${segment.startTime} - ${segment.endTime}] ${segment.shotType}`, 20, y);
          y += 7;

          doc.setFont(undefined, 'normal');

          if (segment.dialogue) {
            const dialogText = `DIALOG (${segment.dialogue.speaker}): "${segment.dialogue.text}"`;
            const dialogLines = doc.splitTextToSize(dialogText, 160);
            doc.text(dialogLines, 25, y);
            y += dialogLines.length * 5;

            if (segment.dialogue.tone) {
              doc.text(`TONE: ${segment.dialogue.tone}`, 25, y);
              y += 5;
            }
          }

          const visualText = `VISUAL: ${segment.visual.description}`;
          const visualLines = doc.splitTextToSize(visualText, 160);
          doc.text(visualLines, 25, y);
          y += visualLines.length * 5;

          if (segment.music) {
            const musicText = `MUSIK: ${segment.music.type} - ${segment.music.description}`;
            const musicLines = doc.splitTextToSize(musicText, 160);
            doc.text(musicLines, 25, y);
            y += musicLines.length * 5;
          }

          if (segment.location) {
            doc.text(`LOKASI: ${segment.location}`, 25, y);
            y += 5;
          }

          if (segment.visualObjects && segment.visualObjects.length > 0) {
            const objText = `OBJEK: ${segment.visualObjects.join(', ')}`;
            const objLines = doc.splitTextToSize(objText, 160);
            doc.text(objLines, 25, y);
            y += objLines.length * 5;
          }

          if (segment.notes) {
            const notesText = `CATATAN: ${segment.notes}`;
            const notesLines = doc.splitTextToSize(notesText, 160);
            doc.text(notesLines, 25, y);
            y += notesLines.length * 5;
          }

          y += 5;
        });
      }

      // Add a new page if we're running out of space
      if (y > 270) {
        doc.addPage();
        y = 20;
      }
    });

    // Add overall timeline if available
    if (script.timeline && script.timeline.length > 0) {
      doc.addPage();
      y = 20;

      doc.setFont(undefined, 'bold');
      doc.text('TIMELINE KESELURUHAN:', 20, y);
      y += 10;

      doc.setFont(undefined, 'normal');
      script.timeline.forEach(segment => {
        // Check if we need a new page
        if (y > 250) {
          doc.addPage();
          y = 20;
        }

        doc.setFont(undefined, 'bold');
        doc.text(`[${segment.startTime} - ${segment.endTime}] ${segment.shotType}`, 20, y);
        y += 7;

        doc.setFont(undefined, 'normal');

        if (segment.dialogue) {
          const dialogText = `DIALOG (${segment.dialogue.speaker}): "${segment.dialogue.text}"`;
          const dialogLines = doc.splitTextToSize(dialogText, 160);
          doc.text(dialogLines, 25, y);
          y += dialogLines.length * 5;

          if (segment.dialogue.tone) {
            doc.text(`TONE: ${segment.dialogue.tone}`, 25, y);
            y += 5;
          }
        }

        const visualText = `VISUAL: ${segment.visual.description}`;
        const visualLines = doc.splitTextToSize(visualText, 160);
        doc.text(visualLines, 25, y);
        y += visualLines.length * 5;

        if (segment.music) {
          const musicText = `MUSIK: ${segment.music.type} - ${segment.music.description}`;
          const musicLines = doc.splitTextToSize(musicText, 160);
          doc.text(musicLines, 25, y);
          y += musicLines.length * 5;
        }

        if (segment.location) {
          doc.text(`LOKASI: ${segment.location}`, 25, y);
          y += 5;
        }

        if (segment.visualObjects && segment.visualObjects.length > 0) {
          const objText = `OBJEK: ${segment.visualObjects.join(', ')}`;
          const objLines = doc.splitTextToSize(objText, 160);
          doc.text(objLines, 25, y);
          y += objLines.length * 5;
        }

        if (segment.notes) {
          const notesText = `CATATAN: ${segment.notes}`;
          const notesLines = doc.splitTextToSize(notesText, 160);
          doc.text(notesLines, 25, y);
          y += notesLines.length * 5;
        }

        y += 5;
      });
    }

    // Add recommendations
    doc.addPage();
    y = 20;

    // Add shot recommendations
    doc.setFont(undefined, 'bold');
    doc.text('REKOMENDASI SHOT:', 20, y);
    y += 7;

    doc.setFont(undefined, 'normal');
    script.shotRecommendations.forEach(shot => {
      const shotText = `- ${shot.shotType}: ${shot.description} (${shot.duration || 'N/A'})`;
      const shotLines = doc.splitTextToSize(shotText, 170);
      doc.text(shotLines, 20, y);
      y += shotLines.length * 5;

      if (shot.cameraMovement) {
        doc.text(`  PERGERAKAN KAMERA: ${shot.cameraMovement}`, 20, y);
        y += 5;
      }

      if (shot.angle) {
        doc.text(`  SUDUT: ${shot.angle}`, 20, y);
        y += 5;
      }

      if (shot.lighting) {
        doc.text(`  PENCAHAYAAN: ${shot.lighting}`, 20, y);
        y += 5;
      }

      y += 2;

      if (y > 270) {
        doc.addPage();
        y = 20;
      }
    });

    y += 5;

    // Add location recommendations
    doc.setFont(undefined, 'bold');
    doc.text('REKOMENDASI LOKASI:', 20, y);
    y += 7;

    doc.setFont(undefined, 'normal');
    script.locationRecommendations.forEach(location => {
      const locationText = `- ${location.name}: ${location.description}`;
      const locationLines = doc.splitTextToSize(locationText, 170);
      doc.text(locationLines, 20, y);
      y += locationLines.length * 5;

      if (location.lighting) {
        doc.text(`  PENCAHAYAAN: ${location.lighting}`, 20, y);
        y += 5;
      }

      if (location.timeOfDay) {
        doc.text(`  WAKTU: ${location.timeOfDay}`, 20, y);
        y += 5;
      }

      if (location.weatherCondition) {
        doc.text(`  CUACA: ${location.weatherCondition}`, 20, y);
        y += 5;
      }

      y += 2;

      if (y > 270) {
        doc.addPage();
        y = 20;
      }
    });

    // Add music recommendations if available
    if (script.musicRecommendations && script.musicRecommendations.length > 0) {
      y += 5;

      doc.setFont(undefined, 'bold');
      doc.text('REKOMENDASI MUSIK:', 20, y);
      y += 7;

      doc.setFont(undefined, 'normal');
      script.musicRecommendations.forEach(music => {
        const musicText = `- ${music.type}: ${music.description}`;
        const musicLines = doc.splitTextToSize(musicText, 170);
        doc.text(musicLines, 20, y);
        y += musicLines.length * 5;

        if (music.startTime && music.endTime) {
          doc.text(`  WAKTU: ${music.startTime} - ${music.endTime}`, 20, y);
          y += 5;
        }

        if (music.mood) {
          doc.text(`  MOOD: ${music.mood}`, 20, y);
          y += 5;
        }

        if (music.tempo) {
          doc.text(`  TEMPO: ${music.tempo}`, 20, y);
          y += 5;
        }

        if (music.volume) {
          doc.text(`  VOLUME: ${music.volume}`, 20, y);
          y += 5;
        }

        y += 2;

        if (y > 270) {
          doc.addPage();
          y = 20;
        }
      });
    }

    // Add additional notes if any
    if (script.additionalNotes) {
      y += 5;

      doc.setFont(undefined, 'bold');
      doc.text('CATATAN TAMBAHAN:', 20, y);
      y += 7;

      doc.setFont(undefined, 'normal');
      const notesLines = doc.splitTextToSize(script.additionalNotes, 170);
      doc.text(notesLines, 20, y);
      y += notesLines.length * 5;
    }

    // Add total duration
    y += 5;
    doc.setFont(undefined, 'bold');
    doc.text(`Total Durasi: ${script.totalDuration || 'N/A'}`, 20, y);

    // Save the PDF
    doc.save(`video-script-${script.title.replace(/\s+/g, '-').toLowerCase()}.pdf`);

    toast({
      title: 'PDF berhasil dibuat',
      description: 'Script telah diekspor ke PDF dengan detail lengkap',
      variant: 'default',
    });
  };

  // Format date
  const formatDate = (date: Date) => {
    return format(date, 'dd MMM yyyy, HH:mm', { locale: id });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        {onNavigateToGenerator && (
          <Button
            variant="outline"
            size="sm"
            onClick={onNavigateToGenerator}
            className="flex items-center gap-1"
          >
            <Video className="h-4 w-4" />
            Buat Script Baru
          </Button>
        )}
        <div className="text-lg font-semibold">Daftar Script Video</div>
        <div className="text-sm text-gray-500">
          {filteredScripts.length} script ditemukan
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Cari script video..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"></div>
        </div>
      ) : filteredScripts.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <FileText className="h-16 w-16 text-gray-300 mb-4" />
          <h3 className="text-lg font-medium mb-2">Belum Ada Script</h3>
          <p className="text-gray-500 mb-4">Buat script video baru di tab Generator Script.</p>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Judul</TableHead>
                  <TableHead>Jenis</TableHead>
                  <TableHead>Tujuan</TableHead>
                  <TableHead>Platform</TableHead>
                  <TableHead>Dibuat</TableHead>
                  <TableHead className="text-right">Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {/* Use memoized current page items to prevent unnecessary re-renders */}
                {useMemo(() => {
                  return currentItems.map((script) => (
                    <ScriptItem
                      key={script.id}
                      script={script}
                      onViewScript={handleViewScript}
                      onEditScript={handleEditScript}
                      onDeleteScript={handleDeleteClick}
                      onExportPDF={handleExportPDF}
                    />
                  ));
                }, [currentItems])}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                disabled={currentPage === 1}
              >
                Sebelumnya
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show pages around current page
                  let pageToShow;
                  if (totalPages <= 5) {
                    pageToShow = i + 1;
                  } else if (currentPage <= 3) {
                    pageToShow = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageToShow = totalPages - 4 + i;
                  } else {
                    pageToShow = currentPage - 2 + i;
                  }

                  return (
                    <Button
                      key={pageToShow}
                      variant={currentPage === pageToShow ? "default" : "outline"}
                      size="sm"
                      className="w-8 h-8 p-0"
                      onClick={() => setCurrentPage(pageToShow)}
                    >
                      {pageToShow}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                disabled={currentPage === totalPages}
              >
                Selanjutnya
              </Button>

              <select
                className="ml-4 px-2 py-1 border rounded-md text-sm"
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value));
                  setCurrentPage(1); // Reset to first page when changing items per page
                }}
              >
                <option value={5}>5 per halaman</option>
                <option value={10}>10 per halaman</option>
                <option value={20}>20 per halaman</option>
                <option value={50}>50 per halaman</option>
              </select>
            </div>
          )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Script Video</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus script "{scriptToDelete?.title}"? Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Batal
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete}>
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Script Dialog */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="max-w-5xl max-h-[85vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedScript?.title}</DialogTitle>
            <DialogDescription>
              <div className="flex flex-wrap gap-2 mt-2">
                <Badge variant="outline">{selectedScript && getVideoTypeName(selectedScript.videoType)}</Badge>
                <Badge variant="outline">{selectedScript && getPurposeName(selectedScript.purpose)}</Badge>
                <Badge variant="outline">{selectedScript && getPlatformName(selectedScript.platform)}</Badge>
                {selectedScript?.productName && (
                  <Badge variant="outline">Produk: {selectedScript.productName}</Badge>
                )}
                {selectedScript?.totalDuration && (
                  <Badge variant="outline">Durasi: {selectedScript.totalDuration}</Badge>
                )}
              </div>
            </DialogDescription>
          </DialogHeader>

          {selectedScript && (
            <div className="space-y-6 mt-4">
              {/* Sections with Timeline */}
              {selectedScript.sections.map((section, index) => (
                <div key={index} className="border-b pb-4">
                  <h3 className="font-medium text-lg">{section.name} <span className="text-sm text-gray-500">({section.duration})</span></h3>
                  <p className="whitespace-pre-line mb-3">{section.content}</p>

                  {/* Timeline for this section */}
                  {section.timeline && section.timeline.length > 0 && (
                    <div className="mt-3 bg-gray-50 p-3 rounded-md">
                      <h4 className="font-medium text-md mb-2">Timeline Detail:</h4>
                      <div className="space-y-4">
                        {section.timeline.map((segment, segIndex) => (
                          <div key={segIndex} className="border-l-2 border-blue-400 pl-3 py-1">
                            <div className="flex items-center mb-1">
                              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded">
                                {segment.startTime} - {segment.endTime}
                              </span>
                              <span className="ml-2 bg-gray-100 text-gray-800 text-xs font-medium px-2 py-0.5 rounded">
                                {segment.shotType}
                              </span>
                            </div>

                            {segment.dialogue && (
                              <div className="mb-1 pl-2">
                                <p className="text-sm">
                                  <span className="font-medium">Dialog ({segment.dialogue.speaker}):</span> "{segment.dialogue.text}"
                                  {segment.dialogue.tone && <span className="text-gray-500 text-xs ml-1"> - Tone: {segment.dialogue.tone}</span>}
                                </p>
                              </div>
                            )}

                            <div className="mb-1 pl-2">
                              <p className="text-sm">
                                <span className="font-medium">Visual:</span> {segment.visual.description}
                                {segment.visual.transition && <span className="text-gray-500 text-xs ml-1"> - Transition: {segment.visual.transition}</span>}
                                {segment.visual.effects && <span className="text-gray-500 text-xs ml-1"> - Effects: {segment.visual.effects}</span>}
                              </p>
                            </div>

                            {segment.music && (
                              <div className="mb-1 pl-2">
                                <p className="text-sm">
                                  <span className="font-medium">Music:</span> {segment.music.type} - {segment.music.description}
                                  {segment.music.volume && <span className="text-gray-500 text-xs ml-1"> - Volume: {segment.music.volume}</span>}
                                </p>
                              </div>
                            )}

                            {segment.location && (
                              <div className="mb-1 pl-2">
                                <p className="text-sm">
                                  <span className="font-medium">Location:</span> {segment.location}
                                </p>
                              </div>
                            )}

                            {segment.visualObjects && segment.visualObjects.length > 0 && (
                              <div className="mb-1 pl-2">
                                <p className="text-sm">
                                  <span className="font-medium">Objects:</span> {segment.visualObjects.join(', ')}
                                </p>
                              </div>
                            )}

                            {segment.notes && (
                              <div className="mb-1 pl-2">
                                <p className="text-sm">
                                  <span className="font-medium">Notes:</span> {segment.notes}
                                </p>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {/* Overall Timeline */}
              {selectedScript.timeline && selectedScript.timeline.length > 0 && (
                <div className="border-b pb-4">
                  <h3 className="font-medium text-lg mb-3">Timeline Keseluruhan</h3>
                  <div className="bg-gray-50 p-3 rounded-md">
                    <div className="space-y-4">
                      {selectedScript.timeline.map((segment, segIndex) => (
                        <div key={segIndex} className="border-l-2 border-green-400 pl-3 py-1">
                          <div className="flex items-center mb-1">
                            <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded">
                              {segment.startTime} - {segment.endTime}
                            </span>
                            <span className="ml-2 bg-gray-100 text-gray-800 text-xs font-medium px-2 py-0.5 rounded">
                              {segment.shotType}
                            </span>
                          </div>

                          {segment.dialogue && (
                            <div className="mb-1 pl-2">
                              <p className="text-sm">
                                <span className="font-medium">Dialog ({segment.dialogue.speaker}):</span> "{segment.dialogue.text}"
                                {segment.dialogue.tone && <span className="text-gray-500 text-xs ml-1"> - Tone: {segment.dialogue.tone}</span>}
                              </p>
                            </div>
                          )}

                          <div className="mb-1 pl-2">
                            <p className="text-sm">
                              <span className="font-medium">Visual:</span> {segment.visual.description}
                              {segment.visual.transition && <span className="text-gray-500 text-xs ml-1"> - Transition: {segment.visual.transition}</span>}
                              {segment.visual.effects && <span className="text-gray-500 text-xs ml-1"> - Effects: {segment.visual.effects}</span>}
                            </p>
                          </div>

                          {segment.music && (
                            <div className="mb-1 pl-2">
                              <p className="text-sm">
                                <span className="font-medium">Music:</span> {segment.music.type} - {segment.music.description}
                                {segment.music.volume && <span className="text-gray-500 text-xs ml-1"> - Volume: {segment.music.volume}</span>}
                              </p>
                            </div>
                          )}

                          {segment.location && (
                            <div className="mb-1 pl-2">
                              <p className="text-sm">
                                <span className="font-medium">Location:</span> {segment.location}
                              </p>
                            </div>
                          )}

                          {segment.visualObjects && segment.visualObjects.length > 0 && (
                            <div className="mb-1 pl-2">
                              <p className="text-sm">
                                <span className="font-medium">Objects:</span> {segment.visualObjects.join(', ')}
                              </p>
                            </div>
                          )}

                          {segment.notes && (
                            <div className="mb-1 pl-2">
                              <p className="text-sm">
                                <span className="font-medium">Notes:</span> {segment.notes}
                              </p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Music Recommendations */}
              {selectedScript.musicRecommendations && selectedScript.musicRecommendations.length > 0 && (
                <div>
                  <h3 className="font-medium text-lg mb-2">Rekomendasi Musik</h3>
                  <ul className="list-disc pl-5 space-y-2">
                    {selectedScript.musicRecommendations.map((music, index) => (
                      <li key={index} className="pb-1">
                        <div>
                          <span className="font-medium">{music.type}</span>: {music.description}
                        </div>
                        {(music.startTime || music.endTime) && (
                          <div className="text-sm text-gray-600 ml-1">
                            Waktu: {music.startTime || '0:00'} - {music.endTime || 'end'}
                          </div>
                        )}
                        {music.mood && (
                          <div className="text-sm text-gray-600 ml-1">
                            Mood: {music.mood}
                          </div>
                        )}
                        {music.tempo && (
                          <div className="text-sm text-gray-600 ml-1">
                            Tempo: {music.tempo}
                          </div>
                        )}
                        {music.volume && (
                          <div className="text-sm text-gray-600 ml-1">
                            Volume: {music.volume}
                          </div>
                        )}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Shot Recommendations */}
              <div>
                <h3 className="font-medium text-lg mb-2">Rekomendasi Shot</h3>
                <ul className="list-disc pl-5 space-y-2">
                  {selectedScript.shotRecommendations.map((shot, index) => (
                    <li key={index} className="pb-1">
                      <div>
                        <span className="font-medium">{shot.shotType}</span>: {shot.description} {shot.duration && `(${shot.duration})`}
                      </div>
                      {shot.cameraMovement && (
                        <div className="text-sm text-gray-600 ml-1">
                          Pergerakan Kamera: {shot.cameraMovement}
                        </div>
                      )}
                      {shot.angle && (
                        <div className="text-sm text-gray-600 ml-1">
                          Sudut: {shot.angle}
                        </div>
                      )}
                      {shot.lighting && (
                        <div className="text-sm text-gray-600 ml-1">
                          Pencahayaan: {shot.lighting}
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Location Recommendations */}
              <div>
                <h3 className="font-medium text-lg mb-2">Rekomendasi Lokasi</h3>
                <ul className="list-disc pl-5 space-y-2">
                  {selectedScript.locationRecommendations.map((location, index) => (
                    <li key={index} className="pb-1">
                      <div>
                        <span className="font-medium">{location.name}</span>: {location.description}
                      </div>
                      {location.lighting && (
                        <div className="text-sm text-gray-600 ml-1">
                          Pencahayaan: {location.lighting}
                        </div>
                      )}
                      {location.timeOfDay && (
                        <div className="text-sm text-gray-600 ml-1">
                          Waktu: {location.timeOfDay}
                        </div>
                      )}
                      {location.weatherCondition && (
                        <div className="text-sm text-gray-600 ml-1">
                          Cuaca: {location.weatherCondition}
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Visual Object Recommendations */}
              <div>
                <h3 className="font-medium text-lg mb-2">Rekomendasi Objek Visual</h3>
                <ul className="list-disc pl-5 space-y-2">
                  {selectedScript.visualObjectRecommendations.map((obj, index) => (
                    <li key={index} className="pb-1">
                      <div>
                        <span className="font-medium">{obj.name}</span>: {obj.purpose}
                      </div>
                      {obj.placement && (
                        <div className="text-sm text-gray-600 ml-1">
                          Penempatan: {obj.placement}
                        </div>
                      )}
                      {obj.interaction && (
                        <div className="text-sm text-gray-600 ml-1">
                          Interaksi: {obj.interaction}
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Additional Notes */}
              {selectedScript.additionalNotes && (
                <div>
                  <h3 className="font-medium text-lg mb-2">Catatan Tambahan</h3>
                  <p className="whitespace-pre-line">{selectedScript.additionalNotes}</p>
                </div>
              )}

              {/* Total Duration */}
              <div className="pt-2">
                <p className="text-sm font-medium">Total Durasi: {selectedScript.totalDuration || 'N/A'}</p>
              </div>
            </div>
          )}

          <DialogFooter className="mt-6">
            <Button variant="outline" onClick={() => setViewDialogOpen(false)}>
              Tutup
            </Button>
            <Button variant="outline" onClick={() => selectedScript && handleExportPDF(selectedScript)}>
              <Download className="h-4 w-4 mr-2" />
              Export PDF
            </Button>
            <Button onClick={() => {
              setViewDialogOpen(false);
              if (selectedScript) {
                onEditScript(selectedScript);
              }
            }}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SavedVideoScriptsList;
