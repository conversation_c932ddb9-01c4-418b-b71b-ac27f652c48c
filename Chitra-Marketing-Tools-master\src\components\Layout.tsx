import { useState, useEffect } from 'react';
import { Outlet, NavLink, useNavigate } from 'react-router-dom';
import { Grid3x3, Package, User, Users, BarChart3, Menu, X, Calculator, Truck, ChevronDown, ChevronRight, FileText, Brain, Book, Lightbulb, FileUp, ChevronsLeft, ChevronsRight, Percent, MessageSquare, Database, MessageCircle, FolderOpen, LineChart, FileSpreadsheet, Megaphone, Sparkles, Calendar, Gift, Instagram, Video, ImageIcon, Camera, Activity, Mic } from 'lucide-react';
import { authService } from '../services/authService';

import DataHubStatus from './DataHubStatus';

export default function Layout() {
  const navigate = useNavigate();
  const [username, setUsername] = useState<string>('');
  const [mobileMenuOpen, setMobileMenuOpen] = useState<boolean>(false);
  const [calculatorMenuOpen, setCalculatorMenuOpen] = useState<boolean>(false);
  const [dataManagementMenuOpen, setDataManagementMenuOpen] = useState<boolean>(false);
  const [analysisMenuOpen, setAnalysisMenuOpen] = useState<boolean>(false);
  const [marketingMenuOpen, setMarketingMenuOpen] = useState<boolean>(false);
  const [aiToolsMenuOpen, setAiToolsMenuOpen] = useState<boolean>(false);
  const [socialMediaMenuOpen, setSocialMediaMenuOpen] = useState<boolean>(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);
  const [windowWidth, setWindowWidth] = useState<number>(window.innerWidth);

  // Get current path to determine active state
  const currentPath = window.location.pathname;

  useEffect(() => {
    // Load Google Fonts
    const link = document.createElement('link');
    link.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap';
    link.rel = 'stylesheet';
    document.head.appendChild(link);

    // Update document title
    document.title = 'Chitra Marketing Tools';

    // Get current user
    const currentUser = authService.getCurrentUser();
    if (currentUser) {
      setUsername(currentUser.username);
    }

    // Handle window resize
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      if (window.innerWidth > 768) {
        setMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleLogout = () => {
    authService.logout();
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex" style={{ fontFamily: 'Inter, sans-serif' }}>
      {/* Mobile backdrop overlay */}
      {mobileMenuOpen && windowWidth <= 768 && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={`bg-white shadow-md fixed inset-y-0 left-0 z-30 sidebar-transition ${
          sidebarCollapsed ? 'w-16' : 'w-64'
        } ${mobileMenuOpen ? 'sidebar-mobile open' : 'sidebar-mobile md:transform-none'}`}
      >
        {/* Sidebar Header */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
          <div className="flex items-center">
            <img src="/assets/cp_logo.png" alt="Chitra Paratama Logo" className="h-8" />
            {!sidebarCollapsed && (
              <h1 className="ml-3 text-lg font-bold text-gray-900 truncate">Chitra Tools</h1>
            )}
          </div>
          <button
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="p-1 rounded-md text-gray-500 hover:bg-gray-100"
          >
            {sidebarCollapsed ? <ChevronsRight size={20} /> : <ChevronsLeft size={20} />}
          </button>
        </div>

        {/* Sidebar Navigation */}
        <nav className="mt-2 px-2 overflow-y-auto h-[calc(100vh-4rem)]">
          <div className="space-y-1">
            {/* Dashboard Menu Item */}
            <NavLink
              to="/analytics-dashboard"
              className={({ isActive }) =>
                `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                  isActive
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100'
                }`
              }
            >
              <BarChart3 size={18} className={sidebarCollapsed ? '' : 'mr-2'} />
              {!sidebarCollapsed && <span>Dashboard</span>}
            </NavLink>

            {/* 1. Kalkulator & Simulasi Menu Group */}
            <div>
              <button
                className={`w-full flex justify-between items-center px-3 py-2 text-sm font-medium rounded-md ${
                  currentPath === '/' || currentPath === '/bundling-calculator' || currentPath === '/zero-margin-bundling' || currentPath === '/promo-simulation'
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setCalculatorMenuOpen(!calculatorMenuOpen)}
              >
                <div className="flex items-center">
                  <Calculator size={18} className={sidebarCollapsed ? '' : 'mr-2'} />
                  {!sidebarCollapsed && <span>Kalkulator & Simulasi</span>}
                </div>
                {!sidebarCollapsed && (
                  calculatorMenuOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />
                )}
              </button>

              {/* Calculator Submenu */}
              {calculatorMenuOpen && (
                <div className={`mt-1 space-y-1 ${sidebarCollapsed ? '' : 'pl-6'}`}>
                  <NavLink
                    to="/ban-27-bundling"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Truck size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>27.00 R 49 Bundling</span>}
                  </NavLink>
                  <NavLink
                    to="/bundling-calculator"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Calculator size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Slow Moving Bundling</span>}
                  </NavLink>

                  <NavLink
                    to="/zero-margin-bundling"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Gift size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>27.00R49 0% + Bonus</span>}
                  </NavLink>

                  <NavLink
                    to="/promo-simulation"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Percent size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Simulasi Promo Bisnis</span>}
                  </NavLink>
                </div>
              )}
            </div>

            {/* 2. Manajemen Data Menu Group */}
            <div>
              <button
                className={`w-full flex justify-between items-center px-3 py-2 text-sm font-medium rounded-md ${
                  currentPath === '/products' || currentPath === '/customers' || currentPath === '/template-management' || currentPath === '/sales-revenue-2025-data-master' || currentPath === '/coal-price-data' || currentPath === '/proposal-builder'
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setDataManagementMenuOpen(!dataManagementMenuOpen)}
              >
                <div className="flex items-center">
                  <FolderOpen size={18} className={sidebarCollapsed ? '' : 'mr-2'} />
                  {!sidebarCollapsed && <span>Manajemen Data</span>}
                </div>
                {!sidebarCollapsed && (
                  dataManagementMenuOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />
                )}
              </button>

              {/* Data Management Submenu */}
              {dataManagementMenuOpen && (
                <div className={`mt-1 space-y-1 ${sidebarCollapsed ? '' : 'pl-6'}`}>
                  <NavLink
                    to="/products"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Package size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Product Management</span>}
                  </NavLink>
                  <NavLink
                    to="/customers"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Users size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Customers</span>}
                  </NavLink>
                  <NavLink
                    to="/coal-price-data"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <BarChart3 size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Data Harga Batu Bara</span>}
                  </NavLink>
                  <NavLink
                    to="/template-management"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <FileUp size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Template Management</span>}
                  </NavLink>
                  <NavLink
                    to="/sales-revenue-2025-data-master"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <FileSpreadsheet size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Sales Revenue 2025 Data Master</span>}
                  </NavLink>
                  <NavLink
                    to="/proposal-builder"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <FileText size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Proposal Builder</span>}
                  </NavLink>
                </div>
              )}
            </div>

            {/* 3. Analisis & Dashboard Menu Group */}
            <div>
              <button
                className={`w-full flex justify-between items-center px-3 py-2 text-sm font-medium rounded-md ${
                  currentPath === '/sales-dashboard' || currentPath === '/fleet-analyzer' || currentPath === '/analytics-dashboard' || currentPath === '/customer-analysis' || currentPath === '/product-analysis' || currentPath === '/whatsapp-chat-analysis' || currentPath === '/whatsapp-web-analysis' || currentPath === '/whatsapp-chat-results'
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setAnalysisMenuOpen(!analysisMenuOpen)}
              >
                <div className="flex items-center">
                  <LineChart size={18} className={sidebarCollapsed ? '' : 'mr-2'} />
                  {!sidebarCollapsed && <span>Analisis & Dashboard</span>}
                </div>
                {!sidebarCollapsed && (
                  analysisMenuOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />
                )}
              </button>

              {/* Analysis & Dashboard Submenu */}
              {analysisMenuOpen && (
                <div className={`mt-1 space-y-1 ${sidebarCollapsed ? '' : 'pl-6'}`}>
                  <NavLink
                    to="/analytics-dashboard"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <BarChart3 size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Dashboard Analitik Terpadu</span>}
                  </NavLink>
                  <NavLink
                    to="/sales-dashboard"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <BarChart3 size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Sales Dashboard</span>}
                  </NavLink>
                  <NavLink
                    to="/fleet-analyzer"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Brain size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Fleet Analyzer</span>}
                  </NavLink>
                  <NavLink
                    to="/customer-analysis"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Users size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Analisis Pelanggan</span>}
                  </NavLink>
                  <NavLink
                    to="/product-analysis"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Package size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Analisis Produk</span>}
                  </NavLink>
                </div>
              )}
            </div>

            {/* 4. Marketing & Proposal Menu Group */}
            <div>
              <button
                className={`w-full flex justify-between items-center px-3 py-2 text-sm font-medium rounded-md ${
                  currentPath === '/bundling-proposal' || currentPath === '/marketing-ban-tips' || currentPath === '/inspirational-sales-stories' || currentPath === '/seasonal-marketing-calendar'
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setMarketingMenuOpen(!marketingMenuOpen)}
              >
                <div className="flex items-center">
                  <Megaphone size={18} className={sidebarCollapsed ? '' : 'mr-2'} />
                  {!sidebarCollapsed && <span>Marketing & Proposal</span>}
                </div>
                {!sidebarCollapsed && (
                  marketingMenuOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />
                )}
              </button>

              {/* Marketing & Proposal Submenu */}
              {marketingMenuOpen && (
                <div className={`mt-1 space-y-1 ${sidebarCollapsed ? '' : 'pl-6'}`}>
                  <NavLink
                    to="/seasonal-marketing-calendar"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Calendar size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Kalender Pemasaran Musiman</span>}
                  </NavLink>
                  <NavLink
                    to="/bundling-proposal"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <FileText size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Bundling Proposal</span>}
                  </NavLink>
                  <NavLink
                    to="/marketing-insights"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Sparkles size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Marketing Insights Hub</span>}
                  </NavLink>
                </div>
              )}
            </div>

            {/* 5. Social Media Marketing Menu Group */}
            <div>
              <button
                className={`w-full flex justify-between items-center px-3 py-2 text-sm font-medium rounded-md ${
                  currentPath === '/social-media-marketing'
                    ? 'bg-blue-50 text-blue-700'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                onClick={() => setSocialMediaMenuOpen(!socialMediaMenuOpen)}
              >
                <div className="flex items-center">
                  <Instagram size={18} className={sidebarCollapsed ? '' : 'mr-2'} />
                  {!sidebarCollapsed && <span>Social Media Marketing</span>}
                </div>
                {!sidebarCollapsed && (
                  socialMediaMenuOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />
                )}
              </button>

              {/* Social Media Marketing Submenu */}
              {socialMediaMenuOpen && (
                <div className={`mt-1 space-y-1 ${sidebarCollapsed ? '' : 'pl-6'}`}>
                  <NavLink
                    to="/social-media-marketing"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Instagram size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Instagram Management</span>}
                  </NavLink>
                  <NavLink
                    to="/monthly-content-plan"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Calendar size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Rencana Konten Bulanan</span>}
                  </NavLink>
                  <NavLink
                    to="/video-script-generator"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Video size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Video Script Generator</span>}
                  </NavLink>
                  <NavLink
                    to="/instagram-analysis"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <BarChart3 size={16} className={sidebarCollapsed ? '' : 'mr-2'} />
                    {!sidebarCollapsed && <span>Instagram Analysis</span>}
                  </NavLink>
                </div>
              )}
            </div>

            {/* 6. AI Tools Menu Group */}
            <div>
              <button
                onClick={() => setAiToolsMenuOpen(!aiToolsMenuOpen)}
                className="flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100 rounded-md"
              >
                <div className="flex items-center">
                  <Brain size={18} className={sidebarCollapsed ? '' : 'mr-2'} />
                  {!sidebarCollapsed && <span>AI Tools</span>}
                </div>
                {!sidebarCollapsed && (
                  <ChevronDown
                    size={16}
                    className={`transform transition-transform ${
                      aiToolsMenuOpen ? 'rotate-180' : ''
                    }`}
                  />
                )}
              </button>

              {aiToolsMenuOpen && !sidebarCollapsed && (
                <div className="pl-4 space-y-1">
                  <NavLink
                    to="/negotiation-simulator"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <MessageSquare size={16} className="mr-2" />
                    <span>AI Negotiation Simulator</span>
                  </NavLink>
                  <NavLink
                    to="/whatsapp-chat-analysis"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <MessageCircle size={16} className="mr-2" />
                    <span>Analisis Chat WhatsApp</span>
                  </NavLink>
                  <NavLink
                    to="/knowledge-base"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Database size={16} className="mr-2" />
                    <span>Knowledge Base</span>
                  </NavLink>
                  <NavLink
                    to="/proposal-analyzer"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <FileText size={16} className="mr-2" />
                    <span>Proposal Analyzer</span>
                  </NavLink>
                  <NavLink
                    to="/image-generator"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <ImageIcon size={16} className="mr-2" />
                    <span>Image Generator</span>
                  </NavLink>
                  <NavLink
                    to="/swot-analysis"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Activity size={16} className="mr-2" />
                    <span>SWOT Analysis AI</span>
                  </NavLink>
                  <NavLink
                    to="/presentation-analyzer"
                    className={({ isActive }) =>
                      `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                        isActive
                          ? 'bg-purple-100 text-purple-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`
                    }
                  >
                    <Mic size={18} className="mr-2" />
                    <span>Presentation Analyzer</span>
                  </NavLink>
                </div>
              )}
            </div>
            {/* Style Guide Link (for developers) */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <NavLink
                to="/style-guide"
                className={({ isActive }) =>
                  `flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                    isActive
                      ? 'bg-purple-100 text-purple-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`
                }
              >
                <Grid3x3 size={18} className={sidebarCollapsed ? '' : 'mr-2'} />
                {!sidebarCollapsed && <span>Design System</span>}
              </NavLink>
            </div>
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <div className={`flex-1 content-transition ${sidebarCollapsed ? 'ml-16' : 'ml-64'} md:ml-0 relative z-10`} style={{ paddingLeft: windowWidth > 768 ? (sidebarCollapsed ? '4rem' : '16rem') : '0' }}>
        {/* Toast notifications are handled by ToastProvider */}

        {/* Top Header */}
        <header className="bg-white shadow-sm sticky top-0 z-10">
          <div className="px-4 py-4 flex items-center justify-between">
            <div className="flex items-center md:hidden">
              <button
                onClick={() => {
                  setMobileMenuOpen(!mobileMenuOpen);
                  // Close submenu dropdowns when toggling mobile menu
                  if (!mobileMenuOpen) {
                    setCalculatorMenuOpen(false);
                    setDataManagementMenuOpen(false);
                    setAnalysisMenuOpen(false);
                    setMarketingMenuOpen(false);
                    setSocialMediaMenuOpen(false);
                    setAiToolsMenuOpen(false);
                  }
                }}
                className="text-gray-600 hover:text-gray-900 focus:outline-none"
              >
                {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
              </button>
            </div>
            <div className="flex items-center">
              <DataHubStatus compact={true} />
            </div>
            <div className="flex items-center ml-auto">
              <div className="flex items-center">
                <User size={16} className="text-gray-500 mr-1" />
                <span className="text-sm font-medium text-gray-700">Chitra User</span>
              </div>
              {/* Logout button temporarily hidden
              <button
                onClick={handleLogout}
                className="flex items-center text-sm font-medium text-gray-600 hover:text-red-600"
              >
                <LogOut size={16} className="mr-1" />
                Logout
              </button>
              */}
            </div>
          </div>
        </header>

        <main className="px-4 sm:px-6 py-8">
          <Outlet />
        </main>

        <footer className="bg-white border-t mt-12 py-6">
          <div className="px-4 sm:px-6">
            <p className="text-center text-gray-500 text-sm">
              Chitra Marketing Tools — Built to help sales teams create perfect bundles with optimal pricing
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
}

