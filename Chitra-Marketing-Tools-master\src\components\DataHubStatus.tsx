import React from 'react';
import { useDataHub } from './DataHubProvider';
import { Database, RefreshCw, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { Button } from './ui/button';

interface DataHubStatusProps {
  showRefreshButton?: boolean;
  compact?: boolean;
}

/**
 * Komponen untuk menampilkan status Data Hub
 */
export default function DataHubStatus({ showRefreshButton = true, compact = false }: DataHubStatusProps) {
  const { isInitialized, isLoading, lastUpdated, refreshData } = useDataHub();

  // Format waktu terakhir diperbarui
  const formatLastUpdated = (date: Date | null) => {
    if (!date) return 'Belum pernah';
    
    // Jika kurang dari 1 menit yang lalu
    const diffMs = Date.now() - date.getTime();
    if (diffMs < 60000) {
      return 'Baru saja';
    }
    
    // Jika kurang dari 1 jam yang lalu
    if (diffMs < 3600000) {
      const minutes = Math.floor(diffMs / 60000);
      return `${minutes} menit yang lalu`;
    }
    
    // Jika kurang dari 24 jam yang lalu
    if (diffMs < 86400000) {
      const hours = Math.floor(diffMs / 3600000);
      return `${hours} jam yang lalu`;
    }
    
    // Jika lebih dari 24 jam yang lalu
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Mendapatkan status keseluruhan
  const getOverallStatus = () => {
    if (!isInitialized) return 'initializing';
    
    const loadingStates = Object.values(isLoading);
    if (loadingStates.some(state => state)) return 'loading';
    
    const updatedStates = Object.values(lastUpdated);
    if (updatedStates.some(date => !date)) return 'partial';
    
    return 'ready';
  };

  // Mendapatkan ikon status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'initializing':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'loading':
        return <RefreshCw className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'partial':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'ready':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      default:
        return <Database className="h-5 w-5 text-gray-500" />;
    }
  };

  // Mendapatkan teks status
  const getStatusText = (status: string) => {
    switch (status) {
      case 'initializing':
        return 'Menginisialisasi Data Hub...';
      case 'loading':
        return 'Memuat data...';
      case 'partial':
        return 'Sebagian data tersedia';
      case 'ready':
        return 'Data Hub siap';
      default:
        return 'Status tidak diketahui';
    }
  };

  // Mendapatkan warna status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'initializing':
        return 'bg-yellow-100 text-yellow-800';
      case 'loading':
        return 'bg-blue-100 text-blue-800';
      case 'partial':
        return 'bg-yellow-100 text-yellow-800';
      case 'ready':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const status = getOverallStatus();

  // Tampilan kompak
  if (compact) {
    return (
      <div className="flex items-center space-x-2">
        {getStatusIcon(status)}
        <span className={`text-xs font-medium ${status === 'ready' ? 'text-green-600' : 'text-gray-600'}`}>
          Data Hub: {getStatusText(status)}
        </span>
        {showRefreshButton && (
          <Button
            size="sm"
            variant="ghost"
            className="h-6 w-6 p-0"
            onClick={() => refreshData()}
            disabled={status === 'loading'}
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  }

  // Tampilan lengkap
  return (
    <div className="bg-white rounded-lg border shadow-sm p-4">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <Database className="h-5 w-5 text-blue-600 mr-2" />
          <h3 className="font-medium">Status Data Hub</h3>
        </div>
        {showRefreshButton && (
          <Button
            size="sm"
            variant="outline"
            className="flex items-center"
            onClick={() => refreshData()}
            disabled={status === 'loading'}
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh Data
          </Button>
        )}
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Status:</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
            {getStatusText(status)}
          </span>
        </div>

        <div className="border-t pt-2">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Terakhir Diperbarui:</h4>
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Produk:</span>
              <span className="font-medium">{formatLastUpdated(lastUpdated.products)}</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Pelanggan:</span>
              <span className="font-medium">{formatLastUpdated(lastUpdated.customers)}</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Data Armada:</span>
              <span className="font-medium">{formatLastUpdated(lastUpdated.fleetData)}</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Harga Batu Bara:</span>
              <span className="font-medium">{formatLastUpdated(lastUpdated.coalPrices)}</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Hari Libur:</span>
              <span className="font-medium">{formatLastUpdated(lastUpdated.holidays)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
