/**
 * Types for the SWOT Analysis AI feature
 */

import { Product } from './index';
import { FleetlistItem } from '../services/fleetlistService';
import { SalesRevenueItem } from '../services/salesRevenue2025Service';

/**
 * SWOT Analysis Request
 */
export interface SwotAnalysisRequest {
  companyName: string;
  includeProductData: boolean;
  includeFleetData: boolean;
  includeSalesData: boolean;
  includeKnowledgeBase: boolean;
  manualStrengths: ManualSwotItem[];
  manualWeaknesses: ManualSwotItem[];
  manualOpportunities: ManualSwotItem[];
  manualThreats: ManualSwotItem[];
  customPrompt?: string;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
}

/**
 * Manual SWOT Item Input
 */
export interface ManualSwotItem {
  id: string;
  title: string;
  description: string;
}

/**
 * SWOT Analysis Result
 */
export interface SwotAnalysisResult {
  id: string;
  timestamp: number;
  companyName: string;
  strengths: SwotItem[];
  weaknesses: SwotItem[];
  opportunities: SwotItem[];
  threats: SwotItem[];
  insights: SwotInsight[];
  recommendations: SwotRecommendation[];
  dataSourcesUsed: {
    productData: boolean;
    fleetData: boolean;
    salesData: boolean;
    knowledgeBase: boolean;
  };
  manualInputUsed: boolean;
}

/**
 * SWOT Item
 */
export interface SwotItem {
  id: string;
  title: string;
  description: string;
  impactScore: number; // 1-10
  category: SwotCategory;
  relatedTo?: string[]; // Related to which aspects
  dataSource?: SwotDataSource;
}

/**
 * SWOT Insight
 */
export interface SwotInsight {
  id: string;
  title: string;
  description: string;
  category: SwotInsightCategory;
  relatedSwotItems: string[]; // IDs of related SWOT items
}

/**
 * SWOT Recommendation
 */
export interface SwotRecommendation {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  implementationTimeframe: 'short-term' | 'medium-term' | 'long-term';
  relatedInsights: string[]; // IDs of related insights
  expectedOutcome: string;
}

/**
 * SWOT Category
 */
export enum SwotCategory {
  STRENGTH = 'strength',
  WEAKNESS = 'weakness',
  OPPORTUNITY = 'opportunity',
  THREAT = 'threat'
}

/**
 * SWOT Insight Category
 */
export enum SwotInsightCategory {
  MARKET_TREND = 'market_trend',
  COMPETITIVE_ADVANTAGE = 'competitive_advantage',
  OPERATIONAL_EFFICIENCY = 'operational_efficiency',
  CUSTOMER_BEHAVIOR = 'customer_behavior',
  PRODUCT_PERFORMANCE = 'product_performance',
  DISTRIBUTION = 'distribution',
  PRICING = 'pricing',
  OTHER = 'other'
}

/**
 * SWOT Data Source
 */
export enum SwotDataSource {
  PRODUCT_DATA = 'product_data',
  FLEET_DATA = 'fleet_data',
  SALES_DATA = 'sales_data',
  KNOWLEDGE_BASE = 'knowledge_base',
  MANUAL_INPUT = 'manual_input',
  AI_ANALYSIS = 'ai_analysis'
}

/**
 * Integrated SWOT Data
 */
export interface IntegratedSwotData {
  companyData: {
    products: Product[];
    fleetData: FleetlistItem[];
    salesData: SalesRevenueItem[];
  };
  knowledgeBaseData?: any[];
  manualInput?: {
    strengths: ManualSwotItem[];
    weaknesses: ManualSwotItem[];
    opportunities: ManualSwotItem[];
    threats: ManualSwotItem[];
  };
}
