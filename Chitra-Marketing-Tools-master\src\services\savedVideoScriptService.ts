/**
 * Service for managing saved video scripts
 */
import {
  SavedVideoScript,
  VideoScriptResponse,
  VideoScriptRequest,
  VideoType,
  VideoPurpose,
  VideoTargetAudience,
  VideoPlatform
} from '../types/videoScript';

// LocalStorage key for saved video scripts
const SAVED_VIDEO_SCRIPTS_KEY = 'saved_video_scripts';

/**
 * Simple compression function for strings
 */
const compressString = (str: string): string => {
  // Basic compression by replacing common patterns
  // This is a simple implementation without external libraries
  return str
    .replace(/"timeline":\[/g, '"t":[')
    .replace(/"shotType"/g, '"st"')
    .replace(/"dialogue"/g, '"d"')
    .replace(/"speaker"/g, '"sp"')
    .replace(/"text"/g, '"tx"')
    .replace(/"tone"/g, '"tn"')
    .replace(/"visual"/g, '"v"')
    .replace(/"description"/g, '"ds"')
    .replace(/"transition"/g, '"tr"')
    .replace(/"effects"/g, '"ef"')
    .replace(/"music"/g, '"m"')
    .replace(/"volume"/g, '"vl"')
    .replace(/"location"/g, '"lc"')
    .replace(/"visualObjects"/g, '"vo"')
    .replace(/"notes"/g, '"nt"')
    .replace(/"startTime"/g, '"st"')
    .replace(/"endTime"/g, '"et"');
};

/**
 * Simple decompression function for strings
 */
const decompressString = (str: string): string => {
  // Reverse the compression
  return str
    .replace(/"t":\[/g, '"timeline":[')
    .replace(/"st"/g, '"shotType"')
    .replace(/"d"/g, '"dialogue"')
    .replace(/"sp"/g, '"speaker"')
    .replace(/"tx"/g, '"text"')
    .replace(/"tn"/g, '"tone"')
    .replace(/"v"/g, '"visual"')
    .replace(/"ds"/g, '"description"')
    .replace(/"tr"/g, '"transition"')
    .replace(/"ef"/g, '"effects"')
    .replace(/"m"/g, '"music"')
    .replace(/"vl"/g, '"volume"')
    .replace(/"lc"/g, '"location"')
    .replace(/"vo"/g, '"visualObjects"')
    .replace(/"nt"/g, '"notes"')
    .replace(/"st"/g, '"startTime"')
    .replace(/"et"/g, '"endTime"');
};

/**
 * Load saved video scripts from localStorage with optimized loading
 */
const loadSavedVideoScripts = (): SavedVideoScript[] => {
  try {
    const savedScripts = localStorage.getItem(SAVED_VIDEO_SCRIPTS_KEY);
    if (savedScripts) {
      // Check if data is compressed
      const isCompressed = savedScripts.startsWith('COMPRESSED:');

      let jsonData = savedScripts;
      if (isCompressed) {
        // Remove the prefix and decompress
        jsonData = decompressString(savedScripts.substring(11));
      }

      // Parse dates from strings to Date objects
      const scripts = JSON.parse(jsonData) as SavedVideoScript[];
      return scripts.map(script => ({
        ...script,
        createdAt: new Date(script.createdAt),
        updatedAt: new Date(script.updatedAt)
      }));
    }
    return [];
  } catch (error) {
    console.error('Error loading saved video scripts:', error);
    return [];
  }
};

/**
 * Save video scripts to localStorage with compression
 */
const saveSavedVideoScripts = (scripts: SavedVideoScript[]): void => {
  try {
    // Compress the data before saving
    const jsonString = JSON.stringify(scripts);

    // Only compress if the data is large enough to benefit
    if (jsonString.length > 1000) {
      const compressed = compressString(jsonString);
      localStorage.setItem(SAVED_VIDEO_SCRIPTS_KEY, 'COMPRESSED:' + compressed);
    } else {
      localStorage.setItem(SAVED_VIDEO_SCRIPTS_KEY, jsonString);
    }
  } catch (error) {
    console.error('Error saving video scripts:', error);

    // Fallback: try to save without compression if compression fails
    try {
      localStorage.setItem(SAVED_VIDEO_SCRIPTS_KEY, JSON.stringify(scripts));
    } catch (fallbackError) {
      console.error('Fallback save also failed:', fallbackError);
    }
  }
};

/**
 * Get all saved video scripts
 */
export const getAllSavedVideoScripts = async (): Promise<SavedVideoScript[]> => {
  return loadSavedVideoScripts();
};

/**
 * Get saved video script by ID
 */
export const getSavedVideoScriptById = async (id: string): Promise<SavedVideoScript | null> => {
  const scripts = loadSavedVideoScripts();
  return scripts.find(script => script.id === id) || null;
};

/**
 * Save a video script
 */
export const saveVideoScript = (
  script: VideoScriptResponse,
  request: VideoScriptRequest,
  relatedContentId?: string
): SavedVideoScript => {
  // Create the new script object first
  const newScript: SavedVideoScript = {
    ...script,
    id: `vs-${Date.now()}`,
    createdAt: new Date(),
    updatedAt: new Date(),
    videoType: request.videoType,
    purpose: request.purpose,
    targetAudience: request.targetAudience,
    platform: request.platform,
    productName: request.productName,
    durationInSeconds: request.duration,
    status: 'draft',
    relatedContentId
  };

  // Use setTimeout to perform the actual saving operation asynchronously
  // This allows the UI to update immediately while saving happens in the background
  setTimeout(() => {
    try {
      const scripts = loadSavedVideoScripts();
      const updatedScripts = [...scripts, newScript];
      saveSavedVideoScripts(updatedScripts);
      console.log('Script saved successfully in background');
    } catch (error) {
      console.error('Error in background save:', error);
    }
  }, 0);

  // Return the new script immediately
  return newScript;
};

/**
 * Update a saved video script
 */
export const updateSavedVideoScript = (script: SavedVideoScript): SavedVideoScript => {
  // Create the updated script object first
  const updatedScript: SavedVideoScript = {
    ...script,
    updatedAt: new Date()
  };

  // Use setTimeout to perform the actual saving operation asynchronously
  setTimeout(() => {
    try {
      const scripts = loadSavedVideoScripts();
      const updatedScripts = scripts.map(s => s.id === script.id ? updatedScript : s);
      saveSavedVideoScripts(updatedScripts);
      console.log('Script updated successfully in background');
    } catch (error) {
      console.error('Error in background update:', error);
    }
  }, 0);

  // Return the updated script immediately
  return updatedScript;
};

/**
 * Delete a saved video script
 */
export const deleteSavedVideoScript = async (id: string): Promise<void> => {
  const scripts = loadSavedVideoScripts();
  const updatedScripts = scripts.filter(script => script.id !== id);
  saveSavedVideoScripts(updatedScripts);
};

/**
 * Get saved video scripts by related content ID
 */
export const getSavedVideoScriptsByRelatedContentId = async (relatedContentId: string): Promise<SavedVideoScript[]> => {
  const scripts = loadSavedVideoScripts();
  return scripts.filter(script => script.relatedContentId === relatedContentId);
};

/**
 * Get video type name in Indonesian
 */
export const getVideoTypeName = (videoType: VideoType): string => {
  const videoTypeNames: Record<VideoType, string> = {
    [VideoType.MARKETING_PRODUCT]: 'Marketing Produk',
    [VideoType.DOCUMENTATION]: 'Dokumentasi',
    [VideoType.REELS]: 'Reels',
    [VideoType.JOKES]: 'Jokes/Humor',
    [VideoType.REVIEW]: 'Review',
    [VideoType.TESTIMONIAL]: 'Testimoni',
    [VideoType.EDUCATIONAL]: 'Edukasi'
  };
  return videoTypeNames[videoType];
};

/**
 * Get purpose name in Indonesian
 */
export const getPurposeName = (purpose: VideoPurpose): string => {
  const purposeNames: Record<VideoPurpose, string> = {
    [VideoPurpose.PROMOTION]: 'Promosi',
    [VideoPurpose.EDUCATION]: 'Edukasi',
    [VideoPurpose.VIRAL]: 'Viral',
    [VideoPurpose.DOCUMENTATION]: 'Dokumentasi'
  };
  return purposeNames[purpose];
};

/**
 * Get target audience name in Indonesian
 */
export const getTargetAudienceName = (targetAudience: VideoTargetAudience): string => {
  const targetAudienceNames: Record<VideoTargetAudience, string> = {
    [VideoTargetAudience.CUSTOMER]: 'Customer',
    [VideoTargetAudience.INTERNAL]: 'Internal',
    [VideoTargetAudience.GENERAL]: 'Umum'
  };
  return targetAudienceNames[targetAudience];
};

/**
 * Get platform name
 */
export const getPlatformName = (platform: VideoPlatform): string => {
  const platformNames: Record<VideoPlatform, string> = {
    [VideoPlatform.INSTAGRAM]: 'Instagram',
    [VideoPlatform.TIKTOK]: 'TikTok',
    [VideoPlatform.YOUTUBE]: 'YouTube',
    [VideoPlatform.WHATSAPP]: 'WhatsApp'
  };
  return platformNames[platform];
};
