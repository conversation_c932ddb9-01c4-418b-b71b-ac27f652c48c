import React, { useState, useEffect } from 'react';
import { Template, TemplateFormData } from '../types/template';
import { ProposalType } from '../types/proposal';
import { 
  getAllTemplates, 
  createTemplate, 
  deleteTemplate, 
  extractVariablesFromDocx 
} from '../services/templateService';
import { 
  Plus, 
  Trash2, 
  FileText, 
  Upload, 
  X, 
  Check, 
  AlertCircle,
  Download
} from 'lucide-react';

export default function TemplateManagement() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isAddingTemplate, setIsAddingTemplate] = useState(false);
  const [formData, setFormData] = useState<TemplateFormData>({
    name: '',
    type: 'bundling',
    file: null
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [detectedVariables, setDetectedVariables] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Load templates on component mount
  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = () => {
    const loadedTemplates = getAllTemplates();
    setTemplates(loadedTemplates);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check if file is a .docx file
    if (!file.name.endsWith('.docx')) {
      setError('Please upload a .docx file');
      return;
    }

    setSelectedFile(file);
    setFormData(prev => ({ ...prev, file }));

    // Extract variables from the file
    setIsLoading(true);
    try {
      const variables = await extractVariablesFromDocx(file);
      setDetectedVariables(variables);
    } catch (error) {
      console.error('Error extracting variables:', error);
      setError('Failed to extract variables from the file');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      setError('Please enter a template name');
      return;
    }

    if (!formData.file) {
      setError('Please upload a template file');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await createTemplate(formData);
      setSuccess('Template added successfully');
      
      // Reset form
      setFormData({
        name: '',
        type: 'bundling',
        file: null
      });
      setSelectedFile(null);
      setDetectedVariables([]);
      setIsAddingTemplate(false);
      
      // Reload templates
      loadTemplates();
    } catch (error) {
      console.error('Error creating template:', error);
      setError('Failed to create template');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteTemplate = async (id: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return;

    setIsLoading(true);
    try {
      const success = deleteTemplate(id);
      if (success) {
        setSuccess('Template deleted successfully');
        loadTemplates();
      } else {
        setError('Failed to delete template');
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      setError('Failed to delete template');
    } finally {
      setIsLoading(false);
    }
  };

  const getProposalTypeName = (type: ProposalType): string => {
    switch (type) {
      case 'bundling': return 'Bundling';
      case 'consignment': return 'Konsinyasi';
      case 'trade-in': return 'Trade-In';
      case 'performance-guarantee': return 'Performance Guarantee';
      case 'performance-warranty': return 'Performance Warranty';
      case 'first-michelin': return 'First Michelin';
      default: return type;
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Template Management</h1>
        <button
          onClick={() => setIsAddingTemplate(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          disabled={isAddingTemplate}
        >
          <Plus size={18} className="mr-2" />
          Add Template
        </button>
      </div>

      {/* Success and Error Messages */}
      {success && (
        <div className="flex items-center p-4 mb-4 bg-green-100 text-green-800 rounded-md">
          <Check size={18} className="mr-2" />
          {success}
          <button 
            onClick={() => setSuccess(null)} 
            className="ml-auto text-green-600 hover:text-green-800"
          >
            <X size={18} />
          </button>
        </div>
      )}

      {error && (
        <div className="flex items-center p-4 mb-4 bg-red-100 text-red-800 rounded-md">
          <AlertCircle size={18} className="mr-2" />
          {error}
          <button 
            onClick={() => setError(null)} 
            className="ml-auto text-red-600 hover:text-red-800"
          >
            <X size={18} />
          </button>
        </div>
      )}

      {/* Add Template Form */}
      {isAddingTemplate && (
        <div className="mb-8 p-6 bg-white rounded-lg shadow-md">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Add New Template</h2>
            <button 
              onClick={() => setIsAddingTemplate(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={20} />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Template Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter template name"
              />
            </div>

            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                Proposal Type
              </label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="bundling">Bundling</option>
                <option value="consignment">Konsinyasi</option>
                <option value="trade-in">Trade-In</option>
                <option value="performance-guarantee">Performance Guarantee</option>
                <option value="performance-warranty">Performance Warranty</option>
                <option value="first-michelin">First Michelin</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Template File (.docx)
              </label>
              <div className="mt-1 flex items-center">
                <input
                  type="file"
                  accept=".docx"
                  onChange={handleFileChange}
                  className="sr-only"
                  id="template-upload"
                />
                <label
                  htmlFor="template-upload"
                  className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Upload size={16} className="mr-2" />
                  Upload Template
                </label>
                {selectedFile && (
                  <span className="ml-3 text-sm text-gray-500">
                    {selectedFile.name}
                  </span>
                )}
              </div>
            </div>

            {detectedVariables.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Detected Variables:</h3>
                <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-md">
                  {detectedVariables.map((variable, index) => (
                    <span 
                      key={index} 
                      className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {`{{${variable}}}`}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="flex justify-end pt-4">
              <button
                type="button"
                onClick={() => setIsAddingTemplate(false)}
                className="mr-3 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
                disabled={isLoading || !selectedFile}
              >
                {isLoading ? 'Saving...' : 'Save Template'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Templates List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Variables
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {templates.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                  No templates found. Add a template to get started.
                </td>
              </tr>
            ) : (
              templates.map(template => (
                <tr key={template.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FileText size={18} className="text-gray-400 mr-2" />
                      <div className="text-sm font-medium text-gray-900">{template.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      {getProposalTypeName(template.type)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500">
                      {template.detectedVariables.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {template.detectedVariables.slice(0, 3).map((variable, index) => (
                            <span 
                              key={index} 
                              className="inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                            >
                              {`{{${variable}}}`}
                            </span>
                          ))}
                          {template.detectedVariables.length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{template.detectedVariables.length - 3} more
                            </span>
                          )}
                        </div>
                      ) : (
                        <span className="text-xs text-gray-400">No variables detected</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(template.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {template.fileUrl && (
                      <a
                        href={template.fileUrl}
                        download={`${template.name}.docx`}
                        className="text-blue-600 hover:text-blue-900 mr-4"
                      >
                        <Download size={18} />
                      </a>
                    )}
                    <button
                      onClick={() => handleDeleteTemplate(template.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 size={18} />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
