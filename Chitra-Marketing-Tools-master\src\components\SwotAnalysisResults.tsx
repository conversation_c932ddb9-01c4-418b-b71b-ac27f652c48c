import React, { useState } from 'react';
import { SwotAnalysisResult, SwotItem, SwotInsight, SwotRecommendation } from '../types/swotAnalysis';
import { exportSwotAnalysisToExcel } from '../services/swotAnalysisService';
import { Download, FileText, ChevronDown, ChevronUp, ExternalLink } from 'lucide-react';
import SwotAnalysisCharts from './SwotAnalysisCharts';

interface SwotAnalysisResultsProps {
  analysisResult: SwotAnalysisResult;
}

const SwotAnalysisResults: React.FC<SwotAnalysisResultsProps> = ({ analysisResult }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'charts' | 'recommendations'>('overview');
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    strengths: true,
    weaknesses: true,
    opportunities: true,
    threats: true,
    insights: true,
    recommendations: true
  });

  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Handle export to Excel
  const handleExportToExcel = () => {
    try {
      exportSwotAnalysisToExcel(analysisResult);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      alert('Error exporting to Excel. Please try again.');
    }
  };

  // Handle export to PDF
  const handleExportToPDF = () => {
    alert('PDF export functionality will be implemented soon.');
  };

  // Render SWOT item
  const renderSwotItem = (item: SwotItem) => (
    <div key={item.id} className="bg-white p-4 rounded-lg shadow mb-4">
      <div className="flex justify-between items-start">
        <div>
          <h4 className="text-lg font-medium">{item.title}</h4>
          <p className="text-gray-600 mt-1">{item.description}</p>
        </div>
        <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
          Impact: {item.impactScore}/10
        </div>
      </div>

      {item.relatedTo && item.relatedTo.length > 0 && (
        <div className="mt-2">
          <p className="text-sm text-gray-500">Related to: {item.relatedTo.join(', ')}</p>
        </div>
      )}

      {item.dataSource && (
        <div className="mt-1">
          <p className="text-xs text-gray-400">Source: {item.dataSource}</p>
        </div>
      )}
    </div>
  );

  // Render insight
  const renderInsight = (insight: SwotInsight) => (
    <div key={insight.id} className="bg-white p-4 rounded-lg shadow mb-4">
      <h4 className="text-lg font-medium">{insight.title}</h4>
      <p className="text-gray-600 mt-1">{insight.description}</p>

      <div className="mt-2 flex items-center">
        <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">
          {insight.category}
        </span>
      </div>

      {insight.relatedSwotItems && insight.relatedSwotItems.length > 0 && (
        <div className="mt-2">
          <p className="text-sm text-gray-500">Related to: {insight.relatedSwotItems.join(', ')}</p>
        </div>
      )}
    </div>
  );

  // Render recommendation
  const renderRecommendation = (recommendation: SwotRecommendation) => (
    <div key={recommendation.id} className="bg-white p-4 rounded-lg shadow mb-4">
      <div className="flex justify-between items-start">
        <h4 className="text-lg font-medium">{recommendation.title}</h4>
        <div className={`px-2 py-1 rounded-full text-xs ${
          recommendation.priority === 'high'
            ? 'bg-red-100 text-red-800'
            : recommendation.priority === 'medium'
              ? 'bg-yellow-100 text-yellow-800'
              : 'bg-green-100 text-green-800'
        }`}>
          {recommendation.priority} priority
        </div>
      </div>

      <p className="text-gray-600 mt-1">{recommendation.description}</p>

      <div className="mt-3 flex flex-wrap gap-2">
        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
          {recommendation.implementationTimeframe}
        </span>

        {recommendation.relatedInsights && recommendation.relatedInsights.length > 0 && (
          <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs">
            {recommendation.relatedInsights.length} related insights
          </span>
        )}
      </div>

      {recommendation.expectedOutcome && (
        <div className="mt-2 border-t pt-2">
          <p className="text-sm font-medium">Expected Outcome:</p>
          <p className="text-sm text-gray-600">{recommendation.expectedOutcome}</p>
        </div>
      )}
    </div>
  );

  return (
    <div className="bg-gray-50 p-6 rounded-lg">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">SWOT Analysis Results</h2>
          <p className="text-gray-600 mt-1">
            {analysisResult.companyName}
          </p>
          <p className="text-sm text-gray-500 mt-1">
            Generated on {new Date(analysisResult.timestamp).toLocaleString()}
          </p>
        </div>

        <div className="flex space-x-2 mt-4 md:mt-0">
          <button
            onClick={handleExportToExcel}
            className="flex items-center px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200"
          >
            <Download size={16} className="mr-2" />
            Export Excel
          </button>

          <button
            onClick={handleExportToPDF}
            className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            <FileText size={16} className="mr-2" />
            Export PDF
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex border-b mb-6">
        <button
          className={`px-4 py-2 font-medium ${
            activeTab === 'overview'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-600 hover:text-blue-600'
          }`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>

        <button
          className={`px-4 py-2 font-medium ${
            activeTab === 'details'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-600 hover:text-blue-600'
          }`}
          onClick={() => setActiveTab('details')}
        >
          Details
        </button>

        <button
          className={`px-4 py-2 font-medium ${
            activeTab === 'charts'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-600 hover:text-blue-600'
          }`}
          onClick={() => setActiveTab('charts')}
        >
          Charts
        </button>

        <button
          className={`px-4 py-2 font-medium ${
            activeTab === 'recommendations'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-600 hover:text-blue-600'
          }`}
          onClick={() => setActiveTab('recommendations')}
        >
          Recommendations
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* SWOT Matrix */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Strengths */}
            <div className="bg-green-50 p-4 rounded-lg">
              <div
                className="flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('strengths')}
              >
                <h3 className="text-xl font-semibold text-green-800">Strengths</h3>
                {expandedSections.strengths ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
              </div>

              {expandedSections.strengths && (
                <div className="mt-3 space-y-2">
                  {analysisResult.strengths.map((strength, index) => (
                    <div key={strength.id} className="bg-white p-3 rounded shadow-sm">
                      <div className="flex justify-between">
                        <p className="font-medium">{strength.title}</p>
                        <span className="text-sm bg-green-100 text-green-800 px-2 rounded-full">
                          {strength.impactScore}/10
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Weaknesses */}
            <div className="bg-red-50 p-4 rounded-lg">
              <div
                className="flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('weaknesses')}
              >
                <h3 className="text-xl font-semibold text-red-800">Weaknesses</h3>
                {expandedSections.weaknesses ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
              </div>

              {expandedSections.weaknesses && (
                <div className="mt-3 space-y-2">
                  {analysisResult.weaknesses.map((weakness, index) => (
                    <div key={weakness.id} className="bg-white p-3 rounded shadow-sm">
                      <div className="flex justify-between">
                        <p className="font-medium">{weakness.title}</p>
                        <span className="text-sm bg-red-100 text-red-800 px-2 rounded-full">
                          {weakness.impactScore}/10
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Opportunities */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <div
                className="flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('opportunities')}
              >
                <h3 className="text-xl font-semibold text-blue-800">Opportunities</h3>
                {expandedSections.opportunities ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
              </div>

              {expandedSections.opportunities && (
                <div className="mt-3 space-y-2">
                  {analysisResult.opportunities.map((opportunity, index) => (
                    <div key={opportunity.id} className="bg-white p-3 rounded shadow-sm">
                      <div className="flex justify-between">
                        <p className="font-medium">{opportunity.title}</p>
                        <span className="text-sm bg-blue-100 text-blue-800 px-2 rounded-full">
                          {opportunity.impactScore}/10
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Threats */}
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div
                className="flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('threats')}
              >
                <h3 className="text-xl font-semibold text-yellow-800">Threats</h3>
                {expandedSections.threats ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
              </div>

              {expandedSections.threats && (
                <div className="mt-3 space-y-2">
                  {analysisResult.threats.map((threat, index) => (
                    <div key={threat.id} className="bg-white p-3 rounded shadow-sm">
                      <div className="flex justify-between">
                        <p className="font-medium">{threat.title}</p>
                        <span className="text-sm bg-yellow-100 text-yellow-800 px-2 rounded-full">
                          {threat.impactScore}/10
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Key Insights */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection('insights')}
            >
              <h3 className="text-xl font-semibold text-purple-800">Key Insights</h3>
              {expandedSections.insights ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
            </div>

            {expandedSections.insights && (
              <div className="mt-3 space-y-2">
                {analysisResult.insights.slice(0, 3).map((insight, index) => (
                  <div key={insight.id} className="bg-white p-3 rounded shadow-sm">
                    <p className="font-medium">{insight.title}</p>
                    <p className="text-sm text-gray-600 mt-1">{insight.description}</p>
                  </div>
                ))}

                {analysisResult.insights.length > 3 && (
                  <button
                    onClick={() => setActiveTab('details')}
                    className="text-purple-600 hover:text-purple-800 text-sm font-medium flex items-center"
                  >
                    View all {analysisResult.insights.length} insights
                    <ExternalLink size={14} className="ml-1" />
                  </button>
                )}
              </div>
            )}
          </div>

          {/* Top Recommendations */}
          <div className="bg-indigo-50 p-4 rounded-lg">
            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => toggleSection('recommendations')}
            >
              <h3 className="text-xl font-semibold text-indigo-800">Top Recommendations</h3>
              {expandedSections.recommendations ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
            </div>

            {expandedSections.recommendations && (
              <div className="mt-3 space-y-2">
                {analysisResult.recommendations
                  .filter(r => r.priority === 'high')
                  .slice(0, 3)
                  .map((recommendation, index) => (
                    <div key={recommendation.id} className="bg-white p-3 rounded shadow-sm">
                      <p className="font-medium">{recommendation.title}</p>
                      <p className="text-sm text-gray-600 mt-1">{recommendation.description}</p>
                      <div className="flex items-center mt-2">
                        <span className="text-xs bg-indigo-100 text-indigo-800 px-2 py-0.5 rounded-full">
                          {recommendation.implementationTimeframe}
                        </span>
                      </div>
                    </div>
                  ))}

                <button
                  onClick={() => setActiveTab('recommendations')}
                  className="text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center"
                >
                  View all {analysisResult.recommendations.length} recommendations
                  <ExternalLink size={14} className="ml-1" />
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'details' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Strengths */}
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Strengths</h3>
              <div className="space-y-4">
                {analysisResult.strengths.map(renderSwotItem)}
              </div>
            </div>

            {/* Weaknesses */}
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Weaknesses</h3>
              <div className="space-y-4">
                {analysisResult.weaknesses.map(renderSwotItem)}
              </div>
            </div>

            {/* Opportunities */}
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Opportunities</h3>
              <div className="space-y-4">
                {analysisResult.opportunities.map(renderSwotItem)}
              </div>
            </div>

            {/* Threats */}
            <div>
              <h3 className="text-xl font-semibold text-gray-800 mb-4">Threats</h3>
              <div className="space-y-4">
                {analysisResult.threats.map(renderSwotItem)}
              </div>
            </div>
          </div>

          {/* Insights */}
          <div>
            <h3 className="text-xl font-semibold text-gray-800 mb-4">Insights</h3>
            <div className="space-y-4">
              {analysisResult.insights.map(renderInsight)}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'charts' && (
        <SwotAnalysisCharts analysisResult={analysisResult} />
      )}

      {activeTab === 'recommendations' && (
        <div className="space-y-6">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">Recommendations</h3>

          {/* Filter by priority */}
          <div className="flex space-x-2 mb-4">
            <button className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
              High Priority
            </button>
            <button className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
              Medium Priority
            </button>
            <button className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
              Low Priority
            </button>
          </div>

          <div className="space-y-4">
            {analysisResult.recommendations.map(renderRecommendation)}
          </div>
        </div>
      )}
    </div>
  );
};

export default SwotAnalysisResults;
