import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Autocomplete,
  Typography,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { Product } from '../../types';
import { ProposalProduct } from '../../services/googleDocsService';
import { fetchProducts } from '../../services/productService';

interface ProductSelectorProps {
  products: ProposalProduct[];
  onChange: (products: ProposalProduct[]) => void;
}

const ProductSelector: React.FC<ProductSelectorProps> = ({ products, onChange }) => {
  const [availableProducts, setAvailableProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [unitPrice, setUnitPrice] = useState<number>(0);
  const [discount, setDiscount] = useState<number>(0);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        const products = await fetchProducts();
        setAvailableProducts(products);
      } catch (error) {
        console.error('Error loading products:', error);
      }
    };
    loadProducts();
  }, []);

  const handleAddProduct = () => {
    if (selectedProduct) {
      const newProduct: ProposalProduct = {
        product: selectedProduct,
        quantity,
        unitPrice,
        discount: discount || undefined,
      };

      onChange([...products, newProduct]);
      setSelectedProduct(null);
      setQuantity(1);
      setUnitPrice(0);
      setDiscount(0);
    }
  };

  const handleRemoveProduct = (index: number) => {
    const newProducts = products.filter((_, i) => i !== index);
    onChange(newProducts);
  };

  const calculateSubtotal = (product: ProposalProduct) => {
    const subtotal = product.quantity * product.unitPrice;
    const discountAmount = product.discount ? (subtotal * product.discount) / 100 : 0;
    return subtotal - discountAmount;
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Produk
      </Typography>

      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Autocomplete
          options={availableProducts}
          getOptionLabel={(option) => option.materialDescription}
          value={selectedProduct}
          onChange={(_, newValue) => {
            setSelectedProduct(newValue);
            if (newValue) {
              setUnitPrice(newValue.price || 0);
            }
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Pilih Produk"
              fullWidth
            />
          )}
          sx={{ flex: 2 }}
        />

        <TextField
          type="number"
          label="Qty"
          value={quantity}
          onChange={(e) => setQuantity(Number(e.target.value))}
          sx={{ flex: 1 }}
        />

        <TextField
          type="number"
          label="Harga Satuan"
          value={unitPrice}
          onChange={(e) => setUnitPrice(Number(e.target.value))}
          sx={{ flex: 1 }}
        />

        <TextField
          type="number"
          label="Diskon (%)"
          value={discount}
          onChange={(e) => setDiscount(Number(e.target.value))}
          sx={{ flex: 1 }}
        />

        <IconButton
          color="primary"
          onClick={handleAddProduct}
          disabled={!selectedProduct}
        >
          <DeleteIcon />
        </IconButton>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>No.</TableCell>
              <TableCell>Produk</TableCell>
              <TableCell>Spesifikasi</TableCell>
              <TableCell align="right">Qty</TableCell>
              <TableCell align="right">Harga Satuan</TableCell>
              <TableCell align="right">Diskon</TableCell>
              <TableCell align="right">Subtotal</TableCell>
              <TableCell align="center">Aksi</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {products.map((product, index) => (
              <TableRow key={index}>
                <TableCell>{index + 1}</TableCell>
                <TableCell>{product.product.materialDescription}</TableCell>
                <TableCell>{product.product.specification || '-'}</TableCell>
                <TableCell align="right">{product.quantity}</TableCell>
                <TableCell align="right">
                  Rp {product.unitPrice.toLocaleString('id-ID')}
                </TableCell>
                <TableCell align="right">
                  {product.discount ? `${product.discount}%` : '-'}
                </TableCell>
                <TableCell align="right">
                  Rp {calculateSubtotal(product).toLocaleString('id-ID')}
                </TableCell>
                <TableCell align="center">
                  <IconButton
                    color="error"
                    onClick={() => handleRemoveProduct(index)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default ProductSelector; 