import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';
import { ProductAnalysisResult } from '../types/productAnalysis';
import { SalesRevenueItem } from '../services/salesRevenue2025Service';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface ProductAnalysisChartsProps {
  analysisResult: ProductAnalysisResult;
}

const ProductAnalysisCharts: React.FC<ProductAnalysisChartsProps> = ({ analysisResult }) => {
  // Generate sales history trend data
  const generateSalesHistoryData = () => {
    // Group sales data by month
    const salesByMonth: Record<string, number> = {};
    
    // Sort sales data by date
    const sortedSales = [...analysisResult.salesData].sort((a, b) => {
      const dateA = new Date(a.billingDate);
      const dateB = new Date(b.billingDate);
      return dateA.getTime() - dateB.getTime();
    });
    
    // Group by month
    sortedSales.forEach(sale => {
      if (!sale.billingDate) return;
      
      const date = new Date(sale.billingDate);
      const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;
      
      if (!salesByMonth[monthYear]) {
        salesByMonth[monthYear] = 0;
      }
      
      salesByMonth[monthYear] += sale.revenueInDocCurr || 0;
    });
    
    // Convert to chart data
    const labels = Object.keys(salesByMonth);
    const data = Object.values(salesByMonth);
    
    return {
      labels,
      datasets: [
        {
          label: 'Revenue per Month',
          data,
          borderColor: 'rgb(53, 162, 235)',
          backgroundColor: 'rgba(53, 162, 235, 0.5)',
          tension: 0.3
        }
      ]
    };
  };
  
  // Generate top customers chart data
  const generateTopCustomersData = () => {
    const { topCustomers } = analysisResult.salesHistory;
    
    return {
      labels: topCustomers.map(customer => customer.customerName),
      datasets: [
        {
          label: 'Revenue',
          data: topCustomers.map(customer => customer.revenue),
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
          ],
          borderWidth: 1,
        }
      ]
    };
  };
  
  // Generate market segments chart data
  const generateMarketSegmentsData = () => {
    const { marketSegments } = analysisResult.marketAnalysis;
    
    return {
      labels: marketSegments.map(segment => segment.segment),
      datasets: [
        {
          label: 'Potential Revenue',
          data: marketSegments.map(segment => segment.potentialRevenue),
          backgroundColor: [
            'rgba(255, 159, 64, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
          ],
          borderColor: [
            'rgba(255, 159, 64, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
          ],
          borderWidth: 1,
        }
      ]
    };
  };
  
  // Generate seasonal adjustments chart data
  const generateSeasonalAdjustmentsData = () => {
    const { seasonalAdjustments } = analysisResult.inventoryRecommendations;
    
    return {
      labels: seasonalAdjustments.map(adjustment => adjustment.season),
      datasets: [
        {
          label: 'Adjustment Factor',
          data: seasonalAdjustments.map(adjustment => adjustment.adjustmentFactor),
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }
      ]
    };
  };
  
  // Chart options
  const lineChartOptions: ChartOptions<'line'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Sales History Trend',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Revenue (IDR)'
        }
      }
    }
  };
  
  const pieChartOptions: ChartOptions<'pie'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: true,
        text: 'Top Customers by Revenue',
      },
    }
  };
  
  const doughnutChartOptions: ChartOptions<'doughnut'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: true,
        text: 'Market Segments',
      },
    }
  };
  
  const barChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Seasonal Inventory Adjustments',
      },
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  };
  
  // Check if we have enough data for each chart
  const hasSalesData = analysisResult.salesData && analysisResult.salesData.length > 0;
  const hasTopCustomers = analysisResult.salesHistory.topCustomers.length > 0;
  const hasMarketSegments = analysisResult.marketAnalysis.marketSegments.length > 0;
  const hasSeasonalAdjustments = analysisResult.inventoryRecommendations.seasonalAdjustments.length > 0;
  
  return (
    <div className="space-y-8">
      {/* Sales History Trend Chart */}
      {hasSalesData && (
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Tren Penjualan</h3>
          <div className="h-64">
            <Line 
              options={lineChartOptions} 
              data={generateSalesHistoryData()} 
            />
          </div>
        </div>
      )}
      
      {/* Top Customers Pie Chart */}
      {hasTopCustomers && (
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Pelanggan Teratas</h3>
          <div className="h-64">
            <Pie 
              options={pieChartOptions} 
              data={generateTopCustomersData()} 
            />
          </div>
        </div>
      )}
      
      {/* Market Segments Doughnut Chart */}
      {hasMarketSegments && (
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Segmen Pasar</h3>
          <div className="h-64">
            <Doughnut 
              options={doughnutChartOptions} 
              data={generateMarketSegmentsData()} 
            />
          </div>
        </div>
      )}
      
      {/* Seasonal Adjustments Bar Chart */}
      {hasSeasonalAdjustments && (
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Penyesuaian Musiman Inventaris</h3>
          <div className="h-64">
            <Bar 
              options={barChartOptions} 
              data={generateSeasonalAdjustmentsData()} 
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductAnalysisCharts;
