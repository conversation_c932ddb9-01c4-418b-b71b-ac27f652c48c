<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">AI Negotiation Simulator</h1>
                    <p class="mt-2 text-gray-600">Simulator negosiasi AI untuk melatih kemampuan sales dan negosiasi</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="exportSession"
                        :disabled="!currentSession"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Session
                    </button>
                    <button
                        @click="startNewSession"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <Plus class="h-4 w-4 mr-2" />
                        New Session
                    </button>
                </div>
            </div>

            <!-- Session Setup -->
            <div v-if="!currentSession" class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-6">Setup Negotiation Session</h2>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Customer Persona -->
                    <div class="space-y-4">
                        <h3 class="text-md font-medium text-gray-900">Customer Persona</h3>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Customer Name</label>
                            <input 
                                v-model="sessionSetup.customerPersona.name" 
                                type="text" 
                                placeholder="PT Mining Sejahtera"
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Industry</label>
                            <select 
                                v-model="sessionSetup.customerPersona.industry" 
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="mining">Mining & Quarrying</option>
                                <option value="construction">Construction</option>
                                <option value="logistics">Logistics & Transportation</option>
                                <option value="agriculture">Agriculture</option>
                                <option value="manufacturing">Manufacturing</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Negotiation Style</label>
                            <select 
                                v-model="sessionSetup.customerPersona.negotiationStyle" 
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="aggressive">Aggressive - Hard bargainer, price-focused</option>
                                <option value="analytical">Analytical - Data-driven, detail-oriented</option>
                                <option value="relationship">Relationship - Values long-term partnership</option>
                                <option value="conservative">Conservative - Risk-averse, cautious</option>
                                <option value="time_pressed">Time-Pressed - Quick decisions, urgent needs</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Budget Range</label>
                            <div class="grid grid-cols-2 gap-2">
                                <input 
                                    v-model.number="sessionSetup.customerPersona.budgetMin" 
                                    type="number" 
                                    placeholder="Min budget"
                                    class="p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                                <input 
                                    v-model.number="sessionSetup.customerPersona.budgetMax" 
                                    type="number" 
                                    placeholder="Max budget"
                                    class="p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Key Concerns</label>
                            <textarea 
                                v-model="sessionSetup.customerPersona.keyConcerns" 
                                rows="3"
                                placeholder="e.g., Price sensitivity, delivery timeline, product quality..."
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            ></textarea>
                        </div>
                    </div>

                    <!-- Product Selection -->
                    <div class="space-y-4">
                        <h3 class="text-md font-medium text-gray-900">Products to Negotiate</h3>
                        
                        <div class="space-y-3">
                            <div 
                                v-for="(product, index) in sessionSetup.selectedProducts" 
                                :key="index"
                                class="p-4 border border-gray-200 rounded-lg"
                            >
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="font-medium text-gray-900">Product {{ index + 1 }}</h4>
                                    <button
                                        @click="removeProduct(index)"
                                        class="text-red-600 hover:text-red-800"
                                    >
                                        <Trash2 class="h-4 w-4" />
                                    </button>
                                </div>
                                
                                <div class="grid grid-cols-1 gap-3">
                                    <select 
                                        v-model="product.productId" 
                                        @change="updateProductDetails(index)"
                                        class="p-2 border border-gray-300 rounded text-sm"
                                    >
                                        <option value="">Select product...</option>
                                        <option 
                                            v-for="p in availableProducts" 
                                            :key="p.id" 
                                            :value="p.id"
                                        >
                                            {{ p.name }}
                                        </option>
                                    </select>
                                    
                                    <div class="grid grid-cols-2 gap-2">
                                        <input 
                                            v-model.number="product.quantity" 
                                            type="number" 
                                            min="1"
                                            placeholder="Quantity"
                                            class="p-2 border border-gray-300 rounded text-sm"
                                        />
                                        <input 
                                            v-model.number="product.originalPrice" 
                                            type="number" 
                                            placeholder="Original Price"
                                            class="p-2 border border-gray-300 rounded text-sm"
                                        />
                                    </div>
                                </div>
                            </div>
                            
                            <button
                                @click="addProduct"
                                class="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-500 hover:text-blue-600 flex items-center justify-center"
                            >
                                <Plus class="h-4 w-4 mr-2" />
                                Add Product
                            </button>
                        </div>

                        <!-- Target Margin -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Target Margin (%)</label>
                            <input 
                                v-model.number="sessionSetup.targetMargin" 
                                type="number" 
                                min="0" 
                                max="100"
                                placeholder="20"
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>

                        <!-- Difficulty Level -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Difficulty Level</label>
                            <select 
                                v-model="sessionSetup.difficultyLevel" 
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="easy">Easy - Cooperative customer</option>
                                <option value="medium">Medium - Standard negotiation</option>
                                <option value="hard">Hard - Challenging customer</option>
                                <option value="expert">Expert - Very difficult negotiation</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <button
                        @click="createSession"
                        :disabled="!canCreateSession"
                        class="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                        <MessageSquare class="h-4 w-4 mr-2" />
                        Start Negotiation
                    </button>
                </div>
            </div>

            <!-- Active Session -->
            <div v-if="currentSession" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Chat Interface -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow">
                        <!-- Chat Header -->
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        Negotiating with {{ currentSession.customerPersona.name }}
                                    </h3>
                                    <p class="text-sm text-gray-600">
                                        {{ currentSession.customerPersona.industry }} | {{ currentSession.customerPersona.negotiationStyle }}
                                    </p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        getSessionStatusColor(currentSession.status)
                                    ]">
                                        {{ getSessionStatusText(currentSession.status) }}
                                    </span>
                                    <button
                                        @click="endSession"
                                        class="text-red-600 hover:text-red-800"
                                    >
                                        <X class="h-4 w-4" />
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Messages -->
                        <div class="h-96 overflow-y-auto p-6 space-y-4" ref="messagesContainer">
                            <div 
                                v-for="message in currentSession.messages" 
                                :key="message.id"
                                :class="[
                                    'flex',
                                    message.sender === 'user' ? 'justify-end' : 'justify-start'
                                ]"
                            >
                                <div :class="[
                                    'max-w-xs lg:max-w-md px-4 py-2 rounded-lg',
                                    message.sender === 'user' 
                                        ? 'bg-blue-600 text-white' 
                                        : 'bg-gray-100 text-gray-900'
                                ]">
                                    <div class="flex items-center mb-1">
                                        <component 
                                            :is="message.sender === 'user' ? User : Bot" 
                                            class="h-4 w-4 mr-2" 
                                        />
                                        <span class="text-xs font-medium">
                                            {{ message.sender === 'user' ? 'You' : currentSession.customerPersona.name }}
                                        </span>
                                    </div>
                                    <p class="text-sm">{{ message.content }}</p>
                                    <p class="text-xs opacity-75 mt-1">
                                        {{ formatTime(message.timestamp) }}
                                    </p>
                                </div>
                            </div>

                            <!-- Typing indicator -->
                            <div v-if="isAiTyping" class="flex justify-start">
                                <div class="bg-gray-100 text-gray-900 max-w-xs lg:max-w-md px-4 py-2 rounded-lg">
                                    <div class="flex items-center">
                                        <Bot class="h-4 w-4 mr-2" />
                                        <span class="text-xs font-medium">{{ currentSession.customerPersona.name }}</span>
                                    </div>
                                    <div class="flex items-center mt-1">
                                        <div class="flex space-x-1">
                                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                                        </div>
                                        <span class="text-xs text-gray-500 ml-2">typing...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Message Input -->
                        <div class="px-6 py-4 border-t border-gray-200">
                            <form @submit.prevent="sendMessage" class="flex space-x-3">
                                <input 
                                    v-model="messageInput" 
                                    type="text" 
                                    placeholder="Type your message..."
                                    :disabled="isAiTyping || currentSession.status !== 'in_progress'"
                                    class="flex-1 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                                />
                                <button
                                    type="submit"
                                    :disabled="!messageInput.trim() || isAiTyping || currentSession.status !== 'in_progress'"
                                    class="px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <Send class="h-4 w-4" />
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Session Info & Analytics -->
                <div class="space-y-6">
                    <!-- Session Progress -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Session Progress</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span>Current Margin</span>
                                    <span class="font-medium">{{ currentMargin.toFixed(1) }}%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div 
                                        class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                        :style="{ width: `${Math.min(100, (currentMargin / currentSession.targetMargin) * 100)}%` }"
                                    ></div>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">
                                    Target: {{ currentSession.targetMargin }}%
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600">Messages</span>
                                    <p class="font-semibold">{{ currentSession.messages.length }}</p>
                                </div>
                                <div>
                                    <span class="text-gray-600">Duration</span>
                                    <p class="font-semibold">{{ sessionDuration }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                        
                        <div class="space-y-3">
                            <button
                                @click="suggestCounterOffer"
                                :disabled="isAiTyping"
                                class="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 text-sm flex items-center justify-center"
                            >
                                <Lightbulb class="h-4 w-4 mr-2" />
                                Suggest Counter Offer
                            </button>
                            
                            <button
                                @click="analyzeNegotiation"
                                :disabled="isAiTyping"
                                class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 text-sm flex items-center justify-center"
                            >
                                <BarChart3 class="h-4 w-4 mr-2" />
                                Analyze Progress
                            </button>
                            
                            <button
                                @click="getAdvice"
                                :disabled="isAiTyping"
                                class="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 text-sm flex items-center justify-center"
                            >
                                <Brain class="h-4 w-4 mr-2" />
                                Get AI Advice
                            </button>
                        </div>
                    </div>

                    <!-- Product Summary -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Products</h3>
                        
                        <div class="space-y-3">
                            <div 
                                v-for="product in currentSession.selectedProducts" 
                                :key="product.product.id"
                                class="p-3 bg-gray-50 rounded-lg"
                            >
                                <h4 class="font-medium text-sm text-gray-900">{{ product.product.name }}</h4>
                                <div class="text-xs text-gray-600 mt-1">
                                    Qty: {{ product.quantity }} | 
                                    Price: {{ formatCurrency(product.originalPrice) }}
                                </div>
                                <div class="text-xs text-gray-600">
                                    Total: {{ formatCurrency(product.quantity * product.originalPrice) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Download,
    Plus,
    MessageSquare,
    Trash2,
    X,
    User,
    Bot,
    Send,
    BarChart3,
    Brain,
    Lightbulb
} from 'lucide-vue-next';
import { ref, computed, onMounted, nextTick } from 'vue';

// Types
interface CustomerPersona {
    name: string;
    industry: string;
    negotiationStyle: string;
    budgetMin: number;
    budgetMax: number;
    keyConcerns: string;
}

interface Product {
    id: string;
    name: string;
    price: number;
}

interface SelectedProduct {
    productId: string;
    quantity: number;
    originalPrice: number;
    product?: Product;
}

interface NegotiationMessage {
    id: string;
    sender: 'user' | 'ai';
    content: string;
    timestamp: Date;
}

interface NegotiationSession {
    id: string;
    customerPersona: CustomerPersona;
    selectedProducts: SelectedProduct[];
    targetMargin: number;
    difficultyLevel: string;
    messages: NegotiationMessage[];
    status: 'in_progress' | 'completed' | 'cancelled';
    startedAt: Date;
}

interface SessionSetup {
    customerPersona: CustomerPersona;
    selectedProducts: SelectedProduct[];
    targetMargin: number;
    difficultyLevel: string;
}

// Reactive state
const currentSession = ref<NegotiationSession | null>(null);
const messageInput = ref('');
const isAiTyping = ref(false);
const messagesContainer = ref<HTMLElement>();

// Session setup
const sessionSetup = ref<SessionSetup>({
    customerPersona: {
        name: '',
        industry: 'mining',
        negotiationStyle: 'analytical',
        budgetMin: 0,
        budgetMax: 0,
        keyConcerns: ''
    },
    selectedProducts: [],
    targetMargin: 20,
    difficultyLevel: 'medium'
});

// Available products
const availableProducts = ref<Product[]>([
    { id: '1', name: '27.00 R 49 XD GRIP B E4T TL', price: 212874175 },
    { id: '2', name: '24.00 R 35 XD GRIP B E4T TL', price: 185000000 },
    { id: '3', name: 'Engine Oil SAE 15W-40 (20L)', price: 850000 },
    { id: '4', name: 'Air Filter Heavy Duty', price: 450000 },
    { id: '5', name: 'Oil Filter Premium', price: 320000 },
    { id: '6', name: 'Coolant Radiator (5L)', price: 275000 }
]);

// Computed properties
const canCreateSession = computed(() => {
    return sessionSetup.value.customerPersona.name.trim() !== '' &&
           sessionSetup.value.selectedProducts.length > 0 &&
           sessionSetup.value.selectedProducts.every(p => p.productId && p.quantity > 0 && p.originalPrice > 0);
});

const currentMargin = computed(() => {
    if (!currentSession.value) return 0;

    const totalOriginal = currentSession.value.selectedProducts.reduce((sum, p) =>
        sum + (p.quantity * p.originalPrice), 0);

    // For demo purposes, assume current negotiated price is 90% of original
    const currentPrice = totalOriginal * 0.9;
    const cost = totalOriginal * 0.8; // Assume 80% cost

    return ((currentPrice - cost) / currentPrice) * 100;
});

const sessionDuration = computed(() => {
    if (!currentSession.value) return '0m';

    const duration = Date.now() - currentSession.value.startedAt.getTime();
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);

    return minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
});

// Utility functions
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getSessionStatusColor = (status: string): string => {
    switch (status) {
        case 'in_progress':
            return 'bg-green-100 text-green-800';
        case 'completed':
            return 'bg-blue-100 text-blue-800';
        case 'cancelled':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

const getSessionStatusText = (status: string): string => {
    switch (status) {
        case 'in_progress':
            return 'Active';
        case 'completed':
            return 'Completed';
        case 'cancelled':
            return 'Cancelled';
        default:
            return 'Unknown';
    }
};

// Product management
const addProduct = () => {
    sessionSetup.value.selectedProducts.push({
        productId: '',
        quantity: 1,
        originalPrice: 0
    });
};

const removeProduct = (index: number) => {
    sessionSetup.value.selectedProducts.splice(index, 1);
};

const updateProductDetails = (index: number) => {
    const selectedProduct = sessionSetup.value.selectedProducts[index];
    const product = availableProducts.value.find(p => p.id === selectedProduct.productId);

    if (product) {
        selectedProduct.originalPrice = product.price;
        selectedProduct.product = product;
    }
};

// Session management
const createSession = async () => {
    try {
        // Create new session
        const session: NegotiationSession = {
            id: `session-${Date.now()}`,
            customerPersona: { ...sessionSetup.value.customerPersona },
            selectedProducts: sessionSetup.value.selectedProducts.map(p => ({
                ...p,
                product: availableProducts.value.find(prod => prod.id === p.productId)!
            })),
            targetMargin: sessionSetup.value.targetMargin,
            difficultyLevel: sessionSetup.value.difficultyLevel,
            messages: [],
            status: 'in_progress',
            startedAt: new Date()
        };

        currentSession.value = session;

        // Start with AI greeting
        await sendAIGreeting();

    } catch (error) {
        console.error('Error creating session:', error);
        alert('Gagal membuat session negosiasi. Silakan coba lagi.');
    }
};

const sendAIGreeting = async () => {
    isAiTyping.value = true;

    try {
        // Simulate AI thinking time
        await new Promise(resolve => setTimeout(resolve, 2000));

        const greeting = generateAIGreeting();

        const aiMessage: NegotiationMessage = {
            id: `msg-${Date.now()}`,
            sender: 'ai',
            content: greeting,
            timestamp: new Date()
        };

        currentSession.value!.messages.push(aiMessage);
        scrollToBottom();

    } catch (error) {
        console.error('Error sending AI greeting:', error);
    } finally {
        isAiTyping.value = false;
    }
};

const generateAIGreeting = (): string => {
    const persona = currentSession.value!.customerPersona;
    const style = persona.negotiationStyle;

    const greetings: Record<string, string[]> = {
        aggressive: [
            `Selamat pagi. Saya dari ${persona.name}. Langsung saja, kami butuh harga terbaik untuk produk ini. Kompetitor sudah kasih penawaran yang sangat menarik.`,
            `Halo, saya perlu diskusi harga. Budget kami terbatas dan kami sudah dapat penawaran lebih murah dari tempat lain.`,
            `Selamat siang. Kami tertarik dengan produk Anda, tapi harga yang Anda tawarkan masih terlalu tinggi. Bisa turun berapa persen?`
        ],
        analytical: [
            `Selamat pagi. Saya sudah review spesifikasi produk Anda dan membandingkan dengan kompetitor. Bisa kita diskusi detail harga dan value proposition?`,
            `Halo, saya butuh breakdown detail cost dan benefit analysis untuk produk ini. Kami perlu justifikasi yang kuat untuk procurement.`,
            `Selamat siang. Saya sudah prepare data perbandingan. Bisa kita bahas ROI dan total cost of ownership?`
        ],
        relationship: [
            `Selamat pagi! Kami sudah lama mendengar reputasi baik Chitra. Kami tertarik untuk membangun partnership jangka panjang.`,
            `Halo, kami mencari supplier yang bisa jadi partner strategis. Selain harga, kami juga nilai after-sales service dan support.`,
            `Selamat siang. Kami ingin diskusi tidak hanya soal harga, tapi juga bagaimana kita bisa saling menguntungkan dalam jangka panjang.`
        ],
        conservative: [
            `Selamat pagi. Kami perlu waktu untuk evaluasi yang detail. Bisa dijelaskan dulu track record dan garansi produk?`,
            `Halo, sebagai perusahaan yang hati-hati dalam investasi, kami butuh assurance yang kuat tentang kualitas dan reliability.`,
            `Selamat siang. Kami tertarik, tapi perlu approval dari manajemen. Bisa kasih penawaran tertulis yang detail?`
        ],
        time_pressed: [
            `Selamat pagi! Kami butuh produk ini segera. Proyek sudah mulai dan kami perlu delivery cepat. Bisa kasih best price untuk urgent order?`,
            `Halo, situasi kami urgent. Bisa langsung kasih bottom price? Kami perlu keputusan hari ini juga.`,
            `Selamat siang. Timeline kami ketat. Kalau harga cocok, kami bisa PO hari ini. Berapa final price-nya?`
        ]
    };

    const styleGreetings = greetings[style] || greetings.analytical;
    return styleGreetings[Math.floor(Math.random() * styleGreetings.length)];
};

// Message handling
const sendMessage = async () => {
    if (!messageInput.value.trim() || !currentSession.value || isAiTyping.value) return;

    const userMessage: NegotiationMessage = {
        id: `msg-${Date.now()}`,
        sender: 'user',
        content: messageInput.value.trim(),
        timestamp: new Date()
    };

    currentSession.value.messages.push(userMessage);
    const userInput = messageInput.value.trim();
    messageInput.value = '';

    scrollToBottom();

    // Get AI response
    await getAIResponse(userInput);
};

const getAIResponse = async (userInput: string) => {
    isAiTyping.value = true;

    try {
        // Simulate AI thinking time
        await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 2000));

        const response = generateAIResponse(userInput);

        const aiMessage: NegotiationMessage = {
            id: `msg-${Date.now()}`,
            sender: 'ai',
            content: response,
            timestamp: new Date()
        };

        currentSession.value!.messages.push(aiMessage);
        scrollToBottom();

    } catch (error) {
        console.error('Error getting AI response:', error);
        const errorMessage: NegotiationMessage = {
            id: `msg-${Date.now()}`,
            sender: 'ai',
            content: 'Maaf, saya mengalami gangguan. Bisa ulangi pertanyaan Anda?',
            timestamp: new Date()
        };
        currentSession.value!.messages.push(errorMessage);
    } finally {
        isAiTyping.value = false;
    }
};

const generateAIResponse = (userInput: string): string => {
    const persona = currentSession.value!.customerPersona;
    const style = persona.negotiationStyle;
    const input = userInput.toLowerCase();

    // Price-related responses
    if (input.includes('harga') || input.includes('price') || input.includes('diskon') || input.includes('discount')) {
        const priceResponses: Record<string, string[]> = {
            aggressive: [
                'Harga segitu masih kemahalan. Kompetitor kasih 15% lebih murah. Bisa match atau tidak?',
                'Kami butuh minimal 20% discount dari harga list. Kalau tidak bisa, kami terpaksa cari supplier lain.',
                'Budget kami fix di angka ini. Take it or leave it. Bisa atau tidak?'
            ],
            analytical: [
                'Berdasarkan analisis kami, harga ini 12% lebih tinggi dari market average. Bisa dijelaskan value add-nya?',
                'Kami butuh breakdown cost yang detail. ROI calculation kami menunjukkan perlu harga lebih kompetitif.',
                'Dari sisi TCO, masih belum justify. Bisa kasih additional value atau adjustment harga?'
            ],
            relationship: [
                'Kami appreciate kualitas produk Anda. Tapi untuk partnership jangka panjang, perlu harga yang sustainable untuk kedua belah pihak.',
                'Sebagai potential long-term partner, kami harap bisa dapat special consideration untuk pricing.',
                'Kami mau commit volume besar kalau harga bisa lebih friendly. Win-win solution.'
            ],
            conservative: [
                'Harga ini perlu approval dari board. Bisa kasih justifikasi yang kuat kenapa premium pricing ini worth it?',
                'Kami perlu assurance kalau investasi ini akan profitable. Risk vs return masih belum balance.',
                'Management kami sangat hati-hati dengan budget. Perlu strong business case untuk approve harga ini.'
            ],
            time_pressed: [
                'Waktu kami terbatas. Kalau harga bisa turun 10%, kami bisa approve sekarang juga.',
                'Urgent situation. Bisa kasih emergency discount? Kami butuh keputusan cepat.',
                'Timeline ketat. Best and final offer sekarang. Bisa atau tidak?'
            ]
        };

        const responses = priceResponses[style] || priceResponses.analytical;
        return responses[Math.floor(Math.random() * responses.length)];
    }

    // Quality/specification responses
    if (input.includes('kualitas') || input.includes('quality') || input.includes('spek') || input.includes('spec')) {
        return 'Kualitas memang penting, tapi harga juga harus masuk akal. Bisa kasih guarantee dan after-sales support yang baik?';
    }

    // Delivery/timeline responses
    if (input.includes('delivery') || input.includes('kirim') || input.includes('waktu') || input.includes('timeline')) {
        return 'Delivery time acceptable. Tapi kalau bisa lebih cepat dengan harga yang sama, itu lebih baik.';
    }

    // Generic responses based on style
    const genericResponses: Record<string, string[]> = {
        aggressive: [
            'Itu belum cukup. Kami butuh deal yang lebih agresif.',
            'Masih jauh dari ekspektasi kami. Bisa lebih baik lagi?',
            'Kompetitor kasih penawaran lebih menarik. Bisa beat their offer?'
        ],
        analytical: [
            'Menarik, tapi kami perlu data lebih detail untuk evaluasi.',
            'Bisa kasih comparison dengan alternatif lain?',
            'Kami perlu waktu untuk analyze proposal ini lebih dalam.'
        ],
        relationship: [
            'Kami appreciate effort Anda. Bagaimana kita bisa make this work untuk kedua belah pihak?',
            'Sebagai potential partner, kami harap bisa dapat consideration khusus.',
            'Long-term relationship kami yang utama. Bisa diskusi lebih flexible?'
        ],
        conservative: [
            'Kami perlu lebih banyak assurance sebelum commit.',
            'Risk mitigation kami yang utama. Bisa kasih lebih banyak guarantee?',
            'Management kami sangat careful. Perlu proposal yang lebih convincing.'
        ],
        time_pressed: [
            'Waktu terbatas. Bisa langsung final decision?',
            'Kami butuh jawaban sekarang. Yes or no?',
            'Timeline ketat. Kalau bisa deal sekarang, kami interested.'
        ]
    };

    const responses = genericResponses[style] || genericResponses.analytical;
    return responses[Math.floor(Math.random() * responses.length)];
};

// Quick actions
const suggestCounterOffer = () => {
    if (!currentSession.value) return;

    const suggestion = `Berdasarkan analisis, Anda bisa tawarkan:\n\n` +
        `1. Diskon 8-12% untuk volume order\n` +
        `2. Extended warranty sebagai value add\n` +
        `3. Flexible payment terms\n` +
        `4. Bundle dengan maintenance service\n\n` +
        `Fokus pada value proposition, bukan hanya harga.`;

    alert(suggestion);
};

const analyzeNegotiation = () => {
    if (!currentSession.value) return;

    const analysis = `Analisis Negosiasi:\n\n` +
        `• Customer Style: ${currentSession.value.customerPersona.negotiationStyle}\n` +
        `• Messages Exchanged: ${currentSession.value.messages.length}\n` +
        `• Current Margin: ${currentMargin.value.toFixed(1)}%\n` +
        `• Target Margin: ${currentSession.value.targetMargin}%\n\n` +
        `Rekomendasi: ${getRecommendation()}`;

    alert(analysis);
};

const getAdvice = () => {
    if (!currentSession.value) return;

    const advice = `AI Sales Advice:\n\n` +
        `Berdasarkan customer persona "${currentSession.value.customerPersona.negotiationStyle}":\n\n` +
        `${getPersonaAdvice(currentSession.value.customerPersona.negotiationStyle)}`;

    alert(advice);
};

const getRecommendation = (): string => {
    const margin = currentMargin.value;
    const target = currentSession.value!.targetMargin;

    if (margin >= target) {
        return 'Margin sudah tercapai. Fokus pada closing deal.';
    } else if (margin >= target * 0.8) {
        return 'Margin hampir tercapai. Tawarkan value add untuk justify harga.';
    } else {
        return 'Margin masih jauh dari target. Perlu strategi yang lebih agresif.';
    }
};

const getPersonaAdvice = (style: string): string => {
    const advice: Record<string, string> = {
        aggressive: 'Tetap tenang dan professional. Fokus pada value dan ROI. Jangan terburu-buru memberikan diskon besar.',
        analytical: 'Siapkan data dan fakta yang kuat. Berikan comparison dan analysis yang detail. Gunakan logical reasoning.',
        relationship: 'Bangun rapport dan trust. Fokus pada long-term partnership. Tawarkan exclusive benefits.',
        conservative: 'Berikan guarantee dan assurance. Minimize risk perception. Provide references dan testimonials.',
        time_pressed: 'Berikan sense of urgency yang positif. Tawarkan quick decision incentives. Streamline process.'
    };

    return advice[style] || advice.analytical;
};

// Utility functions
const scrollToBottom = async () => {
    await nextTick();
    if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
};

const startNewSession = () => {
    currentSession.value = null;
    messageInput.value = '';

    // Reset setup
    sessionSetup.value = {
        customerPersona: {
            name: '',
            industry: 'mining',
            negotiationStyle: 'analytical',
            budgetMin: 0,
            budgetMax: 0,
            keyConcerns: ''
        },
        selectedProducts: [],
        targetMargin: 20,
        difficultyLevel: 'medium'
    };
};

const endSession = () => {
    if (!currentSession.value) return;

    if (confirm('Yakin ingin mengakhiri session negosiasi ini?')) {
        currentSession.value.status = 'cancelled';
        setTimeout(() => {
            startNewSession();
        }, 1000);
    }
};

const exportSession = () => {
    if (!currentSession.value) return;

    try {
        const sessionData = {
            session: currentSession.value,
            summary: {
                duration: sessionDuration.value,
                messageCount: currentSession.value.messages.length,
                currentMargin: currentMargin.value,
                targetMargin: currentSession.value.targetMargin
            }
        };

        const blob = new Blob([JSON.stringify(sessionData, null, 2)], {
            type: 'application/json'
        });

        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `negotiation_session_${currentSession.value.id}.json`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Session berhasil diekspor!');

    } catch (error) {
        console.error('Error exporting session:', error);
        alert('Gagal mengekspor session.');
    }
};

// Initialize on mount
onMounted(() => {
    // Add initial product
    addProduct();
});
</script>
