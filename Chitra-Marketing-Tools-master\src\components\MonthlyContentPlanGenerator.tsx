import React, { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Label } from './ui/label';
import { Input } from './ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Checkbox } from './ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Separator } from './ui/separator';
import { Badge } from './ui/badge';
import { Calendar, Edit, Eye, Trash2, RefreshCw, X, Check, MoreVertical, Video } from 'lucide-react';
import { generateMonthlyContentPlan } from '../services/socialMediaService';
import { ContentCategory, ContentType, MonthlyContentPlan, MonthlyContentPlanPost } from '../types/socialMedia';
import { Spinner } from './Spinner';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from './ui/dialog';
import VideoScriptFromContent from './VideoScriptFromContent';
import { Textarea } from './ui/textarea';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from './ui/dropdown-menu';
import { STYLE_PRESETS } from '../types/imageGenerator';

// Import recharts conditionally to handle cases where it might not be available
let PieChart: any, Pie: any, Cell: any, ResponsiveContainer: any, Tooltip: any, Legend: any;
try {
  const recharts = require('recharts');
  PieChart = recharts.PieChart;
  Pie = recharts.Pie;
  Cell = recharts.Cell;
  ResponsiveContainer = recharts.ResponsiveContainer;
  Tooltip = recharts.Tooltip;
  Legend = recharts.Legend;
} catch (error) {
  // Create fallback components if recharts is not available
  PieChart = ({ children }: { children: React.ReactNode }) => <div>Chart not available</div>;
  Pie = () => null;
  Cell = () => null;
  ResponsiveContainer = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
  Tooltip = () => null;
  Legend = () => null;
}

interface MonthlyContentPlanGeneratorProps {
  onSavePost?: (post: MonthlyContentPlanPost) => void;
}

export default function MonthlyContentPlanGenerator({ onSavePost }: MonthlyContentPlanGeneratorProps) {
  const [year, setYear] = useState<number>(new Date().getFullYear());
  const [month, setMonth] = useState<number>(new Date().getMonth() + 1);
  const [postsPerWeek, setPostsPerWeek] = useState<number>(3);
  const [preferredCategories, setPreferredCategories] = useState<ContentCategory[]>([]);
  const [excludedCategories, setExcludedCategories] = useState<ContentCategory[]>([]);
  const [includeHolidays, setIncludeHolidays] = useState<boolean>(true);
  const [language, setLanguage] = useState<'id' | 'en'>('id');
  const [stylePreset, setStylePreset] = useState<string>(STYLE_PRESETS[0]);

  const [loading, setLoading] = useState<boolean>(false);
  const [regeneratingPost, setRegeneratingPost] = useState<boolean>(false);
  const [contentPlan, setContentPlan] = useState<MonthlyContentPlan | null>(null);
  const [activeTab, setActiveTab] = useState<string>('settings');

  // Dialog states
  const [viewDialogOpen, setViewDialogOpen] = useState<boolean>(false);
  const [editDialogOpen, setEditDialogOpen] = useState<boolean>(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [videoScriptDialogOpen, setVideoScriptDialogOpen] = useState<boolean>(false);
  const [selectedPost, setSelectedPost] = useState<MonthlyContentPlanPost | null>(null);

  // Edit form states
  const [editTitle, setEditTitle] = useState<string>('');
  const [editDescription, setEditDescription] = useState<string>('');
  const [editContentType, setEditContentType] = useState<ContentType>(ContentType.IMAGE);
  const [editContentCategory, setEditContentCategory] = useState<ContentCategory>(ContentCategory.PRODUCT);

  // Colors for the pie chart
  const COLORS = [
    '#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8',
    '#82CA9D', '#FF6B6B', '#6A7FDB', '#F7C59F', '#2D3047', '#FF9A8B'
  ];

  const months = [
    { value: 1, label: 'Januari' },
    { value: 2, label: 'Februari' },
    { value: 3, label: 'Maret' },
    { value: 4, label: 'April' },
    { value: 5, label: 'Mei' },
    { value: 6, label: 'Juni' },
    { value: 7, label: 'Juli' },
    { value: 8, label: 'Agustus' },
    { value: 9, label: 'September' },
    { value: 10, label: 'Oktober' },
    { value: 11, label: 'November' },
    { value: 12, label: 'Desember' }
  ];

  const categoryOptions = Object.entries(ContentCategory).map(([key, value]) => ({
    value,
    label: key.charAt(0).toUpperCase() + key.slice(1).toLowerCase().replace('_', ' ')
  }));

  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);

  const handlePreferredCategoryChange = (category: ContentCategory) => {
    if (preferredCategories.includes(category)) {
      setPreferredCategories(preferredCategories.filter(c => c !== category));
    } else {
      setPreferredCategories([...preferredCategories, category]);
    }
  };

  const handleExcludedCategoryChange = (category: ContentCategory) => {
    if (excludedCategories.includes(category)) {
      setExcludedCategories(excludedCategories.filter(c => c !== category));
    } else {
      setExcludedCategories([...excludedCategories, category]);
    }
  };

  const handleGeneratePlan = async () => {
    try {
      setLoading(true);

      const plan = await generateMonthlyContentPlan({
        year,
        month,
        postsPerWeek,
        preferredCategories,
        excludedCategories,
        includeHolidays,
        language
      });

      setContentPlan(plan);
      setActiveTab('plan');
    } catch (error) {
      console.error('Error generating content plan:', error);
      alert('Gagal membuat rencana konten. Silakan coba lagi.');
    } finally {
      setLoading(false);
    }
  };

  const handleSavePost = async (post: MonthlyContentPlanPost) => {
    try {
      if (onSavePost) {
        await onSavePost({ ...post, stylePreset });
        alert('Konten berhasil disimpan ke daftar konten');
      } else {
        console.error('onSavePost handler tidak tersedia');
        alert('Gagal menyimpan konten: Handler tidak tersedia');
      }
    } catch (error) {
      console.error('Error saving post:', error);
      alert('Gagal menyimpan konten: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  // View post details
  const handleViewPost = (post: MonthlyContentPlanPost) => {
    setSelectedPost(post);
    setViewDialogOpen(true);
  };

  // Open edit dialog
  const handleEditPost = (post: MonthlyContentPlanPost) => {
    setSelectedPost(post);
    setEditTitle(post.title);
    setEditDescription(post.description);
    setEditContentType(post.contentType);
    setEditContentCategory(post.contentCategory);
    setEditDialogOpen(true);
  };

  // Save edited post
  const handleSaveEdit = () => {
    if (!selectedPost || !contentPlan) return;

    const updatedPost: MonthlyContentPlanPost = {
      ...selectedPost,
      title: editTitle,
      description: editDescription,
      contentType: editContentType,
      contentCategory: editContentCategory
    };

    // Update the post in the content plan
    const updatedPosts = contentPlan.posts.map(post =>
      post.date === selectedPost.date && post.title === selectedPost.title ? updatedPost : post
    );

    // Update category distribution
    const updatedDistribution = { ...contentPlan.categoryDistribution };
    if (selectedPost.contentCategory !== editContentCategory) {
      updatedDistribution[selectedPost.contentCategory]--;
      updatedDistribution[editContentCategory] = (updatedDistribution[editContentCategory] || 0) + 1;
    }

    setContentPlan({
      ...contentPlan,
      posts: updatedPosts,
      categoryDistribution: updatedDistribution
    });

    setEditDialogOpen(false);
  };

  // Delete post
  const handleDeleteConfirm = () => {
    if (!selectedPost || !contentPlan) return;

    // Remove the post from the content plan
    const updatedPosts = contentPlan.posts.filter(post =>
      !(post.date === selectedPost.date && post.title === selectedPost.title)
    );

    // Update category distribution
    const updatedDistribution = { ...contentPlan.categoryDistribution };
    updatedDistribution[selectedPost.contentCategory]--;

    setContentPlan({
      ...contentPlan,
      posts: updatedPosts,
      categoryDistribution: updatedDistribution
    });

    setDeleteDialogOpen(false);
  };

  // Open delete confirmation dialog
  const handleDeletePost = (post: MonthlyContentPlanPost) => {
    setSelectedPost(post);
    setDeleteDialogOpen(true);
  };

  // Open video script dialog
  const handleCreateVideoScript = (post: MonthlyContentPlanPost) => {
    // Only allow video script creation for video content types
    if (post.contentType === ContentType.VIDEO || post.contentType === ContentType.REEL) {
      setSelectedPost(post);
      setVideoScriptDialogOpen(true);
    } else {
      alert('Hanya konten dengan tipe Video atau Reel yang dapat dibuat script video.');
    }
  };

  // Regenerate a single post
  const handleRegeneratePost = async (post: MonthlyContentPlanPost) => {
    if (!contentPlan) return;

    try {
      setSelectedPost(post);
      setRegeneratingPost(true);

      // Call the API to generate a new post for the same date and category
      const singlePostPlan = await generateMonthlyContentPlan({
        year,
        month,
        postsPerWeek: 1,
        preferredCategories: [post.contentCategory],
        language
      });

      if (singlePostPlan.posts.length > 0) {
        // Use the first generated post but keep the original date
        const newPost = {
          ...singlePostPlan.posts[0],
          date: post.date
        };

        // Update the post in the content plan
        const updatedPosts = contentPlan.posts.map(p =>
          p.date === post.date && p.title === post.title ? newPost : p
        );

        setContentPlan({
          ...contentPlan,
          posts: updatedPosts
        });
      }
    } catch (error) {
      console.error('Error regenerating post:', error);
      alert('Gagal membuat ulang konten. Silakan coba lagi.');
    } finally {
      setRegeneratingPost(false);
    }
  };

  const getContentTypeLabel = (type: ContentType) => {
    switch (type) {
      case ContentType.IMAGE: return 'Gambar';
      case ContentType.VIDEO: return 'Video';
      case ContentType.CAROUSEL: return 'Carousel';
      case ContentType.STORY: return 'Story';
      case ContentType.REEL: return 'Reel';
      default: return type;
    }
  };

  const getCategoryLabel = (category: ContentCategory) => {
    switch (category) {
      case ContentCategory.PRODUCT: return 'Produk';
      case ContentCategory.SAFETY: return 'Keselamatan';
      case ContentCategory.EDUCATIONAL: return 'Edukasi';
      case ContentCategory.QUIZ: return 'Kuis';
      case ContentCategory.ENGAGEMENT: return 'Engagement';
      case ContentCategory.SERVICE: return 'Layanan';
      case ContentCategory.TESTIMONIAL: return 'Testimoni';
      case ContentCategory.BEHIND_SCENES: return 'Behind the Scenes';
      case ContentCategory.INDUSTRY_NEWS: return 'Berita Industri';
      case ContentCategory.SEASONAL: return 'Musiman';
      case ContentCategory.PROMOTION: return 'Promosi';
      default: return category;
    }
  };

  const getCategoryColor = (category: ContentCategory) => {
    const index = Object.values(ContentCategory).indexOf(category);
    return COLORS[index % COLORS.length];
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' });
  };

  const renderDistributionChart = () => {
    if (!contentPlan) return null;

    const data = Object.entries(contentPlan.categoryDistribution)
      .filter(([_, count]) => count > 0)
      .map(([category, count]) => ({
        name: getCategoryLabel(category as ContentCategory),
        value: count,
        category: category as ContentCategory
      }));

    return (
      <div className="h-80 w-full">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              nameKey="name"
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={getCategoryColor(entry.category)} />
              ))}
            </Pie>
            <Tooltip formatter={(value) => [`${value} post`, 'Jumlah']} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
    );
  };

  // Fungsi untuk handle select post
  const handleSelectPost = (date: string) => {
    setSelectedPosts((prev) =>
      prev.includes(date) ? prev.filter((d) => d !== date) : [...prev, date]
    );
  };

  // Fungsi untuk select/deselect semua
  const handleSelectAll = () => {
    if (!contentPlan) return;
    if (selectedPosts.length === contentPlan.posts.length) {
      setSelectedPosts([]);
    } else {
      setSelectedPosts(contentPlan.posts.map((p) => p.date));
    }
  };

  // Fungsi simpan ke kalender Instagram
  const handleSaveSelectedPosts = async () => {
    if (!contentPlan || !onSavePost) return;
    const postsToSave = contentPlan.posts.filter((post) => selectedPosts.includes(post.date));
    for (const post of postsToSave) {
      await onSavePost(post);
    }
    setSelectedPosts([]); // reset setelah simpan
    alert('Konten terpilih berhasil disimpan ke Kalender Instagram!');
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Generator Rencana Konten Bulanan
        </CardTitle>
        <CardDescription>
          Buat rencana konten Instagram yang bervariasi untuk satu bulan penuh
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="settings">Pengaturan</TabsTrigger>
            <TabsTrigger value="plan" disabled={!contentPlan}>Rencana Konten</TabsTrigger>
            <TabsTrigger value="distribution" disabled={!contentPlan}>Distribusi Kategori</TabsTrigger>
          </TabsList>

          <TabsContent value="settings">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="year">Tahun</Label>
                <Input
                  id="year"
                  type="number"
                  value={year}
                  onChange={(e) => setYear(parseInt(e.target.value))}
                  min={2020}
                  max={2030}
                />
              </div>

              <div>
                <Label htmlFor="month">Bulan</Label>
                <div className="relative">
                  <select
                    id="month"
                    className="w-full h-10 px-3 py-2 text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
                    value={month}
                    onChange={(e) => setMonth(parseInt(e.target.value))}
                  >
                    {months.map((m) => (
                      <option key={m.value} value={m.value}>
                        {m.label}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="postsPerWeek">Jumlah Post per Minggu</Label>
                <Input
                  id="postsPerWeek"
                  type="number"
                  value={postsPerWeek}
                  onChange={(e) => setPostsPerWeek(parseInt(e.target.value) || 1)}
                  min={1}
                  max={7}
                />
              </div>

              <div>
                <Label htmlFor="language">Bahasa</Label>
                <div className="relative">
                  <select
                    id="language"
                    className="w-full h-10 px-3 py-2 text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
                    value={language}
                    onChange={(e) => setLanguage(e.target.value as 'id' | 'en')}
                  >
                    <option value="id">Indonesia</option>
                    <option value="en">Inggris</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="stylePreset">Style Preset</Label>
                <div className="relative">
                  <select
                    id="stylePreset"
                    className="w-full h-10 px-3 py-2 text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
                    value={stylePreset}
                    onChange={(e) => setStylePreset(e.target.value)}
                  >
                    {STYLE_PRESETS.map((preset) => (
                      <option key={preset} value={preset}>{preset}</option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>

              <div className="col-span-1 md:col-span-2">
                <Label className="mb-2 block">Kategori yang Diutamakan</Label>
                <div className="flex flex-wrap gap-2">
                  {categoryOptions.map((category) => (
                    <Badge
                      key={category.value}
                      variant={preferredCategories.includes(category.value as ContentCategory) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => handlePreferredCategoryChange(category.value as ContentCategory)}
                    >
                      {category.label}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="col-span-1 md:col-span-2">
                <Label className="mb-2 block">Kategori yang Dikecualikan</Label>
                <div className="flex flex-wrap gap-2">
                  {categoryOptions.map((category) => (
                    <Badge
                      key={category.value}
                      variant={excludedCategories.includes(category.value as ContentCategory) ? 'destructive' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => handleExcludedCategoryChange(category.value as ContentCategory)}
                    >
                      {category.label}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="col-span-1 md:col-span-2 flex items-center space-x-2">
                <Checkbox
                  id="includeHolidays"
                  checked={includeHolidays}
                  onCheckedChange={(checked) => setIncludeHolidays(checked as boolean)}
                />
                <Label htmlFor="includeHolidays">Sertakan konten untuk hari libur dan hari penting</Label>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="plan">
            {contentPlan && (
              <div>
                <h3 className="text-lg font-medium mb-2">
                  Rencana Konten {months.find(m => m.value === contentPlan.month)?.label} {contentPlan.year}
                </h3>
                <p className="text-sm text-gray-500 mb-4">
                  Total {contentPlan.posts.length} post ({postsPerWeek} post per minggu)
                </p>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>
                          <input
                            type="checkbox"
                            checked={selectedPosts.length === contentPlan.posts.length && contentPlan.posts.length > 0}
                            onChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead>Tanggal</TableHead>
                        <TableHead>Kategori</TableHead>
                        <TableHead>Tipe</TableHead>
                        <TableHead>Judul</TableHead>
                        <TableHead>Deskripsi</TableHead>
                        <TableHead className="text-right">Aksi</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {contentPlan.posts.sort((a, b) => a.date.localeCompare(b.date)).map((post, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <input
                              type="checkbox"
                              checked={selectedPosts.includes(post.date)}
                              onChange={() => handleSelectPost(post.date)}
                            />
                          </TableCell>
                          <TableCell>{formatDate(post.date)}</TableCell>
                          <TableCell>
                            <Badge style={{ backgroundColor: getCategoryColor(post.contentCategory) }}>
                              {getCategoryLabel(post.contentCategory)}
                            </Badge>
                          </TableCell>
                          <TableCell>{getContentTypeLabel(post.contentType)}</TableCell>
                          <TableCell className="max-w-[150px] truncate">{post.title}</TableCell>
                          <TableCell className="max-w-[200px] truncate">{post.description}</TableCell>
                          <TableCell>
                            <div className="flex justify-end gap-1">
                              <button
                                className="p-1 rounded-md hover:bg-blue-50 text-blue-600 transition-colors"
                                onClick={() => handleViewPost(post)}
                                title="Lihat Detail"
                              >
                                <Eye className="h-5 w-5" />
                              </button>
                              <button
                                className="p-1 rounded-md hover:bg-green-50 text-green-600 transition-colors"
                                onClick={() => handleEditPost(post)}
                                title="Edit Konten"
                              >
                                <Edit className="h-5 w-5" />
                              </button>
                              <button
                                className="p-1 rounded-md hover:bg-red-50 text-red-600 transition-colors"
                                onClick={() => handleDeletePost(post)}
                                title="Hapus Konten"
                              >
                                <Trash2 className="h-5 w-5" />
                              </button>
                              <button
                                className="p-1 rounded-md hover:bg-purple-50 text-purple-600 transition-colors"
                                onClick={() => handleRegeneratePost(post)}
                                title="Regenerate Konten"
                                disabled={regeneratingPost && selectedPost?.date === post.date}
                              >
                                {regeneratingPost && selectedPost?.date === post.date ? (
                                  <Spinner className="h-5 w-5" />
                                ) : (
                                  <RefreshCw className="h-5 w-5" />
                                )}
                              </button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
                <div className="mt-4 flex justify-end">
                  <Button
                    onClick={handleSaveSelectedPosts}
                    disabled={selectedPosts.length === 0}
                    className="bg-blue-600 text-white hover:bg-blue-700"
                  >
                    Simpan ke Kalender Instagram
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="distribution">
            {contentPlan && (
              <div>
                <h3 className="text-lg font-medium mb-2">
                  Distribusi Kategori Konten
                </h3>
                <p className="text-sm text-gray-500 mb-4">
                  Visualisasi distribusi kategori konten dalam rencana
                </p>

                {renderDistributionChart()}

                <div className="mt-4">
                  <h4 className="text-md font-medium mb-2">Detail Distribusi</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {Object.entries(contentPlan.categoryDistribution)
                      .filter(([_, count]) => count > 0)
                      .map(([category, count]) => (
                        <div key={category} className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: getCategoryColor(category as ContentCategory) }}
                          ></div>
                          <span>{getCategoryLabel(category as ContentCategory)}: {count} post</span>
                        </div>
                      ))
                    }
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => setActiveTab('settings')}>
          Kembali ke Pengaturan
        </Button>
        <Button onClick={handleGeneratePlan} disabled={loading}>
          {loading ? <Spinner className="mr-2" /> : null}
          {loading ? 'Membuat Rencana...' : 'Buat Rencana Konten'}
        </Button>
      </CardFooter>

      {/* View Post Dialog */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Detail Konten</DialogTitle>
            <DialogDescription>
              Informasi lengkap tentang konten yang dipilih
            </DialogDescription>
          </DialogHeader>

          {selectedPost && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Tanggal</Label>
                  <p className="text-sm">{formatDate(selectedPost.date)}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Tipe Konten</Label>
                  <p className="text-sm">{getContentTypeLabel(selectedPost.contentType)}</p>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Kategori</Label>
                <div className="mt-1">
                  <Badge style={{ backgroundColor: getCategoryColor(selectedPost.contentCategory) }}>
                    {getCategoryLabel(selectedPost.contentCategory)}
                  </Badge>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium">Judul</Label>
                <p className="text-sm mt-1">{selectedPost.title}</p>
              </div>

              <div>
                <Label className="text-sm font-medium">Deskripsi</Label>
                <div className="mt-1 p-3 bg-gray-50 rounded-md text-sm whitespace-pre-wrap">
                  {selectedPost.description}
                </div>
              </div>

              {selectedPost.recommendedHashtags && selectedPost.recommendedHashtags.length > 0 && (
                <div>
                  <Label className="text-sm font-medium">Hashtag yang Direkomendasikan</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {selectedPost.recommendedHashtags.map((tag, i) => (
                      <Badge key={i} variant="secondary">#{tag}</Badge>
                    ))}
                  </div>
                </div>
              )}

              {selectedPost.suggestedImageDescription && (
                <div>
                  <Label className="text-sm font-medium">Deskripsi Gambar yang Disarankan</Label>
                  <p className="text-sm mt-1">{selectedPost.suggestedImageDescription}</p>
                </div>
              )}

              {selectedPost.stylePreset && (
                <div>
                  <Label className="text-sm font-medium">Style Preset</Label>
                  <p className="text-sm mt-1">{selectedPost.stylePreset}</p>
                </div>
              )}
            </div>
          )}

          <DialogFooter className="flex justify-between">
            <Button variant="outline" onClick={() => setViewDialogOpen(false)}>
              Tutup
            </Button>
            <Button onClick={() => {
              setViewDialogOpen(false);
              if (selectedPost) handleSavePost(selectedPost);
            }}>
              Simpan Konten
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Post Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Edit Konten</DialogTitle>
            <DialogDescription>
              Ubah detail konten sesuai kebutuhan
            </DialogDescription>
          </DialogHeader>

          {selectedPost && (
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-date">Tanggal</Label>
                  <Input
                    id="edit-date"
                    value={formatDate(selectedPost.date)}
                    disabled
                  />
                </div>

                <div>
                  <Label htmlFor="edit-content-type">Tipe Konten</Label>
                  <div className="relative">
                    <select
                      id="edit-content-type"
                      className="w-full h-10 px-3 py-2 text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
                      value={editContentType}
                      onChange={(e) => setEditContentType(e.target.value as ContentType)}
                    >
                      <option value={ContentType.IMAGE}>Gambar</option>
                      <option value={ContentType.VIDEO}>Video</option>
                      <option value={ContentType.CAROUSEL}>Carousel</option>
                      <option value={ContentType.STORY}>Story</option>
                      <option value={ContentType.REEL}>Reel</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="edit-category">Kategori</Label>
                <div className="relative">
                  <select
                    id="edit-category"
                    className="w-full h-10 px-3 py-2 text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
                    value={editContentCategory}
                    onChange={(e) => setEditContentCategory(e.target.value as ContentCategory)}
                  >
                    {Object.values(ContentCategory).map((category) => (
                      <option key={category} value={category}>
                        {getCategoryLabel(category)}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="edit-title">Judul</Label>
                <Input
                  id="edit-title"
                  value={editTitle}
                  onChange={(e) => setEditTitle(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="edit-description">Deskripsi</Label>
                <Textarea
                  id="edit-description"
                  value={editDescription}
                  onChange={(e) => setEditDescription(e.target.value)}
                  rows={6}
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Batal
            </Button>
            <Button onClick={handleSaveEdit}>
              Simpan Perubahan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Konten</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus konten ini? Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>

          {selectedPost && (
            <div className="py-4">
              <p className="font-medium">{selectedPost.title}</p>
              <p className="text-sm text-gray-500 mt-1">{formatDate(selectedPost.date)} - {getCategoryLabel(selectedPost.contentCategory)}</p>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Batal
            </Button>
            <Button variant="destructive" onClick={handleDeleteConfirm}>
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Video Script Dialog */}
      <Dialog open={videoScriptDialogOpen} onOpenChange={setVideoScriptDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Buat Script Video</DialogTitle>
            <DialogDescription>
              Buat script video berdasarkan konten yang dipilih
            </DialogDescription>
          </DialogHeader>

          {selectedPost && (
            <VideoScriptFromContent
              post={selectedPost}
              onClose={() => setVideoScriptDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
}
