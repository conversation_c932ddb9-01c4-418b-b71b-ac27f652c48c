import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import { BundleItem, PricingResult } from '../types';
import { formatCurrency } from '../utils/pricing';

// Add autotable to jsPDF
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

export function generateQuotationPDF(
  bundleItems: BundleItem[],
  pricingResult: PricingResult,
  additionalInfo: string
): void {
  try {
    console.log('Generating PDF quotation...');

    // Create a new PDF document
    const doc = new jsPDF();
    console.log('PDF document created');

    // Add company logo/header
    addHeader(doc);
    console.log('Header added');

    // Add quotation title and information
    addQuotationInfo(doc, additionalInfo);
    console.log('Quotation info added');

    // Add product table
    addProductTable(doc, bundleItems);
    console.log('Product table added');

    // Add pricing summary
    addPricingSummary(doc, pricingResult);
    console.log('Pricing summary added');

    // Add footer
    addFooter(doc);
    console.log('Footer added');

    // Save the PDF
    console.log('Saving PDF...');
    doc.save('BundleBoost-Quotation.pdf');
    console.log('PDF saved successfully');

    // Show success message to user
    alert('PDF quotation generated successfully!');
  } catch (error) {
    console.error('Error generating PDF:', error);
    alert('Failed to generate PDF. Please check the console for details.');
  }
}

function addHeader(doc: jsPDF): void {
  // Add company name
  doc.setFontSize(22);
  doc.setTextColor(0, 102, 204); // Blue color
  doc.setFont('helvetica', 'bold');
  doc.text('BundleBoost', 105, 20, { align: 'center' });

  // Add tagline
  doc.setFontSize(12);
  doc.setTextColor(100, 100, 100); // Gray color
  doc.setFont('helvetica', 'italic');
  doc.text('Professional Automotive Product Bundles', 105, 28, { align: 'center' });

  // Add horizontal line
  doc.setDrawColor(0, 102, 204); // Blue color
  doc.setLineWidth(0.5);
  doc.line(20, 32, 190, 32);
}

function addQuotationInfo(doc: jsPDF, additionalInfo: string): void {
  // Add quotation title
  doc.setFontSize(18);
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'bold');
  doc.text('QUOTATION', 105, 45, { align: 'center' });

  // Add date
  const today = new Date();
  const formattedDate = today.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  });

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text(`Date: ${formattedDate}`, 20, 55);

  // Add quotation number
  const quotationNumber = `QT-${today.getFullYear()}${(today.getMonth() + 1).toString().padStart(2, '0')}${today.getDate().toString().padStart(2, '0')}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
  doc.text(`Quotation No: ${quotationNumber}`, 20, 62);

  // Add validity
  const validUntil = new Date();
  validUntil.setDate(today.getDate() + 14); // Valid for 14 days
  const formattedValidUntil = validUntil.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  });
  doc.text(`Valid Until: ${formattedValidUntil}`, 20, 69);

  // Add additional info if provided
  if (additionalInfo && additionalInfo.trim() !== '') {
    doc.setFontSize(10);
    doc.setFont('helvetica', 'italic');
    doc.text(`Notes: ${additionalInfo}`, 20, 76);
  }

  // Add horizontal line
  doc.setDrawColor(200, 200, 200); // Light gray
  doc.setLineWidth(0.2);
  doc.line(20, 80, 190, 80);
}

function addProductTable(doc: jsPDF, bundleItems: BundleItem[]): void {
  try {
    // Prepare table data
    const tableColumn = ['No', 'Product Description', 'Quantity', 'Unit Price (IDR)', 'Total (IDR)'];
    const tableRows = bundleItems.map((item, index) => [
      (index + 1).toString(),
      item.product.materialDescription,
      item.quantity.toString(),
      formatCurrency(item.product.price),
      formatCurrency(item.product.price * item.quantity)
    ]);

    console.log('Table data prepared:', tableRows);

    // Add the table using the correct approach for jspdf-autotable
    const autoTable = (doc as any).autoTable;
    if (typeof autoTable === 'function') {
      autoTable({
        head: [tableColumn],
        body: tableRows,
        startY: 85,
        theme: 'grid',
        styles: {
          fontSize: 9,
          cellPadding: 3,
          lineColor: [200, 200, 200],
          lineWidth: 0.1,
        },
        headStyles: {
          fillColor: [0, 102, 204],
          textColor: [255, 255, 255],
          fontStyle: 'bold',
        },
        columnStyles: {
          0: { cellWidth: 10 }, // No
          1: { cellWidth: 'auto' }, // Description
          2: { cellWidth: 20, halign: 'center' }, // Quantity
          3: { cellWidth: 35, halign: 'right' }, // Unit Price
          4: { cellWidth: 35, halign: 'right' }, // Total
        },
        alternateRowStyles: {
          fillColor: [245, 245, 245],
        },
      });
      console.log('Table added successfully');
    } else {
      console.error('autoTable function not available on jsPDF instance');
      throw new Error('autoTable function not available');
    }
  } catch (error) {
    console.error('Error adding product table:', error);
    throw error;
  }
}

function addPricingSummary(doc: jsPDF, pricingResult: PricingResult): void {
  // Get the final Y position after the table
  const finalY = (doc as any).lastAutoTable.finalY + 10;

  // Add pricing summary
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');

  // Subtotal
  doc.text('Subtotal:', 140, finalY);
  doc.text(formatCurrency(pricingResult.totalCost), 190, finalY, { align: 'right' });

  // Profit margin
  const profitAmount = pricingResult.recommendedPrice - pricingResult.totalCost;
  doc.text('Value Added:', 140, finalY + 7);
  doc.text(formatCurrency(profitAmount), 190, finalY + 7, { align: 'right' });

  // Horizontal line
  doc.setDrawColor(0, 0, 0);
  doc.setLineWidth(0.2);
  doc.line(140, finalY + 10, 190, finalY + 10);

  // Total
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('TOTAL:', 140, finalY + 17);
  doc.text(formatCurrency(pricingResult.recommendedPrice), 190, finalY + 17, { align: 'right' });

  // Add a box around the total
  doc.setDrawColor(0, 102, 204);
  doc.setLineWidth(0.5);
  doc.rect(135, finalY + 10, 55, 12);

  // Add payment terms
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text('Payment Terms: 100% payment upon delivery', 20, finalY + 30);

  // Add note about prices
  doc.setFontSize(9);
  doc.setFont('helvetica', 'italic');
  doc.text('* All prices are in Indonesian Rupiah (IDR) and include applicable taxes.', 20, finalY + 37);
}

function addFooter(doc: jsPDF): void {
  const pageHeight = doc.internal.pageSize.height;

  // Add horizontal line
  doc.setDrawColor(0, 102, 204);
  doc.setLineWidth(0.5);
  doc.line(20, pageHeight - 30, 190, pageHeight - 30);

  // Add contact information
  doc.setFontSize(9);
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(100, 100, 100);
  doc.text('BundleBoost - Your Trusted Automotive Product Partner', 105, pageHeight - 25, { align: 'center' });
  doc.text('Email: <EMAIL> | Phone: +62 ************ | Website: www.bundleboost.com', 105, pageHeight - 20, { align: 'center' });

  // Add page number
  doc.setFontSize(8);
  doc.text(`Page ${doc.getCurrentPageInfo().pageNumber} of ${doc.getNumberOfPages()}`, 105, pageHeight - 10, { align: 'center' });
}
