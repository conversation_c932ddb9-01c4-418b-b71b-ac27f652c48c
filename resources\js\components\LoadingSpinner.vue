<template>
    <div :class="containerClasses">
        <div :class="spinnerClasses">
            <Loader2 :class="iconClasses" />
        </div>
        <div v-if="message" :class="messageClasses">
            {{ message }}
        </div>
    </div>
</template>

<script setup lang="ts">
import { Loader2 } from 'lucide-vue-next';
import { computed } from 'vue';

// Props
interface Props {
    size?: 'sm' | 'md' | 'lg' | 'xl';
    variant?: 'primary' | 'secondary' | 'white';
    fullscreen?: boolean;
    overlay?: boolean;
    message?: string;
}

const props = withDefaults(defineProps<Props>(), {
    size: 'md',
    variant: 'primary',
    fullscreen: false,
    overlay: false,
    message: ''
});

// Computed classes
const containerClasses = computed(() => {
    const base = 'flex flex-col items-center justify-center';
    
    if (props.fullscreen) {
        return `${base} fixed inset-0 z-50 bg-white`;
    }
    
    if (props.overlay) {
        return `${base} absolute inset-0 z-40 bg-white bg-opacity-90`;
    }
    
    return `${base} p-4`;
});

const spinnerClasses = computed(() => {
    const base = 'animate-spin';
    
    switch (props.size) {
        case 'sm':
            return `${base} h-4 w-4`;
        case 'md':
            return `${base} h-6 w-6`;
        case 'lg':
            return `${base} h-8 w-8`;
        case 'xl':
            return `${base} h-12 w-12`;
        default:
            return `${base} h-6 w-6`;
    }
});

const iconClasses = computed(() => {
    const base = 'animate-spin';
    
    const sizeClasses = {
        sm: 'h-4 w-4',
        md: 'h-6 w-6',
        lg: 'h-8 w-8',
        xl: 'h-12 w-12'
    };
    
    const colorClasses = {
        primary: 'text-blue-600',
        secondary: 'text-gray-600',
        white: 'text-white'
    };
    
    return `${base} ${sizeClasses[props.size]} ${colorClasses[props.variant]}`;
});

const messageClasses = computed(() => {
    const base = 'mt-2 text-sm font-medium';
    
    const colorClasses = {
        primary: 'text-blue-600',
        secondary: 'text-gray-600',
        white: 'text-white'
    };
    
    return `${base} ${colorClasses[props.variant]}`;
});
</script>
