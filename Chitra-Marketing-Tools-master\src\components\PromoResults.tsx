import React, { useState } from 'react';
import { PromoResult, PromoItem, PromoConfig, PromoDiscount, DiscountType } from '../types/promotion';
import { FileText, Share2, Save, AlertTriangle } from 'lucide-react';
import { generatePromoPDF } from '../services/promoPdfService';
import { Customer } from '../types';
import { getCustomerById } from '../services/customerService';

interface PromoResultsProps {
  items: PromoItem[];
  result: PromoResult;
  config: PromoConfig;
  discount: PromoDiscount;
}

const PromoResults: React.FC<PromoResultsProps> = ({
  items,
  result,
  config,
  discount
}) => {
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  // Load customer data when customerId changes
  React.useEffect(() => {
    if (config.customerId) {
      const customer = getCustomerById(config.customerId);
      setSelectedCustomer(customer || null);
    } else {
      setSelectedCustomer(null);
    }
  }, [config.customerId]);

  // Format currency
  const formatCurrency = (value: number): string => {
    return `Rp ${value.toLocaleString('id-ID')}`;
  };

  // Calculate total selling price
  const calculateTotalSellingPrice = (): number => {
    return items.reduce((total, item) => total + (item.product.sellingPrice * item.quantity), 0);
  };

  // Handle generate PDF
  const handleGeneratePDF = () => {
    if (items.length > 0) {
      try {
        generatePromoPDF(items, result, config, discount, selectedCustomer);
      } catch (error) {
        console.error('Error generating PDF:', error);
        alert('Gagal membuat PDF. Silakan periksa konsol untuk detail.');
      }
    } else {
      alert('Silakan pilih produk terlebih dahulu sebelum membuat PDF.');
    }
  };

  // Handle share via WhatsApp
  const handleShareWhatsApp = () => {
    if (items.length === 0) {
      alert('Silakan pilih produk terlebih dahulu sebelum membagikan.');
      return;
    }

    try {
      // Create WhatsApp message
      let message = `*PENAWARAN PROMO ${config.name.toUpperCase()}*\n\n`;

      if (selectedCustomer) {
        message += `Kepada Yth.\n${selectedCustomer.name}\n${selectedCustomer.company || ''}\n\n`;
      }

      message += `Periode Promo: ${new Date(config.startDate).toLocaleDateString('id-ID')} - ${new Date(config.endDate).toLocaleDateString('id-ID')}\n\n`;

      message += `*Detail Produk:*\n`;
      items.forEach((item, index) => {
        message += `${index + 1}. ${item.product.materialDescription} (${item.quantity} unit)\n`;
      });

      message += `\n*Harga Normal:* ${formatCurrency(calculateTotalSellingPrice())}\n`;

      // Add discount info based on type
      if (discount.type === DiscountType.PERCENTAGE) {
        message += `*Diskon:* ${discount.value}%\n`;
      } else if (discount.type === DiscountType.FIXED_AMOUNT) {
        message += `*Diskon:* ${formatCurrency(discount.value)}\n`;
      } else if (discount.type === DiscountType.CASHBACK) {
        message += `*Cashback:* ${formatCurrency(discount.value)}\n`;
      } else if (discount.type === DiscountType.BONUS_UNIT) {
        message += `*Bonus:* ${discount.value} unit\n`;
      }

      message += `*Harga Promo:* ${formatCurrency(result.priceAfterPromo)}\n\n`;

      message += `Segera hubungi kami untuk informasi lebih lanjut.\n`;
      message += `Terima kasih.`;

      // Encode message for WhatsApp
      const encodedMessage = encodeURIComponent(message);
      const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;

      // Open WhatsApp in new window
      window.open(whatsappUrl, '_blank');
    } catch (error) {
      console.error('Error sharing via WhatsApp:', error);
      alert('Gagal membagikan via WhatsApp. Silakan periksa konsol untuk detail.');
    }
  };

  // Handle save draft
  const handleSaveDraft = () => {
    if (items.length === 0) {
      alert('Silakan pilih produk terlebih dahulu sebelum menyimpan draft.');
      return;
    }

    try {
      // Create draft object
      const draft = {
        config,
        items,
        discount,
        result,
        timestamp: new Date().toISOString()
      };

      // Get existing drafts or initialize empty array
      const existingDrafts = JSON.parse(localStorage.getItem('promoSimulationDrafts') || '[]');

      // Add new draft
      existingDrafts.push(draft);

      // Save to localStorage
      localStorage.setItem('promoSimulationDrafts', JSON.stringify(existingDrafts));

      alert('Draft promo berhasil disimpan!');
    } catch (error) {
      console.error('Error saving draft:', error);
      alert('Gagal menyimpan draft. Silakan periksa konsol untuk detail.');
    }
  };

  // Get promo type label
  const getPromoTypeLabel = (): string => {
    switch (config.type) {
      case 'DISCOUNT_PERCENTAGE':
        return 'Diskon Persentase';
      case 'DISCOUNT_FIXED':
        return 'Diskon Nominal Tetap';
      case 'CASHBACK':
        return 'Cashback';
      case 'BUY_GET':
        return 'Buy X Get Y';
      case 'BUNDLING':
        return 'Bundling Produk/Jasa';
      case 'LOYALTY':
        return 'Program Loyalitas';
      case 'SEASONAL':
        return 'Promo Musiman';
      case 'CLEARANCE':
        return 'Clearance Sale';
      case 'TRADE_IN':
        return 'Program Trade-in';
      case 'VOLUME':
        return 'Diskon Volume';
      default:
        return 'Promo';
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Hasil Simulasi</h2>
        </div>

        {items.length === 0 ? (
          <div className="text-gray-500 italic">
            Silakan pilih produk dan konfigurasi promo untuk melihat hasil simulasi
          </div>
        ) : (
          <div className="space-y-4">
            {selectedCustomer && (
              <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <h3 className="text-sm font-medium text-blue-800 mb-1">Informasi Customer</h3>
                <div className="text-sm text-blue-700">
                  <p><span className="font-medium">Nama:</span> {selectedCustomer.name}</p>
                  {selectedCustomer.company && <p><span className="font-medium">Perusahaan:</span> {selectedCustomer.company}</p>}
                  <p><span className="font-medium">Kontak:</span> {selectedCustomer.email} | {selectedCustomer.phone}</p>
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Harga Normal</h3>
                <p className="text-lg font-semibold">{formatCurrency(calculateTotalSellingPrice())}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Harga Setelah Promo</h3>
                <p className="text-lg font-semibold text-green-600">{formatCurrency(result.priceAfterPromo)}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Total Diskon</h3>
                <p className="text-lg font-semibold text-red-600">{formatCurrency(result.totalDiscount)}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">Margin Setelah Promo</h3>
                <p className="text-lg font-semibold">{result.marginAfterPromo.toFixed(2)}%</p>
              </div>

              {result.breakEvenPoint && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Break-Even Point</h3>
                  <p className="text-lg font-semibold">{result.breakEvenPoint} unit</p>
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium text-gray-500">Estimasi Laba</h3>
                <p className="text-lg font-semibold">{formatCurrency(result.estimatedProfit)}</p>
              </div>
            </div>

            {result.warning && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md flex items-start">
                <AlertTriangle className="text-yellow-500 mr-2 mt-0.5 flex-shrink-0" size={18} />
                <div>
                  <h3 className="text-sm font-medium text-yellow-800">Peringatan</h3>
                  <p className="text-sm text-yellow-700">{result.warning}</p>
                </div>
              </div>
            )}

            <div className="mt-6 pt-4 border-t border-gray-200">
              <h3 className="text-md font-medium mb-2">Interpretasi Bisnis</h3>
              <div className="text-sm text-gray-700 space-y-2">
                <p>
                  Promo ini akan memberikan diskon sebesar {formatCurrency(result.totalDiscount)} dari harga normal {formatCurrency(calculateTotalSellingPrice())}.
                </p>
                <p>
                  Dengan margin setelah promo sebesar {result.marginAfterPromo.toFixed(2)}%,
                  {result.marginAfterPromo < 10
                    ? ' perhatikan bahwa margin ini di bawah 10% yang bisa berisiko terhadap profitabilitas.'
                    : ' margin ini masih dalam batas yang sehat untuk profitabilitas bisnis.'}
                </p>
                {result.breakEvenPoint && (
                  <p>
                    Anda perlu menjual minimal {result.breakEvenPoint} unit untuk mencapai titik impas (break-even point).
                  </p>
                )}
                <p>
                  Dengan target penjualan {discount.targetSales} unit, estimasi laba yang akan diperoleh adalah {formatCurrency(result.estimatedProfit)}.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold mb-4">Output Tambahan</h2>

        <div className="space-y-3">
          <button
            onClick={handleGeneratePDF}
            disabled={items.length === 0}
            className={`w-full flex items-center justify-center px-4 py-2 rounded-md text-white ${
              items.length === 0
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            <FileText size={16} className="mr-2" />
            Generate Penawaran PDF
          </button>

          <button
            onClick={handleShareWhatsApp}
            disabled={items.length === 0}
            className={`w-full flex items-center justify-center px-4 py-2 rounded-md text-white ${
              items.length === 0
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-green-600 hover:bg-green-700'
            }`}
          >
            <Share2 size={16} className="mr-2" />
            Bagikan via WhatsApp
          </button>

          <button
            onClick={handleSaveDraft}
            disabled={items.length === 0}
            className={`w-full flex items-center justify-center px-4 py-2 rounded-md text-white ${
              items.length === 0
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-purple-600 hover:bg-purple-700'
            }`}
          >
            <Save size={16} className="mr-2" />
            Simpan Draft Promo
          </button>
        </div>


      </div>
    </div>
  );
};

export default PromoResults;
