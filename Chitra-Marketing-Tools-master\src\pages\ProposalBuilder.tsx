import React from 'react';
import { Container, Typography, Box } from '@mui/material';
import ProposalForm from '../components/ProposalBuilder/ProposalForm';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const ProposalBuilder: React.FC = () => {
  return (
    <Container maxWidth="lg">
      <Box sx={{ my: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom align="center">
          Proposal Builder
        </Typography>
        <Typography variant="subtitle1" gutterBottom align="center" color="text.secondary">
          Buat proposal profesional dengan mudah menggunakan template yang sudah disiapkan
        </Typography>
        
        <ProposalForm />
      </Box>
      
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </Container>
  );
};

export default ProposalBuilder; 