import { v4 as uuidv4 } from 'uuid';
import * as XLSX from 'xlsx';
import <PERSON> from 'papapar<PERSON>';
import { saveAs } from 'file-saver';
import {
  getAllItems,
  updateItem,
  deleteItem,
  replaceAllItems,
  STORE_NAMES
} from './indexedDBService';

// Define the SalesRevenue item interface
export interface SalesRevenueItem {
  id: string;
  customerName: string;
  salesman: string;
  materialDescription: string;
  qty: number;
  revenueInDocCurr: number;
  billingDate: string;
  poDate: string;
  [key: string]: any; // Allow additional properties for flexibility
}

// Interface for summary data
export interface SalesRevenueSummary {
  totalRevenue: number;
  totalCustomers: number;
  totalItems: number;
  topCustomers: {
    customer: string;
    revenue: number;
  }[];
  topSalesmen: {
    salesman: string;
    revenue: number;
  }[];
  monthlyRevenue: {
    month: string;
    revenue: number;
  }[];
}

// Store name for IndexedDB
const STORE_NAME = STORE_NAMES.SALES_REVENUE_2025;

// Local storage key (as fallback)
const STORAGE_KEY = 'salesRevenue2025Data';

// In-memory cache
let salesRevenueCache: SalesRevenueItem[] | null = null;

// Sample data for initial load
const SAMPLE_SALES_REVENUE_DATA: SalesRevenueItem[] = [
  {
    id: uuidv4(),
    customerName: 'PT Mining Sejahtera',
    salesman: 'John Doe',
    materialDescription: 'XDRA 27.00R49 E4',
    qty: 4,
    revenueInDocCurr: 340000000,
    billingDate: '2025-01-15',
    poDate: '2025-01-05'
  },
  {
    id: uuidv4(),
    customerName: 'PT Coal Makmur',
    salesman: 'Jane Smith',
    materialDescription: 'XDRA 24.00R35 E4',
    qty: 6,
    revenueInDocCurr: 432000000,
    billingDate: '2025-01-20',
    poDate: '2025-01-10'
  },
  {
    id: uuidv4(),
    customerName: 'PT Tambang Abadi',
    salesman: 'John Doe',
    materialDescription: 'XDRA 27.00R49 E4',
    qty: 2,
    revenueInDocCurr: 170000000,
    billingDate: '2025-02-05',
    poDate: '2025-01-25'
  }
];

/**
 * Load sales revenue data from IndexedDB, with fallback to localStorage and sample data
 */
export const loadSalesRevenueData = async (): Promise<SalesRevenueItem[]> => {
  // If we have cached data, return it
  if (salesRevenueCache) {
    return [...salesRevenueCache];
  }

  try {
    console.log('Attempting to load data from IndexedDB...');
    // First try to load from IndexedDB
    const indexedDBData = await getAllItems<SalesRevenueItem>(STORE_NAME);

    if (indexedDBData && indexedDBData.length > 0) {
      console.log(`Loaded ${indexedDBData.length} items from IndexedDB`);
      salesRevenueCache = indexedDBData;
      return [...indexedDBData];
    }

    console.log('No data in IndexedDB, trying localStorage...');
    // If no data in IndexedDB, try localStorage as fallback
    const storedData = localStorage.getItem(STORAGE_KEY);
    if (storedData) {
      const parsedData = JSON.parse(storedData) as SalesRevenueItem[];
      console.log(`Loaded ${parsedData.length} items from localStorage`);

      // Save to IndexedDB for future use
      await replaceAllItems(STORE_NAME, parsedData);
      console.log('Migrated data from localStorage to IndexedDB');

      salesRevenueCache = parsedData;
      return [...parsedData];
    }

    console.log('No data in localStorage, using sample data...');
    // If no data in localStorage, use sample data
    // Save sample data to IndexedDB for future use
    await replaceAllItems(STORE_NAME, SAMPLE_SALES_REVENUE_DATA);
    console.log('Saved sample data to IndexedDB');

    salesRevenueCache = [...SAMPLE_SALES_REVENUE_DATA];
    return [...SAMPLE_SALES_REVENUE_DATA];
  } catch (error) {
    console.error('Error loading sales revenue data:', error);
    return [...SAMPLE_SALES_REVENUE_DATA];
  }
};

/**
 * Save sales revenue data to IndexedDB and localStorage as backup
 */
export const saveSalesRevenueData = async (data: SalesRevenueItem[]): Promise<boolean> => {
  try {
    console.log('Saving sales revenue data, items count:', data.length);

    // Update cache
    salesRevenueCache = [...data];
    console.log('Cache updated');

    // Save to IndexedDB
    const result = await replaceAllItems(STORE_NAME, data);
    console.log('Data saved to IndexedDB:', result);

    // Save to localStorage as backup
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    console.log('Data saved to localStorage as backup');

    // In a real implementation, we would also save to file system here
    // For now, we'll simulate it by creating a downloadable file
    // But we'll only do this for explicit user-initiated saves, not for automatic saves
    // This prevents unwanted downloads when deleting items

    // Uncomment this for explicit user-initiated saves
    /*
    const jsonString = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    saveAs(blob, 'salesRevenue2025Data.json');
    console.log('Data file prepared for download');
    */

    return true;
  } catch (error) {
    console.error('Error saving sales revenue data:', error);

    // Try to save to localStorage as fallback
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
      console.log('Fallback: Data saved to localStorage');
    } catch (localStorageError) {
      console.error('Error saving to localStorage:', localStorageError);
    }

    return false;
  }
};

/**
 * Get all sales revenue items
 */
export const getAllSalesRevenueItems = async (): Promise<SalesRevenueItem[]> => {
  return loadSalesRevenueData();
};

/**
 * Get sales revenue item by ID
 */
export const getSalesRevenueItemById = async (id: string): Promise<SalesRevenueItem | null> => {
  const items = await loadSalesRevenueData();
  return items.find(item => item.id === id) || null;
};

/**
 * Create a new sales revenue item
 */
export const createSalesRevenueItem = async (item: Omit<SalesRevenueItem, 'id'>): Promise<SalesRevenueItem> => {
  // Ensure all required fields are present with default values if needed
  const newItem: SalesRevenueItem = {
    customerName: '',
    salesman: '',
    materialDescription: '',
    qty: 0,
    revenueInDocCurr: 0,
    billingDate: '',
    poDate: '',
    ...item,
    id: uuidv4()
  };

  try {
    console.log('Creating new sales revenue item:', newItem);

    // Add to IndexedDB directly
    await updateItem(STORE_NAME, newItem);
    console.log('Item added to IndexedDB');

    // Update cache
    if (salesRevenueCache) {
      salesRevenueCache = [...salesRevenueCache, newItem];
    } else {
      // If cache doesn't exist, load all data
      await loadSalesRevenueData();
    }

    return newItem;
  } catch (error) {
    console.error('Error creating sales revenue item:', error);

    // Fallback to the old method if direct IndexedDB update fails
    const items = await loadSalesRevenueData();
    items.push(newItem);
    await saveSalesRevenueData(items);

    return newItem;
  }
};

/**
 * Update a sales revenue item
 */
export const updateSalesRevenueItem = async (id: string, item: Partial<SalesRevenueItem>): Promise<SalesRevenueItem | null> => {
  try {
    console.log(`Updating sales revenue item with ID: ${id}`);

    // Get the current item
    const items = await loadSalesRevenueData();
    const existingItem = items.find(i => i.id === id);

    if (!existingItem) {
      console.error(`Item with ID ${id} not found`);
      return null;
    }

    // Create updated item
    const updatedItem: SalesRevenueItem = {
      ...existingItem,
      ...item
    };

    // Update in IndexedDB directly
    await updateItem(STORE_NAME, updatedItem);
    console.log('Item updated in IndexedDB');

    // Update cache
    if (salesRevenueCache) {
      salesRevenueCache = salesRevenueCache.map(i =>
        i.id === id ? updatedItem : i
      );
    }

    return updatedItem;
  } catch (error) {
    console.error(`Error updating sales revenue item with ID ${id}:`, error);

    // Fallback to the old method if direct IndexedDB update fails
    const items = await loadSalesRevenueData();
    const index = items.findIndex(i => i.id === id);

    if (index === -1) {
      return null;
    }

    const updatedItem = {
      ...items[index],
      ...item
    };

    items[index] = updatedItem;
    await saveSalesRevenueData(items);

    return updatedItem;
  }
};

/**
 * Delete a sales revenue item
 */
export const deleteSalesRevenueItem = async (id: string): Promise<boolean> => {
  console.log('Service: Deleting item with ID:', id);

  try {
    // Delete from IndexedDB directly
    await deleteItem(STORE_NAME, id);
    console.log('Item deleted from IndexedDB');

    // Update cache if it exists
    if (salesRevenueCache) {
      salesRevenueCache = salesRevenueCache.filter(item => item.id !== id);
    }

    return true;
  } catch (error) {
    console.error(`Error deleting item with ID ${id} from IndexedDB:`, error);

    // Fallback to the old method if direct IndexedDB delete fails
    // Load the current data
    const items = await loadSalesRevenueData();
    console.log('Service: Total items before delete:', items.length);

    // Check if the item exists
    const itemExists = items.some(item => item.id === id);
    if (!itemExists) {
      console.error('Service: Item not found with ID:', id);
      return false;
    }

    // Filter out the item to delete
    const filteredItems = items.filter(item => item.id !== id);
    console.log('Service: Total items after filter:', filteredItems.length);

    // Check if any item was removed
    if (filteredItems.length === items.length) {
      console.error('Service: No items were removed during filtering');
      return false;
    }

    // Save the updated data
    try {
      const saveResult = await saveSalesRevenueData(filteredItems);
      console.log('Service: Save result:', saveResult);

      return true;
    } catch (saveError) {
      console.error('Service: Error saving data after delete:', saveError);
      return false;
    }
  }
};

/**
 * Import sales revenue data from CSV or Excel file
 */
export const importSalesRevenueData = async (
  file: File,
  fieldMapping: Record<string, string>
): Promise<{ success: boolean; importedCount: number; errors: string[] }> => {
  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader();

      reader.onload = async (e) => {
        try {
          const result = e.target?.result;
          if (!result) {
            reject(new Error('Failed to read file'));
            return;
          }

          let jsonData: any[] = [];

          // Parse based on file type
          if (file.name.endsWith('.csv')) {
            // Parse CSV
            const csvText = result as string;
            Papa.parse(csvText, {
              header: true,
              complete: (results) => {
                jsonData = results.data as any[];
                processImportedData();
              },
              error: (error: any) => {
                reject(new Error(`CSV parsing error: ${error.message}`));
              }
            });
          } else {
            // Parse Excel
            const data = result as ArrayBuffer;
            const workbook = XLSX.read(data);
            const worksheet = workbook.Sheets[workbook.SheetNames[0]];
            jsonData = XLSX.utils.sheet_to_json(worksheet);
            processImportedData();
          }

          async function processImportedData() {
            if (jsonData.length === 0) {
              reject(new Error('No data found in the imported file'));
              return;
            }

            // Process the data with field mapping
            const existingItems = await loadSalesRevenueData();
            const newItems: SalesRevenueItem[] = [];
            const errors: string[] = [];

            // Function to get value using field mapping
            const getFieldValue = (row: any, fieldType: string): any => {
              const mappedField = fieldMapping[fieldType];
              if (mappedField && row[mappedField] !== undefined) {
                return row[mappedField];
              }
              return '';
            };

            // Process each row
            for (let i = 0; i < jsonData.length; i++) {
              const row = jsonData[i];

              try {
                // Parse and format dates properly
                const parseDateValue = (value: any): string => {
                  if (!value) return '';

                  try {
                    // Check if it's an Excel date (number)
                    if (typeof value === 'number') {
                      // Excel dates are days since 1900-01-01 (except for the leap year bug)
                      const excelEpoch = new Date(1899, 11, 30);
                      const date = new Date(excelEpoch.getTime() + value * 24 * 60 * 60 * 1000);
                      return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
                    }

                    // Try to parse as date string
                    const dateValue = String(value).trim();
                    const date = new Date(dateValue);

                    // Check if valid date
                    if (!isNaN(date.getTime())) {
                      return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
                    }

                    return dateValue; // Return as is if can't parse
                  } catch (e) {
                    console.error('Error parsing date:', value, e);
                    return String(value || '');
                  }
                };

                const billingDateValue = getFieldValue(row, 'billingDate');
                const poDateValue = getFieldValue(row, 'poDate');

                console.log('Raw billing date:', billingDateValue);
                console.log('Raw PO date:', poDateValue);

                const formattedBillingDate = parseDateValue(billingDateValue);
                const formattedPoDate = parseDateValue(poDateValue);

                console.log('Formatted billing date:', formattedBillingDate);
                console.log('Formatted PO date:', formattedPoDate);

                const newItem: SalesRevenueItem = {
                  id: uuidv4(),
                  customerName: String(getFieldValue(row, 'customerName') || ''),
                  salesman: String(getFieldValue(row, 'salesman') || ''),
                  materialDescription: String(getFieldValue(row, 'materialDescription') || ''),
                  qty: Number(getFieldValue(row, 'qty')) || 0,
                  revenueInDocCurr: Number(getFieldValue(row, 'revenueInDocCurr')) || 0,
                  billingDate: formattedBillingDate,
                  poDate: formattedPoDate
                };

                newItems.push(newItem);
              } catch (error) {
                errors.push(`Error processing row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
              }
            }

            // Save the combined data
            const combinedItems = [...existingItems, ...newItems];

            try {
              // Try to save directly to IndexedDB first
              await replaceAllItems(STORE_NAME, combinedItems);
              console.log('Imported data saved directly to IndexedDB');

              // Update cache
              salesRevenueCache = combinedItems;

              // Also save to localStorage as backup
              localStorage.setItem(STORAGE_KEY, JSON.stringify(combinedItems));
            } catch (saveError) {
              console.error('Error saving imported data to IndexedDB:', saveError);
              // Fallback to the regular save method
              await saveSalesRevenueData(combinedItems);
            }

            resolve({
              success: true,
              importedCount: newItems.length,
              errors
            });
          }
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => {
        reject(new Error('Error reading file'));
      };

      if (file.name.endsWith('.csv')) {
        reader.readAsText(file);
      } else {
        reader.readAsArrayBuffer(file);
      }
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Export sales revenue data to Excel
 */
export const exportSalesRevenueData = async (data: SalesRevenueItem[], format: 'xlsx' | 'csv' = 'xlsx'): Promise<void> => {
  try {
    const worksheet = XLSX.utils.json_to_sheet(data);

    if (format === 'csv') {
      const csv = XLSX.utils.sheet_to_csv(worksheet);
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      saveAs(blob, 'sales_revenue_2025_data.csv');
    } else {
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'SalesRevenue2025');
      XLSX.writeFile(workbook, 'sales_revenue_2025_data.xlsx');
    }
  } catch (error) {
    console.error('Error exporting sales revenue data:', error);
    throw error;
  }
};

/**
 * Generate summary data from sales revenue items
 */
export const generateSalesRevenueSummary = (data: SalesRevenueItem[]): SalesRevenueSummary => {
  // Calculate total revenue
  const totalRevenue = data.reduce((sum, item) => sum + item.revenueInDocCurr, 0);

  // Count unique customers
  const uniqueCustomers = new Set(data.map(item => item.customerName));

  // Calculate total items
  const totalItems = data.reduce((sum, item) => sum + item.qty, 0);

  // Get top customers by revenue
  const customerRevenue: { [key: string]: number } = {};
  data.forEach(item => {
    if (!customerRevenue[item.customerName]) {
      customerRevenue[item.customerName] = 0;
    }
    customerRevenue[item.customerName] += item.revenueInDocCurr;
  });

  const topCustomers = Object.entries(customerRevenue)
    .map(([customer, revenue]) => ({ customer, revenue }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  // Get top salesmen by revenue
  const salesmanRevenue: { [key: string]: number } = {};
  data.forEach(item => {
    if (!salesmanRevenue[item.salesman]) {
      salesmanRevenue[item.salesman] = 0;
    }
    salesmanRevenue[item.salesman] += item.revenueInDocCurr;
  });

  const topSalesmen = Object.entries(salesmanRevenue)
    .map(([salesman, revenue]) => ({ salesman, revenue }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  // Get monthly revenue
  const monthlyRevenueMap: { [key: string]: number } = {};
  data.forEach(item => {
    let month = 'Unknown';
    if (item.billingDate) {
      try {
        const date = new Date(item.billingDate);
        if (!isNaN(date.getTime())) {
          month = date.toLocaleDateString('id-ID', { year: 'numeric', month: 'long' });
        }
      } catch (e) {
        // Use the default 'Unknown' if date parsing fails
      }
    }

    if (!monthlyRevenueMap[month]) {
      monthlyRevenueMap[month] = 0;
    }
    monthlyRevenueMap[month] += item.revenueInDocCurr;
  });

  const monthlyRevenue = Object.entries(monthlyRevenueMap)
    .map(([month, revenue]) => ({ month, revenue }))
    .sort((a, b) => {
      // Try to sort by date if possible
      const aDate = new Date(a.month);
      const bDate = new Date(b.month);
      if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
        return aDate.getTime() - bDate.getTime();
      }
      return a.month.localeCompare(b.month);
    });

  return {
    totalRevenue,
    totalCustomers: uniqueCustomers.size,
    totalItems,
    topCustomers,
    topSalesmen,
    monthlyRevenue
  };
};
