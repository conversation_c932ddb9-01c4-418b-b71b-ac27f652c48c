import { Navigate, useLocation } from 'react-router-dom';
import { authService } from '../services/authService';

interface RequireAuthProps {
  children: JSX.Element;
}

// A wrapper component to protect routes that require authentication
export default function RequireAuth({ children }: RequireAuthProps) {
  // TEMPORARY: Authentication check is disabled
  // Always return children without checking authentication
  return children;

  // ORIGINAL CODE (commented out):
  /*
  const location = useLocation();
  const isAuthenticated = authService.isAuthenticated();

  if (!isAuthenticated) {
    // Redirect to the login page with the current location for redirect after login
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return children;
  */
}
