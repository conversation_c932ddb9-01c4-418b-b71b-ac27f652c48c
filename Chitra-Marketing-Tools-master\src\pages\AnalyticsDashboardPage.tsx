import React, { useState, useEffect, useMemo } from 'react';
import { 
  BarChart3, 
  RefreshCw, 
  Filter, 
  Download, 
  Calendar, 
  TrendingUp, 
  Users, 
  Package, 
  Truck, 
  DollarSign,
  Sparkles,
  Target,
  MapPin,
  Clock,
  AlertCircle,
  CheckCircle,
  CloudRain
} from 'lucide-react';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { useDataHub } from '../components/DataHubProvider';
import { 
  useProducts, 
  useCustomers, 
  useFleetData, 
  useCoalPrices, 
  useHolidays 
} from '../hooks/useDataHubHooks';
import { fetchSalesData } from '../services/fleetRevenueService';
import { getSeasonalInsights } from '../services/seasonalMarketingService';
import { formatCurrency } from '../utils/pricing';
import StatCard from '../components/dashboard/StatCard';
import ChartCard from '../components/dashboard/ChartCard';
import DashboardCard from '../components/dashboard/DashboardCard';
import DataHubStatus from '../components/DataHubStatus';
import { SalesItem } from '../services/fleetRevenueService';
import { SeasonalInsight } from '../types/seasonalMarketing';
import { FleetData } from '../services/fleetDataService';
import { Holiday } from '../services/holidayService';
import { CoalPrice, CoalPriceTrend } from '../services/coalPriceService';

export default function AnalyticsDashboardPage() {
  // Data Hub hooks
  const { refreshData } = useDataHub();
  const { products, loading: productsLoading } = useProducts();
  const { customers, loading: customersLoading } = useCustomers();
  const { fleetData, loading: fleetDataLoading } = useFleetData();
  const { coalPrices, coalTrend, loading: coalPricesLoading } = useCoalPrices();
  const { holidays, loading: holidaysLoading } = useHolidays();

  // State for other data
  const [salesData, setSalesData] = useState<SalesItem[]>([]);
  const [insights, setInsights] = useState<SeasonalInsight[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [timeRange, setTimeRange] = useState('6months');

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch sales data and insights in parallel
        const [salesDataResult, insightsData] = await Promise.all([
          fetchSalesData(),
          getSeasonalInsights()
        ]);

        setSalesData(salesDataResult);
        setInsights(insightsData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle refresh
  const handleRefresh = async () => {
    setLoading(true);
    await refreshData();
    
    try {
      // Refresh sales data and insights
      const [salesDataResult, insightsData] = await Promise.all([
        fetchSalesData(),
        getSeasonalInsights()
      ]);

      setSalesData(salesDataResult);
      setInsights(insightsData);
    } catch (error) {
      console.error('Error refreshing dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Calculate summary metrics
  const summaryMetrics = useMemo(() => {
    // Customer metrics
    const totalCustomers = customers.length;
    const activeCustomers = customers.filter(c => c.updatedAt > new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)).length;
    
    // Product metrics
    const totalProducts = products.length;
    const averagePrice = products.length > 0
      ? products.reduce((sum, product) => sum + (product.price || 0), 0) / products.length
      : 0;
    
    // Fleet metrics
    const totalFleetRecords = fleetData.length;
    const totalTires = fleetData.reduce((sum, item) => sum + (parseInt(item.totaltire || '0', 10) || 0), 0);
    const totalUnits = fleetData.reduce((sum, item) => sum + (parseInt(item.unit_qty || '0', 10) || 0), 0);
    
    // Sales metrics
    const totalSales = salesData.reduce((sum, item) => sum + (item.Revenue || 0), 0);
    
    // Coal price metrics
    const currentCoalPrice = coalPrices.length > 0 ? coalPrices[0].price : 0;
    
    // Holiday metrics
    const upcomingHolidays = holidays
      .filter(h => new Date(h.date) > new Date())
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(0, 5);
    
    return {
      totalCustomers,
      activeCustomers,
      totalProducts,
      averagePrice,
      totalFleetRecords,
      totalTires,
      totalUnits,
      totalSales,
      currentCoalPrice,
      upcomingHolidays
    };
  }, [customers, products, fleetData, salesData, coalPrices, holidays]);

  // Prepare chart data for sales
  const salesChartData = useMemo(() => {
    // Group sales by month
    const salesByMonth: Record<string, number> = {};

    salesData.forEach(item => {
      if (item.Date) {
        const date = new Date(item.Date);
        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;

        if (!salesByMonth[monthYear]) {
          salesByMonth[monthYear] = 0;
        }

        salesByMonth[monthYear] += item.Revenue || 0;
      }
    });

    // Sort by date
    const sortedMonths = Object.keys(salesByMonth).sort((a, b) => {
      const [monthA, yearA] = a.split('/').map(Number);
      const [monthB, yearB] = b.split('/').map(Number);

      if (yearA !== yearB) return yearA - yearB;
      return monthA - monthB;
    });

    // Get last 6 months
    const last6Months = sortedMonths.slice(-6);

    // Format month names
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const labels = last6Months.map(monthYear => {
      const [month, year] = monthYear.split('/').map(Number);
      return `${monthNames[month - 1]} ${year}`;
    });

    // Get data for the last 6 months
    const data = last6Months.map(monthYear => salesByMonth[monthYear]);

    return { labels, data };
  }, [salesData]);

  // Prepare chart data for products
  const productChartData = useMemo(() => {
    // Group products by category
    const productsByCategory: Record<string, number> = {};

    products.forEach(product => {
      const category = product.materialDescription?.split(' ')[0] || 'Other';
      
      if (!productsByCategory[category]) {
        productsByCategory[category] = 0;
      }

      productsByCategory[category]++;
    });

    // Sort by count
    const sortedCategories = Object.entries(productsByCategory)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 6);

    const labels = sortedCategories.map(([category]) => category);
    const data = sortedCategories.map(([, count]) => count);

    return { labels, data };
  }, [products]);

  // Prepare chart data for fleet
  const fleetChartData = useMemo(() => {
    // Group fleet data by customer
    const fleetByCustomer: Record<string, number> = {};

    fleetData.forEach(item => {
      if (item.customer) {
        if (!fleetByCustomer[item.customer]) {
          fleetByCustomer[item.customer] = 0;
        }

        fleetByCustomer[item.customer] += parseInt(item.totaltire || '0', 10) || 0;
      }
    });

    // Sort by tire count
    const sortedCustomers = Object.entries(fleetByCustomer)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);

    const labels = sortedCustomers.map(([customer]) => customer);
    const data = sortedCustomers.map(([, count]) => count);

    return { labels, data };
  }, [fleetData]);

  // Prepare chart data for coal prices
  const coalPriceChartData = useMemo(() => {
    // Sort coal prices by date
    const sortedPrices = [...coalPrices]
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(-12);

    const labels = sortedPrices.map(price => {
      const date = new Date(price.date);
      return `${date.getMonth() + 1}/${date.getFullYear()}`;
    });

    const data = sortedPrices.map(price => price.price);

    return { labels, data };
  }, [coalPrices]);

  // Loading state
  if (loading || productsLoading || customersLoading || fleetDataLoading || coalPricesLoading || holidaysLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat data dashboard analitik...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <BarChart3 className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Dashboard Analitik Terpadu</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Pilih Rentang Waktu" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1month">1 Bulan Terakhir</SelectItem>
              <SelectItem value="3months">3 Bulan Terakhir</SelectItem>
              <SelectItem value="6months">6 Bulan Terakhir</SelectItem>
              <SelectItem value="1year">1 Tahun Terakhir</SelectItem>
            </SelectContent>
          </Select>
          <Button
            size="sm"
            variant="outline"
            onClick={handleRefresh}
            className="flex items-center"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh Data
          </Button>
        </div>
      </div>

      {/* Data Hub Status */}
      <div className="mb-6">
        <DataHubStatus />
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-5 w-full">
          <TabsTrigger value="overview">Ikhtisar</TabsTrigger>
          <TabsTrigger value="customers">Pelanggan</TabsTrigger>
          <TabsTrigger value="products">Produk</TabsTrigger>
          <TabsTrigger value="fleet">Armada</TabsTrigger>
          <TabsTrigger value="market">Pasar</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {/* Stats Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <StatCard
              title="Total Pelanggan"
              value={summaryMetrics.totalCustomers}
              icon={Users}
              description={`${summaryMetrics.activeCustomers} pelanggan aktif`}
            />
            <StatCard
              title="Total Produk"
              value={totalProducts}
              icon={Package}
              description={`Harga rata-rata: ${formatCurrency(summaryMetrics.averagePrice)}`}
            />
            <StatCard
              title="Total Unit"
              value={summaryMetrics.totalUnits}
              icon={Truck}
              description={`${summaryMetrics.totalTires} ban`}
            />
            <StatCard
              title="Total Penjualan"
              value={formatCurrency(summaryMetrics.totalSales)}
              icon={DollarSign}
              description="Total pendapatan"
              trend={summaryMetrics.totalSales > 1000000 ? 'up' : 'neutral'}
              trendValue="Dibanding bulan lalu"
            />
          </div>

          {/* Charts Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartCard
              title="Penjualan 6 Bulan Terakhir"
              icon={BarChart3}
              chartType="line"
              labels={salesChartData.labels}
              datasets={[
                {
                  label: 'Penjualan',
                  data: salesChartData.data
                }
              ]}
            />
            <ChartCard
              title="Distribusi Produk"
              icon={Package}
              chartType="pie"
              labels={productChartData.labels}
              datasets={[
                {
                  label: 'Jumlah Produk',
                  data: productChartData.data
                }
              ]}
            />
          </div>

          {/* More Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ChartCard
              title="Top 5 Pelanggan (Jumlah Ban)"
              icon={Users}
              chartType="bar"
              labels={fleetChartData.labels}
              datasets={[
                {
                  label: 'Jumlah Ban',
                  data: fleetChartData.data
                }
              ]}
            />
            <ChartCard
              title="Tren Harga Batu Bara"
              icon={TrendingUp}
              chartType="line"
              labels={coalPriceChartData.labels}
              datasets={[
                {
                  label: 'Harga (USD)',
                  data: coalPriceChartData.data
                }
              ]}
            />
          </div>
        </TabsContent>

        {/* Placeholder for other tabs - will be implemented in future updates */}
        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analisis Pelanggan</CardTitle>
              <CardDescription>
                Analisis mendalam tentang data pelanggan akan ditampilkan di sini.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Fitur ini akan segera hadir.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analisis Produk</CardTitle>
              <CardDescription>
                Analisis mendalam tentang data produk akan ditampilkan di sini.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Fitur ini akan segera hadir.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fleet" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analisis Armada</CardTitle>
              <CardDescription>
                Analisis mendalam tentang data armada akan ditampilkan di sini.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Fitur ini akan segera hadir.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="market" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analisis Pasar</CardTitle>
              <CardDescription>
                Analisis mendalam tentang data pasar akan ditampilkan di sini.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Fitur ini akan segera hadir.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
