import React, { useState, useEffect } from 'react';
import {
  SocialMediaKnowledgeEntry,
  SocialMediaKnowledgeEntryCreateRequest,
  SocialMediaKnowledgeCategory,
  SocialMediaKnowledgeSearchRequest,
  SocialMediaPlatform
} from '../types/socialMedia';
import {
  getAllSocialMediaKnowledge,
  createSocialMediaKnowledge,
  updateSocialMediaKnowledge,
  deleteSocialMediaKnowledge,
  searchSocialMediaKnowledge,
  importProductsToKnowledge
} from '../services/socialMediaService';
import { fetchProducts } from '../services/productService';
import { Product } from '../types';
import {
  PlusCircle,
  Search,
  Edit,
  Trash2,
  Save,
  X,
  Tag,
  BookOpen,
  Filter,
  CheckCircle,
  AlertCircle,
  Instagram,
  Facebook,
  Linkedin,
  Twitter,
  Database,
  Package,
  RefreshCw,
  Check
} from 'lucide-react';

interface SocialMediaKnowledgeManagerProps {
  onSelectEntries?: (entries: SocialMediaKnowledgeEntry[]) => void;
  selectable?: boolean;
  selectedEntries?: SocialMediaKnowledgeEntry[];
}

export default function SocialMediaKnowledgeManager({
  onSelectEntries,
  selectable = false,
  selectedEntries = []
}: SocialMediaKnowledgeManagerProps) {
  // State for knowledge entries
  const [entries, setEntries] = useState<SocialMediaKnowledgeEntry[]>([]);
  const [filteredEntries, setFilteredEntries] = useState<SocialMediaKnowledgeEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // State for editing/creating
  const [isEditing, setIsEditing] = useState(false);
  const [currentEntry, setCurrentEntry] = useState<SocialMediaKnowledgeEntryCreateRequest>({
    title: '',
    content: '',
    category: SocialMediaKnowledgeCategory.CONTENT_IDEAS,
    tags: [],
    platform: SocialMediaPlatform.INSTAGRAM
  });
  const [editingId, setEditingId] = useState<string | null>(null);

  // State for filtering
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<SocialMediaKnowledgeCategory | ''>('');
  const [selectedPlatform, setSelectedPlatform] = useState<SocialMediaPlatform | ''>('');
  const [selectedTag, setSelectedTag] = useState<string>('');

  // State for notifications
  const [notification, setNotification] = useState<{
    message: string;
    type: 'success' | 'error';
    visible: boolean;
  }>({
    message: '',
    type: 'success',
    visible: false
  });

  // State for selection (when used in selectable mode)
  const [selected, setSelected] = useState<string[]>(
    selectedEntries.map(entry => entry.id)
  );

  // State for product import modal
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [isProductLoading, setIsProductLoading] = useState(false);
  const [productSearchQuery, setProductSearchQuery] = useState('');

  // Load entries on component mount
  useEffect(() => {
    loadEntries();
  }, []);

  // Update selected entries when selectedEntries prop changes
  useEffect(() => {
    if (selectable) {
      setSelected(selectedEntries.map(entry => entry.id));
    }
  }, [selectedEntries, selectable]);

  // Update filtered entries when search/filter changes
  useEffect(() => {
    filterEntries();
  }, [entries, searchQuery, selectedCategory, selectedPlatform, selectedTag]);

  // Load all knowledge entries
  const loadEntries = async () => {
    setIsLoading(true);
    try {
      const loadedEntries = await getAllSocialMediaKnowledge();
      setEntries(loadedEntries);
    } catch (error) {
      console.error('Error loading social media knowledge entries:', error);
      showNotification('Gagal memuat data pengetahuan', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter entries based on search query and filters
  const filterEntries = () => {
    let filtered = [...entries];

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(entry =>
        entry.title.toLowerCase().includes(query) ||
        entry.content.toLowerCase().includes(query) ||
        entry.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(entry => entry.category === selectedCategory);
    }

    // Filter by platform
    if (selectedPlatform) {
      filtered = filtered.filter(entry => entry.platform === selectedPlatform);
    }

    // Filter by tag
    if (selectedTag) {
      filtered = filtered.filter(entry =>
        entry.tags.some(tag => tag.toLowerCase() === selectedTag.toLowerCase())
      );
    }

    setFilteredEntries(filtered);
  };

  // Show notification
  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({
      message,
      type,
      visible: true
    });

    // Hide notification after 3 seconds
    setTimeout(() => {
      setNotification(prev => ({ ...prev, visible: false }));
    }, 3000);
  };

  // Reset form
  const resetForm = () => {
    setCurrentEntry({
      title: '',
      content: '',
      category: SocialMediaKnowledgeCategory.CONTENT_IDEAS,
      tags: [],
      platform: SocialMediaPlatform.INSTAGRAM
    });
    setEditingId(null);
    setIsEditing(false);
  };

  // Edit entry
  const editEntry = (entry: SocialMediaKnowledgeEntry) => {
    setCurrentEntry({
      title: entry.title,
      content: entry.content,
      category: entry.category,
      tags: entry.tags,
      platform: entry.platform
    });
    setEditingId(entry.id);
    setIsEditing(true);
  };

  // Save entry (create or update)
  const saveEntry = async () => {
    try {
      if (!currentEntry.title || !currentEntry.content) {
        showNotification('Judul dan konten harus diisi', 'error');
        return;
      }

      if (editingId) {
        // Update existing entry
        const updated = await updateSocialMediaKnowledge({
          id: editingId,
          ...currentEntry
        });

        if (updated) {
          setEntries(prev => prev.map(entry =>
            entry.id === editingId ? updated : entry
          ));
          showNotification('Pengetahuan berhasil diperbarui', 'success');
        }
      } else {
        // Create new entry
        const newEntry = await createSocialMediaKnowledge(currentEntry);
        setEntries(prev => [...prev, newEntry]);
        showNotification('Pengetahuan baru berhasil ditambahkan', 'success');
      }

      resetForm();
    } catch (error) {
      console.error('Error saving social media knowledge entry:', error);
      showNotification('Gagal menyimpan pengetahuan', 'error');
    }
  };

  // Delete an entry
  const deleteEntry = async (id: string) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus pengetahuan ini?')) {
      try {
        const success = await deleteSocialMediaKnowledge(id);
        if (success) {
          setEntries(prev => prev.filter(entry => entry.id !== id));
          showNotification('Pengetahuan berhasil dihapus', 'success');
        }
      } catch (error) {
        console.error('Error deleting social media knowledge entry:', error);
        showNotification('Gagal menghapus pengetahuan', 'error');
      }
    }
  };

  // Toggle selection of an entry (for selectable mode)
  const toggleSelection = (id: string) => {
    if (!selectable) return;

    setSelected(prev => {
      const isSelected = prev.includes(id);
      const newSelected = isSelected
        ? prev.filter(entryId => entryId !== id)
        : [...prev, id];

      // Call the onSelectEntries callback with the selected entries
      if (onSelectEntries) {
        const selectedEntries = entries.filter(entry =>
          newSelected.includes(entry.id)
        );
        onSelectEntries(selectedEntries);
      }

      return newSelected;
    });
  };

  // Get all unique tags from entries
  const getAllTags = (): string[] => {
    const tags = new Set<string>();
    entries.forEach(entry => {
      entry.tags.forEach(tag => tags.add(tag));
    });
    return Array.from(tags).sort();
  };

  // Get platform icon
  const getPlatformIcon = (platform: SocialMediaPlatform) => {
    switch (platform) {
      case SocialMediaPlatform.INSTAGRAM:
        return <Instagram size={16} className="text-pink-500" />;
      case SocialMediaPlatform.FACEBOOK:
        return <Facebook size={16} className="text-blue-600" />;
      case SocialMediaPlatform.LINKEDIN:
        return <Linkedin size={16} className="text-blue-700" />;
      case SocialMediaPlatform.TWITTER:
        return <Twitter size={16} className="text-blue-400" />;
      default:
        return <Instagram size={16} className="text-pink-500" />;
    }
  };

  // Format category name for display
  const formatCategoryName = (category: SocialMediaKnowledgeCategory): string => {
    switch (category) {
      case SocialMediaKnowledgeCategory.CONTENT_IDEAS:
        return 'Ide Konten';
      case SocialMediaKnowledgeCategory.HASHTAG_STRATEGIES:
        return 'Strategi Hashtag';
      case SocialMediaKnowledgeCategory.AUDIENCE_INSIGHTS:
        return 'Insight Audiens';
      case SocialMediaKnowledgeCategory.BEST_PRACTICES:
        return 'Praktik Terbaik';
      case SocialMediaKnowledgeCategory.COMPETITOR_ANALYSIS:
        return 'Analisis Kompetitor';
      case SocialMediaKnowledgeCategory.CASE_STUDIES:
        return 'Studi Kasus';
      case SocialMediaKnowledgeCategory.TREND_ANALYSIS:
        return 'Analisis Tren';
      default:
        return category;
    }
  };

  // Load products for import
  const loadProducts = async () => {
    setIsProductLoading(true);
    try {
      const loadedProducts = await fetchProducts();
      setProducts(loadedProducts);
      console.log(`Loaded ${loadedProducts.length} products for import`);
    } catch (error) {
      console.error('Error loading products:', error);
      showNotification('Gagal memuat data produk', 'error');
    } finally {
      setIsProductLoading(false);
    }
  };

  // Open product import modal
  const openProductModal = async () => {
    setSelectedProducts([]);
    setProductSearchQuery('');
    setIsProductModalOpen(true);
    await loadProducts();
  };

  // Close product import modal
  const closeProductModal = () => {
    setIsProductModalOpen(false);
    setSelectedProducts([]);
  };

  // Toggle product selection
  const toggleProductSelection = (productId: string) => {
    setSelectedProducts(prev => {
      const isSelected = prev.includes(productId);
      return isSelected
        ? prev.filter(id => id !== productId)
        : [...prev, productId];
    });
  };

  // Select all products
  const selectAllProducts = () => {
    const filteredProductIds = getFilteredProducts().map(product => product.id);
    setSelectedProducts(filteredProductIds);
  };

  // Deselect all products
  const deselectAllProducts = () => {
    setSelectedProducts([]);
  };

  // Filter products based on search query
  const getFilteredProducts = (): Product[] => {
    if (!productSearchQuery) {
      return products;
    }

    const query = productSearchQuery.toLowerCase();
    return products.filter(product =>
      product.oldMaterialNo.toLowerCase().includes(query) ||
      product.materialDescription.toLowerCase().includes(query) ||
      (product.description && product.description.toLowerCase().includes(query))
    );
  };

  // Import selected products to knowledge base
  const importSelectedProducts = async () => {
    if (selectedProducts.length === 0) {
      showNotification('Pilih minimal satu produk untuk diimpor', 'error');
      return;
    }

    try {
      setIsProductLoading(true);
      const importedCount = await importProductsToKnowledge(selectedProducts);

      if (importedCount > 0) {
        showNotification(`Berhasil mengimpor ${importedCount} produk ke knowledge base`, 'success');
        closeProductModal();
        loadEntries(); // Reload entries to show the newly imported products
      } else {
        showNotification('Tidak ada produk yang berhasil diimpor', 'error');
      }
    } catch (error) {
      console.error('Error importing products:', error);
      showNotification('Gagal mengimpor produk ke knowledge base', 'error');
    } finally {
      setIsProductLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Notification */}
      {notification.visible && (
        <div className={`p-3 ${notification.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} flex items-center`}>
          {notification.type === 'success' ? (
            <CheckCircle size={16} className="mr-2" />
          ) : (
            <AlertCircle size={16} className="mr-2" />
          )}
          {notification.message}
        </div>
      )}

      {/* Header */}
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="text-lg font-medium">Knowledge Base Media Sosial</h3>
        {!isEditing && (
          <div className="flex space-x-2">
            <button
              onClick={openProductModal}
              className="flex items-center text-sm bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600 transition-colors"
            >
              <Database size={16} className="mr-1" />
              Import dari Produk
            </button>
            <button
              onClick={() => setIsEditing(true)}
              className="flex items-center text-sm bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition-colors"
            >
              <PlusCircle size={16} className="mr-1" />
              Tambah Pengetahuan
            </button>
          </div>
        )}
      </div>

      {/* Edit Form */}
      {isEditing && (
        <div className="p-4 border-b border-gray-200 bg-blue-50">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{editingId ? 'Edit Pengetahuan' : 'Tambah Pengetahuan Baru'}</h4>
            <button
              onClick={resetForm}
              className="text-gray-500 hover:text-gray-700"
            >
              <X size={18} />
            </button>
          </div>

          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Judul</label>
              <input
                type="text"
                value={currentEntry.title}
                onChange={(e) => setCurrentEntry(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Judul pengetahuan"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Kategori</label>
                <select
                  value={currentEntry.category}
                  onChange={(e) => setCurrentEntry(prev => ({ ...prev, category: e.target.value as SocialMediaKnowledgeCategory }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  {Object.values(SocialMediaKnowledgeCategory).map(category => (
                    <option key={category} value={category}>
                      {formatCategoryName(category)}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Platform</label>
                <select
                  value={currentEntry.platform}
                  onChange={(e) => setCurrentEntry(prev => ({ ...prev, platform: e.target.value as SocialMediaPlatform }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  {Object.values(SocialMediaPlatform).map(platform => (
                    <option key={platform} value={platform}>
                      {platform.charAt(0).toUpperCase() + platform.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Konten</label>
              <textarea
                value={currentEntry.content}
                onChange={(e) => setCurrentEntry(prev => ({ ...prev, content: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 min-h-[120px]"
                placeholder="Isi pengetahuan"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tags (pisahkan dengan koma)</label>
              <input
                type="text"
                value={currentEntry.tags.join(', ')}
                onChange={(e) => {
                  const tagsString = e.target.value;
                  const tagsArray = tagsString.split(',').map(tag => tag.trim()).filter(tag => tag);
                  setCurrentEntry(prev => ({ ...prev, tags: tagsArray }));
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="hashtag, instagram, engagement, ..."
              />
            </div>

            <div className="flex justify-end">
              <button
                onClick={resetForm}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 mr-2 hover:bg-gray-50"
              >
                Batal
              </button>
              <button
                onClick={saveEntry}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center"
              >
                <Save size={16} className="mr-1" />
                Simpan
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex flex-col md:flex-row gap-3">
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Cari pengetahuan..."
              />
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value as SocialMediaKnowledgeCategory | '')}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
            >
              <option value="">Semua Kategori</option>
              {Object.values(SocialMediaKnowledgeCategory).map(category => (
                <option key={category} value={category}>
                  {formatCategoryName(category)}
                </option>
              ))}
            </select>

            <select
              value={selectedPlatform}
              onChange={(e) => setSelectedPlatform(e.target.value as SocialMediaPlatform | '')}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
            >
              <option value="">Semua Platform</option>
              {Object.values(SocialMediaPlatform).map(platform => (
                <option key={platform} value={platform}>
                  {platform.charAt(0).toUpperCase() + platform.slice(1)}
                </option>
              ))}
            </select>

            <select
              value={selectedTag}
              onChange={(e) => setSelectedTag(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 text-sm"
            >
              <option value="">Semua Tags</option>
              {getAllTags().map(tag => (
                <option key={tag} value={tag}>{tag}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Knowledge List */}
      <div className="divide-y divide-gray-200 max-h-[600px] overflow-y-auto">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2"></div>
            <p className="text-gray-500">Memuat data...</p>
          </div>
        ) : filteredEntries.length === 0 ? (
          <div className="p-8 text-center">
            <BookOpen size={32} className="mx-auto text-gray-400 mb-2" />
            <p className="text-gray-500">Tidak ada data pengetahuan yang ditemukan</p>
          </div>
        ) : (
          filteredEntries.map(entry => (
            <div
              key={entry.id}
              className={`p-4 hover:bg-gray-50 ${selectable && selected.includes(entry.id) ? 'bg-blue-50' : ''}`}
              onClick={() => selectable && toggleSelection(entry.id)}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center mb-1">
                    {getPlatformIcon(entry.platform)}
                    <h4 className="font-medium ml-2">
                      {entry.title}
                      {entry.id.startsWith('product-') && (
                        <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                          <Package size={12} className="mr-1" />
                          Produk
                        </span>
                      )}
                    </h4>
                  </div>
                  <div className="text-sm text-gray-600 mb-2 whitespace-pre-line">
                    {entry.content.length > 200
                      ? `${entry.content.substring(0, 200)}...`
                      : entry.content}
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                      {formatCategoryName(entry.category)}
                    </span>
                    {entry.tags.slice(0, 3).map(tag => (
                      <span key={tag} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        #{tag}
                      </span>
                    ))}
                    {entry.tags.length > 3 && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        +{entry.tags.length - 3}
                      </span>
                    )}
                  </div>
                </div>

                {!selectable && (
                  <div className="flex space-x-1 ml-4">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        editEntry(entry);
                      }}
                      className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                      title="Edit"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteEntry(entry.id);
                      }}
                      className="p-1 text-red-600 hover:bg-red-100 rounded"
                      title="Hapus"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                )}

                {selectable && (
                  <div className="ml-4">
                    <input
                      type="checkbox"
                      checked={selected.includes(entry.id)}
                      onChange={() => toggleSelection(entry.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Product Import Modal */}
      {isProductModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-medium">Import Produk ke Knowledge Base</h3>
              <button
                onClick={closeProductModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex flex-col md:flex-row gap-3">
                <div className="flex-1">
                  <div className="relative">
                    <input
                      type="text"
                      value={productSearchQuery}
                      onChange={(e) => setProductSearchQuery(e.target.value)}
                      className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                      placeholder="Cari produk..."
                    />
                    <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={selectAllProducts}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50"
                  >
                    Pilih Semua
                  </button>
                  <button
                    onClick={deselectAllProducts}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50"
                  >
                    Batalkan Semua
                  </button>
                </div>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto">
              {isProductLoading ? (
                <div className="p-8 text-center">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2"></div>
                  <p className="text-gray-500">Memuat data produk...</p>
                </div>
              ) : getFilteredProducts().length === 0 ? (
                <div className="p-8 text-center">
                  <Package size={32} className="mx-auto text-gray-400 mb-2" />
                  <p className="text-gray-500">Tidak ada produk yang ditemukan</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {getFilteredProducts().map(product => (
                    <div
                      key={product.id}
                      className={`p-4 hover:bg-gray-50 cursor-pointer ${selectedProducts.includes(product.id) ? 'bg-blue-50' : ''}`}
                      onClick={() => toggleProductSelection(product.id)}
                    >
                      <div className="flex items-start">
                        <div className="flex-shrink-0 pt-1">
                          <input
                            type="checkbox"
                            checked={selectedProducts.includes(product.id)}
                            onChange={() => toggleProductSelection(product.id)}
                            onClick={(e) => e.stopPropagation()}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </div>
                        <div className="ml-3 flex-1">
                          <div className="flex justify-between">
                            <h4 className="font-medium">{product.materialDescription}</h4>
                            <span className="text-gray-500 text-sm">
                              Rp {product.price.toLocaleString('id-ID')}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600">
                            Kode: {product.oldMaterialNo}
                          </div>
                          {product.description && (
                            <div className="text-sm text-gray-500 mt-1">
                              {product.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="p-4 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {selectedProducts.length} produk dipilih dari {getFilteredProducts().length} produk
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={closeProductModal}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Batal
                </button>
                <button
                  onClick={importSelectedProducts}
                  disabled={selectedProducts.length === 0 || isProductLoading}
                  className={`px-4 py-2 rounded-md text-white flex items-center ${
                    selectedProducts.length === 0 || isProductLoading
                      ? 'bg-blue-300 cursor-not-allowed'
                      : 'bg-blue-500 hover:bg-blue-600'
                  }`}
                >
                  {isProductLoading ? (
                    <>
                      <RefreshCw size={16} className="mr-2 animate-spin" />
                      Mengimpor...
                    </>
                  ) : (
                    <>
                      <Database size={16} className="mr-2" />
                      Import ke Knowledge Base
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
