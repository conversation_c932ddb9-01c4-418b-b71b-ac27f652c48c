import axios from 'axios';

const API_KEY = '9d4d525a9e1f2f6dd24126b1';
const BASE_URL = 'https://v6.exchangerate-api.com/v6';

export interface ExchangeRateResponse {
  result: string;
  documentation: string;
  terms_of_use: string;
  time_last_update_unix: number;
  time_last_update_utc: string;
  time_next_update_unix: number;
  time_next_update_utc: string;
  base_code: string;
  conversion_rates: {
    [key: string]: number;
  };
}

export async function fetchExchangeRate(baseCurrency: string = 'IDR'): Promise<number> {
  try {
    const response = await axios.get<ExchangeRateResponse>(
      `${BASE_URL}/${API_KEY}/latest/${baseCurrency}`
    );

    // Get USD rate from conversion_rates
    const usdRate = response.data.conversion_rates.USD;
    
    // Since we're using IDR as base, we need to invert the rate
    // If 1 IDR = 0.000067 USD, then 1 USD = 1/0.000067 IDR
    // Round to nearest integer
    return Math.round(1 / usdRate);
  } catch (error) {
    console.error('Error fetching exchange rate:', error);
    throw new Error('Gagal mengambil nilai tukar mata uang');
  }
}

// Cache exchange rate for 24 hours
let cachedRate: number | null = null;
let lastFetchTime: number | null = null;
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export async function getExchangeRate(): Promise<number> {
  const now = Date.now();
  
  // Return cached rate if it's still valid (within 24 hours)
  if (cachedRate && lastFetchTime && (now - lastFetchTime < CACHE_DURATION)) {
    return cachedRate;
  }

  // Fetch new rate and round it
  const rate = await fetchExchangeRate();
  
  // Update cache
  cachedRate = rate;
  lastFetchTime = now;
  
  return rate;
} 