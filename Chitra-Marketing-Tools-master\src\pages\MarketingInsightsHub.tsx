import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "../components/ui/tabs";
import {
  Lightbulb,
  TrendingUp,
  BarChart2,
  Target,
  Users,
  Search,
  Filter,
  RefreshCw,
  PlusCircle,
  Save,
  Trash2,
  Edit,
  Copy,
  Download,
  ChevronDown,
  ChevronUp,
  MessageSquare,
  Sparkles,
  FileText,
  BookOpen,
  Bookmark,
  Tag,
  Clock,
  AlertTriangle,
  Eye,
  X,
  ExternalLink,
  Link
} from 'lucide-react';
import { useToast } from '../components/ui/use-toast';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertD<PERSON>ogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../components/ui/alert-dialog";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { Textarea } from "../components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../components/ui/select";
import { Badge } from "../components/ui/badge";
import { Skeleton } from "../components/ui/skeleton";
import { generateMarketingInsight, getMarketingInsights, saveMarketingInsight, deleteMarketingInsight } from '../services/marketingInsightsService';

// Types for marketing insights
export enum InsightCategory {
  STRATEGY = 'strategy',
  CASE_STUDY = 'case_study',
  TREND = 'trend',
  COMPETITOR = 'competitor',
  CUSTOMER = 'customer',
  PRODUCT = 'product',
  PRICING = 'pricing',
  PROMOTION = 'promotion'
}

export enum InsightType {
  TIP = 'tip',
  STORY = 'story',
  ANALYSIS = 'analysis',
  FORECAST = 'forecast'
}

export interface MarketingInsight {
  id: string;
  title: string;
  content: string;
  category: InsightCategory;
  type: InsightType;
  tags: string[];
  source?: string;
  createdAt: Date;
  updatedAt: Date;
  isFavorite: boolean;
}

export default function MarketingInsightsHub() {
  // State for insights
  const [insights, setInsights] = useState<MarketingInsight[]>([]);
  const [filteredInsights, setFilteredInsights] = useState<MarketingInsight[]>([]);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);

  // State for filters
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<InsightCategory | 'all'>('all');
  const [typeFilter, setTypeFilter] = useState<InsightType | 'all'>('all');
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  // State for insight creation/editing
  const [isCreatingInsight, setIsCreatingInsight] = useState(false);
  const [isEditingInsight, setIsEditingInsight] = useState(false);
  const [currentInsight, setCurrentInsight] = useState<Partial<MarketingInsight> | null>(null);

  // State for AI generation
  const [generationPrompt, setGenerationPrompt] = useState('');
  const [showGenerationDialog, setShowGenerationDialog] = useState(false);
  const [generationType, setGenerationType] = useState<InsightType>(InsightType.TIP);
  const [generationCategory, setGenerationCategory] = useState<InsightCategory>(InsightCategory.STRATEGY);

  // State for delete confirmation
  const [insightToDelete, setInsightToDelete] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // State for view modal
  const [selectedInsight, setSelectedInsight] = useState<MarketingInsight | null>(null);
  const [showViewModal, setShowViewModal] = useState(false);

  // Toast notifications
  const { toast } = useToast();

  // Load insights on component mount
  useEffect(() => {
    const fetchInsights = async () => {
      try {
        setLoading(true);
        const data = await getMarketingInsights();
        setInsights(data);
        setFilteredInsights(data);
      } catch (error) {
        console.error('Error loading insights:', error);
        toast({
          title: "Error",
          description: "Gagal memuat data insights marketing",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchInsights();
  }, []);

  // Apply filters when dependencies change
  useEffect(() => {
    let filtered = [...insights];

    // Apply search term filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(insight =>
        insight.title.toLowerCase().includes(term) ||
        insight.content.toLowerCase().includes(term) ||
        insight.tags.some(tag => tag.toLowerCase().includes(term))
      );
    }

    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(insight => insight.category === categoryFilter);
    }

    // Apply type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(insight => insight.type === typeFilter);
    }

    // Apply favorites filter
    if (showFavoritesOnly) {
      filtered = filtered.filter(insight => insight.isFavorite);
    }

    // Sort by date (newest first)
    filtered.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());

    setFilteredInsights(filtered);
  }, [insights, searchTerm, categoryFilter, typeFilter, showFavoritesOnly]);

  // Handle insight creation/update
  const handleSaveInsight = async () => {
    if (!currentInsight || !currentInsight.title || !currentInsight.content) {
      toast({
        title: "Error",
        description: "Judul dan konten harus diisi",
        variant: "destructive"
      });
      return;
    }

    try {
      const savedInsight = await saveMarketingInsight(currentInsight as MarketingInsight);

      if (isEditingInsight) {
        setInsights(insights.map(i => i.id === savedInsight.id ? savedInsight : i));
        toast({
          title: "Berhasil",
          description: "Insight berhasil diperbarui",
        });
      } else {
        setInsights([...insights, savedInsight]);
        toast({
          title: "Berhasil",
          description: "Insight baru berhasil dibuat",
        });
      }

      setIsCreatingInsight(false);
      setIsEditingInsight(false);
      setCurrentInsight(null);
    } catch (error) {
      console.error('Error saving insight:', error);
      toast({
        title: "Error",
        description: "Gagal menyimpan insight",
        variant: "destructive"
      });
    }
  };

  // Handle insight deletion
  const handleDeleteInsight = async () => {
    if (!insightToDelete) return;

    try {
      await deleteMarketingInsight(insightToDelete);
      setInsights(insights.filter(insight => insight.id !== insightToDelete));

      toast({
        title: "Berhasil",
        description: "Insight berhasil dihapus",
      });
    } catch (error) {
      console.error('Error deleting insight:', error);
      toast({
        title: "Error",
        description: "Gagal menghapus insight",
        variant: "destructive"
      });
    } finally {
      setShowDeleteDialog(false);
      setInsightToDelete(null);
    }
  };

  // Generate insight with AI
  const handleGenerateInsight = async () => {
    if (!generationPrompt) {
      toast({
        title: "Error",
        description: "Prompt tidak boleh kosong",
        variant: "destructive"
      });
      return;
    }

    try {
      setGenerating(true);

      const generatedInsight = await generateMarketingInsight({
        prompt: generationPrompt,
        type: generationType,
        category: generationCategory
      });

      setCurrentInsight({
        ...generatedInsight,
        createdAt: new Date(),
        updatedAt: new Date(),
        isFavorite: false
      });

      setShowGenerationDialog(false);
      setIsCreatingInsight(true);

      toast({
        title: "Berhasil",
        description: "Insight berhasil dibuat dengan AI",
      });
    } catch (error) {
      console.error('Error generating insight:', error);
      toast({
        title: "Error",
        description: "Gagal membuat insight dengan AI",
        variant: "destructive"
      });
    } finally {
      setGenerating(false);
    }
  };

  // Toggle favorite status
  const toggleFavorite = async (insight: MarketingInsight) => {
    try {
      const updatedInsight = await saveMarketingInsight({
        ...insight,
        isFavorite: !insight.isFavorite,
        updatedAt: new Date()
      });

      setInsights(insights.map(i => i.id === updatedInsight.id ? updatedInsight : i));
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast({
        title: "Error",
        description: "Gagal mengubah status favorit",
        variant: "destructive"
      });
    }
  };

  // Format date
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  // Get category icon
  const getCategoryIcon = (category: InsightCategory) => {
    switch (category) {
      case InsightCategory.STRATEGY:
        return <Target size={16} className="text-blue-500" />;
      case InsightCategory.CASE_STUDY:
        return <FileText size={16} className="text-purple-500" />;
      case InsightCategory.TREND:
        return <TrendingUp size={16} className="text-green-500" />;
      case InsightCategory.COMPETITOR:
        return <Users size={16} className="text-red-500" />;
      case InsightCategory.CUSTOMER:
        return <Users size={16} className="text-yellow-500" />;
      case InsightCategory.PRODUCT:
        return <Tag size={16} className="text-indigo-500" />;
      case InsightCategory.PRICING:
        return <BarChart2 size={16} className="text-orange-500" />;
      case InsightCategory.PROMOTION:
        return <Sparkles size={16} className="text-pink-500" />;
      default:
        return <Lightbulb size={16} className="text-gray-500" />;
    }
  };

  // Get type icon
  const getTypeIcon = (type: InsightType) => {
    switch (type) {
      case InsightType.TIP:
        return <Lightbulb size={16} className="text-amber-500" />;
      case InsightType.STORY:
        return <BookOpen size={16} className="text-blue-500" />;
      case InsightType.ANALYSIS:
        return <BarChart2 size={16} className="text-green-500" />;
      case InsightType.FORECAST:
        return <TrendingUp size={16} className="text-purple-500" />;
      default:
        return <MessageSquare size={16} className="text-gray-500" />;
    }
  };

  // Copy content to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Berhasil",
      description: "Teks berhasil disalin ke clipboard",
    });
  };

  // Extract and open the first URL from a markdown link
  const openSourceUrl = (source: string) => {
    // Try to find the first valid URL in the source
    const urlMatches = source.matchAll(/\[([^\]]+)\]\(([^)]+)\)/g);
    const matches = Array.from(urlMatches);

    if (matches && matches.length > 0) {
      // Get the first URL
      const firstUrl = matches[0][2];

      // Validate URL
      try {
        const url = new URL(firstUrl);
        if (url.protocol === 'https:' || url.protocol === 'http:') {
          // Open in new tab with full window features
          window.open(
            firstUrl,
            '_blank',
            'noopener,noreferrer,toolbar=yes,scrollbars=yes,resizable=yes,top=0,left=0,width=800,height=600'
          );
          return;
        }
      } catch (e) {
        // Invalid URL format, continue to error
      }
    }

    // If we get here, no valid URL was found
    toast({
      title: "Error",
      description: "Tidak dapat menemukan URL sumber yang valid",
      variant: "destructive"
    });
  };

  // Open a specific URL in a new tab
  const openUrlInNewTab = (url: string) => {
    try {
      // Validate URL
      new URL(url);
      // Open in new tab with full window features
      window.open(
        url,
        '_blank',
        'noopener,noreferrer,toolbar=yes,scrollbars=yes,resizable=yes,top=0,left=0,width=800,height=600'
      );
    } catch (e) {
      toast({
        title: "Error",
        description: "URL tidak valid: " + url,
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Sparkles size={20} className="text-blue-500 mr-2" />
          <h1 className="text-2xl font-bold text-gray-900">Marketing Insights Hub</h1>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => {
              setCurrentInsight({
                title: '',
                content: '',
                category: InsightCategory.STRATEGY,
                type: InsightType.TIP,
                tags: [],
                createdAt: new Date(),
                updatedAt: new Date(),
                isFavorite: false
              });
              setIsCreatingInsight(true);
            }}
          >
            <PlusCircle size={16} className="mr-2" />
            Tambah Manual
          </Button>
          <Button
            onClick={() => {
              setGenerationPrompt('');
              setGenerationType(InsightType.TIP);
              setGenerationCategory(InsightCategory.STRATEGY);
              setShowGenerationDialog(true);
            }}
          >
            <Sparkles size={16} className="mr-2" />
            Buat dengan AI
          </Button>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="all">
            <BookOpen className="mr-2 h-4 w-4" />
            Semua Insights
          </TabsTrigger>
          <TabsTrigger value="tips">
            <Lightbulb className="mr-2 h-4 w-4" />
            Tips Marketing
          </TabsTrigger>
          <TabsTrigger value="stories">
            <MessageSquare className="mr-2 h-4 w-4" />
            Cerita Inspiratif
          </TabsTrigger>
          <TabsTrigger value="analysis">
            <BarChart2 className="mr-2 h-4 w-4" />
            Analisis & Tren
          </TabsTrigger>
        </TabsList>

        {/* Search and Filters */}
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="flex-1">
              <div className="relative">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Cari insight berdasarkan judul, konten, atau tag..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 w-full"
                />
              </div>
            </div>
          </div>

          <div className="flex flex-wrap gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Kategori</label>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value as InsightCategory | 'all')}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">Semua Kategori</option>
                {Object.values(InsightCategory).map((category) => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tipe</label>
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value as InsightType | 'all')}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">Semua Tipe</option>
                {Object.values(InsightType).map((type) => (
                  <option key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-end">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={showFavoritesOnly}
                  onChange={() => setShowFavoritesOnly(!showFavoritesOnly)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">Hanya Favorit</span>
              </label>
            </div>
          </div>
        </div>

        <TabsContent value="all" className="mt-0">
          {renderInsightsList(filteredInsights)}
        </TabsContent>

        <TabsContent value="tips" className="mt-0">
          {renderInsightsList(filteredInsights.filter(insight => insight.type === InsightType.TIP))}
        </TabsContent>

        <TabsContent value="stories" className="mt-0">
          {renderInsightsList(filteredInsights.filter(insight => insight.type === InsightType.STORY))}
        </TabsContent>

        <TabsContent value="analysis" className="mt-0">
          {renderInsightsList(filteredInsights.filter(
            insight => insight.type === InsightType.ANALYSIS || insight.type === InsightType.FORECAST
          ))}
        </TabsContent>
      </Tabs>

      {/* Create/Edit Insight Dialog */}
      <Dialog
        open={isCreatingInsight || isEditingInsight}
        onOpenChange={(open) => {
          if (!open) {
            setIsCreatingInsight(false);
            setIsEditingInsight(false);
            setCurrentInsight(null);
          }
        }}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>
              {isEditingInsight ? 'Edit Insight' : 'Tambah Insight Baru'}
            </DialogTitle>
            <DialogDescription>
              {isEditingInsight
                ? 'Edit informasi insight yang sudah ada'
                : 'Tambahkan insight marketing baru ke database'}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right">
                Judul
              </Label>
              <Input
                id="title"
                value={currentInsight?.title || ''}
                onChange={(e) => setCurrentInsight({...currentInsight!, title: e.target.value})}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="category" className="text-right">
                Kategori
              </Label>
              <Select
                value={currentInsight?.category || InsightCategory.STRATEGY}
                onValueChange={(value) => setCurrentInsight({...currentInsight!, category: value as InsightCategory})}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Pilih kategori" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(InsightCategory).map((category) => (
                    <SelectItem key={category} value={category}>
                      <div className="flex items-center">
                        {getCategoryIcon(category as InsightCategory)}
                        <span className="ml-2">
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Tipe
              </Label>
              <Select
                value={currentInsight?.type || InsightType.TIP}
                onValueChange={(value) => setCurrentInsight({...currentInsight!, type: value as InsightType})}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Pilih tipe" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(InsightType).map((type) => (
                    <SelectItem key={type} value={type}>
                      <div className="flex items-center">
                        {getTypeIcon(type as InsightType)}
                        <span className="ml-2">
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="content" className="text-right pt-2">
                Konten
              </Label>
              <Textarea
                id="content"
                value={currentInsight?.content || ''}
                onChange={(e) => setCurrentInsight({...currentInsight!, content: e.target.value})}
                className="col-span-3 min-h-[200px]"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="tags" className="text-right">
                Tags
              </Label>
              <div className="col-span-3">
                <Input
                  id="tags"
                  placeholder="Masukkan tag dan tekan Enter"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && (e.target as HTMLInputElement).value.trim()) {
                      e.preventDefault();
                      const newTag = (e.target as HTMLInputElement).value.trim();
                      if (!currentInsight?.tags?.includes(newTag)) {
                        setCurrentInsight({
                          ...currentInsight!,
                          tags: [...(currentInsight?.tags || []), newTag]
                        });
                      }
                      (e.target as HTMLInputElement).value = '';
                    }
                  }}
                />
                <div className="flex flex-wrap gap-2 mt-2">
                  {currentInsight?.tags?.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <button
                        type="button"
                        onClick={() => {
                          setCurrentInsight({
                            ...currentInsight!,
                            tags: currentInsight!.tags.filter((_, i) => i !== index)
                          });
                        }}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        &times;
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="source" className="text-right pt-2">
                Sumber
              </Label>
              <div className="col-span-3">
                <Textarea
                  id="source"
                  value={currentInsight?.source || ''}
                  onChange={(e) => setCurrentInsight({...currentInsight!, source: e.target.value})}
                  placeholder="Sumber dalam format markdown: [Judul Sumber](URL)"
                  className="min-h-[100px]"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Format: [Judul Sumber](https://example.com) - Satu sumber per baris
                </p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsCreatingInsight(false);
              setIsEditingInsight(false);
              setCurrentInsight(null);
            }}>
              Batal
            </Button>
            <Button onClick={handleSaveInsight}>
              <Save size={16} className="mr-2" />
              Simpan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* AI Generation Dialog */}
      <Dialog open={showGenerationDialog} onOpenChange={setShowGenerationDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Buat Insight dengan AI</DialogTitle>
            <DialogDescription>
              Masukkan prompt untuk membuat insight marketing baru dengan bantuan AI menggunakan model GPT-4o nano
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="generationType" className="text-right">
                Tipe
              </Label>
              <Select
                value={generationType}
                onValueChange={(value) => setGenerationType(value as InsightType)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Pilih tipe" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(InsightType).map((type) => (
                    <SelectItem key={type} value={type}>
                      <div className="flex items-center">
                        {getTypeIcon(type as InsightType)}
                        <span className="ml-2">
                          {type.charAt(0).toUpperCase() + type.slice(1)}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="generationCategory" className="text-right">
                Kategori
              </Label>
              <Select
                value={generationCategory}
                onValueChange={(value) => setGenerationCategory(value as InsightCategory)}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Pilih kategori" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(InsightCategory).map((category) => (
                    <SelectItem key={category} value={category}>
                      <div className="flex items-center">
                        {getCategoryIcon(category as InsightCategory)}
                        <span className="ml-2">
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="generationPrompt" className="text-right pt-2">
                Prompt
              </Label>
              <div className="col-span-3 space-y-3">
                <Textarea
                  id="generationPrompt"
                  value={generationPrompt}
                  onChange={(e) => setGenerationPrompt(e.target.value)}
                  placeholder="Contoh: Buat tips marketing untuk ban tambang dengan fokus pada nilai jangka panjang berdasarkan data industri terbaru"
                  className="min-h-[100px]"
                />
                <div className="bg-blue-50 p-3 rounded-md text-sm text-blue-800 border border-blue-200">
                  <p className="font-medium mb-1">Insight yang dihasilkan akan mencakup:</p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Data faktual dan statistik dari sumber terpercaya</li>
                    <li>Cerita atau contoh kasus nyata yang relevan</li>
                    <li>Referensi dari sumber kredibel (BPS, laporan industri, jurnal, dll)</li>
                    <li>Minimal 5 tags yang relevan dengan konten</li>
                  </ul>
                  <p className="mt-2 text-xs">Untuk hasil terbaik, minta topik spesifik dan sebutkan jenis data atau sumber yang Anda inginkan.</p>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowGenerationDialog(false)}>
              Batal
            </Button>
            <Button onClick={handleGenerateInsight} disabled={generating}>
              {generating ? (
                <>
                  <RefreshCw size={16} className="mr-2 animate-spin" />
                  Membuat...
                </>
              ) : (
                <>
                  <Sparkles size={16} className="mr-2" />
                  Buat dengan AI
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus insight ini? Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteInsight} className="bg-red-500 hover:bg-red-600">
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* View Modal Dialog */}
      {showViewModal && selectedInsight && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <div className="flex items-center">
                <Sparkles size={20} className="text-blue-500 mr-2" />
                <h3 className="text-lg font-medium">Detail Marketing Insight</h3>
              </div>
              <button
                onClick={() => setShowViewModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-6">
              <div className="mb-6">
                <div className="flex items-center gap-2 mb-2">
                  {getTypeIcon(selectedInsight.type)}
                  <Badge variant="outline" className="mr-2">
                    {selectedInsight.type.charAt(0).toUpperCase() + selectedInsight.type.slice(1)}
                  </Badge>
                  {getCategoryIcon(selectedInsight.category)}
                  <Badge variant="outline">
                    {selectedInsight.category.charAt(0).toUpperCase() + selectedInsight.category.slice(1)}
                  </Badge>
                  <button
                    onClick={() => toggleFavorite(selectedInsight)}
                    className={`p-1 rounded-full ml-auto ${
                      selectedInsight.isFavorite ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-300 hover:text-gray-400'
                    }`}
                  >
                    <Bookmark size={16} fill={selectedInsight.isFavorite ? 'currentColor' : 'none'} />
                  </button>
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedInsight.title}</h2>
                <div className="flex items-center text-sm text-gray-500 mb-4">
                  <Clock size={14} className="mr-1" />
                  {formatDate(selectedInsight.updatedAt)}
                </div>
              </div>

              <div className="prose max-w-none mb-6">
                <div className="whitespace-pre-line text-gray-700">
                  {selectedInsight.content}
                </div>
              </div>

              {selectedInsight.source && (
                <div className="mb-6 border-t pt-4">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-sm font-medium text-gray-700 flex items-center">
                      <ExternalLink size={14} className="mr-2" />
                      Sumber Referensi:
                    </h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openSourceUrl(selectedInsight.source)}
                      className="flex items-center text-xs"
                    >
                      <ExternalLink size={12} className="mr-1" />
                      Buka Sumber di Tab Baru
                    </Button>
                  </div>
                  <div className="text-sm text-gray-600 space-y-2">
                    {selectedInsight.source.split('\n\n').map((section, sectionIndex) => {
                      // Check if this is a header section
                      if (section.startsWith('###')) {
                        const headerText = section.replace(/^###\s+/, '');
                        return (
                          <h4 key={sectionIndex} className="font-semibold text-gray-700 mt-4 mb-2">
                            {headerText}
                          </h4>
                        );
                      }

                      // Check if this is a source link
                      const linkMatch = section.match(/\[([^\]]+)\]\(([^)]+)\)(\s*-\s*([^<\n]+))?/);
                      if (linkMatch) {
                        const [_, title, url, publisherPart = '', publisher = ''] = linkMatch;
                        return (
                          <div key={sectionIndex} className="flex items-center mb-2">
                            <ExternalLink className="mr-2 text-blue-500" size={12} />
                            <button
                              onClick={() => openUrlInNewTab(url)}
                              className="text-blue-600 hover:underline font-medium text-left"
                            >
                              {title}
                            </button>
                            {publisherPart && <span className="text-gray-600">{publisherPart}</span>}
                          </div>
                        );
                      }

                      // Regular text
                      return <p key={sectionIndex}>{section}</p>;
                    })}
                  </div>
                </div>
              )}

              {selectedInsight.tags.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Tags:</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedInsight.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        #{tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="p-4 border-t border-gray-200 flex justify-between">
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={() => {
                  setCurrentInsight(selectedInsight);
                  setIsEditingInsight(true);
                  setShowViewModal(false);
                }}>
                  <Edit size={14} className="mr-1" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" onClick={() => copyToClipboard(selectedInsight.content)}>
                  <Copy size={14} className="mr-1" />
                  Salin Konten
                </Button>
              </div>
              <Button variant="outline" onClick={() => setShowViewModal(false)}>
                Tutup
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Helper function to render insights list
  function renderInsightsList(insights: MarketingInsight[]) {
    if (loading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <CardHeader className="pb-2">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3" />
              </CardContent>
              <CardFooter>
                <Skeleton className="h-8 w-full" />
              </CardFooter>
            </Card>
          ))}
        </div>
      );
    }

    if (insights.length === 0) {
      return (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <AlertTriangle size={48} className="mx-auto text-yellow-500 mb-4" />
          <h3 className="text-xl font-semibold text-gray-800 mb-2">Tidak Ada Data</h3>
          <p className="text-gray-600 mb-6">Belum ada insight yang tersedia dengan filter yang dipilih.</p>
          <Button onClick={() => {
            setSearchTerm('');
            setCategoryFilter('all');
            setTypeFilter('all');
            setShowFavoritesOnly(false);
          }}>
            Reset Filter
          </Button>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {insights.map((insight) => (
          <Card key={insight.id} className="overflow-hidden hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    {getTypeIcon(insight.type)}
                    <Badge variant="outline">
                      {insight.type.charAt(0).toUpperCase() + insight.type.slice(1)}
                    </Badge>
                    {getCategoryIcon(insight.category)}
                    <Badge variant="outline">
                      {insight.category.charAt(0).toUpperCase() + insight.category.slice(1)}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg">{insight.title}</CardTitle>
                </div>
                <button
                  onClick={() => toggleFavorite(insight)}
                  className={`p-1 rounded-full ${
                    insight.isFavorite ? 'text-yellow-500 hover:text-yellow-600' : 'text-gray-300 hover:text-gray-400'
                  }`}
                >
                  <Bookmark size={16} fill={insight.isFavorite ? 'currentColor' : 'none'} />
                </button>
              </div>
              <CardDescription className="flex items-center text-xs">
                <Clock size={12} className="mr-1" />
                {formatDate(insight.updatedAt)}
                {insight.source && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      openSourceUrl(insight.source);
                    }}
                    className="ml-2 text-blue-500 hover:text-blue-700 flex items-center"
                    title="Buka sumber di tab baru"
                  >
                    • <ExternalLink size={10} className="mx-1" /> Buka di Tab Baru
                  </button>
                )}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-sm text-gray-700 whitespace-pre-line line-clamp-4">
                {insight.content}
              </div>
              {insight.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-3">
                  {insight.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between pt-2 border-t">
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={() => {
                  setCurrentInsight(insight);
                  setIsEditingInsight(true);
                }}>
                  <Edit size={14} className="mr-1" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" onClick={() => copyToClipboard(insight.content)}>
                  <Copy size={14} className="mr-1" />
                  Salin
                </Button>
                <Button variant="outline" size="sm" onClick={() => {
                  setSelectedInsight(insight);
                  setShowViewModal(true);
                }}>
                  <Eye size={14} className="mr-1" />
                  View Modal
                </Button>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-red-500 hover:text-red-700 hover:bg-red-50"
                onClick={() => {
                  setInsightToDelete(insight.id);
                  setShowDeleteDialog(true);
                }}
              >
                <Trash2 size={14} className="mr-1" />
                Hapus
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }
}
