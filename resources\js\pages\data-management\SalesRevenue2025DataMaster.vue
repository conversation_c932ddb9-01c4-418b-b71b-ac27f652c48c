<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Sales Revenue 2025 Data Master</h1>
                    <p class="mt-2 text-gray-600">Comprehensive sales revenue tracking and forecasting for 2025</p>
                </div>
                <div class="flex space-x-3">
                    <select v-model="selectedPeriod" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                        <option value="monthly">Monthly View</option>
                        <option value="quarterly">Quarterly View</option>
                        <option value="yearly">Yearly View</option>
                    </select>
                    <button
                        @click="exportData"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Report
                    </button>
                    <button
                        @click="refreshForecast"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <RefreshCw class="h-4 w-4 mr-2" />
                        Update Forecast
                    </button>
                </div>
            </div>

            <!-- KPI Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <DollarSign class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">YTD Revenue</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(kpiData.ytdRevenue) }}</p>
                            <p class="text-sm text-green-600">+{{ kpiData.ytdGrowth }}% vs 2024</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Target class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Target Achievement</p>
                            <p class="text-2xl font-bold text-gray-900">{{ kpiData.targetAchievement }}%</p>
                            <p class="text-sm text-blue-600">{{ formatCurrency(kpiData.remainingTarget) }} remaining</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <TrendingUp class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Forecast 2025</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(kpiData.forecast2025) }}</p>
                            <p class="text-sm text-purple-600">{{ kpiData.forecastConfidence }}% confidence</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Calendar class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Avg Monthly</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(kpiData.avgMonthly) }}</p>
                            <p class="text-sm text-orange-600">{{ kpiData.monthsRemaining }} months left</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revenue Trend Chart -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Revenue Trend & Forecast</h3>
                    <div class="flex space-x-2">
                        <button
                            v-for="period in chartPeriods"
                            :key="period.value"
                            @click="selectedChartPeriod = period.value"
                            :class="[
                                'px-3 py-1 text-sm rounded-md',
                                selectedChartPeriod === period.value
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            ]"
                        >
                            {{ period.label }}
                        </button>
                    </div>
                </div>
                <div class="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
                    <div class="text-center">
                        <BarChart3 class="h-12 w-12 text-gray-400 mx-auto mb-2" />
                        <p class="text-gray-600">Revenue Trend Chart</p>
                        <p class="text-sm text-gray-500">Chart.js integration with actual vs forecast data</p>
                    </div>
                </div>
            </div>

            <!-- Target vs Actual Comparison -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Monthly Breakdown -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Monthly Target vs Actual</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div v-for="month in monthlyData" :key="month.month" class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex justify-between items-center mb-1">
                                        <span class="text-sm font-medium text-gray-900">{{ month.month }}</span>
                                        <span class="text-sm text-gray-600">{{ month.achievement }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div 
                                            class="h-2 rounded-full transition-all duration-300"
                                            :class="month.achievement >= 100 ? 'bg-green-500' : month.achievement >= 80 ? 'bg-blue-500' : 'bg-red-500'"
                                            :style="{ width: Math.min(100, month.achievement) + '%' }"
                                        ></div>
                                    </div>
                                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                                        <span>Actual: {{ formatCurrency(month.actual) }}</span>
                                        <span>Target: {{ formatCurrency(month.target) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product Category Performance -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Revenue by Product Category</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div v-for="category in categoryData" :key="category.name" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <div :class="['w-4 h-4 rounded-full mr-3', category.color]"></div>
                                    <div>
                                        <h4 class="font-medium text-gray-900">{{ category.name }}</h4>
                                        <p class="text-sm text-gray-600">{{ category.percentage }}% of total</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold text-gray-900">{{ formatCurrency(category.revenue) }}</p>
                                    <p :class="['text-sm', category.growth >= 0 ? 'text-green-600' : 'text-red-600']">
                                        {{ category.growth >= 0 ? '+' : '' }}{{ category.growth }}%
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Data Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Detailed Revenue Data</h3>
                    <div class="flex space-x-3">
                        <input
                            v-model="searchQuery"
                            type="text"
                            placeholder="Search..."
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center">
                            <Filter class="h-4 w-4 mr-2" />
                            Filter
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actual</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Achievement</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Variance</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Forecast</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="row in filteredTableData" :key="row.period">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ row.period }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(row.target) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(row.actual) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        row.achievement >= 100 ? 'bg-green-100 text-green-800' :
                                        row.achievement >= 80 ? 'bg-blue-100 text-blue-800' :
                                        'bg-red-100 text-red-800'
                                    ]">
                                        {{ row.achievement }}%
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <span :class="row.variance >= 0 ? 'text-green-600' : 'text-red-600'">
                                        {{ row.variance >= 0 ? '+' : '' }}{{ formatCurrency(row.variance) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(row.forecast) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-blue-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-blue-900">Advanced Features Coming Soon</h3>
                        <p class="text-blue-700 mt-1">
                            Enhanced features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-blue-700 mt-2 space-y-1">
                            <li>AI-powered revenue forecasting with machine learning</li>
                            <li>Real-time data integration with ERP systems</li>
                            <li>Advanced scenario planning and what-if analysis</li>
                            <li>Automated alerts for target deviations</li>
                            <li>Interactive dashboard customization</li>
                            <li>Multi-currency support and exchange rate tracking</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Download,
    RefreshCw,
    DollarSign,
    Target,
    TrendingUp,
    Calendar,
    BarChart3,
    Filter,
    Info
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Reactive state
const selectedPeriod = ref('monthly');
const selectedChartPeriod = ref('12months');
const searchQuery = ref('');

// Chart periods
const chartPeriods = [
    { value: '6months', label: '6M' },
    { value: '12months', label: '12M' },
    { value: '24months', label: '24M' }
];

// KPI Data
const kpiData = ref({
    ytdRevenue: 18500000000,
    ytdGrowth: 15.2,
    targetAchievement: 87.3,
    remainingTarget: 3200000000,
    forecast2025: 24800000000,
    forecastConfidence: 92,
    avgMonthly: 2100000000,
    monthsRemaining: 4
});

// Monthly data
const monthlyData = ref([
    { month: 'Jan 2025', target: 2000000000, actual: 2150000000, achievement: 107.5 },
    { month: 'Feb 2025', target: 2000000000, actual: 1950000000, achievement: 97.5 },
    { month: 'Mar 2025', target: 2000000000, actual: 2200000000, achievement: 110.0 },
    { month: 'Apr 2025', target: 2000000000, actual: 1850000000, achievement: 92.5 },
    { month: 'May 2025', target: 2000000000, actual: 2100000000, achievement: 105.0 },
    { month: 'Jun 2025', target: 2000000000, actual: 1980000000, achievement: 99.0 },
    { month: 'Jul 2025', target: 2000000000, actual: 2050000000, achievement: 102.5 },
    { month: 'Aug 2025', target: 2000000000, actual: 1900000000, achievement: 95.0 },
    { month: 'Sep 2025', target: 2200000000, actual: 0, achievement: 0 },
    { month: 'Oct 2025', target: 2200000000, actual: 0, achievement: 0 },
    { month: 'Nov 2025', target: 2400000000, actual: 0, achievement: 0 },
    { month: 'Dec 2025', target: 2600000000, actual: 0, achievement: 0 }
]);

// Category data
const categoryData = ref([
    { name: 'Heavy Equipment Tires', revenue: 8500000000, percentage: 45.9, growth: 18.2, color: 'bg-blue-500' },
    { name: 'Mining Tires', revenue: 6200000000, percentage: 33.5, growth: 12.8, color: 'bg-green-500' },
    { name: 'Truck Tires', revenue: 2800000000, percentage: 15.1, growth: 8.5, color: 'bg-purple-500' },
    { name: 'Passenger Tires', revenue: 1000000000, percentage: 5.4, growth: -2.1, color: 'bg-orange-500' }
]);

// Table data
const tableData = ref([
    { period: 'Q1 2025', target: 6000000000, actual: 6300000000, achievement: 105, variance: 300000000, forecast: 6200000000 },
    { period: 'Q2 2025', target: 6000000000, actual: 5930000000, achievement: 99, variance: -70000000, forecast: 6100000000 },
    { period: 'Q3 2025', target: 6600000000, actual: 0, achievement: 0, variance: 0, forecast: 6400000000 },
    { period: 'Q4 2025', target: 7200000000, actual: 0, achievement: 0, variance: 0, forecast: 7300000000 },
    { period: 'Jan 2025', target: 2000000000, actual: 2150000000, achievement: 108, variance: 150000000, forecast: 2100000000 },
    { period: 'Feb 2025', target: 2000000000, actual: 1950000000, achievement: 98, variance: -50000000, forecast: 2000000000 },
    { period: 'Mar 2025', target: 2000000000, actual: 2200000000, achievement: 110, variance: 200000000, forecast: 2100000000 },
    { period: 'Apr 2025', target: 2000000000, actual: 1850000000, achievement: 93, variance: -150000000, forecast: 1950000000 },
    { period: 'May 2025', target: 2000000000, actual: 2100000000, achievement: 105, variance: 100000000, forecast: 2050000000 },
    { period: 'Jun 2025', target: 2000000000, actual: 1980000000, achievement: 99, variance: -20000000, forecast: 2000000000 },
    { period: 'Jul 2025', target: 2000000000, actual: 2050000000, achievement: 103, variance: 50000000, forecast: 2000000000 },
    { period: 'Aug 2025', target: 2000000000, actual: 1900000000, achievement: 95, variance: -100000000, forecast: 1950000000 }
]);

// Computed properties
const filteredTableData = computed(() => {
    if (!searchQuery.value) return tableData.value;
    return tableData.value.filter(row =>
        row.period.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
});

// Utility functions
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

// Event handlers
const exportData = () => {
    try {
        const csvContent = [
            ['Period', 'Target', 'Actual', 'Achievement (%)', 'Variance', 'Forecast'].join(','),
            ...filteredTableData.value.map(row => [
                row.period,
                row.target,
                row.actual,
                row.achievement,
                row.variance,
                row.forecast
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `sales_revenue_2025_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Data berhasil diekspor ke CSV!');
    } catch (error) {
        console.error('Error exporting data:', error);
        alert('Gagal mengekspor data.');
    }
};

const refreshForecast = () => {
    // Simulate forecast update
    alert('Forecast updated with latest data and market trends!');
};

// Initialize on mount
onMounted(() => {
    // Load initial data
});
</script>
