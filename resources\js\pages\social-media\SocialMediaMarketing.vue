<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Social Media Marketing</h1>
                    <p class="mt-2 text-gray-600">Comprehensive social media management and analytics dashboard</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="schedulePost"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <Calendar class="h-4 w-4 mr-2" />
                        Schedule Post
                    </button>
                    <button
                        @click="generateReport"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <BarChart3 class="h-4 w-4 mr-2" />
                        Generate Report
                    </button>
                </div>
            </div>

            <!-- Social Media Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <Users class="h-6 w-6 text-blue-600" />
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Followers</p>
                            <p class="text-2xl font-bold text-gray-900">{{ socialMetrics.totalFollowers.toLocaleString() }}</p>
                            <p class="text-sm text-green-600">+{{ socialMetrics.followerGrowth }}% this month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <Heart class="h-6 w-6 text-green-600" />
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Engagement Rate</p>
                            <p class="text-2xl font-bold text-gray-900">{{ socialMetrics.engagementRate }}%</p>
                            <p class="text-sm text-green-600">+{{ socialMetrics.engagementGrowth }}% vs last month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <MessageSquare class="h-6 w-6 text-purple-600" />
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Posts This Month</p>
                            <p class="text-2xl font-bold text-gray-900">{{ socialMetrics.postsThisMonth }}</p>
                            <p class="text-sm text-purple-600">{{ socialMetrics.avgPostsPerWeek }} per week</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-orange-100 rounded-lg">
                            <TrendingUp class="h-6 w-6 text-orange-600" />
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Reach</p>
                            <p class="text-2xl font-bold text-gray-900">{{ socialMetrics.reach.toLocaleString() }}</p>
                            <p class="text-sm text-orange-600">{{ socialMetrics.reachGrowth }}% increase</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Platform Performance -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Platform Stats -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Platform Performance</h3>
                    <div class="space-y-4">
                        <div v-for="platform in platformStats" :key="platform.name" class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div :class="['w-4 h-4 rounded-full mr-3', platform.color]"></div>
                                <div>
                                    <h4 class="font-medium text-gray-900">{{ platform.name }}</h4>
                                    <p class="text-sm text-gray-600">{{ platform.followers.toLocaleString() }} followers</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-gray-900">{{ platform.engagement }}%</p>
                                <p class="text-sm text-gray-500">engagement</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Performance -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Content Performance</h3>
                    <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                        <div class="text-center">
                            <BarChart3 class="h-12 w-12 text-gray-400 mx-auto mb-2" />
                            <p class="text-gray-600">Content Performance Chart</p>
                            <p class="text-sm text-gray-500">Chart.js integration</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Posts -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Posts</h3>
                    <div class="flex space-x-3">
                        <select v-model="selectedPlatform" class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option value="all">All Platforms</option>
                            <option value="instagram">Instagram</option>
                            <option value="facebook">Facebook</option>
                            <option value="linkedin">LinkedIn</option>
                            <option value="twitter">Twitter</option>
                        </select>
                        <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center text-sm">
                            <Filter class="h-4 w-4 mr-2" />
                            Filter
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div v-for="post in filteredPosts" :key="post.id" class="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg">
                            <div class="flex-shrink-0">
                                <div :class="['w-10 h-10 rounded-lg flex items-center justify-center', getPlatformColor(post.platform)]">
                                    <component :is="getPlatformIcon(post.platform)" class="h-5 w-5 text-white" />
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-medium text-gray-900 truncate">{{ post.title }}</h4>
                                    <span class="text-sm text-gray-500">{{ post.publishedAt }}</span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">{{ post.content }}</p>
                                <div class="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <Heart class="h-4 w-4 mr-1" />
                                        {{ post.likes }}
                                    </div>
                                    <div class="flex items-center">
                                        <MessageSquare class="h-4 w-4 mr-1" />
                                        {{ post.comments }}
                                    </div>
                                    <div class="flex items-center">
                                        <Share class="h-4 w-4 mr-1" />
                                        {{ post.shares }}
                                    </div>
                                    <div class="flex items-center">
                                        <Eye class="h-4 w-4 mr-1" />
                                        {{ post.views }}
                                    </div>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center mb-4">
                        <Calendar class="h-6 w-6 text-blue-600 mr-3" />
                        <h3 class="font-semibold text-gray-900">Content Calendar</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Plan and schedule your social media content</p>
                    <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Open Calendar
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center mb-4">
                        <Zap class="h-6 w-6 text-purple-600 mr-3" />
                        <h3 class="font-semibold text-gray-900">AI Content Generator</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Generate engaging content with AI assistance</p>
                    <button class="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                        Generate Content
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center mb-4">
                        <BarChart3 class="h-6 w-6 text-green-600 mr-3" />
                        <h3 class="font-semibold text-gray-900">Analytics Dashboard</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Deep dive into your social media performance</p>
                    <button class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                        View Analytics
                    </button>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-blue-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-blue-900">Advanced Social Media Features Coming Soon</h3>
                        <p class="text-blue-700 mt-1">
                            Enhanced social media management features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-blue-700 mt-2 space-y-1">
                            <li>AI-powered content creation and optimization</li>
                            <li>Advanced social listening and sentiment analysis</li>
                            <li>Automated posting and engagement management</li>
                            <li>Competitor analysis and benchmarking</li>
                            <li>Influencer identification and outreach tools</li>
                            <li>ROI tracking and attribution modeling</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Calendar,
    BarChart3,
    Users,
    Heart,
    MessageSquare,
    TrendingUp,
    Filter,
    Share,
    Eye,
    Zap,
    Info
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Reactive state
const selectedPlatform = ref('all');

// Social media metrics
const socialMetrics = ref({
    totalFollowers: 45230,
    followerGrowth: 12.5,
    engagementRate: 4.8,
    engagementGrowth: 8.3,
    postsThisMonth: 28,
    avgPostsPerWeek: 7,
    reach: 125000,
    reachGrowth: 15.2
});

// Platform statistics
const platformStats = ref([
    { name: 'Instagram', followers: 18500, engagement: 5.2, color: 'bg-pink-500' },
    { name: 'Facebook', followers: 12300, engagement: 3.8, color: 'bg-blue-600' },
    { name: 'LinkedIn', followers: 8900, engagement: 6.1, color: 'bg-blue-700' },
    { name: 'Twitter', followers: 5530, engagement: 2.9, color: 'bg-blue-400' }
]);

// Recent posts data
const posts = ref([
    {
        id: 1,
        platform: 'instagram',
        title: 'New Heavy Equipment Tires Launch',
        content: 'Introducing our latest 27.00 R 49 XD GRIP series for extreme mining conditions...',
        publishedAt: '2 hours ago',
        likes: 245,
        comments: 18,
        shares: 12,
        views: 1250
    },
    {
        id: 2,
        platform: 'linkedin',
        title: 'Mining Industry Insights',
        content: 'Key trends shaping the mining industry in 2025 and how tire technology is evolving...',
        publishedAt: '5 hours ago',
        likes: 89,
        comments: 23,
        shares: 34,
        views: 890
    },
    {
        id: 3,
        platform: 'facebook',
        title: 'Customer Success Story',
        content: 'PT Mining Sejahtera shares their experience with our tire solutions...',
        publishedAt: '1 day ago',
        likes: 156,
        comments: 12,
        shares: 8,
        views: 2100
    },
    {
        id: 4,
        platform: 'twitter',
        title: 'Quick Tire Maintenance Tips',
        content: '5 essential tips to extend your heavy equipment tire lifespan #TireMaintenance',
        publishedAt: '2 days ago',
        likes: 67,
        comments: 5,
        shares: 23,
        views: 450
    }
]);

// Computed properties
const filteredPosts = computed(() => {
    if (selectedPlatform.value === 'all') return posts.value;
    return posts.value.filter(post => post.platform === selectedPlatform.value);
});

// Utility functions
const getPlatformColor = (platform: string): string => {
    const colors: Record<string, string> = {
        instagram: 'bg-pink-500',
        facebook: 'bg-blue-600',
        linkedin: 'bg-blue-700',
        twitter: 'bg-blue-400'
    };
    return colors[platform] || 'bg-gray-500';
};

const getPlatformIcon = (platform: string) => {
    // For now, using MessageSquare for all platforms
    // In a real app, you'd import specific platform icons
    return MessageSquare;
};

// Event handlers
const schedulePost = () => {
    alert('Opening post scheduler...\n\nFeatures:\n- Multi-platform posting\n- Optimal timing suggestions\n- Content templates\n- Media library integration');
};

const generateReport = () => {
    alert('Generating comprehensive social media report...\n\nIncluding:\n- Performance metrics\n- Engagement analysis\n- Growth trends\n- Competitor comparison');
};

// Initialize on mount
onMounted(() => {
    // Load social media data
});
</script>
