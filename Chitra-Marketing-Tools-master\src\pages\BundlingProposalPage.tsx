import React, { useState } from 'react';
import { ProposalFormData } from '../types/proposal';
import ProposalForm from '../components/ProposalForm';
import { generateProposal, generateTextProposal } from '../services/proposalService';

const BundlingProposalPage: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateProposal = async (data: ProposalFormData) => {
    try {
      setIsGenerating(true);
      setError(null);

      // Check if a template is provided
      if (!data.template) {
        console.log('No template provided, generating template guide...');
        const guideResult = await generateTextProposal(data);
        alert('Tidak ada template yang dipilih. Panduan template telah diunduh.');
        console.log('Generated template guide:', guideResult);
        return;
      }

      console.log('Template found:', data.template.name);

      try {
        // Try to generate PDF proposal from template
        console.log('Attempting to generate PDF proposal from template...');
        const result = await generateProposal(data);

        // Show success message
        alert('Proposal berhasil dibuat! File akan diunduh secara otomatis.');

        // Log the result
        console.log('Generated proposal:', result);
      } catch (pdfError) {
        console.error('PDF generation failed:', pdfError);

        // Show error message
        let errorMessage = 'Gagal membuat proposal dari template. Silakan periksa format template.';
        if (pdfError instanceof Error) {
          console.error('Error details:', pdfError.message);
          console.error('Error stack:', pdfError.stack);
          errorMessage = `Gagal membuat proposal: ${pdfError.message}`;
        }

        setError(errorMessage);

        // Generate template guide as fallback
        console.log('Generating template guide as fallback...');
        const guideResult = await generateTextProposal(data);
        console.log('Generated template guide:', guideResult);
      }
    } catch (err) {
      console.error('All proposal generation methods failed:', err);

      // Provide more detailed error message
      let errorMessage = 'Gagal membuat proposal. Silakan coba lagi.';
      if (err instanceof Error) {
        console.error('Error details:', err.message);
        console.error('Error stack:', err.stack);
        errorMessage = `Gagal membuat proposal: ${err.message}`;
      }

      setError(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Bundling Proposal Generator</h1>
        <p className="mt-2 text-sm text-gray-600">
          Buat proposal bundling, konsinyasi, atau trade-in dengan mudah menggunakan template yang dapat disesuaikan.
        </p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <div className="bg-white shadow-sm rounded-lg p-6">
        <ProposalForm
          onSubmit={handleGenerateProposal}
        />
      </div>

      {/* Loading overlay */}
      {isGenerating && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-center text-gray-600">Membuat proposal...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default BundlingProposalPage;
