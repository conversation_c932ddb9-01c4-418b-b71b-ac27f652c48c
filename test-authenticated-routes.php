<?php

// Test script untuk mengecek routes setelah login
$baseUrl = 'http://chitra-marketing-tools-laravel.test';
$email = '<EMAIL>';
$password = 'password123';

// Routes to test
$routes = [
    '/dashboard',
    '/ban-27-bundling',
    '/bundling-calculator', 
    '/zero-margin-bundling',
    '/promo-simulation',
    '/products',
    '/customers',
    '/coal-price-data',
    '/template-management',
    '/analytics-dashboard',
    '/sales-dashboard',
    '/seasonal-marketing-calendar',
    '/content-planning-tools',
    '/negotiation-simulator'
];

echo "Testing authenticated routes...\n\n";

// Initialize cURL session for login
$ch = curl_init();

// Get CSRF token first
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookies.txt');

$loginPage = curl_exec($ch);

// Extract CSRF token
preg_match('/<meta name="csrf-token" content="([^"]+)"/', $loginPage, $matches);
$csrfToken = $matches[1] ?? '';

if (empty($csrfToken)) {
    echo "❌ Could not get CSRF token\n";
    exit(1);
}

echo "✅ Got CSRF token: " . substr($csrfToken, 0, 10) . "...\n";

// Perform login
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
    '_token' => $csrfToken,
    'email' => $email,
    'password' => $password
]));
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
curl_setopt($ch, CURLOPT_HEADER, true);

$loginResponse = curl_exec($ch);
$loginHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if ($loginHttpCode == 302) {
    echo "✅ Login successful (redirected)\n\n";
} else {
    echo "❌ Login failed (HTTP $loginHttpCode)\n";
    echo $loginResponse;
    exit(1);
}

// Test each route
$results = [];
foreach ($routes as $route) {
    $url = $baseUrl . $route;
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, false);
    curl_setopt($ch, CURLOPT_POSTFIELDS, '');
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    $status = '';
    $hasError = false;
    
    if ($httpCode == 200) {
        // Check for common error indicators in response
        if (strpos($response, 'Oops! Something went wrong') !== false) {
            $status = '💥 PAGE ERROR';
            $hasError = true;
        } elseif (strpos($response, 'Error') !== false && strpos($response, 'error-boundary') !== false) {
            $status = '💥 COMPONENT ERROR';
            $hasError = true;
        } elseif (strpos($response, '<title>') !== false) {
            // Extract page title
            preg_match('/<title>([^<]+)<\/title>/', $response, $titleMatches);
            $title = $titleMatches[1] ?? 'Unknown';
            $status = "✅ OK ($title)";
        } else {
            $status = '✅ OK';
        }
    } elseif ($httpCode == 302) {
        $status = '🔄 REDIRECT';
    } elseif ($httpCode == 404) {
        $status = '❌ NOT FOUND';
        $hasError = true;
    } elseif ($httpCode == 500) {
        $status = '💥 SERVER ERROR';
        $hasError = true;
    } else {
        $status = "⚠️  HTTP $httpCode";
        $hasError = true;
    }
    
    echo sprintf("%-35s %s\n", $route, $status);
    $results[$route] = ['code' => $httpCode, 'error' => $hasError];
    
    // Small delay to avoid overwhelming server
    usleep(200000); // 200ms
}

curl_close($ch);

// Clean up cookies file
if (file_exists('cookies.txt')) {
    unlink('cookies.txt');
}

echo "\n\nSummary:\n";
$ok = count(array_filter($results, fn($r) => $r['code'] == 200 && !$r['error']));
$errors = count(array_filter($results, fn($r) => $r['error']));
$total = count($results);

echo "✅ Working: $ok\n";
echo "💥 Errors: $errors\n";
echo "📊 Total: $total\n";

if ($errors > 0) {
    echo "\n🔧 Pages with errors need attention:\n";
    foreach ($results as $route => $result) {
        if ($result['error']) {
            echo "   - $route\n";
        }
    }
}

echo "\n" . ($errors == 0 ? "🎉 All pages working!" : "⚠️  Some pages need fixes") . "\n";
