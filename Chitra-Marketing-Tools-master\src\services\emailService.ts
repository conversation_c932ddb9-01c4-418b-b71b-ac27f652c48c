import axios from 'axios';
import { MODELS } from './openRouter';

// Using the same API keys as the other AI services
const OPENROUTER_API_KEY = '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';

export async function generateEmailOffer(
  productDetails: string,
  price: number,
  specialFeatures: string,
  customerInfo: string,
  modelType: string = 'gemini-flash-lite'
) {
  switch (modelType) {
    case 'deepseek':
      return generateDeepSeekEmailOffer(productDetails, price, specialFeatures, customerInfo);
    case 'gemini-flash-lite':
      return generateGeminiFlashLiteEmailOffer(productDetails, price, specialFeatures, customerInfo);
    default:
      return generateOpenRouterEmailOffer(productDetails, price, specialFeatures, customerInfo);
  }
}

async function generateDeepSeekEmailOffer(
  productDetails: string,
  price: number,
  specialFeatures: string,
  customerInfo: string
) {
  try {
    console.log('Generating email offer with DeepSeek Chat API');

    const requestBody = {
      model: MODELS.DEEPSEEK,
      messages: [
        {
          role: 'system',
          content: 'You are a professional sales representative creating formal email offers for potential clients. Your writing is professional, persuasive, and well-structured.'
        },
        {
          role: 'user',
          content: `Create a PROFESSIONAL and FORMAL email offer in Indonesian for this product bundle:

Products: ${productDetails}
Price: Rp ${price.toLocaleString()}
Special features: ${specialFeatures}
${customerInfo ? 'Customer information: ' + customerInfo : ''}

Follow this format:
1. Start with a formal greeting using the customer's name if available
2. Begin with a brief introduction of PT Chitra Paratama and your role
3. Reference any previous conversations or meetings if mentioned in the customer information
4. Present the product bundle with a professional tone, highlighting key benefits and features
5. Clearly state the price with any applicable terms (validity period, payment terms)
6. Include a section about PT Chitra Paratama's quality assurance and after-sales support
7. Add a clear call-to-action for next steps (schedule a meeting, request a formal quotation, etc.)
8. End with a professional closing and your contact information

The email should be formal but warm, professional but not cold, and persuasive without being pushy. Use proper business Indonesian language throughout.`
        }
      ]
    };

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://bundleboost.app',
          'X-Title': 'Chitra Marketing Tools'
        }
      }
    );

    console.log('DeepSeek API response:', JSON.stringify(response.data, null, 2));

    // Check for different possible response formats
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      // Standard OpenAI/OpenRouter format
      if (response.data.choices[0].message && response.data.choices[0].message.content) {
        const content = response.data.choices[0].message.content;
        if (typeof content === 'string') {
          return content;
        } else if (Array.isArray(content)) {
          return content
            .filter((part: any) => part.type === 'text')
            .map((part: any) => part.text)
            .join('\n');
        }
      }

      // Alternative formats
      if (response.data.choices[0].content) {
        if (typeof response.data.choices[0].content === 'string') {
          return response.data.choices[0].content;
        } else if (response.data.choices[0].content.parts) {
          return response.data.choices[0].content.parts
            .filter((part: any) => part.text)
            .map((part: any) => part.text)
            .join('\n');
        }
      }

      if (response.data.choices[0].text) {
        return response.data.choices[0].text;
      }
    }

    console.error('Unexpected response format:', response.data);
    return 'Failed to generate email offer. Unexpected response format.';
  } catch (error) {
    console.error('Error generating email offer with DeepSeek API:', error);

    // More detailed error logging
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
      return `Failed to generate email offer. Server error: ${error.response.status} - ${JSON.stringify(error.response.data)}`;
    } else if (error.request) {
      console.error('Error request:', error.request);
      return 'Failed to generate email offer. No response received from server.';
    } else {
      console.error('Error message:', error.message);
      return `Failed to generate email offer. Error: ${error.message}`;
    }
  }
}

async function generateGeminiFlashLiteEmailOffer(
  productDetails: string,
  price: number,
  specialFeatures: string,
  customerInfo: string
) {
  try {
    console.log('Generating concise email offer with Gemini 2.0 Flash Lite');

    const requestBody = {
      model: MODELS.GEMINI_FLASH_LITE,
      messages: [
        {
          role: 'system',
          content: 'You are a professional sales representative creating concise, impactful email offers. Your writing is professional yet impressive, brief but persuasive.'
        },
        {
          role: 'user',
          content: `Create a CONCISE and PROFESSIONAL email offer in Indonesian for this product bundle:

Products: ${productDetails}
Price: Rp ${price.toLocaleString()}
Special features: ${specialFeatures}
${customerInfo ? 'Customer information: ' + customerInfo : ''}

Follow these guidelines:
1. Keep it SHORT and IMPACTFUL - no more than 10-12 lines total
2. Start with a brief, professional greeting (use customer name if available)
3. Get straight to the point - introduce the product bundle with its key benefits
4. Clearly state the price with a brief mention of value proposition
5. End with a simple, direct call-to-action
6. Sign off professionally with PT Chitra Paratama

The email should be professional but modern, concise but compelling. Use proper business Indonesian language throughout. Make it impressive without being verbose.`
        }
      ]
    };

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://bundleboost.app',
          'X-Title': 'Chitra Marketing Tools'
        }
      }
    );

    console.log('Gemini Flash Lite API response:', JSON.stringify(response.data, null, 2));

    // Check for different possible response formats
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      // Standard OpenAI/OpenRouter format
      if (response.data.choices[0].message && response.data.choices[0].message.content) {
        const content = response.data.choices[0].message.content;
        if (typeof content === 'string') {
          return content;
        } else if (Array.isArray(content)) {
          return content
            .filter((part: any) => part.type === 'text')
            .map((part: any) => part.text)
            .join('\n');
        }
      }

      // Alternative formats
      if (response.data.choices[0].content) {
        if (typeof response.data.choices[0].content === 'string') {
          return response.data.choices[0].content;
        } else if (response.data.choices[0].content.parts) {
          return response.data.choices[0].content.parts
            .filter((part: any) => part.text)
            .map((part: any) => part.text)
            .join('\n');
        }
      }

      if (response.data.choices[0].text) {
        return response.data.choices[0].text;
      }
    }

    console.error('Unexpected response format:', response.data);
    return 'Failed to generate email offer. Unexpected response format.';
  } catch (error) {
    console.error('Error generating email offer with Gemini Flash Lite:', error);

    // More detailed error logging
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
      return `Failed to generate email offer. Server error: ${error.response.status} - ${JSON.stringify(error.response.data)}`;
    } else if (error.request) {
      console.error('Error request:', error.request);
      return 'Failed to generate email offer. No response received from server.';
    } else {
      console.error('Error message:', error.message);
      return `Failed to generate email offer. Error: ${error.message}`;
    }
  }
}

async function generateOpenRouterEmailOffer(
  productDetails: string,
  price: number,
  specialFeatures: string,
  customerInfo: string
) {
  try {
    console.log('Generating email offer with OpenRouter API');

    const requestBody = {
      model: MODELS.GPT_3_5,
      messages: [
        {
          role: 'system',
          content: 'You are a professional sales representative creating formal email offers for potential clients. Your writing is professional, persuasive, and well-structured.'
        },
        {
          role: 'user',
          content: `Create a PROFESSIONAL and FORMAL email offer in Indonesian for this product bundle:

Products: ${productDetails}
Price: Rp ${price.toLocaleString()}
Special features: ${specialFeatures}
${customerInfo ? 'Customer information: ' + customerInfo : ''}

Follow this format:
1. Start with a formal greeting using the customer's name if available
2. Begin with a brief introduction of PT Chitra Paratama and your role
3. Reference any previous conversations or meetings if mentioned in the customer information
4. Present the product bundle with a professional tone, highlighting key benefits and features
5. Clearly state the price with any applicable terms (validity period, payment terms)
6. Include a section about PT Chitra Paratama's quality assurance and after-sales support
7. Add a clear call-to-action for next steps (schedule a meeting, request a formal quotation, etc.)
8. End with a professional closing and your contact information

The email should be formal but warm, professional but not cold, and persuasive without being pushy. Use proper business Indonesian language throughout.`
        }
      ]
    };

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://bundleboost.app',
          'X-Title': 'Chitra Marketing Tools'
        }
      }
    );

    console.log('OpenRouter API response:', JSON.stringify(response.data, null, 2));

    // Check for different possible response formats
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      // Standard OpenAI/OpenRouter format
      if (response.data.choices[0].message && response.data.choices[0].message.content) {
        const content = response.data.choices[0].message.content;
        if (typeof content === 'string') {
          return content;
        } else if (Array.isArray(content)) {
          return content
            .filter((part: any) => part.type === 'text')
            .map((part: any) => part.text)
            .join('\n');
        }
      }

      // Alternative formats
      if (response.data.choices[0].content) {
        if (typeof response.data.choices[0].content === 'string') {
          return response.data.choices[0].content;
        } else if (response.data.choices[0].content.parts) {
          return response.data.choices[0].content.parts
            .filter((part: any) => part.text)
            .map((part: any) => part.text)
            .join('\n');
        }
      }

      if (response.data.choices[0].text) {
        return response.data.choices[0].text;
      }
    }

    console.error('Unexpected response format:', response.data);
    return 'Failed to generate email offer. Unexpected response format.';
  } catch (error) {
    console.error('Error generating email offer with OpenRouter:', error);

    // More detailed error logging
    if (error.response) {
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
      return `Failed to generate email offer. Server error: ${error.response.status} - ${JSON.stringify(error.response.data)}`;
    } else if (error.request) {
      console.error('Error request:', error.request);
      return 'Failed to generate email offer. No response received from server.';
    } else {
      console.error('Error message:', error.message);
      return `Failed to generate email offer. Error: ${error.message}`;
    }
  }
}
