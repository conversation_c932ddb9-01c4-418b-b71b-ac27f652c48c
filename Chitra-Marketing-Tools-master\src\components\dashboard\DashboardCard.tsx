import React, { ReactNode } from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '../../lib/utils';
import { Card, CardHeader, CardContent, CardFooter, CardTitle } from '../ui/card';

interface DashboardCardProps {
  title: string;
  icon?: LucideIcon;
  children: ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  footerClassName?: string;
  footer?: ReactNode;
  variant?: 'default' | 'outline' | 'filled';
  actions?: ReactNode;
}

/**
 * DashboardCard component for displaying content in dashboard layouts
 *
 * @example
 * <DashboardCard
 *   title="Sales Overview"
 *   icon={BarChart}
 *   footer={<Button>View Details</Button>}
 * >
 *   Card content here
 * </DashboardCard>
 */
export default function DashboardCard({
  title,
  icon: Icon,
  children,
  className = "",
  headerClassName = "",
  contentClassName = "",
  footerClassName = "",
  footer,
  variant = "default",
  actions
}: DashboardCardProps) {
  return (
    <Card
      variant={variant}
      className={cn("overflow-hidden", className)}
    >
      <CardHeader className={cn("px-4 py-3 flex-row items-center justify-between space-y-0", headerClassName)}>
        <div className="flex items-center">
          {Icon && <Icon className="h-5 w-5 text-primary-500 mr-2" />}
          <CardTitle className="text-base font-medium">{title}</CardTitle>
        </div>
        {actions && (
          <div className="flex items-center">
            {actions}
          </div>
        )}
      </CardHeader>
      <CardContent className={cn("p-4", contentClassName)}>
        {children}
      </CardContent>
      {footer && (
        <CardFooter className={cn("px-4 py-2 bg-gray-50 border-t text-sm text-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400", footerClassName)}>
          {footer}
        </CardFooter>
      )}
    </Card>
  );
}
