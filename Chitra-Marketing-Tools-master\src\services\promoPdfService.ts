import { jsPDF } from 'jspdf';
import {
  PromoItem,
  PromoResult,
  PromoConfig,
  PromoDiscount,
  DiscountType
} from '../types/promotion';
import { Customer } from '../types';
import { calculateTotalSellingPrice } from '../utils/promoCalculations';

/**
 * Format price for PDF display to prevent overlapping
 * For large numbers, use shorter notation (e.g., "Rp 1.5 Jt" for 1,500,000)
 */
function formatPriceForPDF(price: number): string {
  if (price >= 1000000000) {
    // Format as billions (milyar)
    return `Rp ${(price / 1000000000).toFixed(1)} M`;
  } else if (price >= 1000000) {
    // Format as millions (juta)
    return `Rp ${(price / 1000000).toFixed(1)} Jt`;
  } else if (price >= 1000) {
    // Format as thousands (ribu)
    return `Rp ${(price / 1000).toFixed(1)} Rb`;
  } else {
    // Format normally
    return `Rp ${price.toLocaleString()}`;
  }
}

/**
 * Generate a PDF quotation for a promotion
 */
export function generatePromoPDF(
  items: PromoItem[],
  result: PromoResult,
  config: PromoConfig,
  discount: PromoDiscount,
  customer?: Customer | null
): void {
  try {
    console.log('Generating promotion PDF quotation...');

    // Create a new PDF document
    const doc = new jsPDF();

    // Add header with company info and quotation title
    addHeader(doc, config);

    // Add company and customer information
    addCompanyAndCustomerInfo(doc, customer);

    // Add promotion details
    addPromotionDetails(doc, config);

    // Add product table
    addProductTable(doc, items, discount);

    // Add pricing summary
    addPricingSummary(doc, result, calculateTotalSellingPrice(items));

    // Add notes and terms
    addNotesAndTerms(doc, config);

    // Add footer
    addFooter(doc);

    // Save the PDF with customer name if available
    const filename = customer
      ? `Chitra-Promo-${customer.name.replace(/\s+/g, '_')}.pdf`
      : `Chitra-Promo-${config.name.replace(/\s+/g, '_')}.pdf`;

    doc.save(filename);
    console.log(`Promotion PDF saved successfully as ${filename}`);

    // Show success message with customer name if available
    const successMessage = customer
      ? `Penawaran promo untuk ${customer.name} berhasil dibuat!`
      : 'Penawaran promo berhasil dibuat!';

    alert(successMessage);
  } catch (error) {
    console.error('Error generating promotion PDF:', error);
    alert('Gagal membuat PDF. Silakan periksa konsol untuk detail.');
  }
}

function addHeader(doc: jsPDF, config: PromoConfig): void {
  try {
    // Add company logo from file
    const logoPath = '/assets/cp_logo.png';
    doc.addImage(logoPath, 'PNG', 20, 15, 45, 15);
  } catch (error) {
    console.error('Error adding logo:', error);

    // Fallback if logo loading fails - just show a colored rectangle
    doc.setDrawColor(0, 120, 200);
    doc.setFillColor(0, 120, 200);
    doc.rect(20, 15, 30, 15, 'F');
  }

  // Add PENAWARAN PROMO title on the right
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(80, 80, 200); // Purple-blue color
  doc.setFontSize(18);
  doc.text('PENAWARAN PROMO', 200, 22, { align: 'right' });

  // Add promo name
  doc.setFontSize(12);
  doc.text(config.name, 200, 28, { align: 'right' });

  // Add horizontal line
  doc.setDrawColor(220, 220, 220);
  doc.setLineWidth(0.5);
  doc.line(20, 35, 190, 35);
}

function addCompanyAndCustomerInfo(doc: jsPDF, customer?: Customer | null): void {
  // Company information (left side)
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(11);
  doc.text('PT Chitra Paratama', 25, 45);

  // Customer information (right side) with "To" label
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(11);
  doc.text('Kepada', 140, 45);

  if (customer) {
    doc.setFont('helvetica', 'bold');
    // Wrap customer company/name if too long
    const customerName = customer.company || customer.name;
    if (customerName.length > 30) {
      const lines = doc.splitTextToSize(customerName, 50);
      doc.text(lines, 140, 50);

      // Adjust position for address based on number of name lines
      const addressY = 50 + (lines.length * 5);

      doc.setFont('helvetica', 'normal');
      doc.setFontSize(9);

      // Wrap address if provided
      if (customer.address && customer.address.trim() !== '') {
        const addressLines = doc.splitTextToSize(customer.address, 50);
        doc.text(addressLines, 140, addressY);

        // Adjust position for contact info based on number of address lines
        const contactY = addressY + (addressLines.length * 5);
        doc.text(`${customer.email || ''} ${customer.phone ? '| ' + customer.phone : ''}`, 140, contactY);
      } else {
        doc.text(`${customer.email || ''} ${customer.phone ? '| ' + customer.phone : ''}`, 140, addressY);
      }
    } else {
      doc.text(customerName, 140, 50);

      doc.setFont('helvetica', 'normal');
      doc.setFontSize(9);

      // Wrap address if provided
      if (customer.address && customer.address.trim() !== '') {
        const addressLines = doc.splitTextToSize(customer.address, 50);
        doc.text(addressLines, 140, 55);

        // Adjust position for contact info based on number of address lines
        const contactY = 55 + (addressLines.length * 5);
        doc.text(`${customer.email || ''} ${customer.phone ? '| ' + customer.phone : ''}`, 140, contactY);
      } else {
        doc.text(`${customer.email || ''} ${customer.phone ? '| ' + customer.phone : ''}`, 140, 55);
      }
    }
  } else {
    doc.setFont('helvetica', 'normal');
    doc.text('', 140, 50); // Empty string instead of "Customer details not provided"
  }
}

function addPromotionDetails(doc: jsPDF, config: PromoConfig): void {
  // Promotion details (left side)
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('Tanggal Mulai:', 25, 70);
  doc.text('Tanggal Berakhir:', 25, 75);
  doc.text('Wilayah:', 25, 80);

  doc.setFont('helvetica', 'normal');

  // Format dates
  const startDate = new Date(config.startDate).toLocaleDateString('id-ID', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  });

  const endDate = new Date(config.endDate).toLocaleDateString('id-ID', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  });

  doc.text(startDate, 70, 70);
  doc.text(endDate, 70, 75);
  doc.text(config.region || 'Semua Wilayah', 70, 80);
}

function addProductTable(doc: jsPDF, items: PromoItem[], discount: PromoDiscount): void {
  // Set starting position
  const startY = 90;
  let currentY = startY;

  // Add table header
  doc.setFillColor(65, 105, 225); // Royal blue
  doc.setDrawColor(65, 105, 225);
  doc.rect(20, currentY, 170, 10, 'F');

  // Add header text
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(10);
  doc.text('#', 25, currentY + 6);
  doc.text('Produk', 40, currentY + 6);
  doc.text('Qty', 120, currentY + 6);
  doc.text('Harga Normal', 135, currentY + 6);
  doc.text('Harga Promo', 180, currentY + 6, { align: 'right' });

  currentY += 10;

  // Add table rows
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(9);

  let isAlternateRow = false;

  items.forEach((item, index) => {
    // Set alternate row background
    if (isAlternateRow) {
      doc.setFillColor(245, 245, 255);
      doc.setDrawColor(245, 245, 255);
      doc.rect(20, currentY, 170, 10, 'F');
    }

    // Add row content
    doc.text((index + 1).toString(), 25, currentY + 6);

    // Item description (may need to be wrapped)
    const description = item.product.materialDescription;
    const maxWidth = 70; // Maximum width for description text

    if (description.length > 40) {
      const lines = doc.splitTextToSize(description, maxWidth);
      doc.text(lines, 40, currentY + 4);

      // Adjust row height based on number of lines
      if (lines.length > 2) {
        currentY += (lines.length - 2) * 4; // Add extra height for additional lines
      }
    } else {
      doc.text(description, 40, currentY + 6);
    }

    // Part number in smaller text
    doc.setFontSize(8);
    doc.text(`Part Number: ${item.product.oldMaterialNo}`, 40, currentY + 12);
    doc.setFontSize(9);

    // Quantity
    doc.text(item.quantity.toString(), 120, currentY + 6);

    // Normal price - format with shorter notation for large numbers
    const formattedNormalPrice = formatPriceForPDF(item.product.sellingPrice);
    doc.text(formattedNormalPrice, 135, currentY + 6);

    // Promo price (calculated based on discount type)
    let promoPrice = item.product.sellingPrice;
    if (discount.type === DiscountType.PERCENTAGE) {
      promoPrice = item.product.sellingPrice * (1 - discount.value / 100);
    } else if (discount.type === DiscountType.FIXED_AMOUNT) {
      // Distribute discount proportionally
      const totalPrice = items.reduce((sum, i) => sum + (i.product.sellingPrice * i.quantity), 0);
      const discountRatio = Math.min(1, discount.value / totalPrice);
      promoPrice = item.product.sellingPrice * (1 - discountRatio);
    }

    // Format promo price with shorter notation
    const formattedPromoPrice = formatPriceForPDF(Math.round(promoPrice));
    doc.text(formattedPromoPrice, 180, currentY + 6, { align: 'right' });

    // Move to next row
    currentY += 15;
    isAlternateRow = !isAlternateRow;
  });

  // Add special note for cashback or bonus unit
  if (discount.type === DiscountType.CASHBACK || discount.type === DiscountType.BONUS_UNIT) {
    doc.setFillColor(255, 250, 230);
    doc.setDrawColor(255, 250, 230);
    doc.rect(20, currentY, 170, 15, 'F');

    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 0, 0);

    if (discount.type === DiscountType.CASHBACK) {
      doc.text(`* Cashback sebesar Rp${discount.value.toLocaleString()} akan diberikan setelah pembelian`, 25, currentY + 10);
    } else if (discount.type === DiscountType.BONUS_UNIT) {
      doc.text(`* Bonus ${discount.value} unit gratis untuk setiap pembelian ${items.reduce((sum, i) => sum + i.quantity, 0)} unit`, 25, currentY + 10);
    }

    currentY += 20;
  }

  // Add table border
  doc.setDrawColor(200, 200, 200);
  doc.setLineWidth(0.1);
  doc.rect(20, startY, 170, currentY - startY - (discount.type === DiscountType.CASHBACK || discount.type === DiscountType.BONUS_UNIT ? 5 : 0), 'S');

  // Store the final Y position for later use
  (doc as any).lastTableEndY = currentY;
}

function addPricingSummary(doc: jsPDF, result: PromoResult, totalNormalPrice: number): void {
  // Get the final Y position after the table
  const finalY = (doc as any).lastTableEndY + 5;

  // Add pricing summary
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  doc.text('Harga Normal', 130, finalY + 5);
  doc.text('Total Diskon', 130, finalY + 12);
  doc.text('Harga Promo', 130, finalY + 19);

  // Add the amounts with better formatting
  doc.setFont('helvetica', 'normal');
  doc.text(formatPriceForPDF(totalNormalPrice), 190, finalY + 5, { align: 'right' });
  doc.setTextColor(255, 0, 0); // Red for discount
  doc.text(formatPriceForPDF(result.totalDiscount), 190, finalY + 12, { align: 'right' });
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'bold');
  doc.text(formatPriceForPDF(result.priceAfterPromo), 190, finalY + 19, { align: 'right' });

  // Add a light gray background for the totals section
  doc.setFillColor(245, 245, 245);
  doc.rect(120, finalY, 70, 22, 'F');

  // Re-add the text on top of the background
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(0, 0, 0);
  doc.text('Harga Normal', 130, finalY + 5);
  doc.text('Total Diskon', 130, finalY + 12);
  doc.text('Harga Promo', 130, finalY + 19);

  doc.setFont('helvetica', 'normal');
  doc.text(formatPriceForPDF(totalNormalPrice), 190, finalY + 5, { align: 'right' });
  doc.setTextColor(255, 0, 0); // Red for discount
  doc.text(formatPriceForPDF(result.totalDiscount), 190, finalY + 12, { align: 'right' });
  doc.setTextColor(0, 0, 0);
  doc.setFont('helvetica', 'bold');
  doc.text(formatPriceForPDF(result.priceAfterPromo), 190, finalY + 19, { align: 'right' });

  // Store the final Y position for later use
  (doc as any).lastTotalsEndY = finalY + 25;
}

function addNotesAndTerms(doc: jsPDF, config: PromoConfig): void {
  // Get the final Y position after the totals
  const finalY = (doc as any).lastTotalsEndY + 10;

  // Add Notes
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('Catatan:', 25, finalY);

  doc.setFont('helvetica', 'normal');

  let notesHeight = 0;
  if (config.additionalCosts.description && config.additionalCosts.description.trim() !== '') {
    const lines = doc.splitTextToSize(config.additionalCosts.description, 150);
    doc.text(lines, 25, finalY + 7);
    notesHeight = lines.length * 5; // Estimate height based on number of lines
  } else {
    doc.text('Promo ini berlaku untuk pembelian dalam periode yang ditentukan.', 25, finalY + 7);
    notesHeight = 5; // Single line
  }

  // Add Terms & Conditions
  // Calculate position based on actual notes height
  const termsY = finalY + 7 + notesHeight + 10; // Add extra spacing

  doc.setFont('helvetica', 'bold');
  doc.text('Syarat & Ketentuan:', 25, termsY);

  doc.setFont('helvetica', 'normal');
  doc.text('1. Harga belum termasuk pajak', 25, termsY + 7);
  doc.text('2. Promo tidak dapat digabungkan dengan promo lainnya', 25, termsY + 14);
  doc.text('3. Pembayaran: 30 hari setelah tanggal faktur', 25, termsY + 21);
  doc.text('4. Pengiriman: DDP Site', 25, termsY + 28);

  // Add horizontal line
  const lineY = termsY + 35;
  doc.setDrawColor(100, 100, 100);
  doc.setLineWidth(0.5);
  doc.line(25, lineY, 190, lineY);

  // Store the final Y position for later use
  (doc as any).lastTermsEndY = lineY;
}

function addFooter(doc: jsPDF): void {
  // Get the final Y position after the terms
  const finalY = (doc as any).lastTermsEndY + 10;

  // Add company details in footer
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('PT. CHITRA PARATAMA', 25, finalY);
  doc.text('BANK MANDIRI', 25, finalY + 5);

  doc.setFont('helvetica', 'normal');
  doc.text('IDR A/C NO:127 - 000 - 00 - 17416', 25, finalY + 10);

  // Add horizontal line
  doc.setDrawColor(100, 100, 100);
  doc.setLineWidth(0.5);
  doc.line(25, finalY + 20, 190, finalY + 20);
}
