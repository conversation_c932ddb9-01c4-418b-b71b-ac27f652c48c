import React from 'react';
import { LucideIcon, TrendingUp, TrendingDown, ArrowRight } from 'lucide-react';
import { cn } from '../../lib/utils';
import { Card } from '../ui/card';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description?: string;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  className?: string;
  variant?: 'default' | 'primary' | 'secondary' | 'accent' | 'outline';
}

/**
 * StatCard component for displaying statistics in dashboard layouts
 *
 * @example
 * <StatCard
 *   title="Total Users"
 *   value={1234}
 *   icon={Users}
 *   description="Active users"
 *   trend="up"
 *   trendValue="12% vs last month"
 * />
 */
export default function StatCard({
  title,
  value,
  icon: Icon,
  description,
  trend,
  trendValue,
  className = "",
  variant = "default"
}: StatCardProps) {
  // Get trend color based on direction
  const getTrendColor = () => {
    if (!trend) return '';
    return trend === 'up'
      ? 'text-success-600 dark:text-success-500'
      : trend === 'down'
        ? 'text-red-600 dark:text-red-500'
        : 'text-gray-600 dark:text-gray-400';
  };

  // Get trend icon component based on direction
  const TrendIcon = trend === 'up'
    ? TrendingUp
    : trend === 'down'
      ? TrendingDown
      : ArrowRight;

  // Get icon background color based on variant
  const getIconBgColor = () => {
    switch (variant) {
      case 'primary':
        return 'bg-primary-100 text-primary-600 dark:bg-primary-900 dark:text-primary-300';
      case 'secondary':
        return 'bg-secondary-100 text-secondary-600 dark:bg-secondary-900 dark:text-secondary-300';
      case 'accent':
        return 'bg-accent-100 text-accent-600 dark:bg-accent-900 dark:text-accent-300';
      case 'outline':
        return 'bg-transparent border border-gray-200 text-gray-600 dark:border-gray-700 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  return (
    <Card className={cn("p-4 h-full", className)}>
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</div>
        <div className={cn("p-2 rounded-full", getIconBgColor())}>
          <Icon className="h-5 w-5" />
        </div>
      </div>
      <div className="mt-2">
        <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">{value}</div>
        {description && (
          <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">{description}</div>
        )}
        {trend && trendValue && (
          <div className={cn("text-sm mt-2 flex items-center", getTrendColor())}>
            <TrendIcon className="h-4 w-4 mr-1" />
            <span>{trendValue}</span>
          </div>
        )}
      </div>
    </Card>
  );
}
