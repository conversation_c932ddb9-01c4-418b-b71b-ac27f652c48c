import { User as FirebaseUser } from 'firebase/auth';
import { firebaseAuth } from './firebase';

// Interface for our application user
interface User {
  username: string;
  email: string;
  isLoggedIn: boolean;
}

// For backward compatibility and fallback
const validCredentials = {
  username: '<PERSON><PERSON>',
  password: 'chitra 2024'
};

export const authService = {
  login: async (username: string, password: string): Promise<boolean> => {
    try {
      // Try Firebase authentication first
      // Assuming username is an email for Firebase
      const user = await firebaseAuth.login(username, password);

      if (user) {
        // Store user info in localStorage for app state
        localStorage.setItem('bundleBoostUser', JSON.stringify({
          username: user.displayName || username,
          email: user.email,
          isLoggedIn: true
        }));
        return true;
      }
      return false;
    } catch (error) {
      console.error('Firebase login error:', error);

      // Fallback to local authentication if Firebase fails
      if (username === validCredentials.username && password === validCredentials.password) {
        localStorage.setItem('bundleBoostUser', JSON.stringify({
          username,
          email: '<EMAIL>',
          isLoggedIn: true
        }));
        return true;
      }
      return false;
    }
  },

  logout: async (): Promise<void> => {
    try {
      // Sign out from Firebase
      await firebaseAuth.logout();
    } catch (error) {
      console.error('Firebase logout error:', error);
    } finally {
      // Always remove from localStorage
      localStorage.removeItem('bundleBoostUser');
    }
  },

  getCurrentUser: (): User | null => {
    // First check Firebase current user
    const firebaseUser = firebaseAuth.getCurrentUser();

    if (firebaseUser) {
      return {
        username: firebaseUser.displayName || firebaseUser.email || 'User',
        email: firebaseUser.email || '',
        isLoggedIn: true
      };
    }

    // Fallback to localStorage
    const userJson = localStorage.getItem('bundleBoostUser');
    if (!userJson) return null;

    try {
      return JSON.parse(userJson) as User;
    } catch (e) {
      console.error('Error parsing user data:', e);
      return null;
    }
  },

  isAuthenticated: (): boolean => {
    // Check Firebase authentication first
    if (firebaseAuth.getCurrentUser()) {
      return true;
    }

    // Fallback to localStorage
    const user = authService.getCurrentUser();
    return !!user?.isLoggedIn;
  }
};
