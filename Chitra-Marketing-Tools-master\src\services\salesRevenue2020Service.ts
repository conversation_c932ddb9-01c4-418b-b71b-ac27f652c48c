import axios from 'axios';

// NocoDB API configuration
const NOCODB_API_URL = 'https://app.nocodb.com/api/v2/tables/mvjrzjplqifxpjz/records';
const NOCODB_API_TOKEN = 'ujoiK3hVpHgOiREwdXbCMMMGoL_7BzzODH8Ad5Fk';

// Fallback data for testing when API is not available
export const FALLBACK_DATA: SalesRevenueItem[] = [
  {
    id: '1',
    customerName: 'PT Mining Sejahtera',
    salesman: '<PERSON>',
    materialDescription: 'Heavy Duty Tire 27.00R49',
    matGrpDesc: 'Mining Tires',
    matGrp1Desc: 'Tires',
    matGrp3Desc: 'Heavy Equipment',
    matGrp5Desc: 'Mining',
    revenueInDocCurr: 250000000,
    revenueInLocCurr: 250000000,
    billingDate: '2023-01-15',
    createdAt: '2023-01-15T08:30:00Z',
    updatedAt: '2023-01-15T08:30:00Z'
  },
  {
    id: '2',
    customerName: 'PT Coal Mining Indonesia',
    salesman: '<PERSON>',
    materialDescription: 'Medium Duty Tire 24.00R35',
    matGrpDesc: 'Mining Tires',
    matGrp1Desc: 'Tires',
    matGrp3Desc: 'Medium Equipment',
    matGrp5Desc: 'Mining',
    revenueInDocCurr: 180000000,
    revenueInLocCurr: 180000000,
    billingDate: '2023-02-20',
    createdAt: '2023-02-20T10:15:00Z',
    updatedAt: '2023-02-20T10:15:00Z'
  },
  {
    id: '3',
    customerName: 'PT Tambang Batubara',
    salesman: 'Michael Johnson',
    materialDescription: 'Light Duty Tire 20.00R25',
    matGrpDesc: 'Construction Tires',
    matGrp1Desc: 'Tires',
    matGrp3Desc: 'Light Equipment',
    matGrp5Desc: 'Construction',
    revenueInDocCurr: 120000000,
    revenueInLocCurr: 120000000,
    billingDate: '2023-03-10',
    createdAt: '2023-03-10T09:45:00Z',
    updatedAt: '2023-03-10T09:45:00Z'
  },
  {
    id: '4',
    customerName: 'PT Mineral Resources',
    salesman: 'Sarah Williams',
    materialDescription: 'Ultra Heavy Duty Tire 33.00R51',
    matGrpDesc: 'Mining Tires',
    matGrp1Desc: 'Tires',
    matGrp3Desc: 'Ultra Heavy Equipment',
    matGrp5Desc: 'Mining',
    revenueInDocCurr: 350000000,
    revenueInLocCurr: 350000000,
    billingDate: '2023-04-05',
    createdAt: '2023-04-05T11:20:00Z',
    updatedAt: '2023-04-05T11:20:00Z'
  },
  {
    id: '5',
    customerName: 'PT Coal Mining Indonesia',
    salesman: 'Jane Smith',
    materialDescription: 'Heavy Duty Tire 27.00R49',
    matGrpDesc: 'Mining Tires',
    matGrp1Desc: 'Tires',
    matGrp3Desc: 'Heavy Equipment',
    matGrp5Desc: 'Mining',
    revenueInDocCurr: 240000000,
    revenueInLocCurr: 240000000,
    billingDate: '2023-05-12',
    createdAt: '2023-05-12T09:10:00Z',
    updatedAt: '2023-05-12T09:10:00Z'
  },
  {
    id: '6',
    customerName: 'PT Konstruksi Jaya',
    salesman: 'Robert Brown',
    materialDescription: 'Medium Duty Tire 20.5R25',
    matGrpDesc: 'Construction Tires',
    matGrp1Desc: 'Tires',
    matGrp3Desc: 'Medium Equipment',
    matGrp5Desc: 'Construction',
    revenueInDocCurr: 150000000,
    revenueInLocCurr: 150000000,
    billingDate: '2023-06-18',
    createdAt: '2023-06-18T14:30:00Z',
    updatedAt: '2023-06-18T14:30:00Z'
  }
];

// Interface for sales revenue item
export interface SalesRevenueItem {
  id: string;
  [key: string]: any; // Allow for dynamic properties
  customerName?: string;
  salesman?: string;
  materialDescription?: string;
  matGrpDesc?: string;
  matGrp1Desc?: string;
  matGrp3Desc?: string;
  matGrp5Desc?: string;
  revenueInDocCurr?: number;
  revenueInLocCurr?: number;
  billingDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Interface for sales revenue summary
export interface SalesRevenueSummary {
  totalRevenue: number;
  totalCustomers: number;
  totalSalesmen: number;
  topCustomers: { customer: string; revenue: number }[];
  topSalesmen: { salesman: string; revenue: number }[];
  materialGroupDistribution: { group: string; count: number; revenue: number }[];
  monthlyRevenue: { month: string; revenue: number }[];
}

// Interface for pagination info
export interface PaginationInfo {
  totalItems: number;
  page: number;
  pageSize: number;
  totalPages: number;
  isFirstPage: boolean;
  isLastPage: boolean;
}

/**
 * Fetches sales revenue data from NocoDB API with pagination
 */
export const fetchSalesRevenueData = async (
  offset: number = 0,
  limit: number = 25,
  where: string = '',
  sort: string = ''
): Promise<{ list: SalesRevenueItem[], pageInfo: PaginationInfo }> => {
  try {
    console.log(`Fetching sales revenue data from NocoDB API (offset: ${offset}, limit: ${limit})...`);
    console.log(`API URL: ${NOCODB_API_URL}`);
    console.log(`API Token: ${NOCODB_API_TOKEN.substring(0, 5)}...`);
    console.log(`Where condition: ${where}`);
    console.log(`Sort: ${sort}`);

    // Create request configuration
    const config = {
      params: {
        offset,
        limit,
        where,
        sort
      },
      headers: {
        'xc-token': NOCODB_API_TOKEN,
        'Accept': 'application/json'
      }
    };

    console.log('Request config:', JSON.stringify(config, null, 2));

    // Make the API request
    const response = await axios.get(NOCODB_API_URL, config);

    console.log('NocoDB API response status:', response.status);
    console.log('Response headers:', response.headers);

    if (response.status !== 200) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    // Log the full response for debugging
    console.log('Full response data:', JSON.stringify(response.data, null, 2));

    // Extract data and pagination info
    const data = response.data.list || [];
    const pageInfo = response.data.pageInfo || {
      totalRows: 0
    };

    console.log(`Successfully fetched ${data.length} sales revenue records`);

    if (data.length > 0) {
      console.log('Sample data:', data[0]);
    } else {
      console.warn('No data returned from API');
    }

    // Format pagination info
    const paginationInfo: PaginationInfo = {
      totalItems: pageInfo.totalRows || 0,
      page: Math.floor(offset / limit) + 1,
      pageSize: limit,
      totalPages: Math.ceil((pageInfo.totalRows || 0) / limit) || 1, // Ensure at least 1 page
      isFirstPage: offset === 0,
      isLastPage: offset + limit >= (pageInfo.totalRows || 0)
    };

    console.log('Pagination info:', paginationInfo);

    return {
      list: data,
      pageInfo: paginationInfo
    };
  } catch (error) {
    console.error('Error fetching sales revenue data:', error);
    if (axios.isAxiosError(error)) {
      console.error('Axios error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
    }

    // Return fallback data with default pagination instead of throwing
    console.log('Using fallback data due to API error');
    return {
      list: FALLBACK_DATA,
      pageInfo: {
        totalItems: FALLBACK_DATA.length,
        page: 1,
        pageSize: limit,
        totalPages: Math.ceil(FALLBACK_DATA.length / limit),
        isFirstPage: true,
        isLastPage: FALLBACK_DATA.length <= limit
      }
    };
  }
};

/**
 * Creates a new sales revenue record in NocoDB
 */
export const createSalesRevenueRecord = async (record: Omit<SalesRevenueItem, 'id'>): Promise<SalesRevenueItem> => {
  try {
    console.log('Creating new sales revenue record:', record);

    const response = await axios.post(`${NOCODB_API_URL}`, record, {
      headers: {
        'xc-token': NOCODB_API_TOKEN,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (response.status !== 200 && response.status !== 201) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    console.log('Record created successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error creating sales revenue record:', error);
    throw error;
  }
};

/**
 * Updates an existing sales revenue record in NocoDB
 */
export const updateSalesRevenueRecord = async (id: string, record: Partial<SalesRevenueItem>): Promise<SalesRevenueItem> => {
  try {
    console.log(`Updating sales revenue record ${id}:`, record);

    const response = await axios.patch(`${NOCODB_API_URL}/${id}`, record, {
      headers: {
        'xc-token': NOCODB_API_TOKEN,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (response.status !== 200) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    console.log('Record updated successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error(`Error updating sales revenue record ${id}:`, error);
    throw error;
  }
};

/**
 * Deletes a sales revenue record from NocoDB
 */
export const deleteSalesRevenueRecord = async (id: string): Promise<boolean> => {
  try {
    console.log(`Deleting sales revenue record ${id}`);

    const response = await axios.delete(`${NOCODB_API_URL}/${id}`, {
      headers: {
        'xc-token': NOCODB_API_TOKEN,
        'Accept': 'application/json'
      }
    });

    if (response.status !== 200 && response.status !== 204) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    console.log('Record deleted successfully');
    return true;
  } catch (error) {
    console.error(`Error deleting sales revenue record ${id}:`, error);
    throw error;
  }
};

/**
 * Generate summary data from sales revenue items
 */
export const generateSalesRevenueSummary = (data: SalesRevenueItem[]): SalesRevenueSummary => {
  // Calculate total revenue (using document currency)
  const totalRevenue = data.reduce((sum, item) => sum + (item.revenueInDocCurr || 0), 0);

  // Count unique customers
  const uniqueCustomers = new Set(data.map(item => item.customerName).filter(Boolean));

  // Count unique salesmen
  const uniqueSalesmen = new Set(data.map(item => item.salesman).filter(Boolean));

  // Get top customers by revenue
  const customerRevenue: { [key: string]: number } = {};
  data.forEach(item => {
    if (item.customerName) {
      if (!customerRevenue[item.customerName]) {
        customerRevenue[item.customerName] = 0;
      }
      customerRevenue[item.customerName] += (item.revenueInDocCurr || 0);
    }
  });

  const topCustomers = Object.entries(customerRevenue)
    .map(([customer, revenue]) => ({ customer, revenue }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  // Get top salesmen by revenue
  const salesmanRevenue: { [key: string]: number } = {};
  data.forEach(item => {
    if (item.salesman) {
      if (!salesmanRevenue[item.salesman]) {
        salesmanRevenue[item.salesman] = 0;
      }
      salesmanRevenue[item.salesman] += (item.revenueInDocCurr || 0);
    }
  });

  const topSalesmen = Object.entries(salesmanRevenue)
    .map(([salesman, revenue]) => ({ salesman, revenue }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  // Get material group distribution
  const materialGroupData: { [key: string]: { count: number; revenue: number } } = {};
  data.forEach(item => {
    if (item.matGrpDesc) {
      if (!materialGroupData[item.matGrpDesc]) {
        materialGroupData[item.matGrpDesc] = { count: 0, revenue: 0 };
      }
      materialGroupData[item.matGrpDesc].count += 1;
      materialGroupData[item.matGrpDesc].revenue += (item.revenueInDocCurr || 0);
    }
  });

  const materialGroupDistribution = Object.entries(materialGroupData)
    .map(([group, { count, revenue }]) => ({ group, count, revenue }))
    .sort((a, b) => b.revenue - a.revenue);

  // Get monthly revenue
  const monthlyRevenueMap: { [key: string]: number } = {};
  data.forEach(item => {
    if (item.billingDate) {
      // Extract month from billing date (assuming format YYYY-MM-DD)
      let month = 'Unknown';
      try {
        const date = new Date(item.billingDate);
        month = date.toLocaleString('default', { month: 'long', year: 'numeric' });
      } catch (e) {
        // If date parsing fails, use the raw value
        month = item.billingDate.substring(0, 7); // Get YYYY-MM part
      }

      if (!monthlyRevenueMap[month]) {
        monthlyRevenueMap[month] = 0;
      }
      monthlyRevenueMap[month] += (item.revenueInDocCurr || 0);
    }
  });

  const monthlyRevenue = Object.entries(monthlyRevenueMap)
    .map(([month, revenue]) => ({ month, revenue }))
    .sort((a, b) => {
      // Try to sort by date if possible
      const aDate = new Date(a.month);
      const bDate = new Date(b.month);
      if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
        return aDate.getTime() - bDate.getTime();
      }
      return a.month.localeCompare(b.month);
    });

  return {
    totalRevenue,
    totalCustomers: uniqueCustomers.size,
    totalSalesmen: uniqueSalesmen.size,
    topCustomers,
    topSalesmen,
    materialGroupDistribution,
    monthlyRevenue
  };
};
