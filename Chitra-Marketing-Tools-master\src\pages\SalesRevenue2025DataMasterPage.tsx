import React, { useState, useEffect, useRef } from 'react';
import {
  DollarSign, Search, ChevronLeft, ChevronRight, RefreshCw, Filter,
  Download, Upload, Plus, Edit, Trash2, X, Save, Calendar, User, Package, Hash
} from 'lucide-react';
import {
  SalesRevenueItem,
  SalesRevenueSummary,
  loadSalesRevenueData,
  generateSalesRevenueSummary,
  createSalesRevenueItem,
  updateSalesRevenueItem,
  deleteSalesRevenueItem,
  importSalesRevenueData,
  exportSalesRevenueData,
  saveSalesRevenueData
} from '../services/salesRevenue2025Service';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';

// Field mapping modal component
interface FieldMappingModalProps {
  isOpen: boolean;
  onClose: () => void;
  availableFields: string[];
  fieldMapping: Record<string, string>;
  setFieldMapping: (mapping: Record<string, string>) => void;
  onConfirm: () => void;
  sampleData: Record<string, any> | null;
}

const FieldMappingModal: React.FC<FieldMappingModalProps> = ({
  isOpen,
  onClose,
  availableFields,
  fieldMapping,
  setFieldMapping,
  onConfirm,
  sampleData
}) => {
  if (!isOpen) return null;

  const requiredFields = [
    { key: 'customerName', label: 'Customer Name' },
    { key: 'salesman', label: 'Salesman' },
    { key: 'materialDescription', label: 'Material Description' },
    { key: 'qty', label: 'Quantity' },
    { key: 'revenueInDocCurr', label: 'Revenue in Doc Curr' },
    { key: 'billingDate', label: 'Billing Date' },
    { key: 'poDate', label: 'PO Date' }
  ];

  const handleFieldChange = (fieldKey: string, mappedField: string) => {
    setFieldMapping({
      ...fieldMapping,
      [fieldKey]: mappedField
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Map Fields</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        <p className="mb-4 text-gray-600">
          Please map the fields from your imported file to the required fields in our system.
        </p>

        {sampleData && (
          <div className="mb-4 p-3 bg-gray-50 rounded-md">
            <h3 className="font-medium mb-2">Sample Data Preview:</h3>
            <div className="text-sm overflow-x-auto">
              <pre>{JSON.stringify(sampleData, null, 2)}</pre>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {requiredFields.map(field => (
            <div key={field.key} className="grid grid-cols-3 gap-4 items-center">
              <div>
                <label className="font-medium">{field.label}:</label>
              </div>
              <div className="col-span-2">
                <select
                  className="w-full p-2 border rounded-md"
                  value={fieldMapping[field.key] || ''}
                  onChange={(e) => handleFieldChange(field.key, e.target.value)}
                >
                  <option value="">-- Select Field --</option>
                  {availableFields.map(field => (
                    <option key={field} value={field}>
                      {field}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Import Data
          </button>
        </div>
      </div>
    </div>
  );
};

// Sales Revenue Item Form component
interface SalesRevenueFormProps {
  item: Partial<SalesRevenueItem>;
  onSubmit: (item: Partial<SalesRevenueItem>) => void;
  onCancel: () => void;
  isNew: boolean;
}

const SalesRevenueForm: React.FC<SalesRevenueFormProps> = ({
  item,
  onSubmit,
  onCancel,
  isNew
}) => {
  const [formData, setFormData] = useState<Partial<SalesRevenueItem>>(item);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;

    // Handle different input types
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: value === '' ? 0 : Number(value)
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">
        {isNew ? 'Add New Sales Revenue Item' : 'Edit Sales Revenue Item'}
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Customer Name
          </label>
          <input
            type="text"
            name="customerName"
            value={formData.customerName || ''}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Salesman
          </label>
          <input
            type="text"
            name="salesman"
            value={formData.salesman || ''}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Material Description
          </label>
          <input
            type="text"
            name="materialDescription"
            value={formData.materialDescription || ''}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Quantity
          </label>
          <input
            type="number"
            name="qty"
            value={formData.qty || 0}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
            required
            min="0"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Revenue in Doc Curr
          </label>
          <input
            type="number"
            name="revenueInDocCurr"
            value={formData.revenueInDocCurr || 0}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
            required
            min="0"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Billing Date
          </label>
          <input
            type="date"
            name="billingDate"
            value={formData.billingDate || ''}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            PO Date
          </label>
          <input
            type="date"
            name="poDate"
            value={formData.poDate || ''}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3 mt-6">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          {isNew ? 'Create' : 'Update'}
        </button>
      </div>
    </form>
  );
};

// Main Page Component
export default function SalesRevenue2025DataMasterPage() {
  const [salesRevenueData, setSalesRevenueData] = useState<SalesRevenueItem[]>([]);
  const [filteredData, setFilteredData] = useState<SalesRevenueItem[]>([]);
  const [summary, setSummary] = useState<SalesRevenueSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<string>('customerName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Filter states
  const [filterCustomer, setFilterCustomer] = useState<string>('all');
  const [filterSalesman, setFilterSalesman] = useState<string>('all');
  const [filterDateFrom, setFilterDateFrom] = useState<string>('');
  const [filterDateTo, setFilterDateTo] = useState<string>('');

  // Unique values for filters
  const [uniqueCustomers, setUniqueCustomers] = useState<string[]>([]);
  const [uniqueSalesmen, setUniqueSalesmen] = useState<string[]>([]);

  // Form states
  const [showForm, setShowForm] = useState(false);
  const [currentItem, setCurrentItem] = useState<Partial<SalesRevenueItem>>({});
  const [isNewItem, setIsNewItem] = useState(true);

  // Import states
  const [importFile, setImportFile] = useState<File | null>(null);
  const [showMappingModal, setShowMappingModal] = useState(false);
  const [availableFields, setAvailableFields] = useState<string[]>([]);
  const [fieldMapping, setFieldMapping] = useState<Record<string, string>>({});
  const [sampleData, setSampleData] = useState<Record<string, any> | null>(null);
  const [importLoading, setImportLoading] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load data on component mount
  useEffect(() => {
    loadSalesRevenueItems();
  }, []);

  // Filter and sort data when dependencies change
  useEffect(() => {
    filterAndSortData();
  }, [salesRevenueData, searchQuery, sortField, sortDirection, filterCustomer, filterSalesman, filterDateFrom, filterDateTo]);

  // Load sales revenue data
  const loadSalesRevenueItems = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Loading sales revenue data...');
      const data = await loadSalesRevenueData();

      if (data.length === 0) {
        console.warn('No sales revenue data returned');
      } else {
        console.log('Sales revenue data loaded successfully:', data.length, 'items');
        setSalesRevenueData(data);

        // Generate summary
        const summaryData = generateSalesRevenueSummary(data);
        setSummary(summaryData);

        // Extract unique values for filters
        const customers = Array.from(new Set(data.map(item => item.customerName))).sort();
        const salesmen = Array.from(new Set(data.map(item => item.salesman))).sort();

        setUniqueCustomers(customers);
        setUniqueSalesmen(salesmen);
      }
    } catch (err) {
      console.error('Error loading sales revenue data:', err);
      setError('Failed to load sales revenue data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter and sort data
  const filterAndSortData = () => {
    let filtered = [...salesRevenueData];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.customerName.toLowerCase().includes(query) ||
        item.salesman.toLowerCase().includes(query) ||
        item.materialDescription.toLowerCase().includes(query)
      );
    }

    // Apply customer filter
    if (filterCustomer !== 'all') {
      filtered = filtered.filter(item => item.customerName === filterCustomer);
    }

    // Apply salesman filter
    if (filterSalesman !== 'all') {
      filtered = filtered.filter(item => item.salesman === filterSalesman);
    }

    // Apply date range filter
    if (filterDateFrom) {
      filtered = filtered.filter(item => {
        if (!item.billingDate) return false;
        return new Date(item.billingDate) >= new Date(filterDateFrom);
      });
    }

    if (filterDateTo) {
      filtered = filtered.filter(item => {
        if (!item.billingDate) return false;
        return new Date(item.billingDate) <= new Date(filterDateTo);
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else {
        return sortDirection === 'asc'
          ? (aValue > bValue ? 1 : -1)
          : (bValue > aValue ? 1 : -1);
      }
    });

    setFilteredData(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Reset filters
  const resetFilters = () => {
    setSearchQuery('');
    setFilterCustomer('all');
    setFilterSalesman('all');
    setFilterDateFrom('');
    setFilterDateTo('');
  };

  // Handle file import
  const handleFileImport = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      return;
    }

    const file = e.target.files[0];
    setImportFile(file);
    setImportLoading(true);

    try {
      // Read the file
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data);

      // Get the first worksheet
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];

      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      if (jsonData.length === 0) {
        throw new Error('No data found in the imported file');
      }

      console.log('Imported data sample:', jsonData[0]);

      // Get all possible field names from the first row
      const fields = Object.keys(jsonData[0] as object);
      setAvailableFields(fields);
      setSampleData(jsonData[0] as Record<string, any>);

      // Try to auto-map fields
      const newMapping: Record<string, string> = {};

      // Map fields based on similar names
      fields.forEach(field => {
        const lowerField = field.toLowerCase();

        if (lowerField.includes('customer') || lowerField.includes('client')) {
          newMapping.customerName = field;
        } else if (lowerField.includes('salesman') || lowerField.includes('sales')) {
          newMapping.salesman = field;
        } else if (lowerField.includes('material') || lowerField.includes('product')) {
          newMapping.materialDescription = field;
        } else if (lowerField.includes('qty') || lowerField.includes('quantity')) {
          newMapping.qty = field;
        } else if (lowerField.includes('revenue') || lowerField.includes('amount')) {
          newMapping.revenueInDocCurr = field;
        } else if (lowerField.includes('billing') || lowerField.includes('invoice')) {
          newMapping.billingDate = field;
        } else if (lowerField.includes('po') || lowerField.includes('purchase')) {
          newMapping.poDate = field;
        }
      });

      setFieldMapping(newMapping);
      setShowMappingModal(true);
    } catch (error) {
      console.error('Error reading import file:', error);
      setError(`Failed to read import file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setImportFile(null);

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } finally {
      setImportLoading(false);
    }
  };

  // Process import after field mapping is confirmed
  const processImport = async () => {
    if (!importFile) {
      setError('No file selected for import');
      return;
    }

    setImportLoading(true);
    setError(null);

    try {
      const result = await importSalesRevenueData(importFile, fieldMapping);

      if (result.success) {
        // Reload data after import
        await loadSalesRevenueItems();

        // Show success message
        alert(`Successfully imported ${result.importedCount} items.${result.errors.length > 0 ? ' Some errors occurred.' : ''}`);

        // Close modal and reset import state
        setShowMappingModal(false);
        setImportFile(null);
        setFieldMapping({});
        setSampleData(null);

        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        setError('Failed to import data');
      }
    } catch (error) {
      console.error('Error importing data:', error);
      setError(`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setImportLoading(false);
    }
  };

  // Handle add new item
  const handleAddNew = () => {
    setCurrentItem({});
    setIsNewItem(true);
    setShowForm(true);
  };

  // Handle edit item
  const handleEdit = (item: SalesRevenueItem) => {
    setCurrentItem(item);
    setIsNewItem(false);
    setShowForm(true);
  };

  // Handle delete item
  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      try {
        const success = await deleteSalesRevenueItem(id);
        if (success) {
          // Reload data after delete
          await loadSalesRevenueItems();
        } else {
          setError('Failed to delete item');
        }
      } catch (error) {
        console.error('Error deleting item:', error);
        setError(`Delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  };

  // Handle form submit
  const handleFormSubmit = async (item: Partial<SalesRevenueItem>) => {
    try {
      if (isNewItem) {
        await createSalesRevenueItem(item as Omit<SalesRevenueItem, 'id'>);
      } else {
        if (!currentItem.id) {
          throw new Error('Item ID is missing');
        }
        await updateSalesRevenueItem(currentItem.id, item);
      }

      // Reload data and close form
      await loadSalesRevenueItems();
      setShowForm(false);
    } catch (error) {
      console.error('Error saving item:', error);
      setError(`Save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Export to Excel/CSV
  const exportToExcel = (format: 'xlsx' | 'csv' = 'xlsx') => {
    try {
      exportSalesRevenueData(filteredData, format);
    } catch (error) {
      console.error('Error exporting data:', error);
      setError(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  // Format date
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      return format(date, 'dd MMM yyyy');
    } catch (e) {
      return dateString;
    }
  };

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  // Calculate pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredData.slice(indexOfFirstItem, indexOfLastItem);

  // Pagination controls
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);
  const nextPage = () => setCurrentPage(prev => Math.min(prev + 1, totalPages));
  const prevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1));

  return (
    <div className="space-y-6">
      {/* Field Mapping Modal */}
      <FieldMappingModal
        isOpen={showMappingModal}
        onClose={() => setShowMappingModal(false)}
        availableFields={availableFields}
        fieldMapping={fieldMapping}
        setFieldMapping={setFieldMapping}
        onConfirm={processImport}
        sampleData={sampleData}
      />

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <SalesRevenueForm
              item={currentItem}
              onSubmit={handleFormSubmit}
              onCancel={() => setShowForm(false)}
              isNew={isNewItem}
            />
          </div>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <DollarSign className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Sales Revenue 2025 Data Master</h1>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={handleAddNew}
            className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus size={16} className="mr-2" />
            Add New
          </button>

          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileImport}
            accept=".xlsx,.xls,.csv"
            className="hidden"
          />

          <button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center px-3 py-2 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200"
            disabled={importLoading}
          >
            <Upload size={16} className="mr-2" />
            {importLoading ? 'Importing...' : 'Import Data'}
          </button>

          <div className="relative">
            <button
              onClick={() => exportToExcel('xlsx')}
              className="flex items-center px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200"
            >
              <Download size={16} className="mr-2" />
              Export Excel
            </button>
          </div>

          <button
            onClick={loadSalesRevenueItems}
            className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh Data
          </button>
        </div>
      </div>

      {/* Score Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{formatCurrency(summary.totalRevenue)}</h3>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Customers</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{summary.totalCustomers}</h3>
              </div>
              <div className="p-3 bg-indigo-100 rounded-full">
                <Users className="h-6 w-6 text-indigo-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Items</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{summary.totalItems.toLocaleString()} unit</h3>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Package className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="md:col-span-2">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Search by customer, salesman, or material..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div>
            <label htmlFor="customerFilter" className="block text-sm font-medium text-gray-700 mb-1">
              Customer
            </label>
            <select
              id="customerFilter"
              className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              value={filterCustomer}
              onChange={(e) => setFilterCustomer(e.target.value)}
            >
              <option value="all">All Customers</option>
              {uniqueCustomers.map((customer) => (
                <option key={customer} value={customer}>
                  {customer}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="salesmanFilter" className="block text-sm font-medium text-gray-700 mb-1">
              Salesman
            </label>
            <select
              id="salesmanFilter"
              className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              value={filterSalesman}
              onChange={(e) => setFilterSalesman(e.target.value)}
            >
              <option value="all">All Salesmen</option>
              {uniqueSalesmen.map((salesman) => (
                <option key={salesman} value={salesman}>
                  {salesman}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="dateFromFilter" className="block text-sm font-medium text-gray-700 mb-1">
              Date From
            </label>
            <input
              type="date"
              id="dateFromFilter"
              className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={filterDateFrom}
              onChange={(e) => setFilterDateFrom(e.target.value)}
            />
          </div>

          <div className="flex flex-col">
            <label htmlFor="dateToFilter" className="block text-sm font-medium text-gray-700 mb-1">
              Date To
            </label>
            <input
              type="date"
              id="dateToFilter"
              className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              value={filterDateTo}
              onChange={(e) => setFilterDateTo(e.target.value)}
            />
          </div>

          <div className="flex items-end">
            <button
              onClick={resetFilters}
              className="flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Filter className="h-4 w-4 mr-2" />
              Reset Filters
            </button>
          </div>
        </div>
      </div>

      {/* Sales Revenue Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
            <p className="text-gray-600">Loading sales revenue data...</p>
          </div>
        ) : error ? (
          <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-yellow-700">{error}</p>
                <div className="mt-2 flex space-x-2">
                  <button
                    onClick={loadSalesRevenueItems}
                    className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200 text-sm font-medium"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : filteredData.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-600">No sales revenue data found matching your criteria.</p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('customerName')}
                    >
                      Customer Name {renderSortIndicator('customerName')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('salesman')}
                    >
                      Salesman {renderSortIndicator('salesman')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('materialDescription')}
                    >
                      Material Description {renderSortIndicator('materialDescription')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('qty')}
                    >
                      Qty {renderSortIndicator('qty')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('revenueInDocCurr')}
                    >
                      Revenue {renderSortIndicator('revenueInDocCurr')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('billingDate')}
                    >
                      Billing Date {renderSortIndicator('billingDate')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('poDate')}
                    >
                      PO Date {renderSortIndicator('poDate')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {currentItems.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {item.customerName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.salesman}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.materialDescription}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.qty.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatCurrency(item.revenueInDocCurr)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(item.billingDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(item.poDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => handleEdit(item)}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            <Edit size={16} />
                          </button>
                          <button
                            onClick={() => handleDelete(item.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                    <span className="font-medium">
                      {Math.min(indexOfLastItem, filteredData.length)}
                    </span>{' '}
                    of <span className="font-medium">{filteredData.length}</span> results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={prevPage}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === 1
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Previous</span>
                      <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                    </button>
                    {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                      // Show pages around current page
                      let pageNum = currentPage;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          onClick={() => paginate(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === pageNum
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                    <button
                      onClick={nextPage}
                      disabled={currentPage === totalPages}
                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === totalPages
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Next</span>
                      <ChevronRight className="h-5 w-5" aria-hidden="true" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
