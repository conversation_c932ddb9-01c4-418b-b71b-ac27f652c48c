<script setup lang="ts">
import { cn } from '@/lib/utils';
import { inject, onMounted, onUnmounted, ref } from 'vue';

const select = inject('select', {
  open: { value: false },
});

const clickOutside = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (contentRef.value && !contentRef.value.contains(target)) {
    select.open.value = false;
  }
};

const contentRef = ref<HTMLElement | null>(null);

onMounted(() => {
  document.addEventListener('click', clickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', clickOutside);
});
</script>

<template>
  <div
    v-if="select.open.value"
    ref="contentRef"
    :class="
      cn(
        'absolute z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-80',
        'w-full top-full mt-1'
      )
    "
  >
    <div class="w-full p-1">
      <slot></slot>
    </div>
  </div>
</template>