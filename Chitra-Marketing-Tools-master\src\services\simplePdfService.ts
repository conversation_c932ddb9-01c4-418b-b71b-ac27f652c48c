import { jsPDF } from 'jspdf';
import { BundleItem, PricingResult, Customer } from '../types';
import { formatCurrency } from '../utils/pricing';

export function generateSimpleQuotationPDF(
  bundleItems: BundleItem[],
  pricingResult: PricingResult,
  additionalInfo: string,
  customer?: Customer | null
): void {
  try {
    console.log('Generating simple PDF quotation...');

    // Create a new PDF document
    const doc = new jsPDF();

    // Add title
    doc.setFontSize(22);
    doc.setTextColor(0, 0, 0);
    doc.text('BundleBoost Quotation', 105, 20, { align: 'center' });

    // Add date
    const today = new Date();
    const formattedDate = today.toLocaleDateString();
    doc.setFontSize(12);
    doc.text(`Date: ${formattedDate}`, 20, 30);

    // Add customer information if available
    if (customer) {
      doc.setFontSize(14);
      doc.text('Customer Information:', 20, 40);

      doc.setFontSize(12);
      doc.text(`Name: ${customer.name}`, 25, 50);
      doc.text(`Company: ${customer.company || ''}`, 25, 58);
      doc.text(`Email: ${customer.email}`, 25, 66);
      doc.text(`Phone: ${customer.phone}`, 25, 74);
      doc.text(`Address: ${customer.address}`, 25, 82);
    }

    // Add products
    doc.setFontSize(14);
    // Adjust Y position based on whether customer info is present
    const productsYStart = customer ? 95 : 40;
    doc.text('Products:', 20, productsYStart);

    let yPosition = productsYStart + 10;
    bundleItems.forEach((item, index) => {
      doc.setFontSize(12);
      doc.text(`${index + 1}. ${item.product.materialDescription} (${item.quantity}x) - ${formatCurrency(item.product.price * item.quantity)}`, 25, yPosition);
      yPosition += 10;
    });

    // Add total
    yPosition += 10;
    doc.setFontSize(14);
    doc.text(`Total: ${formatCurrency(pricingResult.recommendedPrice)}`, 20, yPosition);

    // Add notes
    if (additionalInfo && additionalInfo.trim() !== '') {
      yPosition += 20;
      doc.setFontSize(12);
      doc.text(`Notes: ${additionalInfo}`, 20, yPosition);
    }

    // Save the PDF with customer name if available
    const filename = customer
      ? `BundleBoost-Quotation-${customer.name.replace(/\s+/g, '_')}.pdf`
      : 'BundleBoost-Quotation.pdf';

    doc.save(filename);
    console.log(`Simple PDF saved successfully as ${filename}`);

    // Show success message with customer name if available
    const successMessage = customer
      ? `Quotation for ${customer.name} generated successfully!`
      : 'PDF quotation generated successfully!';

    alert(successMessage);
  } catch (error) {
    console.error('Error generating simple PDF:', error);
    alert('Failed to generate simple PDF. Please check the console for details.');
  }
}
