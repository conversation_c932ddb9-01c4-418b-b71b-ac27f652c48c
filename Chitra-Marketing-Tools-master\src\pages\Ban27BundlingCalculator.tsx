import React, { useState, useEffect } from 'react';
import {
  Calculator, Truck, Sparkles, Loader2, Search, ArrowUpDown, DollarSign,
  BarChart, RefreshCw, AlertTriangle, CheckCircle, ArrowDown, ArrowUp,
  Minus, FileText, Save, AlertCircle, PlusCircle, MinusCircle
} from 'lucide-react';
import ProductSelectorNoQty from '../components/ProductSelectorNoQty';
import { BundleItem, Product } from '../types';
import { analyzeBundlePrice } from '../services/priceAnalysisService';
import { CompetitorPrice, fetchCompetitorPrices, calculateAveragePriceByBrand, getPriceStatistics } from '../services/competitorPriceService';
import { getProductByMaterialNo, refreshProductCache } from '../services/productPriceService';
import { formatCurrency } from '../utils/pricing';

interface CalculationResult {
  mainProductPrice: number;
  secondaryProductPrice: number;
  minimumMainProductQty: number;
  minimumSellPrice: number;
  maximumSellPrice: number;
  optimalSellPrice: number;
  profitMargin: number;
  optimalMargin: number;
  mainProductMargin: number; // Custom margin for main product
  secondaryProductMargin: number; // Custom margin for secondary product
  secondaryProductQty: number;
  pricePerPiece: number; // Harga per piece ban 27.00R49
  aiReasoning?: string;
  totalProfit?: number; // Total profit in rupiah
  priceComparisonStatus?: 'cheaper' | 'average' | 'expensive'; // Price comparison with market
  valueAddedPercentage?: number; // Value added percentage for secondary product
}

// Predefined main product details - will be loaded from Product Management
const MAIN_PRODUCT_CODE = '200-047262';
const MAIN_PRODUCT_NAME = '27.00 R 49 XD GRIP B E4T TL **';
const DEFAULT_PRICE = 212874175; // Fallback price if product not found

// Interface for saved bundling calculations
interface SavedBundling {
  id: string;
  date: string;
  mainProduct: Product;
  secondaryProducts: BundleItem[];
  secondaryProductQty: number;
  targetMargin: number;
  result: CalculationResult;
}

export default function Ban27BundlingCalculator() {
  const [mainProduct, setMainProduct] = useState<Product | null>(null);
  const [secondaryProducts, setSecondaryProducts] = useState<BundleItem[]>([]);
  const [targetMargin, setTargetMargin] = useState<number>(20);
  const [mainProductMargin, setMainProductMargin] = useState<number>(20); // Custom margin for main product
  const [secondaryProductMargin, setSecondaryProductMargin] = useState<number>(20); // Custom margin for secondary product
  const [calculationResult, setCalculationResult] = useState<CalculationResult | null>(null);
  const [secondaryProductQty, setSecondaryProductQty] = useState<number>(1);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [aiError, setAiError] = useState<string | null>(null);
  const [isLoadingProduct, setIsLoadingProduct] = useState<boolean>(false);
  const [productError, setProductError] = useState<string | null>(null);

  // AI analysis loading state with progress messages
  const [analysisProgress, setAnalysisProgress] = useState<string>('');
  const [progressStep, setProgressStep] = useState<number>(0);

  // Saved bundling calculations
  const [savedBundlings, setSavedBundlings] = useState<SavedBundling[]>([]);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isExporting, setIsExporting] = useState<boolean>(false);

  // Competitor price data
  const [competitorPrices, setCompetitorPrices] = useState<CompetitorPrice[]>([]);
  const [isLoadingPrices, setIsLoadingPrices] = useState<boolean>(false);
  const [priceError, setPriceError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortField, setSortField] = useState<string>('price');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filteredPrices, setFilteredPrices] = useState<CompetitorPrice[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);

  // Load the main product on component mount
  useEffect(() => {
    loadMainProduct();

    // Load competitor prices
    loadCompetitorPrices();

    // Add event listener for product data updates
    window.addEventListener('productDataUpdated', handleProductDataUpdated);

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('productDataUpdated', handleProductDataUpdated);
    };
  }, []);

  // Handle product data updates from other components
  const handleProductDataUpdated = () => {
    console.log('Product data updated, refreshing main product...');
    loadMainProduct();
  };

  // Manually refresh product data
  const handleRefreshProductData = async () => {
    setIsLoadingProduct(true);
    setProductError(null);

    try {
      // Force refresh the product cache
      await refreshProductCache();
      // Then load the main product with fresh data
      await loadMainProduct();
      setProductError('Data produk berhasil diperbarui dari Product Management.');
    } catch (error) {
      console.error('Error refreshing product data:', error);
      setProductError('Gagal memperbarui data produk. Silakan coba lagi.');
    } finally {
      setIsLoadingProduct(false);
    }
  };

  // Load the main product from Product Management
  const loadMainProduct = async () => {
    setIsLoadingProduct(true);
    setProductError(null);

    try {
      // Create a default product to use as fallback
      const defaultProduct: Product = {
        id: 'ban-27-00-r-49-default',
        oldMaterialNo: MAIN_PRODUCT_CODE,
        materialDescription: MAIN_PRODUCT_NAME,
        description: 'Ban Premium untuk Heavy Equipment',
        price: DEFAULT_PRICE
      };

      // Use our productPriceService to get the latest product data
      const product = await getProductByMaterialNo(
        MAIN_PRODUCT_CODE,
        '27.00 R 49 XD GRIP',
        defaultProduct
      );

      if (product && product.id !== 'ban-27-00-r-49-default') {
        console.log('Found 27.00 R 49 XD GRIP product:', product);
        setMainProduct(product);
      } else {
        console.warn('27.00 R 49 XD GRIP product not found in Product Management, using default values');
        setProductError('Produk 27.00 R 49 XD GRIP tidak ditemukan di Product Management. Menggunakan nilai default.');
        setMainProduct(defaultProduct);
      }
    } catch (error) {
      console.error('Error loading main product:', error);
      setProductError('Gagal memuat data produk dari Product Management. Menggunakan nilai default.');

      // Use default values on error
      setMainProduct({
        id: 'ban-27-00-r-49-default',
        oldMaterialNo: MAIN_PRODUCT_CODE,
        materialDescription: MAIN_PRODUCT_NAME,
        description: 'Ban Premium untuk Heavy Equipment',
        price: DEFAULT_PRICE
      });
    } finally {
      setIsLoadingProduct(false);
    }
  };

  // Load competitor prices
  const loadCompetitorPrices = async () => {
    setIsLoadingPrices(true);
    setPriceError(null);

    try {
      const prices = await fetchCompetitorPrices();
      setCompetitorPrices(prices);
    } catch (error) {
      console.error('Error loading competitor prices:', error);
      setPriceError('Failed to load competitor prices. Using fallback data.');
    } finally {
      setIsLoadingPrices(false);
    }
  };

  // Filter and sort competitor prices
  useEffect(() => {
    if (competitorPrices.length === 0) return;

    let filtered = [...competitorPrices];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.customer.toLowerCase().includes(query) ||
        item.brand.toLowerCase().includes(query) ||
        item.supplier.toLowerCase().includes(query)
      );
    }

    // Sort data
    filtered.sort((a, b) => {
      let aValue = a[sortField as keyof CompetitorPrice];
      let bValue = b[sortField as keyof CompetitorPrice];

      // Handle numeric sorting
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      // Default string comparison
      return sortDirection === 'asc'
        ? String(aValue).localeCompare(String(bValue))
        : String(bValue).localeCompare(String(aValue));
    });

    setFilteredPrices(filtered);
  }, [competitorPrices, searchQuery, sortField, sortDirection]);

  // Handle secondary products selection
  const handleSecondaryProductsChange = (products: BundleItem[]) => {
    setSecondaryProducts(products);
  };

  // Handle secondary product quantity change
  const handleSecondaryQtyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const qty = Math.max(1, parseInt(e.target.value) || 1);
    setSecondaryProductQty(qty);
  };

  // Calculate bundling
  const calculateBundling = async () => {
    if (!mainProduct || secondaryProducts.length === 0) {
      alert('Please select at least one secondary product');
      return;
    }

    // Reset error state and progress
    setAiError(null);
    setAnalysisProgress('Memulai kalkulasi bundling...');
    setProgressStep(1);
    setIsAnalyzing(true);

    // Get the secondary product price (using the first one for calculation)
    const secondaryProduct = secondaryProducts[0];
    const secondaryProductPrice = secondaryProduct.product.price;

    // Calculate total cost of secondary products
    const secondaryProductTotalCost = secondaryProductQty * secondaryProductPrice;

    // Update progress
    setAnalysisProgress('Menghitung jumlah minimum produk utama...');
    setProgressStep(2);
    await new Promise(resolve => setTimeout(resolve, 300)); // Small delay for UI feedback

    // Calculate profit per unit of main product using custom main product margin
    const profitPerUnit = mainProduct.price * (mainProductMargin / 100);

    // Calculate secondary product profit using custom secondary product margin
    const secondaryProductProfit = secondaryProductPrice * (secondaryProductMargin / 100);

    // Calculate effective secondary product cost (considering its margin)
    const effectiveSecondaryProductCost = secondaryProductPrice - secondaryProductProfit;

    // Calculate minimum main product quantity per unit of secondary product
    // This is how many main products we need to sell to cover the cost of one secondary product
    const minMainProductQtyPerSecondary = Math.ceil(effectiveSecondaryProductCost / profitPerUnit);

    // Total minimum main product quantity is proportional to secondary product quantity
    const minimumMainProductQty = minMainProductQtyPerSecondary * secondaryProductQty;

    // Calculate total cost (using original prices, not considering margins)
    const totalCost = (mainProduct.price * minimumMainProductQty) + secondaryProductTotalCost;

    // Calculate recommended sell price based on main product margin
    // We use the main product margin for the final price calculation
    const recommendedSellPrice = totalCost * (1 + mainProductMargin/100);

    // Calculate profit margin based on recommended sell price
    const profitMargin = mainProductMargin; // Initially set to main product margin, will be updated with optimal price later

    // Calculate price per piece (only relevant if minimumMainProductQty > 1)
    const pricePerPiece = minimumMainProductQty > 1
      ? recommendedSellPrice / minimumMainProductQty
      : mainProduct.price;

    // Calculate value added percentage for secondary product
    const valueAddedPercentage = (secondaryProductPrice / mainProduct.price) * 100;

    // Update progress
    setAnalysisProgress('Menganalisis data harga kompetitor...');
    setProgressStep(3);
    await new Promise(resolve => setTimeout(resolve, 300)); // Small delay for UI feedback

    // Get price statistics for competitor analysis
    const priceStats = getPriceStatistics(competitorPrices);
    const brandAvgPrices = calculateAveragePriceByBrand(competitorPrices);

    // Determine price comparison status based on price per piece
    let priceComparisonStatus: 'cheaper' | 'average' | 'expensive' = 'average';
    if (priceStats.avgPrice > 0) {
      if (pricePerPiece < priceStats.economyAvgPrice * 0.95) {
        priceComparisonStatus = 'cheaper';
      } else if (pricePerPiece > priceStats.premiumAvgPrice * 1.05) {
        priceComparisonStatus = 'expensive';
      } else {
        priceComparisonStatus = 'average';
      }
    }

    // Calculate total profit
    const totalProfit = recommendedSellPrice - totalCost;

    // Set initial calculation result with default values
    setCalculationResult({
      mainProductPrice: mainProduct.price,
      secondaryProductPrice: secondaryProductPrice,
      minimumMainProductQty: minimumMainProductQty,
      minimumSellPrice: recommendedSellPrice * 0.95, // 5% below recommended
      maximumSellPrice: recommendedSellPrice * 1.05, // 5% above recommended
      optimalSellPrice: recommendedSellPrice, // Will be updated by AI
      profitMargin: profitMargin,
      optimalMargin: targetMargin, // Will be updated by AI
      mainProductMargin: mainProductMargin, // Custom margin for main product
      secondaryProductMargin: secondaryProductMargin, // Custom margin for secondary product
      secondaryProductQty: secondaryProductQty,
      pricePerPiece: pricePerPiece, // Harga per piece ban 27.00R49
      totalProfit: totalProfit,
      priceComparisonStatus: priceComparisonStatus,
      valueAddedPercentage: valueAddedPercentage
    });

    // Update progress
    setAnalysisProgress('Meminta analisis AI untuk optimasi harga...');
    setProgressStep(4);
    await new Promise(resolve => setTimeout(resolve, 300)); // Small delay for UI feedback

    try {
      const aiAnalysisResult = await analyzeBundlePrice({
        mainProductName: mainProduct.materialDescription,
        mainProductPrice: mainProduct.price,
        mainProductMargin: mainProductMargin,
        secondaryProductName: secondaryProduct.product.materialDescription,
        secondaryProductPrice: secondaryProductPrice,
        secondaryProductMargin: secondaryProductMargin,
        secondaryProductQty: secondaryProductQty,
        minimumMainProductQty: minimumMainProductQty,
        minimumSellPrice: recommendedSellPrice * 0.95,
        maximumSellPrice: recommendedSellPrice * 1.05,
        targetMargin: mainProductMargin, // Use main product margin instead of target margin
        totalCost: totalCost,
        competitorData: {
          avgPrice: priceStats.avgPrice,
          minPrice: priceStats.minPrice,
          maxPrice: priceStats.maxPrice,
          premiumAvgPrice: priceStats.premiumAvgPrice,
          economyAvgPrice: priceStats.economyAvgPrice,
          topBrands: brandAvgPrices.slice(0, 3).map(b => ({
            brand: b.brand,
            price: b.avgPrice
          }))
        }
      });

      // Update progress
      setAnalysisProgress('Memproses hasil analisis AI...');
      setProgressStep(5);
      await new Promise(resolve => setTimeout(resolve, 300)); // Small delay for UI feedback

      // Update calculation result with AI analysis
      setCalculationResult(prevResult => {
        if (!prevResult) return null;

        // Calculate updated price per piece based on AI optimal price
        const updatedPricePerPiece = prevResult.minimumMainProductQty > 1
          ? aiAnalysisResult.optimalPrice / prevResult.minimumMainProductQty
          : prevResult.mainProductPrice;

        // Recalculate price comparison status based on AI optimal price
        let updatedPriceComparisonStatus: 'cheaper' | 'average' | 'expensive' = prevResult.priceComparisonStatus || 'average';
        if (priceStats.avgPrice > 0) {
          if (updatedPricePerPiece < priceStats.economyAvgPrice * 0.95) {
            updatedPriceComparisonStatus = 'cheaper';
          } else if (updatedPricePerPiece > priceStats.premiumAvgPrice * 1.05) {
            updatedPriceComparisonStatus = 'expensive';
          } else {
            updatedPriceComparisonStatus = 'average';
          }
        }

        // Calculate updated total profit
        const updatedTotalProfit = aiAnalysisResult.optimalPrice - totalCost;

        return {
          ...prevResult,
          optimalSellPrice: aiAnalysisResult.optimalPrice,
          optimalMargin: aiAnalysisResult.optimalMargin,
          profitMargin: aiAnalysisResult.optimalMargin, // Update the estimated margin to match the optimal margin
          // Preserve the custom margins
          mainProductMargin: mainProductMargin,
          secondaryProductMargin: secondaryProductMargin,
          aiReasoning: aiAnalysisResult.reasoning,
          pricePerPiece: updatedPricePerPiece, // Update price per piece based on AI optimal price
          totalProfit: updatedTotalProfit,
          priceComparisonStatus: updatedPriceComparisonStatus
        };
      });

      // Final progress update
      setAnalysisProgress('Analisis selesai!');
      setProgressStep(6);
      await new Promise(resolve => setTimeout(resolve, 300)); // Small delay for UI feedback

    } catch (error) {
      console.error('Error during AI price analysis:', error);
      setAiError('Terjadi kesalahan saat menganalisis harga dengan AI. Menggunakan perhitungan default.');
      setAnalysisProgress('Menggunakan perhitungan default...');
      setProgressStep(5);

      // Fallback to default calculation based on main product margin
      let optimalPrice = totalCost * (1 + mainProductMargin/100);
      // Apply psychological pricing (ending in 999,000)
      optimalPrice = Math.floor(optimalPrice / 1000000) * 1000000 + 999000;
      const optimalMargin = ((optimalPrice - totalCost) / totalCost) * 100;

      // Calculate total profit with fallback price
      const fallbackTotalProfit = optimalPrice - totalCost;

      setCalculationResult(prevResult => {
        if (!prevResult) return null;

        // Calculate updated price per piece based on fallback optimal price
        const updatedPricePerPiece = prevResult.minimumMainProductQty > 1
          ? optimalPrice / prevResult.minimumMainProductQty
          : prevResult.mainProductPrice;

        return {
          ...prevResult,
          optimalSellPrice: optimalPrice,
          optimalMargin: optimalMargin,
          profitMargin: optimalMargin, // Update the estimated margin to match the optimal margin
          // Preserve the custom margins
          mainProductMargin: mainProductMargin,
          secondaryProductMargin: secondaryProductMargin,
          pricePerPiece: updatedPricePerPiece, // Update price per piece based on fallback optimal price
          totalProfit: fallbackTotalProfit
        };
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Save bundling calculation
  const saveBundling = () => {
    if (!calculationResult || !mainProduct || secondaryProducts.length === 0) {
      alert('Harap lakukan kalkulasi terlebih dahulu sebelum menyimpan');
      return;
    }

    setIsSaving(true);

    try {
      // Create a new saved bundling object
      const newSavedBundling: SavedBundling = {
        id: `bundling-${Date.now()}`,
        date: new Date().toISOString(),
        mainProduct: mainProduct,
        secondaryProducts: secondaryProducts,
        secondaryProductQty: secondaryProductQty,
        targetMargin: targetMargin,
        result: calculationResult
      };

      // Add to saved bundlings
      setSavedBundlings(prev => [...prev, newSavedBundling]);

      // Could also save to localStorage or a database here
      localStorage.setItem('savedBundlings', JSON.stringify([...savedBundlings, newSavedBundling]));

      alert('Bundling berhasil disimpan!');
    } catch (error) {
      console.error('Error saving bundling:', error);
      alert('Gagal menyimpan bundling. Silakan coba lagi.');
    } finally {
      setIsSaving(false);
    }
  };

  // Export bundling to PDF
  const exportToPDF = () => {
    if (!calculationResult || !mainProduct || secondaryProducts.length === 0) {
      alert('Harap lakukan kalkulasi terlebih dahulu sebelum mengekspor ke PDF');
      return;
    }

    setIsExporting(true);

    try {
      // Here we would call a PDF generation service
      // For now, we'll just simulate it with a timeout
      setTimeout(() => {
        alert('PDF berhasil diekspor! (Simulasi)');
        setIsExporting(false);
      }, 1500);

      // In a real implementation, we would call something like:
      // generateBundlingPDF(mainProduct, secondaryProducts, calculationResult);
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      alert('Gagal mengekspor ke PDF. Silakan coba lagi.');
      setIsExporting(false);
    }
  };

  // Handle secondary product quantity change directly from the results section
  const handleSecondaryQtyChangeInResults = (newQty: number) => {
    if (newQty < 1) return;
    setSecondaryProductQty(newQty);
    // Recalculate if we already have a result
    if (calculationResult) {
      calculateBundling();
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Truck className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">27.00 R 49 Bundling Calculator</h1>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="space-y-6">
          {/* Main Product Display */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium">Main Product</h2>
              <div className="flex space-x-2">
                <button
                  onClick={handleRefreshProductData}
                  className="flex items-center text-sm text-green-600 hover:text-green-800 px-2 py-1 border border-green-200 rounded"
                  disabled={isLoadingProduct}
                  title="Refresh product data from Product Management"
                >
                  {isLoadingProduct ? (
                    <Loader2 size={16} className="mr-1 animate-spin" />
                  ) : (
                    <RefreshCw size={16} className="mr-1" />
                  )}
                  {isLoadingProduct ? 'Updating...' : 'Update from Product Management'}
                </button>
                <button
                  onClick={loadMainProduct}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                  disabled={isLoadingProduct}
                >
                  {isLoadingProduct ? (
                    <Loader2 size={16} className="mr-1 animate-spin" />
                  ) : (
                    <RefreshCw size={16} className="mr-1" />
                  )}
                  {isLoadingProduct ? 'Loading...' : 'Refresh'}
                </button>
              </div>
            </div>

            {isLoadingProduct && !mainProduct && (
              <div className="bg-blue-50 p-4 rounded-lg flex items-center">
                <Loader2 size={20} className="mr-2 animate-spin text-blue-600" />
                <span className="text-blue-600">Loading product data...</span>
              </div>
            )}

            {productError && (
              <div className="bg-yellow-50 p-3 rounded-lg mb-3 text-sm text-yellow-700">
                {productError}
              </div>
            )}

            {mainProduct && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="font-medium text-blue-800">{mainProduct.materialDescription}</div>
                <div className="text-sm text-blue-600">Code: {mainProduct.oldMaterialNo}</div>
                <div className="text-lg font-bold text-blue-900 mt-1">{formatCurrency(mainProduct.price)}</div>
                <div className="mt-2 text-xs text-blue-700">
                  <span className="font-medium">Target Margin:</span> {targetMargin}%
                </div>
                {mainProduct.id === 'ban-27-00-r-49-default' && (
                  <div className="mt-2 text-xs text-orange-600 bg-orange-50 p-1 rounded">
                    Menggunakan harga default. Tambahkan produk 27.00 R 49 XD GRIP di Product Management untuk sinkronisasi harga.
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Secondary Product Selection */}
          <div>
            <h2 className="text-lg font-medium mb-4">Select Secondary Products</h2>
            <ProductSelectorNoQty
              selectedProducts={secondaryProducts}
              onProductsChange={handleSecondaryProductsChange}
            />
          </div>

          {/* Secondary Product Quantity */}
          <div className="pt-4 border-t">
            <h2 className="text-lg font-medium mb-4">Secondary Product Quantity</h2>
            <div className="flex items-center space-x-4">
              <div className="w-full max-w-xs">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Quantity
                </label>
                <input
                  type="number"
                  min="1"
                  value={secondaryProductQty}
                  onChange={handleSecondaryQtyChange}
                  className="w-full p-2 border rounded-md"
                />
              </div>
            </div>
          </div>

          {/* Custom Margins */}
          <div className="pt-4 border-t">
            <h2 className="text-lg font-medium mb-4">Custom Margins</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Main Product Margin (%)
                </label>
                <input
                  type="number"
                  min="0"
                  value={mainProductMargin}
                  onChange={(e) => setMainProductMargin(Math.max(0, Number(e.target.value)))}
                  className="w-full p-2 border rounded-md"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Set the profit margin for the main product (27.00 R 49)
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Secondary Product Margin (%)
                </label>
                <input
                  type="number"
                  min="0"
                  value={secondaryProductMargin}
                  onChange={(e) => setSecondaryProductMargin(Math.max(0, Number(e.target.value)))}
                  className="w-full p-2 border rounded-md"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Set the profit margin for the secondary product
                </p>
              </div>
            </div>
          </div>

          {/* Calculate Button */}
          <div className="pt-4">
            <button
              onClick={calculateBundling}
              className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center"
              disabled={!mainProduct || secondaryProducts.length === 0 || isAnalyzing}
            >
              {isAnalyzing ? (
                <>
                  <Loader2 size={18} className="mr-2 animate-spin" />
                  {analysisProgress || "Analyzing with AI..."}
                </>
              ) : (
                <>
                  <Sparkles size={18} className="mr-2" />
                  Calculate with AI Analysis
                </>
              )}
            </button>

            {/* Progressive Loading Indicator */}
            {isAnalyzing && (
              <div className="mt-3 bg-blue-50 p-3 rounded-lg border border-blue-100">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-blue-800">{analysisProgress}</span>
                  <span className="text-xs text-blue-600">{progressStep}/6</span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2.5">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                    style={{ width: `${(progressStep / 6) * 100}%` }}
                  ></div>
                </div>
                <p className="mt-2 text-xs text-blue-600">
                  {progressStep === 1 && "Mempersiapkan data untuk kalkulasi..."}
                  {progressStep === 2 && "Menghitung jumlah minimum produk utama yang diperlukan..."}
                  {progressStep === 3 && "Menganalisis data harga kompetitor untuk perbandingan..."}
                  {progressStep === 4 && "Mengirim data ke model AI untuk analisis mendalam..."}
                  {progressStep === 5 && "Memproses hasil analisis AI dan mengoptimalkan harga..."}
                  {progressStep === 6 && "Finalisasi hasil dan menyiapkan rekomendasi bisnis..."}
                </p>
              </div>
            )}

            {aiError && (
              <div className="mt-2 text-sm text-red-600 flex items-center">
                <AlertCircle size={16} className="mr-1" />
                {aiError}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Calculation Results */}
      {calculationResult && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium">Calculation Results</h2>

            {/* Action Buttons */}
            <div className="flex space-x-2">
              <button
                onClick={saveBundling}
                disabled={isSaving}
                className="flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                {isSaving ? (
                  <Loader2 size={14} className="mr-1 animate-spin" />
                ) : (
                  <Save size={14} className="mr-1" />
                )}
                Simpan Bundling
              </button>

              <button
                onClick={exportToPDF}
                disabled={isExporting}
                className="flex items-center px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                {isExporting ? (
                  <Loader2 size={14} className="mr-1 animate-spin" />
                ) : (
                  <FileText size={14} className="mr-1" />
                )}
                Export ke PDF
              </button>
            </div>
          </div>

          {/* Low Margin Alert */}
          {calculationResult.optimalMargin < 5 && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-start">
              <AlertTriangle size={20} className="text-red-500 mr-2 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-red-800">Margin Tipis!</h3>
                <p className="text-xs text-red-700 mt-1">
                  Margin keuntungan saat ini hanya {calculationResult.optimalMargin.toFixed(2)}%, yang berada di bawah batas aman 5%.
                </p>
                <div className="mt-2 text-xs">
                  <span className="font-medium text-red-800">Saran perbaikan:</span>
                  <ul className="list-disc pl-5 mt-1 text-red-700 space-y-1">
                    <li>Tingkatkan target margin menjadi minimal 10%</li>
                    <li>Kurangi jumlah produk sekunder yang diberikan gratis</li>
                    <li>Gunakan produk sekunder dengan harga modal yang lebih rendah</li>
                    <li>Tingkatkan jumlah minimum produk utama yang dijual</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-blue-800">Min Main Product Qty</h3>
                <p className="text-2xl font-bold text-blue-900">
                  {calculationResult.minimumMainProductQty} units
                </p>
                <p className="text-xs text-blue-700 mt-1">To cover secondary product cost with custom margins</p>
              </div>

              <div className="bg-green-600 p-4 rounded-lg text-white">
                <h3 className="text-sm font-medium">Total Profit</h3>
                <p className="text-2xl font-bold">
                  {formatCurrency(calculationResult.optimalSellPrice - ((calculationResult.mainProductPrice * calculationResult.minimumMainProductQty) + ((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty)))}
                </p>
                <p className="text-xs mt-1">Profit per unit: {formatCurrency((calculationResult.optimalSellPrice - ((calculationResult.mainProductPrice * calculationResult.minimumMainProductQty) + ((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty))) / calculationResult.minimumMainProductQty)}</p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg relative">
                <div className="absolute top-2 right-2">
                  <Sparkles size={16} className="text-green-600" />
                </div>

                {/* Price Comparison Badge */}
                {calculationResult.priceComparisonStatus && (
                  <div className="absolute -top-3 -left-3">
                    <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      calculationResult.priceComparisonStatus === 'cheaper'
                        ? 'bg-green-500 text-white'
                        : calculationResult.priceComparisonStatus === 'expensive'
                          ? 'bg-red-500 text-white'
                          : 'bg-yellow-500 text-white'
                    }`}>
                      {calculationResult.priceComparisonStatus === 'cheaper' && (
                        <>
                          <ArrowDown size={12} className="mr-1" />
                          Lebih Murah
                        </>
                      )}
                      {calculationResult.priceComparisonStatus === 'expensive' && (
                        <>
                          <ArrowUp size={12} className="mr-1" />
                          Lebih Mahal
                        </>
                      )}
                      {calculationResult.priceComparisonStatus === 'average' && (
                        <>
                          <Minus size={12} className="mr-1" />
                          Rata-rata Pasar
                        </>
                      )}
                    </div>
                  </div>
                )}

                <h3 className="text-sm font-medium text-green-800">AI-Optimized Price</h3>
                <p className="text-2xl font-bold text-green-900">
                  {formatCurrency(calculationResult.optimalSellPrice)}
                </p>
                <p className="text-xs text-green-700 mt-1">Margin: {calculationResult.optimalMargin.toFixed(2)}%</p>
                <div className="mt-1 flex items-center">
                  <span className="text-xs font-medium text-green-800">DeepSeek Analysis</span>
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-purple-800">Margins</h3>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  <div>
                    <h4 className="text-xs font-medium text-purple-800">Main Product</h4>
                    <p className="text-lg font-bold text-purple-900">
                      {calculationResult.mainProductMargin.toFixed(2)}%
                    </p>
                  </div>
                  <div>
                    <h4 className="text-xs font-medium text-purple-800">Secondary Product</h4>
                    <p className="text-lg font-bold text-purple-900">
                      {calculationResult.secondaryProductMargin.toFixed(2)}%
                    </p>
                  </div>
                </div>
                <div className="mt-2 pt-2 border-t border-purple-200">
                  <h4 className="text-xs font-medium text-purple-800">Overall Margin</h4>
                  <p className="text-lg font-bold text-purple-900">
                    {calculationResult.profitMargin.toFixed(2)}%
                  </p>
                  <p className="text-xs text-purple-700">Based on optimal sell price</p>
                </div>

                {/* Total Profit Display */}
                <div className="mt-2 pt-2 border-t border-purple-200">
                  <h4 className="text-xs font-medium text-purple-800">Total Profit</h4>
                  <p className="text-lg font-bold text-purple-900">
                    {formatCurrency(calculationResult.optimalSellPrice - ((calculationResult.mainProductPrice * calculationResult.minimumMainProductQty) + ((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty)))}
                  </p>
                </div>
              </div>

              {calculationResult.minimumMainProductQty > 1 && (
                <div className="bg-amber-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-amber-800">Price Per Piece</h3>
                  <p className="text-2xl font-bold text-amber-900">
                    {formatCurrency(calculationResult.pricePerPiece)}
                  </p>
                  <p className="text-xs text-amber-700 mt-1">
                    Harga per unit ban 27.00R49 dalam bundling
                  </p>
                </div>
              )}
            </div>

            {/* Interactive Secondary Product Component */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Bundling Summary</h3>

              {mainProduct && (
                <div className="mb-3 p-3 bg-blue-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <h4 className="text-sm font-medium text-blue-800">Main Product</h4>
                    <span className="text-xs bg-blue-200 text-blue-800 px-2 py-0.5 rounded-full">
                      {calculationResult.minimumMainProductQty} units
                    </span>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm">{mainProduct.materialDescription}</p>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-gray-600">Unit Price:</span>
                      <span className="text-sm font-medium">{formatCurrency(mainProduct.price)}</span>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-gray-600">Total:</span>
                      <span className="text-sm font-medium">{formatCurrency(mainProduct.price * calculationResult.minimumMainProductQty)}</span>
                    </div>
                  </div>
                </div>
              )}

              {secondaryProducts.length > 0 && (
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="flex justify-between items-center">
                    <h4 className="text-sm font-medium text-green-800">Secondary Product (FREE)</h4>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => handleSecondaryQtyChangeInResults(Math.max(1, secondaryProductQty - 1))}
                        className="p-1 bg-green-200 text-green-800 rounded hover:bg-green-300"
                        disabled={secondaryProductQty <= 1}
                      >
                        <MinusCircle size={14} />
                      </button>
                      <span className="text-xs bg-green-200 text-green-800 px-2 py-0.5 rounded-full min-w-[40px] text-center">
                        {secondaryProductQty} units
                      </span>
                      <button
                        onClick={() => handleSecondaryQtyChangeInResults(secondaryProductQty + 1)}
                        className="p-1 bg-green-200 text-green-800 rounded hover:bg-green-300"
                      >
                        <PlusCircle size={14} />
                      </button>
                    </div>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm">{secondaryProducts[0].product.materialDescription}</p>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-gray-600">Unit Price:</span>
                      <span className="text-sm font-medium">{formatCurrency(secondaryProducts[0].product.price)}</span>
                    </div>
                    <div className="flex justify-between items-center mt-1">
                      <span className="text-xs text-gray-600">Total Value:</span>
                      <span className="text-sm font-medium">{formatCurrency(secondaryProducts[0].product.price * secondaryProductQty)}</span>
                    </div>

                    {/* Value Added Display */}
                    {calculationResult.valueAddedPercentage && (
                      <div className="mt-2 pt-2 border-t border-green-200">
                        <div className="flex justify-between items-center">
                          <span className="text-xs font-medium text-green-800">Value Added:</span>
                          <span className="text-xs font-medium bg-green-700 text-white px-2 py-0.5 rounded-full">
                            {calculationResult.valueAddedPercentage.toFixed(1)}% of main product value
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="mt-4 space-y-3">
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-green-800">Analisis AI & Rekomendasi Bisnis</h3>
                    <div className="flex items-center bg-blue-100 px-2 py-1 rounded-full">
                      <Sparkles size={14} className="text-blue-600 mr-1" />
                      <span className="text-xs font-medium text-blue-700">DeepSeek Analysis</span>
                    </div>
                  </div>

                  {/* Promo Headline */}
                  <div className="bg-white p-3 rounded-lg border border-green-100 mb-3">
                    <p className="text-base font-bold text-green-800 text-center">
                      "Beli {calculationResult.minimumMainProductQty} {mainProduct?.materialDescription} dengan harga {formatCurrency(calculationResult.optimalSellPrice)}, GRATIS {calculationResult.secondaryProductQty} {secondaryProducts[0]?.product.materialDescription}!"
                    </p>
                    <p className="text-xs text-gray-500 text-center mt-1">
                      (Untuk setiap {Math.ceil(calculationResult.secondaryProductPrice / (calculationResult.mainProductPrice * (calculationResult.mainProductMargin/100)))} unit {mainProduct?.materialDescription}, Anda bisa memberikan 1 unit {secondaryProducts[0]?.product.materialDescription} gratis)
                    </p>
                  </div>

                  {/* Enhanced AI Analysis with Formatting */}
                  <div className="bg-blue-50 p-3 rounded-lg mb-3">
                    <h4 className="font-medium text-blue-800 mb-2 flex items-center">
                      <Sparkles size={16} className="mr-2 text-blue-600" />
                      Analisis AI:
                    </h4>

                    {calculationResult.aiReasoning ? (
                      <div className="text-sm text-blue-700 space-y-2">
                        {/* Format the AI reasoning with bullet points and bold keywords */}
                        {calculationResult.aiReasoning.split('. ').map((sentence, index) => {
                          // Skip empty sentences
                          if (!sentence.trim()) return null;

                          // Bold important keywords
                          const formattedSentence = sentence
                            .replace(/optimal/gi, '<strong>optimal</strong>')
                            .replace(/margin/gi, '<strong>margin</strong>')
                            .replace(/harga/gi, '<strong>harga</strong>')
                            .replace(/kompetitor/gi, '<strong>kompetitor</strong>')
                            .replace(/bundling/gi, '<strong>bundling</strong>')
                            .replace(/profit/gi, '<strong>profit</strong>')
                            .replace(/rekomendasi/gi, '<strong>rekomendasi</strong>')
                            .replace(/psikologis/gi, '<strong>psikologis</strong>');

                          return (
                            <div key={index} className="flex">
                              <span className="mr-2">•</span>
                              <span dangerouslySetInnerHTML={{ __html: formattedSentence }} />
                            </div>
                          );
                        })}

                        {/* Highlight Key Insight */}
                        <div className="mt-3 p-2 bg-blue-100 rounded border border-blue-200">
                          <p className="text-sm font-medium text-blue-800">
                            Insight Utama: Harga {formatCurrency(calculationResult.optimalSellPrice)} memberikan keseimbangan optimal antara daya saing pasar dan profitabilitas dengan margin {calculationResult.optimalMargin.toFixed(2)}%.
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <div className="flex">
                          <span className="mr-2">•</span>
                          <span>Berdasarkan <strong>perhitungan algoritma</strong> optimasi harga, sistem AI merekomendasikan <strong>harga jual optimal</strong> sebesar {formatCurrency(calculationResult.optimalSellPrice)}</span>
                        </div>
                        <div className="flex">
                          <span className="mr-2">•</span>
                          <span>Bundling {calculationResult.minimumMainProductQty} unit <strong>{mainProduct?.materialDescription}</strong> dengan {calculationResult.secondaryProductQty} unit <strong>{secondaryProducts[0]?.product.materialDescription}</strong> gratis</span>
                        </div>
                        <div className="flex">
                          <span className="mr-2">•</span>
                          <span>Dengan <strong>margin produk utama {calculationResult.mainProductMargin}%</strong>, setiap unit Ban 27.00 R49 menghasilkan <strong>keuntungan</strong> {formatCurrency(mainProduct?.price * (calculationResult.mainProductMargin/100))}</span>
                        </div>
                        <div className="flex">
                          <span className="mr-2">•</span>
                          <span>Harga ini mempertimbangkan <strong>faktor psikologis</strong> dengan angka 999,000 di akhir</span>
                        </div>
                        <div className="flex">
                          <span className="mr-2">•</span>
                          <span>Posisi harga <strong>{calculationResult.priceComparisonStatus === 'cheaper' ? 'lebih rendah dari' : calculationResult.priceComparisonStatus === 'expensive' ? 'lebih tinggi dari' : 'sesuai dengan'}</strong> rata-rata pasar</span>
                        </div>

                        {/* Highlight Key Insight */}
                        <div className="mt-3 p-2 bg-blue-100 rounded border border-blue-200">
                          <p className="text-sm font-medium text-blue-800">
                            Insight Utama: Harga {formatCurrency(calculationResult.optimalSellPrice)} memberikan keseimbangan optimal antara daya saing pasar dan profitabilitas dengan margin {calculationResult.optimalMargin.toFixed(2)}%.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Factors Considered in Analysis */}
                  <div className="bg-blue-50 p-3 rounded-lg mb-3">
                    <h4 className="font-medium text-blue-800 mb-2">Faktor-faktor Analisis AI:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      <div className="flex items-start">
                        <CheckCircle size={16} className="text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-blue-700">Analisis kompetitif pasar ban heavy equipment</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle size={16} className="text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-blue-700">Optimasi psikologis harga (price ending in 999,000)</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle size={16} className="text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-blue-700">Keseimbangan margin keuntungan dan daya tarik penawaran</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle size={16} className="text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-blue-700">Perhitungan nilai persepsi pelanggan terhadap bundling</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle size={16} className="text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-blue-700">Analisis data historis penjualan dan tren pasar</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle size={16} className="text-blue-600 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-blue-700">Perbandingan dengan harga kompetitor untuk ban 27.00R49</span>
                      </div>
                    </div>
                  </div>

                  {competitorPrices.length > 0 && (
                    <div className="bg-blue-50 p-3 rounded-lg mb-3">
                      <h4 className="font-medium text-blue-800 mb-1">Analisis Harga Kompetitor:</h4>
                      <div className="space-y-2">
                        <p className="text-sm text-blue-700">
                          <span className="font-medium">Harga rata-rata pasar:</span> {formatCurrency(getPriceStatistics(competitorPrices).avgPrice)}
                        </p>
                        <p className="text-sm text-blue-700">
                          <span className="font-medium">Harga rata-rata premium brands:</span> {formatCurrency(getPriceStatistics(competitorPrices).premiumAvgPrice)}
                        </p>
                        <p className="text-sm text-blue-700">
                          <span className="font-medium">Harga rata-rata economy brands:</span> {formatCurrency(getPriceStatistics(competitorPrices).economyAvgPrice)}
                        </p>

                        <div className="mt-2">
                          <p className="text-sm font-medium text-blue-800">Top 3 Brands by Price:</p>
                          <ul className="text-xs text-blue-700 list-disc pl-5 space-y-0.5 mt-1">
                            {calculateAveragePriceByBrand(competitorPrices)
                              .slice(0, 3)
                              .map((item, index) => (
                                <li key={index}>
                                  {item.brand}: {formatCurrency(item.avgPrice)} ({item.count} units)
                                </li>
                              ))}
                          </ul>
                        </div>

                        <p className="text-sm text-blue-700 mt-2">
                          <span className="font-medium">Posisi harga optimal bundling:</span> {
                            calculationResult.optimalSellPrice > getPriceStatistics(competitorPrices).avgPrice
                              ? 'Di atas rata-rata pasar (premium positioning)'
                              : calculationResult.optimalSellPrice < getPriceStatistics(competitorPrices).economyAvgPrice
                                ? 'Di bawah rata-rata economy brands (value positioning)'
                                : 'Kompetitif dengan rata-rata pasar (balanced positioning)'
                          }
                        </p>
                      </div>
                    </div>
                  )}

                  <h4 className="font-medium text-green-800 mb-1">Keuntungan Bisnis:</h4>
                  <ul className="text-sm text-green-700 list-disc pl-5 space-y-1">
                    <li>Harga jual optimal: <span className="font-bold">{formatCurrency(calculationResult.optimalSellPrice)}</span></li>
                    <li>Margin keuntungan: <span className="font-bold">{calculationResult.optimalMargin.toFixed(2)}%</span></li>
                    <li>Total profit: <span className="font-bold">{formatCurrency(calculationResult.optimalSellPrice - ((calculationResult.mainProductPrice * calculationResult.minimumMainProductQty) + ((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty)))}</span></li>
                    {calculationResult.minimumMainProductQty > 1 && (
                      <li>Harga per unit ban 27.00R49: <span className="font-bold">{formatCurrency(calculationResult.pricePerPiece)}</span></li>
                    )}
                    <li>Profit per unit: <span className="font-bold">{formatCurrency((calculationResult.optimalSellPrice - ((calculationResult.mainProductPrice * calculationResult.minimumMainProductQty) + ((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty))) / calculationResult.minimumMainProductQty)}</span></li>
                    <li>Peningkatan persepsi nilai dengan strategi "produk gratis"</li>
                    <li>Potensi peningkatan volume penjualan Ban 27.00 R 49</li>
                    <li>Percepatan perputaran stok produk sekunder</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Competitor Price Table */}
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-6">
              <div className="bg-gray-100 px-4 py-2 border-b flex justify-between items-center">
                <h3 className="font-medium text-gray-800">Harga Ban 27.00R49 Kompetitor</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={loadCompetitorPrices}
                    className="p-1 text-blue-600 hover:text-blue-800"
                    title="Refresh data"
                  >
                    <Loader2 size={16} className={isLoadingPrices ? "animate-spin" : ""} />
                  </button>
                  <BarChart size={16} className="text-blue-600" title="Price statistics" />
                </div>
              </div>

              <div className="p-4">
                {/* Search and Sort */}
                <div className="flex flex-col md:flex-row justify-between mb-4 space-y-2 md:space-y-0">
                  <div className="relative w-full md:w-64">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search customer, brand..."
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">Sort by:</span>
                    <select
                      className="border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      value={sortField}
                      onChange={(e) => setSortField(e.target.value)}
                    >
                      <option value="price">Price</option>
                      <option value="brand">Brand</option>
                      <option value="customer">Customer</option>
                      <option value="date">Date</option>
                    </select>

                    <button
                      onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
                      className="p-2 border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                      <ArrowUpDown size={16} className="text-gray-500" />
                    </button>
                  </div>
                </div>

                {/* Price Statistics */}
                {competitorPrices.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <h4 className="text-xs font-medium text-blue-800">Average Price</h4>
                      <p className="text-lg font-bold text-blue-900">
                        {formatCurrency(getPriceStatistics(competitorPrices).avgPrice)}
                      </p>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg">
                      <h4 className="text-xs font-medium text-green-800">Premium Brands Avg</h4>
                      <p className="text-lg font-bold text-green-900">
                        {formatCurrency(getPriceStatistics(competitorPrices).premiumAvgPrice)}
                      </p>
                    </div>
                    <div className="bg-amber-50 p-3 rounded-lg">
                      <h4 className="text-xs font-medium text-amber-800">Economy Brands Avg</h4>
                      <p className="text-lg font-bold text-amber-900">
                        {formatCurrency(getPriceStatistics(competitorPrices).economyAvgPrice)}
                      </p>
                    </div>
                    <div className="bg-purple-50 p-3 rounded-lg">
                      <h4 className="text-xs font-medium text-purple-800">Price Range</h4>
                      <p className="text-sm font-bold text-purple-900">
                        {formatCurrency(getPriceStatistics(competitorPrices).minPrice)} - {formatCurrency(getPriceStatistics(competitorPrices).maxPrice)}
                      </p>
                    </div>
                  </div>
                )}

                {/* Table */}
                <div className="overflow-x-auto">
                  {isLoadingPrices ? (
                    <div className="py-8 text-center">
                      <Loader2 size={24} className="mx-auto animate-spin text-blue-600 mb-2" />
                      <p className="text-gray-500">Loading competitor prices...</p>
                    </div>
                  ) : priceError ? (
                    <div className="py-4 text-center">
                      <p className="text-red-500">{priceError}</p>
                    </div>
                  ) : filteredPrices.length === 0 ? (
                    <div className="py-8 text-center">
                      <p className="text-gray-500">No competitor prices found.</p>
                    </div>
                  ) : (
                    <>
                      <div className="flex justify-between items-center mb-2">
                        <div className="text-sm text-gray-500">
                          Showing {filteredPrices.length} records for 27.00R49 tires
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500">Show:</span>
                          <select
                            className="border border-gray-300 rounded-md py-1 px-2 text-sm"
                            value={itemsPerPage}
                            onChange={(e) => setItemsPerPage(Number(e.target.value))}
                          >
                            <option value={5}>5</option>
                            <option value={10}>10</option>
                            <option value={20}>20</option>
                            <option value={50}>50</option>
                          </select>
                        </div>
                      </div>

                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Customer
                            </th>
                            <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Brand
                            </th>
                            <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Supplier
                            </th>
                            <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Price
                            </th>
                            <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Date
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {filteredPrices
                            .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                            .map((item) => (
                              <tr key={item.id} className="hover:bg-gray-50">
                                <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-900">
                                  {item.customer}
                                </td>
                                <td className="px-3 py-2 whitespace-nowrap text-xs">
                                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                    ['BRIDGESTONE', 'GOODYEAR', 'MICHELIN', 'GOOD YEAR'].includes(item.brand.toUpperCase())
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-blue-100 text-blue-800'
                                  }`}>
                                    {item.brand}
                                  </span>
                                </td>
                                <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500">
                                  {item.supplier}
                                </td>
                                <td className="px-3 py-2 whitespace-nowrap text-xs font-medium">
                                  {item.currency === 'USD' ? (
                                    <div className="flex items-center">
                                      <DollarSign size={12} className="text-green-600 mr-1" />
                                      <span>{formatCurrency(item.price)}</span>
                                    </div>
                                  ) : (
                                    formatCurrency(item.price)
                                  )}
                                </td>
                                <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500">
                                  {item.date}
                                </td>
                              </tr>
                            ))}
                        </tbody>
                      </table>

                      {/* Pagination */}
                      {filteredPrices.length > itemsPerPage && (
                        <div className="flex justify-between items-center mt-4">
                          <div className="text-sm text-gray-500">
                            Showing {Math.min((currentPage - 1) * itemsPerPage + 1, filteredPrices.length)} to {Math.min(currentPage * itemsPerPage, filteredPrices.length)} of {filteredPrices.length}
                          </div>
                          <div className="flex space-x-1">
                            <button
                              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                              disabled={currentPage === 1}
                              className={`px-2 py-1 text-xs rounded ${
                                currentPage === 1
                                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                  : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
                              }`}
                            >
                              Previous
                            </button>

                            {Array.from({ length: Math.min(5, Math.ceil(filteredPrices.length / itemsPerPage)) }).map((_, i) => {
                              const pageNum = i + 1;
                              return (
                                <button
                                  key={i}
                                  onClick={() => setCurrentPage(pageNum)}
                                  className={`px-2 py-1 text-xs rounded ${
                                    currentPage === pageNum
                                      ? 'bg-blue-600 text-white'
                                      : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
                                  }`}
                                >
                                  {pageNum}
                                </button>
                              );
                            })}

                            <button
                              onClick={() => setCurrentPage(prev => Math.min(prev + 1, Math.ceil(filteredPrices.length / itemsPerPage)))}
                              disabled={currentPage === Math.ceil(filteredPrices.length / itemsPerPage)}
                              className={`px-2 py-1 text-xs rounded ${
                                currentPage === Math.ceil(filteredPrices.length / itemsPerPage)
                                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                  : 'bg-blue-50 text-blue-600 hover:bg-blue-100'
                              }`}
                            >
                              Next
                            </button>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* Brand Average Prices */}
                {competitorPrices.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Average Price by Brand</h4>
                    <div className="space-y-2">
                      {calculateAveragePriceByBrand(competitorPrices).map((item) => (
                        <div key={item.brand} className="relative">
                          <div className="flex justify-between mb-1">
                            <span className="text-xs font-medium text-gray-700">
                              {item.brand} ({item.count} units)
                            </span>
                            <span className="text-xs font-medium text-gray-700">
                              {formatCurrency(item.avgPrice)}
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5">
                            <div
                              className={`h-1.5 rounded-full ${
                                ['BRIDGESTONE', 'GOODYEAR', 'MICHELIN', 'GOOD YEAR'].includes(item.brand.toUpperCase())
                                  ? 'bg-green-600'
                                  : 'bg-blue-600'
                              }`}
                              style={{
                                width: `${(item.avgPrice / getPriceStatistics(competitorPrices).maxPrice) * 100}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Detailed Calculation Steps */}
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div className="bg-gray-100 px-4 py-2 border-b">
                <h3 className="font-medium text-gray-800">Langkah-langkah Perhitungan Detail</h3>
              </div>
              <div className="p-4 space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Langkah 1: Identifikasi Biaya Produk</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">Harga Modal Produk Utama: {formatCurrency(calculationResult.mainProductPrice)}</p>
                    <p className="text-sm text-gray-600">Harga Modal Produk Sekunder: {formatCurrency(calculationResult.secondaryProductPrice)} per unit</p>
                    <p className="text-sm text-gray-600">Jumlah Produk Sekunder: {calculationResult.secondaryProductQty} unit</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Langkah 2: Hitung Total Biaya Produk Sekunder</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">Total Biaya Produk Sekunder = Harga Produk Sekunder × Jumlah</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.secondaryProductPrice)} × {calculationResult.secondaryProductQty}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.secondaryProductPrice * calculationResult.secondaryProductQty)}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Langkah 3: Hitung Faktor Margin untuk Produk Utama</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">Faktor Margin Produk Utama = 1 + (Margin Produk Utama ÷ 100)</p>
                    <p className="text-sm text-gray-600">= 1 + ({calculationResult.mainProductMargin} ÷ 100)</p>
                    <p className="text-sm text-gray-600">= {(1 + calculationResult.mainProductMargin/100).toFixed(2)}</p>
                    <p className="text-sm text-gray-600 mt-1 italic">Catatan: Faktor margin digunakan untuk menghitung keuntungan per unit produk utama</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Langkah 4: Hitung Margin untuk Produk Sekunder</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">Faktor Margin Produk Sekunder = 1 + (Margin Produk Sekunder ÷ 100)</p>
                    <p className="text-sm text-gray-600">= 1 + ({calculationResult.secondaryProductMargin} ÷ 100)</p>
                    <p className="text-sm text-gray-600">= {(1 + calculationResult.secondaryProductMargin/100).toFixed(2)}</p>
                    <p className="text-sm text-gray-600 mt-1 italic">Catatan: Faktor margin digunakan untuk menghitung keuntungan per unit produk sekunder</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Langkah 5: Hitung Pendapatan Efektif Produk Utama</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">Pendapatan Efektif Produk Utama = Harga Modal Produk Utama × Faktor Margin Produk Utama</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductPrice)} × {(1 + calculationResult.mainProductMargin/100).toFixed(2)}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductPrice * (1 + calculationResult.mainProductMargin/100))}</p>
                    <p className="text-sm text-gray-600 mt-1 italic">Catatan: Ini adalah pendapatan yang diharapkan dari setiap unit produk utama dengan margin kustom</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Langkah 6: Hitung Jumlah Minimum Produk Utama</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">1. Hitung Pendapatan Efektif Produk Utama (dengan margin kustom)</p>
                    <p className="text-sm text-gray-600">Pendapatan Efektif Produk Utama = Harga Modal Produk Utama × (1 + Margin Produk Utama/100)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductPrice)} × (1 + {calculationResult.mainProductMargin}/100)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductPrice * (1 + calculationResult.mainProductMargin/100))}</p>

                    <p className="text-sm text-gray-600 mt-2">2. Hitung Profit Produk Sekunder</p>
                    <p className="text-sm text-gray-600">Profit Produk Sekunder = Harga Produk Sekunder × (Margin Produk Sekunder/100)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.secondaryProductPrice)} × ({calculationResult.secondaryProductMargin}/100)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.secondaryProductPrice * (calculationResult.secondaryProductMargin/100))}</p>

                    <p className="text-sm text-gray-600 mt-2">3. Hitung Biaya Efektif Produk Sekunder</p>
                    <p className="text-sm text-gray-600">Biaya Efektif Produk Sekunder = Harga Produk Sekunder - Profit Produk Sekunder</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.secondaryProductPrice)} - {formatCurrency(calculationResult.secondaryProductPrice * (calculationResult.secondaryProductMargin/100))}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.secondaryProductPrice - (calculationResult.secondaryProductPrice * (calculationResult.secondaryProductMargin/100)))}</p>

                    <p className="text-sm text-gray-600 mt-2">4. Hitung Jumlah Minimum Produk Utama Per Unit Produk Sekunder</p>
                    <p className="text-sm text-gray-600">Profit Per Unit Produk Utama = Harga Modal Produk Utama × (Margin Produk Utama/100)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductPrice)} × ({calculationResult.mainProductMargin}/100)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductPrice * (calculationResult.mainProductMargin/100))}</p>

                    <p className="text-sm text-gray-600 mt-2">Jumlah Min Produk Utama Per Produk Sekunder = Biaya Efektif Produk Sekunder ÷ Profit Per Unit Produk Utama</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.secondaryProductPrice - (calculationResult.secondaryProductPrice * (calculationResult.secondaryProductMargin/100)))} ÷ {formatCurrency(calculationResult.mainProductPrice * (calculationResult.mainProductMargin/100))}</p>
                    <p className="text-sm text-gray-600">= {((calculationResult.secondaryProductPrice - (calculationResult.secondaryProductPrice * (calculationResult.secondaryProductMargin/100))) / (calculationResult.mainProductPrice * (calculationResult.mainProductMargin/100))).toFixed(2)} unit</p>
                    <p className="text-sm text-gray-600">Dibulatkan ke atas: {Math.ceil((calculationResult.secondaryProductPrice - (calculationResult.secondaryProductPrice * (calculationResult.secondaryProductMargin/100))) / (calculationResult.mainProductPrice * (calculationResult.mainProductMargin/100)))} unit per produk sekunder</p>

                    <p className="text-sm text-gray-600 mt-2">5. Hitung Total Jumlah Minimum Produk Utama</p>
                    <p className="text-sm text-gray-600">Total Jumlah Min Produk Utama = Jumlah Min Produk Utama Per Produk Sekunder × Jumlah Produk Sekunder</p>
                    <p className="text-sm text-gray-600">= {Math.ceil((calculationResult.secondaryProductPrice - (calculationResult.secondaryProductPrice * (calculationResult.secondaryProductMargin/100))) / (calculationResult.mainProductPrice * (calculationResult.mainProductMargin/100)))} × {calculationResult.secondaryProductQty}</p>
                    <p className="text-sm text-gray-600">= {calculationResult.minimumMainProductQty} unit</p>

                    <div className="mt-2 p-2 bg-yellow-50 rounded-md">
                      <p className="text-sm font-medium text-yellow-800">Catatan Penting:</p>
                      <p className="text-sm text-yellow-700">Jumlah minimum produk utama meningkat secara proporsional dengan jumlah produk sekunder. Jika Anda menambah jumlah produk sekunder, jumlah minimum produk utama yang diperlukan juga akan meningkat untuk mempertahankan margin yang diinginkan.</p>
                      <p className="text-sm text-yellow-700 mt-1">Mengubah margin produk utama atau produk sekunder akan mempengaruhi jumlah minimum produk utama yang diperlukan. Margin produk utama yang lebih tinggi akan mengurangi jumlah minimum, sedangkan margin produk sekunder yang lebih tinggi akan mengurangi biaya efektif produk sekunder.</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Langkah 7: Hitung Total Biaya dan Margin Keuntungan</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600 font-medium">A. Hitung Biaya Produk Utama:</p>
                    <p className="text-sm text-gray-600">Biaya Produk Utama = Harga Modal Produk Utama × Jumlah Minimum</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductPrice)} × {calculationResult.minimumMainProductQty}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductPrice * calculationResult.minimumMainProductQty)}</p>

                    <p className="text-sm text-gray-600 mt-3 font-medium">B. Hitung Biaya Produk Sekunder (dengan margin):</p>
                    <p className="text-sm text-gray-600">Harga Jual Produk Sekunder = Harga Modal × (1 + Margin/100)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.secondaryProductPrice)} × (1 + {calculationResult.secondaryProductMargin}/100)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100))}</p>

                    <p className="text-sm text-gray-600">Total Biaya Produk Sekunder = Harga Jual Produk Sekunder × Jumlah</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100))} × {calculationResult.secondaryProductQty}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty)}</p>

                    <p className="text-sm text-gray-600 mt-3 font-medium">C. Hitung Total Biaya:</p>
                    <p className="text-sm text-gray-600">Total Biaya = Biaya Produk Utama + Total Biaya Produk Sekunder</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductPrice * calculationResult.minimumMainProductQty)} + {formatCurrency((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty)}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency((calculationResult.mainProductPrice * calculationResult.minimumMainProductQty) + ((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty))}</p>

                    <p className="text-sm text-gray-600 mt-3 font-medium">D. Hitung Total Profit:</p>
                    <p className="text-sm text-gray-600">Total Profit = Harga Jual Optimal - Total Biaya</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.optimalSellPrice)} - {formatCurrency((calculationResult.mainProductPrice * calculationResult.minimumMainProductQty) + ((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty))}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.optimalSellPrice - ((calculationResult.mainProductPrice * calculationResult.minimumMainProductQty) + ((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty)))}</p>

                    <div className="mt-2 p-2 bg-green-50 rounded-md">
                      <p className="text-sm font-medium text-green-800">Total Profit: {formatCurrency(calculationResult.optimalSellPrice - ((calculationResult.mainProductPrice * calculationResult.minimumMainProductQty) + ((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty)))}</p>
                      <p className="text-sm text-green-700">Profit per unit Ban 27.00 R 49: {formatCurrency((calculationResult.optimalSellPrice - ((calculationResult.mainProductPrice * calculationResult.minimumMainProductQty) + ((calculationResult.secondaryProductPrice * (1 + calculationResult.secondaryProductMargin/100)) * calculationResult.secondaryProductQty))) / calculationResult.minimumMainProductQty)}</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Langkah 8: Optimasi Harga dengan Kecerdasan Buatan</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <div className="flex items-center mb-2">
                      <Sparkles size={16} className="text-blue-600 mr-2" />
                      <p className="text-sm font-medium text-blue-800">Analisis DeepSeek AI</p>
                    </div>

                    <p className="text-sm text-gray-600">Model AI menganalisis berbagai faktor untuk menentukan harga jual optimal:</p>

                    <div className="mt-2 p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm font-medium text-blue-800">Data Input Analisis AI:</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-xs font-medium text-blue-800 mb-1">Data Produk & Bundling:</p>
                          <ul className="text-xs text-blue-700 list-disc pl-4 space-y-0.5">
                            <li>Produk Utama: {mainProduct?.materialDescription}</li>
                            <li>Harga Modal Produk Utama: {formatCurrency(calculationResult.mainProductPrice)}</li>
                            <li>Produk Sekunder: {secondaryProducts[0]?.product.materialDescription}</li>
                            <li>Harga Modal Produk Sekunder: {formatCurrency(calculationResult.secondaryProductPrice)}</li>
                            <li>Jumlah Produk Sekunder: {calculationResult.secondaryProductQty}</li>
                            <li>Jumlah Minimum Produk Utama: {calculationResult.minimumMainProductQty}</li>
                            <li>Main Product Margin: {calculationResult.mainProductMargin}%</li>
                            <li>Secondary Product Margin: {calculationResult.secondaryProductMargin}%</li>
                            <li>Total Biaya Bundle: {formatCurrency((calculationResult.mainProductPrice * calculationResult.minimumMainProductQty) + (calculationResult.secondaryProductPrice * calculationResult.secondaryProductQty))}</li>
                          </ul>
                        </div>

                        {competitorPrices.length > 0 && (
                          <div>
                            <p className="text-xs font-medium text-blue-800 mb-1">Data Harga Kompetitor 27.00R49:</p>
                            <ul className="text-xs text-blue-700 list-disc pl-4 space-y-0.5">
                              <li>Harga Rata-rata Pasar: {formatCurrency(getPriceStatistics(competitorPrices).avgPrice)}</li>
                              <li>Harga Premium Brands: {formatCurrency(getPriceStatistics(competitorPrices).premiumAvgPrice)}</li>
                              <li>Harga Economy Brands: {formatCurrency(getPriceStatistics(competitorPrices).economyAvgPrice)}</li>
                              <li>Harga Tertinggi: {formatCurrency(getPriceStatistics(competitorPrices).maxPrice)}</li>
                              <li>Harga Terendah: {formatCurrency(getPriceStatistics(competitorPrices).minPrice)}</li>
                              <li>Jumlah Data Kompetitor: {competitorPrices.length} records</li>
                              <li>Top Brand: {calculateAveragePriceByBrand(competitorPrices)[0]?.brand} ({formatCurrency(calculateAveragePriceByBrand(competitorPrices)[0]?.avgPrice)})</li>
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 mt-3">Harga Optimal dari AI = {formatCurrency(calculationResult.optimalSellPrice)}</p>
                    <p className="text-sm text-gray-600">Margin Keuntungan Optimal = {calculationResult.optimalMargin.toFixed(2)}%</p>

                    {calculationResult.aiReasoning ? (
                      <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm font-medium text-blue-800">Penalaran AI:</p>
                        <p className="text-sm text-blue-700 mt-1">{calculationResult.aiReasoning}</p>
                      </div>
                    ) : (
                      <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                        <p className="text-sm font-medium text-blue-800">Wawasan AI:</p>
                        <p className="text-sm text-blue-700">Harga {formatCurrency(calculationResult.optimalSellPrice)} memberikan keseimbangan optimal antara daya saing pasar dan profitabilitas. Harga ini mempertimbangkan faktor psikologis dengan angka 999,000 di akhir, yang cenderung dipersepsikan lebih terjangkau oleh pelanggan.</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
