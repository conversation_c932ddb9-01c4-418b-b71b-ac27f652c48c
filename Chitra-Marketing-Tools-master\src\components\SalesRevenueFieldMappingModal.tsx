import React from 'react';
import { X } from 'lucide-react';

interface SalesRevenueFieldMappingModalProps {
  isOpen: boolean;
  onClose: () => void;
  availableFields: string[];
  fieldMapping: Record<string, string>;
  setFieldMapping: (mapping: Record<string, string>) => void;
  onConfirm: () => void;
  sampleData: Record<string, any> | null;
}

const SalesRevenueFieldMappingModal: React.FC<SalesRevenueFieldMappingModalProps> = ({
  isOpen,
  onClose,
  availableFields,
  fieldMapping,
  setFieldMapping,
  onConfirm,
  sampleData
}) => {
  if (!isOpen) return null;

  const requiredFields = [
    { key: 'customerName', label: 'Customer Name' },
    { key: 'salesman', label: 'Salesman' },
    { key: 'materialDescription', label: 'Material Description' },
    { key: 'qty', label: 'Quantity' },
    { key: 'revenueInDocCurr', label: 'Revenue in Doc Curr' },
    { key: 'billingDate', label: 'Billing Date' },
    { key: 'poDate', label: 'PO Date' }
  ];

  const handleFieldChange = (fieldKey: string, mappedField: string) => {
    setFieldMapping({
      ...fieldMapping,
      [fieldKey]: mappedField
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Map Fields</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        <p className="mb-4 text-gray-600">
          Please map the fields from your imported file to the required fields in our system.
        </p>

        {sampleData && (
          <div className="mb-4 p-3 bg-gray-50 rounded-md">
            <h3 className="font-medium mb-2">Sample Data Preview:</h3>
            <div className="text-sm overflow-x-auto">
              <pre>{JSON.stringify(sampleData, null, 2)}</pre>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {requiredFields.map(field => (
            <div key={field.key} className="grid grid-cols-3 gap-4 items-center">
              <div>
                <label className="font-medium">{field.label}:</label>
              </div>
              <div className="col-span-2">
                <select
                  className="w-full p-2 border rounded-md"
                  value={fieldMapping[field.key] || ''}
                  onChange={(e) => handleFieldChange(field.key, e.target.value)}
                >
                  <option value="">-- Select Field --</option>
                  {availableFields.map(field => (
                    <option key={field} value={field}>
                      {field}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Import Data
          </button>
        </div>
      </div>
    </div>
  );
};

export default SalesRevenueFieldMappingModal;
