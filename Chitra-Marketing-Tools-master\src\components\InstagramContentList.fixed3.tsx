      {/* Post View Modal */}
      {showPostModal && selectedPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <div className="flex items-center">
                <Instagram size={20} className="text-pink-500 mr-2" />
                <h3 className="text-lg font-medium">Detail Konten Instagram</h3>
              </div>
              <button
                onClick={() => setShowPostModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-4 space-y-4">
              {/* Post preview */}
              <div className="border rounded-lg overflow-hidden mb-4">
                <div className="bg-gray-50 p-3 border-b flex items-center">
                  <Instagram size={16} className="text-pink-500 mr-2" />
                  <span className="font-medium">Preview Instagram Post</span>
                </div>
                <div className="p-4 bg-white">
                  <div className="flex items-center mb-3">
                    <div className="w-8 h-8 rounded-full bg-pink-100 flex items-center justify-center text-pink-500 mr-2">
                      <Instagram size={16} />
                    </div>
                    <div>
                      <div className="font-medium text-sm">chitraparatama</div>
                      <div className="text-xs text-gray-500">PT Chitra Paratama</div>
                    </div>
                  </div>

                  <div className="bg-gray-100 rounded-lg h-48 flex items-center justify-center mb-3">
                    {getContentTypeIcon(selectedPost.contentType)}
                    <span className="ml-2 text-gray-500 text-sm">
                      {selectedPost.contentType === ContentType.IMAGE ? 'Preview Gambar' :
                       selectedPost.contentType === ContentType.VIDEO ? 'Preview Video' :
                       selectedPost.contentType === ContentType.CAROUSEL ? 'Preview Carousel' :
                       selectedPost.contentType === ContentType.REEL ? 'Preview Reel' : 'Preview Story'}
                    </span>
                  </div>

                  <div className="text-sm mb-2 whitespace-pre-line">
                    {selectedPost.caption}
                  </div>

                  <div className="text-xs text-blue-500">
                    {selectedPost.hashtags.map(tag => `#${tag}`).join(' ')}
                  </div>
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-700">Judul</h4>
                  <button
                    onClick={() => copyToClipboard(selectedPost.title || 'Tanpa Judul')}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title="Salin judul"
                  >
                    <Copy size={14} />
                  </button>
                </div>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                  {selectedPost.title || 'Tanpa Judul'}
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-700">Caption</h4>
                  <button
                    onClick={() => copyToClipboard(selectedPost.caption)}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title="Salin caption"
                  >
                    <Copy size={14} />
                  </button>
                </div>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm whitespace-pre-line">
                  {selectedPost.caption}
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-700">Hashtag</h4>
                  <button
                    onClick={() => copyToClipboard(selectedPost.hashtags.map(tag => `#${tag}`).join(' '))}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title="Salin hashtag"
                  >
                    <Copy size={14} />
                  </button>
                </div>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                  <div className="flex flex-wrap gap-1">
                    {selectedPost.hashtags.map((tag, index) => (
                      <span key={index} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {selectedPost.imageDescription && (
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <h4 className="text-sm font-medium text-gray-700">Deskripsi Gambar</h4>
                    <button
                      onClick={() => copyToClipboard(selectedPost.imageDescription || '')}
                      className="text-blue-600 hover:text-blue-800 p-1"
                      title="Salin deskripsi gambar"
                    >
                      <Copy size={14} />
                    </button>
                  </div>
                  <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {selectedPost.imageDescription}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">Tipe Konten</h4>
                  <div className="flex items-center p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {getContentTypeIcon(selectedPost.contentType)}
                    <span className="ml-2">
                      {selectedPost.contentType.charAt(0).toUpperCase() + selectedPost.contentType.slice(1)}
                    </span>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">Status</h4>
                  <div className="flex items-center p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {getStatusIcon(selectedPost.status)}
                    <span className="ml-2">{getStatusLabel(selectedPost.status)}</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">Tanggal Dibuat</h4>
                  <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {formatDate(selectedPost.createdAt)}
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">
                    {selectedPost.status === PostStatus.PUBLISHED ? 'Tanggal Publikasi' : 'Tanggal Terjadwal'}
                  </h4>
                  <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {selectedPost.status === PostStatus.PUBLISHED
                      ? formatDate(selectedPost.publishedDate)
                      : formatDate(selectedPost.scheduledDate)}
                  </div>
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex justify-between pt-4 border-t border-gray-200">
                <div className="space-x-2">
                  {selectedPost.status === PostStatus.DRAFT && (
                    <button
                      onClick={() => handleStatusChange(selectedPost, PostStatus.SCHEDULED)}
                      className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                      <Clock size={16} className="inline mr-1" />
                      Jadwalkan
                    </button>
                  )}
                  {(selectedPost.status === PostStatus.DRAFT || selectedPost.status === PostStatus.SCHEDULED) && (
                    <button
                      onClick={() => handleStatusChange(selectedPost, PostStatus.PUBLISHED)}
                      className="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                    >
                      <CheckCircle size={16} className="inline mr-1" />
                      Publikasikan
                    </button>
                  )}
                  {selectedPost.status !== PostStatus.ARCHIVED && (
                    <button
                      onClick={() => handleStatusChange(selectedPost, PostStatus.ARCHIVED)}
                      className="px-3 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                    >
                      Arsipkan
                    </button>
                  )}
                </div>
                <div className="space-x-2">
                  {onEditPost && (
                    <button
                      onClick={() => {
                        onEditPost(selectedPost);
                        setShowPostModal(false);
                      }}
                      className="px-3 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                    >
                      <Edit size={16} className="inline mr-1" />
                      Edit
                    </button>
                  )}
                  {onImproveContent && (
                    <button
                      onClick={() => {
                        onImproveContent(selectedPost);
                        setShowPostModal(false);
                      }}
                      className="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                    >
                      <Sparkles size={16} className="inline mr-1" />
                      Tingkatkan Konten
                    </button>
                  )}
                  <button
                    onClick={() => setShowPostModal(false)}
                    className="px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Tutup
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus Konten</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus konten ini? Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeletePost} className="bg-red-500 hover:bg-red-600">
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
