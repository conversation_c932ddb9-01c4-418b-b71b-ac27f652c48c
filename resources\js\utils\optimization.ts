/**
 * Performance optimization utilities
 */

/**
 * Lazy load component with loading state
 */
export function lazyComponent(importFn: () => Promise<any>, fallback?: any) {
    return defineAsyncComponent({
        loader: importFn,
        loadingComponent: fallback || {
            template: `
                <div class="flex items-center justify-center p-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-gray-600">Loading...</span>
                </div>
            `
        },
        errorComponent: {
            template: `
                <div class="flex items-center justify-center p-8 text-red-600">
                    <span>Failed to load component. Please try again.</span>
                </div>
            `
        },
        delay: 200,
        timeout: 10000
    });
}

/**
 * Debounce function for search inputs
 */
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    
    return (...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

/**
 * Throttle function for scroll events
 */
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    
    return (...args: Parameters<T>) => {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Intersection Observer for lazy loading
 */
export function createIntersectionObserver(
    callback: (entries: IntersectionObserverEntry[]) => void,
    options?: IntersectionObserverInit
) {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
        return null;
    }

    return new IntersectionObserver(callback, {
        root: null,
        rootMargin: '50px',
        threshold: 0.1,
        ...options
    });
}

/**
 * Virtual scrolling for large lists
 */
export function useVirtualScroll<T>(
    items: Ref<T[]>,
    itemHeight: number,
    containerHeight: number
) {
    const scrollTop = ref(0);
    const startIndex = computed(() => Math.floor(scrollTop.value / itemHeight));
    const endIndex = computed(() => 
        Math.min(
            startIndex.value + Math.ceil(containerHeight / itemHeight) + 1,
            items.value.length
        )
    );
    
    const visibleItems = computed(() => 
        items.value.slice(startIndex.value, endIndex.value)
    );
    
    const totalHeight = computed(() => items.value.length * itemHeight);
    const offsetY = computed(() => startIndex.value * itemHeight);

    const onScroll = (event: Event) => {
        const target = event.target as HTMLElement;
        scrollTop.value = target.scrollTop;
    };

    return {
        visibleItems,
        totalHeight,
        offsetY,
        onScroll
    };
}

/**
 * Image lazy loading with placeholder
 */
export function useLazyImage(src: string, placeholder?: string) {
    const isLoaded = ref(false);
    const isError = ref(false);
    const currentSrc = ref(placeholder || '');

    const img = new Image();
    
    img.onload = () => {
        currentSrc.value = src;
        isLoaded.value = true;
    };
    
    img.onerror = () => {
        isError.value = true;
    };
    
    img.src = src;

    return {
        src: currentSrc,
        isLoaded,
        isError
    };
}

/**
 * Memory usage monitoring
 */
export function monitorMemoryUsage() {
    if (typeof window === 'undefined' || !('performance' in window)) {
        return null;
    }

    const memory = (performance as any).memory;
    
    if (!memory) {
        return null;
    }

    return {
        used: Math.round(memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
    };
}

/**
 * Bundle size analyzer
 */
export function analyzeBundleSize() {
    if (typeof window === 'undefined') return;

    const scripts = Array.from(document.querySelectorAll('script[src]'));
    const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'));
    
    console.group('📦 Bundle Analysis');
    
    scripts.forEach((script: any) => {
        if (script.src.includes('/build/')) {
            console.log(`JS: ${script.src.split('/').pop()}`);
        }
    });
    
    styles.forEach((style: any) => {
        if (style.href.includes('/build/')) {
            console.log(`CSS: ${style.href.split('/').pop()}`);
        }
    });
    
    console.log(`Total JS files: ${scripts.length}`);
    console.log(`Total CSS files: ${styles.length}`);
    console.groupEnd();
}

/**
 * Preload critical resources
 */
export function preloadResource(href: string, as: string, type?: string) {
    if (typeof document === 'undefined') return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    
    if (type) {
        link.type = type;
    }
    
    document.head.appendChild(link);
}

/**
 * Critical CSS inlining
 */
export function inlineCriticalCSS(css: string) {
    if (typeof document === 'undefined') return;

    const style = document.createElement('style');
    style.textContent = css;
    document.head.appendChild(style);
}

/**
 * Service Worker registration
 */
export async function registerServiceWorker(swPath: string = '/sw.js') {
    if (typeof navigator === 'undefined' || !('serviceWorker' in navigator)) {
        console.log('Service Worker not supported');
        return null;
    }

    try {
        const registration = await navigator.serviceWorker.register(swPath);
        console.log('Service Worker registered:', registration);
        return registration;
    } catch (error) {
        console.error('Service Worker registration failed:', error);
        return null;
    }
}

/**
 * Resource hints for better performance
 */
export function addResourceHints() {
    if (typeof document === 'undefined') return;

    // DNS prefetch for external domains
    const dnsPrefetch = [
        '//fonts.googleapis.com',
        '//fonts.gstatic.com'
    ];

    dnsPrefetch.forEach(domain => {
        const link = document.createElement('link');
        link.rel = 'dns-prefetch';
        link.href = domain;
        document.head.appendChild(link);
    });

    // Preconnect to critical origins
    const preconnect = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com'
    ];

    preconnect.forEach(origin => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = origin;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
    });
}

// Auto-initialize optimizations
if (typeof window !== 'undefined') {
    // Add resource hints
    addResourceHints();
    
    // Monitor memory usage in development
    if (import.meta.env.DEV) {
        setInterval(() => {
            const memory = monitorMemoryUsage();
            if (memory && memory.used > memory.limit * 0.8) {
                console.warn('🚨 High memory usage detected:', memory);
            }
        }, 30000); // Check every 30 seconds
    }
}

// Vue 3 imports
import { defineAsyncComponent, ref, computed, type Ref } from 'vue';
