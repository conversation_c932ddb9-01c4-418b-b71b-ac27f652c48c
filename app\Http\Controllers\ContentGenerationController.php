<?php

namespace App\Http\Controllers;

use App\Services\OpenRouterService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class ContentGenerationController extends Controller
{
    private OpenRouterService $openRouterService;

    public function __construct(OpenRouterService $openRouterService)
    {
        $this->openRouterService = $openRouterService;
    }

    /**
     * Generate social media content
     */
    public function generateContent(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'platform' => 'required|string|in:Instagram,Facebook,LinkedIn,Twitter',
            'contentType' => 'required|string|in:Post,Story,Reel,Video',
            'tone' => 'string|in:professional,casual,enthusiastic,educational,formal',
            'targetAudience' => 'string|max:500',
            'productDetails' => 'string|max:1000',
            'campaignGoals' => 'string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->openRouterService->generateContent($request->all());
            
            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate content',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate monthly content plan
     */
    public function generateMonthlyPlan(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'month' => 'required|string',
            'year' => 'required|integer|min:2024|max:2030',
            'platform' => 'required|string|in:Instagram,Facebook,LinkedIn,Twitter',
            'postsPerWeek' => 'integer|min:1|max:10',
            'contentMix' => 'array',
            'targetAudience' => 'string|max:500',
            'brandGuidelines' => 'string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->openRouterService->generateMonthlyPlan($request->all());
            
            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate monthly plan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get content templates
     */
    public function getTemplates(): JsonResponse
    {
        $templates = [
            'product_showcase' => [
                'name' => 'Product Showcase',
                'description' => 'Highlight produk dengan fokus pada fitur dan benefit',
                'platform' => ['Instagram', 'Facebook', 'LinkedIn'],
                'contentType' => ['Post', 'Story'],
                'tone' => 'professional'
            ],
            'educational' => [
                'name' => 'Educational Content',
                'description' => 'Konten edukasi tentang perawatan dan tips penggunaan',
                'platform' => ['Instagram', 'Facebook', 'LinkedIn'],
                'contentType' => ['Post', 'Carousel'],
                'tone' => 'educational'
            ],
            'testimonial' => [
                'name' => 'Customer Testimonial',
                'description' => 'Testimoni dan success story dari pelanggan',
                'platform' => ['Instagram', 'Facebook', 'LinkedIn'],
                'contentType' => ['Post', 'Video'],
                'tone' => 'enthusiastic'
            ],
            'behind_scenes' => [
                'name' => 'Behind the Scenes',
                'description' => 'Proses produksi, instalasi, atau operasional',
                'platform' => ['Instagram', 'Facebook'],
                'contentType' => ['Story', 'Reel', 'Video'],
                'tone' => 'casual'
            ],
            'industry_news' => [
                'name' => 'Industry News',
                'description' => 'Berita dan update terkini industri pertambangan',
                'platform' => ['LinkedIn', 'Facebook'],
                'contentType' => ['Post'],
                'tone' => 'professional'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $templates
        ]);
    }

    /**
     * Get content categories
     */
    public function getCategories(): JsonResponse
    {
        $categories = [
            'product' => [
                'name' => 'Product Focus',
                'description' => 'Konten yang fokus pada produk dan fitur',
                'color' => '#3B82F6',
                'icon' => 'package'
            ],
            'education' => [
                'name' => 'Educational',
                'description' => 'Tips, tutorial, dan konten edukasi',
                'color' => '#10B981',
                'icon' => 'book-open'
            ],
            'promotion' => [
                'name' => 'Promotional',
                'description' => 'Promosi, penawaran, dan campaign',
                'color' => '#F59E0B',
                'icon' => 'megaphone'
            ],
            'engagement' => [
                'name' => 'Engagement',
                'description' => 'Konten untuk meningkatkan interaksi',
                'color' => '#EF4444',
                'icon' => 'heart'
            ],
            'company' => [
                'name' => 'Company Culture',
                'description' => 'Budaya perusahaan dan team',
                'color' => '#8B5CF6',
                'icon' => 'users'
            ],
            'industry' => [
                'name' => 'Industry Insights',
                'description' => 'Berita dan insight industri',
                'color' => '#06B6D4',
                'icon' => 'trending-up'
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * Save generated content
     */
    public function saveContent(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'caption' => 'required|string',
            'hashtags' => 'array',
            'platform' => 'required|string',
            'contentType' => 'required|string',
            'category' => 'required|string',
            'scheduledDate' => 'nullable|date',
            'visualSuggestion' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // In a real application, you would save this to database
            // For now, we'll just return success with the data
            $contentData = $request->all();
            $contentData['id'] = uniqid();
            $contentData['createdAt'] = now();
            $contentData['status'] = 'draft';

            return response()->json([
                'success' => true,
                'message' => 'Content saved successfully',
                'data' => $contentData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save content',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
