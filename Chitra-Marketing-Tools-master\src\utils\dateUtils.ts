import { format, formatDistance, formatRelative, isValid } from 'date-fns';
import { id } from 'date-fns/locale';

/**
 * Format a date to a readable string
 * @param date The date to format
 * @param formatString Optional format string (default: 'dd MMM yyyy, HH:mm')
 * @returns Formatted date string
 */
export const formatDate = (date: Date | string | number, formatString: string = 'dd MMM yyyy, HH:mm'): string => {
  try {
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    
    if (!isValid(dateObj)) {
      return 'Invalid date';
    }
    
    return format(dateObj, formatString, { locale: id });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
};

/**
 * Format a date relative to now (e.g., "2 hours ago")
 * @param date The date to format
 * @returns Relative date string
 */
export const formatRelativeDate = (date: Date | string | number): string => {
  try {
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    
    if (!isValid(dateObj)) {
      return 'Invalid date';
    }
    
    return formatDistance(dateObj, new Date(), { 
      addSuffix: true,
      locale: id
    });
  } catch (error) {
    console.error('Error formatting relative date:', error);
    return 'Invalid date';
  }
};

/**
 * Format a date relative to a base date
 * @param date The date to format
 * @param baseDate The base date to format relative to
 * @returns Relative date string
 */
export const formatDateRelativeTo = (
  date: Date | string | number, 
  baseDate: Date | string | number
): string => {
  try {
    const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;
    const baseDateObj = typeof baseDate === 'string' || typeof baseDate === 'number' 
      ? new Date(baseDate) 
      : baseDate;
    
    if (!isValid(dateObj) || !isValid(baseDateObj)) {
      return 'Invalid date';
    }
    
    return formatRelative(dateObj, baseDateObj, { locale: id });
  } catch (error) {
    console.error('Error formatting relative date:', error);
    return 'Invalid date';
  }
};
