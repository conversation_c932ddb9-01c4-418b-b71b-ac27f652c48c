import axios from 'axios';

// Interface for weather data
export interface WeatherForecast {
  date: string;
  location: string;
  condition: string;
  temperature: number;
  humidity: number;
  precipitation: number;
  isRainy: boolean;
}

// Interface for weather forecast response
export interface WeatherForecastResponse {
  location: string;
  forecasts: WeatherForecast[];
  rainyDays: number;
  averagePrecipitation: number;
  startDate: string;
  endDate: string;
}

// Local storage keys
const WEATHER_FORECAST_KEY = 'weatherForecast';
const WEATHER_LAST_UPDATED_KEY = 'weatherLastUpdated';

// Weather API configuration
// Using Open-Meteo API which is free and doesn't require API key
const WEATHER_API_URL = 'https://api.open-meteo.com/v1/forecast';

// Indonesian mining regions with their coordinates
const MINING_REGIONS = {
  'Kalimantan Timur': { latitude: -0.5, longitude: 116.5 },
  'Kalimantan Selatan': { latitude: -3.0, longitude: 115.5 },
  'Kalimantan Tengah': { latitude: -1.5, longitude: 113.5 },
  'Kalimantan Utara': { latitude: 3.0, longitude: 116.0 },
  'Sumatra Selatan': { latitude: -3.3, longitude: 103.8 },
  'Papua': { latitude: -4.0, longitude: 138.0 },
  'Sulawesi': { latitude: -2.5, longitude: 121.0 },
  'Jawa Barat': { latitude: -6.9, longitude: 107.6 }
};

/**
 * Fetch weather forecast for a specific region
 * @param region Mining region name
 * @param days Number of days to forecast (max 16)
 * @returns Weather forecast data
 */
export const fetchWeatherForecast = async (
  region: keyof typeof MINING_REGIONS = 'Kalimantan Timur',
  days: number = 14
): Promise<WeatherForecastResponse> => {
  try {
    // Check if we have cached data that's less than 6 hours old
    const lastUpdated = localStorage.getItem(WEATHER_LAST_UPDATED_KEY);
    const cachedData = localStorage.getItem(`${WEATHER_FORECAST_KEY}_${region}`);
    
    if (lastUpdated && cachedData) {
      const lastUpdateTime = new Date(lastUpdated).getTime();
      const currentTime = new Date().getTime();
      const hoursSinceUpdate = (currentTime - lastUpdateTime) / (1000 * 60 * 60);
      
      if (hoursSinceUpdate < 6) {
        console.log(`Using cached weather data for ${region} (${hoursSinceUpdate.toFixed(1)} hours old)`);
        return JSON.parse(cachedData);
      }
    }
    
    // Get coordinates for the region
    const coordinates = MINING_REGIONS[region];
    if (!coordinates) {
      throw new Error(`Unknown region: ${region}`);
    }
    
    // Limit days to API maximum
    const forecastDays = Math.min(days, 16);
    
    // Fetch data from API
    console.log(`Fetching weather forecast for ${region} (${forecastDays} days)`);
    const response = await axios.get(WEATHER_API_URL, {
      params: {
        latitude: coordinates.latitude,
        longitude: coordinates.longitude,
        daily: 'temperature_2m_max,precipitation_sum,weathercode',
        timezone: 'Asia/Jakarta',
        forecast_days: forecastDays
      }
    });
    
    // Process the response
    const data = response.data;
    const forecasts: WeatherForecast[] = [];
    let totalPrecipitation = 0;
    let rainyDays = 0;
    
    // Start and end dates
    const startDate = data.daily.time[0];
    const endDate = data.daily.time[data.daily.time.length - 1];
    
    // Process each day's forecast
    for (let i = 0; i < data.daily.time.length; i++) {
      const date = data.daily.time[i];
      const temperature = data.daily.temperature_2m_max[i];
      const precipitation = data.daily.precipitation_sum[i];
      const weatherCode = data.daily.weathercode[i];
      
      // Determine weather condition based on WMO weather codes
      // https://www.nodc.noaa.gov/archive/arc0021/0002199/1.1/data/0-data/HTML/WMO-CODE/WMO4677.HTM
      const condition = getWeatherCondition(weatherCode);
      
      // Consider a day rainy if precipitation > 5mm or weather code indicates rain
      const isRainy = precipitation > 5 || isRainyWeatherCode(weatherCode);
      
      if (isRainy) {
        rainyDays++;
      }
      
      totalPrecipitation += precipitation;
      
      forecasts.push({
        date,
        location: region,
        condition,
        temperature,
        humidity: 0, // Not provided by this API
        precipitation,
        isRainy
      });
    }
    
    // Calculate average precipitation
    const averagePrecipitation = totalPrecipitation / forecasts.length;
    
    // Create response object
    const forecastResponse: WeatherForecastResponse = {
      location: region,
      forecasts,
      rainyDays,
      averagePrecipitation,
      startDate,
      endDate
    };
    
    // Cache the data
    localStorage.setItem(`${WEATHER_FORECAST_KEY}_${region}`, JSON.stringify(forecastResponse));
    localStorage.setItem(WEATHER_LAST_UPDATED_KEY, new Date().toISOString());
    
    return forecastResponse;
  } catch (error) {
    console.error('Error fetching weather forecast:', error);
    
    // Return cached data if available, even if it's old
    const cachedData = localStorage.getItem(`${WEATHER_FORECAST_KEY}_${region}`);
    if (cachedData) {
      console.log(`Using old cached weather data for ${region} due to API error`);
      return JSON.parse(cachedData);
    }
    
    // Return empty forecast if no cached data
    return {
      location: region,
      forecasts: [],
      rainyDays: 0,
      averagePrecipitation: 0,
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(new Date().setDate(new Date().getDate() + days)).toISOString().split('T')[0]
    };
  }
};

/**
 * Get weather condition description from WMO weather code
 */
function getWeatherCondition(code: number): string {
  // WMO Weather interpretation codes (WW)
  // https://www.nodc.noaa.gov/archive/arc0021/0002199/1.1/data/0-data/HTML/WMO-CODE/WMO4677.HTM
  if (code === 0) return 'Cerah';
  if (code === 1) return 'Sebagian Cerah';
  if (code >= 2 && code <= 3) return 'Berawan';
  if (code === 45 || code === 48) return 'Berkabut';
  if (code >= 51 && code <= 55) return 'Gerimis';
  if (code >= 56 && code <= 57) return 'Gerimis Beku';
  if (code >= 61 && code <= 65) return 'Hujan';
  if (code >= 66 && code <= 67) return 'Hujan Beku';
  if (code >= 71 && code <= 75) return 'Salju';
  if (code === 77) return 'Butiran Salju';
  if (code >= 80 && code <= 82) return 'Hujan Ringan';
  if (code >= 85 && code <= 86) return 'Hujan Salju';
  if (code >= 95 && code <= 99) return 'Badai Petir';
  return 'Tidak Diketahui';
}

/**
 * Check if a weather code indicates rainy conditions
 */
function isRainyWeatherCode(code: number): boolean {
  // These codes represent various forms of precipitation
  return (
    (code >= 51 && code <= 57) || // Drizzle
    (code >= 61 && code <= 67) || // Rain
    (code >= 80 && code <= 82) || // Rain showers
    (code >= 95 && code <= 99)    // Thunderstorm
  );
}

/**
 * Get all available mining regions
 */
export const getMiningRegions = (): string[] => {
  return Object.keys(MINING_REGIONS);
};

/**
 * Get weather forecasts for all mining regions
 */
export const getAllRegionsWeatherForecast = async (days: number = 14): Promise<WeatherForecastResponse[]> => {
  const regions = getMiningRegions();
  const forecasts: WeatherForecastResponse[] = [];
  
  for (const region of regions) {
    try {
      const forecast = await fetchWeatherForecast(region as keyof typeof MINING_REGIONS, days);
      forecasts.push(forecast);
    } catch (error) {
      console.error(`Error fetching forecast for ${region}:`, error);
    }
  }
  
  return forecasts;
};

/**
 * Get the rainy season status for Indonesia
 * @returns Information about the current rainy season status
 */
export const getRainySeasonStatus = (): { isRainySeason: boolean; startMonth: number; endMonth: number; intensity: string } => {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth() + 1; // 1-12
  
  // Indonesia's rainy season is typically from November to March
  const rainySeasonStart = 11; // November
  const rainySeasonEnd = 3;    // March
  
  // Check if current month is in rainy season
  const isRainySeason = 
    (currentMonth >= rainySeasonStart) || 
    (currentMonth <= rainySeasonEnd);
  
  // Determine intensity based on month
  let intensity = 'Sedang';
  if (currentMonth === 12 || currentMonth === 1) {
    intensity = 'Tinggi';
  } else if (currentMonth === 11 || currentMonth === 2) {
    intensity = 'Sedang';
  } else if (currentMonth === 3) {
    intensity = 'Rendah';
  }
  
  return {
    isRainySeason,
    startMonth: rainySeasonStart,
    endMonth: rainySeasonEnd,
    intensity
  };
};
