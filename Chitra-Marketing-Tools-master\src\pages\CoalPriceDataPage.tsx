import React, { useState } from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import CoalPriceManagement from '../components/CoalPriceManagement';
import CoalPriceAnalysis from '../components/CoalPriceAnalysis';
import { CoalPrice } from '../services/coalPriceService';

const CoalPriceDataPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('management');
  const [coalPriceData, setCoalPriceData] = useState<CoalPrice[]>([]);
  
  // Handle data change from CoalPriceManagement component
  const handleDataChange = (data: CoalPrice[]) => {
    setCoalPriceData(data);
  };
  
  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">Data Harga Batu Bara</h1>
      
      <Tabs defaultValue="management" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="management">Manajemen Data</TabsTrigger>
          <TabsTrigger value="analysis">Analisis Harga</TabsTrigger>
        </TabsList>
        
        <TabsContent value="management">
          <CoalPriceManagement onDataChange={handleDataChange} />
        </TabsContent>
        
        <TabsContent value="analysis">
          <CoalPriceAnalysis />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CoalPriceDataPage;
