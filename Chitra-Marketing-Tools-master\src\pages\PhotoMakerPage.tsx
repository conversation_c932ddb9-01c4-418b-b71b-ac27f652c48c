import React, { useState, useEffect, useRef } from 'react';
import {
  PhotoMakerRequest,
  GeneratedImage,
  SavedImage,
  PhotoMakerModel,
  PhotoMakerStyle,
  IMAGE_SIZES
} from '../types/imageGenerator';
import {
  generatePhotoMakerImages,
  getImageHistory,
  clearImageHistory,
  deleteImageFromHistory,
  getAllPhotoMakerModels
} from '../services/runwareServiceNew';
import {
  ImageIcon,
  Sparkles,
  Download,
  Trash2,
  History,
  RefreshCw,
  Loader2,
  X,
  Copy,
  CheckCircle,
  AlertCircle,
  Upload,
  Sliders,
  Camera
} from 'lucide-react';

// Mock useToast function
const useToast = () => {
  return {
    toast: (options: any) => {
      console.log('Toast:', options);
      alert(options.title + ': ' + options.description);
    }
  };
};

const PhotoMakerPage: React.FC = () => {
  // State for form inputs
  const [prompt, setPrompt] = useState('');
  const [negativePrompt, setNegativePrompt] = useState('');
  const [selectedModel, setSelectedModel] = useState<string>(PhotoMakerModel.REALISM_ENGINE_SDXL);
  const [selectedSize, setSelectedSize] = useState(IMAGE_SIZES[0]);
  const [numberResults, setNumberResults] = useState(1);
  const [selectedStyle, setSelectedStyle] = useState<string>(PhotoMakerStyle.NO_STYLE);
  const [strength, setStrength] = useState(30); // Integer value between 15 and 50
  const [steps, setSteps] = useState(30);
  const [cfgScale, setCfgScale] = useState(7.0);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // State for reference images
  const [referenceImages, setReferenceImages] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State for generated images and history
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [imageHistory, setImageHistory] = useState<SavedImage[]>([]);
  const [showHistory, setShowHistory] = useState(false);

  // State for available models
  const [availableModels, setAvailableModels] = useState<any[]>([]);

  // Loading state
  const [isGenerating, setIsGenerating] = useState(false);

  // Toast notifications
  const { toast } = useToast();

  // Load image history and models on component mount
  useEffect(() => {
    loadImageHistory();
    loadModels();
  }, []);

  // Load image history from local storage
  const loadImageHistory = () => {
    const history = getImageHistory();
    setImageHistory(history);
  };

  // Load all available PhotoMaker models
  const loadModels = () => {
    const models = getAllPhotoMakerModels();
    setAvailableModels(models);
  };

  // Handle file selection for reference images
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Limit to 4 files
    const filesToProcess = Array.from(files).slice(0, 4);

    // Convert files to base64
    Promise.all(
      filesToProcess.map(file => {
        return new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            if (typeof reader.result === 'string') {
              resolve(reader.result);
            } else {
              reject(new Error('Failed to read file as base64'));
            }
          };
          reader.onerror = reject;
          reader.readAsDataURL(file);
        });
      })
    )
    .then(base64Images => {
      setReferenceImages(base64Images);
      toast({
        title: "Images Uploaded",
        description: `${base64Images.length} reference image(s) uploaded successfully`,
      });
    })
    .catch(error => {
      console.error('Error processing files:', error);
      toast({
        title: "Error",
        description: "Failed to process image files",
        variant: "destructive"
      });
    });
  };

  // Remove a reference image
  const removeReferenceImage = (index: number) => {
    setReferenceImages(prev => prev.filter((_, i) => i !== index));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!prompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a prompt",
        variant: "destructive"
      });
      return;
    }

    if (referenceImages.length === 0) {
      toast({
        title: "Error",
        description: "Please upload at least one reference image",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Ensure the prompt includes the required "rwre" trigger word
      let finalPrompt = prompt;
      if (!finalPrompt.includes('rwre')) {
        finalPrompt = `rwre, ${finalPrompt}`;
      }

      const request: PhotoMakerRequest = {
        inputImages: referenceImages,
        style: selectedStyle,
        strength: strength,
        positivePrompt: finalPrompt,
        negativePrompt,
        width: selectedSize.width,
        height: selectedSize.height,
        model: selectedModel,
        steps: steps,
        CFGScale: cfgScale,
        numberResults
      };

      const images = await generatePhotoMakerImages(request);

      if (images && images.length > 0) {
        setGeneratedImages(images);
        loadImageHistory(); // Refresh history after generation

        toast({
          title: "Success",
          description: `Generated ${images.length} image(s)`,
        });
      } else {
        throw new Error("No images were returned from the API");
      }
    } catch (error) {
      console.error('Error generating images:', error);

      // Get a more descriptive error message
      let errorMessage = "Failed to generate images. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle image download
  const handleDownload = (imageUrl: string, index: number) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `photomaker-image-${index + 1}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Downloaded",
      description: "Image downloaded successfully",
    });
  };

  // Handle clearing history
  const handleClearHistory = () => {
    if (confirm('Are you sure you want to clear all image history?')) {
      clearImageHistory();
      setImageHistory([]);
      toast({
        title: "Cleared",
        description: "Image history cleared",
      });
    }
  };

  // Handle deleting a single image from history
  const handleDeleteImage = (imageId: string) => {
    deleteImageFromHistory(imageId);
    loadImageHistory(); // Refresh history after deletion

    toast({
      title: "Deleted",
      description: "Image removed from history",
    });
  };

  // Copy prompt to clipboard
  const copyPrompt = (prompt: string) => {
    navigator.clipboard.writeText(prompt);
    toast({
      title: "Copied",
      description: "Prompt copied to clipboard",
    });
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center mb-6">
        <Camera size={24} className="text-blue-500 mr-2" />
        <h1 className="text-2xl font-bold">Photo Maker</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Form Panel */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="p-4 border-b border-gray-200 flex items-center">
              <Sparkles size={20} className="text-blue-500 mr-2" />
              <h2 className="text-lg font-medium">Generate Images from Photos</h2>
            </div>

            <form onSubmit={handleSubmit} className="p-4 space-y-4">
              {/* Reference Images Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reference Images (Max 4)
                </label>
                <div className="space-y-2">
                  <div className="flex flex-wrap gap-2">
                    {referenceImages.map((img, index) => (
                      <div key={index} className="relative w-20 h-20">
                        <img
                          src={img}
                          alt={`Reference ${index + 1}`}
                          className="w-full h-full object-cover rounded-md"
                        />
                        <button
                          type="button"
                          onClick={() => removeReferenceImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                          title="Remove image"
                        >
                          <X size={12} />
                        </button>
                      </div>
                    ))}
                    {referenceImages.length < 4 && (
                      <button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        className="w-20 h-20 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-gray-500 hover:text-blue-500 hover:border-blue-500"
                      >
                        <Upload size={24} />
                      </button>
                    )}
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleFileChange}
                    className="hidden"
                  />
                  <p className="text-xs text-gray-500">
                    Upload 1-4 reference images of the same person/subject
                  </p>
                </div>
              </div>

              {/* Prompt Input */}
              <div>
                <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-1">
                  Prompt
                </label>
                <textarea
                  id="prompt"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Describe how you want the person/subject to appear..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  The keyword "rwre" will be automatically added to your prompt if not included. This is required for PhotoMaker to work properly.
                </p>
              </div>

              {/* Negative Prompt Input */}
              <div>
                <label htmlFor="negativePrompt" className="block text-sm font-medium text-gray-700 mb-1">
                  Negative Prompt (Optional)
                </label>
                <textarea
                  id="negativePrompt"
                  value={negativePrompt}
                  onChange={(e) => setNegativePrompt(e.target.value)}
                  placeholder="Elements to avoid in the generated image..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={2}
                />
              </div>

              {/* Style Selection */}
              <div>
                <label htmlFor="style" className="block text-sm font-medium text-gray-700 mb-1">
                  Style
                </label>
                <select
                  id="style"
                  value={selectedStyle}
                  onChange={(e) => setSelectedStyle(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={PhotoMakerStyle.NO_STYLE}>No Style (Best Fidelity)</option>
                  <option value={PhotoMakerStyle.PHOTOGRAPHIC}>Photographic</option>
                  <option value={PhotoMakerStyle.CINEMATIC}>Cinematic</option>
                  <option value={PhotoMakerStyle.DISNEY_CHARACTER}>Disney Character</option>
                  <option value={PhotoMakerStyle.DIGITAL_ART}>Digital Art</option>
                  <option value={PhotoMakerStyle.FANTASY_ART}>Fantasy Art</option>
                  <option value={PhotoMakerStyle.NEONPUNK}>Neonpunk</option>
                  <option value={PhotoMakerStyle.ENHANCE}>Enhance</option>
                  <option value={PhotoMakerStyle.COMIC_BOOK}>Comic Book</option>
                  <option value={PhotoMakerStyle.LOWPOLY}>Lowpoly</option>
                  <option value={PhotoMakerStyle.LINE_ART}>Line Art</option>
                </select>
              </div>

              {/* Strength Slider */}
              <div>
                <label htmlFor="strength" className="block text-sm font-medium text-gray-700 mb-1">
                  Strength: {strength}
                </label>
                <input
                  id="strength"
                  type="range"
                  min="15"
                  max="50"
                  step="1"
                  value={strength}
                  onChange={(e) => setStrength(parseInt(e.target.value))}
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Lower values (15-30) preserve more of the reference image, higher values (30-50) apply more of the prompt
                </p>
              </div>

              {/* Advanced Settings Toggle */}
              <div>
                <button
                  type="button"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="text-sm text-blue-500 hover:text-blue-700 flex items-center"
                >
                  <Sliders size={14} className="mr-1" />
                  {showAdvanced ? 'Hide Advanced Settings' : 'Show Advanced Settings'}
                </button>
              </div>

              {/* Advanced Settings */}
              {showAdvanced && (
                <div className="space-y-4 p-3 bg-gray-50 rounded-md">
                  {/* Model Selection */}
                  <div>
                    <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                      Model
                    </label>
                    <select
                      id="model"
                      value={selectedModel}
                      onChange={(e) => setSelectedModel(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {availableModels.map(model => (
                        <option key={model.id} value={model.value}>
                          {model.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Image Size Selection */}
                  <div>
                    <label htmlFor="size" className="block text-sm font-medium text-gray-700 mb-1">
                      Image Size
                    </label>
                    <select
                      id="size"
                      value={`${selectedSize.width}x${selectedSize.height}`}
                      onChange={(e) => {
                        const [width, height] = e.target.value.split('x').map(Number);
                        setSelectedSize({ width, height, label: e.target.options[e.target.selectedIndex].text });
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {IMAGE_SIZES.map((size) => (
                        <option key={`${size.width}x${size.height}`} value={`${size.width}x${size.height}`}>
                          {size.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Steps Slider */}
                  <div>
                    <label htmlFor="steps" className="block text-sm font-medium text-gray-700 mb-1">
                      Steps: {steps}
                    </label>
                    <input
                      id="steps"
                      type="range"
                      min="20"
                      max="50"
                      step="1"
                      value={steps}
                      onChange={(e) => setSteps(parseInt(e.target.value))}
                      className="w-full"
                    />
                  </div>

                  {/* CFG Scale Slider */}
                  <div>
                    <label htmlFor="cfgScale" className="block text-sm font-medium text-gray-700 mb-1">
                      CFG Scale: {cfgScale.toFixed(1)}
                    </label>
                    <input
                      id="cfgScale"
                      type="range"
                      min="1"
                      max="15"
                      step="0.5"
                      value={cfgScale}
                      onChange={(e) => setCfgScale(parseFloat(e.target.value))}
                      className="w-full"
                    />
                  </div>

                  {/* Number of Results */}
                  <div>
                    <label htmlFor="numberResults" className="block text-sm font-medium text-gray-700 mb-1">
                      Number of Images
                    </label>
                    <select
                      id="numberResults"
                      value={numberResults}
                      onChange={(e) => setNumberResults(Number(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value={1}>1</option>
                      <option value={2}>2</option>
                      <option value={4}>4</option>
                    </select>
                  </div>
                </div>
              )}

              {/* Generate Button */}
              <button
                type="submit"
                disabled={isGenerating || referenceImages.length === 0}
                className={`w-full py-2 px-4 rounded-md flex items-center justify-center ${
                  isGenerating || referenceImages.length === 0
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                {isGenerating ? (
                  <>
                    <Loader2 size={20} className="animate-spin mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles size={20} className="mr-2" />
                    Generate Images
                  </>
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Results Panel */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <div className="flex items-center">
                <ImageIcon size={20} className="text-blue-500 mr-2" />
                <h2 className="text-lg font-medium">
                  {showHistory ? 'Image History' : 'Generated Images'}
                </h2>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setShowHistory(!showHistory)}
                  className={`py-1 px-3 rounded-md flex items-center text-sm ${
                    showHistory
                      ? 'bg-blue-100 text-blue-700'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <History size={16} className="mr-1" />
                  {showHistory ? 'Show Results' : 'Show History'}
                </button>
                {showHistory && (
                  <button
                    onClick={handleClearHistory}
                    className="py-1 px-3 bg-red-100 text-red-700 rounded-md flex items-center text-sm hover:bg-red-200"
                    title="Clear history"
                  >
                    <Trash2 size={16} className="mr-1" />
                    Clear
                  </button>
                )}
              </div>
            </div>

            <div className="p-4">
              {/* Generated Images */}
              {!showHistory && (
                <>
                  {generatedImages.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {generatedImages.map((image, index) => (
                        <div key={image.imageUUID} className="border border-gray-200 rounded-lg overflow-hidden">
                          <img
                            src={image.imageURL}
                            alt={`Generated image ${index + 1}`}
                            className="w-full h-auto"
                            loading="lazy"
                          />
                          <div className="p-3 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
                            <div className="text-sm text-gray-500 truncate" style={{ maxWidth: '200px' }}>
                              {image.prompt}
                            </div>
                            <div className="flex space-x-1">
                              <button
                                onClick={() => copyPrompt(image.prompt)}
                                className="p-1 text-gray-500 hover:text-blue-500"
                                title="Copy prompt"
                              >
                                <Copy size={16} />
                              </button>
                              <button
                                onClick={() => handleDownload(image.imageURL, index)}
                                className="p-1 text-gray-500 hover:text-blue-500"
                                title="Download image"
                              >
                                <Download size={16} />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Camera size={48} className="mx-auto text-gray-300 mb-4" />
                      <h3 className="text-lg font-medium text-gray-700 mb-2">No Images Generated Yet</h3>
                      <p className="text-gray-500 max-w-md mx-auto">
                        Upload reference images and enter a prompt to generate new images with PhotoMaker.
                      </p>
                    </div>
                  )}
                </>
              )}

              {/* Image History */}
              {showHistory && (
                <>
                  {imageHistory.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {imageHistory.map((image, index) => (
                        <div key={image.id} className="border border-gray-200 rounded-lg overflow-hidden">
                          <img
                            src={image.imageURL}
                            alt={`History image ${index + 1}`}
                            className="w-full h-auto"
                            loading="lazy"
                          />
                          <div className="p-3 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
                            <div className="text-sm text-gray-500 truncate" style={{ maxWidth: '200px' }}>
                              {image.prompt}
                            </div>
                            <div className="flex space-x-1">
                              <button
                                onClick={() => copyPrompt(image.prompt)}
                                className="p-1 text-gray-500 hover:text-blue-500"
                                title="Copy prompt"
                              >
                                <Copy size={16} />
                              </button>
                              <button
                                onClick={() => handleDownload(image.imageURL, index)}
                                className="p-1 text-gray-500 hover:text-blue-500"
                                title="Download image"
                              >
                                <Download size={16} />
                              </button>
                              <button
                                onClick={() => handleDeleteImage(image.id)}
                                className="p-1 text-gray-500 hover:text-red-500"
                                title="Delete from history"
                              >
                                <Trash2 size={16} />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <History size={48} className="mx-auto text-gray-300 mb-4" />
                      <h3 className="text-lg font-medium text-gray-700 mb-2">No Image History</h3>
                      <p className="text-gray-500">
                        Your generated images will appear here.
                      </p>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhotoMakerPage;
