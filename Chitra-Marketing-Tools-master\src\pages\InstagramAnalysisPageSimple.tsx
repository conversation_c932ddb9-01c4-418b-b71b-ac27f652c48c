import React, { useState, useEffect } from 'react';
import { Instagram, <PERSON><PERSON><PERSON> } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/card';
import { useToast } from '../components/ui/use-toast';

// Try to import the Instagram API service
let fetchInstagramData: any;
let generateDummyInstagramData: any;

try {
  const instagramApiService = require('../services/instagramApiService');
  fetchInstagramData = instagramApiService.fetchInstagramData;
  generateDummyInstagramData = instagramApiService.generateDummyInstagramData;
  console.log('Successfully imported Instagram API services');
} catch (error) {
  console.error('Error importing Instagram API services:', error);
}

export default function InstagramAnalysisPageSimple() {
  console.log('InstagramAnalysisPageSimple component is rendering');
  const { toast } = useToast();
  const [username, setUsername] = useState('');

  useEffect(() => {
    console.log('InstagramAnalysisPageSimple component mounted');
  }, []);

  const handleTestClick = () => {
    toast({
      title: "Test Toast",
      description: "This is a test toast notification",
    });
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Analisis Instagram (Simple)</h1>
        <p className="text-gray-600">Halaman sederhana untuk menguji routing</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Test Card</CardTitle>
          <CardDescription>This is a test card to check if UI components are working</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>Ini adalah halaman sederhana untuk menguji apakah masalah terjadi pada routing atau pada komponen InstagramAnalysisPage.</p>

            <div className="flex items-center space-x-2">
              <Button onClick={handleTestClick}>
                <Instagram className="h-4 w-4 mr-2" />
                Test Toast
              </Button>

              <Button variant="outline">
                <BarChart className="h-4 w-4 mr-2" />
                Test Button
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
