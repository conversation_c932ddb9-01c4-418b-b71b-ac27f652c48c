import { VideoType, VideoPurpose, VideoTargetAudience, VideoPlatform } from './videoScript';

/**
 * Simplified model for storing basic video script form data
 */
export interface VideoScriptFormData {
  id: string;
  title: string;
  createdAt: Date;
  videoType: VideoType;
  purpose: VideoPurpose;
  targetAudience: VideoTargetAudience;
  platform: VideoPlatform;
  productName?: string;
  duration?: string;
  additionalInfo?: string;
}

/**
 * Service for managing video script form data
 */
export const VIDEO_SCRIPT_FORMS_KEY = 'video_script_forms';

/**
 * Load saved video script forms from localStorage
 */
export const loadVideoScriptForms = (): VideoScriptFormData[] => {
  try {
    const savedForms = localStorage.getItem(VIDEO_SCRIPT_FORMS_KEY);
    if (savedForms) {
      // Parse dates from strings to Date objects
      const forms = JSON.parse(savedForms) as VideoScriptFormData[];
      return forms.map(form => ({
        ...form,
        createdAt: new Date(form.createdAt)
      }));
    }
    return [];
  } catch (error) {
    console.error('Error loading video script forms:', error);
    return [];
  }
};

/**
 * Save video script forms to localStorage
 */
export const saveVideoScriptForms = (forms: VideoScriptFormData[]): void => {
  try {
    localStorage.setItem(VIDEO_SCRIPT_FORMS_KEY, JSON.stringify(forms));
  } catch (error) {
    console.error('Error saving video script forms:', error);
  }
};

/**
 * Add a new video script form
 */
export const addVideoScriptForm = (form: Omit<VideoScriptFormData, 'id' | 'createdAt'>): VideoScriptFormData => {
  const newForm: VideoScriptFormData = {
    ...form,
    id: `vsf-${Date.now()}`,
    createdAt: new Date()
  };

  const forms = loadVideoScriptForms();
  const updatedForms = [...forms, newForm];
  saveVideoScriptForms(updatedForms);

  return newForm;
};

/**
 * Delete a video script form
 */
export const deleteVideoScriptForm = (id: string): void => {
  const forms = loadVideoScriptForms();
  const updatedForms = forms.filter(form => form.id !== id);
  saveVideoScriptForms(updatedForms);
};

/**
 * Get all video script forms
 */
export const getAllVideoScriptForms = (): VideoScriptFormData[] => {
  return loadVideoScriptForms();
};

/**
 * Get a video script form by ID
 */
export const getVideoScriptFormById = (id: string): VideoScriptFormData | undefined => {
  const forms = loadVideoScriptForms();
  return forms.find(form => form.id === id);
};
