import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';

interface FieldMappingModalProps {
  isOpen: boolean;
  onClose: () => void;
  availableFields: string[];
  initialMapping: Record<string, string>;
  onSave: (mapping: Record<string, string>) => void;
  sampleData?: Record<string, any>;
}

export default function FieldMappingModal({
  isOpen,
  onClose,
  availableFields,
  initialMapping,
  onSave,
  sampleData
}: FieldMappingModalProps) {
  const [mapping, setMapping] = useState<Record<string, string>>(initialMapping);

  // Reset mapping when modal opens
  useEffect(() => {
    if (isOpen) {
      setMapping(initialMapping);
    }
  }, [isOpen, initialMapping]);

  if (!isOpen) return null;

  const handleChange = (field: string, value: string) => {
    setMapping(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = () => {
    onSave(mapping);
    onClose();
  };

  // Customize field groups based on the component using this modal
  const requiredFields = ['oldMaterialNo', 'materialDescription'];
  const recommendedFields = ['price', 'priceUSD'];
  const optionalFields = ['description', 'exchangeRate'];

  const getSampleValue = (fieldName: string) => {
    if (!sampleData || !fieldName) return '';
    return sampleData[fieldName] !== undefined ? String(sampleData[fieldName]) : '';
  };

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-3xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Map Your Data Fields</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <p className="text-sm text-gray-500 mb-4">
          Please map your data fields to the corresponding fields in our system. This will help us correctly import your data.
        </p>

        {sampleData && (
          <div className="mb-4 p-3 bg-gray-50 rounded-md">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Sample Data Row:</h3>
            <div className="text-xs text-gray-600 overflow-x-auto">
              <pre>{JSON.stringify(sampleData, null, 2)}</pre>
            </div>
          </div>
        )}

        <div className="space-y-6">
          {/* Required Fields */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">Required Fields</h3>
            <div className="space-y-3">
              {requiredFields.map(field => (
                <div key={field} className="grid grid-cols-3 gap-4 items-center">
                  <label className="text-sm font-medium text-gray-700">
                    {field === 'oldMaterialNo' ? 'Material No.' :
                     field === 'materialDescription' ? 'Material Description' :
                     field === 'description' ? 'Additional Description' :
                     field === 'price' ? 'Price (IDR)' :
                     field === 'priceUSD' ? 'USD Price' :
                     field === 'exchangeRate' ? 'Exchange Rate' :
                     field} <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={mapping[field] || ''}
                    onChange={(e) => handleChange(field, e.target.value)}
                    className="col-span-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  >
                    <option value="">-- Select Field --</option>
                    {availableFields.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  <div className="text-sm text-gray-500">
                    Sample: {getSampleValue(mapping[field])}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recommended Fields */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">Recommended Fields</h3>
            <div className="space-y-3">
              {recommendedFields.map(field => (
                <div key={field} className="grid grid-cols-3 gap-4 items-center">
                  <label className="text-sm font-medium text-gray-700">
                    {field === 'oldMaterialNo' ? 'Material No.' :
                     field === 'materialDescription' ? 'Material Description' :
                     field === 'description' ? 'Additional Description' :
                     field === 'price' ? 'Price (IDR)' :
                     field === 'priceUSD' ? 'USD Price' :
                     field === 'exchangeRate' ? 'Exchange Rate' :
                     field}
                  </label>
                  <select
                    value={mapping[field] || ''}
                    onChange={(e) => handleChange(field, e.target.value)}
                    className="col-span-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">-- Select Field --</option>
                    {availableFields.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  <div className="text-sm text-gray-500">
                    Sample: {getSampleValue(mapping[field])}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Optional Fields */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">Optional Fields</h3>
            <div className="space-y-3">
              {optionalFields.map(field => (
                <div key={field} className="grid grid-cols-3 gap-4 items-center">
                  <label className="text-sm font-medium text-gray-700">
                    {field === 'oldMaterialNo' ? 'Material No.' :
                     field === 'materialDescription' ? 'Material Description' :
                     field === 'description' ? 'Additional Description' :
                     field === 'price' ? 'Price (IDR)' :
                     field === 'priceUSD' ? 'USD Price' :
                     field === 'exchangeRate' ? 'Exchange Rate' :
                     field}
                  </label>
                  <select
                    value={mapping[field] || ''}
                    onChange={(e) => handleChange(field, e.target.value)}
                    className="col-span-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">-- Select Field --</option>
                    {availableFields.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  <div className="text-sm text-gray-500">
                    Sample: {getSampleValue(mapping[field])}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={!mapping.oldMaterialNo || !mapping.materialDescription}
          >
            Apply Mapping
          </button>
        </div>
      </div>
    </div>
  );
}
