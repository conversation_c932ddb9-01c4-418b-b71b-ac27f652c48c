import React, { useState, useEffect, useRef } from 'react';
import {
  DollarSign, Search, ChevronLeft, ChevronRight, RefreshCw, Filter,
  Download, Upload, Plus, Edit, Trash2, User, Package,
  CheckSquare, Square, Pencil, Trash
} from 'lucide-react';
import {
  SalesRevenueItem,
  SalesRevenueSummary,
  loadSalesRevenueData,
  generateSalesRevenueSummary,
  createSalesRevenueItem,
  updateSalesRevenueItem,
  deleteSalesRevenueItem,
  importSalesRevenueData,
  exportSalesRevenueData
} from '../services/salesRevenue2025Service';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import SalesRevenueFieldMappingModal from '../components/SalesRevenueFieldMappingModal';
import SalesRevenueForm from '../components/SalesRevenueForm';
import BulkEditModal from '../components/BulkEditModal';
import InteractiveFilter from '../components/InteractiveFilter';
import DateRangeFilter from '../components/DateRangeFilter';

export default function SalesRevenue2025DataMaster() {
  const [salesRevenueData, setSalesRevenueData] = useState<SalesRevenueItem[]>([]);
  const [filteredData, setFilteredData] = useState<SalesRevenueItem[]>([]);
  const [summary, setSummary] = useState<SalesRevenueSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<string>('customerName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Filter states
  const [filterCustomer, setFilterCustomer] = useState<string>('all');
  const [filterSalesman, setFilterSalesman] = useState<string>('all');
  const [filterDateFrom, setFilterDateFrom] = useState<string>('');
  const [filterDateTo, setFilterDateTo] = useState<string>('');

  // Unique values for filters
  const [uniqueCustomers, setUniqueCustomers] = useState<string[]>([]);
  const [uniqueSalesmen, setUniqueSalesmen] = useState<string[]>([]);

  // Form states
  const [showForm, setShowForm] = useState(false);
  const [currentItem, setCurrentItem] = useState<Partial<SalesRevenueItem>>({});
  const [isNewItem, setIsNewItem] = useState(true);

  // Import states
  const [importFile, setImportFile] = useState<File | null>(null);
  const [showMappingModal, setShowMappingModal] = useState(false);
  const [availableFields, setAvailableFields] = useState<string[]>([]);
  const [fieldMapping, setFieldMapping] = useState<Record<string, string>>({});
  const [sampleData, setSampleData] = useState<Record<string, any> | null>(null);
  const [importLoading, setImportLoading] = useState(false);

  // Bulk action states
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showBulkEditModal, setShowBulkEditModal] = useState(false);
  const [bulkEditData, setBulkEditData] = useState<Partial<SalesRevenueItem>>({});

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load data on component mount
  useEffect(() => {
    loadSalesRevenueItems();
  }, []);

  // Filter and sort data when dependencies change
  useEffect(() => {
    filterAndSortData();
  }, [salesRevenueData, searchQuery, sortField, sortDirection, filterCustomer, filterSalesman, filterDateFrom, filterDateTo]);

  // Update summary when filtered data changes
  useEffect(() => {
    // Generate summary based on filtered data
    if (filteredData.length > 0) {
      const filteredSummary = generateSalesRevenueSummary(filteredData);
      setSummary(filteredSummary);
    }
  }, [filteredData]);

  // Load sales revenue data
  const loadSalesRevenueItems = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Loading sales revenue data...');
      const data = await loadSalesRevenueData();

      if (data.length === 0) {
        console.warn('No sales revenue data returned');
      } else {
        console.log('Sales revenue data loaded successfully:', data.length, 'items');
        setSalesRevenueData(data);

        // Note: We don't set summary here anymore as it will be set by the useEffect
        // that watches filteredData

        // Extract unique values for filters
        const customers = Array.from(new Set(data.map(item => item.customerName))).sort();
        const salesmen = Array.from(new Set(data.map(item => item.salesman))).sort();

        setUniqueCustomers(customers);
        setUniqueSalesmen(salesmen);
      }
    } catch (err) {
      console.error('Error loading sales revenue data:', err);
      setError('Failed to load sales revenue data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter and sort data
  const filterAndSortData = () => {
    // Set loading state when filtering large datasets
    if (salesRevenueData.length > 1000) {
      setIsLoading(true);
    }

    // Use setTimeout to prevent UI freezing when filtering large datasets
    setTimeout(() => {
      let filtered = [...salesRevenueData];

      // Apply search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        filtered = filtered.filter(item =>
          item.customerName.toLowerCase().includes(query) ||
          item.salesman.toLowerCase().includes(query) ||
          item.materialDescription.toLowerCase().includes(query)
        );
      }

      // Apply customer filter
      if (filterCustomer !== 'all') {
        filtered = filtered.filter(item => item.customerName === filterCustomer);
      }

      // Apply salesman filter
      if (filterSalesman !== 'all') {
        filtered = filtered.filter(item => item.salesman === filterSalesman);
      }

      // Apply date range filter
      if (filterDateFrom) {
        filtered = filtered.filter(item => {
          if (!item.billingDate) return false;
          return new Date(item.billingDate) >= new Date(filterDateFrom);
        });
      }

      if (filterDateTo) {
        filtered = filtered.filter(item => {
          if (!item.billingDate) return false;
          return new Date(item.billingDate) <= new Date(filterDateTo);
        });
      }

      // Apply sorting
      filtered.sort((a, b) => {
        const aValue = a[sortField];
        const bValue = b[sortField];

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortDirection === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        } else {
          return sortDirection === 'asc'
            ? (aValue > bValue ? 1 : -1)
            : (bValue > aValue ? 1 : -1);
        }
      });

      setFilteredData(filtered);
      setCurrentPage(1); // Reset to first page when filters change
      setIsLoading(false); // End loading state
    }, 0); // Using setTimeout with 0ms delay to allow UI to update
  };

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Reset filters
  const resetFilters = () => {
    setSearchQuery('');
    setFilterCustomer('all');
    setFilterSalesman('all');
    setFilterDateFrom('');
    setFilterDateTo('');
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  // Format date
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';

    try {
      // First, try to parse as ISO date
      const date = new Date(dateString);

      // Check if valid date
      if (!isNaN(date.getTime())) {
        return format(date, 'dd MMM yyyy');
      }

      // If not a valid date, check if it's a number (Excel date)
      const numValue = Number(dateString);
      if (!isNaN(numValue)) {
        // Excel dates are days since 1900-01-01 (except for the leap year bug)
        const excelEpoch = new Date(1899, 11, 30);
        const excelDate = new Date(excelEpoch.getTime() + numValue * 24 * 60 * 60 * 1000);

        if (!isNaN(excelDate.getTime())) {
          return format(excelDate, 'dd MMM yyyy');
        }
      }

      // If all else fails, return the original string
      return dateString;
    } catch (e) {
      console.error('Error formatting date:', dateString, e);
      return dateString;
    }
  };

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  // Calculate pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredData.slice(indexOfFirstItem, indexOfLastItem);

  // Pagination controls
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);
  const nextPage = () => setCurrentPage(prev => Math.min(prev + 1, totalPages));
  const prevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1));

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    // Show warning for very large page sizes
    if (newItemsPerPage >= 1000 && filteredData.length > 1000) {
      if (window.confirm(
        `Loading ${newItemsPerPage} items per page may affect performance. Continue?`
      )) {
        setItemsPerPage(newItemsPerPage);
        // Reset to first page when changing items per page to avoid confusion
        setCurrentPage(1);
        // Set loading state to true to show loading indicator
        setIsLoading(true);
        // Use setTimeout to allow UI to update before processing large dataset
        setTimeout(() => {
          setIsLoading(false);
        }, 100);
      }
    } else {
      setItemsPerPage(newItemsPerPage);
      // Reset to first page when changing items per page to avoid confusion
      setCurrentPage(1);
    }
  };

  // Handle file import
  const handleFileImport = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      return;
    }

    const file = e.target.files[0];
    setImportFile(file);
    setImportLoading(true);

    try {
      // Read the file
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data);

      // Get the first worksheet
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];

      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      if (jsonData.length === 0) {
        throw new Error('No data found in the imported file');
      }

      console.log('Imported data sample:', jsonData[0]);

      // Get all possible field names from the first row
      const fields = Object.keys(jsonData[0] as object);
      setAvailableFields(fields);
      setSampleData(jsonData[0] as Record<string, any>);

      // Try to auto-map fields
      const newMapping: Record<string, string> = {};

      // Map fields based on similar names
      fields.forEach(field => {
        const lowerField = field.toLowerCase();

        if (lowerField.includes('customer') || lowerField.includes('client')) {
          newMapping.customerName = field;
        } else if (lowerField.includes('salesman') || lowerField.includes('sales')) {
          newMapping.salesman = field;
        } else if (lowerField.includes('material') || lowerField.includes('product')) {
          newMapping.materialDescription = field;
        } else if (lowerField.includes('qty') || lowerField.includes('quantity')) {
          newMapping.qty = field;
        } else if (lowerField.includes('revenue') || lowerField.includes('amount')) {
          newMapping.revenueInDocCurr = field;
        } else if (lowerField.includes('billing') || lowerField.includes('invoice')) {
          newMapping.billingDate = field;
        } else if (lowerField.includes('po') || lowerField.includes('purchase')) {
          newMapping.poDate = field;
        }
      });

      setFieldMapping(newMapping);
      setShowMappingModal(true);
    } catch (error) {
      console.error('Error reading import file:', error);
      setError(`Failed to read import file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setImportFile(null);

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } finally {
      setImportLoading(false);
    }
  };

  // Process import after field mapping is confirmed
  const processImport = async () => {
    if (!importFile) {
      setError('No file selected for import');
      return;
    }

    // Show warning for large files
    if (importFile.size > 5 * 1024 * 1024) { // 5MB
      if (!window.confirm('This is a large file and may take some time to process. Continue?')) {
        return;
      }
    }

    setImportLoading(true);
    setError(null);

    try {
      // Show progress message for large files
      if (importFile.size > 1 * 1024 * 1024) { // 1MB
        console.log(`Processing large file (${(importFile.size / (1024 * 1024)).toFixed(2)} MB). This may take a moment...`);
      }

      const result = await importSalesRevenueData(importFile, fieldMapping);

      if (result.success) {
        // Reload data after import
        await loadSalesRevenueItems();

        // Show detailed success message
        const successMessage = `Successfully imported ${result.importedCount} items.`;
        const errorMessage = result.errors.length > 0
          ? `\n\n${result.errors.length} errors occurred during import.`
          : '';

        alert(successMessage + errorMessage);

        // Close modal and reset import state
        setShowMappingModal(false);
        setImportFile(null);
        setFieldMapping({});
        setSampleData(null);

        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        setError('Failed to import data');
      }
    } catch (error) {
      console.error('Error importing data:', error);
      setError(`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setImportLoading(false);
    }
  };

  // Handle add new item
  const handleAddNew = () => {
    setCurrentItem({});
    setIsNewItem(true);
    setShowForm(true);
  };

  // Handle edit item
  const handleEdit = (item: SalesRevenueItem) => {
    setCurrentItem(item);
    setIsNewItem(false);
    setShowForm(true);
  };

  // Handle delete item
  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this item?')) {
      try {
        console.log('Deleting item with ID:', id);

        // Verify the item exists before attempting to delete
        const items = await loadSalesRevenueData();
        const itemToDelete = items.find(item => item.id === id);

        if (!itemToDelete) {
          console.error('Item not found with ID:', id);
          setError(`Failed to delete item: Item with ID ${id} not found`);
          return;
        }

        console.log('Found item to delete:', itemToDelete);

        const success = await deleteSalesRevenueItem(id);
        console.log('Delete operation result:', success);

        if (success) {
          // Reload data after delete
          await loadSalesRevenueItems();
          console.log('Data reloaded after delete');
        } else {
          console.error('Delete operation returned false');
          setError('Failed to delete item');
        }
      } catch (error) {
        console.error('Error deleting item:', error);
        setError(`Delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  };

  // Handle form submit
  const handleFormSubmit = async (item: Partial<SalesRevenueItem>) => {
    try {
      if (isNewItem) {
        await createSalesRevenueItem(item as Omit<SalesRevenueItem, 'id'>);
      } else {
        if (!currentItem.id) {
          throw new Error('Item ID is missing');
        }
        await updateSalesRevenueItem(currentItem.id, item);
      }

      // Reload data and close form
      await loadSalesRevenueItems();
      setShowForm(false);
    } catch (error) {
      console.error('Error saving item:', error);
      setError(`Save failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // State for tracking if all data is selected
  const [selectAllData, setSelectAllData] = useState(false);

  // Handle select all on current page
  const handleSelectAll = () => {
    if (selectAll) {
      // If already all selected on current page, deselect all
      setSelectedItems([]);
      setSelectAllData(false);
    } else {
      // Select all items on the current page
      const pageItemIds = currentItems.map(item => item.id);
      setSelectedItems(pageItemIds);
    }
    setSelectAll(!selectAll);
  };

  // Handle select all data across all pages
  const handleSelectAllData = () => {
    if (selectAllData) {
      // If already all data selected, deselect all
      setSelectedItems([]);
      setSelectAll(false);
    } else {
      // Select all items across all pages
      const allItemIds = filteredData.map(item => item.id);
      setSelectedItems(allItemIds);
      setSelectAll(true);
    }
    setSelectAllData(!selectAllData);
  };

  // Handle individual item selection
  const handleSelectItem = (id: string) => {
    if (selectedItems.includes(id)) {
      // If already selected, remove from selection
      setSelectedItems(selectedItems.filter(itemId => itemId !== id));
      setSelectAll(false);
      setSelectAllData(false);
    } else {
      // Add to selection
      setSelectedItems([...selectedItems, id]);

      // Check if all items on the current page are now selected
      const allCurrentPageSelected = currentItems.every(item =>
        selectedItems.includes(item.id) || item.id === id
      );

      if (allCurrentPageSelected) {
        setSelectAll(true);

        // Check if all items across all pages are now selected
        const allItemsSelected = filteredData.every(item =>
          selectedItems.includes(item.id) || item.id === id
        );

        if (allItemsSelected) {
          setSelectAllData(true);
        }
      }
    }
  };

  // Handle bulk edit
  const handleBulkEdit = () => {
    if (selectedItems.length === 0) {
      alert('Please select at least one item to edit');
      return;
    }

    setBulkEditData({});
    setShowBulkEditModal(true);
  };

  // Handle bulk edit submit
  const handleBulkEditSubmit = async (data: Partial<SalesRevenueItem>) => {
    if (Object.keys(data).length === 0) {
      alert('Please select at least one field to update');
      return;
    }

    try {
      // Show confirmation for large updates
      if (selectedItems.length > 100) {
        if (!window.confirm(`You are about to update ${selectedItems.length} items. This operation cannot be undone. Continue?`)) {
          return;
        }
      }

      // Set loading state for large operations
      if (selectedItems.length > 500) {
        setIsLoading(true);
      }

      // Update each selected item
      for (const id of selectedItems) {
        await updateSalesRevenueItem(id, data);
      }

      // Reload data and close modal
      await loadSalesRevenueItems();
      setShowBulkEditModal(false);

      // Clear selection
      setSelectedItems([]);
      setSelectAll(false);
      setSelectAllData(false);

      alert(`Successfully updated ${selectedItems.length} items`);
    } catch (error) {
      console.error('Error bulk editing items:', error);
      setError(`Bulk edit failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) {
      alert('Please select at least one item to delete');
      return;
    }

    // Show a more detailed confirmation for large deletions
    const confirmMessage = selectAllData
      ? `Are you sure you want to delete ALL ${filteredData.length} items? This action cannot be undone.`
      : `Are you sure you want to delete ${selectedItems.length} selected items?`;

    if (window.confirm(confirmMessage)) {
      try {
        console.log('Bulk deleting items with IDs:', selectedItems);

        // Set loading state for large operations
        if (selectedItems.length > 100) {
          setIsLoading(true);
        }

        // Verify items exist before attempting to delete
        const items = await loadSalesRevenueData();
        const itemsToDelete = items.filter(item => selectedItems.includes(item.id));

        if (itemsToDelete.length !== selectedItems.length) {
          console.error('Some items not found:',
            selectedItems.filter(id => !items.some(item => item.id === id)));
          setError('Some items could not be found for deletion');
        }

        console.log('Found items to delete:', itemsToDelete);

        // Store the count before deletion
        const countToDelete = selectedItems.length;

        // Process deletions in batches for better performance with large datasets
        const batchSize = 50;
        let successCount = 0;

        for (let i = 0; i < selectedItems.length; i += batchSize) {
          const batch = selectedItems.slice(i, i + batchSize);

          // Process each item in the current batch
          for (const id of batch) {
            const success = await deleteSalesRevenueItem(id);
            if (success) {
              successCount++;
            } else {
              console.error('Failed to delete item with ID:', id);
            }
          }

          // Update progress for large operations
          if (selectedItems.length > 100) {
            console.log(`Deleted ${successCount} out of ${countToDelete} items so far...`);
          }
        }

        console.log(`Deleted ${successCount} out of ${countToDelete} items total`);

        // Reload data
        await loadSalesRevenueItems();

        // Clear selection
        setSelectedItems([]);
        setSelectAll(false);
        setSelectAllData(false);

        if (successCount === countToDelete) {
          alert(`Successfully deleted ${successCount} items`);
        } else {
          alert(`Deleted ${successCount} out of ${countToDelete} items. Some items could not be deleted.`);
        }
      } catch (error) {
        console.error('Error bulk deleting items:', error);
        setError(`Bulk delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Export to Excel/CSV
  const exportToExcel = (format: 'xlsx' | 'csv' = 'xlsx') => {
    try {
      exportSalesRevenueData(filteredData, format);
    } catch (error) {
      console.error('Error exporting data:', error);
      setError(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <div className="space-y-6">
      {/* Field Mapping Modal */}
      <SalesRevenueFieldMappingModal
        isOpen={showMappingModal}
        onClose={() => setShowMappingModal(false)}
        availableFields={availableFields}
        fieldMapping={fieldMapping}
        setFieldMapping={setFieldMapping}
        onConfirm={processImport}
        sampleData={sampleData}
      />

      {/* Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <SalesRevenueForm
              item={currentItem}
              onSubmit={handleFormSubmit}
              onCancel={() => setShowForm(false)}
              isNew={isNewItem}
            />
          </div>
        </div>
      )}

      {/* Bulk Edit Modal */}
      <BulkEditModal
        isOpen={showBulkEditModal}
        onClose={() => setShowBulkEditModal(false)}
        onSubmit={handleBulkEditSubmit}
        selectedCount={selectedItems.length}
        isAllDataSelected={selectAllData}
        totalDataCount={filteredData.length}
      />

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <DollarSign className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Sales Revenue 2025 Data Master</h1>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={handleAddNew}
            className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus size={16} className="mr-2" />
            Add New
          </button>

          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileImport}
            accept=".xlsx,.xls,.csv"
            className="hidden"
          />

          <button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center px-3 py-2 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200"
            disabled={importLoading}
          >
            <Upload size={16} className="mr-2" />
            {importLoading ? 'Importing...' : 'Import Data'}
          </button>

          <button
            onClick={() => exportToExcel('xlsx')}
            className="flex items-center px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200"
          >
            <Download size={16} className="mr-2" />
            Export Excel
          </button>

          <button
            onClick={loadSalesRevenueItems}
            className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh Data
          </button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <div className="bg-blue-50 p-3 rounded-lg mb-4 flex items-center justify-between">
          <div className="flex items-center">
            <span className="font-medium text-blue-700 mr-2">
              {selectedItems.length} items selected
              {selectAllData && ` (All ${filteredData.length} items)`}
            </span>
            {!selectAllData && filteredData.length > itemsPerPage && (
              <button
                onClick={handleSelectAllData}
                className="ml-2 text-sm text-blue-600 hover:text-blue-800 underline"
              >
                Select all {filteredData.length} items
              </button>
            )}
            {selectAllData && (
              <button
                onClick={() => {
                  setSelectedItems([]);
                  setSelectAll(false);
                  setSelectAllData(false);
                }}
                className="ml-2 text-sm text-blue-600 hover:text-blue-800 underline"
              >
                Clear selection
              </button>
            )}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleBulkEdit}
              className="flex items-center px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            >
              <Pencil size={16} className="mr-2" />
              Bulk Edit
            </button>
            <button
              onClick={handleBulkDelete}
              className="flex items-center px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              <Trash size={16} className="mr-2" />
              Bulk Delete
            </button>
          </div>
        </div>
      )}

      {/* Score Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{formatCurrency(summary.totalRevenue)}</h3>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Customers</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{summary.totalCustomers}</h3>
              </div>
              <div className="p-3 bg-indigo-100 rounded-full">
                <User className="h-6 w-6 text-indigo-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Items</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{summary.totalItems.toLocaleString()} unit</h3>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Package className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="md:col-span-2">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Search by customer, salesman, or material..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div>
            <InteractiveFilter
              label="Customer"
              options={uniqueCustomers}
              value={filterCustomer}
              onChange={(value) => setFilterCustomer(typeof value === 'string' ? value : 'all')}
              placeholder="All Customers"
            />
          </div>

          <div>
            <InteractiveFilter
              label="Salesman"
              options={uniqueSalesmen}
              value={filterSalesman}
              onChange={(value) => setFilterSalesman(typeof value === 'string' ? value : 'all')}
              placeholder="All Salesmen"
            />
          </div>

          <div className="lg:col-span-2">
            <DateRangeFilter
              label="Date Range"
              startDate={filterDateFrom}
              endDate={filterDateTo}
              onStartDateChange={setFilterDateFrom}
              onEndDateChange={setFilterDateTo}
            />
          </div>

          <div className="flex items-end">
            <button
              onClick={resetFilters}
              className="flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Filter className="h-4 w-4 mr-2" />
              Reset Filters
            </button>
          </div>
        </div>
      </div>

      {/* Sales Revenue Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
            <p className="text-gray-600">
              {itemsPerPage > 500
                ? `Loading large dataset (${itemsPerPage} items per page). This may take a moment...`
                : "Loading sales revenue data..."}
            </p>
          </div>
        ) : error ? (
          <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-yellow-700">{error}</p>
                <div className="mt-2 flex space-x-2">
                  <button
                    onClick={loadSalesRevenueItems}
                    className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200 text-sm font-medium"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : filteredData.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-600">No sales revenue data found matching your criteria.</p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th
                      scope="col"
                      className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      <div className="flex justify-center items-center">
                        <button
                          onClick={handleSelectAll}
                          className="focus:outline-none"
                          title={selectAll ? "Deselect All" : "Select All"}
                        >
                          {selectAll ? (
                            <CheckSquare className="h-5 w-5 text-blue-600" />
                          ) : (
                            <Square className="h-5 w-5 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('customerName')}
                    >
                      Customer Name {renderSortIndicator('customerName')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('salesman')}
                    >
                      Salesman {renderSortIndicator('salesman')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('materialDescription')}
                    >
                      Material Description {renderSortIndicator('materialDescription')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('qty')}
                    >
                      Qty {renderSortIndicator('qty')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('revenueInDocCurr')}
                    >
                      Revenue {renderSortIndicator('revenueInDocCurr')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('billingDate')}
                    >
                      Billing Date {renderSortIndicator('billingDate')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('poDate')}
                    >
                      PO Date {renderSortIndicator('poDate')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {currentItems.length > 0 ? (
                    // Use React.memo or a similar optimization for large datasets
                    currentItems.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-3 py-4 whitespace-nowrap text-center">
                          <div className="flex justify-center">
                            <button
                              onClick={() => handleSelectItem(item.id)}
                              className="focus:outline-none"
                            >
                              {selectedItems.includes(item.id) ? (
                                <CheckSquare className="h-5 w-5 text-blue-600" />
                              ) : (
                                <Square className="h-5 w-5 text-gray-400" />
                              )}
                            </button>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {item.customerName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.salesman}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.materialDescription}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.qty.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatCurrency(item.revenueInDocCurr)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(item.billingDate)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(item.poDate)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => handleEdit(item)}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              <Edit size={16} />
                            </button>
                            <button
                              onClick={() => handleDelete(item.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={9} className="px-6 py-4 text-center text-sm text-gray-500">
                        No data found matching your criteria
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={prevPage}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    currentPage === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                  className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    currentPage === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div className="flex items-center">
                  <p className="text-sm text-gray-700 mr-4">
                    Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                    <span className="font-medium">
                      {Math.min(indexOfLastItem, filteredData.length)}
                    </span>{' '}
                    of <span className="font-medium">{filteredData.length}</span> results
                  </p>

                  <div className="flex items-center">
                    <label htmlFor="itemsPerPage" className="text-sm text-gray-600 mr-2">
                      View:
                    </label>
                    <select
                      id="itemsPerPage"
                      value={itemsPerPage}
                      onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
                      className="border border-gray-300 rounded-md py-1 px-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value={10}>10</option>
                      <option value={50}>50</option>
                      <option value={100}>100</option>
                      <option value={500}>500</option>
                      <option value={1000}>1000</option>
                      <option value={10000}>10000</option>
                    </select>
                  </div>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={prevPage}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === 1
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Previous</span>
                      <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                    </button>

                    {/* Page numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNumber;
                      if (totalPages <= 5) {
                        pageNumber = i + 1;
                      } else if (currentPage <= 3) {
                        pageNumber = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNumber = totalPages - 4 + i;
                      } else {
                        pageNumber = currentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageNumber}
                          onClick={() => paginate(pageNumber)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === pageNumber
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {pageNumber}
                        </button>
                      );
                    })}

                    <button
                      onClick={nextPage}
                      disabled={currentPage === totalPages}
                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === totalPages
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Next</span>
                      <ChevronRight className="h-5 w-5" aria-hidden="true" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
