import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js';
import { <PERSON>, Pie, Radar } from 'react-chartjs-2';
import { SwotAnalysisResult, SwotItem } from '../types/swotAnalysis';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend
);

interface SwotAnalysisChartsProps {
  analysisResult: SwotAnalysisResult;
}

const SwotAnalysisCharts: React.FC<SwotAnalysisChartsProps> = ({ analysisResult }) => {
  // Common chart options
  const chartOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'SWOT Analysis Impact Scores',
      },
    },
  };

  // Generate data for SWOT impact score bar chart
  const generateImpactScoreData = () => {
    // Get top 5 items from each category by impact score
    const topStrengths = [...analysisResult.strengths]
      .sort((a, b) => b.impactScore - a.impactScore)
      .slice(0, 5);

    const topWeaknesses = [...analysisResult.weaknesses]
      .sort((a, b) => b.impactScore - a.impactScore)
      .slice(0, 5);

    const topOpportunities = [...analysisResult.opportunities]
      .sort((a, b) => b.impactScore - a.impactScore)
      .slice(0, 5);

    const topThreats = [...analysisResult.threats]
      .sort((a, b) => b.impactScore - a.impactScore)
      .slice(0, 5);

    return {
      labels: ['Strengths', 'Weaknesses', 'Opportunities', 'Threats'],
      datasets: [
        {
          label: 'Average Impact Score',
          data: [
            topStrengths.reduce((sum, item) => sum + item.impactScore, 0) / (topStrengths.length || 1),
            topWeaknesses.reduce((sum, item) => sum + item.impactScore, 0) / (topWeaknesses.length || 1),
            topOpportunities.reduce((sum, item) => sum + item.impactScore, 0) / (topOpportunities.length || 1),
            topThreats.reduce((sum, item) => sum + item.impactScore, 0) / (topThreats.length || 1)
          ],
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ],
          borderColor: [
            'rgba(75, 192, 192, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 159, 64, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
  };

  // Generate data for SWOT distribution pie chart
  const generateDistributionData = () => {
    return {
      labels: ['Strengths', 'Weaknesses', 'Opportunities', 'Threats'],
      datasets: [
        {
          data: [
            analysisResult.strengths.length,
            analysisResult.weaknesses.length,
            analysisResult.opportunities.length,
            analysisResult.threats.length
          ],
          backgroundColor: [
            'rgba(75, 192, 192, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ],
          borderColor: [
            'rgba(75, 192, 192, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 159, 64, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
  };

  // Generate data for SWOT radar chart
  const generateRadarData = () => {
    // Get average impact score for each category
    const avgStrengthScore = analysisResult.strengths.reduce((sum, item) => sum + item.impactScore, 0) /
      (analysisResult.strengths.length || 1);

    const avgWeaknessScore = analysisResult.weaknesses.reduce((sum, item) => sum + item.impactScore, 0) /
      (analysisResult.weaknesses.length || 1);

    const avgOpportunityScore = analysisResult.opportunities.reduce((sum, item) => sum + item.impactScore, 0) /
      (analysisResult.opportunities.length || 1);

    const avgThreatScore = analysisResult.threats.reduce((sum, item) => sum + item.impactScore, 0) /
      (analysisResult.threats.length || 1);

    return {
      labels: ['Strengths', 'Opportunities', 'Weaknesses', 'Threats'],
      datasets: [
        {
          label: analysisResult.companyName,
          data: [avgStrengthScore, avgOpportunityScore, avgWeaknessScore, avgThreatScore],
          backgroundColor: 'rgba(54, 162, 235, 0.2)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 2,
        },
        // Add manual input indicator if used
        ...(analysisResult.manualInputUsed ? [
          {
            label: 'Manual Input',
            data: [
              analysisResult.strengths.filter(s => s.dataSource === 'manual_input').length > 0 ? 8 : 0,
              analysisResult.opportunities.filter(o => o.dataSource === 'manual_input').length > 0 ? 8 : 0,
              analysisResult.weaknesses.filter(w => w.dataSource === 'manual_input').length > 0 ? 8 : 0,
              analysisResult.threats.filter(t => t.dataSource === 'manual_input').length > 0 ? 8 : 0
            ],
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 2,
          }
        ] : [])
      ]
    };
  };

  // Generate data for recommendations priority chart
  const generateRecommendationsPriorityData = () => {
    const highPriority = analysisResult.recommendations.filter(r => r.priority === 'high').length;
    const mediumPriority = analysisResult.recommendations.filter(r => r.priority === 'medium').length;
    const lowPriority = analysisResult.recommendations.filter(r => r.priority === 'low').length;

    return {
      labels: ['High Priority', 'Medium Priority', 'Low Priority'],
      datasets: [
        {
          data: [highPriority, mediumPriority, lowPriority],
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(75, 192, 192, 0.6)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(75, 192, 192, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
  };

  return (
    <div className="space-y-8">
      {/* SWOT Radar Chart */}
      <div className="bg-white p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-800 mb-4">SWOT Radar</h3>
        <div className="h-64">
          <Radar
            data={generateRadarData()}
            options={{
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                r: {
                  min: 0,
                  max: 10,
                  ticks: {
                    stepSize: 2
                  }
                }
              }
            }}
          />
        </div>
      </div>

      {/* SWOT Impact Score Bar Chart */}
      <div className="bg-white p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Impact Score</h3>
        <div className="h-64">
          <Bar
            options={chartOptions}
            data={generateImpactScoreData()}
          />
        </div>
      </div>

      {/* SWOT Distribution Pie Chart */}
      <div className="bg-white p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-800 mb-4">SWOT Distribution</h3>
        <div className="h-64">
          <Pie
            data={generateDistributionData()}
            options={{
              responsive: true,
              maintainAspectRatio: false
            }}
          />
        </div>
      </div>

      {/* Recommendations Priority Pie Chart */}
      <div className="bg-white p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Recommendations by Priority</h3>
        <div className="h-64">
          <Pie
            data={generateRecommendationsPriorityData()}
            options={{
              responsive: true,
              maintainAspectRatio: false
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default SwotAnalysisCharts;
