import {
  ContentGenerationRequest,
  SocialMediaPlatform,
  ContentType,
  InstagramCalendarDay
} from '../types/socialMedia';
import {
  CalendarDay,
  SeasonalInsight,
  SeasonalFactor,
  SeasonalFactorType,
  RecommendationLevel
} from '../types/seasonalMarketing';

/**
 * Convert calendar data (day and/or insight) to a content generation request
 */
export const convertCalendarDataToContentRequest = (
  selectedDay?: CalendarDay,
  selectedInsight?: SeasonalInsight
): Partial<ContentGenerationRequest> => {
  // Initialize the request with default values
  const request: Partial<ContentGenerationRequest> = {
    platform: SocialMediaPlatform.INSTAGRAM,
    contentType: ContentType.IMAGE,
    language: 'id'
  };

  // If we have a selected day, use its data
  if (selectedDay) {
    // Extract product details from the day's factors
    const productDetails = extractProductDetailsFromFactors(selectedDay.factors);
    if (productDetails) {
      request.productDetails = productDetails;
    }

    // Set tone based on recommendation level
    request.tone = getToneFromRecommendationLevel(selectedDay.recommendationLevel);

    // Set campaign goals based on the day's score
    request.campaignGoals = getCampaignGoalsFromScore(selectedDay.score);
  }

  // If we have a selected insight, use its data (overriding day data if both exist)
  if (selectedInsight) {
    // Use insight title and description for product details
    request.productDetails = `${selectedInsight.title}. ${selectedInsight.description}`;

    // Use recommended products if available
    if (selectedInsight.recommendedProducts && selectedInsight.recommendedProducts.length > 0) {
      request.productDetails += `\nProduk yang direkomendasikan: ${selectedInsight.recommendedProducts.join(', ')}`;
    }

    // Use recommended promo types if available
    if (selectedInsight.recommendedPromoTypes && selectedInsight.recommendedPromoTypes.length > 0) {
      request.campaignGoals = `Promosi dengan tipe: ${selectedInsight.recommendedPromoTypes.join(', ')}`;
    }

    // Set target audience based on insight factors
    request.targetAudience = getTargetAudienceFromFactors(selectedInsight.factors);
  }

  return request;
};

/**
 * Extract product details from seasonal factors
 */
const extractProductDetailsFromFactors = (factors: SeasonalFactor[]): string | undefined => {
  // Look for mining operations factors first
  const miningFactors = factors.filter(f => f.type === SeasonalFactorType.MINING_OPERATIONS);
  if (miningFactors.length > 0) {
    return `Produk ban untuk operasi pertambangan. ${miningFactors.map(f => f.description).join(' ')}`;
  }

  // Look for maintenance factors
  const maintenanceFactors = factors.filter(f => f.type === SeasonalFactorType.MAINTENANCE);
  if (maintenanceFactors.length > 0) {
    return `Produk ban untuk periode maintenance. ${maintenanceFactors.map(f => f.description).join(' ')}`;
  }

  // If no specific factors found, use a generic description
  if (factors.length > 0) {
    return `Produk ban Michelin dengan performa terbaik. ${factors[0].description}`;
  }

  return undefined;
};

/**
 * Get tone from recommendation level
 */
const getToneFromRecommendationLevel = (level: RecommendationLevel): string => {
  switch (level) {
    case RecommendationLevel.EXCELLENT:
      return 'enthusiastic';
    case RecommendationLevel.GOOD:
      return 'professional';
    case RecommendationLevel.NEUTRAL:
      return 'educational';
    case RecommendationLevel.POOR:
      return 'formal';
    case RecommendationLevel.AVOID:
      return 'formal';
    default:
      return 'professional';
  }
};

/**
 * Get campaign goals from score
 */
const getCampaignGoalsFromScore = (score: number): string => {
  if (score >= 80) {
    return 'Meningkatkan penjualan dan awareness dengan promosi agresif';
  } else if (score >= 60) {
    return 'Membangun brand awareness dan mengedukasi pelanggan';
  } else if (score >= 40) {
    return 'Mempertahankan visibilitas brand dengan konten informatif';
  } else {
    return 'Menyediakan informasi produk dan mempersiapkan pelanggan untuk periode promosi mendatang';
  }
};

/**
 * Get target audience from factors
 */
const getTargetAudienceFromFactors = (factors: SeasonalFactor[]): string => {
  // Check for mining operations factors
  const hasMiningFactor = factors.some(f => f.type === SeasonalFactorType.MINING_OPERATIONS);
  if (hasMiningFactor) {
    return 'Perusahaan pertambangan, kontraktor alat berat, dan operator tambang';
  }

  // Check for budget cycle factors
  const hasBudgetFactor = factors.some(f => f.type === SeasonalFactorType.BUDGET_CYCLE);
  if (hasBudgetFactor) {
    return 'Manajer pengadaan, direktur operasional, dan pengambil keputusan keuangan';
  }

  // Check for maintenance factors
  const hasMaintenanceFactor = factors.some(f => f.type === SeasonalFactorType.MAINTENANCE);
  if (hasMaintenanceFactor) {
    return 'Manajer maintenance, teknisi, dan tim operasional';
  }

  // Default audience
  return 'Pelanggan industri ban, perusahaan logistik, dan operator armada';
};

/**
 * Generate content ideas based on calendar data
 */
export const generateContentIdeasFromCalendarData = (
  selectedDay?: CalendarDay,
  selectedInsight?: SeasonalInsight
): string[] => {
  const ideas: string[] = [];

  // Add ideas based on day recommendation level
  if (selectedDay) {
    switch (selectedDay.recommendationLevel) {
      case RecommendationLevel.EXCELLENT:
        ideas.push('Promosi bundling dengan diskon spesial');
        ideas.push('Konten testimonial dari pelanggan puas');
        ideas.push('Video demonstrasi produk di lapangan');
        break;
      case RecommendationLevel.GOOD:
        ideas.push('Highlight fitur produk unggulan');
        ideas.push('Perbandingan dengan produk kompetitor');
        ideas.push('Cerita sukses implementasi produk');
        break;
      case RecommendationLevel.NEUTRAL:
        ideas.push('Konten edukasi tentang perawatan ban');
        ideas.push('Tips memaksimalkan umur pakai ban');
        ideas.push('Informasi teknologi terbaru');
        break;
      case RecommendationLevel.POOR:
      case RecommendationLevel.AVOID:
        ideas.push('Konten informatif tentang spesifikasi produk');
        ideas.push('Artikel tentang inovasi perusahaan');
        ideas.push('Persiapan untuk musim atau periode mendatang');
        break;
    }
  }

  // Add ideas based on insight
  if (selectedInsight) {
    // Add ideas based on recommended promo types
    if (selectedInsight.recommendedPromoTypes) {
      selectedInsight.recommendedPromoTypes.forEach(promoType => {
        switch (promoType.toLowerCase()) {
          case 'bundling':
            ideas.push('Promosi bundling produk utama dengan produk sekunder');
            break;
          case 'discount':
          case 'diskon':
            ideas.push('Penawaran diskon spesial untuk periode terbatas');
            break;
          case 'trade-in':
            ideas.push('Program trade-in ban lama dengan ban baru');
            break;
          case 'performance guarantee':
          case 'garansi performa':
            ideas.push('Highlight garansi performa produk');
            break;
        }
      });
    }

    // Add ideas based on recommended products
    if (selectedInsight.recommendedProducts && selectedInsight.recommendedProducts.length > 0) {
      ideas.push(`Spotlight produk ${selectedInsight.recommendedProducts.join(', ')}`);
    }
  }

  // Add generic ideas if we don't have enough
  if (ideas.length < 3) {
    ideas.push('Konten edukasi tentang teknologi ban');
    ideas.push('Tips pemilihan ban yang tepat untuk aplikasi spesifik');
    ideas.push('Highlight keunggulan produk dibanding kompetitor');
  }

  return ideas;
};

/**
 * Convert Instagram calendar day data to content generation request
 */
export const convertInstagramCalendarDayToContentRequest = (
  instagramDay: InstagramCalendarDay
): Partial<ContentGenerationRequest> => {
  // Initialize the request with default values
  const request: Partial<ContentGenerationRequest> = {
    platform: SocialMediaPlatform.INSTAGRAM,
    contentType: ContentType.IMAGE,
    language: 'id',
    tone: 'professional',
    length: 'medium',
    includeHashtags: true,
    includeEmojis: true
  };

  // If the day has a recommendation score, use it to set the tone and campaign goals
  if (instagramDay.recommendationScore !== undefined) {
    // Set tone based on recommendation score
    if (instagramDay.recommendationScore >= 80) {
      request.tone = 'enthusiastic';
      request.campaignGoals = 'Meningkatkan penjualan dan awareness dengan promosi agresif';
    } else if (instagramDay.recommendationScore >= 60) {
      request.tone = 'professional';
      request.campaignGoals = 'Membangun brand awareness dan mengedukasi pelanggan';
    } else if (instagramDay.recommendationScore >= 40) {
      request.tone = 'educational';
      request.campaignGoals = 'Mempertahankan visibilitas brand dengan konten informatif';
    } else {
      request.tone = 'formal';
      request.campaignGoals = 'Menyediakan informasi produk dan mempersiapkan pelanggan untuk periode promosi mendatang';
    }
  }

  // If it's a holiday, add that to the product details
  if (instagramDay.isHoliday && instagramDay.holidayName) {
    request.productDetails = `Konten untuk hari libur: ${instagramDay.holidayName}. Fokus pada produk ban Michelin dengan performa terbaik.`;
    request.targetAudience = 'Pelanggan industri ban, perusahaan logistik, dan operator armada yang merayakan hari libur';
  } else {
    request.productDetails = 'Produk ban Michelin dengan performa terbaik untuk aplikasi industri dan pertambangan.';
    request.targetAudience = 'Pelanggan industri ban, perusahaan logistik, dan operator armada';
  }

  return request;
};
