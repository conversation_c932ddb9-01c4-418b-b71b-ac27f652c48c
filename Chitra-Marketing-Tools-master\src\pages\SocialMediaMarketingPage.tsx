import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger
} from '../components/ui/tabs';
import {
  Instagram,
  Calendar,
  BookOpen,
  Sparkles,
  Info,
  MessageSquare,
  BarChart,
  Settings,
  List,
  RefreshCw,
  Zap,
  PlusCircle
} from 'lucide-react';
import InstagramCalendar from '../components/InstagramCalendar';
import SocialMediaKnowledgeManager from '../components/SocialMediaKnowledgeManager';
import InstagramContentGenerator from '../components/InstagramContentGenerator';
import InstagramContentList from '../components/InstagramContentList';
import InstagramContentEditModal from '../components/InstagramContentEditModal';
import ContentImprovementTool from '../components/ContentImprovementTool';
import MonthlyContentPlanGenerator from '../components/MonthlyContentPlanGenerator';
import {
  InstagramCalendarDay,
  SocialMediaPost,
  SocialMediaKnowledgeEntry,
  ContentType,
  PostStatus,
  SocialMediaPlatform
} from '../types/socialMedia';
import { createSocialMediaPost, getAllSocialMediaPosts } from '../services/socialMediaService';

export default function SocialMediaMarketingPage() {
  // State for active tab
  const [activeTab, setActiveTab] = useState('calendar');

  // State for post creation modal
  const [showPostModal, setShowPostModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string>('');

  // State for selected calendar day
  const [selectedCalendarDay, setSelectedCalendarDay] = useState<InstagramCalendarDay | null>(null);

  // State for post details
  const [postTitle, setPostTitle] = useState('');
  const [postCaption, setPostCaption] = useState('');
  const [postContentType, setPostContentType] = useState<ContentType>(ContentType.IMAGE);
  const [postHashtags, setPostHashtags] = useState('');

  // State for edit modal
  const [showEditModal, setShowEditModal] = useState(false);
  const [postToEdit, setPostToEdit] = useState<SocialMediaPost | null>(null);

  // State for content improvement
  const [contentToImprove, setContentToImprove] = useState<string>('');

  // Initialize posts data on component mount to prevent 404 errors
  useEffect(() => {
    const initializeData = async () => {
      try {
        // Load posts to ensure data is available
        const posts = await getAllSocialMediaPosts();
        console.log(`Loaded ${posts.length} social media posts`);
      } catch (error) {
        console.error('Error initializing social media data:', error);
      }
    };

    initializeData();
  }, []);

  // Handle day click in calendar
  const handleDayClick = (day: InstagramCalendarDay) => {
    console.log('Day clicked:', day);
    setSelectedCalendarDay(day);
  };

  // Handle post click in calendar
  const handlePostClick = (post: SocialMediaPost) => {
    console.log('Post clicked:', post);
  };

  // Handle add post button click
  const handleAddPost = (date: string) => {
    setSelectedDate(date);
    setShowPostModal(true);
  };

  // Handle generate content button click
  const handleGenerateContent = (day: InstagramCalendarDay) => {
    setSelectedCalendarDay(day);
    setActiveTab('generator');
  };

  // Handle post creation
  const handleCreatePost = async () => {
    if (!postTitle || !postCaption) {
      alert('Judul dan caption harus diisi');
      return;
    }

    try {
      // Create hashtags array from comma-separated string
      const hashtags = postHashtags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag);

      // Create new post
      const newPost: Omit<SocialMediaPost, 'id' | 'createdAt' | 'updatedAt'> = {
        platform: SocialMediaPlatform.INSTAGRAM,
        contentType: postContentType,
        title: postTitle,
        caption: postCaption,
        hashtags,
        scheduledDate: selectedDate ? new Date(selectedDate) : undefined,
        status: PostStatus.SCHEDULED
      };

      await createSocialMediaPost(newPost);

      // Reset form and close modal
      resetPostForm();
      setShowPostModal(false);

      // Refresh calendar (this would typically trigger a re-fetch)
      // For now, we'll just reload the page
      window.location.reload();
    } catch (error) {
      console.error('Error creating post:', error);
      alert('Gagal membuat post');
    }
  };

  // Reset post form
  const resetPostForm = () => {
    setPostTitle('');
    setPostCaption('');
    setPostContentType(ContentType.IMAGE);
    setPostHashtags('');
  };

  // Handle content saved event
  const handleContentSaved = () => {
    // Switch back to calendar tab
    setActiveTab('calendar');
    // Reset selected calendar day
    setSelectedCalendarDay(null);
    // Reload the page to refresh the calendar
    window.location.reload();
  };

  // Handle edit post
  const handleEditPost = (post: SocialMediaPost) => {
    setPostToEdit(post);
    setShowEditModal(true);
  };

  // Handle post update after edit
  const handlePostUpdated = (updatedPost: SocialMediaPost) => {
    // Refresh the content list by switching to another tab and back
    const currentTab = activeTab;
    setActiveTab(currentTab === 'calendar' ? 'content-list' : 'calendar');
    setTimeout(() => {
      setActiveTab(currentTab);
    }, 100);
  };

  // Handle improve content button click
  const handleImproveContent = (post: SocialMediaPost) => {
    // Set the content to improve
    const contentText = post.content || post.caption;
    setContentToImprove(contentText);

    // Switch to the improvement tab
    setActiveTab('improvement');
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Social Media Marketing</h1>
        <p className="text-gray-600">Kelola konten media sosial, jadwal posting, dan strategi marketing</p>
      </div>

      <div className="mb-6 bg-white border border-gray-200 rounded-lg p-1 shadow-sm">
        <div className="flex flex-wrap">
          <button
            onClick={() => setActiveTab('calendar')}
            className={`flex items-center px-4 py-2 text-sm font-medium rounded-md ${
              activeTab === 'calendar' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            <Calendar size={16} className="mr-2" />
            Kalender Instagram
          </button>
          <button
            onClick={() => setActiveTab('content-list')}
            className={`flex items-center px-4 py-2 text-sm font-medium rounded-md ${
              activeTab === 'content-list' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            <List size={16} className="mr-2" />
            Daftar Konten
          </button>
          <button
            onClick={() => setActiveTab('knowledge')}
            className={`flex items-center px-4 py-2 text-sm font-medium rounded-md ${
              activeTab === 'knowledge' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            <BookOpen size={16} className="mr-2" />
            Knowledge Base
          </button>
          <button
            onClick={() => setActiveTab('generator')}
            className={`flex items-center px-4 py-2 text-sm font-medium rounded-md ${
              activeTab === 'generator' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            <Sparkles size={16} className="mr-2" />
            Generator Konten
          </button>
          <button
            onClick={() => setActiveTab('improvement')}
            className={`flex items-center px-4 py-2 text-sm font-medium rounded-md ${
              activeTab === 'improvement' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            <Zap size={16} className="mr-2" />
            Peningkatan Konten
          </button>
          <button
            onClick={() => setActiveTab('monthly-plan')}
            className={`flex items-center px-4 py-2 text-sm font-medium rounded-md ${
              activeTab === 'monthly-plan' ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            <Calendar size={16} className="mr-2" />
            Rencana Konten Bulanan
          </button>
        </div>
      </div>

      {/* Error handling for tab content */}
      <div className="mb-4">
        {activeTab === 'content-list' && (
          <div className="text-sm text-gray-500">
            <p>Jika konten tidak muncul, silakan klik tombol <RefreshCw size={14} className="inline" /> untuk memuat ulang data.</p>
          </div>
        )}
      </div>

      {/* Tab Content */}
      <div className="mt-0">
        {activeTab === 'calendar' && (
          <InstagramCalendar
            onDayClick={handleDayClick}
            onPostClick={handlePostClick}
            onAddPost={handleAddPost}
            onGenerateContent={handleGenerateContent}
          />
        )}

        {activeTab === 'content-list' && (
          <InstagramContentList
            onEditPost={handleEditPost}
            onImproveContent={handleImproveContent}
          />
        )}

        {activeTab === 'knowledge' && (
          <SocialMediaKnowledgeManager />
        )}

        {activeTab === 'generator' && (
          <InstagramContentGenerator
            calendarDay={selectedCalendarDay || undefined}
            onContentSaved={handleContentSaved}
          />
        )}

        {activeTab === 'improvement' && (
          <ContentImprovementTool initialContent={contentToImprove} />
        )}

        {activeTab === 'monthly-plan' && (
          <MonthlyContentPlanGenerator onSavePost={handleContentSaved} />
        )}
      </div>

      {/* Post Creation Modal */}
      {showPostModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <div className="flex items-center">
                <Instagram size={20} className="text-pink-500 mr-2" />
                <h3 className="text-lg font-medium">Buat Post Instagram Baru</h3>
              </div>
              <button
                onClick={() => setShowPostModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>

            <div className="p-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tanggal</label>
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  readOnly
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Judul Post</label>
                <input
                  type="text"
                  value={postTitle}
                  onChange={(e) => setPostTitle(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="Judul untuk post ini"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tipe Konten</label>
                <select
                  value={postContentType}
                  onChange={(e) => setPostContentType(e.target.value as ContentType)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value={ContentType.IMAGE}>Gambar</option>
                  <option value={ContentType.VIDEO}>Video</option>
                  <option value={ContentType.CAROUSEL}>Carousel</option>
                  <option value={ContentType.REEL}>Reel</option>
                  <option value={ContentType.STORY}>Story</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Caption</label>
                <textarea
                  value={postCaption}
                  onChange={(e) => setPostCaption(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 min-h-[120px]"
                  placeholder="Caption untuk post ini"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Hashtags (pisahkan dengan koma)</label>
                <input
                  type="text"
                  value={postHashtags}
                  onChange={(e) => setPostHashtags(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="hashtag1, hashtag2, hashtag3"
                />
              </div>

              <div className="flex justify-end space-x-3 pt-2">
                <button
                  onClick={() => setShowPostModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Batal
                </button>
                <button
                  onClick={handleCreatePost}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  Simpan Post
                </button>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Content Improvement Card */}
      <div className="mt-4 bg-gradient-to-r from-blue-50 to-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start">
          <Zap size={24} className="text-green-500 mr-3 mt-0.5" />
          <div>
            <h3 className="font-medium text-green-800 mb-1">AI-Powered Content Improvement</h3>
            <p className="text-sm text-green-700 mb-2">
              Tingkatkan kualitas konten media sosial Anda dengan bantuan AI. Analisis konten, dapatkan saran peningkatan, dan buat variasi A/B testing untuk meningkatkan engagement.
            </p>
            <button
              onClick={() => setActiveTab('improvement')}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <Zap size={16} className="mr-2" />
              Tingkatkan Konten Anda
            </button>
          </div>
        </div>
      </div>

      {/* Info Card */}
      <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Info size={24} className="text-blue-500 mr-3 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-800 mb-1">Tips Pengelolaan Media Sosial</h3>
            <ul className="text-sm text-blue-700 space-y-1 list-disc list-inside">
              <li>Gunakan Kalender Instagram untuk merencanakan dan menjadwalkan konten</li>
              <li>Manfaatkan Knowledge Base untuk menyimpan strategi, ide konten, dan hashtag</li>
              <li>Gunakan Generator Konten untuk membuat caption dan hashtag yang menarik</li>
              <li>Gunakan Peningkatan Konten untuk menganalisis dan meningkatkan kualitas konten Anda</li>
              <li>Manfaatkan fitur A/B Testing untuk membuat variasi konten dan menguji efektivitasnya</li>
              <li>Gunakan Rencana Konten Bulanan untuk membuat konten yang bervariasi dan tidak monoton</li>
              <li>Posting secara konsisten 2-3 kali seminggu untuk hasil optimal</li>
              <li>Analisis performa post untuk mengetahui konten yang paling efektif</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Edit Modal */}
      {showEditModal && postToEdit && (
        <InstagramContentEditModal
          post={postToEdit}
          onClose={() => setShowEditModal(false)}
          onSave={handlePostUpdated}
        />
      )}
    </div>
  );
}
