<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Template Management</h1>
                    <p class="mt-2 text-gray-600">Kelola template untuk proposal, dokumen, dan komunikasi</p>
                </div>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center">
                        <Plus class="h-4 w-4 mr-2" />
                        Buat Template
                    </button>
                </div>
            </div>

            <!-- Template Categories -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center mb-4">
                        <FileText class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Proposal Templates</h3>
                            <p class="text-sm text-gray-600">12 templates</p>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4">Template untuk proposal bisnis, penawaran, dan kontrak</p>
                    <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Kelola Templates
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center mb-4">
                        <Mail class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Email Templates</h3>
                            <p class="text-sm text-gray-600">8 templates</p>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4">Template untuk komunikasi email dengan customer</p>
                    <button class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                        Kelola Templates
                    </button>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center mb-4">
                        <MessageSquare class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">WhatsApp Templates</h3>
                            <p class="text-sm text-gray-600">15 templates</p>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-4">Template untuk komunikasi WhatsApp Business</p>
                    <button class="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                        Kelola Templates
                    </button>
                </div>
            </div>

            <!-- Recent Templates -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Template Terbaru</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div v-for="template in recentTemplates" :key="template.id" 
                             class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                            <div class="flex items-center">
                                <component :is="template.icon" class="h-6 w-6 text-gray-600 mr-3" />
                                <div>
                                    <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
                                    <p class="text-sm text-gray-600">{{ template.description }}</p>
                                    <p class="text-xs text-gray-500">Diupdate {{ template.updatedAt }}</p>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                    Edit
                                </button>
                                <button class="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200">
                                    Use
                                </button>
                                <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                                    Copy
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-yellow-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-yellow-900">Fitur Dalam Pengembangan</h3>
                        <p class="text-yellow-700 mt-1">
                            Template Management sedang dalam tahap pengembangan. Fitur yang akan tersedia:
                        </p>
                        <ul class="list-disc list-inside text-yellow-700 mt-2 space-y-1">
                            <li>Template editor dengan rich text formatting</li>
                            <li>Variable placeholder system</li>
                            <li>Template versioning dan history</li>
                            <li>Sharing dan collaboration features</li>
                            <li>Template analytics dan usage tracking</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { 
    Plus, 
    FileText, 
    Mail, 
    MessageSquare,
    Info
} from 'lucide-vue-next';
import { ref } from 'vue';

const recentTemplates = ref([
    {
        id: 1,
        name: 'Proposal Ban Heavy Equipment',
        description: 'Template proposal untuk penjualan ban alat berat',
        icon: FileText,
        updatedAt: '2 hari lalu'
    },
    {
        id: 2,
        name: 'Email Follow-up Customer',
        description: 'Template email untuk follow-up setelah presentasi',
        icon: Mail,
        updatedAt: '3 hari lalu'
    },
    {
        id: 3,
        name: 'WhatsApp Penawaran Promo',
        description: 'Template WhatsApp untuk penawaran promo bulanan',
        icon: MessageSquare,
        updatedAt: '1 minggu lalu'
    },
    {
        id: 4,
        name: 'Kontrak Penjualan',
        description: 'Template kontrak untuk penjualan volume besar',
        icon: FileText,
        updatedAt: '2 minggu lalu'
    }
]);
</script>
