/**
 * Types for the AI Negotiation Simulator
 */

import { Product } from './index';
import { KnowledgeEntry } from './knowledgeBase';

/**
 * Customer persona types for negotiation
 */
export enum CustomerPersonaType {
  PRICE_SENSITIVE = 'price_sensitive',
  LOYAL_CUSTOMER = 'loyal_customer',
  AGGRESSIVE_BUYER = 'aggressive_buyer'
}

/**
 * Customer persona details
 */
export interface CustomerPersona {
  type: CustomerPersonaType;
  name: string;
  company: string;
  description: string;
  negotiationStyle: string;
  priceExpectation: 'low' | 'medium' | 'high';
  loyaltyLevel: 'low' | 'medium' | 'high';
  valueFeatures: string[];
  painPoints: string[];
  avatar: string;
}

/**
 * Message in the negotiation chat
 */
export interface NegotiationMessage {
  id: string;
  sender: 'ai' | 'user';
  content: string;
  timestamp: Date;
  isThinking?: boolean;
}

/**
 * Negotiation session
 */
export interface NegotiationSession {
  id: string;
  startedAt: Date;
  endedAt?: Date;
  customerPersona: CustomerPersona;
  selectedProducts: {
    product: Product;
    quantity: number;
    originalPrice: number;
    negotiatedPrice?: number;
  }[];
  selectedKnowledgeEntries?: KnowledgeEntry[];
  messages: NegotiationMessage[];
  targetMargin: number;
  actualMargin?: number;
  status: 'in_progress' | 'completed' | 'abandoned';
  evaluation?: NegotiationEvaluation;
}

/**
 * Evaluation of the negotiation
 */
export interface NegotiationEvaluation {
  marginAchieved: boolean;
  marginDifference: number; // Difference between target and actual margin
  discountAppropriate: boolean;
  discountReason: string;
  alternativeStrategies: string[];
  overallScore: number; // 0-100
  strengths: string[];
  improvements: string[];
  keyInsights: string;
}

/**
 * AI request for negotiation
 */
export interface NegotiationAIRequest {
  customerPersona: CustomerPersona;
  selectedProducts: {
    product: Product;
    quantity: number;
    originalPrice: number;
    negotiatedPrice?: number;
  }[];
  messages: NegotiationMessage[];
  targetMargin: number;
  currentMargin?: number;
  isFirstMessage?: boolean;
  isEvaluation?: boolean;
  knowledgeEntries?: KnowledgeEntry[];
}

/**
 * AI response for negotiation
 */
export interface NegotiationAIResponse {
  message: string;
  suggestedAction?: 'offer_discount' | 'hold_firm' | 'suggest_alternative' | 'close_deal';
  suggestedDiscount?: number;
  reasoning?: string;
  evaluation?: NegotiationEvaluation;
}
