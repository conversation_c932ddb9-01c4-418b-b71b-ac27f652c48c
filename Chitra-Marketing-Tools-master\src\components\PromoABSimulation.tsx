import React, { useState, useEffect } from 'react';
import {
  PromoConfig,
  PromoItem,
  PromoDiscount,
  PromoResult,
  PromoType,
  DiscountType
} from '../types/promotion';
import { SimulationScenario } from '../types/abSimulation';
import { calculatePromoResult } from '../utils/promoCalculations';
import { generateABSimulationPDF } from '../services/abSimulationPdfService';
import {
  PlusCircle,
  MinusCircle,
  BarChart3,
  ArrowRight,
  FileText,
  Share2,
  ChevronDown,
  ChevronUp,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface PromoABSimulationProps {
  initialItems: PromoItem[];
  initialConfig: PromoConfig;
  initialDiscount: PromoDiscount;
}

const PromoABSimulation: React.FC<PromoABSimulationProps> = ({
  initialItems,
  initialConfig,
  initialDiscount
}) => {
  const [scenarios, setScenarios] = useState<SimulationScenario[]>([]);
  const [activeTab, setActiveTab] = useState<string>('');
  const [showComparison, setShowComparison] = useState<boolean>(false);
  const [expandedProducts, setExpandedProducts] = useState<Record<string, boolean>>({});
  const [isExporting, setIsExporting] = useState<boolean>(false);
  const [isCalculating, setIsCalculating] = useState<boolean>(false);

  // Initialize with two scenarios
  useEffect(() => {
    if (initialItems.length > 0 && !scenarios.length) {
      const scenario1: SimulationScenario = {
        id: 'scenario-1',
        name: 'Skenario A',
        items: [...initialItems],
        config: { ...initialConfig },
        discount: { ...initialDiscount },
        result: calculatePromoResult(initialItems, initialDiscount, initialConfig)
      };

      const scenario2: SimulationScenario = {
        id: 'scenario-2',
        name: 'Skenario B',
        items: [...initialItems],
        config: { ...initialConfig },
        discount: {
          ...initialDiscount,
          value: initialDiscount.value + 5 // Slightly higher discount for comparison
        },
        result: calculatePromoResult(
          initialItems,
          {
            ...initialDiscount,
            value: initialDiscount.value + 5
          },
          initialConfig
        )
      };

      setScenarios([scenario1, scenario2]);
      setActiveTab(scenario1.id);

      // Initialize expanded products state
      setExpandedProducts({
        'scenario-1': false,
        'scenario-2': false
      });
    }
  }, [initialItems, initialConfig, initialDiscount]);

  // Add a new scenario
  const handleAddScenario = () => {
    if (scenarios.length >= 3) {
      alert('Maksimal 3 skenario yang dapat dibandingkan.');
      return;
    }

    const newId = `scenario-${scenarios.length + 1}`;
    const newScenario: SimulationScenario = {
      id: newId,
      name: `Skenario ${String.fromCharCode(65 + scenarios.length)}`, // A, B, C
      items: [...initialItems],
      config: { ...initialConfig },
      discount: { ...initialDiscount },
      result: calculatePromoResult(initialItems, initialDiscount, initialConfig)
    };

    setScenarios([...scenarios, newScenario]);
    setActiveTab(newId);
  };

  // Remove a scenario
  const handleRemoveScenario = (id: string) => {
    if (scenarios.length <= 2) {
      alert('Minimal 2 skenario diperlukan untuk perbandingan.');
      return;
    }

    const updatedScenarios = scenarios.filter(scenario => scenario.id !== id);
    setScenarios(updatedScenarios);

    // If active tab is removed, set active tab to first scenario
    if (activeTab === id) {
      setActiveTab(updatedScenarios[0].id);
    }
  };

  // Update scenario name
  const handleNameChange = (id: string, name: string) => {
    const updatedScenarios = scenarios.map(scenario => {
      if (scenario.id === id) {
        return { ...scenario, name };
      }
      return scenario;
    });
    setScenarios(updatedScenarios);
  };

  // Update scenario discount
  const handleDiscountChange = (id: string, discount: PromoDiscount) => {
    setIsCalculating(true);

    // Use setTimeout to allow the UI to update with the loading state
    setTimeout(() => {
      try {
        const updatedScenarios = scenarios.map(scenario => {
          if (scenario.id === id) {
            const updatedResult = calculatePromoResult(scenario.items, discount, scenario.config);
            return {
              ...scenario,
              discount,
              result: updatedResult
            };
          }
          return scenario;
        });
        setScenarios(updatedScenarios);
      } catch (error) {
        console.error('Error calculating results:', error);
      } finally {
        setIsCalculating(false);
      }
    }, 100);
  };

  // Format currency
  const formatCurrency = (value: number): string => {
    return `Rp ${value.toLocaleString('id-ID')}`;
  };

  // Toggle product accordion
  const toggleProductAccordion = (scenarioId: string) => {
    setExpandedProducts(prev => ({
      ...prev,
      [scenarioId]: !prev[scenarioId]
    }));
  };

  // Export PDF
  const handleExportPDF = () => {
    try {
      setIsExporting(true);
      generateABSimulationPDF(scenarios, 'Perbandingan Skema Promo');
    } catch (error) {
      console.error('Error exporting PDF:', error);
      alert('Gagal mengekspor PDF. Silakan coba lagi nanti.');
    } finally {
      setIsExporting(false);
    }
  };

  // Share via WhatsApp
  const handleShareWhatsApp = () => {
    try {
      // Create a summary text
      let summaryText = `*Perbandingan Skema Promo*\n\n`;

      scenarios.forEach(scenario => {
        summaryText += `*${scenario.name}*\n`;
        summaryText += `Bentuk Promo: ${getPromoTypeLabel(scenario.discount.type)}\n`;
        summaryText += `Nilai Diskon: ${formatDiscountValue(scenario.discount.type, scenario.discount.value)}\n`;
        summaryText += `Harga Setelah Promo: ${formatCurrency(scenario.result.priceAfterPromo)}\n`;
        summaryText += `Margin: ${scenario.result.marginAfterPromo.toFixed(2)}%\n`;
        summaryText += `Estimasi Profit: ${formatCurrency(scenario.result.estimatedProfit)}\n\n`;
      });

      // Create WhatsApp URL
      const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(summaryText)}`;

      // Open WhatsApp
      window.open(whatsappUrl, '_blank');
    } catch (error) {
      console.error('Error sharing via WhatsApp:', error);
      alert('Gagal membagikan via WhatsApp. Silakan coba lagi nanti.');
    }
  };

  // Get promo type label
  const getPromoTypeLabel = (type: DiscountType): string => {
    switch (type) {
      case DiscountType.PERCENTAGE:
        return 'Diskon %';
      case DiscountType.FIXED_AMOUNT:
        return 'Diskon Nominal';
      case DiscountType.CASHBACK:
        return 'Cashback';
      case DiscountType.BONUS_UNIT:
        return 'Bonus Unit';
      default:
        return 'Diskon';
    }
  };

  // Format discount value
  const formatDiscountValue = (type: DiscountType, value: number): string => {
    if (type === DiscountType.PERCENTAGE) {
      return `${value}%`;
    } else if (type === DiscountType.BONUS_UNIT) {
      return `${value} unit`;
    } else {
      return formatCurrency(value);
    }
  };

  // Get best scenario for a metric
  const getBestScenarioForMetric = (metric: keyof PromoResult): string | undefined => {
    if (scenarios.length === 0) return undefined;

    let bestScenarioId: string | undefined;
    let bestValue: number = 0;

    // Determine comparison function based on metric
    const isHigherBetter = ['marginAfterPromo', 'estimatedProfit'].includes(metric);
    const isLowerBetter = ['priceAfterPromo', 'breakEvenPoint', 'totalPromoCost', 'promoCostPercentage'].includes(metric);
    const isHigherBetterForTotalDiscount = true; // Higher discount is better for customer

    scenarios.forEach(scenario => {
      const value = scenario.result[metric] as number;

      // Skip undefined values (like breakEvenPoint might be)
      if (value === undefined) return;

      if (!bestScenarioId) {
        bestScenarioId = scenario.id;
        bestValue = value;
        return;
      }

      if (metric === 'totalDiscount' && isHigherBetterForTotalDiscount) {
        if (value > bestValue) {
          bestScenarioId = scenario.id;
          bestValue = value;
        }
      } else if (isHigherBetter) {
        if (value > bestValue) {
          bestScenarioId = scenario.id;
          bestValue = value;
        }
      } else if (isLowerBetter) {
        if (value < bestValue && value > 0) { // Ensure positive values for metrics like breakEvenPoint
          bestScenarioId = scenario.id;
          bestValue = value;
        }
      }
    });

    return bestScenarioId;
  };

  // Generate automatic insights based on comparison
  const generateInsights = (): string => {
    if (scenarios.length < 2) {
      return "Tambahkan minimal 2 skenario untuk mendapatkan insight perbandingan.";
    }

    // Find best scenarios for key metrics
    const bestProfitScenarioId = getBestScenarioForMetric('estimatedProfit');
    const bestMarginScenarioId = getBestScenarioForMetric('marginAfterPromo');
    const bestPromoCostScenarioId = getBestScenarioForMetric('totalPromoCost');
    const bestPromoCostPercentageScenarioId = getBestScenarioForMetric('promoCostPercentage');

    if (!bestProfitScenarioId || !bestMarginScenarioId) {
      return "Tidak dapat menghasilkan insight karena data tidak lengkap.";
    }

    const bestProfitScenario = scenarios.find(s => s.id === bestProfitScenarioId);
    const bestMarginScenario = scenarios.find(s => s.id === bestMarginScenarioId);

    // Compare the two main scenarios (usually A and B)
    const scenarioA = scenarios.find(s => s.name.includes('A'));
    const scenarioB = scenarios.find(s => s.name.includes('B'));

    if (!scenarioA || !scenarioB) {
      return "Tidak dapat membandingkan skenario A dan B.";
    }

    let insight = `📊 ${scenarioA.name} memberikan estimasi profit ${formatCurrency(scenarioA.result.estimatedProfit)} dengan margin ${scenarioA.result.marginAfterPromo.toFixed(2)}%, sementara ${scenarioB.name} ${formatCurrency(scenarioB.result.estimatedProfit)} dengan margin ${scenarioB.result.marginAfterPromo.toFixed(2)}%. `;

    // Add recommendation based on profit and margin
    if (bestProfitScenarioId === bestMarginScenarioId) {
      insight += `${bestProfitScenario!.name} lebih unggul dari segi profit dan margin. `;
    } else {
      insight += `${bestProfitScenario!.name} lebih unggul dari segi profit, namun ${bestMarginScenario!.name} memberikan margin lebih tinggi. `;
    }

    // Add efficiency insight
    if (bestPromoCostPercentageScenarioId) {
      const bestEfficiencyScenario = scenarios.find(s => s.id === bestPromoCostPercentageScenarioId);
      if (bestEfficiencyScenario) {
        insight += `${bestEfficiencyScenario.name} lebih efisien dari segi biaya promo (${(bestEfficiencyScenario.result.promoCostPercentage || 0).toFixed(2)}% dari revenue). `;
      }
    }

    // Add margin safety insight
    const scenarioAMarginStatus = scenarioA.result.marginSafetyStatus;
    const scenarioBMarginStatus = scenarioB.result.marginSafetyStatus;

    if (scenarioAMarginStatus === 'safe' && scenarioBMarginStatus !== 'safe') {
      insight += `${scenarioA.name} memiliki margin yang lebih aman (≥10%). `;
    } else if (scenarioBMarginStatus === 'safe' && scenarioAMarginStatus !== 'safe') {
      insight += `${scenarioB.name} memiliki margin yang lebih aman (≥10%). `;
    } else if (scenarioAMarginStatus === 'unsafe' && scenarioBMarginStatus === 'unsafe') {
      insight += `Kedua skenario memiliki margin yang rendah (<5%). Pertimbangkan untuk menyesuaikan strategi promo. `;
    }

    return insight;
  };

  // Get margin safety badge
  const getMarginSafetyBadge = (status?: 'safe' | 'thin' | 'unsafe') => {
    switch (status) {
      case 'safe':
        return (
          <span className="ml-2 px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full flex items-center">
            <CheckCircle size={12} className="mr-1" />
            Aman
          </span>
        );
      case 'thin':
        return (
          <span className="ml-2 px-2 py-0.5 text-xs bg-yellow-100 text-yellow-800 rounded-full flex items-center">
            <AlertTriangle size={12} className="mr-1" />
            Tipis
          </span>
        );
      case 'unsafe':
        return (
          <span className="ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full flex items-center">
            <AlertTriangle size={12} className="mr-1" />
            Tidak Aman
          </span>
        );
      default:
        return null;
    }
  };

  // Get active scenario
  const activeScenario = scenarios.find(scenario => scenario.id === activeTab);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Perbandingan Skema Promo (A/B Simulation)</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowComparison(!showComparison)}
            className="flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            <BarChart3 size={16} className="mr-1" />
            {showComparison ? 'Sembunyikan Perbandingan' : 'Tampilkan Perbandingan'}
          </button>
          <button
            onClick={handleAddScenario}
            disabled={scenarios.length >= 3}
            className={`flex items-center px-3 py-1 text-sm rounded-md ${
              scenarios.length >= 3
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-green-100 text-green-700 hover:bg-green-200'
            }`}
          >
            <PlusCircle size={16} className="mr-1" />
            Tambah Skenario
          </button>
          <button
            onClick={handleExportPDF}
            disabled={scenarios.length < 2 || isExporting}
            className={`flex items-center px-3 py-1 text-sm rounded-md ${
              scenarios.length < 2 || isExporting
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            <FileText size={16} className="mr-1" />
            Export PDF
          </button>
          <button
            onClick={handleShareWhatsApp}
            disabled={scenarios.length < 2}
            className={`flex items-center px-3 py-1 text-sm rounded-md ${
              scenarios.length < 2
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-green-600 text-white hover:bg-green-700'
            }`}
          >
            <Share2 size={16} className="mr-1" />
            Bagikan via WhatsApp
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <div className="flex">
          {scenarios.map(scenario => (
            <div
              key={scenario.id}
              className={`relative py-2 px-4 cursor-pointer ${
                activeTab === scenario.id
                  ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab(scenario.id)}
            >
              <div className="flex items-center">
                <span>{scenario.name}</span>
                {scenarios.length > 2 && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveScenario(scenario.id);
                    }}
                    className="ml-2 text-gray-400 hover:text-red-500"
                  >
                    <MinusCircle size={14} />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Active Scenario Content */}
      {activeScenario && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nama Skenario
              </label>
              <input
                type="text"
                value={activeScenario.name}
                onChange={(e) => handleNameChange(activeScenario.id, e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Bentuk Promo
              </label>
              <select
                value={activeScenario.discount.type}
                onChange={(e) => handleDiscountChange(activeScenario.id, {
                  ...activeScenario.discount,
                  type: e.target.value as DiscountType
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value={DiscountType.PERCENTAGE}>Diskon %</option>
                <option value={DiscountType.FIXED_AMOUNT}>Diskon Nominal (Rp)</option>
                <option value={DiscountType.CASHBACK}>Cashback</option>
                <option value={DiscountType.BONUS_UNIT}>Bonus Unit</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nilai Diskon
              </label>
              <input
                type="number"
                value={activeScenario.discount.value}
                onChange={(e) => handleDiscountChange(activeScenario.id, {
                  ...activeScenario.discount,
                  value: Number(e.target.value)
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Estimasi Penjualan (Unit)
              </label>
              <input
                type="number"
                value={activeScenario.discount.targetSales}
                onChange={(e) => handleDiscountChange(activeScenario.id, {
                  ...activeScenario.discount,
                  targetSales: Number(e.target.value)
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Biaya Promosi (Rp)
              </label>
              <input
                type="number"
                value={activeScenario.discount.marketingCost}
                onChange={(e) => handleDiscountChange(activeScenario.id, {
                  ...activeScenario.discount,
                  marketingCost: Number(e.target.value)
                })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Products Accordion - Active Scenario */}
          <div className="mt-4 border border-gray-200 rounded-md overflow-hidden">
            <div
              className="flex justify-between items-center p-3 bg-gray-50 cursor-pointer"
              onClick={() => toggleProductAccordion(activeScenario.id)}
            >
              <h3 className="text-md font-medium">Produk Disimulasikan ({activeScenario.name})</h3>
              {expandedProducts[activeScenario.id] ? (
                <ChevronUp size={18} className="text-gray-500" />
              ) : (
                <ChevronDown size={18} className="text-gray-500" />
              )}
            </div>

            {expandedProducts[activeScenario.id] && (
              <div className="p-3">
                {activeScenario.items.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            No
                          </th>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Nama Produk
                          </th>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Jumlah Unit
                          </th>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Harga Pokok
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {activeScenario.items.map((item, index) => (
                          <tr key={index}>
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                              {index + 1}
                            </td>
                            <td className="px-3 py-2 text-sm text-gray-900">
                              {item.product.materialDescription}
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                              {item.quantity}
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                              {formatCurrency(item.product.cost)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500 py-2">Belum ada produk disimulasikan untuk skenario ini.</p>
                )}
              </div>
            )}
          </div>

          {/* Products Accordion - Scenario B (always visible) */}
          {activeScenario.id === 'scenario-1' && scenarios.length >= 2 && (
            <div className="mt-4 border border-gray-200 rounded-md overflow-hidden">
              <div
                className="flex justify-between items-center p-3 bg-gray-50 cursor-pointer"
                onClick={() => toggleProductAccordion('scenario-2')}
              >
                <h3 className="text-md font-medium">Produk Disimulasikan (Skenario B)</h3>
                {expandedProducts['scenario-2'] ? (
                  <ChevronUp size={18} className="text-gray-500" />
                ) : (
                  <ChevronDown size={18} className="text-gray-500" />
                )}
              </div>

              {expandedProducts['scenario-2'] && (
                <div className="p-3">
                  {scenarios[1].items.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              No
                            </th>
                            <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Nama Produk
                            </th>
                            <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Jumlah Unit
                            </th>
                            <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Harga Pokok
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {scenarios[1].items.map((item, index) => (
                            <tr key={index}>
                              <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                {index + 1}
                              </td>
                              <td className="px-3 py-2 text-sm text-gray-900">
                                {item.product.materialDescription}
                              </td>
                              <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                {item.quantity}
                              </td>
                              <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                {formatCurrency(item.product.cost)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 py-2">Belum ada produk disimulasikan untuk skenario ini.</p>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Results for active scenario */}
          <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200 relative">
            {isCalculating && (
              <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                  <p className="mt-2 text-sm text-gray-600">Menghitung hasil...</p>
                </div>
              </div>
            )}

            <h3 className="text-md font-medium mb-3">Hasil Simulasi</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">Harga Setelah Promo</h4>
                <p className="text-lg font-semibold text-green-600">{formatCurrency(activeScenario.result.priceAfterPromo)}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Total Diskon</h4>
                <p className="text-lg font-semibold text-red-600">{formatCurrency(activeScenario.result.totalDiscount)}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Margin Setelah Promo</h4>
                <div className="flex items-center">
                  <p className="text-lg font-semibold">{activeScenario.result.marginAfterPromo.toFixed(2)}%</p>
                  {getMarginSafetyBadge(activeScenario.result.marginSafetyStatus)}
                </div>
                {activeScenario.result.marginAfterPromo < 5 && (
                  <p className="text-xs text-red-600 flex items-center mt-1">
                    <AlertTriangle size={12} className="mr-1" />
                    Margin sangat tipis. Risiko profit rendah.
                  </p>
                )}
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Break-Even Point</h4>
                <p className="text-lg font-semibold">
                  {activeScenario.result.breakEvenPoint
                    ? `${activeScenario.result.breakEvenPoint} unit`
                    : 'Tidak dapat dihitung'}
                </p>
                {!activeScenario.result.breakEvenPoint && (
                  <p className="text-xs text-red-600 flex items-center mt-1">
                    <AlertTriangle size={12} className="mr-1" />
                    Margin per unit negatif
                  </p>
                )}
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Estimasi Profit</h4>
                <p className="text-lg font-semibold">{formatCurrency(activeScenario.result.estimatedProfit)}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">Profit per Unit</h4>
                <p className="text-lg font-semibold">
                  {activeScenario.result.profitPerUnit !== undefined
                    ? formatCurrency(activeScenario.result.profitPerUnit)
                    : 'N/A'}
                </p>
              </div>
            </div>

            {/* Additional Analysis */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Analisis Tambahan</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Total Biaya Promo:</p>
                  <p className="text-md font-medium">
                    {formatCurrency(activeScenario.result.totalPromoCost || 0)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Biaya Promo sebagai % dari Revenue:</p>
                  <p className="text-md font-medium">
                    {(activeScenario.result.promoCostPercentage || 0).toFixed(2)}%
                  </p>
                </div>

                {activeScenario.result.marginAfterPromo < 10 && activeScenario.result.targetSalesForMargin && (
                  <div className="col-span-1 md:col-span-2">
                    <p className="text-sm text-gray-500">Target Penjualan untuk Margin 10%:</p>
                    <p className="text-md font-medium">
                      {activeScenario.result.targetSalesForMargin} unit
                      {activeScenario.result.targetSalesForMargin > activeScenario.discount.targetSales && (
                        <span className="ml-2 text-xs text-yellow-600">
                          (Perlu {activeScenario.result.targetSalesForMargin - activeScenario.discount.targetSales} unit lebih banyak dari estimasi saat ini)
                        </span>
                      )}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Insight Box */}
            {activeScenario.id === scenarios[0]?.id && scenarios.length >= 2 && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">{generateInsights()}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Comparison Table */}
      {showComparison && (
        <div className="mt-8 p-4 bg-white rounded-lg border border-gray-200">
          <h3 className="text-md font-medium mb-4">Perbandingan Skenario</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Metrik
                  </th>
                  {scenarios.map(scenario => (
                    <th key={scenario.id} scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {scenario.name}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Bentuk Promo
                  </td>
                  {scenarios.map(scenario => (
                    <td key={scenario.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {getPromoTypeLabel(scenario.discount.type)}
                    </td>
                  ))}
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Nilai Diskon
                  </td>
                  {scenarios.map(scenario => (
                    <td key={scenario.id} className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDiscountValue(scenario.discount.type, scenario.discount.value)}
                    </td>
                  ))}
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Harga Setelah Promo
                  </td>
                  {scenarios.map(scenario => {
                    const isBest = getBestScenarioForMetric('priceAfterPromo') === scenario.id;
                    return (
                      <td
                        key={scenario.id}
                        className={`px-6 py-4 whitespace-nowrap text-sm ${isBest ? 'bg-green-50 font-medium' : 'text-gray-500'}`}
                      >
                        {formatCurrency(scenario.result.priceAfterPromo)}
                        {isBest && <CheckCircle size={14} className="inline ml-1 text-green-500" />}
                      </td>
                    );
                  })}
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Total Diskon
                  </td>
                  {scenarios.map(scenario => {
                    const isBest = getBestScenarioForMetric('totalDiscount') === scenario.id;
                    return (
                      <td
                        key={scenario.id}
                        className={`px-6 py-4 whitespace-nowrap text-sm ${isBest ? 'bg-green-50 font-medium' : 'text-gray-500'}`}
                      >
                        {formatCurrency(scenario.result.totalDiscount)}
                        {isBest && <CheckCircle size={14} className="inline ml-1 text-green-500" />}
                      </td>
                    );
                  })}
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Margin Setelah Promo
                  </td>
                  {scenarios.map(scenario => {
                    const isBest = getBestScenarioForMetric('marginAfterPromo') === scenario.id;
                    const marginStatus = scenario.result.marginSafetyStatus;

                    let statusBadge;
                    if (marginStatus === 'safe') {
                      statusBadge = <span className="ml-1 text-xs bg-green-100 text-green-800 px-1 rounded">✅ Aman</span>;
                    } else if (marginStatus === 'thin') {
                      statusBadge = <span className="ml-1 text-xs bg-yellow-100 text-yellow-800 px-1 rounded">⚠️ Tipis</span>;
                    } else if (marginStatus === 'unsafe') {
                      statusBadge = <span className="ml-1 text-xs bg-red-100 text-red-800 px-1 rounded">❌ Tidak Aman</span>;
                    }

                    return (
                      <td
                        key={scenario.id}
                        className={`px-6 py-4 whitespace-nowrap text-sm ${
                          isBest ? 'bg-green-50 font-medium' :
                          marginStatus === 'unsafe' ? 'bg-red-50' : 'text-gray-500'
                        }`}
                      >
                        <div className="flex items-center flex-wrap">
                          <span>{scenario.result.marginAfterPromo.toFixed(2)}%</span>
                          {isBest && <CheckCircle size={14} className="inline ml-1 text-green-500" />}
                          {statusBadge}
                        </div>
                      </td>
                    );
                  })}
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Break-Even Point
                  </td>
                  {scenarios.map(scenario => {
                    const isBest = scenario.result.breakEvenPoint &&
                                  getBestScenarioForMetric('breakEvenPoint') === scenario.id;
                    return (
                      <td
                        key={scenario.id}
                        className={`px-6 py-4 whitespace-nowrap text-sm ${
                          isBest ? 'bg-green-50 font-medium' :
                          !scenario.result.breakEvenPoint ? 'bg-red-50' : 'text-gray-500'
                        }`}
                      >
                        {scenario.result.breakEvenPoint
                          ? `${scenario.result.breakEvenPoint} unit`
                          : 'Tidak dapat dihitung'}
                        {isBest && <CheckCircle size={14} className="inline ml-1 text-green-500" />}
                      </td>
                    );
                  })}
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Estimasi Profit
                  </td>
                  {scenarios.map(scenario => {
                    const isBest = getBestScenarioForMetric('estimatedProfit') === scenario.id;
                    return (
                      <td
                        key={scenario.id}
                        className={`px-6 py-4 whitespace-nowrap text-sm ${isBest ? 'bg-green-50 font-medium' : 'text-gray-500'}`}
                      >
                        {formatCurrency(scenario.result.estimatedProfit)}
                        {isBest && <CheckCircle size={14} className="inline ml-1 text-green-500" />}
                      </td>
                    );
                  })}
                </tr>

                {/* Promotion Cost Efficiency */}
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Total Biaya Promo
                  </td>
                  {scenarios.map(scenario => {
                    const isBest = getBestScenarioForMetric('totalPromoCost') === scenario.id;
                    return (
                      <td
                        key={scenario.id}
                        className={`px-6 py-4 whitespace-nowrap text-sm ${isBest ? 'bg-green-50 font-medium' : 'text-gray-500'}`}
                      >
                        {formatCurrency(scenario.result.totalPromoCost || 0)}
                        {isBest && <CheckCircle size={14} className="inline ml-1 text-green-500" />}
                      </td>
                    );
                  })}
                </tr>
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Biaya Promo sebagai % dari Revenue
                  </td>
                  {scenarios.map(scenario => {
                    const isBest = getBestScenarioForMetric('promoCostPercentage') === scenario.id;
                    return (
                      <td
                        key={scenario.id}
                        className={`px-6 py-4 whitespace-nowrap text-sm ${isBest ? 'bg-green-50 font-medium' : 'text-gray-500'}`}
                      >
                        {(scenario.result.promoCostPercentage || 0).toFixed(2)}%
                        {isBest && <CheckCircle size={14} className="inline ml-1 text-green-500" />}
                      </td>
                    );
                  })}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default PromoABSimulation;
