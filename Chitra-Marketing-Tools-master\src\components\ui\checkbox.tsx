import React, { useState } from "react";

interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
}

const Checkbox: React.FC<CheckboxProps> = ({
  className = "",
  checked,
  onCheckedChange,
  ...props
}) => {
  const [isChecked, setIsChecked] = useState(checked || false);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newChecked = event.target.checked;
    if (checked === undefined) {
      setIsChecked(newChecked);
    }
    onCheckedChange?.(newChecked);
  };

  return (
    <div className="relative flex items-center">
      <input
        type="checkbox"
        className="sr-only"
        checked={checked !== undefined ? checked : isChecked}
        onChange={handleChange}
        {...props}
      />
      <div
        className={`h-4 w-4 rounded border border-gray-300 flex items-center justify-center ${
          (checked !== undefined ? checked : isChecked)
            ? "bg-blue-600 border-blue-600"
            : "bg-white"
        } ${className}`}
      >
        {(checked !== undefined ? checked : isChecked) && (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="h-3 w-3 text-white"
          >
            <path d="M20 6 9 17l-5-5" />
          </svg>
        )}
      </div>
    </div>
  );
};

export { Checkbox };
