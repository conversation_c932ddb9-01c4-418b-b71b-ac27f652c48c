/**
 * Runware.ai Image Generation Service
 *
 * This service provides functions for generating images using the Runware.ai API.
 * It includes functionality for generating images, saving them to history,
 * and managing the image history.
 */

import {
  ImageGenerationRequest,
  PhotoMakerRequest,
  GeneratedImage,
  SavedImage,
  CustomModel,
  ImageModel,
  TaskType,
  PhotoMakerModel,
  PhotoMakerStyle
} from '../types/imageGenerator';
import { v4 as uuidv4 } from 'uuid';

// Runware API key - hardcoded for reliability
const RUNWARE_API_KEY = 'AYKdFJicrfah3pHCIJFbskrBndRmlhCM';

// Runware API endpoint
const RUNWARE_API_URL = 'https://api.runware.ai/v1';

// Local storage key for image history
const IMAGE_HISTORY_KEY = 'runware_image_history';

// Local storage key for custom models
const CUSTOM_MODELS_KEY = 'runware_custom_models';

// Maximum retries for API calls
const MAX_RETRIES = 2;

// Retry delay in milliseconds
const RETRY_DELAY = 1000;

/**
 * Sleep function for retry delay
 */
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Save generated images to local storage history
 */
export const saveImagesToHistory = (images: GeneratedImage[]): void => {
  try {
    // Get existing history from local storage
    const existingHistoryJson = localStorage.getItem(IMAGE_HISTORY_KEY);
    let existingHistory;

    try {
      existingHistory = existingHistoryJson ? JSON.parse(existingHistoryJson) : { images: [] };
    } catch (parseError) {
      console.error('Error parsing history JSON, resetting history:', parseError);
      existingHistory = { images: [] };
    }

    // Add new images to history with unique IDs
    const newImages: SavedImage[] = images.map(image => ({
      ...image,
      id: uuidv4(),
      tags: []
    }));

    // Update history with new images (add to beginning)
    const updatedHistory = {
      images: [...newImages, ...(existingHistory.images || [])]
    };

    // Save updated history to local storage
    localStorage.setItem(IMAGE_HISTORY_KEY, JSON.stringify(updatedHistory));
    console.log('Saved images to history, total images:', updatedHistory.images.length);
  } catch (error) {
    console.error('Error saving images to history:', error);
  }
};

/**
 * Get image generation history from local storage
 */
export const getImageHistory = (): SavedImage[] => {
  try {
    const historyJson = localStorage.getItem(IMAGE_HISTORY_KEY);
    if (!historyJson) {
      return [];
    }

    try {
      const history = JSON.parse(historyJson);
      return history.images || [];
    } catch (parseError) {
      console.error('Error parsing history JSON:', parseError);
      return [];
    }
  } catch (error) {
    console.error('Error getting image history:', error);
    return [];
  }
};

/**
 * Clear image generation history
 */
export const clearImageHistory = (): void => {
  try {
    localStorage.removeItem(IMAGE_HISTORY_KEY);
    console.log('Image history cleared');
  } catch (error) {
    console.error('Error clearing image history:', error);
  }
};

/**
 * Delete a specific image from history
 */
export const deleteImageFromHistory = (imageId: string): void => {
  try {
    const history = getImageHistory();
    const updatedHistory = history.filter(image => image.id !== imageId);

    localStorage.setItem(IMAGE_HISTORY_KEY, JSON.stringify({ images: updatedHistory }));
    console.log('Deleted image from history, remaining images:', updatedHistory.length);
  } catch (error) {
    console.error('Error deleting image from history:', error);
  }
};

/**
 * Get all custom models from local storage
 */
export const getCustomModels = (): CustomModel[] => {
  try {
    const modelsJson = localStorage.getItem(CUSTOM_MODELS_KEY);
    if (!modelsJson) {
      return [];
    }

    try {
      const models = JSON.parse(modelsJson);
      return Array.isArray(models) ? models : [];
    } catch (parseError) {
      console.error('Error parsing custom models JSON:', parseError);
      return [];
    }
  } catch (error) {
    console.error('Error getting custom models:', error);
    return [];
  }
};

/**
 * Save a new custom model to local storage
 */
export const saveCustomModel = (model: Omit<CustomModel, 'id'>): CustomModel => {
  try {
    const models = getCustomModels();

    // Check if a model with the same name already exists
    const existingModelIndex = models.findIndex(m => m.name.toLowerCase() === model.name.toLowerCase());

    const newModel: CustomModel = {
      ...model,
      id: existingModelIndex >= 0 ? models[existingModelIndex].id : uuidv4()
    };

    if (existingModelIndex >= 0) {
      // Update existing model
      models[existingModelIndex] = newModel;
    } else {
      // Add new model
      models.push(newModel);
    }

    localStorage.setItem(CUSTOM_MODELS_KEY, JSON.stringify(models));
    console.log('Saved custom model:', newModel);

    return newModel;
  } catch (error) {
    console.error('Error saving custom model:', error);
    throw error;
  }
};

/**
 * Delete a custom model from local storage
 */
export const deleteCustomModel = (modelId: string): void => {
  try {
    const models = getCustomModels();
    const updatedModels = models.filter(model => model.id !== modelId);

    localStorage.setItem(CUSTOM_MODELS_KEY, JSON.stringify(updatedModels));
    console.log('Deleted custom model, remaining models:', updatedModels.length);
  } catch (error) {
    console.error('Error deleting custom model:', error);
  }
};

/**
 * Get all available models (built-in + custom)
 */
export const getAllModels = (): CustomModel[] => {
  // Create models from the built-in enum
  const builtInModels: CustomModel[] = [
    { id: 'builtin-juggernaut', name: 'Juggernaut Pro FLUX', value: ImageModel.JUGGERNAUT_PRO, isDefault: true },
    { id: 'builtin-juggernaut-lightning', name: 'Juggernaut Lightning FLUX', value: ImageModel.JUGGERNAUT_LIGHTNING, isDefault: true },
    { id: 'builtin-juggernaut-xl-v8', name: 'Juggernaut XL v8', value: ImageModel.JUGGERNAUT_XL_V8, isDefault: true },
    { id: 'builtin-airtist-realistic-xl', name: 'Airtist Realistic XL', value: ImageModel.AIRTIST_REALISTIC_XL, isDefault: true },
    { id: 'builtin-anime', name: 'Anime', value: ImageModel.ANIME, isDefault: true },
    { id: 'builtin-2d', name: '2D', value: ImageModel.TWO_D, isDefault: true },
    { id: 'builtin-campaign', name: 'Campaign Poster', value: ImageModel.CAMPAIGN_POSTER, isDefault: true }
  ];

  // Get custom models
  const customModels = getCustomModels();

  // Combine and return all models
  return [...builtInModels, ...customModels];
};

/**
 * Get all available PhotoMaker models
 */
export const getAllPhotoMakerModels = (): CustomModel[] => {
  // Create models from the PhotoMakerModel enum
  const photoMakerModels: CustomModel[] = [
    { id: 'photomaker-realism', name: 'Realism Engine SDXL v3.0', value: PhotoMakerModel.REALISM_ENGINE_SDXL, isDefault: true },
    { id: 'photomaker-realvisxl', name: 'RealVisXL V4.0', value: PhotoMakerModel.JUGGERNAUT_XL_V4, isDefault: true },
    { id: 'photomaker-sdxl', name: 'SDXL v1.0', value: PhotoMakerModel.SDXL, isDefault: true },
    { id: 'photomaker-dreamshaper', name: 'DreamShaper XL alpha2', value: PhotoMakerModel.DREAMSHAPER_XL, isDefault: true },
    { id: 'photomaker-juggernaut-v8', name: 'Juggernaut XL V8', value: PhotoMakerModel.JUGGERNAUT_XL_V8, isDefault: true },
    { id: 'photomaker-juggernaut-xi', name: 'Juggernaut XL XI', value: PhotoMakerModel.JUGGERNAUT_XL_XI, isDefault: true },
    { id: 'photomaker-juggernaut-x', name: 'Juggernaut XL X', value: PhotoMakerModel.JUGGERNAUT_XL_X, isDefault: true },
    { id: 'photomaker-juggernaut-v7', name: 'Juggernaut XL V7', value: PhotoMakerModel.JUGGERNAUT_XL_V7, isDefault: true },
    { id: 'photomaker-juggernaut-v6', name: 'Juggernaut XL V6', value: PhotoMakerModel.JUGGERNAUT_XL_V6, isDefault: true },
    { id: 'photomaker-juggernaut-v9', name: 'Juggernaut XL V9', value: PhotoMakerModel.JUGGERNAUT_XL_V9, isDefault: true },
    { id: 'photomaker-juggernaut-hyper', name: 'Juggernaut XL X Hyper', value: PhotoMakerModel.JUGGERNAUT_XL_X_HYPER, isDefault: true }
  ];

  return photoMakerModels;
};

/**
 * Generate images using Runware.ai API with retry logic
 */
export const generateImages = async (request: ImageGenerationRequest): Promise<GeneratedImage[]> => {
  let retries = 0;

  while (retries <= MAX_RETRIES) {
    try {
      console.log(`Generating images with Runware.ai (attempt ${retries + 1}/${MAX_RETRIES + 1}):`, request);

      const { positivePrompt, negativePrompt, width, height, model, numberResults } = request;

      // Generate a unique task UUID
      const taskUUID = uuidv4();

      // Prepare the API request body
      const requestBody = [
        {
          taskType: 'imageInference',
          taskUUID,
          positivePrompt,
          width,
          height,
          model,
          numberResults
        }
      ];

      // Add negative prompt only if it's provided
      if (negativePrompt && negativePrompt.trim() !== '') {
        requestBody[0].negativePrompt = negativePrompt;
      }

      console.log('Request body:', JSON.stringify(requestBody, null, 2));

      // Call the Runware API
      const response = await fetch(RUNWARE_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${RUNWARE_API_KEY}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', errorText);

        // If we've reached max retries, throw the error
        if (retries === MAX_RETRIES) {
          throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        // Otherwise, increment retries and try again after delay
        retries++;
        await sleep(RETRY_DELAY);
        continue;
      }

      // Parse the response
      let responseData;
      try {
        responseData = await response.json();
        console.log('Runware API response:', JSON.stringify(responseData, null, 2));
      } catch (parseError) {
        console.error('Error parsing API response:', parseError);

        if (retries === MAX_RETRIES) {
          throw new Error('Failed to parse API response');
        }

        retries++;
        await sleep(RETRY_DELAY);
        continue;
      }

      // Extract the generated images from the response
      const images: GeneratedImage[] = [];

      // Check if the response has the expected format with a data array
      if (responseData && responseData.data && Array.isArray(responseData.data)) {
        // Process each item in the data array
        for (const item of responseData.data) {
          // Check if this is an imageInference response with the required fields
          if (
            item.taskType === 'imageInference' &&
            item.imageURL
          ) {
            images.push({
              imageUUID: item.imageUUID || uuidv4(), // Use provided UUID or generate one
              imageURL: item.imageURL,
              prompt: positivePrompt,
              model,
              width,
              height,
              generatedAt: new Date()
            });
          }
        }
      }

      if (images.length === 0) {
        console.warn('No images found in the response:', responseData);

        if (retries === MAX_RETRIES) {
          throw new Error('No images were generated. Please try again with a different prompt or model.');
        }

        retries++;
        await sleep(RETRY_DELAY);
        continue;
      }

      // Save the generated images to history
      saveImagesToHistory(images);

      return images;
    } catch (error) {
      console.error(`Error generating images (attempt ${retries + 1}/${MAX_RETRIES + 1}):`, error);

      if (retries === MAX_RETRIES) {
        throw error;
      }

      retries++;
      await sleep(RETRY_DELAY);
    }
  }

  // This should never be reached due to the throw in the last retry,
  // but TypeScript requires a return value
  throw new Error('Failed to generate images after multiple attempts');
};

/**
 * Generate images using Runware.ai PhotoMaker API with retry logic
 * @param request PhotoMaker request parameters
 * @returns Promise with array of generated images
 */
export const generatePhotoMakerImages = async (request: PhotoMakerRequest): Promise<GeneratedImage[]> => {
  let retries = 0;

  while (retries <= MAX_RETRIES) {
    try {
      console.log(`Generating PhotoMaker images with Runware.ai (attempt ${retries + 1}/${MAX_RETRIES + 1}):`, request);

      const {
        inputImages,
        style,
        strength,
        positivePrompt,
        negativePrompt,
        width,
        height,
        model,
        scheduler,
        steps,
        CFGScale,
        outputFormat,
        includeCost,
        numberResults
      } = request;

      // Generate a unique task UUID
      const taskUUID = uuidv4();

      // Prepare the API request body
      const requestBody = [
        {
          taskType: TaskType.PHOTO_MAKER,
          taskUUID,
          inputImages,
          positivePrompt,
          width,
          height,
          model,
          numberResults
        }
      ];

      // Add optional parameters if provided
      if (style) {
        requestBody[0].style = style;
      }

      if (strength !== undefined) {
        requestBody[0].strength = strength;
      }

      if (negativePrompt && negativePrompt.trim() !== '') {
        requestBody[0].negativePrompt = negativePrompt;
      }

      if (scheduler) {
        requestBody[0].scheduler = scheduler;
      }

      if (steps !== undefined) {
        requestBody[0].steps = steps;
      }

      if (CFGScale !== undefined) {
        requestBody[0].CFGScale = CFGScale;
      }

      if (outputFormat) {
        requestBody[0].outputFormat = outputFormat;
      }

      if (includeCost !== undefined) {
        requestBody[0].includeCost = includeCost;
      }

      console.log('PhotoMaker request body:', JSON.stringify(requestBody, null, 2));

      // Call the Runware API
      const response = await fetch(RUNWARE_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${RUNWARE_API_KEY}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', errorText);

        // If we've reached max retries, throw the error
        if (retries === MAX_RETRIES) {
          throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
        }

        // Otherwise, increment retries and try again after delay
        retries++;
        await sleep(RETRY_DELAY);
        continue;
      }

      // Parse the response
      let responseData;
      try {
        responseData = await response.json();
        console.log('Runware PhotoMaker API response:', JSON.stringify(responseData, null, 2));
      } catch (parseError) {
        console.error('Error parsing API response:', parseError);

        if (retries === MAX_RETRIES) {
          throw new Error('Failed to parse API response');
        }

        retries++;
        await sleep(RETRY_DELAY);
        continue;
      }

      // Extract the generated images from the response
      const images: GeneratedImage[] = [];

      // Check if the response has the expected format with a data array
      if (responseData && responseData.data && Array.isArray(responseData.data)) {
        // Process each item in the data array
        for (const item of responseData.data) {
          // Check if this is a photoMaker response with the required fields
          if (
            item.taskType === TaskType.PHOTO_MAKER &&
            item.imageURL
          ) {
            images.push({
              imageUUID: item.imageUUID || uuidv4(), // Use provided UUID or generate one
              imageURL: item.imageURL,
              prompt: positivePrompt,
              model,
              width,
              height,
              generatedAt: new Date()
            });
          }
        }
      }

      if (images.length === 0) {
        console.warn('No images found in the response:', responseData);

        if (retries === MAX_RETRIES) {
          throw new Error('No images were generated. Please try again with different parameters.');
        }

        retries++;
        await sleep(RETRY_DELAY);
        continue;
      }

      // Save the generated images to history
      saveImagesToHistory(images);

      return images;
    } catch (error) {
      console.error(`Error generating PhotoMaker images (attempt ${retries + 1}/${MAX_RETRIES + 1}):`, error);

      if (retries === MAX_RETRIES) {
        throw error;
      }

      retries++;
      await sleep(RETRY_DELAY);
    }
  }

  // This should never be reached due to the throw in the last retry,
  // but TypeScript requires a return value
  throw new Error('Failed to generate PhotoMaker images after multiple attempts');
};
