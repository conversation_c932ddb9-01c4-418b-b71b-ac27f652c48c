<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Template UUID</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .container {
            margin-top: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .uuid-input {
            padding: 10px;
            width: 100%;
            margin-bottom: 10px;
            font-family: monospace;
        }
        .actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Template UUID Checker</h1>
    
    <div class="container">
        <p>This tool checks if a template with the specified UUID exists in localStorage.</p>
        
        <label for="uuid">UUID to check:</label>
        <input type="text" id="uuid" class="uuid-input" value="2a08939d-dfb6-4b63-b0da-4ea510947b7b">
        
        <div class="actions">
            <button onclick="checkTemplate()">Check Template</button>
            <button onclick="listAllTemplates()">List All Templates</button>
            <button onclick="deleteTemplate()">Delete Template</button>
        </div>
        
        <h2>Results:</h2>
        <pre id="results">Click a button to perform an action.</pre>
    </div>

    <script>
        const TEMPLATES_STORAGE_KEY = 'chitra_templates';

        // Get templates from localStorage
        function getAllTemplates() {
            const templatesJson = localStorage.getItem(TEMPLATES_STORAGE_KEY);
            if (!templatesJson) {
                return [];
            }

            try {
                return JSON.parse(templatesJson);
            } catch (error) {
                console.error('Error parsing templates from localStorage:', error);
                return [];
            }
        }

        // Find template by ID
        function getTemplateById(id) {
            const templates = getAllTemplates();
            const template = templates.find(t => t.id === id);
            return template || null;
        }

        // Check for a specific template
        function checkTemplate() {
            const targetUuid = document.getElementById('uuid').value.trim();
            const resultsElement = document.getElementById('results');
            
            let output = `Checking for template with UUID: ${targetUuid}\n\n`;
            
            const template = getTemplateById(targetUuid);
            
            if (template) {
                output += "Template found!\n";
                output += "Template details:\n";
                output += `Name: ${template.name}\n`;
                output += `Type: ${template.type}\n`;
                output += `Created: ${new Date(template.createdAt).toLocaleString()}\n`;
                output += `Updated: ${new Date(template.updatedAt).toLocaleString()}\n`;
                output += `Variables: ${JSON.stringify(template.detectedVariables, null, 2)}\n`;
                output += `File URL: ${template.fileUrl || 'None'}\n`;
            } else {
                output += "No template found with this UUID\n";
            }
            
            resultsElement.textContent = output;
        }

        // List all templates
        function listAllTemplates() {
            const resultsElement = document.getElementById('results');
            const allTemplates = getAllTemplates();
            
            let output = `Found ${allTemplates.length} templates in total:\n\n`;
            
            if (allTemplates.length === 0) {
                output += "No templates found in localStorage.";
            } else {
                allTemplates.forEach((t, index) => {
                    output += `${index + 1}. ${t.name} (ID: ${t.id})\n`;
                    output += `   Type: ${t.type}\n`;
                    output += `   Created: ${new Date(t.createdAt).toLocaleString()}\n`;
                    output += `   Variables: ${t.detectedVariables.length}\n\n`;
                });
            }
            
            resultsElement.textContent = output;
        }

        // Delete a template
        function deleteTemplate() {
            const targetUuid = document.getElementById('uuid').value.trim();
            const resultsElement = document.getElementById('results');
            
            const templates = getAllTemplates();
            const filteredTemplates = templates.filter(t => t.id !== targetUuid);
            
            if (filteredTemplates.length === templates.length) {
                resultsElement.textContent = `No template with UUID ${targetUuid} found to delete.`;
                return;
            }
            
            localStorage.setItem(TEMPLATES_STORAGE_KEY, JSON.stringify(filteredTemplates));
            resultsElement.textContent = `Template with UUID ${targetUuid} has been deleted.`;
        }
    </script>
</body>
</html>
