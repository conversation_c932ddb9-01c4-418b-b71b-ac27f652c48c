import { jsPDF } from 'jspdf';
import { SimulationScenario } from '../types/abSimulation';
import { DiscountType } from '../types/promotion';
import 'jspdf-autotable';

/**
 * Generate a PDF for A/B simulation comparison
 */
export const generateABSimulationPDF = (
  scenarios: SimulationScenario[],
  title: string = 'Perbandingan Skema Promo'
): void => {
  // Create a new PDF document
  const doc = new jsPDF();
  
  // Add title
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text(title, 105, 20, { align: 'center' });
  
  // Add date
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text(`Tanggal: ${new Date().toLocaleDateString('id-ID')}`, 105, 30, { align: 'center' });
  
  // Add comparison table
  addComparisonTable(doc, scenarios);
  
  // Add product tables for each scenario
  let yPosition = (doc as any).lastAutoTable.finalY + 20;
  
  // Check if we need a new page
  if (yPosition > 240) {
    doc.addPage();
    yPosition = 20;
  }
  
  // Add product tables
  for (let i = 0; i < scenarios.length; i++) {
    const scenario = scenarios[i];
    
    // Add scenario title
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text(`Produk Disimulasikan (${scenario.name})`, 14, yPosition);
    
    // Add product table
    addProductTable(doc, scenario, yPosition + 10);
    
    // Update y position for next scenario
    yPosition = (doc as any).lastAutoTable.finalY + 20;
    
    // Check if we need a new page for the next scenario
    if (i < scenarios.length - 1 && yPosition > 240) {
      doc.addPage();
      yPosition = 20;
    }
  }
  
  // Add promotion cost efficiency
  if (yPosition > 200) {
    doc.addPage();
    yPosition = 20;
  }
  
  addPromoCostEfficiencyTable(doc, scenarios, yPosition);
  
  // Save the PDF
  doc.save(`${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`);
};

/**
 * Add comparison table to the PDF
 */
const addComparisonTable = (doc: jsPDF, scenarios: SimulationScenario[]): void => {
  // Find best values for highlighting
  const bestValues = {
    priceAfterPromo: Math.min(...scenarios.map(s => s.result.priceAfterPromo)),
    totalDiscount: Math.max(...scenarios.map(s => s.result.totalDiscount)),
    marginAfterPromo: Math.max(...scenarios.map(s => s.result.marginAfterPromo)),
    breakEvenPoint: Math.min(...scenarios.filter(s => s.result.breakEvenPoint !== undefined).map(s => s.result.breakEvenPoint || Infinity)),
    estimatedProfit: Math.max(...scenarios.map(s => s.result.estimatedProfit)),
    totalPromoCost: Math.min(...scenarios.map(s => s.result.totalPromoCost || 0)),
    promoCostPercentage: Math.min(...scenarios.map(s => s.result.promoCostPercentage || 0))
  };
  
  // Prepare table headers
  const headers = [
    [
      { content: 'Metrik', styles: { fontStyle: 'bold', halign: 'left' } },
      ...scenarios.map(scenario => ({ 
        content: scenario.name, 
        styles: { fontStyle: 'bold', halign: 'center' } 
      }))
    ]
  ];
  
  // Prepare table body
  const body = [
    // Bentuk Promo
    [
      { content: 'Bentuk Promo', styles: { fontStyle: 'bold' } },
      ...scenarios.map(scenario => ({
        content: getPromoTypeLabel(scenario.discount.type),
        styles: {}
      }))
    ],
    // Nilai Diskon
    [
      { content: 'Nilai Diskon', styles: { fontStyle: 'bold' } },
      ...scenarios.map(scenario => ({
        content: formatDiscountValue(scenario.discount.type, scenario.discount.value),
        styles: {}
      }))
    ],
    // Harga Setelah Promo
    [
      { content: 'Harga Setelah Promo', styles: { fontStyle: 'bold' } },
      ...scenarios.map(scenario => ({
        content: formatCurrency(scenario.result.priceAfterPromo),
        styles: scenario.result.priceAfterPromo === bestValues.priceAfterPromo ? 
          { fillColor: [240, 255, 240] } : {}
      }))
    ],
    // Total Diskon
    [
      { content: 'Total Diskon', styles: { fontStyle: 'bold' } },
      ...scenarios.map(scenario => ({
        content: formatCurrency(scenario.result.totalDiscount),
        styles: scenario.result.totalDiscount === bestValues.totalDiscount ? 
          { fillColor: [240, 255, 240] } : {}
      }))
    ],
    // Margin Setelah Promo
    [
      { content: 'Margin Setelah Promo', styles: { fontStyle: 'bold' } },
      ...scenarios.map(scenario => {
        const content = `${scenario.result.marginAfterPromo.toFixed(2)}%`;
        const isLowMargin = scenario.result.marginAfterPromo < 5;
        
        return {
          content: isLowMargin ? `${content} ⚠️` : content,
          styles: scenario.result.marginAfterPromo === bestValues.marginAfterPromo ? 
            { fillColor: [240, 255, 240] } : 
            isLowMargin ? { fillColor: [255, 240, 240] } : {}
        };
      })
    ],
    // Break-Even Point
    [
      { content: 'Break-Even Point', styles: { fontStyle: 'bold' } },
      ...scenarios.map(scenario => {
        const bep = scenario.result.breakEvenPoint;
        const content = bep ? `${bep} unit` : 'Tidak dapat dihitung';
        
        return {
          content,
          styles: bep && bep === bestValues.breakEvenPoint ? 
            { fillColor: [240, 255, 240] } : 
            !bep ? { fillColor: [255, 240, 240] } : {}
        };
      })
    ],
    // Estimasi Profit
    [
      { content: 'Estimasi Profit', styles: { fontStyle: 'bold' } },
      ...scenarios.map(scenario => ({
        content: formatCurrency(scenario.result.estimatedProfit),
        styles: scenario.result.estimatedProfit === bestValues.estimatedProfit ? 
          { fillColor: [240, 255, 240] } : {}
      }))
    ]
  ];
  
  // Add the table to the PDF
  (doc as any).autoTable({
    head: headers,
    body: body,
    startY: 40,
    theme: 'grid',
    styles: {
      fontSize: 10,
      cellPadding: 5
    },
    columnStyles: {
      0: { cellWidth: 50 }
    },
    headStyles: {
      fillColor: [220, 230, 240],
      textColor: [0, 0, 0],
      fontStyle: 'bold'
    }
  });
};

/**
 * Add product table for a scenario
 */
const addProductTable = (doc: jsPDF, scenario: SimulationScenario, startY: number): void => {
  // Prepare table headers
  const headers = [
    [
      { content: 'No', styles: { fontStyle: 'bold', halign: 'center' } },
      { content: 'Nama Produk', styles: { fontStyle: 'bold', halign: 'left' } },
      { content: 'Jumlah Unit', styles: { fontStyle: 'bold', halign: 'center' } },
      { content: 'Harga Pokok', styles: { fontStyle: 'bold', halign: 'right' } }
    ]
  ];
  
  // Prepare table body
  const body = scenario.items.map((item, index) => [
    { content: (index + 1).toString(), styles: { halign: 'center' } },
    { content: item.product.materialDescription, styles: { halign: 'left' } },
    { content: item.quantity.toString(), styles: { halign: 'center' } },
    { content: formatCurrency(item.product.cost), styles: { halign: 'right' } }
  ]);
  
  // Add the table to the PDF
  (doc as any).autoTable({
    head: headers,
    body: body,
    startY: startY,
    theme: 'grid',
    styles: {
      fontSize: 9,
      cellPadding: 4
    },
    columnStyles: {
      0: { cellWidth: 15 },
      1: { cellWidth: 'auto' },
      2: { cellWidth: 30 },
      3: { cellWidth: 40 }
    },
    headStyles: {
      fillColor: [240, 240, 240],
      textColor: [0, 0, 0],
      fontStyle: 'bold'
    }
  });
};

/**
 * Add promotion cost efficiency table
 */
const addPromoCostEfficiencyTable = (doc: jsPDF, scenarios: SimulationScenario[], startY: number): void => {
  // Find best values for highlighting
  const bestValues = {
    totalPromoCost: Math.min(...scenarios.map(s => s.result.totalPromoCost || 0)),
    promoCostPercentage: Math.min(...scenarios.map(s => s.result.promoCostPercentage || 0))
  };
  
  // Add section title
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('Efisiensi Biaya Promosi', 14, startY);
  
  // Prepare table headers
  const headers = [
    [
      { content: 'Metrik', styles: { fontStyle: 'bold', halign: 'left' } },
      ...scenarios.map(scenario => ({ 
        content: scenario.name, 
        styles: { fontStyle: 'bold', halign: 'center' } 
      }))
    ]
  ];
  
  // Prepare table body
  const body = [
    // Total Biaya Promosi
    [
      { content: 'Total Biaya Promosi', styles: { fontStyle: 'bold' } },
      ...scenarios.map(scenario => ({
        content: formatCurrency(scenario.result.totalPromoCost || 0),
        styles: (scenario.result.totalPromoCost || 0) === bestValues.totalPromoCost ? 
          { fillColor: [240, 255, 240] } : {}
      }))
    ],
    // Biaya Promosi sebagai % dari Revenue
    [
      { content: 'Biaya Promosi sebagai % dari Revenue', styles: { fontStyle: 'bold' } },
      ...scenarios.map(scenario => ({
        content: `${(scenario.result.promoCostPercentage || 0).toFixed(2)}%`,
        styles: (scenario.result.promoCostPercentage || 0) === bestValues.promoCostPercentage ? 
          { fillColor: [240, 255, 240] } : {}
      }))
    ]
  ];
  
  // Add the table to the PDF
  (doc as any).autoTable({
    head: headers,
    body: body,
    startY: startY + 10,
    theme: 'grid',
    styles: {
      fontSize: 10,
      cellPadding: 5
    },
    columnStyles: {
      0: { cellWidth: 80 }
    },
    headStyles: {
      fillColor: [220, 230, 240],
      textColor: [0, 0, 0],
      fontStyle: 'bold'
    }
  });
};

/**
 * Format currency
 */
const formatCurrency = (value: number): string => {
  return `Rp ${value.toLocaleString('id-ID')}`;
};

/**
 * Format discount value
 */
const formatDiscountValue = (type: DiscountType, value: number): string => {
  if (type === DiscountType.PERCENTAGE) {
    return `${value}%`;
  } else if (type === DiscountType.BONUS_UNIT) {
    return `${value} unit`;
  } else {
    return formatCurrency(value);
  }
};

/**
 * Get promo type label
 */
const getPromoTypeLabel = (type: DiscountType): string => {
  switch (type) {
    case DiscountType.PERCENTAGE:
      return 'Diskon %';
    case DiscountType.FIXED_AMOUNT:
      return 'Diskon Nominal';
    case DiscountType.CASHBACK:
      return 'Cashback';
    case DiscountType.BONUS_UNIT:
      return 'Bonus Unit';
    default:
      return 'Diskon';
  }
};
