import React, { useState, useEffect } from 'react';
import { KnowledgeEntry, KnowledgeCategory } from '../types/knowledgeBase';
import { createKnowledgeEntry, searchKnowledgeEntries } from '../services/knowledgeBaseService';
import { v4 as uuidv4 } from 'uuid';
import { Save, Plus, Trash2 } from 'lucide-react';

const CompanyDataForm: React.FC = () => {
  const [title, setTitle] = useState<string>('');
  const [content, setContent] = useState<string>('');
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState<string>('');
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);
  const [existingEntries, setExistingEntries] = useState<KnowledgeEntry[]>([]);

  // Load existing company data entries
  useEffect(() => {
    const loadExistingEntries = async () => {
      try {
        const result = await searchKnowledgeEntries({
          category: KnowledgeCategory.COMPANY_GENERAL_DATA
        });

        setExistingEntries(result.entries);
      } catch (error) {
        console.error('Error loading company data entries:', error);
      }
    };

    loadExistingEntries();
  }, []);

  // Add a new tag
  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  // Remove a tag
  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!title.trim() || !content.trim()) {
      setMessage({
        text: 'Judul dan konten harus diisi',
        type: 'error'
      });
      return;
    }

    setIsSaving(true);
    setMessage(null);

    try {
      const newEntry: KnowledgeEntry = {
        id: uuidv4(),
        title,
        content,
        category: KnowledgeCategory.COMPANY_GENERAL_DATA,
        tags,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true
      };

      await createKnowledgeEntry(newEntry);

      // Reset form
      setTitle('');
      setContent('');
      setTags([]);

      // Show success message
      setMessage({
        text: 'Data perusahaan berhasil disimpan',
        type: 'success'
      });

      // Refresh existing entries
      const result = await searchKnowledgeEntries({
        category: KnowledgeCategory.COMPANY_GENERAL_DATA
      });

      setExistingEntries(result.entries);
    } catch (error) {
      console.error('Error saving company data:', error);

      setMessage({
        text: 'Gagal menyimpan data perusahaan',
        type: 'error'
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Tambah Data Perusahaan Umum</h2>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Judul
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full p-2 border rounded-md"
              placeholder="Contoh: Profil Perusahaan, Visi Misi, Struktur Organisasi, dll."
            />
          </div>

          {/* Content */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Konten
            </label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="w-full p-2 border rounded-md"
              rows={6}
              placeholder="Masukkan informasi detail tentang perusahaan..."
            />
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tags
            </label>

            <div className="flex space-x-2 mb-2">
              <input
                type="text"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                className="flex-1 p-2 border rounded-md"
                placeholder="Tambahkan tag..."
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddTag();
                  }
                }}
              />
              <button
                type="button"
                onClick={handleAddTag}
                className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Plus size={16} />
              </button>
            </div>

            {/* Tags List */}
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-2">
                {tags.map(tag => (
                  <div key={tag} className="flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-md">
                    <span>{tag}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Message */}
          {message && (
            <div className={`p-3 rounded ${
              message.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {message.text}
            </div>
          )}

          {/* Submit Button */}
          <div>
            <button
              type="submit"
              className="flex items-center justify-center w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Menyimpan...
                </>
              ) : (
                <>
                  <Save size={16} className="mr-2" />
                  Simpan Data Perusahaan
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Existing Entries */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Data Perusahaan yang Tersimpan</h2>

        {existingEntries.length === 0 ? (
          <p className="text-gray-600">Belum ada data perusahaan yang tersimpan.</p>
        ) : (
          <div className="space-y-4">
            {existingEntries.map(entry => (
              <div key={entry.id} className="border p-4 rounded-lg">
                <h3 className="text-lg font-medium text-gray-800">{entry.title}</h3>
                <p className="text-gray-600 mt-1 whitespace-pre-line">{entry.content}</p>

                {entry.tags && entry.tags.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {entry.tags.map(tag => (
                      <span key={tag} className="bg-gray-100 text-gray-800 px-2 py-0.5 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                )}

                <p className="text-xs text-gray-400 mt-2">
                  Dibuat pada: {entry.createdAt instanceof Date ? entry.createdAt.toLocaleString() : new Date(entry.createdAt).toLocaleString()}
                </p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default CompanyDataForm;
