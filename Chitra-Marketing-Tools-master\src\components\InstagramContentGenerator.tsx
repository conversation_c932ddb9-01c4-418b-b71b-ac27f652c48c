import React, { useState, useEffect } from 'react';
import {
  ContentGenerationRequest,
  ContentGenerationResponse,
  SocialMediaPlatform,
  ContentType,
  HashtagAnalysis,
  InstagramCalendarDay,
  SocialMediaKnowledgeEntry
} from '../types/socialMedia';
import {
  generateInstagramContent,
  analyzeHashtags,
  createSocialMediaPost,
  getAllSocialMediaKnowledge
} from '../services/socialMediaService';
import { convertInstagramCalendarDayToContentRequest } from '../services/calendarContentService';
import {
  Sparkles,
  Copy,
  CheckCircle,
  AlertCircle,
  Instagram,
  Hash,
  Image,
  BarChart,
  Loader2,
  Save,
  Calendar,
  Package,
  Database,
  Search,
  X
} from 'lucide-react';

interface InstagramContentGeneratorProps {
  calendarDay?: InstagramCalendarDay;
  onContentSaved?: () => void;
}

export default function InstagramContentGenerator({
  calendarDay,
  onContentSaved
}: InstagramContentGeneratorProps) {
  // State for form inputs
  const [contentRequest, setContentRequest] = useState<ContentGenerationRequest>({
    platform: SocialMediaPlatform.INSTAGRAM,
    contentType: ContentType.IMAGE,
    productDetails: '',
    targetAudience: '',
    campaignGoals: '',
    tone: 'professional',
    length: 'medium',
    includeHashtags: true,
    includeEmojis: true,
    language: 'id'
  });

  // Initialize form with calendar day data when provided
  React.useEffect(() => {
    if (calendarDay) {
      const initialRequest = convertInstagramCalendarDayToContentRequest(calendarDay);
      setContentRequest(prev => ({
        ...prev,
        ...initialRequest
      }));
    }
  }, [calendarDay]);

  // State for generated content
  const [generatedContent, setGeneratedContent] = useState<ContentGenerationResponse | null>(null);

  // State for hashtag analysis
  const [hashtagAnalysis, setHashtagAnalysis] = useState<HashtagAnalysis[]>([]);

  // Loading states
  const [isGenerating, setIsGenerating] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isLoadingKnowledge, setIsLoadingKnowledge] = useState(false);

  // Knowledge base state
  const [knowledgeEntries, setKnowledgeEntries] = useState<SocialMediaKnowledgeEntry[]>([]);
  const [productKnowledge, setProductKnowledge] = useState<SocialMediaKnowledgeEntry[]>([]);
  const [isKnowledgeModalOpen, setIsKnowledgeModalOpen] = useState(false);
  const [knowledgeSearchQuery, setKnowledgeSearchQuery] = useState('');

  // Notification state
  const [notification, setNotification] = useState<{
    message: string;
    type: 'success' | 'error';
    visible: boolean;
  }>({
    message: '',
    type: 'success',
    visible: false
  });

  // Load knowledge entries on component mount
  useEffect(() => {
    loadKnowledgeEntries();
  }, []);

  // Load knowledge entries
  const loadKnowledgeEntries = async () => {
    setIsLoadingKnowledge(true);
    try {
      const entries = await getAllSocialMediaKnowledge();
      setKnowledgeEntries(entries);

      // Filter product knowledge entries
      const productEntries = entries.filter(entry => entry.id.startsWith('product-'));
      setProductKnowledge(productEntries);
    } catch (error) {
      console.error('Error loading knowledge entries:', error);
      showNotification('Gagal memuat data knowledge base', 'error');
    } finally {
      setIsLoadingKnowledge(false);
    }
  };

  // Generate content
  const generateContent = async () => {
    if (!contentRequest.productDetails) {
      showNotification('Detail produk harus diisi', 'error');
      return;
    }

    setIsGenerating(true);
    try {
      const content = await generateInstagramContent(contentRequest);
      setGeneratedContent(content);

      // Automatically analyze hashtags if they exist
      if (content.hashtags && content.hashtags.length > 0) {
        analyzeContentHashtags(content.hashtags);
      }
    } catch (error) {
      console.error('Error generating content:', error);
      showNotification('Gagal menghasilkan konten', 'error');
    } finally {
      setIsGenerating(false);
    }
  };

  // Analyze hashtags
  const analyzeContentHashtags = async (hashtags: string[]) => {
    setIsAnalyzing(true);
    try {
      const analysis = await analyzeHashtags(hashtags);
      setHashtagAnalysis(analysis);
    } catch (error) {
      console.error('Error analyzing hashtags:', error);
      showNotification('Gagal menganalisis hashtag', 'error');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Copy content to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    showNotification('Berhasil disalin ke clipboard', 'success');
  };

  // Save content to calendar
  const saveToCalendar = async () => {
    if (!generatedContent || !calendarDay) {
      showNotification('Tidak ada konten yang dapat disimpan atau hari kalender tidak dipilih', 'error');
      return;
    }

    try {
      // Create a new post
      const post = await createSocialMediaPost({
        platform: SocialMediaPlatform.INSTAGRAM,
        contentType: contentRequest.contentType,
        title: `Konten untuk ${new Date(calendarDay.date).toLocaleDateString('id-ID')}`,
        caption: generatedContent.caption,
        hashtags: generatedContent.hashtags,
        imageDescription: generatedContent.suggestedImageDescription,
        scheduledDate: new Date(calendarDay.date),
        status: 'draft'
      });

      showNotification('Konten berhasil disimpan ke kalender', 'success');

      // Call the callback if provided
      if (onContentSaved) {
        onContentSaved();
      }
    } catch (error) {
      console.error('Error saving content to calendar:', error);
      showNotification('Gagal menyimpan konten ke kalender', 'error');
    }
  };

  // Show notification
  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({
      message,
      type,
      visible: true
    });

    // Hide notification after 3 seconds
    setTimeout(() => {
      setNotification(prev => ({ ...prev, visible: false }));
    }, 3000);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setContentRequest(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setContentRequest(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Get recommendation color
  const getRecommendationColor = (recommendation: 'high' | 'medium' | 'low'): string => {
    switch (recommendation) {
      case 'high':
        return 'text-green-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  // Open knowledge modal
  const openKnowledgeModal = () => {
    setKnowledgeSearchQuery('');
    setIsKnowledgeModalOpen(true);
  };

  // Close knowledge modal
  const closeKnowledgeModal = () => {
    setIsKnowledgeModalOpen(false);
  };

  // Filter product knowledge entries based on search query
  const getFilteredProductKnowledge = (): SocialMediaKnowledgeEntry[] => {
    if (!knowledgeSearchQuery) {
      return productKnowledge;
    }

    const query = knowledgeSearchQuery.toLowerCase();
    return productKnowledge.filter(entry =>
      entry.title.toLowerCase().includes(query) ||
      entry.content.toLowerCase().includes(query) ||
      entry.tags.some(tag => tag.toLowerCase().includes(query))
    );
  };

  // Select product knowledge entry
  const selectProductKnowledge = (entry: SocialMediaKnowledgeEntry) => {
    // Extract product details from the knowledge entry content
    const contentLines = entry.content.split('\n');
    let productDetails = '';

    // Find the product information section
    const infoStartIndex = contentLines.findIndex(line => line.includes('INFORMASI PRODUK:'));
    if (infoStartIndex >= 0) {
      // Extract the product information section
      const infoEndIndex = contentLines.findIndex((line, index) =>
        index > infoStartIndex && line.trim() === '' && contentLines[index + 1]?.includes('IDE KONTEN')
      );

      if (infoEndIndex > infoStartIndex) {
        productDetails = contentLines.slice(infoStartIndex, infoEndIndex).join('\n');
      } else {
        productDetails = entry.title + '\n' + entry.content.substring(0, 200);
      }
    } else {
      productDetails = entry.title + '\n' + entry.content.substring(0, 200);
    }

    // Update the content request with the product details
    setContentRequest(prev => ({
      ...prev,
      productDetails
    }));

    // Close the modal
    closeKnowledgeModal();

    // Show notification
    showNotification('Produk berhasil dipilih', 'success');
  };

  return (
    <div className="space-y-6">
      {/* Notification */}
      {notification.visible && (
        <div className={`p-3 rounded-md ${notification.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'} flex items-center`}>
          {notification.type === 'success' ? (
            <CheckCircle size={16} className="mr-2" />
          ) : (
            <AlertCircle size={16} className="mr-2" />
          )}
          {notification.message}
        </div>
      )}

      {/* Product Knowledge Modal */}
      {isKnowledgeModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-medium">Pilih Produk dari Knowledge Base</h3>
              <button
                onClick={closeKnowledgeModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="relative">
                <input
                  type="text"
                  value={knowledgeSearchQuery}
                  onChange={(e) => setKnowledgeSearchQuery(e.target.value)}
                  className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder="Cari produk..."
                />
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>
            </div>

            <div className="flex-1 overflow-y-auto">
              {isLoadingKnowledge ? (
                <div className="p-8 text-center">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2"></div>
                  <p className="text-gray-500">Memuat data produk...</p>
                </div>
              ) : getFilteredProductKnowledge().length === 0 ? (
                <div className="p-8 text-center">
                  <Package size={32} className="mx-auto text-gray-400 mb-2" />
                  <p className="text-gray-500">
                    {productKnowledge.length === 0
                      ? 'Tidak ada data produk di knowledge base. Silakan import produk terlebih dahulu di menu Knowledge Base.'
                      : 'Tidak ada produk yang sesuai dengan pencarian.'}
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {getFilteredProductKnowledge().map(entry => (
                    <div
                      key={entry.id}
                      className="p-4 hover:bg-gray-50 cursor-pointer"
                      onClick={() => selectProductKnowledge(entry)}
                    >
                      <div className="flex items-start">
                        <div className="flex-shrink-0 pt-1">
                          <Package size={16} className="text-green-500" />
                        </div>
                        <div className="ml-3 flex-1">
                          <h4 className="font-medium">{entry.title}</h4>
                          <div className="text-sm text-gray-600 mt-1 line-clamp-2">
                            {entry.content.substring(0, 150)}...
                          </div>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {entry.tags.slice(0, 5).map(tag => (
                              <span key={tag} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                #{tag}
                              </span>
                            ))}
                            {entry.tags.length > 5 && (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                +{entry.tags.length - 5}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="p-4 border-t border-gray-200 bg-gray-50 flex justify-end">
              <button
                onClick={closeKnowledgeModal}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Batal
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Input Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-4 border-b border-gray-200 flex items-center">
            <Instagram size={20} className="text-pink-500 mr-2" />
            <h3 className="text-lg font-medium">Generator Konten Instagram</h3>
          </div>

          <div className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tipe Konten</label>
              <select
                name="contentType"
                value={contentRequest.contentType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value={ContentType.IMAGE}>Gambar</option>
                <option value={ContentType.VIDEO}>Video</option>
                <option value={ContentType.CAROUSEL}>Carousel</option>
                <option value={ContentType.REEL}>Reel</option>
                <option value={ContentType.STORY}>Story</option>
              </select>
            </div>

            <div>
              <div className="flex justify-between items-center mb-1">
                <label className="block text-sm font-medium text-gray-700">Detail Produk*</label>
                <button
                  type="button"
                  onClick={openKnowledgeModal}
                  className="flex items-center text-xs bg-green-500 text-white px-2 py-1 rounded hover:bg-green-600 transition-colors"
                >
                  <Database size={12} className="mr-1" />
                  Pilih dari Produk
                </button>
              </div>
              <textarea
                name="productDetails"
                value={contentRequest.productDetails}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 min-h-[80px]"
                placeholder="Deskripsi produk, fitur, manfaat, dll."
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Target Audiens</label>
              <input
                type="text"
                name="targetAudience"
                value={contentRequest.targetAudience}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Contoh: Perusahaan tambang, kontraktor, dll."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tujuan Kampanye</label>
              <input
                type="text"
                name="campaignGoals"
                value={contentRequest.campaignGoals}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Contoh: Meningkatkan awareness, promosi produk baru, dll."
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tone</label>
                <select
                  name="tone"
                  value={contentRequest.tone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value="professional">Profesional</option>
                  <option value="casual">Santai</option>
                  <option value="formal">Formal</option>
                  <option value="enthusiastic">Antusias</option>
                  <option value="educational">Edukatif</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Panjang</label>
                <select
                  name="length"
                  value={contentRequest.length}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value="short">Pendek</option>
                  <option value="medium">Sedang</option>
                  <option value="long">Panjang</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Bahasa</label>
                <select
                  name="language"
                  value={contentRequest.language}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  <option value="id">Indonesia</option>
                  <option value="en">Inggris</option>
                </select>
              </div>

              <div className="flex items-end space-x-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="includeHashtags"
                    name="includeHashtags"
                    checked={contentRequest.includeHashtags}
                    onChange={handleCheckboxChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="includeHashtags" className="ml-2 text-sm text-gray-700">
                    Hashtag
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="includeEmojis"
                    name="includeEmojis"
                    checked={contentRequest.includeEmojis}
                    onChange={handleCheckboxChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="includeEmojis" className="ml-2 text-sm text-gray-700">
                    Emoji
                  </label>
                </div>
              </div>
            </div>

            <div className="pt-2">
              <button
                onClick={generateContent}
                disabled={isGenerating}
                className="w-full flex justify-center items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <>
                    <Loader2 size={16} className="mr-2 animate-spin" />
                    Menghasilkan Konten...
                  </>
                ) : (
                  <>
                    <Sparkles size={16} className="mr-2" />
                    Hasilkan Konten Instagram
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Generated Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-4 border-b border-gray-200 flex items-center">
            <Image size={20} className="text-blue-500 mr-2" />
            <h3 className="text-lg font-medium">Hasil Konten</h3>
          </div>

          {generatedContent ? (
            <div className="p-4 space-y-4">
              <div>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-700">Caption</h4>
                  <button
                    onClick={() => copyToClipboard(generatedContent.caption)}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title="Salin caption"
                  >
                    <Copy size={14} />
                  </button>
                </div>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm whitespace-pre-line">
                  {generatedContent.caption}
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-700">Hashtag</h4>
                  <button
                    onClick={() => copyToClipboard(generatedContent.hashtags.map(tag => `#${tag}`).join(' '))}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title="Salin hashtag"
                  >
                    <Copy size={14} />
                  </button>
                </div>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                  <div className="flex flex-wrap gap-1">
                    {generatedContent.hashtags.map((tag, index) => (
                      <span key={index} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {generatedContent.suggestedImageDescription && (
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <h4 className="text-sm font-medium text-gray-700">Deskripsi Gambar yang Disarankan</h4>
                    <button
                      onClick={() => copyToClipboard(generatedContent.suggestedImageDescription || '')}
                      className="text-blue-600 hover:text-blue-800 p-1"
                      title="Salin deskripsi gambar"
                    >
                      <Copy size={14} />
                    </button>
                  </div>
                  <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {generatedContent.suggestedImageDescription}
                  </div>
                </div>
              )}

              {/* Save to Calendar button - only show if calendarDay is provided */}
              {calendarDay && (
                <div className="mt-4">
                  <button
                    onClick={saveToCalendar}
                    className="w-full flex justify-center items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                  >
                    <Save size={16} className="mr-2" />
                    Simpan ke Kalender
                  </button>
                  <div className="mt-2 flex items-center text-xs text-gray-500">
                    <Calendar size={14} className="mr-1" />
                    <span>Akan disimpan untuk tanggal: {new Date(calendarDay.date).toLocaleDateString('id-ID')}</span>
                  </div>
                </div>
              )}

              {/* Hashtag Analysis */}
              {hashtagAnalysis.length > 0 && (
                <div>
                  <div className="flex items-center mb-1">
                    <BarChart size={16} className="text-blue-500 mr-1" />
                    <h4 className="text-sm font-medium text-gray-700">Analisis Hashtag</h4>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 text-sm">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Hashtag
                          </th>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Popularitas
                          </th>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Relevansi
                          </th>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Kompetisi
                          </th>
                          <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Rekomendasi
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {hashtagAnalysis.map((analysis, index) => (
                          <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <div className="flex items-center">
                                <Hash size={14} className="text-blue-500 mr-1" />
                                <span>{analysis.hashtag}</span>
                              </div>
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <div className="w-full bg-gray-200 rounded-full h-2.5">
                                <div
                                  className="bg-blue-600 h-2.5 rounded-full"
                                  style={{ width: `${analysis.popularity}%` }}
                                ></div>
                              </div>
                              <span className="text-xs text-gray-500">{analysis.popularity}%</span>
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <div className="w-full bg-gray-200 rounded-full h-2.5">
                                <div
                                  className="bg-green-600 h-2.5 rounded-full"
                                  style={{ width: `${analysis.relevance}%` }}
                                ></div>
                              </div>
                              <span className="text-xs text-gray-500">{analysis.relevance}%</span>
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <div className="w-full bg-gray-200 rounded-full h-2.5">
                                <div
                                  className="bg-red-600 h-2.5 rounded-full"
                                  style={{ width: `${analysis.competition}%` }}
                                ></div>
                              </div>
                              <span className="text-xs text-gray-500">{analysis.competition}%</span>
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <span className={`font-medium ${getRecommendationColor(analysis.recommendation)}`}>
                                {analysis.recommendation === 'high' ? 'Tinggi' :
                                 analysis.recommendation === 'medium' ? 'Sedang' : 'Rendah'}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="p-8 text-center">
              <Instagram size={48} className="mx-auto text-gray-300 mb-3" />
              <p className="text-gray-500">Isi form dan klik "Hasilkan Konten Instagram" untuk membuat konten</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
