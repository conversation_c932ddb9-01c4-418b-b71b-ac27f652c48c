import React, { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { NegotiationSession, NegotiationEvaluation } from '../types/negotiation';
import { formatCurrency } from '../utils/pricing';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, BorderStyle, WidthType, AlignmentType } from 'docx';
import { saveAs } from 'file-saver';
import {
  ArrowLeft,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  BarChart3,
  Award,
  Lightbulb,
  MessageSquare,
  User,
  Percent,
  TrendingUp,
  TrendingDown,
  Minus,
  Download,
  Share2,
  FileText,
  MessageCircle
} from 'lucide-react';

interface LocationState {
  session: NegotiationSession;
  evaluation: NegotiationEvaluation;
}

export default function NegotiationResults() {
  const location = useLocation();
  const navigate = useNavigate();
  const [session, setSession] = useState<NegotiationSession | null>(null);
  const [evaluation, setEvaluation] = useState<NegotiationEvaluation | null>(null);

  useEffect(() => {
    // Get session and evaluation from location state
    const state = location.state as LocationState;

    if (!state || !state.session || !state.evaluation) {
      // If no session or evaluation, redirect to simulator
      navigate('/negotiation-simulator');
      return;
    }

    setSession(state.session);
    setEvaluation(state.evaluation);
  }, [location, navigate]);

  // If no session or evaluation, show loading
  if (!session || !evaluation) {
    return (
      <div className="container mx-auto px-4 py-8 flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat hasil negosiasi...</p>
        </div>
      </div>
    );
  }

  // Calculate total original value and negotiated value
  const totalOriginalValue = session.selectedProducts.reduce(
    (sum, item) => sum + (item.originalPrice * item.quantity),
    0
  );

  const totalNegotiatedValue = session.selectedProducts.reduce(
    (sum, item) => sum + ((item.negotiatedPrice || item.originalPrice) * item.quantity),
    0
  );

  // Calculate discount percentage
  const discountPercentage = totalOriginalValue > 0
    ? ((totalOriginalValue - totalNegotiatedValue) / totalOriginalValue) * 100
    : 0;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold">Hasil Negosiasi</h1>
        <button
          onClick={() => navigate('/negotiation-simulator')}
          className="flex items-center text-blue-600 hover:text-blue-800"
        >
          <ArrowLeft className="mr-1" />
          Kembali ke Simulator
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Negotiation Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-medium mb-4 flex items-center">
              <MessageSquare className="mr-2 text-blue-500" />
              Ringkasan Negosiasi
            </h2>

            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-700">Pelanggan</h3>
                <div className="flex items-center mt-1">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <User size={16} className="text-blue-500" />
                  </div>
                  <div className="ml-2">
                    <p className="font-medium">{session.customerPersona.name}</p>
                    <p className="text-xs text-gray-600">{session.customerPersona.company}</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700">Produk yang Dinegosiasikan</h3>
                <div className="mt-1 space-y-2">
                  {session.selectedProducts.map(item => (
                    <div key={item.product.id} className="p-2 bg-gray-50 rounded-md">
                      <p className="font-medium text-sm">{item.product.materialDescription}</p>
                      <div className="flex justify-between text-xs mt-1">
                        <span>{item.quantity} units</span>
                        <div className="flex items-center">
                          <span className="line-through text-gray-500 mr-1">
                            {formatCurrency(item.originalPrice)}
                          </span>
                          <span className="text-green-600">
                            {formatCurrency(item.negotiatedPrice || item.originalPrice)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium text-gray-700">Nilai Awal</h3>
                  <span className="font-medium">{formatCurrency(totalOriginalValue)}</span>
                </div>
                <div className="flex justify-between items-center mt-2">
                  <h3 className="text-sm font-medium text-gray-700">Nilai Negosiasi</h3>
                  <span className="font-medium">{formatCurrency(totalNegotiatedValue)}</span>
                </div>
                <div className="flex justify-between items-center mt-2">
                  <h3 className="text-sm font-medium text-gray-700">Diskon</h3>
                  <span className={`font-medium ${discountPercentage > 0 ? 'text-red-600' : 'text-gray-700'}`}>
                    {discountPercentage.toFixed(2)}%
                  </span>
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium text-gray-700">Target Margin</h3>
                  <span className="font-medium">{session.targetMargin}%</span>
                </div>
                <div className="flex justify-between items-center mt-2">
                  <h3 className="text-sm font-medium text-gray-700">Margin Aktual</h3>
                  <span className={`font-medium ${
                    (session.actualMargin || 0) >= session.targetMargin
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}>
                    {session.actualMargin?.toFixed(2) || 0}%
                  </span>
                </div>
                <div className="flex justify-between items-center mt-2">
                  <h3 className="text-sm font-medium text-gray-700">Selisih Margin</h3>
                  <div className="flex items-center">
                    {evaluation.marginDifference >= 0 ? (
                      <>
                        <TrendingUp size={14} className="text-green-600 mr-1" />
                        <span className="font-medium text-green-600">+{evaluation.marginDifference.toFixed(2)}%</span>
                      </>
                    ) : (
                      <>
                        <TrendingDown size={14} className="text-red-600 mr-1" />
                        <span className="font-medium text-red-600">{evaluation.marginDifference.toFixed(2)}%</span>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium text-gray-700">Skor Keseluruhan</h3>
                  <div className="flex items-center">
                    <span className={`font-medium ${
                      evaluation.overallScore >= 80
                        ? 'text-green-600'
                        : evaluation.overallScore >= 60
                          ? 'text-amber-600'
                          : 'text-red-600'
                    }`}>
                      {evaluation.overallScore}/100
                    </span>
                  </div>
                </div>

                <div className="mt-2 w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className={`h-2.5 rounded-full ${
                      evaluation.overallScore >= 80
                        ? 'bg-green-600'
                        : evaluation.overallScore >= 60
                          ? 'bg-amber-500'
                          : 'bg-red-600'
                    }`}
                    style={{ width: `${evaluation.overallScore}%` }}
                  ></div>
                </div>
              </div>

              <div className="pt-4 flex space-x-2">
                <button
                  onClick={() => navigate('/negotiation-simulator')}
                  className="flex-1 py-2 px-3 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center"
                >
                  <ArrowLeft size={16} className="mr-1" />
                  Negosiasi Baru
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Evaluation Results */}
        <div className="lg:col-span-2">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-medium mb-4 flex items-center">
              <Award className="mr-2 text-purple-500" />
              Evaluasi Negosiasi
            </h2>

            <div className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className={`p-4 rounded-lg ${
                  evaluation.marginAchieved ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
                }`}>
                  <div className="flex items-center">
                    {evaluation.marginAchieved ? (
                      <CheckCircle2 size={20} className="text-green-600 mr-2" />
                    ) : (
                      <XCircle size={20} className="text-red-600 mr-2" />
                    )}
                    <h3 className="font-medium">Target Margin</h3>
                  </div>
                  <p className={`text-sm mt-1 ${
                    evaluation.marginAchieved ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {evaluation.marginAchieved
                      ? 'Berhasil mempertahankan target margin'
                      : 'Gagal mempertahankan target margin'}
                  </p>
                </div>

                <div className={`p-4 rounded-lg ${
                  evaluation.discountAppropriate ? 'bg-green-50 border border-green-200' : 'bg-amber-50 border border-amber-200'
                }`}>
                  <div className="flex items-center">
                    {evaluation.discountAppropriate ? (
                      <CheckCircle2 size={20} className="text-green-600 mr-2" />
                    ) : (
                      <AlertTriangle size={20} className="text-amber-600 mr-2" />
                    )}
                    <h3 className="font-medium">Keputusan Diskon</h3>
                  </div>
                  <p className={`text-sm mt-1 ${
                    evaluation.discountAppropriate ? 'text-green-700' : 'text-amber-700'
                  }`}>
                    {evaluation.discountAppropriate
                      ? 'Strategi diskon yang tepat'
                      : 'Perlu perbaikan pendekatan diskon'}
                  </p>
                </div>

                <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
                  <div className="flex items-center">
                    <BarChart3 size={20} className="text-blue-600 mr-2" />
                    <h3 className="font-medium">Performa Keseluruhan</h3>
                  </div>
                  <p className="text-sm mt-1 text-blue-700">
                    {evaluation.overallScore >= 80
                      ? 'Keterampilan negosiasi sangat baik'
                      : evaluation.overallScore >= 60
                        ? 'Negosiasi baik dengan ruang untuk perbaikan'
                        : 'Membutuhkan perbaikan yang signifikan'}
                  </p>
                </div>
              </div>

              {/* Discount Reasoning */}
              <div className="p-4 bg-gray-50 rounded-lg border">
                <h3 className="font-medium flex items-center">
                  <Percent size={18} className="text-gray-700 mr-2" />
                  Analisis Diskon
                </h3>
                <p className="text-sm mt-2">{evaluation.discountReason}</p>
              </div>

              {/* Key Insights */}
              <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
                <h3 className="font-medium flex items-center text-purple-800">
                  <Lightbulb size={18} className="text-purple-600 mr-2" />
                  Wawasan Utama
                </h3>
                <p className="text-sm mt-2 text-purple-700">{evaluation.keyInsights}</p>
              </div>

              {/* Strengths and Improvements */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <h3 className="font-medium flex items-center text-green-800">
                    <CheckCircle2 size={18} className="text-green-600 mr-2" />
                    Kekuatan
                  </h3>
                  <ul className="mt-2 space-y-1">
                    {evaluation.strengths.map((strength, index) => (
                      <li key={index} className="flex items-start text-sm text-green-700">
                        <span className="mr-2">•</span>
                        <span>{strength}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="p-4 bg-amber-50 rounded-lg border border-amber-200">
                  <h3 className="font-medium flex items-center text-amber-800">
                    <AlertTriangle size={18} className="text-amber-600 mr-2" />
                    Area untuk Perbaikan
                  </h3>
                  <ul className="mt-2 space-y-1">
                    {evaluation.improvements.map((improvement, index) => (
                      <li key={index} className="flex items-start text-sm text-amber-700">
                        <span className="mr-2">•</span>
                        <span>{improvement}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Alternative Strategies */}
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h3 className="font-medium flex items-center text-blue-800">
                  <Lightbulb size={18} className="text-blue-600 mr-2" />
                  Strategi Alternatif
                </h3>
                <ul className="mt-2 space-y-1">
                  {evaluation.alternativeStrategies.map((strategy, index) => (
                    <li key={index} className="flex items-start text-sm text-blue-700">
                      <span className="mr-2">•</span>
                      <span>{strategy}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <button
                  className="flex-1 py-2 px-4 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center justify-center"
                >
                  <Download size={16} className="mr-2" />
                  Unduh Laporan
                </button>
                <button
                  className="flex-1 py-2 px-4 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center justify-center"
                >
                  <Share2 size={16} className="mr-2" />
                  Bagikan Hasil
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
