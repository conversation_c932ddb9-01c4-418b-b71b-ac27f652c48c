<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Sales Dashboard</h1>
                    <p class="mt-2 text-gray-600">Monitor performa penjualan dan target achievement</p>
                </div>
                <div class="flex space-x-3">
                    <select class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                        <option>Bulan Ini</option>
                        <option>Kuartal Ini</option>
                        <option>Tahun Ini</option>
                    </select>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                        <Download class="h-4 w-4 mr-2" />
                        Export Report
                    </button>
                </div>
            </div>

            <!-- KPI Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <DollarSign class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                            <p class="text-2xl font-bold text-gray-900">Rp 2.4M</p>
                            <p class="text-sm text-green-600">+12.5% dari bulan lalu</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Target class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Target Achievement</p>
                            <p class="text-2xl font-bold text-gray-900">87%</p>
                            <p class="text-sm text-blue-600">Target: Rp 2.8M</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <ShoppingCart class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Orders</p>
                            <p class="text-2xl font-bold text-gray-900">156</p>
                            <p class="text-sm text-purple-600">+8 dari minggu lalu</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <TrendingUp class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Avg Order Value</p>
                            <p class="text-2xl font-bold text-gray-900">Rp 15.4K</p>
                            <p class="text-sm text-orange-600">+5.2% improvement</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Sales Trend Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Sales Trend (6 Bulan Terakhir)</h3>
                    <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                        <div class="text-center">
                            <BarChart3 class="h-12 w-12 text-gray-400 mx-auto mb-2" />
                            <p class="text-gray-600">Sales Trend Chart</p>
                            <p class="text-sm text-gray-500">Chart.js integration</p>
                        </div>
                    </div>
                </div>

                <!-- Product Performance -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Performing Products</h3>
                    <div class="space-y-4">
                        <div v-for="product in topProducts" :key="product.id" 
                             class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">{{ product.name }}</h4>
                                <p class="text-sm text-gray-600">{{ product.category }}</p>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-gray-900">{{ product.revenue }}</p>
                                <p class="text-sm text-green-600">{{ product.growth }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales Team Performance -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Sales Team Performance</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales Person</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Achievement</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deals Closed</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="person in salesTeam" :key="person.id">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-700">{{ person.initials }}</span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">{{ person.name }}</p>
                                            <p class="text-sm text-gray-500">{{ person.region }}</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ person.target }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ person.achievement }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-blue-600 h-2 rounded-full" :style="{ width: person.percentage + '%' }"></div>
                                        </div>
                                        <span class="text-sm text-gray-900">{{ person.percentage }}%</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ person.deals }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-blue-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-blue-900">Fitur Dalam Pengembangan</h3>
                        <p class="text-blue-700 mt-1">
                            Sales Dashboard sedang dalam tahap pengembangan. Fitur yang akan tersedia:
                        </p>
                        <ul class="list-disc list-inside text-blue-700 mt-2 space-y-1">
                            <li>Real-time sales tracking dan notifications</li>
                            <li>Advanced analytics dengan Chart.js integration</li>
                            <li>Sales forecasting menggunakan AI</li>
                            <li>Commission calculator dan reporting</li>
                            <li>Integration dengan CRM system</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { 
    Download, 
    DollarSign, 
    Target, 
    ShoppingCart, 
    TrendingUp,
    BarChart3,
    Info
} from 'lucide-vue-next';
import { ref } from 'vue';

const topProducts = ref([
    { id: 1, name: '27.00 R 49 XD GRIP', category: 'Heavy Equipment', revenue: 'Rp 450K', growth: '+15%' },
    { id: 2, name: '24.00 R 35 XD GRIP', category: 'Mining', revenue: 'Rp 380K', growth: '+12%' },
    { id: 3, name: 'Engine Oil SAE 15W-40', category: 'Lubricants', revenue: 'Rp 220K', growth: '+8%' },
    { id: 4, name: 'Air Filter Heavy Duty', category: 'Parts', revenue: 'Rp 180K', growth: '+5%' }
]);

const salesTeam = ref([
    { id: 1, name: 'Ahmad Wijaya', initials: 'AW', region: 'Jakarta', target: 'Rp 500K', achievement: 'Rp 450K', percentage: 90, deals: 23 },
    { id: 2, name: 'Siti Nurhaliza', initials: 'SN', region: 'Surabaya', target: 'Rp 400K', achievement: 'Rp 380K', percentage: 95, deals: 19 },
    { id: 3, name: 'Budi Santoso', initials: 'BS', region: 'Medan', target: 'Rp 350K', achievement: 'Rp 280K', percentage: 80, deals: 15 },
    { id: 4, name: 'Rina Kartika', initials: 'RK', region: 'Bandung', target: 'Rp 300K', achievement: 'Rp 290K', percentage: 97, deals: 18 }
]);
</script>
