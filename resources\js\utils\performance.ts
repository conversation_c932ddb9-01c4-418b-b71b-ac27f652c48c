/**
 * Performance monitoring utilities
 */

interface PerformanceMetric {
    name: string;
    startTime: number;
    endTime?: number;
    duration?: number;
    metadata?: Record<string, any>;
}

class PerformanceMonitor {
    private metrics: Map<string, PerformanceMetric> = new Map();
    private isEnabled: boolean = true;

    constructor() {
        // Disable in production unless explicitly enabled
        this.isEnabled = import.meta.env.DEV || import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true';
    }

    /**
     * Start measuring a performance metric
     */
    start(name: string, metadata?: Record<string, any>): void {
        if (!this.isEnabled) return;

        const metric: PerformanceMetric = {
            name,
            startTime: performance.now(),
            metadata
        };

        this.metrics.set(name, metric);
        console.log(`🚀 Performance: Started measuring "${name}"`);
    }

    /**
     * End measuring a performance metric
     */
    end(name: string): number | null {
        if (!this.isEnabled) return null;

        const metric = this.metrics.get(name);
        if (!metric) {
            console.warn(`⚠️ Performance: No metric found for "${name}"`);
            return null;
        }

        metric.endTime = performance.now();
        metric.duration = metric.endTime - metric.startTime;

        console.log(`✅ Performance: "${name}" took ${metric.duration.toFixed(2)}ms`);

        // Log slow operations
        if (metric.duration > 1000) {
            console.warn(`🐌 Performance: Slow operation detected - "${name}" took ${metric.duration.toFixed(2)}ms`);
        }

        return metric.duration;
    }

    /**
     * Measure a function execution time
     */
    async measure<T>(name: string, fn: () => Promise<T> | T, metadata?: Record<string, any>): Promise<T> {
        this.start(name, metadata);
        try {
            const result = await fn();
            return result;
        } finally {
            this.end(name);
        }
    }

    /**
     * Get all metrics
     */
    getMetrics(): PerformanceMetric[] {
        return Array.from(this.metrics.values());
    }

    /**
     * Get a specific metric
     */
    getMetric(name: string): PerformanceMetric | undefined {
        return this.metrics.get(name);
    }

    /**
     * Clear all metrics
     */
    clear(): void {
        this.metrics.clear();
    }

    /**
     * Get performance summary
     */
    getSummary(): {
        totalMetrics: number;
        averageDuration: number;
        slowestOperation: PerformanceMetric | null;
        fastestOperation: PerformanceMetric | null;
    } {
        const metrics = this.getMetrics().filter(m => m.duration !== undefined);
        
        if (metrics.length === 0) {
            return {
                totalMetrics: 0,
                averageDuration: 0,
                slowestOperation: null,
                fastestOperation: null
            };
        }

        const durations = metrics.map(m => m.duration!);
        const averageDuration = durations.reduce((sum, duration) => sum + duration, 0) / durations.length;
        
        const slowestOperation = metrics.reduce((slowest, current) => 
            (current.duration! > slowest.duration!) ? current : slowest
        );
        
        const fastestOperation = metrics.reduce((fastest, current) => 
            (current.duration! < fastest.duration!) ? current : fastest
        );

        return {
            totalMetrics: metrics.length,
            averageDuration,
            slowestOperation,
            fastestOperation
        };
    }

    /**
     * Log performance summary to console
     */
    logSummary(): void {
        if (!this.isEnabled) return;

        const summary = this.getSummary();
        
        console.group('📊 Performance Summary');
        console.log(`Total operations measured: ${summary.totalMetrics}`);
        console.log(`Average duration: ${summary.averageDuration.toFixed(2)}ms`);
        
        if (summary.slowestOperation) {
            console.log(`Slowest operation: "${summary.slowestOperation.name}" (${summary.slowestOperation.duration!.toFixed(2)}ms)`);
        }
        
        if (summary.fastestOperation) {
            console.log(`Fastest operation: "${summary.fastestOperation.name}" (${summary.fastestOperation.duration!.toFixed(2)}ms)`);
        }
        
        console.groupEnd();
    }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Decorator for measuring Vue component method performance
 */
export function measurePerformance(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
        const methodName = `${target.constructor.name}.${propertyKey}`;
        return await performanceMonitor.measure(methodName, () => originalMethod.apply(this, args));
    };

    return descriptor;
}

/**
 * Utility to measure page load performance
 */
export function measurePageLoad(pageName: string): void {
    if (typeof window === 'undefined') return;

    // Measure when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            measurePageLoadMetrics(pageName);
        });
    } else {
        measurePageLoadMetrics(pageName);
    }
}

function measurePageLoadMetrics(pageName: string): void {
    // Use Navigation Timing API
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    if (navigation) {
        const metrics = {
            dns: navigation.domainLookupEnd - navigation.domainLookupStart,
            tcp: navigation.connectEnd - navigation.connectStart,
            request: navigation.responseStart - navigation.requestStart,
            response: navigation.responseEnd - navigation.responseStart,
            dom: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            load: navigation.loadEventEnd - navigation.loadEventStart,
            total: navigation.loadEventEnd - navigation.navigationStart
        };

        console.group(`📈 Page Load Metrics: ${pageName}`);
        console.log(`DNS Lookup: ${metrics.dns.toFixed(2)}ms`);
        console.log(`TCP Connection: ${metrics.tcp.toFixed(2)}ms`);
        console.log(`Request: ${metrics.request.toFixed(2)}ms`);
        console.log(`Response: ${metrics.response.toFixed(2)}ms`);
        console.log(`DOM Processing: ${metrics.dom.toFixed(2)}ms`);
        console.log(`Load Event: ${metrics.load.toFixed(2)}ms`);
        console.log(`Total Load Time: ${metrics.total.toFixed(2)}ms`);
        console.groupEnd();

        // Warn about slow page loads
        if (metrics.total > 3000) {
            console.warn(`🐌 Slow page load detected: ${pageName} took ${metrics.total.toFixed(2)}ms`);
        }
    }
}

/**
 * Utility to measure bundle size impact
 */
export function logBundleInfo(): void {
    if (typeof window === 'undefined' || !import.meta.env.DEV) return;

    // Log loaded scripts
    const scripts = Array.from(document.querySelectorAll('script[src]'));
    let totalSize = 0;

    console.group('📦 Bundle Information');
    
    scripts.forEach((script: any) => {
        if (script.src.includes('/build/')) {
            console.log(`Script: ${script.src.split('/').pop()}`);
        }
    });

    console.log(`Total scripts loaded: ${scripts.length}`);
    console.groupEnd();
}

// Auto-initialize performance monitoring
if (typeof window !== 'undefined') {
    // Log bundle info on load
    window.addEventListener('load', () => {
        logBundleInfo();
        
        // Log performance summary after a delay
        setTimeout(() => {
            performanceMonitor.logSummary();
        }, 1000);
    });
}
