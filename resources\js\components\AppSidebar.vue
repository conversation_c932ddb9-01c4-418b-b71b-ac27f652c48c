<script setup lang="ts">
import NavFooter from '@/components/NavFooter.vue';
import NavMain from '@/components/NavMain.vue';
import NavUser from '@/components/NavUser.vue';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/vue3';
import {
    BookOpen,
    Folder,
    LayoutGrid,
    Package,
    Calculator,
    FolderOpen,
    LineChart,
    Megaphone,
    Instagram,
    Brain,
    Users,
    BarChart3,
    Truck,
    Gift,
    Percent,
    FileSpreadsheet,
    FileText,
    FileUp,
    Calendar,
    Sparkles,
    Video,
    MessageSquare,
    Settings,
    MessageCircle,
    Database,
    ImageIcon,
    Activity,
    Mic
} from 'lucide-vue-next';
import AppLogo from './AppLogo.vue';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: BarChart3,
    },
    {
        title: 'Kalkulator & Simulasi',
        icon: Calculator,
        items: [
            {
                title: '27.00 R 49 Bundling',
                href: '/ban-27-bundling',
                icon: Truck,
            },
            {
                title: 'Slow Moving Bundling',
                href: '/bundling-calculator',
                icon: Calculator,
            },
            {
                title: '27.00R49 0% + Bonus',
                href: '/zero-margin-bundling',
                icon: Gift,
            },
            {
                title: 'Simulasi Promo Bisnis',
                href: '/promo-simulation',
                icon: Percent,
            },
        ],
    },
    {
        title: 'Manajemen Data',
        icon: FolderOpen,
        items: [
            {
                title: 'Product Management',
                href: '/products',
                icon: Package,
            },
            {
                title: 'Customers',
                href: '/customers',
                icon: Users,
            },
            {
                title: 'Data Harga Batu Bara',
                href: '/coal-price-data',
                icon: BarChart3,
            },
            {
                title: 'Template Management',
                href: '/template-management',
                icon: FileUp,
            },
            {
                title: 'Sales Revenue 2025 Data Master',
                href: '/sales-revenue-2025-data-master',
                icon: FileSpreadsheet,
            },
            {
                title: 'Proposal Builder',
                href: '/proposal-builder',
                icon: FileText,
            },
        ],
    },
    {
        title: 'Analisis & Dashboard',
        icon: LineChart,
        items: [
            {
                title: 'Dashboard Analitik Terpadu',
                href: '/analytics-dashboard',
                icon: BarChart3,
            },
            {
                title: 'Sales Dashboard',
                href: '/sales-dashboard',
                icon: BarChart3,
            },
            {
                title: 'Fleet Analyzer',
                href: '/fleet-analyzer',
                icon: Brain,
            },
            {
                title: '🚛 Fleet Tire Analyzer',
                href: '/fleet-tire-analyzer',
                icon: Truck,
            },
            {
                title: 'Analisis Pelanggan',
                href: '/customer-analysis',
                icon: Users,
            },
            {
                title: 'Analisis Produk',
                href: '/product-analysis',
                icon: Package,
            },
        ],
    },
    {
        title: 'Marketing & Proposal',
        icon: Megaphone,
        items: [
            {
                title: 'Kalender Pemasaran Musiman',
                href: '/seasonal-marketing-calendar',
                icon: Calendar,
            },
            {
                title: 'Bundling Proposal',
                href: '/bundling-proposal',
                icon: FileText,
            },
            {
                title: 'Marketing Insights Hub',
                href: '/marketing-insights',
                icon: Sparkles,
            },
        ],
    },
    {
        title: 'Social Media Marketing',
        icon: Instagram,
        items: [
            {
                title: 'Instagram Management',
                href: '/social-media-marketing',
                icon: Instagram,
            },
            {
                title: 'Rencana Konten Bulanan',
                href: '/monthly-content-plan',
                icon: Calendar,
            },
            {
                title: 'Video Script Generator',
                href: '/video-script-generator',
                icon: Video,
            },
            {
                title: 'Instagram Analysis',
                href: '/instagram-analysis',
                icon: BarChart3,
            },
        ],
    },
    {
        title: 'AI Tools',
        icon: Brain,
        items: [
            {
                title: 'AI Negotiation Simulator',
                href: '/negotiation-simulator',
                icon: MessageSquare,
            },
            {
                title: 'Analisis Chat WhatsApp',
                href: '/whatsapp-chat-analysis',
                icon: MessageCircle,
            },
            {
                title: 'Knowledge Base',
                href: '/knowledge-base',
                icon: Database,
            },
            {
                title: 'Proposal Analyzer',
                href: '/proposal-analyzer',
                icon: FileText,
            },
            {
                title: 'Image Generator',
                href: '/image-generator',
                icon: ImageIcon,
            },
            {
                title: 'SWOT Analysis AI',
                href: '/swot-analysis',
                icon: Activity,
            },
            {
                title: 'Presentation Analyzer',
                href: '/presentation-analyzer',
                icon: Mic,
            },
        ],
    },
];

const footerNavItems: NavItem[] = [
    {
        title: 'Design System',
        href: '/style-guide',
        icon: LayoutGrid,
    },
];
</script>

<template>
    <Sidebar collapsible="icon" variant="inset">
        <SidebarHeader>
            <SidebarMenu>
                <SidebarMenuItem>
                    <SidebarMenuButton size="lg" as-child>
                        <Link :href="route('dashboard')">
                            <AppLogo />
                        </Link>
                    </SidebarMenuButton>
                </SidebarMenuItem>
            </SidebarMenu>
        </SidebarHeader>

        <SidebarContent>
            <NavMain :items="mainNavItems" />
        </SidebarContent>

        <SidebarFooter>
            <NavFooter :items="footerNavItems" />
            <NavUser />
        </SidebarFooter>
    </Sidebar>
    <slot />
</template>
