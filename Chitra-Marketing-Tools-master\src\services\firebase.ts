import { initializeApp } from 'firebase/app';
import { getFirestore, collection, getDocs, doc, setDoc, updateDoc, deleteDoc, query, where, DocumentData, QuerySnapshot } from 'firebase/firestore';
import { getAuth, signInWithEmailAndPassword, signOut, createUserWithEmailAndPassword, onAuthStateChanged, User } from 'firebase/auth';
import { getAnalytics } from 'firebase/analytics';
import { Product } from '../types';

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyBiKBxhDuOE_b1Mb53TmCOpoeZD16YaVIQ",
  authDomain: "chitra-marketing-tools.firebaseapp.com",
  databaseURL: "https://chitra-marketing-tools-default-rtdb.firebaseio.com",
  projectId: "chitra-marketing-tools",
  storageBucket: "chitra-marketing-tools.firebasestorage.app",
  messagingSenderId: "290890558102",
  appId: "1:290890558102:web:a1fa8ad3d9c229e16430b7",
  measurementId: "G-4E9VL6F09Q"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = typeof window !== 'undefined' ? getAnalytics(app) : null;
const db = getFirestore(app);
const auth = getAuth(app);

// Products Collection
const productsCollection = collection(db, 'products');

// Cache for products to avoid frequent Firebase calls
let productsCache: Product[] | null = null;
let lastFetchTime = 0;
const CACHE_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes

// Fetch all products from Firestore with caching
export async function fetchProducts(forceRefresh = false): Promise<Product[]> {
  const now = Date.now();

  // Use cache if available and not expired, unless force refresh is requested
  if (!forceRefresh && productsCache && (now - lastFetchTime < CACHE_EXPIRY_MS)) {
    console.log('Using cached products data');
    return productsCache;
  }

  // Try localStorage first for faster initial load
  const localProducts = loadProducts();

  try {
    // Start loading from Firebase
    console.log('Fetching products from Firebase...');
    const querySnapshot = await getDocs(productsCollection);
    const products: Product[] = [];

    querySnapshot.forEach((doc) => {
      products.push(doc.data() as Product);
    });

    if (products.length > 0) {
      // Update cache
      productsCache = products;
      lastFetchTime = now;

      // Also update localStorage for offline access
      localStorage.setItem('bundleBoostProducts', JSON.stringify(products));
      console.log(`Loaded ${products.length} products from Firebase`);

      return products;
    } else {
      // If no products in Firestore, use localStorage data
      console.log('No products found in Firestore, using localStorage data');
      productsCache = localProducts;
      return localProducts;
    }
  } catch (error) {
    console.error('Error fetching products from Firebase:', error);
    // On error, use localStorage as fallback
    console.log('Falling back to localStorage products');
    productsCache = localProducts;
    return localProducts;
  }
}

// Save products to Firestore using proper batching
export async function saveProductsToFirestore(products: Product[]): Promise<void> {
  try {
    if (products.length === 0) return;

    console.log(`Starting to save ${products.length} products to Firebase...`);

    // Process in batches to avoid rate limits
    const batchSize = 500; // Firestore batch limit is 500
    const totalBatches = Math.ceil(products.length / batchSize);

    // Create an array of promises for parallel processing
    const batchPromises = [];

    for (let batchNum = 0; batchNum < totalBatches; batchNum++) {
      const startIndex = batchNum * batchSize;
      const endIndex = Math.min(startIndex + batchSize, products.length);
      const batchItems = products.slice(startIndex, endIndex);

      // Create a new batch
      const writeBatch = getFirestore().batch();

      // Add each product to the batch
      batchItems.forEach(product => {
        const docRef = doc(productsCollection, product.id);
        writeBatch.set(docRef, product);
      });

      // Add the batch commit promise to our array
      batchPromises.push(
        writeBatch.commit()
          .then(() => {
            console.log(`Batch ${batchNum + 1}/${totalBatches} saved (${startIndex} to ${endIndex-1})`);
          })
          .catch(error => {
            console.error(`Error in batch ${batchNum + 1}/${totalBatches}:`, error);
          })
      );
    }

    // Wait for all batches to complete
    await Promise.all(batchPromises);

    // Update cache after successful save
    productsCache = [...products];
    lastFetchTime = Date.now();

    console.log('All products saved to Firebase successfully');
  } catch (error) {
    console.error('Error saving products to Firebase:', error);
  }
}

// Save products to localStorage and Firebase
export function saveProducts(products: Product[]) {
  try {
    // Save to localStorage
    localStorage.setItem('bundleBoostProducts', JSON.stringify(products));
    console.log('Products saved to localStorage:', products.length);

    // Dispatch a custom event to notify components in the same tab
    const updateEvent = new Event('productDataUpdated');
    window.dispatchEvent(updateEvent);

    // For cross-tab communication, we can use the storage event
    // This is a hack to trigger storage events in the same tab
    // by modifying a temporary item that signals data changes
    const timestamp = new Date().getTime();
    localStorage.setItem('productUpdate', timestamp.toString());
    localStorage.removeItem('productUpdate');

    // Also save to Firebase
    saveProductsToFirestore(products);

  } catch (e) {
    console.error('Error saving products:', e);
  }
}

// Update a single product in Firestore and localStorage
export async function updateProduct(product: Product): Promise<boolean> {
  try {
    console.log(`Updating product ${product.oldMaterialNo}...`);

    // Update in Firestore
    await setDoc(doc(productsCollection, product.id), product);

    // Update in localStorage and cache
    if (productsCache) {
      // Update the cache directly for immediate UI updates
      productsCache = productsCache.map(p => p.id === product.id ? product : p);
    }

    // Update localStorage
    const products = loadProducts();
    const updatedProducts = products.map(p =>
      p.id === product.id ? product : p
    );
    localStorage.setItem('bundleBoostProducts', JSON.stringify(updatedProducts));

    // Dispatch update event
    const updateEvent = new Event('productDataUpdated');
    window.dispatchEvent(updateEvent);

    console.log(`Product ${product.oldMaterialNo} updated successfully`);
    return true;
  } catch (error) {
    console.error('Error updating product:', error);
    return false;
  }
}

// Delete a product from Firestore and localStorage
export async function deleteProduct(productId: string): Promise<boolean> {
  try {
    console.log(`Deleting product with ID ${productId}...`);

    // Delete from Firestore
    await deleteDoc(doc(productsCollection, productId));

    // Update cache if it exists
    if (productsCache) {
      productsCache = productsCache.filter(p => p.id !== productId);
    }

    // Delete from localStorage
    const products = loadProducts();
    const updatedProducts = products.filter(p => p.id !== productId);
    localStorage.setItem('bundleBoostProducts', JSON.stringify(updatedProducts));

    // Dispatch update event
    const updateEvent = new Event('productDataUpdated');
    window.dispatchEvent(updateEvent);

    console.log('Product deleted successfully');
    return true;
  } catch (error) {
    console.error('Error deleting product:', error);
    return false;
  }
}

// Sample data based on the existing mock products
export const mockProducts: Product[] = [
  {
    id: '1',
    oldMaterialNo: '226-10.00-20 GT',
    materialDescription: '10.00 - 20 GT MILLER',
    description: 'CK SEBAMBAN',
    price: 3157882
  },
  {
    id: '2',
    oldMaterialNo: '206-10.00R20-HN10',
    materialDescription: '10.00 R 20 TT HN10',
    description: 'Palembang',
    price: 3964627
  },
  {
    id: '3',
    oldMaterialNo: '206-11R22.5-HN08',
    materialDescription: '11 R 22.5 TL HN08',
    description: 'Jakarta',
    price: 2960168
  },
  {
    id: '4',
    oldMaterialNo: '206-11R22.5-HN10',
    materialDescription: '11 R 22.5 TL HN10',
    description: 'Jakarta',
    price: 3543140
  },
  {
    id: '5',
    oldMaterialNo: '200-078266',
    materialDescription: '11 R 22.5 XMZ 2 TL 148/145LVM MI',
    description: 'Pekanbaru',
    price: 5116178
  }
];

// Load products from localStorage
function loadProducts(): Product[] {
  const storedProducts = localStorage.getItem('bundleBoostProducts');
  if (storedProducts) {
    try {
      return JSON.parse(storedProducts);
    } catch (e) {
      console.error('Error parsing stored products:', e);
    }
  }

  // Default to mockProducts if none in storage
  return mockProducts;
}

// Authentication functions
export const firebaseAuth = {
  login: async (email: string, password: string): Promise<User> => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      return userCredential.user;
    } catch (error) {
      console.error('Firebase login error:', error);
      throw error;
    }
  },

  logout: async (): Promise<void> => {
    try {
      await signOut(auth);
    } catch (error) {
      console.error('Firebase logout error:', error);
      throw error;
    }
  },

  getCurrentUser: (): User | null => {
    return auth.currentUser;
  },

  onAuthStateChanged: (callback: (user: User | null) => void): (() => void) => {
    return onAuthStateChanged(auth, callback);
  }
};

export { db, auth, analytics };
