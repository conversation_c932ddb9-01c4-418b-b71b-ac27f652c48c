import React, { useState, useCallback, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import axios from 'axios';
import { CheckCircle, AlertCircle, MessageSquare, BarChart2, Clock, Users } from 'lucide-react';
import html2pdf from 'html2pdf.js';

interface PresentationScore {
  score: number;
  maxScore: number;
  feedback: string;
}

interface PresentationAnalysis {
  type: 'presentation' | 'meeting' | 'negotiation';
  overallScore: number;
  scores: {
    structure: PresentationScore;
    delivery: PresentationScore;
    content: PresentationScore;
    engagement: PresentationScore;
    timeManagement: PresentationScore;
  };
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  keyPoints: {
    mainTopics: string[];
    actionItems: string[];
    nextSteps: string[];
  };
  audienceFeedback: {
    engagement: string;
    understanding: string;
    suggestions: string[];
  };
}

interface AnalysisResult {
  type: 'presentation' | 'meeting' | 'negotiation';
  content: string;
  structuredAnalysis?: PresentationAnalysis;
}

const PresentationAnalyzer: React.FC = () => {
  const [audioFile, setAudioFile] = useState<File | null>(null);
  const [transcript, setTranscript] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const pdfRef = useRef<HTMLDivElement>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    console.log('File yang diterima:', file);
    
    if (file) {
      // Validasi tipe file
      const validTypes = ['audio/mp3', 'audio/wav', 'audio/m4a', 'audio/mpeg'];
      const fileType = file.type.toLowerCase();
      
      console.log('Tipe file:', fileType);
      
      if (validTypes.includes(fileType)) {
        setAudioFile(file);
        setError(null);
        console.log('File valid, siap untuk upload');
      } else {
        setError(`Format file tidak didukung. Format yang didukung: MP3, WAV, M4A. Tipe file yang diupload: ${fileType}`);
        console.error('Format file tidak valid:', fileType);
      }
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'audio/*': ['.mp3', '.wav', '.m4a']
    },
    maxSize: 100 * 1024 * 1024, // 100MB
    onDropRejected: (rejectedFiles) => {
      console.error('File ditolak:', rejectedFiles);
      setError('File terlalu besar atau format tidak didukung');
    }
  });

  const handleTranscribe = async () => {
    if (!audioFile) {
      setError('Tidak ada file yang dipilih');
      return;
    }

    setIsLoading(true);
    setError(null);
    setUploadProgress(0);

    try {
      console.log('Memulai proses upload...');
      
      // Upload audio ke AssemblyAI
      const formData = new FormData();
      formData.append('audio', audioFile);

      console.log('Mengirim request ke AssemblyAI...');
      const uploadResponse = await axios.post(
        'https://api.assemblyai.com/v2/upload',
        formData,
        {
          headers: {
            'authorization': '71367b4deed34a77b9b6d1d358125da3',
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            const progress = progressEvent.total
              ? Math.round((progressEvent.loaded * 100) / progressEvent.total)
              : 0;
            setUploadProgress(progress);
            console.log(`Progress upload: ${progress}%`);
          }
        }
      );

      console.log('Upload berhasil:', uploadResponse.data);
      const audioUrl = uploadResponse.data.upload_url;

      // Transcribe audio
      console.log('Memulai proses transkripsi...');
      const transcriptResponse = await axios.post(
        'https://api.assemblyai.com/v2/transcript',
        {
          audio_url: audioUrl,
          language_code: 'id',
          speech_model: 'nano'
        },
        {
          headers: {
            'authorization': '71367b4deed34a77b9b6d1d358125da3',
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Transkripsi dimulai:', transcriptResponse.data);
      const transcriptId = transcriptResponse.data.id;

      // Poll untuk hasil transkrip
      let transcriptResult;
      let attempts = 0;
      const maxAttempts = 60; // Maksimal 60 detik

      while (attempts < maxAttempts) {
        console.log(`Mencoba mendapatkan hasil transkripsi (percobaan ${attempts + 1})...`);
        const statusResponse = await axios.get(
          `https://api.assemblyai.com/v2/transcript/${transcriptId}`,
          {
            headers: {
              'authorization': '71367b4deed34a77b9b6d1d358125da3'
            }
          }
        );

        console.log('Status transkripsi:', statusResponse.data.status);

        if (statusResponse.data.status === 'completed') {
          transcriptResult = statusResponse.data.text;
          break;
        } else if (statusResponse.data.status === 'error') {
          throw new Error(`Transkripsi gagal: ${statusResponse.data.error}`);
        }

        attempts++;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      if (!transcriptResult) {
        throw new Error('Waktu tunggu transkripsi habis');
      }

      setTranscript(transcriptResult);
      console.log('Transkripsi berhasil');
    } catch (error: any) {
      console.error('Error detail:', error);
      setError(
        error.response?.data?.error || 
        error.message || 
        'Terjadi kesalahan saat memproses audio'
      );
    } finally {
      setIsLoading(false);
      setUploadProgress(0);
    }
  };

  const handleAnalyze = async () => {
    if (!transcript) return;

    setIsLoading(true);
    setError(null);
    try {
      const response = await axios.post(
        'https://openrouter.ai/api/v1/chat/completions',
        {
          model: 'openai/gpt-4.1-nano',
          messages: [
            {
              role: 'system',
              content: `Anda adalah asisten yang ahli dalam menganalisis transkrip audio. Analisis apakah ini presentasi, rapat, atau negosiasi, dan berikan analisis yang sesuai dalam format JSON berikut:
              {
                "type": "presentation|meeting|negotiation",
                "overallScore": number,
                "scores": {
                  "structure": { "score": number, "maxScore": 10, "feedback": string },
                  "delivery": { "score": number, "maxScore": 10, "feedback": string },
                  "content": { "score": number, "maxScore": 10, "feedback": string },
                  "engagement": { "score": number, "maxScore": 10, "feedback": string },
                  "timeManagement": { "score": number, "maxScore": 10, "feedback": string }
                },
                "strengths": string[],
                "weaknesses": string[],
                "recommendations": string[],
                "keyPoints": {
                  "mainTopics": string[],
                  "actionItems": string[],
                  "nextSteps": string[]
                },
                "audienceFeedback": {
                  "engagement": string,
                  "understanding": string,
                  "suggestions": string[]
                }
              }`
            },
            {
              role: 'user',
              content: transcript
            }
          ]
        },
        {
          headers: {
            'Authorization': 'Bearer 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',
            'Content-Type': 'application/json'
          }
        }
      );

      const analysis = response.data.choices[0].message.content;
      let structuredAnalysis;
      try {
        structuredAnalysis = JSON.parse(analysis);
      } catch (e) {
        console.error('Error parsing analysis:', e);
      }

      setAnalysisResult({
        type: structuredAnalysis?.type || 'presentation',
        content: analysis,
        structuredAnalysis
      });
    } catch (error) {
      console.error('Error during analysis:', error);
      setError('Terjadi kesalahan saat menganalisis transkrip');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportPDF = () => {
    if (pdfRef.current) {
      html2pdf()
        .set({
          margin: 0,
          filename: 'Hasil-Analisis-Presentasi.pdf',
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: { scale: 2, useCORS: true },
          jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        })
        .from(pdfRef.current)
        .save();
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-sm">
      <h1 className="text-3xl font-bold mb-6 text-gray-800">Presentation Analyzer</h1>
      
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer mb-6 transition-colors
          ${isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400'}`}
      >
        <input {...getInputProps()} />
        {audioFile ? (
          <div className="space-y-2">
            <p className="text-gray-700">File terpilih: {audioFile.name}</p>
            <p className="text-sm text-gray-500">Klik untuk memilih file lain</p>
          </div>
        ) : (
          <div className="space-y-2">
            <p className="text-gray-700">Drag & drop file audio (.mp3, .wav, .m4a) atau klik untuk memilih file</p>
            <p className="text-sm text-gray-500">Maksimal ukuran file: 100MB</p>
          </div>
        )}
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {uploadProgress > 0 && uploadProgress < 100 && (
        <div className="mb-6">
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className="bg-blue-600 h-2.5 rounded-full transition-all duration-300" 
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600 mt-2">Upload: {uploadProgress}%</p>
        </div>
      )}

      {audioFile && !transcript && (
        <button
          onClick={handleTranscribe}
          disabled={isLoading}
          className="w-full bg-blue-500 text-white px-4 py-3 rounded-lg hover:bg-blue-600 disabled:bg-gray-400 transition-colors font-medium"
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {uploadProgress > 0 ? `Mengupload: ${uploadProgress}%` : 'Memproses...'}
            </div>
          ) : (
            'Transkrip Audio'
          )}
        </button>
      )}

      {transcript && (
        <div className="mt-6 space-y-6">
          <div>
            <h2 className="text-xl font-semibold mb-4 text-gray-800">Hasil Transkrip:</h2>
            <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
              <p className="whitespace-pre-wrap text-gray-700">{transcript}</p>
            </div>
          </div>

          <button
            onClick={handleAnalyze}
            disabled={isLoading}
            className="w-full bg-green-500 text-white px-4 py-3 rounded-lg hover:bg-green-600 disabled:bg-gray-400 transition-colors font-medium"
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Menganalisis...
              </div>
            ) : (
              'Analisa dengan AI'
            )}
          </button>
        </div>
      )}

      {analysisResult && (
        <div className="mt-6 space-y-6">
          <div className="flex justify-end mb-4">
            <button
              onClick={handleExportPDF}
              className="bg-red-600 text-white px-4 py-2 rounded-lg shadow hover:bg-red-700 transition-colors font-medium"
            >
              Export PDF
            </button>
          </div>
          <div ref={pdfRef}>
            <h2 className="text-2xl font-bold text-gray-800">Hasil Analisis Presentasi</h2>
            
            {analysisResult.structuredAnalysis ? (
              <div className="space-y-6">
                {/* Overall Score Card */}
                <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">Skor Keseluruhan</h3>
                      <p className="text-sm opacity-90">Evaluasi komprehensif presentasi</p>
                    </div>
                    <div className="text-4xl font-bold">
                      {analysisResult.structuredAnalysis.overallScore}/10
                    </div>
                  </div>
                </div>

                {/* Detailed Scores */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(analysisResult.structuredAnalysis.scores).map(([key, score]) => (
                    <div key={key} className="bg-white rounded-lg shadow p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-700 capitalize">{key}</h4>
                        <div className="text-lg font-bold text-blue-600">{score.score}/{score.maxScore}</div>
                      </div>
                      <div className="h-2 bg-gray-200 rounded-full">
                        <div 
                          className="h-2 bg-blue-600 rounded-full"
                          style={{ width: `${(score.score / score.maxScore) * 100}%` }}
                        ></div>
                      </div>
                      <p className="mt-2 text-sm text-gray-600">{score.feedback}</p>
                    </div>
                  ))}
                </div>

                {/* Strengths and Weaknesses */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-white rounded-lg shadow p-6">
                    <h3 className="text-lg font-semibold mb-4 flex items-center text-green-600">
                      <CheckCircle className="mr-2" size={20} />
                      Kekuatan
                    </h3>
                    <ul className="space-y-2">
                      {analysisResult.structuredAnalysis.strengths.map((strength, index) => (
                        <li key={index} className="flex items-start">
                          <span className="inline-block w-5 h-5 rounded-full bg-green-100 text-green-800 text-xs flex items-center justify-center mr-2 mt-0.5">
                            {index + 1}
                          </span>
                          <span className="text-gray-700">{strength}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="bg-white rounded-lg shadow p-6">
                    <h3 className="text-lg font-semibold mb-4 flex items-center text-red-600">
                      <AlertCircle className="mr-2" size={20} />
                      Area Perbaikan
                    </h3>
                    <ul className="space-y-2">
                      {analysisResult.structuredAnalysis.weaknesses.map((weakness, index) => (
                        <li key={index} className="flex items-start">
                          <span className="inline-block w-5 h-5 rounded-full bg-red-100 text-red-800 text-xs flex items-center justify-center mr-2 mt-0.5">
                            {index + 1}
                          </span>
                          <span className="text-gray-700">{weakness}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* Key Points */}
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center text-indigo-600">
                    <MessageSquare className="mr-2" size={20} />
                    Poin-Poin Kunci
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">Topik Utama</h4>
                      <ul className="space-y-2">
                        {analysisResult.structuredAnalysis.keyPoints.mainTopics.map((topic, index) => (
                          <li key={index} className="text-gray-600">{topic}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">Action Items</h4>
                      <ul className="space-y-2">
                        {analysisResult.structuredAnalysis.keyPoints.actionItems.map((item, index) => (
                          <li key={index} className="text-gray-600">{item}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">Langkah Selanjutnya</h4>
                      <ul className="space-y-2">
                        {analysisResult.structuredAnalysis.keyPoints.nextSteps.map((step, index) => (
                          <li key={index} className="text-gray-600">{step}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Recommendations */}
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center text-blue-600">
                    <BarChart2 className="mr-2" size={20} />
                    Rekomendasi
                  </h3>
                  <ul className="space-y-3">
                    {analysisResult.structuredAnalysis.recommendations.map((recommendation, index) => (
                      <li key={index} className="flex items-start">
                        <span className="inline-block w-5 h-5 rounded-full bg-blue-100 text-blue-800 text-xs flex items-center justify-center mr-2 mt-0.5">
                          {index + 1}
                        </span>
                        <span className="text-gray-700">{recommendation}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Audience Feedback */}
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold mb-4 flex items-center text-purple-600">
                    <Users className="mr-2" size={20} />
                    Feedback Audiens
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">Tingkat Keterlibatan</h4>
                      <p className="text-gray-600">{analysisResult.structuredAnalysis.audienceFeedback.engagement}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">Tingkat Pemahaman</h4>
                      <p className="text-gray-600">{analysisResult.structuredAnalysis.audienceFeedback.understanding}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">Saran</h4>
                      <ul className="space-y-2">
                        {analysisResult.structuredAnalysis.audienceFeedback.suggestions.map((suggestion, index) => (
                          <li key={index} className="flex items-start">
                            <span className="inline-block w-5 h-5 rounded-full bg-purple-100 text-purple-800 text-xs flex items-center justify-center mr-2 mt-0.5">
                              {index + 1}
                            </span>
                            <span className="text-gray-600">{suggestion}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                <p className="whitespace-pre-wrap text-gray-700">{analysisResult.content}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PresentationAnalyzer; 