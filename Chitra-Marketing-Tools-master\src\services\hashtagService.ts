import {
  HashtagEntry,
  HashtagCreateRequest,
  HashtagUpdateRequest,
  HashtagSearchRequest,
  HashtagSearchResponse,
  HashtagAnalysisRequest,
  HashtagAnalysisResponse,
  HashtagCategory,
  HashtagGroup
} from '../types/hashtag';
import { MODELS } from './socialMediaService';

// Local storage key for hashtags
const HASHTAGS_STORAGE_KEY = 'chitraMarketingTools_hashtags';
const HASHTAG_GROUPS_STORAGE_KEY = 'chitraMarketingTools_hashtagGroups';

// OpenRouter API key
const OPENROUTER_API_KEY = '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';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Default model for hashtag analysis
const DEFAULT_MODEL = MODELS.DEEPSEEK;

/**
 * Load hashtags from local storage
 */
export const loadHashtags = (): HashtagEntry[] => {
  const hashtagsJson = localStorage.getItem(HASHTAGS_STORAGE_KEY);
  if (!hashtagsJson) return getSampleHashtags();
  
  try {
    const hashtags = JSON.parse(hashtagsJson) as HashtagEntry[];
    
    // Convert string dates back to Date objects
    return hashtags.map(hashtag => ({
      ...hashtag,
      createdAt: new Date(hashtag.createdAt),
      updatedAt: new Date(hashtag.updatedAt),
      performance: hashtag.performance ? {
        ...hashtag.performance,
        lastUpdated: new Date(hashtag.performance.lastUpdated)
      } : undefined
    }));
  } catch (error) {
    console.error('Error parsing hashtags:', error);
    return getSampleHashtags();
  }
};

/**
 * Save hashtags to local storage
 */
export const saveHashtags = (hashtags: HashtagEntry[]): void => {
  localStorage.setItem(HASHTAGS_STORAGE_KEY, JSON.stringify(hashtags));
};

/**
 * Get all hashtags
 */
export const getAllHashtags = async (): Promise<HashtagEntry[]> => {
  return loadHashtags();
};

/**
 * Get hashtag by ID
 */
export const getHashtagById = async (id: string): Promise<HashtagEntry | null> => {
  const hashtags = loadHashtags();
  return hashtags.find(hashtag => hashtag.id === id) || null;
};

/**
 * Create a new hashtag
 */
export const createHashtag = async (request: HashtagCreateRequest): Promise<HashtagEntry> => {
  const hashtags = loadHashtags();
  
  // Check if hashtag with same name already exists
  const existingHashtag = hashtags.find(h => 
    h.name.toLowerCase() === request.name.toLowerCase()
  );
  
  if (existingHashtag) {
    throw new Error(`Hashtag dengan nama ${request.name} sudah ada`);
  }
  
  const newHashtag: HashtagEntry = {
    id: `hashtag-${Date.now()}`,
    name: request.name.startsWith('#') ? request.name.substring(1) : request.name,
    category: request.category,
    description: request.description,
    isActive: request.isActive !== undefined ? request.isActive : true,
    relatedHashtags: request.relatedHashtags || [],
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  const updatedHashtags = [...hashtags, newHashtag];
  saveHashtags(updatedHashtags);
  
  return newHashtag;
};

/**
 * Update an existing hashtag
 */
export const updateHashtag = async (request: HashtagUpdateRequest): Promise<HashtagEntry> => {
  const hashtags = loadHashtags();
  const index = hashtags.findIndex(h => h.id === request.id);
  
  if (index === -1) {
    throw new Error(`Hashtag dengan ID ${request.id} tidak ditemukan`);
  }
  
  // Check if updating name and if it already exists
  if (request.name && request.name !== hashtags[index].name) {
    const existingHashtag = hashtags.find(h => 
      h.id !== request.id && h.name.toLowerCase() === request.name.toLowerCase()
    );
    
    if (existingHashtag) {
      throw new Error(`Hashtag dengan nama ${request.name} sudah ada`);
    }
  }
  
  const updatedHashtag: HashtagEntry = {
    ...hashtags[index],
    name: request.name !== undefined ? (request.name.startsWith('#') ? request.name.substring(1) : request.name) : hashtags[index].name,
    category: request.category !== undefined ? request.category : hashtags[index].category,
    description: request.description !== undefined ? request.description : hashtags[index].description,
    isActive: request.isActive !== undefined ? request.isActive : hashtags[index].isActive,
    relatedHashtags: request.relatedHashtags !== undefined ? request.relatedHashtags : hashtags[index].relatedHashtags,
    performance: request.performance ? {
      ...hashtags[index].performance,
      ...request.performance,
      lastUpdated: new Date()
    } : hashtags[index].performance,
    updatedAt: new Date()
  };
  
  hashtags[index] = updatedHashtag;
  saveHashtags(hashtags);
  
  return updatedHashtag;
};

/**
 * Delete a hashtag
 */
export const deleteHashtag = async (id: string): Promise<void> => {
  const hashtags = loadHashtags();
  const updatedHashtags = hashtags.filter(h => h.id !== id);
  saveHashtags(updatedHashtags);
};

/**
 * Search hashtags
 */
export const searchHashtags = async (request: HashtagSearchRequest): Promise<HashtagSearchResponse> => {
  const hashtags = loadHashtags();
  let filtered = [...hashtags];
  
  // Apply filters
  if (request.searchTerm) {
    const searchTerm = request.searchTerm.toLowerCase();
    filtered = filtered.filter(h => 
      h.name.toLowerCase().includes(searchTerm) ||
      (h.description && h.description.toLowerCase().includes(searchTerm))
    );
  }
  
  if (request.category) {
    filtered = filtered.filter(h => h.category === request.category);
  }
  
  if (request.recommendationLevel) {
    filtered = filtered.filter(h => 
      h.performance && h.performance.recommendation === request.recommendationLevel
    );
  }
  
  if (request.isActive !== undefined) {
    filtered = filtered.filter(h => h.isActive === request.isActive);
  }
  
  // Sort results
  if (request.sortBy) {
    filtered.sort((a, b) => {
      let aValue: any = a[request.sortBy as keyof HashtagEntry];
      let bValue: any = b[request.sortBy as keyof HashtagEntry];
      
      // Handle nested properties
      if (request.sortBy === 'popularity' || request.sortBy === 'relevance' || request.sortBy === 'competition') {
        aValue = a.performance ? a.performance[request.sortBy] : 0;
        bValue = b.performance ? b.performance[request.sortBy] : 0;
      }
      
      if (aValue === undefined) return 1;
      if (bValue === undefined) return -1;
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return request.sortDirection === 'asc' 
          ? aValue.localeCompare(bValue) 
          : bValue.localeCompare(aValue);
      }
      
      return request.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
    });
  }
  
  // Apply pagination
  const total = filtered.length;
  if (request.limit && request.offset !== undefined) {
    filtered = filtered.slice(request.offset, request.offset + request.limit);
  }
  
  return {
    hashtags: filtered,
    total
  };
};

/**
 * Analyze hashtags
 */
export const analyzeHashtags = async (hashtags: string[]): Promise<HashtagAnalysisResponse['results']> => {
  try {
    console.log('Analyzing hashtags with OpenRouter API');

    // Prepare system prompt
    const systemPrompt = `Anda adalah spesialis analisis hashtag Instagram yang ahli dalam industri ban dan otomotif. Anda akan menganalisis hashtag dan memberikan skor popularitas, relevansi, dan tingkat kompetisi, serta rekomendasi penggunaan.`;

    // Prepare user prompt
    const userPrompt = `Analisis hashtag berikut untuk industri ban dan berikan skor popularitas, relevansi, dan tingkat kompetisi (0-100), serta rekomendasi penggunaan (high/medium/low):

${hashtags.join(', ')}

Berikan respons dalam format JSON berikut:
{
  "results": [
    {
      "hashtag": "nama_hashtag",
      "popularity": 85,
      "relevance": 90,
      "competition": 70,
      "recommendation": "high"
    },
    ...
  ]
}`;

    // Make the API call to OpenRouter
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Hashtag Analysis'
      },
      body: JSON.stringify({
        model: DEFAULT_MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('API response:', data);

    // Extract the content from the response
    const content = data.choices[0].message.content;

    try {
      // Try to parse the JSON response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : content;
      const parsedData = JSON.parse(jsonString);

      // Update hashtags in storage with new analysis data
      updateHashtagsWithAnalysis(hashtags, parsedData.results);

      return parsedData.results;
    } catch (error) {
      console.error('Error parsing JSON response:', error);
      throw new Error('Invalid response format from API');
    }
  } catch (error) {
    console.error('Error analyzing hashtags:', error);
    
    // Return fallback analysis
    const fallbackResults = hashtags.map(hashtag => ({
      hashtag,
      popularity: Math.floor(Math.random() * 100),
      relevance: Math.floor(Math.random() * 100),
      competition: Math.floor(Math.random() * 100),
      recommendation: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)] as 'high' | 'medium' | 'low'
    }));
    
    // Update hashtags in storage with fallback analysis
    updateHashtagsWithAnalysis(hashtags, fallbackResults);
    
    return fallbackResults;
  }
};

/**
 * Update hashtags with analysis results
 */
const updateHashtagsWithAnalysis = (hashtags: string[], results: HashtagAnalysisResponse['results']): void => {
  const allHashtags = loadHashtags();
  
  // Update existing hashtags with analysis results
  const updatedHashtags = allHashtags.map(hashtag => {
    const result = results.find(r => r.hashtag === hashtag.name);
    if (result) {
      return {
        ...hashtag,
        performance: {
          popularity: result.popularity,
          relevance: result.relevance,
          competition: result.competition,
          recommendation: result.recommendation,
          usageCount: hashtag.performance?.usageCount || 0,
          averageEngagement: hashtag.performance?.averageEngagement || 0,
          lastUpdated: new Date()
        },
        updatedAt: new Date()
      };
    }
    return hashtag;
  });
  
  // Add new hashtags from analysis results that don't exist yet
  results.forEach(result => {
    const exists = updatedHashtags.some(h => h.name === result.hashtag);
    if (!exists) {
      updatedHashtags.push({
        id: `hashtag-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        name: result.hashtag,
        category: HashtagCategory.INDUSTRY, // Default category
        isActive: true,
        performance: {
          popularity: result.popularity,
          relevance: result.relevance,
          competition: result.competition,
          recommendation: result.recommendation,
          usageCount: 0,
          averageEngagement: 0,
          lastUpdated: new Date()
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
  });
  
  saveHashtags(updatedHashtags);
};

/**
 * Import hashtags from file
 */
export const importHashtags = async (file: File): Promise<HashtagEntry[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        let importedHashtags: HashtagEntry[] = [];
        
        if (file.name.endsWith('.json')) {
          // Parse JSON file
          const parsed = JSON.parse(content);
          importedHashtags = Array.isArray(parsed) ? parsed : parsed.hashtags || [];
        } else if (file.name.endsWith('.csv')) {
          // Parse CSV file
          const lines = content.split('\n');
          const headers = lines[0].split(',');
          
          const nameIndex = headers.findIndex(h => h.toLowerCase().includes('name') || h.toLowerCase().includes('hashtag'));
          const categoryIndex = headers.findIndex(h => h.toLowerCase().includes('category') || h.toLowerCase().includes('kategori'));
          const descriptionIndex = headers.findIndex(h => h.toLowerCase().includes('description') || h.toLowerCase().includes('deskripsi'));
          
          if (nameIndex === -1) {
            throw new Error('Format CSV tidak valid: kolom nama hashtag tidak ditemukan');
          }
          
          for (let i = 1; i < lines.length; i++) {
            const line = lines[i].trim();
            if (!line) continue;
            
            const values = line.split(',');
            const name = values[nameIndex].trim();
            if (!name) continue;
            
            const category = categoryIndex !== -1 && values[categoryIndex] 
              ? values[categoryIndex].trim() as HashtagCategory 
              : HashtagCategory.INDUSTRY;
              
            const description = descriptionIndex !== -1 ? values[descriptionIndex].trim() : undefined;
            
            importedHashtags.push({
              id: `hashtag-import-${Date.now()}-${i}`,
              name: name.startsWith('#') ? name.substring(1) : name,
              category,
              description,
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            });
          }
        } else {
          throw new Error('Format file tidak didukung. Gunakan JSON atau CSV.');
        }
        
        // Save imported hashtags
        const existingHashtags = loadHashtags();
        const newHashtags: HashtagEntry[] = [];
        
        importedHashtags.forEach(importedHashtag => {
          // Check if hashtag already exists
          const exists = existingHashtags.some(h => 
            h.name.toLowerCase() === importedHashtag.name.toLowerCase()
          );
          
          if (!exists) {
            newHashtags.push(importedHashtag);
          }
        });
        
        saveHashtags([...existingHashtags, ...newHashtags]);
        resolve(newHashtags);
      } catch (error) {
        console.error('Error importing hashtags:', error);
        reject(error);
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Gagal membaca file'));
    };
    
    if (file.name.endsWith('.json') || file.name.endsWith('.csv')) {
      reader.readAsText(file);
    } else {
      reject(new Error('Format file tidak didukung. Gunakan JSON atau CSV.'));
    }
  });
};

/**
 * Export hashtags to file
 */
export const exportHashtags = async (hashtags: HashtagEntry[]): Promise<void> => {
  const data = JSON.stringify(hashtags, null, 2);
  const blob = new Blob([data], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  const a = document.createElement('a');
  a.href = url;
  a.download = `hashtags-export-${new Date().toISOString().split('T')[0]}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

/**
 * Get sample hashtags for initial data
 */
const getSampleHashtags = (): HashtagEntry[] => {
  return [
    {
      id: 'hashtag-1',
      name: 'MichelinTires',
      category: HashtagCategory.BRAND,
      description: 'Hashtag resmi untuk produk ban Michelin',
      isActive: true,
      performance: {
        popularity: 85,
        relevance: 95,
        competition: 70,
        recommendation: 'high',
        usageCount: 120,
        averageEngagement: 4.2,
        lastUpdated: new Date('2024-01-15')
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: 'hashtag-2',
      name: 'BanTambang',
      category: HashtagCategory.INDUSTRY,
      description: 'Hashtag untuk konten terkait ban tambang',
      isActive: true,
      performance: {
        popularity: 65,
        relevance: 90,
        competition: 40,
        recommendation: 'high',
        usageCount: 85,
        averageEngagement: 3.8,
        lastUpdated: new Date('2024-01-15')
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: 'hashtag-3',
      name: 'ChitraParatama',
      category: HashtagCategory.BRAND,
      description: 'Hashtag resmi PT Chitra Paratama',
      isActive: true,
      performance: {
        popularity: 45,
        relevance: 100,
        competition: 10,
        recommendation: 'high',
        usageCount: 200,
        averageEngagement: 5.1,
        lastUpdated: new Date('2024-01-15')
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: 'hashtag-4',
      name: 'PerformaTangguh',
      category: HashtagCategory.CAMPAIGN,
      description: 'Hashtag untuk kampanye performa ban',
      isActive: true,
      performance: {
        popularity: 55,
        relevance: 85,
        competition: 30,
        recommendation: 'medium',
        usageCount: 65,
        averageEngagement: 3.5,
        lastUpdated: new Date('2024-01-15')
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: 'hashtag-5',
      name: 'BanPremium',
      category: HashtagCategory.PRODUCT,
      description: 'Hashtag untuk produk ban premium',
      isActive: true,
      performance: {
        popularity: 70,
        relevance: 80,
        competition: 60,
        recommendation: 'medium',
        usageCount: 95,
        averageEngagement: 3.2,
        lastUpdated: new Date('2024-01-15')
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: 'hashtag-6',
      name: 'MiningTires',
      category: HashtagCategory.INDUSTRY,
      description: 'Hashtag internasional untuk ban tambang',
      isActive: true,
      performance: {
        popularity: 75,
        relevance: 85,
        competition: 65,
        recommendation: 'high',
        usageCount: 110,
        averageEngagement: 3.9,
        lastUpdated: new Date('2024-01-15')
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: 'hashtag-7',
      name: 'TireSafety',
      category: HashtagCategory.SAFETY,
      description: 'Hashtag untuk konten keselamatan ban',
      isActive: true,
      performance: {
        popularity: 80,
        relevance: 75,
        competition: 55,
        recommendation: 'medium',
        usageCount: 75,
        averageEngagement: 4.0,
        lastUpdated: new Date('2024-01-15')
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    },
    {
      id: 'hashtag-8',
      name: 'EarthMoverTires',
      category: HashtagCategory.PRODUCT,
      description: 'Hashtag untuk ban alat berat',
      isActive: true,
      performance: {
        popularity: 60,
        relevance: 95,
        competition: 35,
        recommendation: 'high',
        usageCount: 55,
        averageEngagement: 4.5,
        lastUpdated: new Date('2024-01-15')
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-15')
    }
  ];
};
