import React, { useState, useEffect, useMemo } from 'react';
import { PromoItem } from '../types/promotion';
import { Product } from '../types';
import { fetchProducts } from '../services/productService';
import { getEffectivePrice } from '../services/customerPricingService';
import { Search, Plus, Minus, X, Tag } from 'lucide-react';

interface PromoItemSelectorProps {
  selectedItems: PromoItem[];
  onItemsChange: (items: PromoItem[]) => void;
  customerId?: string; // Added customerId for special pricing
}

const PromoItemSelector: React.FC<PromoItemSelectorProps> = ({ selectedItems, onItemsChange, customerId }) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Load products on component mount
  useEffect(() => {
    const loadProducts = async () => {
      setIsLoading(true);
      try {
        const loadedProducts = await fetchProducts();
        setProducts(loadedProducts);
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProducts();
  }, []);

  // Handle search input change with error prevention
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      e.preventDefault();
      e.stopPropagation();
      setSearchQuery(e.target.value);
      setIsDropdownOpen(true);
    } catch (err) {
      console.error('Error in search input change:', err);
    }
  };

  // Filter products based on search query with error handling
  const filteredProducts = useMemo(() => {
    try {
      if (!searchQuery || !searchQuery.trim()) {
        return [];
      }

      const lowerSearchTerm = searchQuery.toLowerCase();
      return products.filter(product => {
        try {
          return (
            ((product.materialDescription || '').toLowerCase()).includes(lowerSearchTerm) ||
            ((product.oldMaterialNo || '').toLowerCase()).includes(lowerSearchTerm)
          );
        } catch (err) {
          console.error('Error filtering product:', product, err);
          return false;
        }
      });
    } catch (err) {
      console.error('Error in filtering products:', err);
      return []; // Return empty array if there's an error
    }
  }, [searchQuery, products]);

  // Handle product selection
  const handleSelectProduct = (product: Product) => {
    // Check if product is already selected
    const existingIndex = selectedItems.findIndex(item => item.product.id === product.id);

    if (existingIndex === -1) {
      // Add new product with default values
      const defaultMargin = 20; // Default margin 20%

      // Get effective price (special price for customer or regular price)
      const cost = getEffectivePrice(product.id, product.price, customerId);

      const sellingPrice = cost * (1 + defaultMargin / 100); // Calculate selling price based on cost and margin
      const hasSpecialPrice = customerId && cost !== product.price;

      const newItem: PromoItem = {
        product: {
          id: product.id,
          oldMaterialNo: product.oldMaterialNo,
          materialDescription: product.materialDescription,
          cost: cost,
          margin: defaultMargin,
          sellingPrice: Math.round(sellingPrice),
          hasSpecialPrice: hasSpecialPrice,
          regularPrice: product.price
        },
        quantity: 1,
        stock: 100, // Default stock value
      };

      onItemsChange([...selectedItems, newItem]);
    }

    setSearchQuery('');
    setIsDropdownOpen(false);
  };

  // Handle quantity change
  const handleQuantityChange = (index: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    const updatedItems = [...selectedItems];
    updatedItems[index] = {
      ...updatedItems[index],
      quantity: newQuantity,
    };

    onItemsChange(updatedItems);
  };

  // Handle stock change
  const handleStockChange = (index: number, newStock: number) => {
    if (newStock < 0) return;

    const updatedItems = [...selectedItems];
    updatedItems[index] = {
      ...updatedItems[index],
      stock: newStock,
    };

    onItemsChange(updatedItems);
  };

  // Handle margin change
  const handleMarginChange = (index: number, newMargin: number) => {
    if (newMargin < 0) return;

    const updatedItems = [...selectedItems];
    const cost = updatedItems[index].product.cost;
    const newSellingPrice = cost * (1 + newMargin / 100);

    updatedItems[index] = {
      ...updatedItems[index],
      product: {
        ...updatedItems[index].product,
        margin: newMargin,
        sellingPrice: Math.round(newSellingPrice),
      },
    };

    onItemsChange(updatedItems);
  };

  // Handle removing a product
  const handleRemoveProduct = (index: number) => {
    const updatedItems = selectedItems.filter((_, i) => i !== index);
    onItemsChange(updatedItems);
  };

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">Data Produk yang Dipromosikan</h2>

      {/* Product Search */}
      <form
        className="relative"
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          return false;
        }}
      >
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Cari produk ban..."
            value={searchQuery}
            onChange={handleSearchChange}
            onFocus={(e) => {
              e.preventDefault();
              setIsDropdownOpen(true);
            }}
            onBlur={(e) => {
              e.preventDefault();
              setTimeout(() => setIsDropdownOpen(false), 200);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                e.stopPropagation();
              }
            }}
            className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 no-navigation"
          />
          {searchQuery && (
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setSearchQuery('');
              }}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <X className="h-5 w-5 text-gray-400 hover:text-gray-600" />
            </button>
          )}
        </div>

        {isDropdownOpen && filteredProducts.length > 0 && (
          <div className="absolute z-10 mt-1 w-full bg-white border rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {filteredProducts.map(product => (
              <div
                key={product.id}
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleSelectProduct(product);
                }}
              >
                <div className="font-medium">{product.materialDescription || 'No Name'}</div>
                <div className="text-sm text-gray-600">{product.description || 'No Description'}</div>
                <div className="text-sm text-gray-500">
                  Code: {product.oldMaterialNo || 'N/A'} •
                  Price: IDR {(product.price || 0).toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </form>

      {/* Selected Products */}
      <div className="mt-4">
        <h3 className="text-md font-medium mb-2">Produk Terpilih</h3>

        {selectedItems.length === 0 ? (
          <div className="text-gray-500 italic">Belum ada produk yang dipilih</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produk</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Harga Pokok</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Margin (%)</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Harga Jual</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jumlah</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aksi</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {selectedItems.map((item, index) => (
                  <tr key={`${item.product.id}-${index}`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{item.product.materialDescription}</div>
                      <div className="text-xs text-gray-500">{item.product.oldMaterialNo}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">Rp {item.product.cost.toLocaleString()}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="number"
                        value={item.product.margin}
                        onChange={(e) => handleMarginChange(index, Number(e.target.value))}
                        className="w-20 px-2 py-1 text-sm border border-gray-300 rounded-md"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm text-gray-900">Rp {item.product.sellingPrice.toLocaleString()}</div>
                        {item.product.hasSpecialPrice && (
                          <div className="ml-2 flex items-center text-xs text-green-600">
                            <Tag size={12} className="mr-1" />
                            <span>Harga Khusus</span>
                          </div>
                        )}
                      </div>
                      {item.product.hasSpecialPrice && item.product.regularPrice && (
                        <div className="text-xs text-gray-500 line-through">
                          Rp {item.product.regularPrice.toLocaleString()}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => handleQuantityChange(index, item.quantity - 1)}
                          className="p-1 text-gray-500 hover:text-gray-700 border rounded"
                        >
                          <Minus size={14} />
                        </button>
                        <input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => handleQuantityChange(index, Number(e.target.value))}
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded-md text-center"
                        />
                        <button
                          onClick={() => handleQuantityChange(index, item.quantity + 1)}
                          className="p-1 text-gray-500 hover:text-gray-700 border rounded"
                        >
                          <Plus size={14} />
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleRemoveProduct(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <X size={18} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default PromoItemSelector;
