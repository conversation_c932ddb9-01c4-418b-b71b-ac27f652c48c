// Direct Runware API test script
// Run with: node directRunwareTest.js

const fetch = require('node-fetch');
const { v4: uuidv4 } = require('uuid');

// Runware API key
const RUNWARE_API_KEY = 'AYKdFJicrfah3pHCIJFbskrBndRmlhCM';

// Runware API endpoint
const RUNWARE_API_URL = 'https://api.runware.ai/v1';

async function testRunwareAPI() {
  try {
    console.log('Starting Runware API test...');

    // Generate a unique task UUID
    const taskUUID = uuidv4();

    // Create the request body
    const requestBody = [
      {
        taskType: 'imageInference',
        taskUUID,
        positivePrompt: 'a cat',
        width: 512,
        height: 512,
        model: 'civitai:56519@60938', // Using the anime model which is faster
        numberResults: 1
      }
    ];

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    // Call the Runware API
    console.log('Calling Runware API...');
    const response = await fetch(RUNWARE_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RUNWARE_API_KEY}`
      },
      body: JSON.stringify(requestBody)
    });

    // Check if the response is OK
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API error response:', errorText);
      throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    // Parse the response
    const responseData = await response.json();
    console.log('Runware API response:', JSON.stringify(responseData, null, 2));

    // Extract the generated images from the response
    if (responseData && responseData.data && Array.isArray(responseData.data)) {
      for (const item of responseData.data) {
        if (item.taskType === 'imageInference' && item.imageURL) {
          console.log('Generated image URL:', item.imageURL);
        }
      }
    } else {
      console.warn('No images found in the response');
    }
  } catch (error) {
    console.error('Error testing Runware API:', error);
  }
}

// Run the test
testRunwareAPI();
