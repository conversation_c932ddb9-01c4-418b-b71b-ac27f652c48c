import { v4 as uuidv4 } from 'uuid';
import { InsightCategory, InsightType, MarketingInsight } from '../pages/MarketingInsightsHub';
import { performWebSearch } from './openRouterSearchService';

// Local storage key
const INSIGHTS_STORAGE_KEY = 'chitraMarketingTools_marketingInsights';

// OpenRouter API key
const OPENROUTER_API_KEY = '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';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Models
const MODELS = {
  DEEPSEEK: 'openai/gpt-4.1-nano',
  CLAUDE_OPUS: 'openai/gpt-4.1-nano',
  CLAUDE_SONNET: 'openai/gpt-4.1-nano',
  CLAUDE_HAIKU: 'openai/gpt-4.1-nano',
  MISTRAL_LARGE: 'openai/gpt-4.1-nano',
  MISTRAL_SMALL: 'openai/gpt-4.1-nano',
  OPENCHAT: 'openai/gpt-4.1-nano',
  GPT35: 'openai/gpt-4.1-nano',
  GPT4: 'openai/gpt-4.1-nano',
};

// Default model for marketing insights
const DEFAULT_MODEL = MODELS.DEEPSEEK; // Using GPT-4.1-nano for better quality insights

// Interface for AI generation request
interface GenerationRequest {
  prompt: string;
  type: InsightType;
  category: InsightCategory;
  model?: string;
}

/**
 * Get all marketing insights
 */
export const getMarketingInsights = async (): Promise<MarketingInsight[]> => {
  try {
    const storedData = localStorage.getItem(INSIGHTS_STORAGE_KEY);
    if (!storedData) {
      // Return sample data if no data exists
      const sampleData = getSampleInsights();
      await saveInsightsToStorage(sampleData);
      return sampleData;
    }

    const parsedData = JSON.parse(storedData);

    // Convert string dates to Date objects
    return parsedData.map((insight: any) => ({
      ...insight,
      createdAt: new Date(insight.createdAt),
      updatedAt: new Date(insight.updatedAt)
    }));
  } catch (error) {
    console.error('Error getting marketing insights:', error);
    return [];
  }
};

/**
 * Save a marketing insight
 */
export const saveMarketingInsight = async (insight: MarketingInsight): Promise<MarketingInsight> => {
  try {
    const insights = await getMarketingInsights();

    let updatedInsight: MarketingInsight;

    if ('id' in insight) {
      // Update existing insight
      updatedInsight = {
        ...insight,
        updatedAt: new Date()
      };

      const index = insights.findIndex(i => i.id === insight.id);
      if (index !== -1) {
        insights[index] = updatedInsight;
      } else {
        throw new Error('Insight not found');
      }
    } else {
      // Create new insight
      updatedInsight = {
        ...insight,
        id: uuidv4(),
        createdAt: new Date(),
        updatedAt: new Date(),
        isFavorite: false
      } as MarketingInsight;

      insights.push(updatedInsight);
    }

    await saveInsightsToStorage(insights);
    return updatedInsight;
  } catch (error) {
    console.error('Error saving marketing insight:', error);
    throw error;
  }
};

/**
 * Delete a marketing insight
 */
export const deleteMarketingInsight = async (id: string): Promise<void> => {
  try {
    const insights = await getMarketingInsights();
    const filteredInsights = insights.filter(insight => insight.id !== id);
    await saveInsightsToStorage(filteredInsights);
  } catch (error) {
    console.error('Error deleting marketing insight:', error);
    throw error;
  }
};

/**
 * Generate a marketing insight using AI with real-time web search
 */
export const generateMarketingInsight = async (request: GenerationRequest): Promise<Partial<MarketingInsight>> => {
  try {
    const { prompt, type, category, model = DEFAULT_MODEL } = request;

    console.log('Generating marketing insight with web search for:', prompt);

    // First, perform a web search to get real-time information
    const searchResult = await performWebSearch({
      query: `${category} ${type} ${prompt} marketing ban tambang`,
      maxResults: 5
    });

    console.log('Web search completed with sources:', searchResult.sources.length);

    // Create system prompt based on the type and category
    const systemPrompt = getSystemPromptForInsight(type, category);

    // Create user prompt with search results
    const userPrompt = `${prompt}

Gunakan informasi terbaru berikut dari pencarian web untuk membuat insight yang akurat dan terkini:

RINGKASAN PENCARIAN:
${searchResult.summary}

SUMBER-SUMBER (${searchResult.sources.length}):
${searchResult.sources.map((source, index) =>
  `${index + 1}. ${source.title} - ${source.url}${source.publisher ? `\n   Penerbit: ${source.publisher}` : ''}${source.snippet ? `\n   Snippet: ${source.snippet}` : ''}`
).join('\n\n')}

Berikan respons dalam format JSON dengan struktur berikut:
{
  "title": "Judul yang menarik dan informatif",
  "content": "Konten lengkap dengan format yang baik, termasuk data, contoh kasus, dan referensi. Sertakan link ke sumber asli dalam format markdown.",
  "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
  "sources": [
    {"title": "Judul Sumber 1", "url": "URL lengkap sumber 1", "publisher": "Nama penerbit/website"},
    {"title": "Judul Sumber 2", "url": "URL lengkap sumber 2", "publisher": "Nama penerbit/website"},
    {"title": "Judul Sumber 3", "url": "URL lengkap sumber 3", "publisher": "Nama penerbit/website"}
  ]
}

SANGAT PENTING:
1. Gunakan HANYA URL yang valid dan dapat diakses dari sumber-sumber yang diberikan di atas
2. Jangan pernah membuat URL yang tidak ada atau tidak valid
3. Pastikan setiap URL dimulai dengan https:// dan mengarah ke halaman spesifik
4. Lebih baik memberikan lebih sedikit sumber yang valid daripada banyak sumber yang tidak valid
5. Sertakan nama penerbit/website untuk setiap sumber

Pastikan konten memiliki format yang baik dengan paragraf, bullet points, dan penekanan yang sesuai.
Sertakan data faktual, statistik, dan contoh kasus nyata untuk mendukung poin-poin Anda.
Di bagian akhir konten, tambahkan bagian "Sumber:" yang mencantumkan semua referensi yang digunakan.
Berikan minimal 5 tags yang relevan dengan konten.`;

    // Make the API call to OpenRouter
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Marketing Insights'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 2000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content;

    // Parse the JSON response
    const parsedContent = JSON.parse(content);

    // Validate URL function
    const isValidUrl = (urlString: string): boolean => {
      try {
        const url = new URL(urlString);
        return url.protocol === 'https:' || url.protocol === 'http:';
      } catch (e) {
        return false;
      }
    };

    // Format sources as markdown links with publisher info if available
    let formattedSources;
    if (Array.isArray(parsedContent.sources)) {
      // Filter out invalid URLs
      const validSources = parsedContent.sources.filter(source =>
        source.url && isValidUrl(source.url) && source.title
      );

      if (validSources.length > 0) {
        formattedSources = validSources.map(source => {
          const publisherInfo = source.publisher ? ` - ${source.publisher}` : '';
          return `[${source.title}](${source.url})${publisherInfo}`;
        }).join('\n\n');
      } else {
        formattedSources = 'AI Generated dengan sumber kredibel';
      }
    } else {
      // Fallback for string format
      formattedSources = parsedContent.sources || 'AI Generated dengan sumber kredibel';
    }

    // Add search sources if not included in the response or if we have fewer than 2 valid sources
    const validSourcesCount = Array.isArray(parsedContent.sources)
      ? parsedContent.sources.filter(source => source.url && isValidUrl(source.url)).length
      : 0;

    if (validSourcesCount < 2 && searchResult.sources.length > 0) {
      // Filter search results to only include valid URLs
      const validSearchSources = searchResult.sources.filter(source =>
        source.url && isValidUrl(source.url) && source.title
      );

      if (validSearchSources.length > 0) {
        formattedSources += '\n\n### Sumber tambahan terverifikasi:\n' +
          validSearchSources.slice(0, 3).map(source => {
            const publisherInfo = source.publisher ? ` - ${source.publisher}` : '';
            return `[${source.title}](${source.url})${publisherInfo}`;
          }).join('\n\n');
      }
    }

    // Always add at least one verified source from our search results if we don't have any valid sources
    if ((formattedSources === 'AI Generated dengan sumber kredibel' || !formattedSources) && searchResult.sources.length > 0) {
      // Filter search results to only include valid URLs
      const validSearchSources = searchResult.sources.filter(source =>
        source.url && isValidUrl(source.url) && source.title
      );

      if (validSearchSources.length > 0) {
        formattedSources = '### Sumber terverifikasi:\n' +
          validSearchSources.map(source => {
            const publisherInfo = source.publisher ? ` - ${source.publisher}` : '';
            return `[${source.title}](${source.url})${publisherInfo}`;
          }).join('\n\n');
      }
    }

    return {
      title: parsedContent.title,
      content: parsedContent.content,
      tags: parsedContent.tags || [],
      category,
      type,
      source: formattedSources
    };
  } catch (error) {
    console.error('Error generating marketing insight:', error);
    throw error;
  }
};

/**
 * Get system prompt for insight generation based on type and category
 */
const getSystemPromptForInsight = (type: InsightType, category: InsightCategory): string => {
  let basePrompt = 'Bertindaklah Seperti Copywriter direct respond kelas dunia dengan pengalaman lebih dari 20 tahun dalam membuat kampanye penjualan dengan konversi tinggi di bidang Distributor ban tambang, Tire Repair, Tire mangement, dan teknologi Tire di Indonesia. ';

  // Add type-specific instructions
  switch (type) {
    case InsightType.TIP:
      basePrompt += 'Tugas Anda adalah memberikan tips marketing yang praktis, actionable, dan efektif. Tips harus konkret, spesifik, dan dapat langsung diterapkan oleh tim marketing. ';
      break;
    case InsightType.STORY:
      basePrompt += 'Tugas Anda adalah menceritakan kisah inspiratif tentang strategi marketing yang sukses. Cerita harus memiliki alur yang jelas, mengandung pelajaran berharga, dan relevan dengan industri ban. Gunakan contoh nyata dari perusahaan ban terkemuka atau industri terkait. ';
      break;
    case InsightType.ANALYSIS:
      basePrompt += 'Tugas Anda adalah memberikan analisis mendalam tentang aspek marketing tertentu. Analisis harus didukung data, memiliki struktur yang jelas, dan memberikan wawasan yang berharga. Sertakan statistik, tren pasar, dan data kuantitatif yang relevan. ';
      break;
    case InsightType.FORECAST:
      basePrompt += 'Tugas Anda adalah memberikan prediksi atau forecast tentang tren marketing di masa depan. Forecast harus berdasarkan tren saat ini, didukung oleh data dan penelitian terkini, dan memberikan rekomendasi strategis. Sertakan proyeksi angka dan estimasi yang masuk akal. ';
      break;
    default:
      basePrompt += 'Tugas Anda adalah memberikan informasi marketing yang berharga dan relevan. ';
  }

  // Add category-specific instructions
  switch (category) {
    case InsightCategory.STRATEGY:
      basePrompt += 'Fokus pada strategi marketing secara keseluruhan, termasuk positioning, targeting, dan perencanaan kampanye. ';
      break;
    case InsightCategory.CASE_STUDY:
      basePrompt += 'Fokus pada studi kasus nyata atau hipotesis yang menunjukkan penerapan strategi marketing dalam situasi spesifik. Gunakan contoh perusahaan nyata dan hasil yang terukur. ';
      break;
    case InsightCategory.TREND:
      basePrompt += 'Fokus pada tren terkini dalam marketing ban, termasuk perubahan perilaku konsumen, teknologi baru, dan inovasi dalam industri. Sertakan data dari laporan industri terbaru dan sumber terpercaya. ';
      break;
    case InsightCategory.COMPETITOR:
      basePrompt += 'Fokus pada analisis kompetitor, termasuk strategi yang digunakan pesaing, kekuatan dan kelemahan mereka, serta cara merespons persaingan. Gunakan contoh dari kompetitor utama seperti Michelin, Bridgestone, Goodyear, dll. ';
      break;
    case InsightCategory.CUSTOMER:
      basePrompt += 'Fokus pada pemahaman pelanggan, termasuk kebutuhan, preferensi, perilaku pembelian, dan cara membangun hubungan jangka panjang. Sertakan data demografis dan psikografis yang relevan. ';
      break;
    case InsightCategory.PRODUCT:
      basePrompt += 'Fokus pada strategi produk, termasuk pengembangan, diferensiasi, positioning, dan cara mengkomunikasikan keunggulan produk. Gunakan contoh spesifikasi produk dan fitur unggulan. ';
      break;
    case InsightCategory.PRICING:
      basePrompt += 'Fokus pada strategi harga, termasuk penetapan harga, diskon, bundling, dan cara mengkomunikasikan nilai produk. Sertakan contoh struktur harga dan analisis margin. ';
      break;
    case InsightCategory.PROMOTION:
      basePrompt += 'Fokus pada strategi promosi, termasuk iklan, promosi penjualan, public relations, dan pemasaran digital. Berikan contoh kampanye sukses dan metrik keberhasilan. ';
      break;
    default:
      basePrompt += 'Fokus pada aspek marketing yang paling relevan dengan permintaan pengguna. ';
  }

  basePrompt += 'Berikan konten yang informatif, praktis, dan dapat langsung diterapkan oleh tim marketing ban. Gunakan bahasa yang jelas, profesional, dan persuasif. ';

  // Add requirements for data, stories, and credible sources
  basePrompt += 'PENTING: Selalu sertakan data faktual, statistik, dan angka yang spesifik untuk mendukung poin-poin Anda. Gunakan cerita atau contoh kasus nyata untuk mengilustrasikan konsep. ';
  basePrompt += 'Yang paling penting, selalu sertakan sumber yang kredibel untuk informasi Anda, seperti: ';
  basePrompt += '1) Berita dari sumber terpercaya (Bloomberg, Reuters, CNBC, dll) ';
  basePrompt += '2) Data dari Badan Pusat Statistik (BPS) atau badan statistik resmi lainnya ';
  basePrompt += '3) Laporan industri dari lembaga riset terkemuka (McKinsey, Deloitte, Frost & Sullivan, dll) ';
  basePrompt += '4) Jurnal akademik dan publikasi ilmiah ';
  basePrompt += '5) Buku dan artikel dari pakar industri terkemuka ';
  basePrompt += '6) Website resmi perusahaan ban terkemuka ';
  basePrompt += 'Cantumkan sumber-sumber ini dengan jelas di bagian akhir konten dalam format yang tepat (nama sumber, judul, tahun, URL jika ada).';

  return basePrompt;
};

/**
 * Save insights to local storage
 */
const saveInsightsToStorage = async (insights: MarketingInsight[]): Promise<void> => {
  try {
    localStorage.setItem(INSIGHTS_STORAGE_KEY, JSON.stringify(insights));
  } catch (error) {
    console.error('Error saving insights to storage:', error);
    throw error;
  }
};

/**
 * Get sample insights for initial data
 */
const getSampleInsights = (): MarketingInsight[] => {
  const now = new Date();

  return [
    {
      id: uuidv4(),
      title: 'Strategi Nilai Jangka Panjang untuk Ban Tambang',
      content: `Dalam penjualan ban tambang, fokus pada nilai jangka panjang adalah kunci untuk memenangkan klien besar. Berikut adalah strategi efektif untuk mengkomunikasikan nilai jangka panjang:

1. **Kalkulasi Total Cost of Ownership (TCO)**
   - Buat kalkulator TCO yang membandingkan biaya awal vs. biaya siklus hidup
   - Tunjukkan penghematan bahan bakar dari ban premium
   - Hitung pengurangan downtime kendaraan

2. **Dokumentasikan Kasus Nyata**
   - Kumpulkan studi kasus dari pelanggan yang telah mengalami penghematan signifikan
   - Sajikan data umur pakai ban yang lebih panjang dengan grafik perbandingan
   - Gunakan testimonial dari fleet manager terkemuka

3. **Program Garansi Komprehensif**
   - Tawarkan garansi kilometer atau jam operasi
   - Sertakan layanan pemeriksaan rutin dalam paket
   - Berikan jaminan penggantian untuk kasus tertentu

4. **Edukasi tentang Teknologi Ban**
   - Jelaskan teknologi khusus yang memperpanjang umur ban
   - Demonstrasikan ketahanan terhadap kerusakan dan panas
   - Tunjukkan bagaimana desain tread mempengaruhi produktivitas

Ingat: Pelanggan tambang lebih mementingkan keandalan dan produktivitas daripada harga awal. Komunikasikan bagaimana ban premium meningkatkan produktivitas dan mengurangi biaya per ton material yang ditambang.`,
      category: InsightCategory.STRATEGY,
      type: InsightType.TIP,
      tags: ['nilai jangka panjang', 'TCO', 'ban tambang', 'penjualan premium', 'garansi'],
      createdAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      updatedAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      isFavorite: true
    },
    {
      id: uuidv4(),
      title: 'Kisah Sukses: Bagaimana Michelin Memenangkan Kontrak Tambang Terbesar di Indonesia',
      content: `Pada tahun 2019, salah satu tambang batubara terbesar di Kalimantan Timur menghadapi masalah serius dengan ban kendaraan tambang mereka. Ban dari supplier sebelumnya hanya bertahan rata-rata 3.500 jam operasi, jauh di bawah standar industri, menyebabkan downtime yang signifikan dan biaya operasional tinggi.

Tim penjualan Michelin, dipimpin oleh Budi Santoso, mengambil pendekatan berbeda dari kompetitor. Alih-alih fokus pada penawaran harga yang lebih rendah, tim Budi melakukan hal-hal berikut:

1. **Analisis Operasional Mendalam**
   Tim menghabiskan dua minggu di lokasi tambang, mempelajari kondisi jalan, pola penggunaan, dan mengidentifikasi faktor-faktor yang menyebabkan keausan ban prematur.

2. **Solusi Kustom**
   Berdasarkan analisis, mereka merekomendasikan ban XDR2 dengan komposisi karet khusus untuk kondisi tambang tersebut, dilengkapi dengan sistem pemantauan tekanan ban MEMS.

3. **Uji Coba Terbatas**
   Alih-alih meminta kontrak penuh, mereka menawarkan uji coba 10 ban selama 3 bulan dengan jaminan pengembalian dana jika performa tidak sesuai ekspektasi.

4. **Pelatihan Operator**
   Selama uji coba, tim Michelin memberikan pelatihan kepada operator dan tim pemeliharaan tentang praktik terbaik penggunaan dan perawatan ban.

Hasilnya? Ban Michelin mencapai umur pakai rata-rata 6.800 jam - hampir dua kali lipat dari supplier sebelumnya. Meskipun harga per ban 30% lebih tinggi, TCO turun 22% berkat umur pakai yang lebih panjang dan downtime yang berkurang.

Tambang tersebut menandatangani kontrak eksklusif 3 tahun senilai Rp 45 milyar, dan Budi menerima penghargaan "Sales Excellence Award" dari Michelin Asia Pasifik.

**Pelajaran utama:** Jangan bersaing dalam hal harga, tetapi dalam hal nilai. Investasikan waktu untuk memahami masalah pelanggan secara mendalam dan tawarkan solusi komprehensif, bukan sekadar produk.`,
      category: InsightCategory.CASE_STUDY,
      type: InsightType.STORY,
      tags: ['michelin', 'studi kasus', 'tambang batubara', 'ban tambang', 'nilai vs harga'],
      createdAt: new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000), // 14 days ago
      updatedAt: new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000),
      isFavorite: false
    },
    {
      id: uuidv4(),
      title: 'Analisis Tren Pembelian Ban Tambang 2025',
      content: `## Tren Utama dalam Pembelian Ban Tambang 2025

Berdasarkan analisis data industri dan wawancara dengan pelaku pasar, berikut adalah tren utama yang akan mempengaruhi pembelian ban tambang di tahun 2025:

### 1. Peningkatan Fokus pada Keberlanjutan
- 78% perusahaan tambang besar kini memiliki target pengurangan emisi karbon
- Permintaan ban dengan resistensi gulir rendah meningkat 34% YoY
- Ban dengan bahan daur ulang menjadi preferensi untuk tender pemerintah

### 2. Digitalisasi Manajemen Ban
- Adopsi sistem pemantauan ban real-time meningkat 56% dalam 2 tahun terakhir
- 62% fleet manager menginginkan integrasi data ban dengan sistem manajemen armada
- Prediktif maintenance menjadi fitur yang paling dicari

### 3. Pergeseran Model Pembelian
- Kontrak berbasis performa (Rp/jam atau Rp/ton) naik 45%
- Model sewa-beli (leasing) ban premium tumbuh 28%
- Bundling ban dengan layanan pemeliharaan menjadi standar baru

### 4. Konsolidasi Supplier
- 70% perusahaan tambang mengurangi jumlah supplier ban
- Preferensi untuk supplier yang dapat menyediakan solusi end-to-end
- Peningkatan loyalitas merek untuk mengurangi kompleksitas inventaris

### 5. Lokalisasi Produksi
- Preferensi untuk ban yang diproduksi lokal meningkat 23%
- Ketersediaan stok dan waktu pengiriman menjadi faktor keputusan utama
- Fasilitas vulkanisasi lokal menjadi nilai tambah signifikan

**Implikasi untuk Strategi Penjualan:**
- Kembangkan proposisi nilai yang mencakup aspek keberlanjutan
- Investasikan dalam solusi digital dan analitik
- Tawarkan model bisnis fleksibel, tidak hanya penjualan tradisional
- Bangun hubungan strategis jangka panjang, bukan transaksional
- Tingkatkan ketersediaan lokal dan layanan purna jual`,
      category: InsightCategory.TREND,
      type: InsightType.ANALYSIS,
      tags: ['tren 2025', 'ban tambang', 'keberlanjutan', 'digitalisasi', 'model bisnis'],
      createdAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      updatedAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
      isFavorite: true
    },
    {
      id: uuidv4(),
      title: 'Strategi Menghadapi Kompetitor dengan Harga Rendah',
      content: `# Strategi Menghadapi Kompetitor Harga Rendah

Persaingan dengan produsen ban berbiaya rendah semakin intensif di sektor tambang. Berikut strategi efektif untuk mempertahankan dan meningkatkan pangsa pasar:

## 1. Diferensiasi yang Jelas dan Terukur
- **Dokumentasikan performa superior** dengan data spesifik (jam operasi, ketahanan terhadap kerusakan, dll)
- **Buat perbandingan TCO** yang menunjukkan biaya per jam operasi, bukan hanya harga pembelian
- **Visualisasikan perbedaan kualitas** dengan sampel potongan ban, demonstrasi material, atau video pengujian

## 2. Segmentasi Pelanggan yang Tepat
- **Identifikasi pelanggan yang memprioritaskan nilai** daripada harga awal
- **Fokus pada tambang dengan kondisi operasi berat** di mana ban berkualitas rendah cepat rusak
- **Target decision maker level tinggi** (Direktur Operasi, bukan hanya Purchasing)

## 3. Bundling Strategis
- **Tawarkan paket ban + layanan** yang sulit dibandingkan langsung dengan kompetitor
- **Sertakan teknologi pemantauan** yang tidak dimiliki kompetitor harga rendah
- **Bundling dengan produk komplementer** (wheel loader, rim protection, dll)

## 4. Pendekatan Konsultatif
- **Posisikan tim penjualan sebagai konsultan** produktivitas tambang
- **Lakukan audit operasional** untuk mengidentifikasi area peningkatan
- **Berikan rekomendasi yang melampaui produk ban** (desain jalan tambang, praktik loading, dll)

## 5. Program Loyalitas Bernilai Tinggi
- **Buat program buy-back** untuk ban bekas
- **Tawarkan pelatihan operator** yang meningkatkan umur pakai ban
- **Berikan akses ke data industri eksklusif** dan benchmark performa

Ingat: Jangan terjebak dalam perang harga. Alihkan percakapan dari "Berapa harganya?" menjadi "Berapa nilai yang Anda dapatkan?"`,
      category: InsightCategory.COMPETITOR,
      type: InsightType.TIP,
      tags: ['kompetitor', 'harga rendah', 'diferensiasi', 'nilai vs harga', 'strategi penjualan'],
      createdAt: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
      updatedAt: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000),
      isFavorite: false
    }
  ];
};
