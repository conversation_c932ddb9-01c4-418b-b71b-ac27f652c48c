<template>
    <AppLayout>
        <div class="space-y-6">
            <div class="border-b border-gray-200 pb-4">
                <h1 class="text-2xl font-bold text-gray-900">27.00 R 49 Bundling Calculator</h1>
                <p class="mt-2 text-gray-600">Calculate optimal bundling strategies for 27.00 R 49 tire products.</p>
            </div>

            <!-- Main Product Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center mb-4">
                    <Truck class="h-5 w-5 text-blue-600 mr-2" />
                    <h2 class="text-lg font-semibold text-gray-900">Main Product</h2>
                </div>

                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="font-medium text-blue-900">{{ mainProduct.name }}</h3>
                            <p class="text-sm text-blue-700">{{ mainProduct.code }}</p>
                            <p class="text-xs text-blue-600 mt-1">{{ mainProduct.description }}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-blue-900">{{ formatCurrency(mainProduct.price) }}</p>
                            <p class="text-xs text-blue-600">per unit</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Calculator Form -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center mb-4">
                    <Calculator class="h-5 w-5 text-green-600 mr-2" />
                    <h2 class="text-lg font-semibold text-gray-900">Bundling Configuration</h2>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Secondary Product Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Secondary Product
                        </label>
                        <select
                            v-model="selectedSecondaryProduct"
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="">Select secondary product...</option>
                            <option
                                v-for="product in secondaryProducts"
                                :key="product.id"
                                :value="product"
                            >
                                {{ product.name }} - {{ formatCurrency(product.price) }}
                            </option>
                        </select>
                    </div>

                    <!-- Secondary Product Quantity -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Secondary Product Quantity
                        </label>
                        <input
                            v-model.number="secondaryProductQty"
                            type="number"
                            min="1"
                            max="10"
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>

                    <!-- Main Product Margin -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Main Product Margin (%)
                        </label>
                        <input
                            v-model.number="mainProductMargin"
                            type="number"
                            min="0"
                            max="100"
                            step="0.1"
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>

                    <!-- Secondary Product Margin -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Secondary Product Margin (%)
                        </label>
                        <input
                            v-model.number="secondaryProductMargin"
                            type="number"
                            min="0"
                            max="100"
                            step="0.1"
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>
                </div>

                <!-- Calculate Button -->
                <div class="mt-6">
                    <button
                        @click="calculateBundling"
                        :disabled="!selectedSecondaryProduct || isCalculating"
                        class="w-full py-3 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <Loader2 v-if="isCalculating" class="h-5 w-5 mr-2 animate-spin" />
                        <Calculator v-else class="h-5 w-5 mr-2" />
                        {{ isCalculating ? 'Calculating...' : 'Calculate Bundling' }}
                    </button>
                </div>
            </div>

            <!-- Calculation Results -->
            <div v-if="calculationResult" class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center mb-4">
                    <BarChart3 class="h-5 w-5 text-purple-600 mr-2" />
                    <h2 class="text-lg font-semibold text-gray-900">Calculation Results</h2>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <!-- Minimum Main Product Quantity -->
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-blue-800">Minimum Main Product Qty</h3>
                        <p class="text-2xl font-bold text-blue-900">{{ calculationResult.minimumMainProductQty }}</p>
                        <p class="text-xs text-blue-700 mt-1">units required</p>
                    </div>

                    <!-- Optimal Sell Price -->
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-green-800">Optimal Sell Price</h3>
                        <p class="text-2xl font-bold text-green-900">{{ formatCurrency(calculationResult.optimalSellPrice) }}</p>
                        <p class="text-xs text-green-700 mt-1">recommended price</p>
                    </div>

                    <!-- Profit Margin -->
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-purple-800">Profit Margin</h3>
                        <p class="text-2xl font-bold text-purple-900">{{ calculationResult.profitMargin.toFixed(2) }}%</p>
                        <p class="text-xs text-purple-700 mt-1">expected margin</p>
                    </div>
                </div>

                <!-- Price Range -->
                <div class="bg-gray-50 p-4 rounded-lg mb-4">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">Price Range</h3>
                    <div class="flex justify-between items-center">
                        <div class="text-center">
                            <p class="text-sm text-gray-600">Minimum</p>
                            <p class="font-semibold text-gray-900">{{ formatCurrency(calculationResult.minimumSellPrice) }}</p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-gray-600">Optimal</p>
                            <p class="font-semibold text-green-600">{{ formatCurrency(calculationResult.optimalSellPrice) }}</p>
                        </div>
                        <div class="text-center">
                            <p class="text-sm text-gray-600">Maximum</p>
                            <p class="font-semibold text-gray-900">{{ formatCurrency(calculationResult.maximumSellPrice) }}</p>
                        </div>
                    </div>
                </div>

                <!-- Bundling Summary -->
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-yellow-800 mb-2">Bundling Summary</h3>
                    <div class="text-sm text-yellow-700">
                        <p><strong>{{ calculationResult.minimumMainProductQty }}</strong> units of {{ mainProduct.name }}</p>
                        <p><strong>{{ secondaryProductQty }}</strong> units of {{ selectedSecondaryProduct?.name }} (FREE)</p>
                        <p class="mt-2">Total bundle price: <strong>{{ formatCurrency(calculationResult.optimalSellPrice) }}</strong></p>
                        <p v-if="calculationResult.minimumMainProductQty > 1">
                            Price per main product: <strong>{{ formatCurrency(calculationResult.pricePerPiece) }}</strong>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Calculator, Truck, BarChart3, Loader2 } from 'lucide-vue-next';
import { ref, computed } from 'vue';

// Types
interface Product {
    id: string;
    code: string;
    name: string;
    description: string;
    price: number;
}

interface CalculationResult {
    mainProductPrice: number;
    secondaryProductPrice: number;
    minimumMainProductQty: number;
    minimumSellPrice: number;
    maximumSellPrice: number;
    optimalSellPrice: number;
    profitMargin: number;
    optimalMargin: number;
    mainProductMargin: number;
    secondaryProductMargin: number;
    secondaryProductQty: number;
    pricePerPiece: number;
    totalProfit?: number;
}

// Main product data (27.00 R 49)
const mainProduct = ref<Product>({
    id: 'ban-27-00-r-49',
    code: '200-047262',
    name: '27.00 R 49 XD GRIP B E4T TL **',
    description: 'Ban Premium untuk Heavy Equipment',
    price: 212874175 // Default price in IDR
});

// Secondary products data
const secondaryProducts = ref<Product[]>([
    {
        id: 'oli-engine-1',
        code: 'OIL-001',
        name: 'Engine Oil SAE 15W-40',
        description: 'Premium Engine Oil for Heavy Equipment',
        price: 850000
    },
    {
        id: 'filter-air-1',
        code: 'FLT-001',
        name: 'Air Filter Heavy Duty',
        description: 'High Performance Air Filter',
        price: 450000
    },
    {
        id: 'filter-oli-1',
        code: 'FLT-002',
        name: 'Oil Filter Premium',
        description: 'Premium Oil Filter for Heavy Equipment',
        price: 320000
    },
    {
        id: 'coolant-1',
        code: 'COL-001',
        name: 'Coolant Radiator',
        description: 'Premium Coolant for Heavy Equipment',
        price: 275000
    }
]);

// Reactive state
const selectedSecondaryProduct = ref<Product | null>(null);
const secondaryProductQty = ref<number>(1);
const mainProductMargin = ref<number>(20);
const secondaryProductMargin = ref<number>(20);
const isCalculating = ref<boolean>(false);
const calculationResult = ref<CalculationResult | null>(null);

// Utility function to format currency
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

// Calculate bundling function
const calculateBundling = async () => {
    if (!selectedSecondaryProduct.value) return;

    isCalculating.value = true;

    try {
        // Simulate calculation delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        const secondaryProduct = selectedSecondaryProduct.value;
        const secondaryProductPrice = secondaryProduct.price;
        const secondaryProductTotalCost = secondaryProductQty.value * secondaryProductPrice;

        // Calculate minimum main product quantity needed to cover secondary product cost
        const mainProductCostWithMargin = mainProduct.value.price * (1 + mainProductMargin.value / 100);
        const minimumMainProductQty = Math.ceil(secondaryProductTotalCost / (mainProductCostWithMargin * (mainProductMargin.value / 100)));

        // Ensure minimum quantity is at least 1
        const finalMinimumQty = Math.max(1, minimumMainProductQty);

        // Calculate total cost
        const totalMainProductCost = finalMinimumQty * mainProduct.value.price;
        const totalCost = totalMainProductCost + secondaryProductTotalCost;

        // Calculate recommended sell price with target margin
        const recommendedSellPrice = totalMainProductCost * (1 + mainProductMargin.value / 100);

        // Calculate actual profit margin
        const profit = recommendedSellPrice - totalMainProductCost;
        const profitMargin = (profit / recommendedSellPrice) * 100;

        // Calculate price per piece
        const pricePerPiece = finalMinimumQty > 1
            ? recommendedSellPrice / finalMinimumQty
            : mainProduct.value.price;

        // Set calculation result
        calculationResult.value = {
            mainProductPrice: mainProduct.value.price,
            secondaryProductPrice: secondaryProductPrice,
            minimumMainProductQty: finalMinimumQty,
            minimumSellPrice: recommendedSellPrice * 0.95, // 5% below recommended
            maximumSellPrice: recommendedSellPrice * 1.05, // 5% above recommended
            optimalSellPrice: recommendedSellPrice,
            profitMargin: profitMargin,
            optimalMargin: mainProductMargin.value,
            mainProductMargin: mainProductMargin.value,
            secondaryProductMargin: secondaryProductMargin.value,
            secondaryProductQty: secondaryProductQty.value,
            pricePerPiece: pricePerPiece,
            totalProfit: profit
        };

    } catch (error) {
        console.error('Error calculating bundling:', error);
        alert('Error occurred during calculation. Please try again.');
    } finally {
        isCalculating.value = false;
    }
};
</script>
