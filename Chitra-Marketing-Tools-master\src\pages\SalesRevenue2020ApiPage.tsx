import React, { useState, useEffect, useCallback } from 'react';
import { DollarSign, Search, ChevronLeft, ChevronRight, RefreshCw, Filter, Download, Edit, Trash, Plus, X, Save, Check, Database } from 'lucide-react';
import {
  SalesRevenueItem,
  SalesRevenueSummary,
  PaginationInfo,
  fetchSalesRevenueData,
  createSalesRevenueRecord as createSalesRevenueItem,
  updateSalesRevenueRecord as updateSalesRevenueItem,
  deleteSalesRevenueRecord as deleteSalesRevenueItem,
  generateSalesRevenueSummary
} from '../services/salesRevenue2020Service';
import * as XLSX from 'xlsx';
import { useToast } from '../components/ui/use-toast';

export default function SalesRevenue2020ApiPage() {
  // State for sales revenue data
  const [salesRevenueData, setSalesRevenueData] = useState<SalesRevenueItem[]>([]);
  const [summary, setSummary] = useState<SalesRevenueSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for search and filters
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCustomer, setFilterCustomer] = useState<string>('all');
  const [filterSalesman, setFilterSalesman] = useState<string>('all');
  const [filterMatGrp, setFilterMatGrp] = useState<string>('all');

  // State for unique filter values
  const [uniqueCustomers, setUniqueCustomers] = useState<string[]>([]);
  const [uniqueSalesmen, setUniqueSalesmen] = useState<string[]>([]);
  const [uniqueMatGrps, setUniqueMatGrps] = useState<string[]>([]);

  // State for sorting
  const [sortField, setSortField] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // State for pagination
  const [paginationInfo, setPaginationInfo] = useState<PaginationInfo>({
    totalItems: 0,
    page: 1,
    pageSize: 25,
    totalPages: 0,
    isFirstPage: true,
    isLastPage: false
  });

  // State for CRUD operations
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentItem, setCurrentItem] = useState<SalesRevenueItem | null>(null);
  const [formData, setFormData] = useState<Partial<SalesRevenueItem>>({});

  // Toast notifications
  const { toast } = useToast();

  // Load data on component mount and when pagination/sorting changes
  useEffect(() => {
    loadSalesRevenueData();
  }, [paginationInfo.page, paginationInfo.pageSize, sortField, sortDirection]);

  // Load sales revenue data with pagination
  const loadSalesRevenueData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Starting to load sales revenue data...');

      // Calculate offset based on current page and page size
      const offset = (paginationInfo.page - 1) * paginationInfo.pageSize;

      // Prepare sort parameter
      const sort = sortField ? `${sortDirection === 'desc' ? '-' : ''}${sortField}` : '';

      // Prepare where parameter based on filters
      let whereConditions = [];
      if (filterCustomer !== 'all') {
        whereConditions.push(`(customerName,eq,${filterCustomer})`);
      }
      if (filterSalesman !== 'all') {
        whereConditions.push(`(salesman,eq,${filterSalesman})`);
      }
      if (filterMatGrp !== 'all') {
        whereConditions.push(`(matGrpDesc,eq,${filterMatGrp})`);
      }
      if (searchQuery) {
        whereConditions.push(`(customerName,like,%${searchQuery}%)`);
      }

      const where = whereConditions.length > 0 ? whereConditions.join('~and') : '';

      console.log('Calling fetchSalesRevenueData with params:', { offset, limit: paginationInfo.pageSize, where, sort });

      // Fetch data from API
      const { list, pageInfo } = await fetchSalesRevenueData(
        offset,
        paginationInfo.pageSize,
        where,
        sort
      );

      console.log('Received data from API:', {
        dataLength: list.length,
        pageInfo
      });

      // Update state with fetched data
      setSalesRevenueData(list);
      setPaginationInfo(pageInfo);

      // Generate summary if we have data
      if (list.length > 0) {
        const summaryData = generateSalesRevenueSummary(list);
        setSummary(summaryData);
      } else {
        // Create a default summary if no data
        setSummary({
          totalRevenue: 0,
          totalCustomers: 0,
          totalSalesmen: 0,
          topCustomers: [],
          topSalesmen: [],
          materialGroupDistribution: [],
          monthlyRevenue: []
        });

        // If this is the first load and we have no data, show an error
        if (uniqueCustomers.length === 0) {
          console.warn('No data returned from API on initial load');
          setError('No data available from the API. The server might be down or the API endpoint might be incorrect.');
        }
      }

      // Extract unique values for filters (only on first load)
      if (uniqueCustomers.length === 0 && list.length > 0) {
        console.log('Extracting unique values for filters');
        const customers = Array.from(new Set(list.map(item => item.customerName).filter(Boolean))).sort();
        const salesmen = Array.from(new Set(list.map(item => item.salesman).filter(Boolean))).sort();
        const matGrps = Array.from(new Set(list.map(item => item.matGrpDesc).filter(Boolean))).sort();

        console.log('Unique values:', {
          customers: customers.length,
          salesmen: salesmen.length,
          matGrps: matGrps.length
        });

        setUniqueCustomers(customers);
        setUniqueSalesmen(salesmen);
        setUniqueMatGrps(matGrps);
      }
    } catch (error) {
      console.error('Error loading sales revenue data:', error);
      setError('Failed to load sales revenue data. Please check the console for more details and try again later.');

      // Set empty data and default summary
      setSalesRevenueData([]);
      setSummary({
        totalRevenue: 0,
        totalCustomers: 0,
        totalSalesmen: 0,
        topCustomers: [],
        topSalesmen: [],
        materialGroupDistribution: [],
        monthlyRevenue: []
      });
    } finally {
      setIsLoading(false);
    }
  }, [paginationInfo.page, paginationInfo.pageSize, sortField, sortDirection, filterCustomer, filterSalesman, filterMatGrp, searchQuery, uniqueCustomers.length]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle search submit
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Reset to first page when searching
    setPaginationInfo(prev => ({ ...prev, page: 1 }));
    loadSalesRevenueData();
  };

  // Handle sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(prev => (prev === 'asc' ? 'desc' : 'asc'));
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Handle filter changes
  const handleFilterChange = (filter: string, value: string) => {
    switch (filter) {
      case 'customer':
        setFilterCustomer(value);
        break;
      case 'salesman':
        setFilterSalesman(value);
        break;
      case 'matGrp':
        setFilterMatGrp(value);
        break;
    }
    // Reset to first page when filtering
    setPaginationInfo(prev => ({ ...prev, page: 1 }));
    loadSalesRevenueData();
  };

  // Handle pagination
  const goToPage = (page: number) => {
    setPaginationInfo(prev => ({ ...prev, page }));
  };

  // Handle page size change
  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newPageSize = parseInt(e.target.value);
    setPaginationInfo(prev => ({
      ...prev,
      pageSize: newPageSize,
      page: 1 // Reset to first page when changing page size
    }));
  };

  // Export data to Excel
  const exportToExcel = () => {
    try {
      const worksheet = XLSX.utils.json_to_sheet(salesRevenueData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sales Revenue 2020-2024');
      XLSX.writeFile(workbook, 'sales_revenue_2020_2024.xlsx');

      toast({
        title: 'Export Successful',
        description: 'Data has been exported to Excel',
        duration: 3000
      });
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export data to Excel',
        variant: 'destructive',
        duration: 3000
      });
    }
  };

  // Handle form input change
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submit for create/update
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (isEditModalOpen && currentItem) {
        // Update existing record
        await updateSalesRevenueItem(currentItem.id, formData);
        toast({
          title: 'Update Successful',
          description: 'Record has been updated',
          duration: 3000
        });
      } else {
        // Create new record
        await createSalesRevenueItem(formData as Omit<SalesRevenueItem, 'id'>);
        toast({
          title: 'Create Successful',
          description: 'New record has been created',
          duration: 3000
        });
      }

      // Close modal and reload data
      setIsAddModalOpen(false);
      setIsEditModalOpen(false);
      loadSalesRevenueData();
    } catch (error) {
      console.error('Error saving record:', error);
      toast({
        title: 'Operation Failed',
        description: 'Failed to save record',
        variant: 'destructive',
        duration: 3000
      });
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!currentItem) return;

    try {
      await deleteSalesRevenueItem(currentItem.id);
      toast({
        title: 'Delete Successful',
        description: 'Record has been deleted',
        duration: 3000
      });

      // Close modal and reload data
      setIsDeleteModalOpen(false);
      loadSalesRevenueData();
    } catch (error) {
      console.error('Error deleting record:', error);
      toast({
        title: 'Delete Failed',
        description: 'Failed to delete record',
        variant: 'destructive',
        duration: 3000
      });
    }
  };

  // Open edit modal
  const openEditModal = (item: SalesRevenueItem) => {
    setCurrentItem(item);
    setFormData({ ...item });
    setIsEditModalOpen(true);
  };

  // Open delete modal
  const openDeleteModal = (item: SalesRevenueItem) => {
    setCurrentItem(item);
    setIsDeleteModalOpen(true);
  };

  // Open add modal
  const openAddModal = () => {
    setFormData({});
    setIsAddModalOpen(true);
  };

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  // Format currency
  const formatCurrency = (value: number | undefined) => {
    if (value === undefined) return '-';
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(value);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <DollarSign className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Sales Revenue 2020-2024 API</h1>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={openAddModal}
            className="flex items-center px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200"
          >
            <Plus size={16} className="mr-2" />
            Add New
          </button>

          <button
            onClick={exportToExcel}
            className="flex items-center px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200"
          >
            <Download size={16} className="mr-2" />
            Export Excel
          </button>

          <button
            onClick={loadSalesRevenueData}
            className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh Data
          </button>

          <button
            onClick={async () => {
              try {
                setIsLoading(true);
                // Create a test record
                const testRecord = {
                  customerName: "Test Customer",
                  salesman: "Test Salesman",
                  materialDescription: "Test Material",
                  matGrpDesc: "Test Group",
                  revenueInDocCurr: 1000000,
                  revenueInLocCurr: 1000000,
                  billingDate: new Date().toISOString().split('T')[0]
                };

                await createSalesRevenueItem(testRecord);
                toast({
                  title: 'Test Record Created',
                  description: 'A test record has been created successfully',
                  duration: 3000
                });

                // Reload data
                loadSalesRevenueData();
              } catch (error) {
                console.error('Error creating test record:', error);
                toast({
                  title: 'Test Failed',
                  description: 'Failed to create test record. Check console for details.',
                  variant: 'destructive',
                  duration: 3000
                });
              } finally {
                setIsLoading(false);
              }
            }}
            className="flex items-center px-3 py-2 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200"
          >
            <Plus size={16} className="mr-2" />
            Create Test Record
          </button>

          <button
            onClick={() => {
              // Import the fallback data directly
              import('../services/salesRevenue2020Service').then(module => {
                const fallbackData = (module as any).FALLBACK_DATA || [];
                setSalesRevenueData(fallbackData);

                // Generate summary
                if (fallbackData.length > 0) {
                  const summaryData = generateSalesRevenueSummary(fallbackData);
                  setSummary(summaryData);

                  // Extract unique values for filters
                  const customers = Array.from(new Set(fallbackData.map(item => item.customerName).filter(Boolean))).sort();
                  const salesmen = Array.from(new Set(fallbackData.map(item => item.salesman).filter(Boolean))).sort();
                  const matGrps = Array.from(new Set(fallbackData.map(item => item.matGrpDesc).filter(Boolean))).sort();

                  setUniqueCustomers(customers);
                  setUniqueSalesmen(salesmen);
                  setUniqueMatGrps(matGrps);
                }

                // Update pagination
                setPaginationInfo({
                  totalItems: fallbackData.length,
                  page: 1,
                  pageSize: 25,
                  totalPages: Math.ceil(fallbackData.length / 25),
                  isFirstPage: true,
                  isLastPage: fallbackData.length <= 25
                });

                toast({
                  title: 'Using Fallback Data',
                  description: `Loaded ${fallbackData.length} sample records`,
                  duration: 3000
                });
              });
            }}
            className="flex items-center px-3 py-2 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200"
          >
            <Database size={16} className="mr-2" />
            Use Sample Data
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          {/* Search Form */}
          <form onSubmit={handleSearchSubmit} className="flex w-full md:w-auto">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="text"
                placeholder="Search by customer name..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="pl-10 pr-4 py-2 border rounded-l-md w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded-r-md hover:bg-blue-700"
            >
              Search
            </button>
          </form>

          {/* Filters */}
          <div className="flex flex-wrap gap-2">
            {/* Customer Filter */}
            <div className="flex items-center">
              <label htmlFor="customerFilter" className="mr-2 text-sm font-medium text-gray-700">
                Customer:
              </label>
              <select
                id="customerFilter"
                value={filterCustomer}
                onChange={(e) => handleFilterChange('customer', e.target.value)}
                className="border rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Customers</option>
                {uniqueCustomers.map((customer) => (
                  <option key={customer} value={customer}>
                    {customer}
                  </option>
                ))}
              </select>
            </div>

            {/* Salesman Filter */}
            <div className="flex items-center">
              <label htmlFor="salesmanFilter" className="mr-2 text-sm font-medium text-gray-700">
                Salesman:
              </label>
              <select
                id="salesmanFilter"
                value={filterSalesman}
                onChange={(e) => handleFilterChange('salesman', e.target.value)}
                className="border rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Salesmen</option>
                {uniqueSalesmen.map((salesman) => (
                  <option key={salesman} value={salesman}>
                    {salesman}
                  </option>
                ))}
              </select>
            </div>

            {/* Material Group Filter */}
            <div className="flex items-center">
              <label htmlFor="matGrpFilter" className="mr-2 text-sm font-medium text-gray-700">
                Material Group:
              </label>
              <select
                id="matGrpFilter"
                value={filterMatGrp}
                onChange={(e) => handleFilterChange('matGrp', e.target.value)}
                className="border rounded-md px-3 py-1.5 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Material Groups</option>
                {uniqueMatGrps.map((matGrp) => (
                  <option key={matGrp} value={matGrp}>
                    {matGrp}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>
      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{formatCurrency(summary.totalRevenue)}</h3>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Customers</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{summary.totalCustomers}</h3>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Filter className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Salesmen</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{summary.totalSalesmen}</h3>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Filter className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Records</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{paginationInfo.totalItems}</h3>
              </div>
              <div className="p-3 bg-yellow-100 rounded-full">
                <Filter className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Sales Revenue Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
            <p className="text-gray-600">Loading sales revenue data...</p>
          </div>
        ) : error ? (
          <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-yellow-700">{error}</p>
                <div className="mt-2 flex space-x-2">
                  <button
                    onClick={loadSalesRevenueData}
                    className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200 text-sm font-medium"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : salesRevenueData.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-600">No sales revenue data found matching your criteria.</p>
          </div>
        ) : (
          <>
            {/* Table with sticky header and horizontal scroll */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('customerName')}
                    >
                      Customer {renderSortIndicator('customerName')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('salesman')}
                    >
                      Salesman {renderSortIndicator('salesman')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('materialDescription')}
                    >
                      Material {renderSortIndicator('materialDescription')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('matGrpDesc')}
                    >
                      Mat Group {renderSortIndicator('matGrpDesc')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('revenueInDocCurr')}
                    >
                      Revenue {renderSortIndicator('revenueInDocCurr')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('billingDate')}
                    >
                      Billing Date {renderSortIndicator('billingDate')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {salesRevenueData.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.customerName || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.salesman || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.materialDescription || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.matGrpDesc || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(item.revenueInDocCurr)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.billingDate ? new Date(item.billingDate).toLocaleDateString('id-ID') : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => openEditModal(item)}
                          className="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => openDeleteModal(item)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash size={16} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination Controls */}
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-col sm:flex-row justify-between items-center">
              <div className="flex items-center mb-4 sm:mb-0">
                <span className="text-sm text-gray-700 mr-4">
                  Showing {salesRevenueData.length} of {paginationInfo.totalItems} items
                </span>
                <select
                  value={paginationInfo.pageSize}
                  onChange={handlePageSizeChange}
                  className="border rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={10}>10 per page</option>
                  <option value={25}>25 per page</option>
                  <option value={50}>50 per page</option>
                  <option value={100}>100 per page</option>
                </select>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => goToPage(1)}
                  disabled={paginationInfo.isFirstPage}
                  className={`px-3 py-1 rounded-md text-sm ${
                    paginationInfo.isFirstPage
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  }`}
                >
                  First
                </button>
                <button
                  onClick={() => goToPage(paginationInfo.page - 1)}
                  disabled={paginationInfo.isFirstPage}
                  className={`px-3 py-1 rounded-md text-sm ${
                    paginationInfo.isFirstPage
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  }`}
                >
                  <ChevronLeft size={16} />
                </button>
                <span className="text-sm text-gray-700">
                  Page {paginationInfo.page} of {paginationInfo.totalPages}
                </span>
                <button
                  onClick={() => goToPage(paginationInfo.page + 1)}
                  disabled={paginationInfo.isLastPage}
                  className={`px-3 py-1 rounded-md text-sm ${
                    paginationInfo.isLastPage
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  }`}
                >
                  <ChevronRight size={16} />
                </button>
                <button
                  onClick={() => goToPage(paginationInfo.totalPages)}
                  disabled={paginationInfo.isLastPage}
                  className={`px-3 py-1 rounded-md text-sm ${
                    paginationInfo.isLastPage
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  }`}
                >
                  Last
                </button>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Add/Edit Modal */}
      {(isAddModalOpen || isEditModalOpen) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">
                {isEditModalOpen ? 'Edit Sales Revenue Record' : 'Add New Sales Revenue Record'}
              </h2>
              <button
                onClick={() => {
                  setIsAddModalOpen(false);
                  setIsEditModalOpen(false);
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>
            <form onSubmit={handleFormSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Customer Name
                  </label>
                  <input
                    type="text"
                    name="customerName"
                    value={formData.customerName || ''}
                    onChange={handleFormChange}
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Salesman
                  </label>
                  <input
                    type="text"
                    name="salesman"
                    value={formData.salesman || ''}
                    onChange={handleFormChange}
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Material Description
                  </label>
                  <input
                    type="text"
                    name="materialDescription"
                    value={formData.materialDescription || ''}
                    onChange={handleFormChange}
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Material Group
                  </label>
                  <input
                    type="text"
                    name="matGrpDesc"
                    value={formData.matGrpDesc || ''}
                    onChange={handleFormChange}
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Revenue (Document Currency)
                  </label>
                  <input
                    type="number"
                    name="revenueInDocCurr"
                    value={formData.revenueInDocCurr || ''}
                    onChange={handleFormChange}
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Billing Date
                  </label>
                  <input
                    type="date"
                    name="billingDate"
                    value={formData.billingDate || ''}
                    onChange={handleFormChange}
                    className="w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setIsAddModalOpen(false);
                    setIsEditModalOpen(false);
                  }}
                  className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-100"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  {isEditModalOpen ? 'Update' : 'Save'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Confirm Delete</h2>
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>
            <p className="text-gray-700 mb-6">
              Are you sure you want to delete this sales revenue record? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setIsDeleteModalOpen(false)}
                className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-100"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteConfirm}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
