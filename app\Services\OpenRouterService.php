<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OpenRouterService
{
    private const API_URL = 'https://openrouter.ai/api/v1/chat/completions';
    private const API_KEY = '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';
    private const MODEL = 'openai/gpt-4.1-nano';

    /**
     * Generate content using OpenRouter API
     */
    public function generateContent(array $request): array
    {
        try {
            $prompt = $this->buildPrompt($request);
            
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . self::API_KEY,
                'HTTP-Referer' => 'https://chitraparatama.co.id',
                'X-Title' => 'Chitra Marketing Tools'
            ])->post(self::API_URL, [
                'model' => self::MODEL,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'Anda adalah asisten marketing profesional yang ahli dalam membuat konten social media untuk produk ban. Berikan respons yang kreatif, menarik, dan profesional.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 1000
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $content = $data['choices'][0]['message']['content'] ?? '';
                
                return $this->parseGeneratedContent($content, $request);
            } else {
                Log::error('OpenRouter API Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                
                return $this->getFallbackContent($request);
            }
        } catch (\Exception $e) {
            Log::error('OpenRouter Service Error', [
                'message' => $e->getMessage(),
                'request' => $request
            ]);
            
            return $this->getFallbackContent($request);
        }
    }

    /**
     * Generate monthly content plan
     */
    public function generateMonthlyPlan(array $request): array
    {
        try {
            $prompt = $this->buildMonthlyPlanPrompt($request);
            
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . self::API_KEY,
                'HTTP-Referer' => 'https://chitraparatama.co.id',
                'X-Title' => 'Chitra Marketing Tools'
            ])->post(self::API_URL, [
                'model' => self::MODEL,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'Anda adalah strategis marketing yang ahli dalam membuat rencana konten bulanan untuk social media. Berikan rencana yang terstruktur, kreatif, dan sesuai dengan industri ban.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.8,
                'max_tokens' => 2000
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $content = $data['choices'][0]['message']['content'] ?? '';
                
                return $this->parseMonthlyPlan($content, $request);
            } else {
                return $this->getFallbackMonthlyPlan($request);
            }
        } catch (\Exception $e) {
            Log::error('Monthly Plan Generation Error', [
                'message' => $e->getMessage(),
                'request' => $request
            ]);
            
            return $this->getFallbackMonthlyPlan($request);
        }
    }

    /**
     * Build prompt for content generation
     */
    private function buildPrompt(array $request): string
    {
        $platform = $request['platform'] ?? 'Instagram';
        $contentType = $request['contentType'] ?? 'Post';
        $tone = $request['tone'] ?? 'professional';
        $targetAudience = $request['targetAudience'] ?? 'Perusahaan pertambangan dan logistik';
        $productDetails = $request['productDetails'] ?? 'Ban premium untuk aplikasi industri';
        $campaignGoals = $request['campaignGoals'] ?? 'Meningkatkan brand awareness';

        return "
        Buatkan konten {$contentType} untuk platform {$platform} dengan detail berikut:

        Target Audience: {$targetAudience}
        Produk: {$productDetails}
        Tone: {$tone}
        Tujuan Kampanye: {$campaignGoals}

        Konten harus:
        1. Menarik dan engaging untuk target audience
        2. Mencerminkan kualitas premium produk ban
        3. Menggunakan tone {$tone}
        4. Menyertakan call-to-action yang kuat
        5. Relevan dengan industri pertambangan/logistik

        Format output:
        CAPTION: [caption lengkap dengan emoji]
        HASHTAGS: [15-20 hashtag yang relevan]
        VISUAL_SUGGESTION: [deskripsi visual yang disarankan]
        ";
    }

    /**
     * Build prompt for monthly plan generation
     */
    private function buildMonthlyPlanPrompt(array $request): string
    {
        $month = $request['month'] ?? date('F');
        $year = $request['year'] ?? date('Y');
        $platform = $request['platform'] ?? 'Instagram';
        $postsPerWeek = $request['postsPerWeek'] ?? 3;

        return "
        Buatkan rencana konten bulanan untuk {$platform} bulan {$month} {$year} untuk perusahaan ban premium.

        Requirements:
        - {$postsPerWeek} post per minggu
        - Mix antara konten edukasi, promosi, dan engagement
        - Fokus pada industri pertambangan dan logistik
        - Sertakan hari-hari penting dan seasonal events
        - Variasi jenis konten (foto, video, carousel, story)

        Format output JSON:
        {
            \"month\": \"{$month}\",
            \"year\": {$year},
            \"posts\": [
                {
                    \"date\": \"YYYY-MM-DD\",
                    \"title\": \"Judul Post\",
                    \"type\": \"image/video/carousel\",
                    \"category\": \"education/promotion/engagement\",
                    \"caption\": \"Caption lengkap\",
                    \"hashtags\": [\"hashtag1\", \"hashtag2\"],
                    \"visual_suggestion\": \"Deskripsi visual\"
                }
            ]
        }
        ";
    }

    /**
     * Parse generated content
     */
    private function parseGeneratedContent(string $content, array $request): array
    {
        $lines = explode("\n", $content);
        $result = [
            'caption' => '',
            'hashtags' => [],
            'visual_suggestion' => '',
            'platform' => $request['platform'] ?? 'Instagram',
            'content_type' => $request['contentType'] ?? 'Post'
        ];

        foreach ($lines as $line) {
            $line = trim($line);
            if (strpos($line, 'CAPTION:') === 0) {
                $result['caption'] = trim(substr($line, 8));
            } elseif (strpos($line, 'HASHTAGS:') === 0) {
                $hashtags = trim(substr($line, 9));
                $result['hashtags'] = array_map('trim', explode(' ', $hashtags));
            } elseif (strpos($line, 'VISUAL_SUGGESTION:') === 0) {
                $result['visual_suggestion'] = trim(substr($line, 18));
            }
        }

        return $result;
    }

    /**
     * Parse monthly plan
     */
    private function parseMonthlyPlan(string $content, array $request): array
    {
        // Try to extract JSON from the content
        $jsonStart = strpos($content, '{');
        $jsonEnd = strrpos($content, '}');
        
        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonContent = substr($content, $jsonStart, $jsonEnd - $jsonStart + 1);
            $decoded = json_decode($jsonContent, true);
            
            if ($decoded && isset($decoded['posts'])) {
                return $decoded;
            }
        }

        // Fallback if JSON parsing fails
        return $this->getFallbackMonthlyPlan($request);
    }

    /**
     * Get fallback content
     */
    private function getFallbackContent(array $request): array
    {
        return [
            'caption' => '🔧 Tingkatkan performa operasional Anda dengan ban premium berkualitas tinggi! Dirancang khusus untuk kondisi kerja ekstrem dan memberikan daya tahan maksimal. #BanPremium #PerformaOptimal',
            'hashtags' => ['#BanPremium', '#ChitraParatama', '#Mining', '#HeavyEquipment', '#TireSafety', '#Industrial', '#Quality', '#Performance'],
            'visual_suggestion' => 'Foto ban premium di lokasi tambang dengan equipment berat, pencahayaan golden hour',
            'platform' => $request['platform'] ?? 'Instagram',
            'content_type' => $request['contentType'] ?? 'Post'
        ];
    }

    /**
     * Get fallback monthly plan
     */
    private function getFallbackMonthlyPlan(array $request): array
    {
        $month = $request['month'] ?? date('F');
        $year = $request['year'] ?? date('Y');
        
        return [
            'month' => $month,
            'year' => (int)$year,
            'posts' => [
                [
                    'date' => date('Y-m-01'),
                    'title' => 'Kick-off Bulan Produktif',
                    'type' => 'image',
                    'category' => 'motivation',
                    'caption' => '🚀 Memulai bulan dengan semangat tinggi! Ban premium untuk performa maksimal sepanjang bulan.',
                    'hashtags' => ['#NewMonth', '#BanPremium', '#Productivity'],
                    'visual_suggestion' => 'Sunrise di lokasi tambang dengan equipment siap beroperasi'
                ],
                [
                    'date' => date('Y-m-15'),
                    'title' => 'Tips Maintenance Ban',
                    'type' => 'carousel',
                    'category' => 'education',
                    'caption' => '💡 Tips perawatan ban untuk memperpanjang umur pakai dan mengoptimalkan performa.',
                    'hashtags' => ['#TipsMaintenance', '#BanCare', '#Efficiency'],
                    'visual_suggestion' => 'Step-by-step maintenance process dengan infografis'
                ]
            ]
        ];
    }
}
