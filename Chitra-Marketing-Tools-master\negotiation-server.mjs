import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import axios from 'axios';

const app = express();

// Enable CORS for all routes
app.use(cors());

// Parse JSON request bodies
app.use(bodyParser.json());

// OpenRouter API key
const OPENROUTER_API_KEY = '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';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Models
const MODELS = {
  GPT4: 'openai/gpt-4.1-nano',
  CLAUDE_OPUS: 'openai/gpt-4.1-nano',
  CLAUDE_SONNET: 'openai/gpt-4.1-nano',
  DEEPSEEK: 'openai/gpt-4.1-nano',
};

// Default model for negotiation
const DEFAULT_MODEL = MODELS.GPT4;

// Customer personas
const CUSTOMER_PERSONAS = {
  price_sensitive: {
    type: 'price_sensitive',
    name: 'Budi <PERSON>o',
    company: 'PT Hemat Selalu',
    description: 'A price-conscious buyer who always looks for the best deal and is willing to walk away if the price isn\'t right.',
    negotiationStyle: 'Direct, focused on price, often compares with competitor offers',
    priceExpectation: 'low',
    loyaltyLevel: 'low',
    valueFeatures: ['Low price', 'Discounts', 'Payment terms'],
    painPoints: ['Budget constraints', 'Previous bad experiences with overpriced products', 'Pressure from management to reduce costs'],
    avatar: '/assets/avatars/price-sensitive.png'
  },
  loyal_customer: {
    type: 'loyal_customer',
    name: 'Dewi Wijaya',
    company: 'PT Setia Mining',
    description: 'A long-term customer who values relationship and quality but still expects fair pricing.',
    negotiationStyle: 'Collaborative, relationship-focused, values consistency and reliability',
    priceExpectation: 'medium',
    loyaltyLevel: 'high',
    valueFeatures: ['Quality', 'Reliability', 'After-sales support', 'Long-term partnership'],
    painPoints: ['Service inconsistency', 'Feeling taken for granted', 'Lack of special treatment despite loyalty'],
    avatar: '/assets/avatars/loyal-customer.png'
  },
  aggressive_buyer: {
    type: 'aggressive_buyer',
    name: 'Agus Pratama',
    company: 'PT Tegas Resources',
    description: 'Seorang pembeli dari perusahaan pertambangan yang sedang melakukan negosiasi pembelian ban alat berat dengan gaya tegas dan kadang konfrontatif.',
    negotiationStyle: 'Tegas, kadang konfrontatif, sering menggunakan batas waktu, tekanan kuantitas, atau ancaman pindah supplier',
    priceExpectation: 'low',
    loyaltyLevel: 'medium',
    valueFeatures: [
      'Menang dalam negosiasi (deal terbaik, harga terendah, atau bonus terbanyak)',
      'Transparansi & justifikasi yang jelas dari penjual',
      'Respons cepat dan tanggapan masuk akal, tidak basa-basi'
    ],
    painPoints: [
      'Menghindari pembelian yang tidak memberikan nilai lebih (value for money)',
      'Menginginkan penawaran yang bisa dibuktikan secara nyata meningkatkan efisiensi biaya',
      'Terbiasa membandingkan dengan supplier lain karena pasar sangat kompetitif',
      'Ingin menghindari kerugian atau margin yang terlalu kecil untuk operasional internal'
    ],
    avatar: '/assets/avatars/aggressive-buyer.png'
  }
};

// API routes
app.post('/api/negotiation/message', async (req, res) => {
  try {
    const {
      customerPersona,
      selectedProducts,
      messages,
      targetMargin,
      currentMargin,
      isFirstMessage,
      isEvaluation
    } = req.body;

    // Format products for the prompt
    const productsText = selectedProducts.map(item =>
      `- ${item.product.materialDescription} (${item.quantity} units): ${formatCurrency(item.originalPrice)} per unit`
    ).join('\n');

    // Format negotiation history
    const negotiationHistory = messages.map(msg =>
      `${msg.sender === 'ai' ? customerPersona.name : 'Sales'}: ${msg.content}`
    ).join('\n\n');

    let systemPrompt = '';
    let userPrompt = '';

    if (isEvaluation) {
      // Evaluation prompt
      systemPrompt = `You are an expert sales coach who evaluates negotiations between sales representatives and customers. You provide detailed, constructive feedback to help sales representatives improve their negotiation skills.`;

      userPrompt = `Please evaluate this completed negotiation between a sales representative and ${customerPersona.name} from ${customerPersona.company}.

Customer Persona: ${customerPersona.description}
Negotiation Style: ${customerPersona.negotiationStyle}

Products Discussed:
${productsText}

Target Margin: ${targetMargin}%
Actual Margin Achieved: ${currentMargin?.toFixed(2)}%

Negotiation Transcript:
${negotiationHistory}

Provide a detailed evaluation in JSON format with the following structure:
{
  "marginAchieved": boolean,
  "marginDifference": number,
  "discountAppropriate": boolean,
  "discountReason": "string explaining if discounts were appropriate",
  "alternativeStrategies": ["array of alternative strategies that could have been used"],
  "overallScore": number (0-100),
  "strengths": ["array of negotiation strengths demonstrated"],
  "improvements": ["array of areas for improvement"],
  "keyInsights": "string with key insights from this negotiation"
}`;
    } else if (isFirstMessage) {
      // First message prompt - customer initiates the conversation in Bahasa Indonesia
      systemPrompt = `Kamu adalah ${customerPersona.name} dari ${customerPersona.company}, ${customerPersona.description} Kamu sedang melakukan negosiasi dengan seorang sales dari PT Chitra Paratama.

Karakter dan Gaya Negosiasi:
- ${customerPersona.negotiationStyle}
- Tujuan utama adalah mendapatkan harga terbaik, bonus tambahan, atau bundling yang menguntungkan
- Tidak mudah percaya dengan penawaran awal sales

Fokus & Kekhawatiran:
${customerPersona.painPoints.map(point => `- ${point}`).join('\n')}

Nilai yang Dipegang:
${customerPersona.valueFeatures.map(value => `- ${value}`).join('\n')}

Cara Komunikasi:
- Gunakan bahasa Indonesia sehari-hari, profesional tapi santai seperti pelanggan sungguhan
- Jangan gunakan bahasa Inggris kecuali untuk nama produk atau istilah teknis
- Hindari tanggapan template seperti "I'm having trouble…" atau "Let's reconnect"

Konteks Negosiasi:
- Produk yang diminati: 10.00 - 20 GT MILLER
- Harga produk: Rp 3.157.882 per unit
- Jumlah yang diminati: 1 unit (bisa bertambah kalau penawaran menarik)
- Kamu menginginkan minimal diskon 10% atau bundling dengan sensor gratis (contoh: CTS atau VBOX)

Perilaku Saat Simulasi:
- Jawab setiap pertanyaan sales seperti pembeli sungguhan
- Uji kemampuan sales dalam menawarkan alternatif: diskon, bundling, atau promosi
- Jika penawaran terlalu standar, beri tekanan: "supplier lain bisa lebih murah", "kami butuh bukti value"
- Jangan langsung menerima tawaran — buat percakapan berlangsung 3–5 putaran negosiasi`;

      userPrompt = `Mulailah percakapan dengan sales dari PT Chitra Paratama. Perkenalkan diri, tunjukkan ketertarikan pada produk ban 10.00 - 20 GT MILLER, tapi langsung mulai negosiasi sesuai karakter kamu yang tegas. Gunakan bahasa Indonesia yang natural dan profesional.`;
    } else {
      // Ongoing conversation prompt in Bahasa Indonesia
      systemPrompt = `Kamu adalah ${customerPersona.name} dari ${customerPersona.company}, ${customerPersona.description} Kamu sedang melakukan negosiasi dengan seorang sales dari PT Chitra Paratama.

Karakter dan Gaya Negosiasi:
- ${customerPersona.negotiationStyle}
- Tujuan utama adalah mendapatkan harga terbaik, bonus tambahan, atau bundling yang menguntungkan
- Tidak mudah percaya dengan penawaran awal sales

Fokus & Kekhawatiran:
${customerPersona.painPoints.map(point => `- ${point}`).join('\n')}

Nilai yang Dipegang:
${customerPersona.valueFeatures.map(value => `- ${value}`).join('\n')}

Cara Komunikasi:
- Gunakan bahasa Indonesia sehari-hari, profesional tapi santai seperti pelanggan sungguhan
- Jangan gunakan bahasa Inggris kecuali untuk nama produk atau istilah teknis
- Hindari tanggapan template seperti "I'm having trouble…" atau "Let's reconnect"

Konteks Negosiasi:
- Produk yang diminati: 10.00 - 20 GT MILLER
- Harga produk: Rp 3.157.882 per unit
- Jumlah yang diminati: 1 unit (bisa bertambah kalau penawaran menarik)
- Kamu menginginkan minimal diskon 10% atau bundling dengan sensor gratis (contoh: CTS atau VBOX)

Perilaku Saat Simulasi:
- Jawab setiap pertanyaan sales seperti pembeli sungguhan
- Uji kemampuan sales dalam menawarkan alternatif: diskon, bundling, atau promosi
- Jika penawaran terlalu standar, beri tekanan: "supplier lain bisa lebih murah", "kami butuh bukti value"
- Jangan langsung menerima tawaran — buat percakapan berlangsung 3–5 putaran negosiasi`;

      userPrompt = `Ini adalah percakapan yang sudah berlangsung:
${negotiationHistory}

Tanggapi pesan terakhir dari sales dengan gaya negosiasi kamu yang tegas. Ingat untuk tetap menggunakan bahasa Indonesia yang natural. Jika negosiasi terlihat akan berakhir, kamu bisa menerima penawaran jika sudah sesuai harapan atau berikan penawaran balik terakhir.`;
    }

    // Make the API call to OpenRouter
    const response = await axios.post(OPENROUTER_API_URL, {
      model: DEFAULT_MODEL,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 1500
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Negotiation Simulator'
      }
    });

    // Extract the content from the response
    const content = response.data.choices[0].message.content;

    if (isEvaluation) {
      try {
        // Try to parse the JSON evaluation
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        const jsonString = jsonMatch ? jsonMatch[0] : content;
        const evaluation = JSON.parse(jsonString);

        res.json({
          message: 'Evaluation completed',
          evaluation
        });
      } catch (error) {
        console.error('Error parsing evaluation JSON:', error);
        res.json({
          message: content,
          evaluation: undefined
        });
      }
    } else {
      res.json({
        message: content
      });
    }
  } catch (error) {
    console.error('Error calling AI service:', error);

    // Return a fallback response
    if (req.body.isEvaluation) {
      res.json({
        message: 'Unable to evaluate the negotiation due to a technical error.',
        evaluation: {
          marginAchieved: false,
          marginDifference: 0,
          discountAppropriate: false,
          discountReason: 'Unable to evaluate due to technical error',
          alternativeStrategies: ['Try again later'],
          overallScore: 0,
          strengths: [],
          improvements: ['Try again when the service is available'],
          keyInsights: 'Technical error occurred during evaluation'
        }
      });
    } else {
      res.json({
        message: req.body.isFirstMessage
          ? `Halo, saya ${req.body.customerPersona.name} dari ${req.body.customerPersona.company}. Saya tertarik dengan produk ban 10.00 - 20 GT MILLER, tapi sedang ada masalah koneksi. Bisa kita coba lagi sebentar?`
          : "Maaf, sepertinya ada kendala teknis di sisi saya. Bisa kita lanjutkan percakapan ini sebentar lagi?"
      });
    }
  }
});

// Helper function to format currency
function formatCurrency(amount) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}

// Get customer personas
app.get('/api/negotiation/personas', (req, res) => {
  res.json(CUSTOMER_PERSONAS);
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({ message: 'Negotiation server is working!' });
});

// Start the server
const PORT = process.env.PORT || 3002;
app.listen(PORT, () => {
  console.log(`Negotiation server running on port ${PORT}`);
});
