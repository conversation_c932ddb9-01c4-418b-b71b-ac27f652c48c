import axios from 'axios';
import <PERSON> from 'papaparse';

export interface FleetlistItem {
  id: string;
  id_fleet_list?: string;
  customer?: string;
  site?: string;
  status?: string;
  location?: string;
  kabupaten?: string;
  kecamatan?: string;
  unit_manufacture?: string;
  model?: string;
  tire_size?: string;
  tire_quantity?: string;
  unit_qty?: string;
  totaltire?: string;
  annual?: string;
  forecast?: string;
  [key: string]: any; // For any additional fields from the CSV
}

export const fetchFleetlist = async (): Promise<FleetlistItem[]> => {
  try {
    console.log('Attempting to fetch fleetlist data from Google Sheets CSV...');

    // Set a timeout for the request
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

    // Google Sheets CSV URL
    const csvUrl = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vSRFlBtJQvRB5xMg5276wnm6-aMeR9oli3IxMAG15QHbu79qh2Le8BEUINgomb72j0l60CxExqXAXJy/pub?gid=0&single=true&output=csv';

    const response = await axios.get(csvUrl, {
      signal: controller.signal,
      responseType: 'text' // Important: we want the raw CSV text
    });

    clearTimeout(timeoutId);

    // Parse CSV data
    return new Promise((resolve, reject) => {
      Papa.parse(response.data, {
        header: true, // First row is headers
        skipEmptyLines: true,
        complete: (results) => {
          console.log('CSV parsing complete:', results);

          if (results.data && Array.isArray(results.data) && results.data.length > 0) {
            // Map the CSV data to our FleetlistItem interface
            const fleetlist = results.data.map((item: any, index: number) => {
              // Create a normalized object with consistent property names
              const normalizedItem: FleetlistItem = {
                id: `fleet-${index + 1}`,
                id_fleet_list: item['id_fleet_list'] || '',
                customer: item['customer'] || '',
                site: item['site'] || '',
                status: item['status'] || 'Active',
                location: item['location'] || '',
                kabupaten: item['kabupaten'] || '',
                kecamatan: item['kecamatan'] || '',
                unit_manufacture: item['unit_manufacture'] || '',
                model: item['model'] || '',
                tire_size: item['tire_size'] || '',
                tire_quantity: item['tire_quantity'] || '',
                unit_qty: item['unit_qty'] || '',
                totaltire: item['totaltire'] || '',
                annual: item['annual'] || '',
                forecast: item['forecast'] || ''
              };

              // Add any other fields that might be in the CSV
              Object.keys(item).forEach(key => {
                if (!Object.keys(normalizedItem).includes(key.toLowerCase().replace(' ', '_'))) {
                  normalizedItem[key] = item[key];
                }
              });

              return normalizedItem;
            });

            console.log(`Successfully parsed ${fleetlist.length} fleet items from CSV`);
            resolve(fleetlist);
          } else {
            console.error('CSV parsing returned no valid data:', results);
            reject(new Error('CSV parsing returned no valid data'));
          }
        },
        error: (error) => {
          console.error('Error parsing CSV:', error);
          reject(error);
        }
      });
    }).catch(error => {
      console.error('Error processing CSV data:', error);
      return FALLBACK_FLEETLIST;
    });
  } catch (error) {
    console.error('Error fetching fleetlist from Google Sheets:', error);
    console.log('Using fallback data due to API error');
    return FALLBACK_FLEETLIST;
  }
};

// Fallback data in case the API fails
export const FALLBACK_FLEETLIST: FleetlistItem[] = [
  {
    id: "1",
    id_fleet_list: "1556",
    customer: "Rimba Perkasa Utama",
    site: "DKB",
    status: "Active",
    location: "Kalimantan Timur",
    kabupaten: "Kutai Kartanegara",
    kecamatan: "Tenggarong",
    unit_manufacture: "Komatsu",
    model: "HD785-7",
    tire_size: "27.00R49",
    tire_quantity: "6",
    unit_qty: "10",
    totaltire: "60",
    annual: "120",
    forecast: "180"
  },
  {
    id: "2",
    id_fleet_list: "1557",
    customer: "Adaro Indonesia",
    site: "ADRO",
    status: "Active",
    location: "Kalimantan Selatan",
    kabupaten: "Tabalong",
    kecamatan: "Tanjung",
    unit_manufacture: "Caterpillar",
    model: "777D",
    tire_size: "24.00R35",
    tire_quantity: "6",
    unit_qty: "15",
    totaltire: "90",
    annual: "180",
    forecast: "270"
  },
  {
    id: "3",
    id_fleet_list: "1558",
    customer: "Berau Coal",
    site: "BCL",
    status: "Maintenance",
    location: "Kalimantan Timur",
    kabupaten: "Berau",
    kecamatan: "Tanjung Redeb",
    unit_manufacture: "Hitachi",
    model: "EH3500",
    tire_size: "27.00R49",
    tire_quantity: "6",
    unit_qty: "8",
    totaltire: "48",
    annual: "96",
    forecast: "144"
  },
  {
    id: "4",
    fleet_no: "FL004",
    fleet_name: "Wheel Loader 1",
    fleet_type: "Wheel Loader",
    fleet_brand: "Caterpillar",
    fleet_model: "950GC",
    fleet_year: "2021",
    fleet_status: "Active",
    fleet_location: "Site C",
    fleet_customer: "PT Coal Mining"
  },
  {
    id: "5",
    fleet_no: "FL005",
    fleet_name: "Grader 1",
    fleet_type: "Grader",
    fleet_brand: "Caterpillar",
    fleet_model: "120K2",
    fleet_year: "2020",
    fleet_status: "Active",
    fleet_location: "Site A",
    fleet_customer: "PT Mining Indonesia"
  },
  {
    id: "6",
    fleet_no: "FL006",
    fleet_name: "Haul Truck 1",
    fleet_type: "Haul Truck",
    fleet_brand: "Komatsu",
    fleet_model: "HD785-7",
    fleet_year: "2019",
    fleet_status: "Active",
    fleet_location: "Site D",
    fleet_customer: "PT Adaro Mining"
  },
  {
    id: "7",
    fleet_no: "FL007",
    fleet_name: "Articulated Dump Truck 1",
    fleet_type: "Articulated Dump Truck",
    fleet_brand: "Volvo",
    fleet_model: "A40G",
    fleet_year: "2021",
    fleet_status: "Active",
    fleet_location: "Site B",
    fleet_customer: "PT Coal Mining"
  },
  {
    id: "8",
    fleet_no: "FL008",
    fleet_name: "Excavator 2",
    fleet_type: "Excavator",
    fleet_brand: "Hitachi",
    fleet_model: "ZX870LC-5G",
    fleet_year: "2020",
    fleet_status: "Maintenance",
    fleet_location: "Workshop",
    fleet_customer: "PT Adaro Mining"
  },
  {
    id: "9",
    fleet_no: "FL009",
    fleet_name: "Bulldozer 2",
    fleet_type: "Bulldozer",
    fleet_brand: "Caterpillar",
    fleet_model: "D9T",
    fleet_year: "2018",
    fleet_status: "Inactive",
    fleet_location: "Storage",
    fleet_customer: "PT Mining Indonesia"
  },
  {
    id: "10",
    fleet_no: "FL010",
    fleet_name: "Wheel Loader 2",
    fleet_type: "Wheel Loader",
    fleet_brand: "Komatsu",
    fleet_model: "WA500-6",
    fleet_year: "2019",
    fleet_status: "Active",
    fleet_location: "Site A",
    fleet_customer: "PT Mining Indonesia"
  },
  {
    id: "11",
    fleet_no: "FL011",
    fleet_name: "Dump Truck 2",
    fleet_type: "Dump Truck",
    fleet_brand: "Scania",
    fleet_model: "P410",
    fleet_year: "2021",
    fleet_status: "Active",
    fleet_location: "Site C",
    fleet_customer: "PT Coal Mining"
  },
  {
    id: "12",
    fleet_no: "FL012",
    fleet_name: "Motor Grader 1",
    fleet_type: "Motor Grader",
    fleet_brand: "Caterpillar",
    fleet_model: "14M",
    fleet_year: "2020",
    fleet_status: "Active",
    fleet_location: "Site D",
    fleet_customer: "PT Adaro Mining"
  },
  {
    id: "13",
    fleet_no: "FL013",
    fleet_name: "Haul Truck 2",
    fleet_type: "Haul Truck",
    fleet_brand: "Caterpillar",
    fleet_model: "777E",
    fleet_year: "2018",
    fleet_status: "Maintenance",
    fleet_location: "Workshop",
    fleet_customer: "PT Mining Indonesia"
  },
  {
    id: "14",
    fleet_no: "FL014",
    fleet_name: "Excavator 3",
    fleet_type: "Excavator",
    fleet_brand: "Komatsu",
    fleet_model: "PC2000-8",
    fleet_year: "2019",
    fleet_status: "Active",
    fleet_location: "Site B",
    fleet_customer: "PT Coal Mining"
  },
  {
    id: "15",
    fleet_no: "FL015",
    fleet_name: "Water Truck 1",
    fleet_type: "Water Truck",
    fleet_brand: "Mercedes-Benz",
    fleet_model: "Actros 4140K",
    fleet_year: "2020",
    fleet_status: "Active",
    fleet_location: "Site A",
    fleet_customer: "PT Mining Indonesia"
  }
];
