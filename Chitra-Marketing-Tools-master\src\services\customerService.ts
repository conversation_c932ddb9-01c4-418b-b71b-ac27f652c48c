import { Customer, CustomerFormData } from '../types';
import { fetchFleetlist, FleetlistItem } from './fleetlistService';

// Mock data for initial customers
const MOCK_CUSTOMERS: Customer[] = [
  {
    id: '1',
    name: 'PT <PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+62 812-3456-7890',
    address: 'Jl. Sudirman No. 123, Jakarta',
    company: 'PT Maju Bersama',
    notes: 'Regular customer, orders monthly',
    createdAt: new Date('2023-01-15'),
    updatedAt: new Date('2023-06-20')
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+62 878-9012-3456',
    address: 'Jl. Gatot Subroto No. 45, Bandung',
    company: 'Bengkel Sejahtera',
    notes: 'Prefers premium products',
    createdAt: new Date('2023-02-10'),
    updatedAt: new Date('2023-05-15')
  },
  {
    id: '3',
    name: '<PERSON><PERSON> Sparepart Jaya',
    email: '<EMAIL>',
    phone: '+62 856-7890-1234',
    address: 'Jl. Ahmad Yani No. 78, Surabaya',
    company: 'Toko Sparepart Jaya',
    createdAt: new Date('2023-03-05'),
    updatedAt: new Date('2023-03-05')
  }
];

// Local storage key
const STORAGE_KEY = 'chitra_customers';

// Load customers from localStorage or use mock data if none exists
const loadCustomersFromStorage = (): Customer[] => {
  try {
    const storedData = localStorage.getItem(STORAGE_KEY);
    if (storedData) {
      // Parse the stored JSON data
      const parsedData = JSON.parse(storedData);

      // Convert string dates back to Date objects
      return parsedData.map((customer: any) => ({
        ...customer,
        createdAt: new Date(customer.createdAt),
        updatedAt: new Date(customer.updatedAt)
      }));
    }
  } catch (error) {
    console.error('Error loading customers from localStorage:', error);
  }

  // Return mock data if no stored data or error
  return [...MOCK_CUSTOMERS];
};

// Save customers to localStorage
const saveCustomersToStorage = (customersData: Customer[]): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(customersData));
  } catch (error) {
    console.error('Error saving customers to localStorage:', error);
  }
};

// In-memory cache of customers, initially loaded from localStorage
let customers: Customer[] = loadCustomersFromStorage();

// Get all customers
export const getAllCustomers = (): Customer[] => {
  // Refresh from localStorage to ensure we have the latest data
  customers = loadCustomersFromStorage();
  return [...customers];
};

// Get customer by ID
export const getCustomerById = (id: string): Customer | undefined => {
  // Make sure we have the latest data from localStorage
  customers = loadCustomersFromStorage();
  return customers.find(customer => customer.id === id);
};

// Create a new customer
export const createCustomer = (customerData: CustomerFormData): Customer => {
  const newCustomer: Customer = {
    id: Date.now().toString(), // Simple ID generation
    ...customerData,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Add to in-memory array
  customers.push(newCustomer);

  // Save to localStorage
  saveCustomersToStorage(customers);

  return newCustomer;
};

// Update an existing customer
export const updateCustomer = (id: string, customerData: CustomerFormData): Customer | null => {
  const index = customers.findIndex(customer => customer.id === id);

  if (index === -1) {
    return null;
  }

  const updatedCustomer: Customer = {
    ...customers[index],
    ...customerData,
    updatedAt: new Date()
  };

  // Update in-memory array
  customers[index] = updatedCustomer;

  // Save to localStorage
  saveCustomersToStorage(customers);

  return updatedCustomer;
};

// Delete a customer
export const deleteCustomer = (id: string): boolean => {
  const initialLength = customers.length;
  customers = customers.filter(customer => customer.id !== id);

  // Save to localStorage if a customer was deleted
  if (customers.length < initialLength) {
    saveCustomersToStorage(customers);
    return true;
  }

  return false;
};

// Search customers
export const searchCustomers = (query: string): Customer[] => {
  if (!query) {
    return getAllCustomers();
  }

  // Make sure we have the latest data from localStorage
  customers = loadCustomersFromStorage();

  const lowerQuery = query.toLowerCase();

  return customers.filter(customer =>
    customer.name.toLowerCase().includes(lowerQuery) ||
    customer.email.toLowerCase().includes(lowerQuery) ||
    customer.phone.toLowerCase().includes(lowerQuery) ||
    (customer.company && customer.company.toLowerCase().includes(lowerQuery))
  );
};

// Import customer names from Fleetlist
export const importCustomersFromFleetlist = async (): Promise<{
  added: number,
  skipped: number,
  customerNames: string[]
}> => {
  try {
    // Fetch fleetlist data
    const fleetlistData = await fetchFleetlist();

    // Get all existing customers (this will refresh from localStorage)
    const existingCustomers = getAllCustomers();

    // Extract unique customer names from fleetlist
    const uniqueCustomerNames = new Set<string>();

    fleetlistData.forEach(item => {
      // Check for customer field or fleet_customer field
      const customerName = item.customer || item.fleet_customer;
      if (customerName && typeof customerName === 'string' && customerName.trim() !== '') {
        uniqueCustomerNames.add(customerName.trim());
      }
    });

    // Convert Set to Array
    const customerNames = Array.from(uniqueCustomerNames);

    // Track statistics
    let addedCount = 0;
    let skippedCount = 0;

    // Create new customers for each unique name that doesn't already exist
    customerNames.forEach(name => {
      // Check if customer with this name already exists
      const exists = existingCustomers.some(
        customer => customer.name.toLowerCase() === name.toLowerCase()
      );

      if (!exists) {
        // Create a new customer with only the name field populated
        const customerData: CustomerFormData = {
          name: name,
          email: `${name.replace(/\s+/g, '.').toLowerCase()}@example.com`, // Generate placeholder email
          phone: '',
          address: '',
          company: name, // Use name as company name too
          notes: 'Imported from Fleetlist'
        };

        // createCustomer will save to localStorage automatically
        createCustomer(customerData);
        addedCount++;
      } else {
        skippedCount++;
      }
    });

    // Final save to localStorage to ensure all changes are persisted
    if (addedCount > 0) {
      saveCustomersToStorage(customers);
    }

    return {
      added: addedCount,
      skipped: skippedCount,
      customerNames: customerNames
    };
  } catch (error) {
    console.error('Error importing customers from Fleetlist:', error);
    throw error;
  }
};
