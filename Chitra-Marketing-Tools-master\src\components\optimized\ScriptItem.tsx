import React, { memo } from 'react';
import { SavedVideoScript } from '../../types/videoScript';
import { Badge } from '../ui/badge';
import { formatDate } from '../../utils/dateUtils';
import {
  getVideoTypeName,
  getPurposeName,
  getPlatformName,
  getTargetAudienceName
} from '../../utils/videoScriptUtils';
import { MoreHorizontal, Edit, Eye, Download, Trash2 } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { Button } from '../ui/button';

interface ScriptItemProps {
  script: SavedVideoScript;
  onViewScript: (script: SavedVideoScript) => void;
  onEditScript: (script: SavedVideoScript) => void;
  onDeleteScript: (script: SavedVideoScript) => void;
  onExportPDF: (script: SavedVideoScript) => void;
}

/**
 * Optimized script item component using React.memo to prevent unnecessary re-renders
 */
const ScriptItem = memo(({
  script,
  onViewScript,
  onEditScript,
  onDeleteScript,
  onExportPDF
}: ScriptItemProps) => {
  return (
    <tr className="border-b hover:bg-gray-50">
      <td className="px-4 py-3">
        <div className="font-medium">{script.title}</div>
        <div className="text-sm text-gray-500">
          {formatDate(script.createdAt)}
        </div>
      </td>
      <td className="px-4 py-3">
        <div className="flex flex-wrap gap-1">
          <Badge variant="outline">{getVideoTypeName(script.videoType)}</Badge>
          <Badge variant="outline">{getPurposeName(script.purpose)}</Badge>
          <Badge variant="outline">{getPlatformName(script.platform)}</Badge>
        </div>
      </td>
      <td className="px-4 py-3">
        {script.productName || '-'}
      </td>
      <td className="px-4 py-3">
        {script.totalDuration || '-'}
      </td>
      <td className="px-4 py-3">
        <div className="flex items-center justify-end gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => onViewScript(script)}
            title="Lihat"
          >
            <Eye className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            onClick={() => onEditScript(script)}
            title="Edit"
          >
            <Edit className="h-4 w-4" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <div className="px-2 py-1.5 text-sm font-semibold">Aksi</div>
              <div className="h-px my-1 bg-gray-200" />
              <DropdownMenuItem onClick={() => onViewScript(script)}>
                <Eye className="h-4 w-4 mr-2" />
                Lihat
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEditScript(script)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onExportPDF(script)}>
                <Download className="h-4 w-4 mr-2" />
                Export PDF
              </DropdownMenuItem>
              <div className="h-px my-1 bg-gray-200" />
              <DropdownMenuItem
                className="text-red-600"
                onClick={() => onDeleteScript(script)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Hapus
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </td>
    </tr>
  );
});

ScriptItem.displayName = 'ScriptItem';

export default ScriptItem;
