<template>
    <AppLayout>
        <div class="space-y-6">
            <div class="flex justify-between items-center border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Simulasi Promo Bisnis</h1>
                    <p class="mt-2 text-gray-600">Simulate business promotion scenarios and analyze their impact.</p>
                </div>
                <div class="flex space-x-2">
                    <button
                        @click="saveSimulation"
                        :disabled="selectedProducts.length === 0"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                        <Save class="h-4 w-4 mr-2" />
                        Simpan Simulasi
                    </button>
                </div>
            </div>

            <!-- Tab Navigation -->
            <div class="bg-white rounded-lg shadow">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6">
                        <button
                            v-for="tab in tabs"
                            :key="tab.id"
                            @click="activeTab = tab.id"
                            :class="[
                                'py-4 px-1 border-b-2 font-medium text-sm',
                                activeTab === tab.id
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            ]"
                        >
                            <component :is="tab.icon" class="h-4 w-4 mr-2 inline" />
                            {{ tab.name }}
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="p-6">
                    <!-- Basic Simulation Tab -->
                    <div v-if="activeTab === 'basic'" class="space-y-6">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Left Column: Configuration -->
                            <div class="lg:col-span-2 space-y-6">
                                <!-- Promo Configuration -->
                                <div class="bg-gray-50 p-6 rounded-lg">
                                    <div class="flex items-center mb-4">
                                        <Settings class="h-5 w-5 text-purple-600 mr-2" />
                                        <h3 class="text-lg font-semibold text-gray-900">Konfigurasi Promo</h3>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Nama Promo
                                            </label>
                                            <input
                                                v-model="promoConfig.name"
                                                type="text"
                                                placeholder="Masukkan nama promo..."
                                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                            />
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Tipe Promo
                                            </label>
                                            <select
                                                v-model="promoConfig.type"
                                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                            >
                                                <option value="percentage">Diskon Persentase</option>
                                                <option value="fixed">Diskon Nominal</option>
                                                <option value="bundle">Bundle Package</option>
                                                <option value="buy_get">Buy X Get Y</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Tanggal Mulai
                                            </label>
                                            <input
                                                v-model="promoConfig.startDate"
                                                type="date"
                                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                            />
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Tanggal Berakhir
                                            </label>
                                            <input
                                                v-model="promoConfig.endDate"
                                                type="date"
                                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                            />
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Region
                                            </label>
                                            <select
                                                v-model="promoConfig.region"
                                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                            >
                                                <option value="">Semua Region</option>
                                                <option value="jakarta">Jakarta</option>
                                                <option value="surabaya">Surabaya</option>
                                                <option value="bandung">Bandung</option>
                                                <option value="medan">Medan</option>
                                                <option value="makassar">Makassar</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Target Customer
                                            </label>
                                            <select
                                                v-model="promoConfig.customerId"
                                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                            >
                                                <option value="">Semua Customer</option>
                                                <option value="vip">VIP Customer</option>
                                                <option value="regular">Regular Customer</option>
                                                <option value="new">New Customer</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Product Selection -->
                                <div class="bg-gray-50 p-6 rounded-lg">
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center">
                                            <Package class="h-5 w-5 text-green-600 mr-2" />
                                            <h3 class="text-lg font-semibold text-gray-900">Pilih Produk</h3>
                                        </div>
                                        <button
                                            @click="addProduct"
                                            class="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm flex items-center"
                                        >
                                            <Plus class="h-4 w-4 mr-1" />
                                            Tambah Produk
                                        </button>
                                    </div>

                                    <div v-if="selectedProducts.length === 0" class="text-center py-8 text-gray-500">
                                        <Package class="h-12 w-12 mx-auto text-gray-300 mb-2" />
                                        <p>Belum ada produk yang dipilih</p>
                                        <p class="text-sm">Klik "Tambah Produk" untuk memulai</p>
                                    </div>

                                    <div v-else class="space-y-3">
                                        <div
                                            v-for="(product, index) in selectedProducts"
                                            :key="index"
                                            class="flex items-center justify-between p-4 bg-white rounded-lg border"
                                        >
                                            <div class="flex-1">
                                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                                    <div>
                                                        <label class="block text-xs font-medium text-gray-500 mb-1">Produk</label>
                                                        <select
                                                            v-model="product.productId"
                                                            @change="updateProductDetails(index)"
                                                            class="w-full p-2 border border-gray-300 rounded text-sm"
                                                        >
                                                            <option value="">Pilih produk...</option>
                                                            <option
                                                                v-for="p in availableProducts"
                                                                :key="p.id"
                                                                :value="p.id"
                                                            >
                                                                {{ p.name }}
                                                            </option>
                                                        </select>
                                                    </div>
                                                    <div>
                                                        <label class="block text-xs font-medium text-gray-500 mb-1">Quantity</label>
                                                        <input
                                                            v-model.number="product.quantity"
                                                            type="number"
                                                            min="1"
                                                            class="w-full p-2 border border-gray-300 rounded text-sm"
                                                        />
                                                    </div>
                                                    <div>
                                                        <label class="block text-xs font-medium text-gray-500 mb-1">Harga Satuan</label>
                                                        <input
                                                            v-model.number="product.price"
                                                            type="number"
                                                            class="w-full p-2 border border-gray-300 rounded text-sm"
                                                        />
                                                    </div>
                                                    <div>
                                                        <label class="block text-xs font-medium text-gray-500 mb-1">Total</label>
                                                        <div class="p-2 bg-gray-100 rounded text-sm font-semibold">
                                                            {{ formatCurrency(product.quantity * product.price) }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <button
                                                @click="removeProduct(index)"
                                                class="ml-4 p-2 text-red-600 hover:bg-red-50 rounded"
                                            >
                                                <Trash2 class="h-4 w-4" />
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Discount Configuration -->
                                <div class="bg-gray-50 p-6 rounded-lg">
                                    <div class="flex items-center mb-4">
                                        <Percent class="h-5 w-5 text-orange-600 mr-2" />
                                        <h3 class="text-lg font-semibold text-gray-900">Konfigurasi Diskon</h3>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Tipe Diskon
                                            </label>
                                            <select
                                                v-model="discountConfig.type"
                                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                            >
                                                <option value="percentage">Persentase (%)</option>
                                                <option value="fixed">Nominal (IDR)</option>
                                            </select>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Nilai Diskon
                                            </label>
                                            <input
                                                v-model.number="discountConfig.value"
                                                type="number"
                                                :min="0"
                                                :max="discountConfig.type === 'percentage' ? 100 : undefined"
                                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                            />
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                                Target Sales
                                            </label>
                                            <input
                                                v-model.number="discountConfig.targetSales"
                                                type="number"
                                                min="0"
                                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column: Results -->
                            <div class="lg:col-span-1">
                                <div class="bg-white border rounded-lg p-6 sticky top-6">
                                    <div class="flex items-center mb-4">
                                        <BarChart3 class="h-5 w-5 text-blue-600 mr-2" />
                                        <h3 class="text-lg font-semibold text-gray-900">Hasil Simulasi</h3>
                                    </div>

                                    <div v-if="selectedProducts.length === 0" class="text-center py-8 text-gray-500">
                                        <BarChart3 class="h-12 w-12 mx-auto text-gray-300 mb-2" />
                                        <p>Pilih produk untuk melihat hasil simulasi</p>
                                    </div>

                                    <div v-else class="space-y-4">
                                        <!-- Summary Cards -->
                                        <div class="grid grid-cols-1 gap-3">
                                            <div class="bg-blue-50 p-3 rounded-lg">
                                                <div class="text-xs font-medium text-blue-600">Total Sebelum Diskon</div>
                                                <div class="text-lg font-bold text-blue-900">{{ formatCurrency(simulationResult.totalBeforeDiscount) }}</div>
                                            </div>

                                            <div class="bg-red-50 p-3 rounded-lg">
                                                <div class="text-xs font-medium text-red-600">Total Diskon</div>
                                                <div class="text-lg font-bold text-red-900">{{ formatCurrency(simulationResult.totalDiscount) }}</div>
                                            </div>

                                            <div class="bg-green-50 p-3 rounded-lg">
                                                <div class="text-xs font-medium text-green-600">Total Setelah Diskon</div>
                                                <div class="text-lg font-bold text-green-900">{{ formatCurrency(simulationResult.totalAfterDiscount) }}</div>
                                            </div>

                                            <div class="bg-purple-50 p-3 rounded-lg">
                                                <div class="text-xs font-medium text-purple-600">Estimasi Profit</div>
                                                <div class="text-lg font-bold text-purple-900">{{ formatCurrency(simulationResult.estimatedProfit) }}</div>
                                            </div>
                                        </div>

                                        <!-- Detailed Breakdown -->
                                        <div class="border-t pt-4">
                                            <h4 class="text-sm font-medium text-gray-700 mb-3">Detail Produk</h4>
                                            <div class="space-y-2">
                                                <div
                                                    v-for="(product, index) in selectedProducts"
                                                    :key="index"
                                                    class="text-xs"
                                                >
                                                    <div class="flex justify-between">
                                                        <span class="text-gray-600">{{ getProductName(product.productId) }}</span>
                                                        <span class="font-medium">{{ formatCurrency(product.quantity * product.price) }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="border-t pt-4 space-y-2">
                                            <button
                                                @click="calculateSimulation"
                                                :disabled="isCalculating"
                                                class="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center text-sm"
                                            >
                                                <Loader2 v-if="isCalculating" class="h-4 w-4 mr-2 animate-spin" />
                                                <Calculator v-else class="h-4 w-4 mr-2" />
                                                {{ isCalculating ? 'Menghitung...' : 'Hitung Ulang' }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- A/B Testing Tab -->
                    <div v-if="activeTab === 'ab-testing'" class="space-y-6">
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <GitCompare class="h-5 w-5 text-indigo-600 mr-2" />
                                    <h3 class="text-lg font-semibold text-gray-900">Perbandingan Skenario A/B</h3>
                                </div>
                                <button
                                    @click="addScenario"
                                    :disabled="scenarios.length >= 3"
                                    class="px-3 py-1 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm flex items-center disabled:opacity-50"
                                >
                                    <Plus class="h-4 w-4 mr-1" />
                                    Tambah Skenario
                                </button>
                            </div>

                            <div v-if="scenarios.length === 0" class="text-center py-8 text-gray-500">
                                <GitCompare class="h-12 w-12 mx-auto text-gray-300 mb-2" />
                                <p>Belum ada skenario untuk dibandingkan</p>
                                <p class="text-sm">Buat skenario pertama di tab "Simulasi Dasar"</p>
                            </div>

                            <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <div
                                    v-for="(scenario, index) in scenarios"
                                    :key="scenario.id"
                                    class="bg-white p-4 rounded-lg border"
                                >
                                    <div class="flex items-center justify-between mb-3">
                                        <h4 class="font-semibold text-gray-900">{{ scenario.name }}</h4>
                                        <button
                                            @click="removeScenario(index)"
                                            class="text-red-600 hover:bg-red-50 p-1 rounded"
                                        >
                                            <Trash2 class="h-4 w-4" />
                                        </button>
                                    </div>

                                    <div class="space-y-3">
                                        <div>
                                            <label class="block text-xs font-medium text-gray-500 mb-1">Diskon</label>
                                            <div class="flex space-x-2">
                                                <input
                                                    v-model.number="scenario.discountValue"
                                                    type="number"
                                                    class="flex-1 p-2 border border-gray-300 rounded text-sm"
                                                    @input="updateScenario(index)"
                                                />
                                                <select
                                                    v-model="scenario.discountType"
                                                    class="p-2 border border-gray-300 rounded text-sm"
                                                    @change="updateScenario(index)"
                                                >
                                                    <option value="percentage">%</option>
                                                    <option value="fixed">IDR</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div>
                                            <label class="block text-xs font-medium text-gray-500 mb-1">Target Sales</label>
                                            <input
                                                v-model.number="scenario.targetSales"
                                                type="number"
                                                class="w-full p-2 border border-gray-300 rounded text-sm"
                                                @input="updateScenario(index)"
                                            />
                                        </div>

                                        <!-- Results -->
                                        <div class="border-t pt-3 space-y-2">
                                            <div class="flex justify-between text-xs">
                                                <span class="text-gray-600">Total Diskon:</span>
                                                <span class="font-medium text-red-600">{{ formatCurrency(scenario.result.totalDiscount) }}</span>
                                            </div>
                                            <div class="flex justify-between text-xs">
                                                <span class="text-gray-600">Setelah Diskon:</span>
                                                <span class="font-medium text-green-600">{{ formatCurrency(scenario.result.totalAfterDiscount) }}</span>
                                            </div>
                                            <div class="flex justify-between text-xs">
                                                <span class="text-gray-600">Est. Profit:</span>
                                                <span class="font-medium text-blue-600">{{ formatCurrency(scenario.result.estimatedProfit) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Comparison Summary -->
                            <div v-if="scenarios.length > 1" class="mt-6 bg-white p-4 rounded-lg border">
                                <h4 class="font-semibold text-gray-900 mb-3">Perbandingan Hasil</h4>
                                <div class="overflow-x-auto">
                                    <table class="w-full text-sm">
                                        <thead>
                                            <tr class="border-b">
                                                <th class="text-left py-2">Skenario</th>
                                                <th class="text-right py-2">Diskon</th>
                                                <th class="text-right py-2">Total Setelah Diskon</th>
                                                <th class="text-right py-2">Est. Profit</th>
                                                <th class="text-right py-2">ROI</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr
                                                v-for="scenario in scenarios"
                                                :key="scenario.id"
                                                class="border-b"
                                            >
                                                <td class="py-2 font-medium">{{ scenario.name }}</td>
                                                <td class="py-2 text-right">
                                                    {{ scenario.discountValue }}{{ scenario.discountType === 'percentage' ? '%' : '' }}
                                                </td>
                                                <td class="py-2 text-right">{{ formatCurrency(scenario.result.totalAfterDiscount) }}</td>
                                                <td class="py-2 text-right">{{ formatCurrency(scenario.result.estimatedProfit) }}</td>
                                                <td class="py-2 text-right">
                                                    {{ ((scenario.result.estimatedProfit / scenario.result.totalAfterDiscount) * 100).toFixed(1) }}%
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status & Library Tab -->
                    <div v-if="activeTab === 'library'" class="space-y-6">
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <div class="flex items-center mb-4">
                                <Library class="h-5 w-5 text-green-600 mr-2" />
                                <h3 class="text-lg font-semibold text-gray-900">Library Simulasi</h3>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Save as Template -->
                                <div class="bg-white p-4 rounded-lg border">
                                    <h4 class="font-semibold text-gray-900 mb-3">Simpan sebagai Template</h4>
                                    <div class="space-y-3">
                                        <input
                                            v-model="templateName"
                                            type="text"
                                            placeholder="Nama template..."
                                            class="w-full p-2 border border-gray-300 rounded text-sm"
                                        />
                                        <button
                                            @click="saveAsTemplate"
                                            :disabled="!templateName || selectedProducts.length === 0"
                                            class="w-full py-2 px-4 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 text-sm"
                                        >
                                            Simpan Template
                                        </button>
                                    </div>
                                </div>

                                <!-- Load Template -->
                                <div class="bg-white p-4 rounded-lg border">
                                    <h4 class="font-semibold text-gray-900 mb-3">Template Tersimpan</h4>
                                    <div v-if="savedTemplates.length === 0" class="text-center py-4 text-gray-500 text-sm">
                                        Belum ada template tersimpan
                                    </div>
                                    <div v-else class="space-y-2">
                                        <div
                                            v-for="template in savedTemplates"
                                            :key="template.id"
                                            class="flex items-center justify-between p-2 bg-gray-50 rounded text-sm"
                                        >
                                            <span>{{ template.name }}</span>
                                            <div class="flex space-x-1">
                                                <button
                                                    @click="loadTemplate(template)"
                                                    class="px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
                                                >
                                                    Load
                                                </button>
                                                <button
                                                    @click="deleteTemplate(template.id)"
                                                    class="px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700"
                                                >
                                                    Del
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Settings,
    Package,
    Plus,
    Trash2,
    Percent,
    BarChart3,
    Calculator,
    Loader2,
    Save,
    GitCompare,
    Library
} from 'lucide-vue-next';
import { ref, computed, watch } from 'vue';

// Types
interface Product {
    id: string;
    name: string;
    price: number;
}

interface SelectedProduct {
    productId: string;
    quantity: number;
    price: number;
}

interface PromoConfig {
    name: string;
    type: string;
    startDate: string;
    endDate: string;
    region: string;
    customerId: string;
}

interface DiscountConfig {
    type: string;
    value: number;
    targetSales: number;
}

interface SimulationResult {
    totalBeforeDiscount: number;
    totalDiscount: number;
    totalAfterDiscount: number;
    estimatedProfit: number;
}

interface Scenario {
    id: string;
    name: string;
    discountType: string;
    discountValue: number;
    targetSales: number;
    result: SimulationResult;
}

interface Template {
    id: string;
    name: string;
    config: PromoConfig;
    products: SelectedProduct[];
    discount: DiscountConfig;
}

// Tab configuration
const tabs = ref([
    { id: 'basic', name: 'Simulasi Dasar', icon: Calculator },
    { id: 'ab-testing', name: 'A/B Testing', icon: GitCompare },
    { id: 'library', name: 'Library', icon: Library }
]);

// Reactive state
const activeTab = ref('basic');
const isCalculating = ref(false);
const templateName = ref('');

// Promo configuration
const promoConfig = ref<PromoConfig>({
    name: '',
    type: 'percentage',
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    region: '',
    customerId: ''
});

// Available products
const availableProducts = ref<Product[]>([
    { id: 'ban-27-00-r-49', name: '27.00 R 49 XD GRIP B E4T TL **', price: 212874175 },
    { id: 'ban-24-00-r-35', name: '24.00 R 35 XD GRIP B E4T TL **', price: 185000000 },
    { id: 'oli-engine', name: 'Engine Oil SAE 15W-40 (20L)', price: 850000 },
    { id: 'filter-air', name: 'Air Filter Heavy Duty', price: 450000 },
    { id: 'filter-oli', name: 'Oil Filter Premium', price: 320000 },
    { id: 'coolant', name: 'Coolant Radiator (5L)', price: 275000 }
]);

// Selected products
const selectedProducts = ref<SelectedProduct[]>([]);

// Discount configuration
const discountConfig = ref<DiscountConfig>({
    type: 'percentage',
    value: 10,
    targetSales: 1000000000
});

// A/B Testing scenarios
const scenarios = ref<Scenario[]>([]);

// Saved templates
const savedTemplates = ref<Template[]>([]);

// Utility function to format currency
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

// Get product name by ID
const getProductName = (productId: string): string => {
    const product = availableProducts.value.find(p => p.id === productId);
    return product ? product.name : 'Unknown Product';
};

// Calculate simulation result
const simulationResult = computed<SimulationResult>(() => {
    if (selectedProducts.value.length === 0) {
        return {
            totalBeforeDiscount: 0,
            totalDiscount: 0,
            totalAfterDiscount: 0,
            estimatedProfit: 0
        };
    }

    const totalBeforeDiscount = selectedProducts.value.reduce((sum, product) => {
        return sum + (product.quantity * product.price);
    }, 0);

    let totalDiscount = 0;
    if (discountConfig.value.type === 'percentage') {
        totalDiscount = totalBeforeDiscount * (discountConfig.value.value / 100);
    } else {
        totalDiscount = discountConfig.value.value;
    }

    const totalAfterDiscount = totalBeforeDiscount - totalDiscount;

    // Estimate profit (assuming 20% base margin)
    const baseCost = totalBeforeDiscount * 0.8;
    const estimatedProfit = totalAfterDiscount - baseCost;

    return {
        totalBeforeDiscount,
        totalDiscount,
        totalAfterDiscount,
        estimatedProfit
    };
});

// Product management functions
const addProduct = () => {
    selectedProducts.value.push({
        productId: '',
        quantity: 1,
        price: 0
    });
};

const removeProduct = (index: number) => {
    selectedProducts.value.splice(index, 1);
};

const updateProductDetails = (index: number) => {
    const product = selectedProducts.value[index];
    const productData = availableProducts.value.find(p => p.id === product.productId);
    if (productData) {
        product.price = productData.price;
    }
};

// Simulation functions
const calculateSimulation = async () => {
    isCalculating.value = true;

    try {
        // Simulate calculation delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // The result is automatically calculated via computed property
        console.log('Simulation calculated:', simulationResult.value);

    } catch (error) {
        console.error('Error calculating simulation:', error);
        alert('Error occurred during calculation. Please try again.');
    } finally {
        isCalculating.value = false;
    }
};

const saveSimulation = () => {
    if (selectedProducts.value.length === 0) {
        alert('Silakan pilih produk terlebih dahulu sebelum menyimpan simulasi.');
        return;
    }

    const simulation = {
        id: Date.now().toString(),
        config: { ...promoConfig.value },
        products: [...selectedProducts.value],
        discount: { ...discountConfig.value },
        result: { ...simulationResult.value },
        createdAt: new Date()
    };

    // Save to localStorage (in real app, this would be API call)
    const saved = localStorage.getItem('promoSimulations') || '[]';
    const simulations = JSON.parse(saved);
    simulations.push(simulation);
    localStorage.setItem('promoSimulations', JSON.stringify(simulations));

    alert('Simulasi promo berhasil disimpan!');
};

// A/B Testing functions
const addScenario = () => {
    if (scenarios.value.length >= 3) {
        alert('Maksimal 3 skenario yang dapat dibandingkan.');
        return;
    }

    const scenarioId = `scenario-${scenarios.value.length + 1}`;
    const scenarioName = `Skenario ${String.fromCharCode(65 + scenarios.value.length)}`;

    const newScenario: Scenario = {
        id: scenarioId,
        name: scenarioName,
        discountType: discountConfig.value.type,
        discountValue: discountConfig.value.value + (scenarios.value.length * 5),
        targetSales: discountConfig.value.targetSales,
        result: { ...simulationResult.value }
    };

    scenarios.value.push(newScenario);
    updateScenario(scenarios.value.length - 1);
};

const removeScenario = (index: number) => {
    scenarios.value.splice(index, 1);
};

const updateScenario = (index: number) => {
    const scenario = scenarios.value[index];

    // Recalculate result for this scenario
    const totalBeforeDiscount = selectedProducts.value.reduce((sum, product) => {
        return sum + (product.quantity * product.price);
    }, 0);

    let totalDiscount = 0;
    if (scenario.discountType === 'percentage') {
        totalDiscount = totalBeforeDiscount * (scenario.discountValue / 100);
    } else {
        totalDiscount = scenario.discountValue;
    }

    const totalAfterDiscount = totalBeforeDiscount - totalDiscount;
    const baseCost = totalBeforeDiscount * 0.8;
    const estimatedProfit = totalAfterDiscount - baseCost;

    scenario.result = {
        totalBeforeDiscount,
        totalDiscount,
        totalAfterDiscount,
        estimatedProfit
    };
};

// Template functions
const saveAsTemplate = () => {
    if (!templateName.value || selectedProducts.value.length === 0) {
        alert('Silakan isi nama template dan pilih produk terlebih dahulu.');
        return;
    }

    const template: Template = {
        id: Date.now().toString(),
        name: templateName.value,
        config: { ...promoConfig.value },
        products: [...selectedProducts.value],
        discount: { ...discountConfig.value }
    };

    savedTemplates.value.push(template);

    // Save to localStorage
    localStorage.setItem('promoTemplates', JSON.stringify(savedTemplates.value));

    templateName.value = '';
    alert('Template berhasil disimpan!');
};

const loadTemplate = (template: Template) => {
    if (selectedProducts.value.length > 0 && !confirm('Menggunakan template ini akan menggantikan data yang ada. Lanjutkan?')) {
        return;
    }

    promoConfig.value = { ...template.config };
    selectedProducts.value = [...template.products];
    discountConfig.value = { ...template.discount };

    activeTab.value = 'basic';
    alert('Template berhasil diterapkan!');
};

const deleteTemplate = (templateId: string) => {
    if (!confirm('Yakin ingin menghapus template ini?')) {
        return;
    }

    const index = savedTemplates.value.findIndex(t => t.id === templateId);
    if (index > -1) {
        savedTemplates.value.splice(index, 1);
        localStorage.setItem('promoTemplates', JSON.stringify(savedTemplates.value));
        alert('Template berhasil dihapus!');
    }
};

// Load saved templates on mount
const loadSavedTemplates = () => {
    const saved = localStorage.getItem('promoTemplates');
    if (saved) {
        savedTemplates.value = JSON.parse(saved);
    }
};

// Initialize scenarios when products change
watch(selectedProducts, () => {
    if (scenarios.value.length > 0) {
        scenarios.value.forEach((_, index) => {
            updateScenario(index);
        });
    }
}, { deep: true });

// Load saved data on component mount
loadSavedTemplates();
</script>
