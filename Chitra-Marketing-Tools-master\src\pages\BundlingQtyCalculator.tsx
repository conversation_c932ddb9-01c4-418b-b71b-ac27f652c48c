import React, { useState, useEffect } from 'react';
import { Calculator } from 'lucide-react';
import ProductSelector from '../components/ProductSelector';
import { BundleItem, Product } from '../types';

interface CalculationResult {
  bundlingPrice: number;
  suggestedQty: number;
  marginPercentage: number;
  mainProductCost: number;
  secondaryProductPrice: number;
  totalCost: number;
  availableBudget: number;
  totalCostWithSuggestedQty: number;
  actualMarginAmount: number;
  minimumMainProductQty: number;
}

export default function BundlingQtyCalculator() {
  const [mainProduct, setMainProduct] = useState<BundleItem | null>(null);
  const [secondaryProducts, setSecondaryProducts] = useState<BundleItem[]>([]);
  const [targetMargin, setTargetMargin] = useState<number>(20);
  const [calculationResult, setCalculationResult] = useState<CalculationResult | null>(null);

  // Handle main product selection
  const handleMainProductChange = (products: BundleItem[]) => {
    if (products.length > 0) {
      setMainProduct(products[0]);
    } else {
      setMainProduct(null);
    }
  };

  // Handle secondary products selection
  const handleSecondaryProductsChange = (products: BundleItem[]) => {
    setSecondaryProducts(products);
  };

  // Calculate bundling quantities and pricing
  const calculateBundling = () => {
    if (!mainProduct || secondaryProducts.length === 0) {
      alert('Please select both main product and at least one secondary product');
      return;
    }

    // Get the main product cost
    const mainProductCost = mainProduct.product.price;

    // Get the secondary product price (using the first one for calculation)
    const secondaryProduct = secondaryProducts[0];
    const secondaryProductPrice = secondaryProduct.product.price;
    const secondaryProductQty = secondaryProduct.quantity;

    // Calculate the bundling price with target margin
    const totalCost = mainProductCost + (secondaryProductQty * secondaryProductPrice);
    const bundlingPrice = totalCost * (1 + (targetMargin / 100));
    const roundedBundlingPrice = Math.ceil(bundlingPrice / 1000) * 1000; // Round up to nearest 1000

    // Calculate how many secondary products can be included
    const availableBudget = roundedBundlingPrice - mainProductCost;
    const suggestedQty = Math.floor(availableBudget / secondaryProductPrice);

    // Calculate the actual margin percentage
    const totalCostWithSuggestedQty = mainProductCost + (suggestedQty * secondaryProductPrice);
    const actualMarginAmount = roundedBundlingPrice - totalCostWithSuggestedQty;
    const marginPercentage = (actualMarginAmount / totalCostWithSuggestedQty) * 100;

    // Calculate minimum quantity of main product needed to cover secondary product cost while maintaining target margin
    // Formula: (Secondary Product Price × Quantity) / (Main Product Price × Margin Factor)
    // Where Margin Factor = (1 - (Target Margin / 100))
    const secondaryProductTotalCost = secondaryProductQty * secondaryProductPrice;
    const marginFactor = 1 - (targetMargin / 100); // If target margin is 20%, we need to account for that in our calculation
    const minimumMainProductQty = Math.ceil(secondaryProductTotalCost / (mainProductCost * marginFactor));

    setCalculationResult({
      bundlingPrice: roundedBundlingPrice,
      suggestedQty,
      marginPercentage,
      mainProductCost,
      secondaryProductPrice,
      totalCost,
      availableBudget,
      totalCostWithSuggestedQty,
      actualMarginAmount,
      minimumMainProductQty
    });
  };

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Calculator className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Slow Moving Bundling Calculator</h1>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Main Product Selection */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-medium mb-4">Select Main Product</h2>
          <ProductSelector
            selectedProducts={mainProduct ? [mainProduct] : []}
            onProductsChange={handleMainProductChange}
          />
        </div>

        {/* Secondary Products Selection */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-medium mb-4">Select Secondary Products</h2>
          <ProductSelector
            selectedProducts={secondaryProducts}
            onProductsChange={handleSecondaryProductsChange}
          />
        </div>
      </div>

      {/* Calculation Configuration */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-medium mb-4">Calculation Settings</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Target Margin (%)
            </label>
            <input
              type="number"
              min="0"
              value={targetMargin}
              onChange={(e) => setTargetMargin(Math.max(0, Number(e.target.value)))}
              className="w-full p-2 border rounded-md"
            />
            <p className="mt-1 text-sm text-gray-500">
              Set the target profit margin you want to achieve with this bundle
            </p>
          </div>

          <button
            onClick={calculateBundling}
            className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Calculate Bundling
          </button>
        </div>
      </div>

      {/* Calculation Results */}
      {calculationResult && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-medium mb-4">Calculation Results</h2>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-blue-800">Bundling Price</h3>
                <p className="text-2xl font-bold text-blue-900">
                  {formatCurrency(calculationResult.bundlingPrice)}
                </p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-green-800">Suggested Secondary Qty</h3>
                <p className="text-2xl font-bold text-green-900">
                  {calculationResult.suggestedQty} units
                </p>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-orange-800">Min Main Product Qty</h3>
                <p className="text-2xl font-bold text-orange-900">
                  {calculationResult.minimumMainProductQty} units
                </p>
                <p className="text-xs text-orange-700 mt-1">To cover secondary product cost with {targetMargin}% margin</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-purple-800">Margin Percentage</h3>
                <p className="text-2xl font-bold text-purple-900">
                  {calculationResult.marginPercentage.toFixed(2)}%
                </p>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Bundling Summary</h3>
              {mainProduct && (
                <div className="mb-2">
                  <span className="font-medium">Main Product:</span> {mainProduct.product.materialDescription} - {formatCurrency(mainProduct.product.price)}
                </div>
              )}
              {secondaryProducts.length > 0 && (
                <div>
                  <span className="font-medium">Secondary Product:</span> {secondaryProducts[0].product.materialDescription} - {formatCurrency(secondaryProducts[0].product.price)} x {calculationResult.suggestedQty}
                </div>
              )}
              <div className="mt-4 space-y-3">
                <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                  <p className="text-sm text-yellow-800">
                    <span className="font-medium">Bundling Recommendation:</span> Bundle {mainProduct?.product.materialDescription} with {calculationResult.suggestedQty} units of {secondaryProducts[0]?.product.materialDescription} at {formatCurrency(calculationResult.bundlingPrice)} to achieve a {calculationResult.marginPercentage.toFixed(2)}% margin.
                  </p>
                </div>
                <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <p className="text-sm text-orange-800">
                    <span className="font-medium">Minimum Main Product Requirement:</span> You need to sell at least {calculationResult.minimumMainProductQty} units of {mainProduct?.product.materialDescription} to cover the cost of {secondaryProducts[0]?.quantity} units of {secondaryProducts[0]?.product.materialDescription} while maintaining your target margin of {targetMargin}%.
                  </p>
                </div>

                <div className="p-4 bg-green-50 rounded-lg border border-green-200 mt-3">
                  <h3 className="font-medium text-green-800 mb-2">Interpretasi Bisnis & Contoh Promosi</h3>
                  <p className="text-sm text-green-700 mb-2">
                    Jika Anda jual {calculationResult.minimumMainProductQty} {mainProduct?.product.materialDescription} dengan margin {targetMargin}%, maka Anda bisa bundling {secondaryProducts[0]?.quantity} {secondaryProducts[0]?.product.materialDescription} secara "gratis" atau promo, tanpa merusak margin target.
                  </p>
                  <div className="bg-white p-3 rounded-lg border border-green-100">
                    <p className="text-base font-bold text-green-800 text-center">
                      "Beli {calculationResult.minimumMainProductQty} {mainProduct?.product.materialDescription}, GRATIS {secondaryProducts[0]?.quantity} {secondaryProducts[0]?.product.materialDescription}!"
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Detailed Calculation Steps */}
            <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <div className="bg-gray-100 px-4 py-2 border-b">
                <h3 className="font-medium text-gray-800">Detailed Calculation Steps</h3>
              </div>
              <div className="p-4 space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Step 1: Identify Product Costs</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">Main Product Cost: {formatCurrency(calculationResult.mainProductCost)}</p>
                    <p className="text-sm text-gray-600">Secondary Product Price: {formatCurrency(calculationResult.secondaryProductPrice)} per unit</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Step 2: Calculate Initial Bundle Cost</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">Initial Bundle Cost = Main Product Cost + (Secondary Product Price × Initial Quantity)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductCost)} + ({formatCurrency(calculationResult.secondaryProductPrice)} × {secondaryProducts[0]?.quantity || 1})</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.totalCost)}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Step 3: Apply Target Margin ({targetMargin}%)</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">Bundling Price = Initial Bundle Cost × (1 + Target Margin/100)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.totalCost)} × (1 + {targetMargin}/100)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.totalCost)} × {(1 + targetMargin/100).toFixed(2)}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.totalCost * (1 + targetMargin/100))}</p>
                    <p className="text-sm text-gray-600">Rounded to: {formatCurrency(calculationResult.bundlingPrice)}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Step 4: Calculate Available Budget for Secondary Products</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">Available Budget = Bundling Price - Main Product Cost</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.bundlingPrice)} - {formatCurrency(calculationResult.mainProductCost)}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.availableBudget)}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Step 5: Determine Optimal Quantity of Secondary Products</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">Suggested Quantity = Available Budget ÷ Secondary Product Price</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.availableBudget)} ÷ {formatCurrency(calculationResult.secondaryProductPrice)}</p>
                    <p className="text-sm text-gray-600">= {(calculationResult.availableBudget / calculationResult.secondaryProductPrice).toFixed(2)} units</p>
                    <p className="text-sm text-gray-600">Rounded down to: {calculationResult.suggestedQty} units</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Step 6: Calculate Final Costs and Margin</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">Total Cost with Suggested Quantity = Main Product Cost + (Secondary Product Price × Suggested Quantity)</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductCost)} + ({formatCurrency(calculationResult.secondaryProductPrice)} × {calculationResult.suggestedQty})</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.totalCostWithSuggestedQty)}</p>
                    <p className="text-sm text-gray-600 mt-2">Actual Margin Amount = Bundling Price - Total Cost with Suggested Quantity</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.bundlingPrice)} - {formatCurrency(calculationResult.totalCostWithSuggestedQty)}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.actualMarginAmount)}</p>
                    <p className="text-sm text-gray-600 mt-2">Margin Percentage = (Actual Margin Amount ÷ Total Cost with Suggested Quantity) × 100%</p>
                    <p className="text-sm text-gray-600">= ({formatCurrency(calculationResult.actualMarginAmount)} ÷ {formatCurrency(calculationResult.totalCostWithSuggestedQty)}) × 100%</p>
                    <p className="text-sm text-gray-600">= {calculationResult.marginPercentage.toFixed(2)}%</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-700">Step 7: Calculate Minimum Main Product Quantity</h4>
                  <div className="pl-4 border-l-2 border-gray-200">
                    <p className="text-sm text-gray-600">This calculation determines how many units of the main product would be needed to cover the cost of the secondary products while maintaining your target margin of {targetMargin}%.</p>

                    <p className="text-sm text-gray-600 mt-2">Total Secondary Product Cost = Secondary Product Price × Secondary Product Quantity</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.secondaryProductPrice)} × {secondaryProducts[0]?.quantity || 1}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(secondaryProducts[0]?.quantity * calculationResult.secondaryProductPrice || calculationResult.secondaryProductPrice)}</p>

                    <p className="text-sm text-gray-600 mt-2">Margin Factor = 1 - (Target Margin ÷ 100)</p>
                    <p className="text-sm text-gray-600">= 1 - ({targetMargin} ÷ 100)</p>
                    <p className="text-sm text-gray-600">= {(1 - targetMargin/100).toFixed(2)}</p>

                    <p className="text-sm text-gray-600 mt-2">Effective Main Product Revenue = Main Product Cost × Margin Factor</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductCost)} × {(1 - targetMargin/100).toFixed(2)}</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(calculationResult.mainProductCost * (1 - targetMargin/100))}</p>

                    <p className="text-sm text-gray-600 mt-2">Minimum Main Product Quantity = Total Secondary Product Cost ÷ Effective Main Product Revenue</p>
                    <p className="text-sm text-gray-600">= {formatCurrency(secondaryProducts[0]?.quantity * calculationResult.secondaryProductPrice || calculationResult.secondaryProductPrice)} ÷ {formatCurrency(calculationResult.mainProductCost * (1 - targetMargin/100))}</p>
                    <p className="text-sm text-gray-600">= {(secondaryProducts[0]?.quantity * calculationResult.secondaryProductPrice / (calculationResult.mainProductCost * (1 - targetMargin/100))).toFixed(2)} units</p>
                    <p className="text-sm text-gray-600">Rounded up to: {calculationResult.minimumMainProductQty} units</p>

                    <p className="text-sm text-gray-600 mt-2 text-orange-700 font-medium">Interpretation: You would need to sell at least {calculationResult.minimumMainProductQty} units of {mainProduct?.product.materialDescription} to cover the cost of {secondaryProducts[0]?.quantity} units of {secondaryProducts[0]?.product.materialDescription} while maintaining your target margin of {targetMargin}%.</p>

                    <div className="mt-4 p-3 bg-green-50 rounded-lg">
                      <p className="text-sm font-medium text-green-800">Interpretasi Bisnis:</p>
                      <ul className="mt-1 text-sm text-green-700 list-disc pl-5 space-y-1">
                        <li>Dengan menjual {calculationResult.minimumMainProductQty} unit {mainProduct?.product.materialDescription}, Anda bisa menutupi biaya {secondaryProducts[0]?.quantity} unit {secondaryProducts[0]?.product.materialDescription} sambil tetap mempertahankan margin {targetMargin}%.</li>
                        <li>Ini memungkinkan Anda membuat promosi bundling yang menarik seperti "Beli {calculationResult.minimumMainProductQty}, Gratis {secondaryProducts[0]?.quantity}" tanpa mengorbankan profitabilitas.</li>
                        <li>Strategi ini efektif untuk meningkatkan volume penjualan produk utama dan menghabiskan stok produk sekunder.</li>
                        <li>Pelanggan merasa mendapatkan nilai lebih dengan "produk gratis", sementara Anda tetap mencapai target margin keuntungan.</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
