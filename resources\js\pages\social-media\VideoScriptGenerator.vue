<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Video Script Generator</h1>
                    <p class="mt-2 text-gray-600">AI-powered video script generation for social media content</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="saveScript"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center"
                    >
                        <Save class="h-4 w-4 mr-2" />
                        Save Script
                    </button>
                    <button
                        @click="exportScript"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export
                    </button>
                </div>
            </div>

            <!-- Script Configuration -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Video Configuration</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Video Type</label>
                        <select 
                            v-model="scriptConfig.videoType" 
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="product-demo">Product Demo</option>
                            <option value="educational">Educational</option>
                            <option value="testimonial">Customer Testimonial</option>
                            <option value="behind-scenes">Behind the Scenes</option>
                            <option value="promotional">Promotional</option>
                            <option value="tutorial">Tutorial</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Duration</label>
                        <select 
                            v-model="scriptConfig.duration" 
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="15">15 seconds (Instagram Reel)</option>
                            <option value="30">30 seconds (TikTok/Instagram)</option>
                            <option value="60">1 minute (Facebook/Instagram)</option>
                            <option value="120">2 minutes (YouTube Shorts)</option>
                            <option value="300">5 minutes (YouTube)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Target Platform</label>
                        <select 
                            v-model="scriptConfig.platform" 
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="instagram">Instagram</option>
                            <option value="tiktok">TikTok</option>
                            <option value="youtube">YouTube</option>
                            <option value="facebook">Facebook</option>
                            <option value="linkedin">LinkedIn</option>
                        </select>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Topic/Product</label>
                        <input 
                            v-model="scriptConfig.topic" 
                            type="text" 
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="e.g., 27.00 R 49 XD GRIP Tire"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Target Audience</label>
                        <select 
                            v-model="scriptConfig.audience" 
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="mining">Mining Industry</option>
                            <option value="construction">Construction</option>
                            <option value="logistics">Logistics & Transportation</option>
                            <option value="general">General Public</option>
                        </select>
                    </div>
                </div>

                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Key Messages (Optional)</label>
                    <textarea 
                        v-model="scriptConfig.keyMessages" 
                        rows="3"
                        class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter key points you want to highlight in the video..."
                    ></textarea>
                </div>

                <div class="mt-6">
                    <button
                        @click="generateScript"
                        :disabled="isGenerating"
                        class="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                        <Loader2 v-if="isGenerating" class="h-4 w-4 mr-2 animate-spin" />
                        <Sparkles v-else class="h-4 w-4 mr-2" />
                        Generate Script
                    </button>
                </div>
            </div>

            <!-- Generated Script -->
            <div v-if="generatedScript" class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Generated Script</h3>
                    <div class="flex space-x-3">
                        <button
                            @click="regenerateScript"
                            class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center text-sm"
                        >
                            <RefreshCw class="h-4 w-4 mr-2" />
                            Regenerate
                        </button>
                        <button
                            @click="copyScript"
                            class="px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 flex items-center text-sm"
                        >
                            <Copy class="h-4 w-4 mr-2" />
                            Copy
                        </button>
                    </div>
                </div>
                
                <div class="p-6">
                    <!-- Script Metadata -->
                    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-600">Type:</span>
                                <p class="text-gray-900">{{ scriptConfig.videoType }}</p>
                            </div>
                            <div>
                                <span class="font-medium text-gray-600">Duration:</span>
                                <p class="text-gray-900">{{ scriptConfig.duration }} seconds</p>
                            </div>
                            <div>
                                <span class="font-medium text-gray-600">Platform:</span>
                                <p class="text-gray-900">{{ scriptConfig.platform }}</p>
                            </div>
                            <div>
                                <span class="font-medium text-gray-600">Word Count:</span>
                                <p class="text-gray-900">{{ wordCount }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Script Content -->
                    <div class="space-y-6">
                        <!-- Hook -->
                        <div class="border-l-4 border-blue-500 pl-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Hook (0-3 seconds)</h4>
                            <p class="text-gray-700">{{ generatedScript.hook }}</p>
                        </div>

                        <!-- Main Content -->
                        <div class="border-l-4 border-green-500 pl-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Main Content (3-{{ scriptConfig.duration - 5 }} seconds)</h4>
                            <div class="space-y-2">
                                <p v-for="(point, index) in generatedScript.mainPoints" :key="index" class="text-gray-700">
                                    {{ index + 1 }}. {{ point }}
                                </p>
                            </div>
                        </div>

                        <!-- Call to Action -->
                        <div class="border-l-4 border-purple-500 pl-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Call to Action ({{ scriptConfig.duration - 5 }}-{{ scriptConfig.duration }} seconds)</h4>
                            <p class="text-gray-700">{{ generatedScript.cta }}</p>
                        </div>

                        <!-- Visual Cues -->
                        <div class="border-l-4 border-orange-500 pl-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Visual Cues & Directions</h4>
                            <ul class="list-disc list-inside text-gray-700 space-y-1">
                                <li v-for="(cue, index) in generatedScript.visualCues" :key="index">{{ cue }}</li>
                            </ul>
                        </div>

                        <!-- Hashtags -->
                        <div class="border-l-4 border-pink-500 pl-4">
                            <h4 class="font-semibold text-gray-900 mb-2">Suggested Hashtags</h4>
                            <div class="flex flex-wrap gap-2">
                                <span 
                                    v-for="hashtag in generatedScript.hashtags" 
                                    :key="hashtag"
                                    class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm"
                                >
                                    #{{ hashtag }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Script Templates -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Templates</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div 
                        v-for="template in scriptTemplates" 
                        :key="template.id"
                        @click="useTemplate(template)"
                        class="p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-shadow"
                    >
                        <div class="flex items-center mb-3">
                            <component :is="template.icon" class="h-6 w-6 text-blue-600 mr-3" />
                            <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">{{ template.description }}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">{{ template.duration }}s</span>
                            <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">Use Template</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-purple-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-purple-900">Advanced Video Features Coming Soon</h3>
                        <p class="text-purple-700 mt-1">
                            Enhanced video script generation features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-purple-700 mt-2 space-y-1">
                            <li>AI voice-over generation and text-to-speech</li>
                            <li>Automated video editing and scene suggestions</li>
                            <li>Stock footage and music recommendations</li>
                            <li>Multi-language script translation</li>
                            <li>Performance prediction based on script analysis</li>
                            <li>Integration with video editing tools</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Save,
    Download,
    Loader2,
    Sparkles,
    RefreshCw,
    Copy,
    Video,
    FileText,
    Megaphone,
    Info
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Types
interface ScriptConfig {
    videoType: string;
    duration: number;
    platform: string;
    topic: string;
    audience: string;
    keyMessages: string;
}

interface GeneratedScript {
    hook: string;
    mainPoints: string[];
    cta: string;
    visualCues: string[];
    hashtags: string[];
}

interface ScriptTemplate {
    id: string;
    name: string;
    description: string;
    duration: number;
    icon: any;
}

// Reactive state
const isGenerating = ref(false);
const generatedScript = ref<GeneratedScript | null>(null);

const scriptConfig = ref<ScriptConfig>({
    videoType: 'product-demo',
    duration: 30,
    platform: 'instagram',
    topic: '',
    audience: 'mining',
    keyMessages: ''
});

// Script templates
const scriptTemplates = ref<ScriptTemplate[]>([
    {
        id: 'product-demo',
        name: 'Product Demo',
        description: 'Showcase product features and benefits',
        duration: 30,
        icon: Video
    },
    {
        id: 'educational',
        name: 'Educational Content',
        description: 'Teach and inform your audience',
        duration: 60,
        icon: FileText
    },
    {
        id: 'promotional',
        name: 'Promotional Video',
        description: 'Drive sales and conversions',
        duration: 15,
        icon: Megaphone
    }
]);

// Computed properties
const wordCount = computed(() => {
    if (!generatedScript.value) return 0;

    const allText = [
        generatedScript.value.hook,
        ...generatedScript.value.mainPoints,
        generatedScript.value.cta
    ].join(' ');

    return allText.split(' ').length;
});

// Script generation
const generateScript = async () => {
    isGenerating.value = true;

    try {
        // Simulate AI processing
        await new Promise(resolve => setTimeout(resolve, 3000));

        generatedScript.value = generateScriptContent();

    } catch (error) {
        console.error('Error generating script:', error);
        alert('Gagal membuat script. Silakan coba lagi.');
    } finally {
        isGenerating.value = false;
    }
};

const generateScriptContent = (): GeneratedScript => {
    const { videoType, duration, topic, audience } = scriptConfig.value;

    // Generate hook based on video type
    const hooks: Record<string, string[]> = {
        'product-demo': [
            `Tired of tires that can't handle extreme conditions? Meet the ${topic || 'ultimate solution'}.`,
            `What if I told you there's a tire that can revolutionize your ${audience} operations?`,
            `Stop! Before you buy another tire, watch this.`
        ],
        'educational': [
            `Here's what most people don't know about tire maintenance...`,
            `The #1 mistake that's costing you thousands in tire replacement.`,
            `Industry secret: This simple trick extends tire life by 40%.`
        ],
        'promotional': [
            `Limited time offer you can't miss!`,
            `This deal ends in 24 hours - don't wait!`,
            `Exclusive discount for ${audience} professionals only.`
        ]
    };

    const selectedHooks = hooks[videoType] || hooks['product-demo'];
    const hook = selectedHooks[Math.floor(Math.random() * selectedHooks.length)];

    // Generate main points
    const mainPoints = generateMainPoints(videoType, topic, audience);

    // Generate CTA
    const cta = generateCTA(videoType, duration);

    // Generate visual cues
    const visualCues = generateVisualCues(videoType);

    // Generate hashtags
    const hashtags = generateHashtags(topic, audience);

    return {
        hook,
        mainPoints,
        cta,
        visualCues,
        hashtags
    };
};

const generateMainPoints = (videoType: string, topic: string, audience: string): string[] => {
    const pointsMap: Record<string, string[]> = {
        'product-demo': [
            `Built specifically for ${audience} industry with advanced compound technology`,
            `Proven to last 30% longer than standard tires in extreme conditions`,
            `Trusted by leading companies across Indonesia for over 20 years`
        ],
        'educational': [
            `Check tire pressure weekly - underinflation reduces lifespan by 25%`,
            `Rotate tires every 10,000 km to ensure even wear patterns`,
            `Inspect for cuts, cracks, and foreign objects monthly`
        ],
        'promotional': [
            `Get 20% off all ${topic || 'premium tires'} this month only`,
            `Free installation and balancing included with purchase`,
            `Extended warranty coverage for added peace of mind`
        ]
    };

    return pointsMap[videoType] || pointsMap['product-demo'];
};

const generateCTA = (videoType: string, duration: number): string => {
    const ctas: Record<string, string[]> = {
        'product-demo': [
            'Contact us today for a free consultation and quote!',
            'Visit our showroom or call now to learn more!',
            'DM us for exclusive pricing and availability!'
        ],
        'educational': [
            'Follow for more tire maintenance tips!',
            'Save this post and share with your team!',
            'Comment below with your tire questions!'
        ],
        'promotional': [
            'Order now - limited stock available!',
            'Call today before this offer expires!',
            'Visit our website to claim your discount!'
        ]
    };

    const selectedCTAs = ctas[videoType] || ctas['product-demo'];
    return selectedCTAs[Math.floor(Math.random() * selectedCTAs.length)];
};

const generateVisualCues = (videoType: string): string[] => {
    const cuesMap: Record<string, string[]> = {
        'product-demo': [
            'Close-up shots of tire tread pattern and construction',
            'Show tire in action on heavy equipment',
            'Split screen comparison with competitor tires',
            'Display key specifications and certifications'
        ],
        'educational': [
            'Demonstrate proper tire pressure checking technique',
            'Show before/after examples of tire maintenance',
            'Use graphics to highlight key statistics',
            'Include step-by-step visual instructions'
        ],
        'promotional': [
            'Eye-catching discount graphics and animations',
            'Show product lineup with pricing',
            'Include countdown timer for urgency',
            'Display contact information clearly'
        ]
    };

    return cuesMap[videoType] || cuesMap['product-demo'];
};

const generateHashtags = (topic: string, audience: string): string[] => {
    const baseHashtags = ['ChitraTires', 'QualityTires', 'Indonesia'];
    const audienceHashtags: Record<string, string[]> = {
        mining: ['Mining', 'HeavyEquipment', 'MiningTires'],
        construction: ['Construction', 'BuildingIndonesia', 'ConstructionTires'],
        logistics: ['Logistics', 'Transportation', 'TruckTires'],
        general: ['Automotive', 'TireSafety', 'QualityFirst']
    };

    const topicHashtags = topic ? [topic.replace(/\s+/g, '')] : [];
    const selectedAudienceHashtags = audienceHashtags[audience] || audienceHashtags.general;

    return [...baseHashtags, ...selectedAudienceHashtags, ...topicHashtags];
};

// Event handlers
const regenerateScript = () => {
    generateScript();
};

const copyScript = async () => {
    if (!generatedScript.value) return;

    const scriptText = `
HOOK: ${generatedScript.value.hook}

MAIN CONTENT:
${generatedScript.value.mainPoints.map((point, index) => `${index + 1}. ${point}`).join('\n')}

CALL TO ACTION: ${generatedScript.value.cta}

VISUAL CUES:
${generatedScript.value.visualCues.map(cue => `• ${cue}`).join('\n')}

HASHTAGS: ${generatedScript.value.hashtags.map(tag => `#${tag}`).join(' ')}
    `.trim();

    try {
        await navigator.clipboard.writeText(scriptText);
        alert('Script copied to clipboard!');
    } catch (error) {
        console.error('Error copying script:', error);
        alert('Failed to copy script to clipboard.');
    }
};

const saveScript = () => {
    if (!generatedScript.value) {
        alert('No script to save. Please generate a script first.');
        return;
    }

    try {
        const scriptData = {
            config: scriptConfig.value,
            script: generatedScript.value,
            createdAt: new Date().toISOString()
        };

        localStorage.setItem('video_script', JSON.stringify(scriptData));
        alert('Script saved successfully!');
    } catch (error) {
        console.error('Error saving script:', error);
        alert('Failed to save script.');
    }
};

const exportScript = () => {
    if (!generatedScript.value) {
        alert('No script to export. Please generate a script first.');
        return;
    }

    const scriptText = `
VIDEO SCRIPT - ${scriptConfig.value.topic || 'Untitled'}
Duration: ${scriptConfig.value.duration} seconds
Platform: ${scriptConfig.value.platform}
Type: ${scriptConfig.value.videoType}

HOOK (0-3 seconds):
${generatedScript.value.hook}

MAIN CONTENT (3-${scriptConfig.value.duration - 5} seconds):
${generatedScript.value.mainPoints.map((point, index) => `${index + 1}. ${point}`).join('\n')}

CALL TO ACTION (${scriptConfig.value.duration - 5}-${scriptConfig.value.duration} seconds):
${generatedScript.value.cta}

VISUAL CUES & DIRECTIONS:
${generatedScript.value.visualCues.map(cue => `• ${cue}`).join('\n')}

SUGGESTED HASHTAGS:
${generatedScript.value.hashtags.map(tag => `#${tag}`).join(' ')}

Generated on: ${new Date().toLocaleDateString('id-ID')}
    `.trim();

    const blob = new Blob([scriptText], { type: 'text/plain' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `video_script_${Date.now()}.txt`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

const useTemplate = (template: ScriptTemplate) => {
    scriptConfig.value.videoType = template.id;
    scriptConfig.value.duration = template.duration;
    alert(`Template "${template.name}" applied! You can now customize and generate the script.`);
};

// Initialize on mount
onMounted(() => {
    // Load saved script if exists
    try {
        const savedScript = localStorage.getItem('video_script');
        if (savedScript) {
            const parsedScript = JSON.parse(savedScript);
            scriptConfig.value = { ...scriptConfig.value, ...parsedScript.config };
            generatedScript.value = parsedScript.script;
        }
    } catch (error) {
        console.error('Error loading saved script:', error);
    }
});
</script>
