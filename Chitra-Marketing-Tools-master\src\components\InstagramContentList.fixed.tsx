import React, { useState, useEffect } from 'react';
import {
  SocialMediaPost,
  SocialMediaPlatform,
  ContentType,
  PostStatus
} from '../types/socialMedia';
import {
  getAllSocialMediaPosts,
  deleteSocialMediaPost,
  updateSocialMediaPost
} from '../services/socialMediaService';
import {
  List,
  Instagram,
  Image as ImageIcon,
  Video,
  Layers,
  Clock,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  Trash2,
  Edit,
  Eye,
  Calendar,
  Search,
  Filter,
  X,
  Save,
  Copy,
  RefreshCw,
  ArrowUpDown,
  SortAsc,
  SortDesc,
  Grid,
  Table as TableIcon,
  Sparkles
} from 'lucide-react';
import { useToast } from '../components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../components/ui/alert-dialog";

interface InstagramContentListProps {
  onEditPost?: (post: SocialMediaPost) => void;
  onImproveContent?: (post: SocialMediaPost) => void;
}

export default function InstagramContentList({ onEditPost, onImproveContent }: InstagramContentListProps) {
  // State for posts
  const [posts, setPosts] = useState<SocialMediaPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<SocialMediaPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<PostStatus | 'all'>('all');
  const [contentTypeFilter, setContentTypeFilter] = useState<ContentType | 'all'>('all');

  // State for post view modal
  const [selectedPost, setSelectedPost] = useState<SocialMediaPost | null>(null);
  const [showPostModal, setShowPostModal] = useState(false);

  // State for delete confirmation
  const [postToDelete, setPostToDelete] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // State for view mode
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');

  // State for sorting
  const [sortField, setSortField] = useState<'createdAt' | 'scheduledDate' | 'title'>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [postsPerPage, setPostsPerPage] = useState(10);

  // Toast notifications
  const { toast } = useToast();

  // Load posts function
  const loadPosts = async () => {
    setLoading(true);
    try {
      const allPosts = await getAllSocialMediaPosts();
      // Filter only Instagram posts
      const instagramPosts = allPosts.filter(post => post.platform === SocialMediaPlatform.INSTAGRAM);
      setPosts(instagramPosts);

      // Initialize filtered posts to prevent empty state
      setFilteredPosts(instagramPosts);

      console.log(`Loaded ${instagramPosts.length} Instagram posts`);
    } catch (error) {
      console.error('Error loading posts:', error);
      toast({
        title: "Error",
        description: "Gagal memuat data konten Instagram",
        variant: "destructive"
      });

      // Set empty arrays to prevent undefined errors
      setPosts([]);
      setFilteredPosts([]);
    } finally {
      setLoading(false);
    }
  };

  // Load posts on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Loading Instagram content list data...');
        await loadPosts();
        console.log('Instagram content list data loaded successfully');
      } catch (error) {
        console.error('Error loading Instagram content list data:', error);
        // Set empty arrays to prevent undefined errors
        setPosts([]);
        setFilteredPosts([]);

        toast({
          title: "Error",
          description: "Gagal memuat data konten Instagram. Silakan coba lagi.",
          variant: "destructive"
        });
      }
    };

    fetchData();
  }, []);

  // Refresh posts
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadPosts();
    setRefreshing(false);
    toast({
      title: "Berhasil",
      description: "Data konten Instagram berhasil diperbarui",
    });
  };

  // Apply filters and sorting when dependencies change
  useEffect(() => {
    let filtered = [...posts];

    // Apply search term filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(post =>
        (post.title?.toLowerCase().includes(term) || false) ||
        post.caption.toLowerCase().includes(term) ||
        post.hashtags.some(tag => tag.toLowerCase().includes(term))
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(post => post.status === statusFilter);
    }

    // Apply content type filter
    if (contentTypeFilter !== 'all') {
      filtered = filtered.filter(post => post.contentType === contentTypeFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let valueA, valueB;

      // Handle different sort fields
      if (sortField === 'title') {
        valueA = a.title || '';
        valueB = b.title || '';
      } else if (sortField === 'scheduledDate') {
        valueA = (a.scheduledDate || a.publishedDate || a.createdAt).getTime();
        valueB = (b.scheduledDate || b.publishedDate || b.createdAt).getTime();
      } else {
        // Default to createdAt
        valueA = a.createdAt.getTime();
        valueB = b.createdAt.getTime();
      }

      // Apply sort direction
      if (sortDirection === 'asc') {
        return typeof valueA === 'string'
          ? valueA.localeCompare(valueB as string)
          : valueA - (valueB as number);
      } else {
        return typeof valueA === 'string'
          ? valueB.localeCompare(valueA as string)
          : valueB - (valueA as number);
      }
    });

    setFilteredPosts(filtered);

    // Reset to first page when filters change
    setCurrentPage(1);
  }, [posts, searchTerm, statusFilter, contentTypeFilter, sortField, sortDirection]);

  // Open delete confirmation dialog
  const confirmDelete = (id: string) => {
    setPostToDelete(id);
    setShowDeleteDialog(true);
  };

  // Handle post deletion
  const handleDeletePost = async () => {
    if (!postToDelete) return;

    try {
      await deleteSocialMediaPost(postToDelete);
      setPosts(prevPosts => prevPosts.filter(post => post.id !== postToDelete));

      toast({
        title: "Berhasil",
        description: "Konten berhasil dihapus",
      });
    } catch (error) {
      console.error('Error deleting post:', error);
      toast({
        title: "Error",
        description: "Gagal menghapus konten",
        variant: "destructive"
      });
    } finally {
      setShowDeleteDialog(false);
      setPostToDelete(null);
    }
  };

  // Handle post status change
  const handleStatusChange = async (post: SocialMediaPost, newStatus: PostStatus) => {
    try {
      const updatedPost = await updateSocialMediaPost({
        ...post,
        status: newStatus,
        publishedDate: newStatus === PostStatus.PUBLISHED ? new Date() : post.publishedDate
      });

      setPosts(prevPosts =>
        prevPosts.map(p => p.id === updatedPost.id ? updatedPost : p)
      );

      toast({
        title: "Berhasil",
        description: `Status konten berhasil diubah menjadi ${getStatusLabel(newStatus)}`,
      });

      // Close modal if open
      if (showPostModal) {
        setShowPostModal(false);
      }
    } catch (error) {
      console.error('Error updating post status:', error);
      toast({
        title: "Error",
        description: "Gagal mengubah status konten",
        variant: "destructive"
      });
    }
  };

  // Get content type icon
  const getContentTypeIcon = (contentType: ContentType) => {
    switch (contentType) {
      case ContentType.IMAGE:
        return <ImageIcon size={16} className="text-blue-500" />;
      case ContentType.VIDEO:
        return <Video size={16} className="text-red-500" />;
      case ContentType.CAROUSEL:
        return <Layers size={16} className="text-purple-500" />;
      case ContentType.STORY:
        return <Clock size={16} className="text-orange-500" />;
      case ContentType.REEL:
        return <Video size={16} className="text-pink-500" />;
      default:
        return <ImageIcon size={16} />;
    }
  };

  // Get status icon and color
  const getStatusIcon = (status: PostStatus) => {
    switch (status) {
      case PostStatus.PUBLISHED:
        return <CheckCircle size={16} className="text-green-500" />;
      case PostStatus.SCHEDULED:
        return <Clock size={16} className="text-blue-500" />;
      case PostStatus.DRAFT:
        return <AlertCircle size={16} className="text-yellow-500" />;
      case PostStatus.ARCHIVED:
        return <AlertCircle size={16} className="text-gray-500" />;
      default:
        return null;
    }
  };

  // Get status label
  const getStatusLabel = (status: PostStatus): string => {
    switch (status) {
      case PostStatus.PUBLISHED:
        return 'Dipublikasikan';
      case PostStatus.SCHEDULED:
        return 'Terjadwal';
      case PostStatus.DRAFT:
        return 'Draft';
      case PostStatus.ARCHIVED:
        return 'Diarsipkan';
      default:
        return '';
    }
  };

  // Format date
  const formatDate = (date: Date | undefined): string => {
    if (!date) return 'N/A';
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  // Copy content to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Berhasil",
      description: "Teks berhasil disalin ke clipboard",
    });
  };

  // Get paginated posts
  const getPaginatedPosts = () => {
    const indexOfLastPost = currentPage * postsPerPage;
    const indexOfFirstPost = indexOfLastPost - postsPerPage;
    return filteredPosts.slice(indexOfFirstPost, indexOfLastPost);
  };

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Toggle sort direction
  const handleSort = (field: 'createdAt' | 'scheduledDate' | 'title') => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to descending
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Error fallback component
  const ErrorFallback = () => (
    <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 text-center">
      <AlertTriangle size={48} className="mx-auto text-red-500 mb-4" />
      <h3 className="text-xl font-semibold text-gray-800 mb-2">Terjadi Kesalahan</h3>
      <p className="text-gray-600 mb-6">Maaf, terjadi kesalahan saat memuat data konten Instagram.</p>
      <button
        onClick={handleRefresh}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 inline-flex items-center"
      >
        <RefreshCw size={16} className="mr-2" />
        Coba Lagi
      </button>
    </div>
  );
