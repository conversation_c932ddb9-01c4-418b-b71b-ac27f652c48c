import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js';
import { Line, Bar, Pie } from 'react-chartjs-2';
import { CustomerAnalysisResult } from '../types/customerAnalysis';
import { SalesRevenueItem } from '../services/salesRevenue2025Service';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface CustomerAnalysisChartsProps {
  analysisResult: CustomerAnalysisResult;
}

const CustomerAnalysisCharts: React.FC<CustomerAnalysisChartsProps> = ({ analysisResult }) => {
  // Generate purchase history trend data
  const generatePurchaseHistoryData = () => {
    // Group sales data by month
    const salesByMonth: Record<string, number> = {};

    // Sort sales data by date
    const sortedSales = [...analysisResult.salesData].sort((a, b) => {
      const dateA = new Date(a.billingDate);
      const dateB = new Date(b.billingDate);
      return dateA.getTime() - dateB.getTime();
    });

    // Group by month
    sortedSales.forEach(sale => {
      if (!sale.billingDate) return;

      const date = new Date(sale.billingDate);
      const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;

      if (!salesByMonth[monthYear]) {
        salesByMonth[monthYear] = 0;
      }

      salesByMonth[monthYear] += sale.revenueInDocCurr || 0;
    });

    // Convert to chart data
    const labels = Object.keys(salesByMonth);
    const data = Object.values(salesByMonth);

    return {
      labels,
      datasets: [
        {
          label: 'Revenue per Month',
          data,
          borderColor: 'rgb(53, 162, 235)',
          backgroundColor: 'rgba(53, 162, 235, 0.5)',
          tension: 0.3
        }
      ]
    };
  };

  // Generate fleet composition pie chart data
  const generateFleetCompositionData = () => {
    const { fleetComposition } = analysisResult.fleetAnalysis;

    return {
      labels: fleetComposition.map(item => item.tireSize),
      datasets: [
        {
          label: 'Fleet Composition',
          data: fleetComposition.map(item => item.quantity),
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(199, 199, 199, 0.6)',
            'rgba(83, 102, 255, 0.6)',
            'rgba(78, 252, 3, 0.6)',
            'rgba(252, 186, 3, 0.6)',
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(199, 199, 199, 1)',
            'rgba(83, 102, 255, 1)',
            'rgba(78, 252, 3, 1)',
            'rgba(252, 186, 3, 1)',
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Generate product recommendations bar chart data
  const generateProductRecommendationsData = () => {
    const { productRecommendations } = analysisResult;

    return {
      labels: productRecommendations.map(rec => rec.productName),
      datasets: [
        {
          label: 'Confidence (%)',
          data: productRecommendations.map(rec => rec.confidence),
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        },
        {
          label: 'Potential Revenue (in millions IDR)',
          data: productRecommendations.map(rec => rec.potentialRevenue / 1000000), // Convert to millions
          backgroundColor: 'rgba(255, 159, 64, 0.6)',
          borderColor: 'rgba(255, 159, 64, 1)',
          borderWidth: 1
        }
      ]
    };
  };

  // Chart options
  const lineChartOptions: ChartOptions<'line'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Purchase History Trend',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Revenue (IDR)'
        }
      }
    }
  };

  const pieChartOptions: ChartOptions<'pie'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: true,
        text: 'Fleet Composition by Tire Size',
      },
    }
  };

  const barChartOptions: ChartOptions<'bar'> = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Product Recommendations',
      },
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  };

  // Check if we have enough data for each chart
  const hasPurchaseData = analysisResult.salesData && analysisResult.salesData.length > 0;
  const hasFleetData = analysisResult.fleetAnalysis.fleetComposition.length > 0;
  const hasRecommendations = analysisResult.productRecommendations.length > 0;

  return (
    <div className="space-y-8">
      {/* Purchase History Trend Chart */}
      {hasPurchaseData && (
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Tren Pembelian</h3>
          <div className="h-64">
            <Line
              options={lineChartOptions}
              data={generatePurchaseHistoryData()}
            />
          </div>
        </div>
      )}

      {/* Fleet Composition Pie Chart */}
      {hasFleetData && (
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Komposisi Armada</h3>
          <div className="h-64">
            <Pie
              options={pieChartOptions}
              data={generateFleetCompositionData()}
            />
          </div>
        </div>
      )}

      {/* Product Recommendations Bar Chart */}
      {hasRecommendations && (
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-800 mb-4">Rekomendasi Produk</h3>
          <div className="h-64">
            <Bar
              options={barChartOptions}
              data={generateProductRecommendationsData()}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default CustomerAnalysisCharts;
