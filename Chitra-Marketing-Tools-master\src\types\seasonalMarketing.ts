/**
 * Types for the Seasonal Marketing Calendar feature
 */

/**
 * Recommendation level for a specific time period
 */
export enum RecommendationLevel {
  EXCELLENT = 'excellent',   // Highly recommended time for promotions
  GOOD = 'good',             // Good time for promotions
  NEUTRAL = 'neutral',       // Average time for promotions
  POOR = 'poor',             // Not recommended for promotions
  AVOID = 'avoid'            // Strongly not recommended for promotions
}

/**
 * Seasonal factor types that influence tire demand
 */
export enum SeasonalFactorType {
  WEATHER = 'weather',                 // Weather conditions
  MINING_OPERATIONS = 'mining_ops',    // Mining operational cycles
  ECONOMIC = 'economic',               // Economic factors
  COMPETITOR = 'competitor',           // Competitor activities
  HISTORICAL = 'historical',           // Historical sales data
  INDUSTRY_EVENT = 'industry_event',   // Industry events/conferences
  BUDGET_CYCLE = 'budget_cycle',       // Customer budget cycles
  MAINTENANCE = 'maintenance'          // Scheduled maintenance periods
}

/**
 * Seasonal factor that influences tire demand
 */
export interface SeasonalFactor {
  id: string;
  type: SeasonalFactorType;
  name: string;
  description: string;
  impact: number;              // Impact score from -10 (negative) to 10 (positive)
  startDate?: string;          // Optional specific date range
  endDate?: string;
  region?: string;             // Optional region specificity
  source?: string;             // Data source
  isHoliday?: boolean;         // Whether this factor is a holiday
  holidayType?: string;        // Type of holiday (National, Religious, etc.)
}

/**
 * Calendar day with recommendation data
 */
export interface CalendarDay {
  date: string;                // ISO date string
  recommendationLevel: RecommendationLevel;
  factors: SeasonalFactor[];   // Factors affecting this day
  score: number;               // Aggregate recommendation score (0-100)
  notes?: string;              // Optional notes
  holidays?: any[];            // Holidays on this day
}

/**
 * Calendar month with aggregated data
 */
export interface CalendarMonth {
  year: number;
  month: number;               // 1-12
  days: CalendarDay[];
  averageScore: number;        // Average recommendation score for the month
  topFactors: SeasonalFactor[]; // Top factors for this month
  holidays?: any[];            // All holidays in this month
}

/**
 * Scheduled promotion on the calendar
 */
export interface ScheduledPromotion {
  id: string;
  name: string;
  description?: string;
  startDate: string;           // ISO date string
  endDate: string;             // ISO date string
  promoType: string;           // Type of promotion
  products: string[];          // Product IDs or names
  status: 'planned' | 'active' | 'completed' | 'cancelled';
  budget?: number;             // Optional budget
  targetAudience?: string;     // Optional target audience
  expectedImpact?: number;     // Expected impact score (0-100)
  actualImpact?: number;       // Actual impact after completion (0-100)
  notes?: string;              // Optional notes
  color?: string;              // Optional color for display
}

/**
 * Seasonal insight for a specific time period
 */
export interface SeasonalInsight {
  id: string;
  title: string;
  description: string;
  startDate: string;           // ISO date string
  endDate: string;             // ISO date string
  recommendationLevel: RecommendationLevel;
  factors: SeasonalFactor[];
  recommendedPromoTypes: string[];
  recommendedProducts: string[];
  potentialImpact: number;     // Potential impact score (0-100)
  confidence: number;          // Confidence level (0-100)
  aiGenerated: boolean;        // Whether this insight was generated by AI
}

/**
 * Settings for the seasonal marketing calendar
 */
export interface CalendarSettings {
  regions: string[];           // Regions to consider
  productCategories: string[]; // Product categories to focus on
  factorWeights: {             // Weights for different factor types (0-100)
    [key in SeasonalFactorType]: number;
  };
  displayOptions: {
    showWeekends: boolean;
    showCompetitors: boolean;
    showHistorical: boolean;
    colorCoding: 'score' | 'recommendation' | 'impact';
  };
}

/**
 * Request for AI analysis of seasonal data
 */
export interface SeasonalAnalysisRequest {
  timeframe: {
    startDate: string;
    endDate: string;
  };
  region?: string;
  productCategories?: string[];
  historicalData?: any;        // Historical sales data
  competitorData?: any;        // Competitor activity data
  economicFactors?: any;       // Economic indicators
  weatherData?: any;           // Weather patterns
  includeWeatherData?: boolean; // Whether to include weather data in the analysis
  includeCoalPriceData?: boolean; // Whether to include coal price data in the analysis
}

/**
 * Response from AI analysis of seasonal data
 */
export interface SeasonalAnalysisResponse {
  insights: SeasonalInsight[];
  recommendedTimeframes: {
    startDate: string;
    endDate: string;
    score: number;
    recommendationLevel: RecommendationLevel;
    primaryFactors: SeasonalFactor[];
  }[];
  analysis: string;            // Textual analysis
}
