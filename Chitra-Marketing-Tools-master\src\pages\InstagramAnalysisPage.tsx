import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger
} from '../components/ui/tabs';
import {
  Instagram,
  BarChart,
  Users,
  TrendingUp,
  Hash,
  Calendar,
  MessageSquare,
  Heart,
  Share2,
  Bookmark,
  Eye,
  RefreshCw,
  FileText,
  Sparkles,
  Upload,
  HelpCircle,
  Info,
  Check,
  AlertCircle,
  Download,
  Loader2,
  Database,
  Plus,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight,
  Clock,
  Edit,
  Trash,
  Video,
  Image as ImageIcon,
  Copy
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Textarea } from '../components/ui/textarea';
import { useToast } from '../components/ui/use-toast';
import { SocialMediaPlatform, SocialMediaPost } from '../types/socialMedia';
import { PostAnalytics } from '../types/socialMediaEnhanced';
// Import Instagram analysis services
import { analyzeInstagramPerformance } from '../services/instagramAnalysisService';

// Import Instagram API services with error handling
let fetchInstagramData: (username: string) => Promise<string>;
let generateDummyInstagramData: (username: string) => string;

try {
  const instagramApiService = require('../services/instagramApiService');
  fetchInstagramData = instagramApiService.fetchInstagramData;
  generateDummyInstagramData = instagramApiService.generateDummyInstagramData;
  console.log('Successfully imported Instagram API services');
} catch (error) {
  console.error('Error importing Instagram API services:', error);
  // Fallback implementations
  fetchInstagramData = async (username: string) => {
    console.log('Using fallback fetchInstagramData');
    return JSON.stringify({
      username,
      followers: 1000,
      following: 500,
      posts: [],
      isRealData: false
    });
  };
  generateDummyInstagramData = (username: string) => {
    console.log('Using fallback generateDummyInstagramData');
    return JSON.stringify({
      username,
      followers: 1000,
      following: 500,
      posts: [],
      isRealData: false
    });
  };
}
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import {
  Progress
} from "../components/ui/progress";

// Import recharts conditionally to handle cases where it might not be available
let BarChart2: any, LineChart: any, Line: any, Bar: any, XAxis: any, YAxis: any, CartesianGrid: any, Tooltip: any, Legend: any, ResponsiveContainer: any, PieChart: any, Pie: any, Cell: any;
try {
  const recharts = require('recharts');
  BarChart2 = recharts.BarChart;
  LineChart = recharts.LineChart;
  Line = recharts.Line;
  Bar = recharts.Bar;
  XAxis = recharts.XAxis;
  YAxis = recharts.YAxis;
  CartesianGrid = recharts.CartesianGrid;
  Tooltip = recharts.Tooltip;
  Legend = recharts.Legend;
  ResponsiveContainer = recharts.ResponsiveContainer;
  PieChart = recharts.PieChart;
  Pie = recharts.Pie;
  Cell = recharts.Cell;
} catch (error) {
  // Create fallback components if recharts is not available
  BarChart2 = ({ children }: { children: React.ReactNode }) => <div>Chart not available</div>;
  LineChart = ({ children }: { children: React.ReactNode }) => <div>Chart not available</div>;
  Line = () => null;
  Bar = () => null;
  XAxis = () => null;
  YAxis = () => null;
  CartesianGrid = () => null;
  Tooltip = () => null;
  Legend = () => null;
  ResponsiveContainer = ({ children }: { children: React.ReactNode }) => <div>{children}</div>;
  PieChart = ({ children }: { children: React.ReactNode }) => <div>Chart not available</div>;
  Pie = () => null;
  Cell = () => null;
}

// Sample colors for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

// Function to generate random Instagram post data
const generateRandomInstagramPost = (index: number, username: string) => {
  // Post types
  const postTypes = ['image', 'carousel', 'video', 'reels'];
  const selectedType = postTypes[Math.floor(Math.random() * postTypes.length)];

  // Random dates for the last 6 months (newest first)
  const date = new Date();
  date.setDate(date.getDate() - (index * 3)); // Every 3 days
  const formattedDate = date.toISOString().split('T')[0];

  // Random engagement metrics
  const likes = Math.floor(Math.random() * 500) + 50;
  const comments = Math.floor(Math.random() * 50) + 5;
  const shares = Math.floor(Math.random() * 30) + 2;
  const saves = Math.floor(Math.random() * 40) + 3;

  // Video-specific metrics
  const views = selectedType === 'video' || selectedType === 'reels'
    ? Math.floor(Math.random() * 2000) + 500
    : 0;

  // Hashtags
  const hashtagPool = [
    'BanTerbaik', 'ChitraParatama', 'MiningTires', 'TireSafety', 'HeavyDuty',
    'BanTambang', 'FleetManagement', 'TirePerformance', 'MiningIndustry',
    'OTR', 'OffRoad', 'IndustriPertambangan', 'KualitasTerbaik', 'SolusiTambang'
  ];

  // Select 3-5 random hashtags
  const hashtagCount = Math.floor(Math.random() * 3) + 3;
  const selectedHashtags = [];
  for (let i = 0; i < hashtagCount; i++) {
    const randomIndex = Math.floor(Math.random() * hashtagPool.length);
    selectedHashtags.push(hashtagPool[randomIndex]);
    // Remove selected hashtag to avoid duplicates
    hashtagPool.splice(randomIndex, 1);
  }

  // Generate caption based on post type
  let caption = '';
  if (selectedType === 'image') {
    caption = `Ban tambang ${username} terbukti tahan di kondisi ekstrem! #${selectedHashtags.join(' #')}`;
  } else if (selectedType === 'carousel') {
    caption = `Swipe untuk melihat perbedaan performa ban premium kami. Sempurna untuk armada Anda! #${selectedHashtags.join(' #')}`;
  } else if (selectedType === 'video' || selectedType === 'reels') {
    caption = `Lihat ban kami beraksi di lokasi tambang! Ketahanan teruji dalam kondisi ekstrem. #${selectedHashtags.join(' #')}`;
  }

  return {
    id: `post_${index}`,
    type: selectedType,
    caption,
    likes,
    comments,
    shares,
    saves,
    views,
    date: formattedDate,
    hashtags: selectedHashtags,
    engagement_rate: ((likes + comments * 2 + shares * 3 + saves * 4) / 1000 * 100).toFixed(2)
  };
};

export default function InstagramAnalysisPage() {
  console.log('InstagramAnalysisPage component is rendering');

  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [instagramData, setInstagramData] = useState<string>('');
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [isConnectingInstagram, setIsConnectingInstagram] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isRealData, setIsRealData] = useState(false);
  const [instagramUsername, setInstagramUsername] = useState('');
  const [showConnectDialog, setShowConnectDialog] = useState(false);
  const [scrapingProgress, setScrapingProgress] = useState(0);
  const [isScrapingData, setIsScrapingData] = useState(false);
  const [scrapingStatus, setScrapingStatus] = useState('');
  const [scrapedPostCount, setScrapedPostCount] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Log when component mounts
  useEffect(() => {
    console.log('InstagramAnalysisPage component mounted');

    // Check for any errors in the imports
    try {
      console.log('fetchInstagramData function exists:', typeof fetchInstagramData === 'function');
      console.log('generateDummyInstagramData function exists:', typeof generateDummyInstagramData === 'function');
    } catch (error) {
      console.error('Error checking imported functions:', error);
    }
  }, []);

  // Function to fetch real Instagram data
  const scrapeInstagramData = async (username: string): Promise<string> => {
    setIsScrapingData(true);
    setScrapingProgress(0);
    setScrapingStatus('Menginisialisasi koneksi...');

    try {
      // Initialize connection
      await new Promise(resolve => setTimeout(resolve, 500));
      setScrapingProgress(10);
      setScrapingStatus('Mengautentikasi...');

      // Authentication step
      await new Promise(resolve => setTimeout(resolve, 500));
      setScrapingProgress(20);
      setScrapingStatus('Mengakses profil Instagram...');

      // Try to fetch real Instagram data
      let jsonData: string;
      try {
        // Attempt to fetch real data from Instagram API
        jsonData = await fetchInstagramData(username);
        setScrapingStatus('Berhasil mengambil data dari Instagram API');
      } catch (error) {
        console.error('Error fetching real Instagram data:', error);
        setScrapingStatus('Gagal mengambil data dari API, menggunakan data simulasi...');

        // If real API fails, fall back to dummy data
        jsonData = generateDummyInstagramData(username);
      }

      // Parse the data to count posts
      try {
        const parsedData = JSON.parse(jsonData);
        const totalPosts = parsedData.posts?.length || 0;
        setScrapedPostCount(totalPosts);
        setScrapingStatus(`Berhasil mengambil ${totalPosts} postingan`);
      } catch (e) {
        console.error('Error parsing Instagram data:', e);
      }

      // Complete the process
      setScrapingProgress(100);
      setScrapingStatus('Selesai mengambil data');
      setIsScrapingData(false);

      return jsonData;
    } catch (error) {
      console.error('Error in Instagram data scraping process:', error);
      setScrapingStatus('Terjadi kesalahan saat mengambil data');
      setScrapingProgress(100);
      setIsScrapingData(false);

      // Return dummy data as fallback
      return generateDummyInstagramData(username);
    }
  };

  // Sample data for demonstration
  const sampleEngagementData = [
    { name: 'Jan', likes: 400, comments: 240, shares: 100 },
    { name: 'Feb', likes: 300, comments: 139, shares: 80 },
    { name: 'Mar', likes: 200, comments: 980, shares: 200 },
    { name: 'Apr', likes: 278, comments: 390, shares: 150 },
    { name: 'May', likes: 189, comments: 480, shares: 120 },
    { name: 'Jun', likes: 239, comments: 380, shares: 110 },
  ];

  const sampleContentTypeData = [
    { name: 'Image', value: 400 },
    { name: 'Video', value: 300 },
    { name: 'Carousel', value: 300 },
    { name: 'Reels', value: 200 },
  ];

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        setInstagramData(content);

        // Check if the data is real or simulated
        try {
          const parsedData = JSON.parse(content);
          setIsRealData(parsedData.isRealData === true);
          setIsConnected(true);
        } catch (parseError) {
          console.error('Error parsing JSON from file:', parseError);
          setIsRealData(false);
          setIsConnected(true);
        }

        toast({
          title: "File Berhasil Diunggah",
          description: `Berhasil memuat data dari ${file.name}`,
        });
      } catch (error) {
        console.error('Error reading file:', error);
        toast({
          title: "Unggahan Gagal",
          description: "Gagal membaca file. Silakan coba lagi.",
          variant: "destructive"
        });
      }
    };

    reader.onerror = () => {
      toast({
        title: "Unggahan Gagal",
        description: "Gagal membaca file. Silakan coba lagi.",
        variant: "destructive"
      });
    };

    reader.readAsText(file);
  };

  // Trigger file input click
  const triggerFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle Instagram connection
  const handleConnectInstagram = async () => {
    if (!instagramUsername.trim()) {
      toast({
        title: "Username Diperlukan",
        description: "Silakan masukkan username Instagram Anda",
        variant: "destructive"
      });
      return;
    }

    setIsConnectingInstagram(true);

    try {
      // Start scraping process
      toast({
        title: "Memulai Pengambilan Data",
        description: "Mengambil data postingan terbaru dari akun Instagram Anda",
      });

      // Call the scraping function
      const scrapedData = await scrapeInstagramData(instagramUsername);

      // Parse the data to check if it's real or dummy
      try {
        const parsedData = JSON.parse(scrapedData);
        const dataIsReal = parsedData.isRealData === true;

        // Set the scraped data
        setInstagramData(scrapedData);

        // Update UI state
        setIsConnected(true);
        setIsRealData(dataIsReal);
        setShowConnectDialog(false);

        if (dataIsReal) {
          toast({
            title: "Pengambilan Data Selesai",
            description: `Berhasil mengambil data asli dari akun Instagram: ${instagramUsername}`,
          });
        } else {
          toast({
            title: "Pengambilan Data Selesai",
            description: `Data diambil untuk akun: ${instagramUsername}. Beberapa data mungkin disimulasikan.`,
          });
        }
      } catch (e) {
        console.error('Error parsing Instagram data:', e);

        // Set the data anyway
        setInstagramData(scrapedData);
        setIsConnected(true);
        setIsRealData(false); // Assume it's not real data if we can't parse it
        setShowConnectDialog(false);

        toast({
          title: "Pengambilan Data Selesai",
          description: `Data diambil untuk akun: ${instagramUsername}`,
        });
      }
    } catch (error) {
      console.error('Error scraping Instagram data:', error);
      toast({
        title: "Pengambilan Data Gagal",
        description: "Gagal mengambil data Instagram. Silakan coba lagi.",
        variant: "destructive"
      });
    } finally {
      setIsConnectingInstagram(false);
    }
  };

  // Handle analysis
  const handleAnalyze = async () => {
    if (!instagramData.trim()) {
      toast({
        title: "Input Required",
        description: "Please enter Instagram data to analyze",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const result = await analyzeInstagramPerformance(instagramData);
      setAnalysisResult(result);
      toast({
        title: "Analysis Complete",
        description: "Instagram data has been analyzed successfully",
      });
    } catch (error) {
      console.error('Error analyzing Instagram data:', error);
      toast({
        title: "Analysis Failed",
        description: "Failed to analyze Instagram data. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Analisis Instagram</h1>
        <p className="text-gray-600">Analisis performa Instagram Anda dan dapatkan wawasan untuk meningkatkan strategi</p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4 flex flex-wrap justify-start overflow-x-auto sticky top-0 z-10 bg-white pb-2 pt-2 border-b">
          <TabsTrigger value="overview" className="flex items-center gap-1">
            <BarChart className="h-4 w-4" />
            Ringkasan
          </TabsTrigger>
          <TabsTrigger value="content" className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            Analisis Konten
          </TabsTrigger>
          <TabsTrigger value="audience" className="flex items-center gap-1">
            <Users className="h-4 w-4" />
            Wawasan Audiens
          </TabsTrigger>
          <TabsTrigger value="hashtags" className="flex items-center gap-1">
            <Hash className="h-4 w-4" />
            Analisis Hashtag
          </TabsTrigger>
          <TabsTrigger value="recommendations" className="flex items-center gap-1">
            <Sparkles className="h-4 w-4" />
            Rekomendasi
          </TabsTrigger>
          <TabsTrigger value="blueprint" className="flex items-center gap-1">
            <TrendingUp className="h-4 w-4" />
            Blueprint Pertumbuhan
          </TabsTrigger>
          <TabsTrigger value="competitors" className="flex items-center gap-1">
            <Eye className="h-4 w-4" />
            Analisis Kompetitor
          </TabsTrigger>
          <TabsTrigger value="scheduler" className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            Jadwal Posting
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>Input Data Instagram</CardTitle>
                    <CardDescription>
                      Tempel data Instagram Anda atau unggah file untuk dianalisis
                    </CardDescription>
                  </div>
                  {isConnected && (
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                      isRealData
                        ? 'bg-green-100 text-green-800 border border-green-300'
                        : 'bg-amber-100 text-amber-800 border border-amber-300'
                    }`}>
                      {isRealData ? 'Data Asli' : 'Data Simulasi'}
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="instagram-data">Data Instagram</Label>
                    <Textarea
                      id="instagram-data"
                      placeholder="Tempel data Instagram Anda di sini..."
                      className="min-h-[200px]"
                      value={instagramData}
                      onChange={(e) => setInstagramData(e.target.value)}
                    />
                  </div>
                  {isConnected && (
                    <div className={`mb-4 p-3 rounded-md text-sm ${
                      isRealData
                        ? 'bg-green-50 text-green-800 border border-green-200'
                        : 'bg-amber-50 text-amber-800 border border-amber-200'
                    }`}>
                      <div className="flex items-start">
                        {isRealData ? (
                          <Check className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                        ) : (
                          <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
                        )}
                        <div>
                          <p className="font-medium">
                            {isRealData
                              ? 'Menggunakan data asli Instagram'
                              : 'Menggunakan data simulasi Instagram'}
                          </p>
                          <p className="mt-1">
                            {isRealData
                              ? 'Data ini diambil langsung dari API Instagram dan mencerminkan performa akun yang sebenarnya.'
                              : 'Data ini disimulasikan karena koneksi ke API Instagram tidak tersedia. Analisis mungkin tidak sepenuhnya akurat.'}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {/* Hidden file input */}
                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileUpload}
                        accept=".json,.txt,.csv"
                        className="hidden"
                      />
                      <Button
                        variant="outline"
                        className="flex items-center"
                        onClick={triggerFileUpload}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Unggah Data
                      </Button>

                      <Dialog open={showConnectDialog} onOpenChange={setShowConnectDialog}>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            className="flex items-center"
                            onClick={() => setShowConnectDialog(true)}
                          >
                            <Instagram className="h-4 w-4 mr-2" />
                            {isConnected ? 'Terhubung' : 'Hubungkan Instagram'}
                            {isConnected && <Check className="h-3 w-3 ml-1 text-green-500" />}
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[425px]">
                          <DialogHeader>
                            <DialogTitle>Hubungkan ke Instagram</DialogTitle>
                            <DialogDescription>
                              Masukkan username Instagram Anda untuk menghubungkan akun dan mengambil 30 postingan terbaru.
                            </DialogDescription>
                          </DialogHeader>

                          {isScrapingData ? (
                            <div className="py-6 space-y-4">
                              <div className="flex items-center justify-center mb-2">
                                <Database className="h-12 w-12 text-blue-500 animate-pulse" />
                              </div>
                              <div className="text-center mb-2">
                                <h3 className="text-lg font-medium">{scrapingStatus}</h3>
                                <p className="text-sm text-gray-500">
                                  {scrapedPostCount > 0 && `${scrapedPostCount} dari 30 postingan`}
                                </p>
                              </div>
                              <Progress value={scrapingProgress} className="w-full" />
                              <p className="text-xs text-center text-gray-500">
                                Mengambil data likes, komentar, views, shares, dan metrik lainnya untuk analisis komprehensif
                              </p>
                            </div>
                          ) : (
                            <>
                              <div className="grid gap-4 py-4">
                                <div className="grid grid-cols-4 items-center gap-4">
                                  <Label htmlFor="instagram-username" className="text-right">
                                    Username
                                  </Label>
                                  <Input
                                    id="instagram-username"
                                    placeholder="@namapengguna"
                                    className="col-span-3"
                                    value={instagramUsername}
                                    onChange={(e) => setInstagramUsername(e.target.value)}
                                  />
                                </div>
                                <div className="col-span-4">
                                  <p className="text-xs text-gray-500 mt-2">
                                    <Info className="h-3 w-3 inline mr-1" />
                                    Kami akan mengambil data postingan terbaru termasuk likes, komentar, views, dan shares dari akun Instagram Anda untuk analisis yang lebih akurat.
                                  </p>
                                  <p className="text-xs text-gray-500 mt-2">
                                    <AlertCircle className="h-3 w-3 inline mr-1" />
                                    Jika koneksi ke Instagram API gagal, sistem akan menggunakan data simulasi sebagai fallback.
                                  </p>
                                </div>
                              </div>
                              <DialogFooter>
                                <Button
                                  type="submit"
                                  onClick={handleConnectInstagram}
                                  disabled={isConnectingInstagram || !instagramUsername.trim()}
                                >
                                  {isConnectingInstagram ? (
                                    <>
                                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                      Mempersiapkan...
                                    </>
                                  ) : (
                                    <>
                                      <Download className="h-4 w-4 mr-2" />
                                      Ambil Data
                                    </>
                                  )}
                                </Button>
                              </DialogFooter>
                            </>
                          )}

                        </DialogContent>
                      </Dialog>
                    </div>
                    <Button
                      onClick={handleAnalyze}
                      disabled={isLoading}
                      className="flex items-center"
                    >
                      {isLoading ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Sparkles className="h-4 w-4 mr-2" />
                      )}
                      Analisis
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {analysisResult && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-blue-500" />
                        Total Postingan
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{analysisResult.ringkasan?.totalPostingan || analysisResult.totalPosts || 0}</div>
                      <p className="text-xs text-gray-500 mt-1">Dalam 30 hari terakhir</p>
                    </CardContent>
                  </Card>
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <TrendingUp className="h-4 w-4 mr-2 text-green-500" />
                        Tingkat Engagement
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{(analysisResult.ringkasan?.tingkatEngagement || analysisResult.engagementRate || 0).toFixed(2)}%</div>
                      <p className="text-xs text-gray-500 mt-1">
                        {(analysisResult.ringkasan?.tingkatEngagement || 0) > 3 ?
                          <span className="text-green-600 flex items-center"><TrendingUp className="h-3 w-3 mr-1" /> Di atas rata-rata industri</span> :
                          <span className="text-amber-600 flex items-center"><TrendingUp className="h-3 w-3 mr-1" /> Perlu peningkatan</span>}
                      </p>
                    </CardContent>
                  </Card>
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <Heart className="h-4 w-4 mr-2 text-red-500" />
                        Rata-rata Likes
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{analysisResult.ringkasan?.rataRataLikes || analysisResult.averageLikes || 0}</div>
                      <p className="text-xs text-gray-500 mt-1">Per postingan</p>
                    </CardContent>
                  </Card>
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center">
                        <MessageSquare className="h-4 w-4 mr-2 text-purple-500" />
                        Rata-rata Komentar
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{analysisResult.ringkasan?.rataRataKomentar || analysisResult.averageComments || 0}</div>
                      <p className="text-xs text-gray-500 mt-1">Per postingan</p>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <CardTitle className="flex items-center text-base">
                        <Sparkles className="h-5 w-5 mr-2 text-amber-500" />
                        Skor Kinerja Keseluruhan
                      </CardTitle>
                      <CardDescription>
                        Berdasarkan engagement, konsistensi, dan pertumbuhan
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Skor: {analysisResult.ringkasan?.skorKinerjaKeseluruhan || 7.2}/10</span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          (analysisResult.ringkasan?.skorKinerjaKeseluruhan || 7.2) >= 8 ? 'bg-green-100 text-green-800' :
                          (analysisResult.ringkasan?.skorKinerjaKeseluruhan || 7.2) >= 6 ? 'bg-amber-100 text-amber-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {(analysisResult.ringkasan?.skorKinerjaKeseluruhan || 7.2) >= 8 ? 'Sangat Baik' :
                           (analysisResult.ringkasan?.skorKinerjaKeseluruhan || 7.2) >= 6 ? 'Baik' : 'Perlu Perbaikan'}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className={`h-2.5 rounded-full ${
                            (analysisResult.ringkasan?.skorKinerjaKeseluruhan || 7.2) >= 8 ? 'bg-green-600' :
                            (analysisResult.ringkasan?.skorKinerjaKeseluruhan || 7.2) >= 6 ? 'bg-amber-500' :
                            'bg-red-600'
                          }`}
                          style={{ width: `${(analysisResult.ringkasan?.skorKinerjaKeseluruhan || 7.2) * 10}%` }}
                        ></div>
                      </div>
                      <div className="mt-4">
                        <h4 className="text-sm font-medium mb-2">Jenis Konten Terbaik</h4>
                        <div className="flex flex-wrap gap-2">
                          {(analysisResult.ringkasan?.jenisKontenTerbaik || ['Carousel', 'Reels']).map((jenis, i) => (
                            <span key={i} className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded-full">
                              {jenis}
                            </span>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <CardTitle className="flex items-center text-base">
                        <Info className="h-5 w-5 mr-2 text-blue-500" />
                        Analisis Singkat
                      </CardTitle>
                      <CardDescription>
                        Ringkasan performa akun Instagram Anda
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-700">
                        {analysisResult.ringkasan?.analisisSingkat ||
                          "Akun Instagram menunjukkan kinerja yang cukup baik dengan tingkat engagement 3.8%. Konten jenis carousel dan reels menunjukkan performa terbaik. Terdapat peluang untuk meningkatkan interaksi dengan audiens melalui strategi konten yang lebih terfokus."}
                      </p>
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <h4 className="text-sm font-medium mb-2">Tindakan Cepat yang Direkomendasikan</h4>
                        <ul className="space-y-2">
                          <li className="flex items-start">
                            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-2 mt-0.5">
                              <Check className="h-3 w-3 text-green-600" />
                            </div>
                            <span className="text-sm">Tingkatkan frekuensi posting {(analysisResult.ringkasan?.jenisKontenTerbaik || ['Carousel', 'Reels'])[0]}</span>
                          </li>
                          <li className="flex items-start">
                            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-2 mt-0.5">
                              <Check className="h-3 w-3 text-green-600" />
                            </div>
                            <span className="text-sm">Optimalkan waktu posting berdasarkan aktivitas audiens</span>
                          </li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </>
            )}

            {/* Enhanced Engagement Chart */}
            <Card className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <div>
                  <CardTitle className="text-base flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2 text-blue-500" />
                    Tren Engagement
                  </CardTitle>
                  <CardDescription>
                    Likes, komentar, dan shares dari waktu ke waktu
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" className="h-8 text-xs">
                    7 Hari
                  </Button>
                  <Button variant="outline" size="sm" className="h-8 text-xs bg-blue-50">
                    30 Hari
                  </Button>
                  <Button variant="outline" size="sm" className="h-8 text-xs">
                    6 Bulan
                  </Button>
                  <Button variant="outline" size="sm" className="h-8 px-2">
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-80 w-full">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={sampleEngagementData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="name" stroke="#888888" />
                      <YAxis stroke="#888888" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'rgba(255, 255, 255, 0.95)',
                          borderRadius: '8px',
                          border: '1px solid #e2e8f0',
                          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                        }}
                      />
                      <Legend
                        verticalAlign="top"
                        height={36}
                        iconType="circle"
                        iconSize={8}
                      />
                      <Line
                        type="monotone"
                        dataKey="likes"
                        name="Likes"
                        stroke="#8884d8"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6, stroke: '#8884d8', strokeWidth: 2, fill: '#fff' }}
                      />
                      <Line
                        type="monotone"
                        dataKey="comments"
                        name="Komentar"
                        stroke="#82ca9d"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6, stroke: '#82ca9d', strokeWidth: 2, fill: '#fff' }}
                      />
                      <Line
                        type="monotone"
                        dataKey="shares"
                        name="Shares"
                        stroke="#ffc658"
                        strokeWidth={2}
                        dot={{ r: 4 }}
                        activeDot={{ r: 6, stroke: '#ffc658', strokeWidth: 2, fill: '#fff' }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
                <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-purple-50 rounded-lg p-3 flex items-center">
                    <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                      <Heart className="h-6 w-6 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-xs text-purple-700">Total Likes</p>
                      <p className="text-xl font-bold text-purple-900">4,328</p>
                      <p className="text-xs text-purple-700 flex items-center">
                        <TrendingUp className="h-3 w-3 mr-1" /> +12.5% dari bulan lalu
                      </p>
                    </div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-3 flex items-center">
                    <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mr-3">
                      <MessageSquare className="h-6 w-6 text-green-600" />
                    </div>
                    <div>
                      <p className="text-xs text-green-700">Total Komentar</p>
                      <p className="text-xl font-bold text-green-900">856</p>
                      <p className="text-xs text-green-700 flex items-center">
                        <TrendingUp className="h-3 w-3 mr-1" /> +8.3% dari bulan lalu
                      </p>
                    </div>
                  </div>
                  <div className="bg-amber-50 rounded-lg p-3 flex items-center">
                    <div className="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center mr-3">
                      <Share2 className="h-6 w-6 text-amber-600" />
                    </div>
                    <div>
                      <p className="text-xs text-amber-700">Total Shares</p>
                      <p className="text-xl font-bold text-amber-900">642</p>
                      <p className="text-xs text-amber-700 flex items-center">
                        <TrendingUp className="h-3 w-3 mr-1" /> +15.7% dari bulan lalu
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="content">
          <Card>
            <CardHeader>
              <CardTitle>Content Type Distribution</CardTitle>
              <CardDescription>
                Distribution of different content types on your Instagram
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80 w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={sampleContentTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {sampleContentTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} posts`, 'Count']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {analysisResult && analysisResult.contentAnalysis && (
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Best Performing Posts</CardTitle>
                  <CardDescription>
                    Analysis of your top performing content
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analysisResult.contentAnalysis.bestPerformingPosts.map((post: any, index: number) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <div className="font-medium">{post.postType}</div>
                          <div className="text-sm bg-green-100 text-green-800 px-2 py-0.5 rounded">
                            {post.engagementRate}% Engagement
                          </div>
                        </div>
                        <div className="text-sm text-gray-600 mb-2">Topic: {post.topic}</div>
                        <div className="mt-2">
                          <div className="text-xs font-medium text-gray-500 mb-1">STRENGTHS</div>
                          <div className="flex flex-wrap gap-1">
                            {post.strengths.map((strength: string, i: number) => (
                              <span key={i} className="text-xs bg-blue-50 text-blue-700 px-2 py-0.5 rounded">
                                {strength}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div className="mt-2">
                          <div className="text-xs font-medium text-gray-500 mb-1">AREAS TO IMPROVE</div>
                          <div className="flex flex-wrap gap-1">
                            {post.weaknesses.map((weakness: string, i: number) => (
                              <span key={i} className="text-xs bg-amber-50 text-amber-700 px-2 py-0.5 rounded">
                                {weakness}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Caption Analysis</CardTitle>
                  <CardDescription>
                    Insights about your caption performance
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Optimal Caption Length</h4>
                      <div className="text-lg font-medium">{analysisResult.contentAnalysis.captionAnalysis.optimalLength}</div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Effective Elements</h4>
                      <div className="flex flex-wrap gap-1">
                        {analysisResult.contentAnalysis.captionAnalysis.effectiveElements.map((element: string, i: number) => (
                          <span key={i} className="text-xs bg-green-50 text-green-700 px-2 py-0.5 rounded">
                            {element}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Improvement Suggestions</h4>
                      <ul className="list-disc pl-5 space-y-1 text-sm">
                        {analysisResult.contentAnalysis.captionAnalysis.improvementSuggestions.map((suggestion: string, i: number) => (
                          <li key={i}>{suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="audience">
          {analysisResult && analysisResult.audienceInsights ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Audience Demographics</CardTitle>
                  <CardDescription>
                    Key information about your audience
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="p-4 bg-blue-50 rounded-lg mb-4">
                    <p className="text-blue-800">{analysisResult.audienceInsights.demographicInsights}</p>
                  </div>

                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Audience Interests</h4>
                    <div className="flex flex-wrap gap-1">
                      {analysisResult.audienceInsights.audienceInterests.map((interest: string, i: number) => (
                        <span key={i} className="text-xs bg-purple-50 text-purple-700 px-2 py-0.5 rounded">
                          {interest}
                        </span>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Engagement Timing</CardTitle>
                  <CardDescription>
                    Best times to post for maximum engagement
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Peak Engagement Times</h4>
                      <div className="flex flex-col space-y-2">
                        {analysisResult.audienceInsights.peakEngagementTimes.map((time: string, i: number) => (
                          <div key={i} className="flex items-center">
                            <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                            <span>{time}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Growth Opportunities</h4>
                      <ul className="list-disc pl-5 space-y-1 text-sm">
                        {analysisResult.audienceInsights.audienceGrowthOpportunities.map((opportunity: string, i: number) => (
                          <li key={i}>{opportunity}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 mb-4">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Audience Data Available</h3>
              <p className="text-gray-500 max-w-md mx-auto">
                Enter your Instagram data in the Overview tab and click Analyze to see audience insights.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="hashtags">
          {analysisResult && analysisResult.hashtagAnalysis ? (
            <div className="grid grid-cols-1 gap-6">
              <Card className="hover:shadow-md transition-shadow">
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <div>
                    <CardTitle className="text-base flex items-center">
                      <Hash className="h-5 w-5 mr-2 text-blue-500" />
                      Analisis Hashtag
                    </CardTitle>
                    <CardDescription>
                      Analisis strategi dan performa hashtag Anda
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" className="h-8 text-xs">
                      <Plus className="h-4 w-4 mr-1" /> Tambah Hashtag
                    </Button>
                    <Button variant="outline" size="sm" className="h-8 px-2">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                        <Check className="h-4 w-4 mr-1 text-green-600" />
                        Hashtag Efektif
                      </h4>
                      <div className="flex flex-wrap gap-2 mb-6">
                        {(analysisResult.hashtagAnalysis.effectiveHashtags || analysisResult.hashtagAnalysis.hashtagEfektif || ['BanTerbaik', 'TireSafety', 'MiningTires', 'BanTambang']).map((hashtag: string, i: number) => (
                          <div key={i} className="group relative">
                            <span className="text-sm bg-green-50 text-green-700 px-3 py-1 rounded-full flex items-center hover:bg-green-100 transition-colors cursor-pointer">
                              #{hashtag}
                              <span className="ml-1 text-xs bg-green-200 text-green-800 px-1 rounded">
                                {Math.floor(Math.random() * 30) + 70}%
                              </span>
                            </span>
                            <div className="absolute z-10 hidden group-hover:block bg-white p-2 rounded shadow-lg border mt-1 w-48">
                              <div className="text-xs font-medium mb-1">Metrik Hashtag #{hashtag}</div>
                              <div className="space-y-1">
                                <div>
                                  <div className="flex justify-between text-xs">
                                    <span>Popularitas</span>
                                    <span>{Math.floor(Math.random() * 30) + 70}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-1">
                                    <div className="bg-green-600 h-1 rounded-full" style={{ width: `${Math.floor(Math.random() * 30) + 70}%` }}></div>
                                  </div>
                                </div>
                                <div>
                                  <div className="flex justify-between text-xs">
                                    <span>Relevansi</span>
                                    <span>{Math.floor(Math.random() * 20) + 80}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-1">
                                    <div className="bg-green-600 h-1 rounded-full" style={{ width: `${Math.floor(Math.random() * 20) + 80}%` }}></div>
                                  </div>
                                </div>
                                <div>
                                  <div className="flex justify-between text-xs">
                                    <span>Kompetisi</span>
                                    <span>{Math.floor(Math.random() * 40) + 30}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-1">
                                    <div className="bg-amber-600 h-1 rounded-full" style={{ width: `${Math.floor(Math.random() * 40) + 30}%` }}></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-1 text-red-600" />
                        Hashtag Tidak Efektif
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {(analysisResult.hashtagAnalysis.ineffectiveHashtags || analysisResult.hashtagAnalysis.hashtagTidakEfektif || ['InstaDaily', 'PicOfTheDay', 'OOTD', 'Trending']).map((hashtag: string, i: number) => (
                          <div key={i} className="group relative">
                            <span className="text-sm bg-red-50 text-red-700 px-3 py-1 rounded-full flex items-center hover:bg-red-100 transition-colors cursor-pointer">
                              #{hashtag}
                              <span className="ml-1 text-xs bg-red-200 text-red-800 px-1 rounded">
                                {Math.floor(Math.random() * 30) + 10}%
                              </span>
                            </span>
                            <div className="absolute z-10 hidden group-hover:block bg-white p-2 rounded shadow-lg border mt-1 w-48">
                              <div className="text-xs font-medium mb-1">Metrik Hashtag #{hashtag}</div>
                              <div className="space-y-1">
                                <div>
                                  <div className="flex justify-between text-xs">
                                    <span>Popularitas</span>
                                    <span>{Math.floor(Math.random() * 50) + 50}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-1">
                                    <div className="bg-amber-600 h-1 rounded-full" style={{ width: `${Math.floor(Math.random() * 50) + 50}%` }}></div>
                                  </div>
                                </div>
                                <div>
                                  <div className="flex justify-between text-xs">
                                    <span>Relevansi</span>
                                    <span>{Math.floor(Math.random() * 30) + 10}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-1">
                                    <div className="bg-red-600 h-1 rounded-full" style={{ width: `${Math.floor(Math.random() * 30) + 10}%` }}></div>
                                  </div>
                                </div>
                                <div>
                                  <div className="flex justify-between text-xs">
                                    <span>Kompetisi</span>
                                    <span>{Math.floor(Math.random() * 30) + 70}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-1">
                                    <div className="bg-red-600 h-1 rounded-full" style={{ width: `${Math.floor(Math.random() * 30) + 70}%` }}></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                        <Sparkles className="h-4 w-4 mr-1 text-blue-600" />
                        Hashtag Direkomendasikan
                      </h4>
                      <div className="flex flex-wrap gap-2 mb-6">
                        {(analysisResult.hashtagAnalysis.recommendedHashtags || analysisResult.hashtagAnalysis.hashtagDirekomendasikan || ['ChitraParatama', 'BanTambang', 'PerformaBan', 'IndustriPertambangan', 'BanKualitas', 'SolusiTambang']).map((hashtag: string, i: number) => (
                          <div key={i} className="group relative">
                            <span className="text-sm bg-blue-50 text-blue-700 px-3 py-1 rounded-full flex items-center hover:bg-blue-100 transition-colors cursor-pointer">
                              #{hashtag}
                              <span className="ml-1 text-xs bg-blue-200 text-blue-800 px-1 rounded">
                                {Math.floor(Math.random() * 20) + 80}%
                              </span>
                            </span>
                            <div className="absolute z-10 hidden group-hover:block bg-white p-2 rounded shadow-lg border mt-1 w-48">
                              <div className="text-xs font-medium mb-1">Metrik Hashtag #{hashtag}</div>
                              <div className="space-y-1">
                                <div>
                                  <div className="flex justify-between text-xs">
                                    <span>Popularitas</span>
                                    <span>{Math.floor(Math.random() * 40) + 60}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-1">
                                    <div className="bg-blue-600 h-1 rounded-full" style={{ width: `${Math.floor(Math.random() * 40) + 60}%` }}></div>
                                  </div>
                                </div>
                                <div>
                                  <div className="flex justify-between text-xs">
                                    <span>Relevansi</span>
                                    <span>{Math.floor(Math.random() * 20) + 80}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-1">
                                    <div className="bg-blue-600 h-1 rounded-full" style={{ width: `${Math.floor(Math.random() * 20) + 80}%` }}></div>
                                  </div>
                                </div>
                                <div>
                                  <div className="flex justify-between text-xs">
                                    <span>Kompetisi</span>
                                    <span>{Math.floor(Math.random() * 40) + 20}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-1">
                                    <div className="bg-green-600 h-1 rounded-full" style={{ width: `${Math.floor(Math.random() * 40) + 20}%` }}></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                        <Info className="h-4 w-4 mr-1 text-amber-600" />
                        Strategi Hashtag
                      </h4>
                      <div className="p-3 bg-amber-50 rounded-lg">
                        <p className="text-amber-800">{analysisResult.hashtagAnalysis.hashtagStrategy || analysisResult.hashtagAnalysis.strategiHashtag || 'Gunakan 5-7 hashtag yang sangat spesifik untuk industri daripada hashtag populer umum'}</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                      <FileText className="h-4 w-4 mr-1 text-purple-600" />
                      Contoh Set Hashtag
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {(analysisResult.hashtagAnalysis.contohSetHashtag || [
                        "#ChitraParatama #BanTambang #SolusiTambang #KualitasTerbaik #IndustriPertambangan",
                        "#BanTerbaik #ChitraParatama #PerformaBan #SolusiIndustri #BanTangguh"
                      ]).map((set: string, i: number) => (
                        <div key={i} className="border rounded-lg p-3 hover:border-purple-200 hover:bg-purple-50 transition-colors">
                          <div className="flex justify-between items-start mb-2">
                            <h5 className="text-sm font-medium">Set Hashtag {i+1}</h5>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                          <p className="text-sm text-gray-700">{set}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium mb-2 flex items-center">
                      <TrendingUp className="h-4 w-4 mr-2 text-blue-600" />
                      Tren Hashtag Industri Ban & Pertambangan
                    </h4>
                    <div className="space-y-3">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm flex items-center">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
                            #SustainableMining
                          </span>
                          <span className="text-xs text-gray-500">+42% bulan ini</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-green-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm flex items-center">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
                            #EcoFriendlyTires
                          </span>
                          <span className="text-xs text-gray-500">+38% bulan ini</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-green-600 h-2 rounded-full" style={{ width: '78%' }}></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm flex items-center">
                            <TrendingUp className="h-3 w-3 mr-1 text-green-600" />
                            #MiningInnovation
                          </span>
                          <span className="text-xs text-gray-500">+27% bulan ini</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div className="bg-green-600 h-2 rounded-full" style={{ width: '65%' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 mb-4">
                <Hash className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Data Analisis Hashtag Belum Tersedia</h3>
              <p className="text-gray-500 max-w-md mx-auto">
                Masukkan data Instagram Anda di tab Ringkasan dan klik Analisis untuk melihat analisis hashtag.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="recommendations">
          {analysisResult && analysisResult.recommendations ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Content Strategy</CardTitle>
                  <CardDescription>
                    Recommendations for your content strategy
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {analysisResult.recommendations.contentStrategy.map((strategy: string, i: number) => (
                      <li key={i} className="flex items-start">
                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2 mt-0.5">
                          <span className="text-xs font-medium text-blue-700">{i+1}</span>
                        </div>
                        <span>{strategy}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Engagement Tactics</CardTitle>
                  <CardDescription>
                    Ways to improve engagement with your audience
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {analysisResult.recommendations.engagementTactics.map((tactic: string, i: number) => (
                      <li key={i} className="flex items-start">
                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-purple-100 flex items-center justify-center mr-2 mt-0.5">
                          <span className="text-xs font-medium text-purple-700">{i+1}</span>
                        </div>
                        <span>{tactic}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Growth Opportunities</CardTitle>
                  <CardDescription>
                    Opportunities to grow your Instagram presence
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {analysisResult.recommendations.growthOpportunities.map((opportunity: string, i: number) => (
                      <li key={i} className="flex items-start">
                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-2 mt-0.5">
                          <span className="text-xs font-medium text-green-700">{i+1}</span>
                        </div>
                        <span>{opportunity}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Immediate Actions</CardTitle>
                  <CardDescription>
                    Actions you can take right now to improve
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {analysisResult.recommendations.immediateActions.map((action: string, i: number) => (
                      <li key={i} className="flex items-start">
                        <div className="flex-shrink-0 w-5 h-5 rounded-full bg-amber-100 flex items-center justify-center mr-2 mt-0.5">
                          <span className="text-xs font-medium text-amber-700">{i+1}</span>
                        </div>
                        <span>{action}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 mb-4">
                <Sparkles className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Recommendations Available</h3>
              <p className="text-gray-500 max-w-md mx-auto">
                Enter your Instagram data in the Overview tab and click Analyze to see personalized recommendations.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="blueprint">
          {analysisResult && analysisResult.blueprintPertumbuhan ? (
            <div className="space-y-6">
              {/* Target Audiens Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    Target Audiens
                  </CardTitle>
                  <CardDescription>
                    Strategi dual-audience untuk B2B dan audiens umum
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* B2B Audience */}
                    <div className="border rounded-lg p-4">
                      <h3 className="text-lg font-medium mb-2 text-blue-700">Audiens B2B</h3>
                      <p className="text-sm text-gray-600 mb-4">{analysisResult.blueprintPertumbuhan.targetAudiens.b2b.deskripsi}</p>

                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Persona Utama</h4>
                        <div className="space-y-1">
                          {analysisResult.blueprintPertumbuhan.targetAudiens.b2b.personaUtama.map((persona: string, i: number) => (
                            <div key={i} className="flex items-start">
                              <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2 mt-0.5">
                                <span className="text-xs font-medium text-blue-700">{i+1}</span>
                              </div>
                              <span className="text-sm">{persona}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Pain Points</h4>
                        <div className="space-y-1">
                          {analysisResult.blueprintPertumbuhan.targetAudiens.b2b.painPoints.map((point: string, i: number) => (
                            <div key={i} className="flex items-start">
                              <div className="flex-shrink-0 text-red-500 mr-2">•</div>
                              <span className="text-sm">{point}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Strategi Khusus</h4>
                        <div className="space-y-1">
                          {analysisResult.blueprintPertumbuhan.targetAudiens.b2b.strategiKhusus.map((strategi: string, i: number) => (
                            <div key={i} className="flex items-start">
                              <div className="flex-shrink-0 text-blue-500 mr-2">→</div>
                              <span className="text-sm">{strategi}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* General Audience */}
                    <div className="border rounded-lg p-4">
                      <h3 className="text-lg font-medium mb-2 text-green-700">Audiens Umum</h3>
                      <p className="text-sm text-gray-600 mb-4">{analysisResult.blueprintPertumbuhan.targetAudiens.umum.deskripsi}</p>

                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Persona Utama</h4>
                        <div className="space-y-1">
                          {analysisResult.blueprintPertumbuhan.targetAudiens.umum.personaUtama.map((persona: string, i: number) => (
                            <div key={i} className="flex items-start">
                              <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-2 mt-0.5">
                                <span className="text-xs font-medium text-green-700">{i+1}</span>
                              </div>
                              <span className="text-sm">{persona}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Pain Points</h4>
                        <div className="space-y-1">
                          {analysisResult.blueprintPertumbuhan.targetAudiens.umum.painPoints.map((point: string, i: number) => (
                            <div key={i} className="flex items-start">
                              <div className="flex-shrink-0 text-red-500 mr-2">•</div>
                              <span className="text-sm">{point}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Strategi Khusus</h4>
                        <div className="space-y-1">
                          {analysisResult.blueprintPertumbuhan.targetAudiens.umum.strategiKhusus.map((strategi: string, i: number) => (
                            <div key={i} className="flex items-start">
                              <div className="flex-shrink-0 text-green-500 mr-2">→</div>
                              <span className="text-sm">{strategi}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Viral Strategy Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2" />
                    Strategi Viral
                  </CardTitle>
                  <CardDescription>
                    Taktik untuk mencapai pertumbuhan eksponensial
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-3">Taktik Utama</h4>
                      <div className="space-y-2">
                        {analysisResult.blueprintPertumbuhan.strategiViral.taktikUtama.map((taktik: string, i: number) => (
                          <div key={i} className="flex items-start p-2 bg-purple-50 rounded-md">
                            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-purple-100 flex items-center justify-center mr-2 mt-0.5">
                              <span className="text-xs font-medium text-purple-700">{i+1}</span>
                            </div>
                            <span className="text-sm">{taktik}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-3">Konten Potensial Viral</h4>
                      <div className="space-y-2">
                        {analysisResult.blueprintPertumbuhan.strategiViral.kontenPotensialViral.map((konten: string, i: number) => (
                          <div key={i} className="flex items-start p-2 bg-amber-50 rounded-md">
                            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-amber-100 flex items-center justify-center mr-2 mt-0.5">
                              <span className="text-xs font-medium text-amber-700">{i+1}</span>
                            </div>
                            <span className="text-sm">{konten}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Kolaborasi Strategis</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {analysisResult.blueprintPertumbuhan.strategiViral.kolaborasiStrategis.map((kolaborasi: string, i: number) => (
                        <div key={i} className="flex items-center border p-2 rounded-md">
                          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                            <Users className="h-3 w-3 text-blue-700" />
                          </div>
                          <span className="text-sm">{kolaborasi}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Calendar Events</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {analysisResult.blueprintPertumbuhan.strategiViral.calendarEvent.map((event: string, i: number) => (
                        <div key={i} className="flex items-center border p-2 rounded-md">
                          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-green-100 flex items-center justify-center mr-2">
                            <Calendar className="h-3 w-3 text-green-700" />
                          </div>
                          <span className="text-sm">{event}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Growth Roadmap Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart className="h-5 w-5 mr-2" />
                    Roadmap Pertumbuhan
                  </CardTitle>
                  <CardDescription>
                    Rencana bertahap untuk mencapai 1 juta+ followers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Phase 1 */}
                    <div className="border-l-4 border-blue-500 pl-4 py-2">
                      <h3 className="text-lg font-medium mb-1">Fase 1: {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase1.durasi}</h3>
                      <p className="text-sm text-gray-600 mb-3">Target: {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase1.target}</p>

                      <div className="mb-3">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Fokus Utama</h4>
                        <div className="space-y-1">
                          {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase1.fokusUtama.map((fokus: string, i: number) => (
                            <div key={i} className="flex items-start">
                              <div className="flex-shrink-0 text-blue-500 mr-2">→</div>
                              <span className="text-sm">{fokus}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">KPI</h4>
                        <div className="flex flex-wrap gap-2">
                          {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase1.kpi.map((kpi: string, i: number) => (
                            <span key={i} className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded-full">
                              {kpi}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Phase 2 */}
                    <div className="border-l-4 border-purple-500 pl-4 py-2">
                      <h3 className="text-lg font-medium mb-1">Fase 2: {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase2.durasi}</h3>
                      <p className="text-sm text-gray-600 mb-3">Target: {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase2.target}</p>

                      <div className="mb-3">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Fokus Utama</h4>
                        <div className="space-y-1">
                          {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase2.fokusUtama.map((fokus: string, i: number) => (
                            <div key={i} className="flex items-start">
                              <div className="flex-shrink-0 text-purple-500 mr-2">→</div>
                              <span className="text-sm">{fokus}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">KPI</h4>
                        <div className="flex flex-wrap gap-2">
                          {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase2.kpi.map((kpi: string, i: number) => (
                            <span key={i} className="text-xs bg-purple-50 text-purple-700 px-2 py-1 rounded-full">
                              {kpi}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Phase 3 */}
                    <div className="border-l-4 border-green-500 pl-4 py-2">
                      <h3 className="text-lg font-medium mb-1">Fase 3: {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase3.durasi}</h3>
                      <p className="text-sm text-gray-600 mb-3">Target: {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase3.target}</p>

                      <div className="mb-3">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Fokus Utama</h4>
                        <div className="space-y-1">
                          {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase3.fokusUtama.map((fokus: string, i: number) => (
                            <div key={i} className="flex items-start">
                              <div className="flex-shrink-0 text-green-500 mr-2">→</div>
                              <span className="text-sm">{fokus}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-2">KPI</h4>
                        <div className="flex flex-wrap gap-2">
                          {analysisResult.blueprintPertumbuhan.roadmapPertumbuhan.fase3.kpi.map((kpi: string, i: number) => (
                            <span key={i} className="text-xs bg-green-50 text-green-700 px-2 py-1 rounded-full">
                              {kpi}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Growth Hacking Tactics Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Sparkles className="h-5 w-5 mr-2" />
                    Taktik Growth Hacking
                  </CardTitle>
                  <CardDescription>
                    Teknik pertumbuhan cepat yang terbukti efektif
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-3">Teknik Akuisisi</h4>
                      <div className="space-y-2">
                        {analysisResult.blueprintPertumbuhan.taktikGrowthHacking.teknikAkuisisi.map((teknik: string, i: number) => (
                          <div key={i} className="flex items-start p-2 bg-blue-50 rounded-md">
                            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2 mt-0.5">
                              <span className="text-xs font-medium text-blue-700">{i+1}</span>
                            </div>
                            <span className="text-sm">{teknik}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-3">Teknik Retensi</h4>
                      <div className="space-y-2">
                        {analysisResult.blueprintPertumbuhan.taktikGrowthHacking.teknikRetensi.map((teknik: string, i: number) => (
                          <div key={i} className="flex items-start p-2 bg-green-50 rounded-md">
                            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-green-100 flex items-center justify-center mr-2 mt-0.5">
                              <span className="text-xs font-medium text-green-700">{i+1}</span>
                            </div>
                            <span className="text-sm">{teknik}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-3">Teknik Viral</h4>
                      <div className="space-y-2">
                        {analysisResult.blueprintPertumbuhan.taktikGrowthHacking.teknikViral.map((teknik: string, i: number) => (
                          <div key={i} className="flex items-start p-2 bg-purple-50 rounded-md">
                            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-purple-100 flex items-center justify-center mr-2 mt-0.5">
                              <span className="text-xs font-medium text-purple-700">{i+1}</span>
                            </div>
                            <span className="text-sm">{teknik}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-3">Automasi</h4>
                      <div className="space-y-2">
                        {analysisResult.blueprintPertumbuhan.taktikGrowthHacking.automasi.map((automasi: string, i: number) => (
                          <div key={i} className="flex items-start p-2 bg-amber-50 rounded-md">
                            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-amber-100 flex items-center justify-center mr-2 mt-0.5">
                              <span className="text-xs font-medium text-amber-700">{i+1}</span>
                            </div>
                            <span className="text-sm">{automasi}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Conclusion Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Kesimpulan Growth Hacker</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                    <p className="text-gray-800 italic">{analysisResult.blueprintPertumbuhan.kesimpulanGrowthHacker}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-blue-100 mb-4">
                <TrendingUp className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Blueprint Pertumbuhan Belum Tersedia</h3>
              <p className="text-gray-500 max-w-md mx-auto">
                Masukkan data Instagram Anda di tab Ringkasan dan klik Analisis untuk melihat blueprint pertumbuhan yang dipersonalisasi.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="competitors">
          <div className="space-y-6">
            <Card className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center text-base">
                  <Eye className="h-5 w-5 mr-2 text-blue-500" />
                  Analisis Kompetitor
                </CardTitle>
                <CardDescription>
                  Bandingkan performa akun Instagram Anda dengan kompetitor
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium">Kompetitor yang Dipantau</h3>
                    <Button variant="outline" size="sm" className="h-8 text-xs flex items-center">
                      <Plus className="h-3 w-3 mr-1" /> Tambah Kompetitor
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="border rounded-lg p-4 hover:border-blue-200 hover:bg-blue-50 transition-colors">
                      <div className="flex items-center mb-3">
                        <div className="w-10 h-10 rounded-full bg-gray-200 mr-3 overflow-hidden">
                          <img src="https://via.placeholder.com/40" alt="Competitor 1" className="w-full h-full object-cover" />
                        </div>
                        <div>
                          <h4 className="font-medium">@competitor1</h4>
                          <p className="text-xs text-gray-500">Tire Manufacturer</p>
                        </div>
                        <Button variant="ghost" size="sm" className="ml-auto p-0 h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-3 gap-2 text-center">
                        <div>
                          <p className="text-xs text-gray-500">Followers</p>
                          <p className="font-medium">12.5K</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Posts</p>
                          <p className="font-medium">342</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Eng. Rate</p>
                          <p className="font-medium">4.2%</p>
                        </div>
                      </div>
                    </div>

                    <div className="border rounded-lg p-4 hover:border-blue-200 hover:bg-blue-50 transition-colors">
                      <div className="flex items-center mb-3">
                        <div className="w-10 h-10 rounded-full bg-gray-200 mr-3 overflow-hidden">
                          <img src="https://via.placeholder.com/40" alt="Competitor 2" className="w-full h-full object-cover" />
                        </div>
                        <div>
                          <h4 className="font-medium">@competitor2</h4>
                          <p className="text-xs text-gray-500">Mining Equipment</p>
                        </div>
                        <Button variant="ghost" size="sm" className="ml-auto p-0 h-8 w-8">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-3 gap-2 text-center">
                        <div>
                          <p className="text-xs text-gray-500">Followers</p>
                          <p className="font-medium">8.3K</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Posts</p>
                          <p className="font-medium">187</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Eng. Rate</p>
                          <p className="font-medium">3.7%</p>
                        </div>
                      </div>
                    </div>

                    <div className="border rounded-lg p-4 border-dashed flex items-center justify-center">
                      <Button variant="ghost" className="h-full w-full flex flex-col items-center justify-center py-4">
                        <Plus className="h-6 w-6 mb-2 text-gray-400" />
                        <span className="text-sm text-gray-500">Tambah Kompetitor Baru</span>
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="mb-6">
                  <h3 className="text-sm font-medium mb-4">Perbandingan Metrik Utama</h3>
                  <div className="h-80 w-full">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={[
                          { name: 'Anda', followers: 5.2, engagement: 3.8, postFrequency: 2.5 },
                          { name: 'Competitor 1', followers: 12.5, engagement: 4.2, postFrequency: 3.2 },
                          { name: 'Competitor 2', followers: 8.3, engagement: 3.7, postFrequency: 4.1 },
                        ]}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                        <XAxis dataKey="name" stroke="#888888" />
                        <YAxis stroke="#888888" />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            borderRadius: '8px',
                            border: '1px solid #e2e8f0',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                          }}
                        />
                        <Legend
                          verticalAlign="top"
                          height={36}
                          iconType="circle"
                          iconSize={8}
                        />
                        <Bar dataKey="followers" name="Followers (K)" fill="#8884d8" radius={[4, 4, 0, 0]} />
                        <Bar dataKey="engagement" name="Engagement Rate (%)" fill="#82ca9d" radius={[4, 4, 0, 0]} />
                        <Bar dataKey="postFrequency" name="Post per Week" fill="#ffc658" radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-4">Analisis Konten Kompetitor</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium mb-3 flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-blue-500" />
                        Jenis Konten Terpopuler
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">Carousel</span>
                            <span className="text-xs text-gray-500">42%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{ width: '42%' }}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">Reels</span>
                            <span className="text-xs text-gray-500">35%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{ width: '35%' }}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">Single Image</span>
                            <span className="text-xs text-gray-500">18%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{ width: '18%' }}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">Video</span>
                            <span className="text-xs text-gray-500">5%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-600 h-2 rounded-full" style={{ width: '5%' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium mb-3 flex items-center">
                        <MessageSquare className="h-4 w-4 mr-2 text-green-500" />
                        Topik Konten Terpopuler
                      </h4>
                      <div className="space-y-3">
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">Product Features</span>
                            <span className="text-xs text-gray-500">38%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-green-600 h-2 rounded-full" style={{ width: '38%' }}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">Customer Success Stories</span>
                            <span className="text-xs text-gray-500">27%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-green-600 h-2 rounded-full" style={{ width: '27%' }}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">Industry News</span>
                            <span className="text-xs text-gray-500">20%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-green-600 h-2 rounded-full" style={{ width: '20%' }}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">Behind the Scenes</span>
                            <span className="text-xs text-gray-500">15%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-green-600 h-2 rounded-full" style={{ width: '15%' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium mb-2 flex items-center">
                    <Sparkles className="h-4 w-4 mr-2 text-blue-600" />
                    Rekomendasi Berdasarkan Analisis Kompetitor
                  </h4>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2 mt-0.5">
                        <Check className="h-3 w-3 text-blue-600" />
                      </div>
                      <span className="text-sm">Tingkatkan frekuensi posting Reels untuk menyamai kompetitor (3-4 per minggu)</span>
                    </li>
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2 mt-0.5">
                        <Check className="h-3 w-3 text-blue-600" />
                      </div>
                      <span className="text-sm">Tambahkan lebih banyak konten customer success stories yang terbukti populer di akun kompetitor</span>
                    </li>
                    <li className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center mr-2 mt-0.5">
                        <Check className="h-3 w-3 text-blue-600" />
                      </div>
                      <span className="text-sm">Gunakan hashtag industri yang lebih spesifik seperti yang digunakan kompetitor</span>
                    </li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="scheduler">
          <div className="space-y-6">
            <Card className="hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="flex items-center text-base">
                  <Calendar className="h-5 w-5 mr-2 text-blue-500" />
                  Jadwal Posting Instagram
                </CardTitle>
                <CardDescription>
                  Rencanakan dan jadwalkan konten Instagram Anda berdasarkan waktu optimal
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" className="h-8">
                      <Calendar className="h-4 w-4 mr-2" />
                      Bulan Ini
                    </Button>
                    <Button variant="outline" size="sm" className="h-8">
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="h-8">
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                  <Button className="flex items-center">
                    <Plus className="h-4 w-4 mr-2" />
                    Jadwalkan Posting Baru
                  </Button>
                </div>

                <div className="border rounded-lg overflow-hidden">
                  <div className="grid grid-cols-7 bg-gray-50 border-b">
                    {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map((day, i) => (
                      <div key={i} className="py-2 text-center text-sm font-medium text-gray-700">
                        {day}
                      </div>
                    ))}
                  </div>

                  <div className="grid grid-cols-7 auto-rows-fr">
                    {Array.from({ length: 35 }).map((_, i) => {
                      const day = i % 31 + 1;
                      const isCurrentMonth = i < 31;
                      const hasPost = [3, 7, 12, 15, 19, 23, 27].includes(day) && isCurrentMonth;
                      const isToday = day === 15 && isCurrentMonth;

                      return (
                        <div
                          key={i}
                          className={`min-h-[100px] p-1 border-r border-b relative ${
                            !isCurrentMonth ? 'bg-gray-50 text-gray-400' : ''
                          } ${isToday ? 'bg-blue-50' : ''}`}
                        >
                          <div className="text-xs font-medium p-1">{day}</div>

                          {hasPost && (
                            <div className="absolute top-6 left-0 right-0 px-1">
                              <div className={`text-xs p-1 rounded mb-1 ${
                                day % 3 === 0 ? 'bg-purple-100 text-purple-800' :
                                day % 3 === 1 ? 'bg-green-100 text-green-800' :
                                'bg-amber-100 text-amber-800'
                              }`}>
                                {day % 3 === 0 ? 'Carousel' : day % 3 === 1 ? 'Reels' : 'Image'}
                                <div className="text-[10px] truncate">
                                  {day % 3 === 0 ? 'Product Features' :
                                   day % 3 === 1 ? 'Customer Story' :
                                   'Industry News'}
                                </div>
                              </div>

                              {day === 15 && (
                                <div className="bg-blue-100 text-blue-800 text-xs p-1 rounded">
                                  Reels
                                  <div className="text-[10px] truncate">
                                    Behind the Scenes
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-sm font-medium mb-4">Posting Terjadwal Mendatang</h3>

                  <div className="space-y-3">
                    <div className="border rounded-lg p-3 hover:border-blue-200 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded bg-purple-100 flex items-center justify-center mr-3">
                            <ImageIcon className="h-5 w-5 text-purple-600" />
                          </div>
                          <div>
                            <h4 className="font-medium">Carousel: Fitur Produk Terbaru</h4>
                            <p className="text-xs text-gray-500">Terjadwal untuk 23 Juli 2023, 19:30 WIB</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm" className="h-8">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-8">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    <div className="border rounded-lg p-3 hover:border-blue-200 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded bg-green-100 flex items-center justify-center mr-3">
                            <Video className="h-5 w-5 text-green-600" />
                          </div>
                          <div>
                            <h4 className="font-medium">Reels: Testimoni Pelanggan</h4>
                            <p className="text-xs text-gray-500">Terjadwal untuk 27 Juli 2023, 08:00 WIB</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="outline" size="sm" className="h-8">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="h-8">
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium mb-2 flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-blue-600" />
                    Waktu Optimal untuk Posting
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h5 className="text-sm font-medium mb-2">Hari Kerja</h5>
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                          <span className="text-sm">08:00 - 10:00 WIB (Engagement Tinggi)</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                          <span className="text-sm">19:00 - 21:00 WIB (Engagement Tinggi)</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-amber-500 mr-2"></div>
                          <span className="text-sm">12:00 - 13:00 WIB (Engagement Sedang)</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h5 className="text-sm font-medium mb-2">Akhir Pekan</h5>
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                          <span className="text-sm">10:00 - 12:00 WIB (Engagement Tinggi)</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                          <span className="text-sm">18:00 - 20:00 WIB (Engagement Tinggi)</span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-3 h-3 rounded-full bg-amber-500 mr-2"></div>
                          <span className="text-sm">14:00 - 16:00 WIB (Engagement Sedang)</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
