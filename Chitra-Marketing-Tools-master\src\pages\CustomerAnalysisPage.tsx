import React, { useState, useEffect, useRef } from 'react';
import {
  Search,
  Filter,
  RefreshCw,
  User,
  Truck,
  ShoppingBag,
  BarChart3,
  ChevronDown,
  ChevronUp,
  Download,
  Sparkles,
  AlertCircle,
  CheckCircle,
  Info,
  FileText,
  Users,
  Calendar,
  ArrowRight
} from 'lucide-react';
import { Customer } from '../types/customer';
import { Product } from '../types';
import { FleetlistItem } from '../services/fleetlistService';
import { SalesRevenueItem } from '../services/salesRevenue2025Service';
import {
  CustomerAnalysisResult,
  IntegratedCustomerData,
  CustomerFilterOptions
} from '../types/customerAnalysis';
import {
  getIntegratedCustomerData,
  analyzeCustomerData
} from '../services/customerAnalysisService';
import {
  getAllPossibleCustomerNames
} from '../services/customerMatchingService';
import { getAllCustomers } from '../services/customerService';
import { fetchFleetlist } from '../services/fleetlistService';
import { loadSalesRevenueData } from '../services/salesRevenue2025Service';
import CustomerAnalysisCharts from '../components/CustomerAnalysisCharts';
import * as XLSX from 'xlsx';
// @ts-ignore
import html2pdf from 'html2pdf.js';

const CustomerAnalysisPage: React.FC = () => {
  // State for customer data
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [fleetData, setFleetData] = useState<FleetlistItem[]>([]);
  const [salesData, setSalesData] = useState<SalesRevenueItem[]>([]);
  const [possibleCustomerNames, setPossibleCustomerNames] = useState<string[]>([]);

  // State for selected customer
  const [selectedCustomer, setSelectedCustomer] = useState<string>('');
  const [integratedData, setIntegratedData] = useState<IntegratedCustomerData | null>(null);
  const [analysisResult, setAnalysisResult] = useState<CustomerAnalysisResult | null>(null);

  // UI state
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredCustomerNames, setFilteredCustomerNames] = useState<string[]>([]);
  const [expandedSections, setExpandedSections] = useState<{
    purchaseHistory: boolean;
    fleetAnalysis: boolean;
    productRecommendations: boolean;
    charts: boolean;
    batchAnalysis: boolean;
  }>({
    purchaseHistory: true,
    fleetAnalysis: true,
    productRecommendations: true,
    charts: true,
    batchAnalysis: false
  });
  const [error, setError] = useState<string | null>(null);

  // Advanced filtering state
  const [showAdvancedFilters, setShowAdvancedFilters] = useState<boolean>(false);
  const [filterOptions, setFilterOptions] = useState<CustomerFilterOptions>({
    searchQuery: '',
    hasPurchaseHistory: false,
    hasFleetData: false,
    minPurchaseAmount: undefined,
    maxPurchaseAmount: undefined,
    purchaseDateFrom: undefined,
    purchaseDateTo: undefined,
    productCategory: undefined
  });

  // Batch analysis state
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [batchAnalysisResults, setBatchAnalysisResults] = useState<CustomerAnalysisResult[]>([]);
  const [isBatchAnalyzing, setIsBatchAnalyzing] = useState<boolean>(false);

  // Fungsi normalisasi nama pelanggan
  function normalizeCustomerName(name: string): string {
    return name
      .replace(/(^pt\.?\s*|\s*pt\.?$)/gi, '') // Hilangkan PT di depan/belakang
      .replace(/[.,]/g, '') // Hilangkan tanda baca
      .replace(/\s+/g, ' ') // Hilangkan spasi berlebih
      .trim() // Hilangkan spasi depan/belakang
      .toLowerCase(); // Huruf kecil semua
  }

  // Proses grouping nama pelanggan saat data dimuat
  const [groupedCustomerNames, setGroupedCustomerNames] = useState<{ [key: string]: string[] }>({});
  const [groupedFilteredNames, setGroupedFilteredNames] = useState<string[]>([]); // key hasil normalisasi

  // Fungsi utilitas untuk Top 10 Produk by Revenue
  function getTopProductsByRevenue(customerNames: string[]) {
    // Ambil salesData yang sesuai dengan semua variasi nama customer
    const filtered = salesData.filter(item =>
      customerNames.map(n => n.toLowerCase()).includes(item.customerName.toLowerCase())
    );
    // Kelompokkan berdasarkan materialDescription
    const productMap: { [key: string]: { productName: string; totalRevenue: number; totalQty: number; lastPurchaseDate: string } } = {};
    filtered.forEach(item => {
      const key = item.materialDescription || 'Unknown';
      if (!productMap[key]) {
        productMap[key] = {
          productName: key,
          totalRevenue: 0,
          totalQty: 0,
          lastPurchaseDate: ''
        };
      }
      productMap[key].totalRevenue += item.revenueInDocCurr || 0;
      productMap[key].totalQty += item.qty || 0;
      if (!productMap[key].lastPurchaseDate || new Date(item.billingDate) > new Date(productMap[key].lastPurchaseDate)) {
        productMap[key].lastPurchaseDate = item.billingDate;
      }
    });
    // Urutkan dan ambil 10 teratas
    return Object.values(productMap)
      .sort((a, b) => b.totalRevenue - a.totalRevenue)
      .slice(0, 10);
  }

  // Fungsi utilitas untuk Top 10 Unit Terbanyak
  function getTopUnitsByQty(customerNames: string[]) {
    // Ambil fleetData yang sesuai dengan semua variasi nama customer
    const filtered = fleetData.filter(item =>
      customerNames.map(n => n.toLowerCase()).includes((item.customer || '').toLowerCase())
    );
    // Kelompokkan berdasarkan model/unit_manufacture + model + tire_size
    const unitMap: { [key: string]: { model: string; unit_manufacture: string; tire_size: string; totalUnit: number; totalTire: number } } = {};
    filtered.forEach(item => {
      const key = `${item.unit_manufacture || ''} ${item.model || ''} ${item.tire_size || ''}`.trim();
      if (!unitMap[key]) {
        unitMap[key] = {
          model: item.model || '-',
          unit_manufacture: item.unit_manufacture || '-',
          tire_size: item.tire_size || '-',
          totalUnit: 0,
          totalTire: 0
        };
      }
      unitMap[key].totalUnit += parseInt(item.unit_qty || '0', 10);
      unitMap[key].totalTire += parseInt(item.totaltire || '0', 10);
    });
    // Urutkan dan ambil 10 teratas berdasarkan totalUnit
    return Object.values(unitMap)
      .sort((a, b) => b.totalUnit - a.totalUnit)
      .slice(0, 10);
  }

  // Load initial data
  useEffect(() => {
    loadAllData();
  }, []);

  // Proses grouping nama pelanggan saat data dimuat
  useEffect(() => {
    // Grouping nama berdasarkan hasil normalisasi
    const groups: { [key: string]: string[] } = {};
    possibleCustomerNames.forEach((name) => {
      const key = normalizeCustomerName(name);
      if (!groups[key]) groups[key] = [];
      if (!groups[key].includes(name)) groups[key].push(name);
    });
    setGroupedCustomerNames(groups);
  }, [possibleCustomerNames]);

  // Filter customer names when search query or filter options change
  useEffect(() => {
    let filtered = Object.keys(groupedCustomerNames);

    // Apply text search filter
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(key =>
        key.includes(query) ||
        groupedCustomerNames[key].some(name => name.toLowerCase().includes(query))
      );
    }

    // Apply advanced filters if enabled
    if (showAdvancedFilters) {
      // Filter by purchase history
      if (filterOptions.hasPurchaseHistory) {
        filtered = filtered.filter(key => {
          const customerSalesData = salesData.filter(item =>
            item.customerName.toLowerCase() === key.toLowerCase()
          );
          return customerSalesData.length > 0;
        });
      }

      // Filter by fleet data
      if (filterOptions.hasFleetData) {
        filtered = filtered.filter(key => {
          const customerFleetData = fleetData.filter(item =>
            item.customer?.toLowerCase() === key.toLowerCase()
          );
          return customerFleetData.length > 0;
        });
      }

      // Filter by purchase amount
      if (filterOptions.minPurchaseAmount !== undefined || filterOptions.maxPurchaseAmount !== undefined) {
        filtered = filtered.filter(key => {
          const customerSalesData = salesData.filter(item =>
            item.customerName.toLowerCase() === key.toLowerCase()
          );

          const totalPurchase = customerSalesData.reduce((sum, item) => sum + (item.revenueInDocCurr || 0), 0);

          let passesMinFilter = true;
          let passesMaxFilter = true;

          if (filterOptions.minPurchaseAmount !== undefined) {
            passesMinFilter = totalPurchase >= filterOptions.minPurchaseAmount;
          }

          if (filterOptions.maxPurchaseAmount !== undefined) {
            passesMaxFilter = totalPurchase <= filterOptions.maxPurchaseAmount;
          }

          return passesMinFilter && passesMaxFilter;
        });
      }

      // Filter by purchase date
      if (filterOptions.purchaseDateFrom !== undefined || filterOptions.purchaseDateTo !== undefined) {
        filtered = filtered.filter(key => {
          const customerSalesData = salesData.filter(item =>
            item.customerName.toLowerCase() === key.toLowerCase()
          );

          if (customerSalesData.length === 0) return false;

          let passesFromFilter = true;
          let passesToFilter = true;

          if (filterOptions.purchaseDateFrom !== undefined) {
            const fromDate = new Date(filterOptions.purchaseDateFrom);
            passesFromFilter = customerSalesData.some(item => {
              const saleDate = new Date(item.billingDate);
              return saleDate >= fromDate;
            });
          }

          if (filterOptions.purchaseDateTo !== undefined) {
            const toDate = new Date(filterOptions.purchaseDateTo);
            passesToFilter = customerSalesData.some(item => {
              const saleDate = new Date(item.billingDate);
              return saleDate <= toDate;
            });
          }

          return passesFromFilter && passesToFilter;
        });
      }
    }

    setGroupedFilteredNames(filtered);
  }, [searchQuery, groupedCustomerNames, showAdvancedFilters, filterOptions, salesData, fleetData]);

  // Load all data from different sources
  const loadAllData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Load customers
      const customersData = getAllCustomers();
      setCustomers(customersData);

      // Load fleet data
      const fleetListData = await fetchFleetlist();
      setFleetData(fleetListData);

      // Load sales data
      const salesRevenueData = await loadSalesRevenueData();
      setSalesData(salesRevenueData);

      // Get all possible customer names
      const allNames = getAllPossibleCustomerNames(customersData, fleetListData, salesRevenueData);
      setPossibleCustomerNames(allNames);
      setFilteredCustomerNames(allNames);

      console.log(`Loaded ${customersData.length} customers, ${fleetListData.length} fleet records, ${salesRevenueData.length} sales records`);
      console.log(`Found ${allNames.length} unique customer names`);
    } catch (error) {
      console.error('Error loading data:', error);
      setError('Gagal memuat data. Silakan coba lagi.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle customer selection
  const handleSelectCustomer = async (normalizedKey: string) => {
    setSelectedCustomer(normalizedKey);
    setIntegratedData(null);
    setAnalysisResult(null);
    setIsLoading(true);
    setError(null);

    try {
      // Gabungkan data dari semua nama asli dalam grup
      const allNames = groupedCustomerNames[normalizedKey] || [];
      let allData: IntegratedCustomerData[] = [];
      for (const name of allNames) {
        const data = await getIntegratedCustomerData(name);
        if (data) allData.push(data);
      }
      // Gabungkan data (bisa disesuaikan sesuai kebutuhan analisa)
      // Untuk contoh, kita hanya ambil data pertama jika ada
      const mergedData = allData.length > 0 ? allData[0] : null;
      setIntegratedData(mergedData);

      if (mergedData) {
        setIsAnalyzing(true);
        try {
          const analysis = await analyzeCustomerData(mergedData);
          setAnalysisResult(analysis);
        } catch (aiError) {
          setError('Gagal melakukan analisis AI. Silakan cek koneksi atau coba lagi nanti.');
        }
      } else {
        setError(`Tidak dapat menemukan data untuk pelanggan: ${groupedCustomerNames[normalizedKey]?.join(', ')}`);
      }
    } catch (error) {
      console.error('Error analyzing customer:', error);
      setError('Gagal menganalisis data pelanggan. Silakan cek koneksi atau coba lagi nanti.');
    } finally {
      setIsLoading(false);
      setIsAnalyzing(false);
    }
  };

  // Toggle section expansion
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Handle batch analysis
  const handleBatchAnalysis = async () => {
    if (selectedCustomers.length === 0) {
      setError('Pilih setidaknya satu pelanggan untuk analisis batch.');
      return;
    }

    setIsBatchAnalyzing(true);
    setError(null);

    try {
      const results: CustomerAnalysisResult[] = [];

      // Analyze each selected customer
      for (const customerName of selectedCustomers) {
        // Get integrated data
        const data = await getIntegratedCustomerData(customerName);

        if (data) {
          // Analyze customer data
          const analysis = await analyzeCustomerData(data);
          results.push(analysis);
        }
      }

      setBatchAnalysisResults(results);

      // Expand the batch analysis section
      setExpandedSections(prev => ({
        ...prev,
        batchAnalysis: true
      }));
    } catch (error) {
      console.error('Error performing batch analysis:', error);
      setError('Gagal melakukan analisis batch. Silakan coba lagi.');
    } finally {
      setIsBatchAnalyzing(false);
    }
  };

  // Handle customer selection for batch analysis
  const toggleCustomerSelection = (customerName: string) => {
    setSelectedCustomers(prev => {
      if (prev.includes(customerName)) {
        return prev.filter(name => name !== customerName);
      } else {
        return [...prev, customerName];
      }
    });
  };

  // Handle advanced filter changes
  const handleFilterChange = (key: keyof CustomerFilterOptions, value: any) => {
    setFilterOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Export analysis to Excel
  const exportToExcel = () => {
    if (!analysisResult) return;

    try {
      // Create workbook
      const wb = XLSX.utils.book_new();

      // Create customer info sheet
      const customerInfo = [
        ['Customer Analysis Report'],
        ['Customer Name', analysisResult.customerName],
        ['Generated Date', new Date().toLocaleString()],
        [''],
        ['Analysis Summary'],
        [analysisResult.analysisText]
      ];
      const customerSheet = XLSX.utils.aoa_to_sheet(customerInfo);
      XLSX.utils.book_append_sheet(wb, customerSheet, 'Customer Info');

      // Create purchase history sheet
      const purchaseHistory = [
        ['Purchase History'],
        ['Total Purchases', analysisResult.purchaseHistory.totalPurchases],
        ['Total Revenue', analysisResult.purchaseHistory.totalRevenue],
        ['First Purchase Date', analysisResult.purchaseHistory.firstPurchaseDate],
        ['Last Purchase Date', analysisResult.purchaseHistory.lastPurchaseDate],
        ['Purchase Trend', analysisResult.purchaseHistory.purchaseTrend],
        [''],
        ['Top 10 Produk by Revenue'],
        ['Produk', 'Total Revenue', 'Total Qty', 'Pembelian Terakhir']
      ];

      const selectedCustomerNames = groupedCustomerNames[selectedCustomer] || [];
      const topProducts = getTopProductsByRevenue(selectedCustomerNames);
      topProducts.forEach(product => {
        purchaseHistory.push([
          product.productName,
          new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(product.totalRevenue),
          product.totalQty,
          product.lastPurchaseDate
        ]);
      });

      const purchaseSheet = XLSX.utils.aoa_to_sheet(purchaseHistory);
      XLSX.utils.book_append_sheet(wb, purchaseSheet, 'Top 10 Produk by Revenue');

      // Create fleet analysis sheet
      const fleetAnalysis = [
        ['Fleet Analysis'],
        ['Total Units', analysisResult.fleetAnalysis.totalUnits],
        ['Total Tires', analysisResult.fleetAnalysis.totalTires],
        ['Unique Tire Sizes', analysisResult.fleetAnalysis.uniqueTireSizes.join(', ')],
        [''],
        ['Top 10 Unit Terbanyak'],
        ['Model', 'Manufacture', 'Size Tyre', 'Total Unit', 'Total Ban']
      ];

      const topUnits = getTopUnitsByQty(selectedCustomerNames);
      topUnits.forEach(unit => {
        fleetAnalysis.push([
          unit.model,
          unit.unit_manufacture,
          unit.tire_size,
          unit.totalUnit,
          unit.totalTire
        ]);
      });

      const fleetSheet = XLSX.utils.aoa_to_sheet(fleetAnalysis);
      XLSX.utils.book_append_sheet(wb, fleetSheet, 'Top 10 Unit Terbanyak');

      // Create product recommendations sheet
      const recommendations = [
        ['Product Recommendations'],
        ['Product Name', 'Reason', 'Confidence', 'Potential Revenue']
      ];

      analysisResult.productRecommendations.forEach(rec => {
        recommendations.push([
          rec.productName,
          rec.reason,
          `${rec.confidence}%`,
          rec.potentialRevenue
        ]);
      });

      const recSheet = XLSX.utils.aoa_to_sheet(recommendations);
      XLSX.utils.book_append_sheet(wb, recSheet, 'Recommendations');

      // Save the file
      XLSX.writeFile(wb, `Customer_Analysis_${analysisResult.customerName.replace(/\s+/g, '_')}.xlsx`);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      setError('Gagal mengekspor data ke Excel. Silakan coba lagi.');
    }
  };

  const analysisPdfRef = useRef<HTMLDivElement>(null);

  const handleExportPDF = () => {
    if (analysisPdfRef.current) {
      html2pdf()
        .set({
          margin: [16, 16, 16, 16],
          filename: `Analisis-Pelanggan-${analysisResult?.customerName || 'Export'}.pdf`,
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: { scale: 2, useCORS: true },
          jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        })
        .from(analysisPdfRef.current)
        .save();
    }
  };

  const selectedCustomerNames = groupedCustomerNames[selectedCustomer] || [];
  const filteredSales = salesData.filter(item =>
    selectedCustomerNames.map(n => n.toLowerCase()).includes(item.customerName.toLowerCase())
  );
  const totalPembelian = filteredSales.reduce((sum, item) => sum + (item.qty || 0), 0);
  const totalRevenue = filteredSales.reduce((sum, item) => sum + (item.revenueInDocCurr || 0), 0);
  let purchaseTrend = 'Stabil';
  if (filteredSales.length > 1) {
    const monthly = {};
    filteredSales.forEach(item => {
      if (item.billingDate) {
        const month = item.billingDate.slice(0, 7);
        if (!monthly[month]) monthly[month] = 0;
        monthly[month] += item.revenueInDocCurr || 0;
      }
    });
    const months = Object.keys(monthly).sort();
    if (months.length > 1) {
      const first = monthly[months[0]];
      const last = monthly[months[months.length - 1]];
      if (last > first) purchaseTrend = 'Meningkat';
      else if (last < first) purchaseTrend = 'Menurun';
    }
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex flex-col space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Analisis Pelanggan</h1>
            <p className="text-gray-600 mt-1">
              Analisis data pelanggan dari berbagai sumber dengan AI
            </p>
          </div>

          <div className="flex space-x-2 mt-4 md:mt-0">
            <button
              onClick={loadAllData}
              className="flex items-center px-3 py-2 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100"
              disabled={isLoading}
            >
              <RefreshCw size={16} className="mr-2" />
              Refresh Data
            </button>

            {analysisResult && (
              <button
                onClick={exportToExcel}
                className="flex items-center px-3 py-2 bg-green-50 text-green-600 rounded-md hover:bg-green-100"
              >
                <Download size={16} className="mr-2" />
                Export Excel
              </button>
            )}
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={18} className="text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Cari pelanggan..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className="flex space-x-2">
              <button
                className="flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                disabled={isLoading || !selectedCustomer}
                onClick={() => selectedCustomer && handleSelectCustomer(selectedCustomer)}
              >
                <Sparkles size={18} className="mr-2" />
                Analisis Pelanggan
              </button>

              <button
                className="flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                disabled={isLoading || selectedCustomers.length === 0 || isBatchAnalyzing}
                onClick={handleBatchAnalysis}
              >
                <Users size={18} className="mr-2" />
                Analisis Batch
              </button>

              <button
                className="flex items-center justify-center px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              >
                <Filter size={18} className="mr-2" />
                Filter
              </button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="mt-4 p-4 border border-gray-200 rounded-md bg-gray-50">
              <h3 className="text-md font-medium text-gray-700 mb-3">Filter Lanjutan</h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={filterOptions.hasPurchaseHistory}
                      onChange={(e) => handleFilterChange('hasPurchaseHistory', e.target.checked)}
                      className="rounded text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Memiliki Riwayat Pembelian</span>
                  </label>
                </div>

                <div>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={filterOptions.hasFleetData}
                      onChange={(e) => handleFilterChange('hasFleetData', e.target.checked)}
                      className="rounded text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Memiliki Data Armada</span>
                  </label>
                </div>

                <div className="md:col-span-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Rentang Nilai Pembelian
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="number"
                        placeholder="Min"
                        value={filterOptions.minPurchaseAmount || ''}
                        onChange={(e) => handleFilterChange('minPurchaseAmount', e.target.value ? Number(e.target.value) : undefined)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                      />
                      <input
                        type="number"
                        placeholder="Max"
                        value={filterOptions.maxPurchaseAmount || ''}
                        onChange={(e) => handleFilterChange('maxPurchaseAmount', e.target.value ? Number(e.target.value) : undefined)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Rentang Tanggal Pembelian
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="date"
                        value={filterOptions.purchaseDateFrom || ''}
                        onChange={(e) => handleFilterChange('purchaseDateFrom', e.target.value || undefined)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                      />
                      <input
                        type="date"
                        value={filterOptions.purchaseDateTo || ''}
                        onChange={(e) => handleFilterChange('purchaseDateTo', e.target.value || undefined)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Customer List */}
          <div className="mt-4 max-h-60 overflow-y-auto border border-gray-200 rounded-md">
            {groupedFilteredNames.length > 0 ? (
              <ul className="divide-y divide-gray-200">
                {groupedFilteredNames.map((key, index) => (
                  <li key={index}>
                    <div className="flex items-center px-4 py-2 hover:bg-gray-50">
                      <input
                        type="checkbox"
                        checked={selectedCustomers.includes(key)}
                        onChange={() => toggleCustomerSelection(key)}
                        className="mr-3 rounded text-blue-600 focus:ring-blue-500"
                      />
                      <button
                        className={`flex-1 text-left ${selectedCustomer === key ? 'text-blue-700 font-medium' : ''}`}
                        onClick={() => setSelectedCustomer(key)}
                      >
                        <div className="flex flex-col">
                          <div className="flex items-center">
                            <User size={16} className="mr-2 text-gray-500" />
                            <span>{groupedCustomerNames[key][0]}</span>
                          </div>
                          {groupedCustomerNames[key].length > 1 && (
                            <span className="text-xs text-gray-400 mt-1">Gabungan dari: {groupedCustomerNames[key].join('; ')}</span>
                          )}
                        </div>
                      </button>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="p-4 text-center text-gray-500">
                {searchQuery ? 'Tidak ada pelanggan yang cocok dengan pencarian' : 'Tidak ada data pelanggan'}
              </div>
            )}
          </div>

          {/* Selected Customers Summary */}
          {selectedCustomers.length > 0 && (
            <div className="mt-2 flex justify-between items-center text-sm text-gray-600">
              <span>{selectedCustomers.length} pelanggan dipilih untuk analisis batch</span>
              <button
                className="text-red-600 hover:text-red-800"
                onClick={() => setSelectedCustomers([])}
              >
                Hapus Semua
              </button>
            </div>
          )}
        </div>

        {/* Loading State */}
        {(isLoading || isAnalyzing) && (
          <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700 mb-4"></div>
            <p className="text-gray-600">
              {isLoading ? 'Memuat data pelanggan...' : 'Menganalisis data pelanggan dengan AI...'}
            </p>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-start">
            <AlertCircle size={20} className="text-red-500 mr-3 mt-0.5" />
            <div>
              <h3 className="font-medium text-red-800">Error</h3>
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        {/* Analysis Results */}
        {analysisResult && !isLoading && !isAnalyzing && (
          <div ref={analysisPdfRef} className="space-y-6">
            {/* Customer Overview */}
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="bg-blue-600 px-6 py-4">
                <h2 className="text-xl font-bold text-white">
                  Analisis Pelanggan: {analysisResult.customerName}
                </h2>
              </div>

              <div className="p-6">
                <div className="prose max-w-none">
                  {/* Tampilkan insight AI jika ada dan berupa object, bukan hanya analysisText */}
                  {analysisResult && typeof analysisResult === 'object' && Object.entries(analysisResult).some(([key]) => key.includes('Insight') || key.includes('Peluang') || key.includes('Rekomendasi') || key.includes('Strategi')) ? (
                    <div className="space-y-6">
                      {Object.entries(analysisResult).map(([section, items]) => {
                        let icon = null;
                        if (section.includes('Insight')) icon = <Sparkles className="inline-block text-yellow-500 mr-2" size={20} />;
                        if (section.includes('Peluang')) icon = <BarChart3 className="inline-block text-green-500 mr-2" size={20} />;
                        if (section.includes('Rekomendasi')) icon = <CheckCircle className="inline-block text-blue-500 mr-2" size={20} />;
                        if (section.includes('Strategi')) icon = <Users className="inline-block text-purple-500 mr-2" size={20} />;
                        return (section.includes('Insight') || section.includes('Peluang') || section.includes('Rekomendasi') || section.includes('Strategi')) && Array.isArray(items) ? (
                          <div key={section} className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center mb-2">
                              {icon}
                              <h3 className="text-lg font-semibold text-gray-800 mb-0">{section.replace(/^[^a-zA-Z0-9]+/, '')}</h3>
                            </div>
                            <table className="min-w-full divide-y divide-gray-200">
                              <tbody>
                                {items.map((item, idx) => (
                                  <tr key={idx}>
                                    <td className="py-2 align-top w-2"><span className="text-blue-400 font-bold">•</span></td>
                                    <td className="py-2 text-gray-700">{item}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        ) : null;
                      })}
                    </div>
                  ) : (
                    <p>{analysisResult.analysisText}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Purchase History */}
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div
                className="px-6 py-4 bg-gray-50 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('purchaseHistory')}
              >
                <div className="flex items-center">
                  <ShoppingBag size={20} className="text-blue-600 mr-3" />
                  <h3 className="text-lg font-medium text-gray-800">Riwayat Pembelian</h3>
                </div>
                {expandedSections.purchaseHistory ? (
                  <ChevronUp size={20} className="text-gray-500" />
                ) : (
                  <ChevronDown size={20} className="text-gray-500" />
                )}
              </div>

              {expandedSections.purchaseHistory && (
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm text-blue-600 font-medium">Total Pembelian</p>
                      <p className="text-2xl font-bold text-blue-800">{totalPembelian}</p>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg">
                      <p className="text-sm text-green-600 font-medium">Total Revenue</p>
                      <p className="text-2xl font-bold text-green-800">{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(totalRevenue)}</p>
                    </div>

                    <div className="bg-purple-50 p-4 rounded-lg">
                      <p className="text-sm text-purple-600 font-medium">Tren Pembelian</p>
                      <p className="text-2xl font-bold text-purple-800 capitalize">{purchaseTrend}</p>
                    </div>
                  </div>

                  {/* Top 10 Produk by Revenue */}
                  <div className="mt-6">
                    <h4 className="text-md font-medium text-gray-700 mb-3">Top 10 Produk Berdasarkan Revenue</h4>
                    {getTopProductsByRevenue(groupedCustomerNames[selectedCustomer] || []).length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produk</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Revenue</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Qty</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pembelian Terakhir</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {getTopProductsByRevenue(groupedCustomerNames[selectedCustomer] || []).map((product, idx) => (
                              <tr key={idx}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.productName}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(product.totalRevenue)}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.totalQty}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.lastPurchaseDate}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-gray-500 italic">Tidak ada data produk dari Sales Revenue 2025</p>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Fleet Analysis */}
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div
                className="px-6 py-4 bg-gray-50 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('fleetAnalysis')}
              >
                <div className="flex items-center">
                  <Truck size={20} className="text-blue-600 mr-3" />
                  <h3 className="text-lg font-medium text-gray-800">Analisis Armada</h3>
                </div>
                {expandedSections.fleetAnalysis ? (
                  <ChevronUp size={20} className="text-gray-500" />
                ) : (
                  <ChevronDown size={20} className="text-gray-500" />
                )}
              </div>

              {expandedSections.fleetAnalysis && (
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm text-blue-600 font-medium">Total Unit</p>
                      <p className="text-2xl font-bold text-blue-800">
                        {analysisResult.fleetAnalysis.totalUnits}
                      </p>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg">
                      <p className="text-sm text-green-600 font-medium">Total Ban</p>
                      <p className="text-2xl font-bold text-green-800">
                        {analysisResult.fleetAnalysis.totalTires}
                      </p>
                    </div>

                    <div className="bg-purple-50 p-4 rounded-lg">
                      <p className="text-sm text-purple-600 font-medium">Ukuran Ban Unik</p>
                      <p className="text-2xl font-bold text-purple-800">
                        {analysisResult.fleetAnalysis.uniqueTireSizes.length}
                      </p>
                    </div>
                  </div>

                  {/* Top 10 Unit Terbanyak */}
                  <div className="mt-6">
                    <h4 className="text-md font-medium text-gray-700 mb-3">Top 10 Unit Terbanyak</h4>
                    {getTopUnitsByQty(groupedCustomerNames[selectedCustomer] || []).length > 0 ? (
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manufacture</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size Tyre</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Unit</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Ban</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {getTopUnitsByQty(groupedCustomerNames[selectedCustomer] || []).map((unit, idx) => (
                              <tr key={idx}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{unit.model}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{unit.unit_manufacture}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{unit.tire_size}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{unit.totalUnit}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{unit.totalTire}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <p className="text-gray-500 italic">Tidak ada data unit dari Fleet Analyzer</p>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Product Recommendations */}
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div
                className="px-6 py-4 bg-gray-50 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('productRecommendations')}
              >
                <div className="flex items-center">
                  <BarChart3 size={20} className="text-blue-600 mr-3" />
                  <h3 className="text-lg font-medium text-gray-800">Rekomendasi Produk</h3>
                </div>
                {expandedSections.productRecommendations ? (
                  <ChevronUp size={20} className="text-gray-500" />
                ) : (
                  <ChevronDown size={20} className="text-gray-500" />
                )}
              </div>

              {expandedSections.productRecommendations && (
                <div className="p-6">
                  {analysisResult.productRecommendations.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {analysisResult.productRecommendations.map((rec, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <h4 className="text-md font-medium text-gray-800">{rec.productName}</h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              rec.confidence >= 80 ? 'bg-green-100 text-green-800' :
                              rec.confidence >= 60 ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {rec.confidence}% Confidence
                            </span>
                          </div>
                          <p className="mt-2 text-sm text-gray-600">{rec.reason}</p>
                          <div className="mt-3 flex justify-between items-center">
                            <span className="text-sm text-gray-500">Potensi Revenue:</span>
                            <span className="font-medium text-green-600">
                              {new Intl.NumberFormat('id-ID', {
                                style: 'currency',
                                currency: 'IDR',
                                minimumFractionDigits: 0
                              }).format(rec.potentialRevenue)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 italic">Tidak ada rekomendasi produk</p>
                  )}
                </div>
              )}
            </div>

            {/* Data Visualization Charts */}
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div
                className="px-6 py-4 bg-gray-50 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('charts')}
              >
                <div className="flex items-center">
                  <BarChart3 size={20} className="text-blue-600 mr-3" />
                  <h3 className="text-lg font-medium text-gray-800">Visualisasi Data</h3>
                </div>
                {expandedSections.charts ? (
                  <ChevronUp size={20} className="text-gray-500" />
                ) : (
                  <ChevronDown size={20} className="text-gray-500" />
                )}
              </div>

              {expandedSections.charts && (
                <div className="p-6">
                  <CustomerAnalysisCharts analysisResult={analysisResult} />
                </div>
              )}
            </div>
          </div>
        )}

        {/* Batch Analysis Results */}
        {batchAnalysisResults.length > 0 && !isLoading && !isBatchAnalyzing && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div
                className="px-6 py-4 bg-gray-50 flex justify-between items-center cursor-pointer"
                onClick={() => toggleSection('batchAnalysis')}
              >
                <div className="flex items-center">
                  <Users size={20} className="text-blue-600 mr-3" />
                  <h3 className="text-lg font-medium text-gray-800">Hasil Analisis Batch ({batchAnalysisResults.length} Pelanggan)</h3>
                </div>
                {expandedSections.batchAnalysis ? (
                  <ChevronUp size={20} className="text-gray-500" />
                ) : (
                  <ChevronDown size={20} className="text-gray-500" />
                )}
              </div>

              {expandedSections.batchAnalysis && (
                <div className="p-6">
                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm text-blue-600 font-medium">Total Pelanggan</p>
                      <p className="text-2xl font-bold text-blue-800">
                        {batchAnalysisResults.length}
                      </p>
                    </div>

                    <div className="bg-green-50 p-4 rounded-lg">
                      <p className="text-sm text-green-600 font-medium">Total Revenue</p>
                      <p className="text-2xl font-bold text-green-800">
                        {new Intl.NumberFormat('id-ID', {
                          style: 'currency',
                          currency: 'IDR',
                          minimumFractionDigits: 0
                        }).format(batchAnalysisResults.reduce((sum, result) =>
                          sum + result.purchaseHistory.totalRevenue, 0))}
                      </p>
                    </div>

                    <div className="bg-purple-50 p-4 rounded-lg">
                      <p className="text-sm text-purple-600 font-medium">Total Unit</p>
                      <p className="text-2xl font-bold text-purple-800">
                        {batchAnalysisResults.reduce((sum, result) =>
                          sum + result.fleetAnalysis.totalUnits, 0)}
                      </p>
                    </div>
                  </div>

                  {/* Batch Results Table */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Pelanggan
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total Revenue
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total Unit
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total Ban
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Tren Pembelian
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Aksi
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {batchAnalysisResults.map((result, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                              {result.customerName}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Intl.NumberFormat('id-ID', {
                                style: 'currency',
                                currency: 'IDR',
                                minimumFractionDigits: 0
                              }).format(result.purchaseHistory.totalRevenue)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {result.fleetAnalysis.totalUnits}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {result.fleetAnalysis.totalTires}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                result.purchaseHistory.purchaseTrend === 'increasing' ? 'bg-green-100 text-green-800' :
                                result.purchaseHistory.purchaseTrend === 'decreasing' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {result.purchaseHistory.purchaseTrend === 'increasing' ? 'Meningkat' :
                                 result.purchaseHistory.purchaseTrend === 'decreasing' ? 'Menurun' : 'Stabil'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              <button
                                className="text-blue-600 hover:text-blue-800"
                                onClick={() => {
                                  setSelectedCustomer(result.customerName);
                                  handleSelectCustomer(result.customerName);
                                }}
                              >
                                <div className="flex items-center">
                                  <span>Detail</span>
                                  <ArrowRight size={16} className="ml-1" />
                                </div>
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Export Batch Results Button */}
                  <div className="mt-6 flex justify-end">
                    <button
                      onClick={() => {
                        // Create workbook
                        const wb = XLSX.utils.book_new();

                        // Create summary sheet
                        const summaryData = [
                          ['Batch Analysis Summary'],
                          ['Total Customers', batchAnalysisResults.length],
                          ['Total Revenue', batchAnalysisResults.reduce((sum, result) => sum + result.purchaseHistory.totalRevenue, 0)],
                          ['Total Units', batchAnalysisResults.reduce((sum, result) => sum + result.fleetAnalysis.totalUnits, 0)],
                          ['Total Tires', batchAnalysisResults.reduce((sum, result) => sum + result.fleetAnalysis.totalTires, 0)],
                          [''],
                          ['Customer', 'Total Revenue', 'Total Units', 'Total Tires', 'Purchase Trend']
                        ];

                        batchAnalysisResults.forEach(result => {
                          summaryData.push([
                            result.customerName,
                            result.purchaseHistory.totalRevenue,
                            result.fleetAnalysis.totalUnits,
                            result.fleetAnalysis.totalTires,
                            result.purchaseHistory.purchaseTrend
                          ]);
                        });

                        const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
                        XLSX.utils.book_append_sheet(wb, summarySheet, 'Batch Analysis');

                        // Save the file
                        XLSX.writeFile(wb, `Batch_Analysis_${new Date().toISOString().split('T')[0]}.xlsx`);
                      }}
                      className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                    >
                      <Download size={16} className="mr-2" />
                      Export Excel
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {analysisResult && !isLoading && !isAnalyzing && (
          <div className="flex justify-end mb-4">
            <button
              onClick={handleExportPDF}
              className="bg-red-600 text-white px-4 py-2 rounded-lg shadow hover:bg-red-700 transition-colors font-medium"
            >
              Export PDF
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerAnalysisPage;
