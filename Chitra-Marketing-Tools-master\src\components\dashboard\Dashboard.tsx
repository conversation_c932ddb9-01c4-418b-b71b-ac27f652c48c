import React, { useState, useEffect } from 'react';
import {
  Users,
  Package,
  DollarSign,
  TrendingUp,
  Calendar,
  BarChart3,
  ShoppingCart,
  Truck,
  CloudRain,
  AlertCircle,
  CheckCircle,
  Clock,
  Sparkles,
  ArrowRight,
  RefreshCw,
  FileText,
  Activity,
  Calculator
} from 'lucide-react';
import StatCard from './StatCard';
import DashboardCard from './DashboardCard';
import ChartCard from './ChartCard';
import { Button } from '../ui/button';
import { fetchSalesData } from '../../services/fleetRevenueService';
import { getSeasonalInsights } from '../../services/seasonalMarketingService';
import { Customer } from '../../types/customer';
import { Product } from '../../types';
import { CoalPrice, CoalPriceTrend } from '../../services/coalPriceService';
import { SalesItem } from '../../services/fleetRevenueService';
import { SeasonalInsight } from '../../types/seasonalMarketing';
import { formatCurrency } from '../../utils/pricing';
import { Link } from 'react-router-dom';
import { useDataHub } from '../DataHubProvider';
import { useProducts, useCustomers, useCoalPrices } from '../../hooks/useDataHubHooks';

export default function Dashboard() {
  // Use Data Hub hooks
  const { refreshData } = useDataHub();
  const { products, loading: productsLoading } = useProducts();
  const { customers, loading: customersLoading } = useCustomers();
  const { coalPrices, coalTrend, loading: coalPricesLoading } = useCoalPrices();

  // State for other data
  const [salesData, setSalesData] = useState<SalesItem[]>([]);
  const [insights, setInsights] = useState<SeasonalInsight[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch sales data and insights in parallel
        // Products, customers, and coal prices are already being fetched by the Data Hub hooks
        const [salesDataResult, insightsData] = await Promise.all([
          fetchSalesData(),
          getSeasonalInsights()
        ]);

        setSalesData(salesDataResult);
        setInsights(insightsData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Calculate summary metrics
  const totalCustomers = customers.length;
  const totalProducts = products.length;
  const totalSales = salesData.reduce((sum, item) => sum + (item.Revenue || 0), 0);
  const averagePrice = products.length > 0
    ? products.reduce((sum, product) => sum + (product.price || 0), 0) / products.length
    : 0;

  // Get current coal price
  const currentCoalPrice = coalPrices.length > 0 ? coalPrices[0].price : 0;

  // Get high priority insights
  const highPriorityInsights = insights
    .filter(insight => insight.recommendationLevel === 'high')
    .slice(0, 3);

  // Prepare chart data with marketing context
  const prepareMarketingSalesChartData = () => {
    // Group sales by month
    const salesByMonth: Record<string, number> = {};
    const marketingEffectivenessData: Record<string, number> = {};
    const promotionPeriods: Record<string, boolean> = {};

    // Process sales data
    salesData.forEach(item => {
      if (item.Date) {
        const date = new Date(item.Date);
        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;

        if (!salesByMonth[monthYear]) {
          salesByMonth[monthYear] = 0;
          marketingEffectivenessData[monthYear] = 0;
        }

        salesByMonth[monthYear] += item.Revenue || 0;

        // Calculate a simple marketing effectiveness metric (revenue per customer)
        // This is a placeholder - in a real app, you would use actual marketing metrics
        if (item.CustomerCount && item.CustomerCount > 0) {
          marketingEffectivenessData[monthYear] =
            (marketingEffectivenessData[monthYear] || 0) +
            ((item.Revenue || 0) / item.CustomerCount);
        }
      }
    });

    // Get seasonal insights to identify promotion periods
    const seasonalInsights = insights || [];
    seasonalInsights.forEach(insight => {
      if (insight.startDate && insight.endDate) {
        const startDate = new Date(insight.startDate);
        const endDate = new Date(insight.endDate);

        // Mark all months in this date range as promotion periods
        let currentDate = new Date(startDate);
        while (currentDate <= endDate) {
          const monthYear = `${currentDate.getMonth() + 1}/${currentDate.getFullYear()}`;
          promotionPeriods[monthYear] = true;

          // Move to next month
          currentDate.setMonth(currentDate.getMonth() + 1);
        }
      }
    });

    // Sort by date
    const sortedMonths = Object.keys(salesByMonth).sort((a, b) => {
      const [monthA, yearA] = a.split('/').map(Number);
      const [monthB, yearB] = b.split('/').map(Number);

      if (yearA !== yearB) return yearA - yearB;
      return monthA - monthB;
    });

    // Take last 6 months
    const last6Months = sortedMonths.slice(-6);

    // Format month names for better readability
    const formattedLabels = last6Months.map(month => {
      const [monthNum, year] = month.split('/').map(Number);
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agt', 'Sep', 'Okt', 'Nov', 'Des'];
      return `${monthNames[monthNum-1]} ${year}`;
    });

    // Normalize marketing effectiveness data for visualization
    const maxSales = Math.max(...last6Months.map(month => salesByMonth[month]));
    const normalizedMarketingData = last6Months.map(month => {
      const rawValue = marketingEffectivenessData[month] || 0;
      // Scale to be in similar range as sales data for visualization
      return (rawValue / (maxSales / 100)) * maxSales * 0.7;
    });

    return {
      labels: formattedLabels,
      salesData: last6Months.map(month => salesByMonth[month]),
      marketingData: normalizedMarketingData,
      promotionPeriods: last6Months.map(month => promotionPeriods[month] || false)
    };
  };

  const prepareProductChartData = () => {
    // Group products by category or type
    const productsByCategory: Record<string, number> = {};

    products.forEach(product => {
      const category = product.materialDescription?.split(' ')[0] || 'Other';

      if (!productsByCategory[category]) {
        productsByCategory[category] = 0;
      }

      productsByCategory[category]++;
    });

    // Sort by count
    const sortedCategories = Object.keys(productsByCategory)
      .sort((a, b) => productsByCategory[b] - productsByCategory[a])
      .slice(0, 5); // Take top 5

    return {
      labels: sortedCategories,
      data: sortedCategories.map(category => productsByCategory[category])
    };
  };

  const marketingSalesChartData = prepareMarketingSalesChartData();
  const productChartData = prepareProductChartData();

  // Loading state
  if (loading || productsLoading || customersLoading || coalPricesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat data dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <BarChart3 className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Dashboard Chitra Marketing Tools</h1>
        </div>
        <Button
          size="sm"
          variant="outline"
          onClick={() => {
            refreshData(); // Refresh Data Hub data
            window.location.reload(); // Also reload the page for other data
          }}
          className="flex items-center"
        >
          <RefreshCw className="h-4 w-4 mr-1" />
          Refresh Data
        </Button>
      </div>

      {/* Welcome Banner */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white rounded-lg p-6 shadow-md">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div>
            <h2 className="text-xl font-bold mb-2">Selamat Datang di Chitra Marketing Tools</h2>
            <p className="text-blue-100 mb-4">
              Pantau metrik penting, analisis data, dan kelola strategi pemasaran Anda dari satu tempat.
            </p>
            <div className="flex space-x-3">
              <Link to="/seasonal-marketing-calendar">
                <Button size="sm" className="bg-yellow-500 text-black font-medium hover:bg-yellow-400">
                  <Calendar className="h-4 w-4 mr-1" />
                  Kalender Promosi
                </Button>
              </Link>
              <Link to="/sales-dashboard">
                <Button size="sm" className="bg-green-500 text-black font-medium hover:bg-green-400">
                  <BarChart3 className="h-4 w-4 mr-1" />
                  Sales Dashboard
                </Button>
              </Link>
            </div>
          </div>
          <div className="hidden md:block">
            <Activity className="h-24 w-24 text-blue-200 opacity-75" />
          </div>
        </div>
      </div>

      {/* Stats Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Link to="/customers" className="transition-transform hover:scale-105">
          <StatCard
            title="Total Pelanggan"
            value={totalCustomers}
            icon={Users}
            description="Jumlah pelanggan aktif"
          />
        </Link>
        <Link to="/products" className="transition-transform hover:scale-105">
          <StatCard
            title="Total Produk"
            value={totalProducts}
            icon={Package}
            description="Jumlah produk tersedia"
          />
        </Link>
        <Link to="/sales-dashboard" className="transition-transform hover:scale-105">
          <StatCard
            title="Total Penjualan"
            value={formatCurrency(totalSales)}
            icon={DollarSign}
            description="Total pendapatan"
            trend={totalSales > 1000000 ? 'up' : 'neutral'}
            trendValue="Dibanding bulan lalu"
          />
        </Link>
        <Link to="/coal-price-data" className="transition-transform hover:scale-105">
          <StatCard
            title="Harga Batu Bara"
            value={`$${currentCoalPrice.toFixed(2)}`}
            icon={TrendingUp}
            description="Harga acuan terbaru"
            trend={coalTrend?.trend || 'neutral'}
            trendValue={coalTrend ? `${coalTrend.percentChange.toFixed(1)}%` : 'Stabil'}
          />
        </Link>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard
          title="Performa Marketing & Penjualan 6 Bulan Terakhir"
          icon={BarChart3}
          chartType="line"
          labels={marketingSalesChartData.labels}
          datasets={[
            {
              label: 'Penjualan',
              data: marketingSalesChartData.salesData,
              borderColor: 'rgb(59, 130, 246)',
              backgroundColor: 'rgba(59, 130, 246, 0.1)'
            },
            {
              label: 'Efektivitas Marketing',
              data: marketingSalesChartData.marketingData,
              borderColor: 'rgb(236, 72, 153)',
              backgroundColor: 'rgba(236, 72, 153, 0.1)',
              borderDash: marketingSalesChartData.promotionPeriods.some(p => p) ? [5, 5] : undefined
            }
          ]}
        />
        <ChartCard
          title="Distribusi Produk"
          icon={Package}
          chartType="pie"
          labels={productChartData.labels}
          datasets={[
            {
              label: 'Jumlah Produk',
              data: productChartData.data
            }
          ]}
        />
      </div>

      {/* Additional Info Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Seasonal Insights */}
        <DashboardCard
          title="Insight Musiman Terbaru"
          icon={Calendar}
          footer={
            <Link to="/seasonal-marketing-calendar" className="flex items-center text-blue-600 hover:text-blue-800">
              <span>Lihat semua insight</span>
              <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          }
        >
          <div className="space-y-3">
            {highPriorityInsights.length > 0 ? (
              highPriorityInsights.map((insight, index) => (
                <div key={index} className="p-3 border rounded-md flex items-start hover:bg-blue-50 transition-colors">
                  <Sparkles className="h-5 w-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium">{insight.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{insight.description.substring(0, 100)}...</p>
                    <div className="flex items-center mt-2 text-xs text-gray-500">
                      <Calendar className="h-3 w-3 mr-1" />
                      <span>{new Date(insight.startDate).toLocaleDateString('id-ID', { day: 'numeric', month: 'long' })} - {new Date(insight.endDate).toLocaleDateString('id-ID', { day: 'numeric', month: 'long' })}</span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 italic">Tidak ada insight prioritas tinggi saat ini.</p>
            )}
          </div>
        </DashboardCard>

        {/* Status Overview */}
        <DashboardCard
          title="Status Operasional"
          icon={CheckCircle}
          footer={
            <div className="flex items-center text-gray-500">
              <Clock className="h-4 w-4 mr-1" />
              <span>Terakhir diperbarui: {new Date().toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' })}</span>
            </div>
          }
        >
          <div className="space-y-3">
            <div className="flex items-center justify-between p-2 border-b">
              <div className="flex items-center">
                <CloudRain className="h-5 w-5 text-blue-500 mr-2" />
                <span>Curah Hujan Kalimantan</span>
              </div>
              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                Sedang
              </span>
            </div>
            <div className="flex items-center justify-between p-2 border-b">
              <div className="flex items-center">
                <Truck className="h-5 w-5 text-green-500 mr-2" />
                <span>Ketersediaan Produk</span>
              </div>
              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                Baik
              </span>
            </div>
            <div className="flex items-center justify-between p-2 border-b">
              <div className="flex items-center">
                <ShoppingCart className="h-5 w-5 text-purple-500 mr-2" />
                <span>Permintaan Pasar</span>
              </div>
              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                Tinggi
              </span>
            </div>
            <div className="flex items-center justify-between p-2">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                <span>Status Kompetitor</span>
              </div>
              <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">
                Agresif
              </span>
            </div>
          </div>
        </DashboardCard>
      </div>

      {/* Quick Access Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Link to="/bundling-proposal" className="bg-white rounded-lg border shadow-sm p-4 flex items-center hover:bg-blue-50 transition-colors">
          <div className="p-3 rounded-full bg-blue-100 mr-4">
            <FileText className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="font-medium">Proposal Marketing</h3>
            <p className="text-sm text-gray-500">Buat proposal bundling dan promosi</p>
          </div>
        </Link>
        <Link to="/promo-simulation" className="bg-white rounded-lg border shadow-sm p-4 flex items-center hover:bg-blue-50 transition-colors">
          <div className="p-3 rounded-full bg-purple-100 mr-4">
            <Sparkles className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h3 className="font-medium">Simulasi Promo</h3>
            <p className="text-sm text-gray-500">Simulasikan promosi bisnis</p>
          </div>
        </Link>
        <Link to="/bundling-calculator" className="bg-white rounded-lg border shadow-sm p-4 flex items-center hover:bg-blue-50 transition-colors">
          <div className="p-3 rounded-full bg-green-100 mr-4">
            <Calculator className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h3 className="font-medium">Kalkulator Bundling</h3>
            <p className="text-sm text-gray-500">Hitung harga bundling produk</p>
          </div>
        </Link>
      </div>
    </div>
  );
}
