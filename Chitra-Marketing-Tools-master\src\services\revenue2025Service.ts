import axios from 'axios';
import <PERSON> from 'papapar<PERSON>';

// URL for the dataset
const REVENUE_DATA_URL = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vSrcOlsVNOmwifGY-dHqFbQHrkUuYMKGSE9yi9PrgwcIVa723xyjJ8vcP4cQhVNvGT_sVpzppeUkZFO/pub?gid=0&single=true&output=csv';

// Add CORS proxy if needed
const useCorsProxy = true;
const corsProxy = 'https://corsproxy.io/?';

// Get the actual URL with CORS proxy if needed
const getRevenueDataUrl = () => useCorsProxy ? `${corsProxy}${encodeURIComponent(REVENUE_DATA_URL)}` : REVENUE_DATA_URL;

// Interface for Revenue data
export interface RevenueItem {
  id: string;
  customerName: string;
  salesman: string;
  materialDescription: string;
  matGrpDesc: string;
  matGrp1Desc: string;
  matGrp3Desc: string;
  matGrp5Desc: string;
  revenueInDocCurr: number;
  revenueInLocCurr: number;
  billingDate: string;
  [key: string]: any;
}

// Interface for Revenue Summary
export interface RevenueSummary {
  totalRevenue: number;
  totalCustomers: number;
  totalSalesmen: number;
  topCustomers: {
    customer: string;
    revenue: number;
  }[];
  topSalesmen: {
    salesman: string;
    revenue: number;
  }[];
  materialGroupDistribution: {
    matGrpDesc: string;
    count: number;
    revenue: number;
  }[];
  monthlyRevenue: {
    month: string;
    revenue: number;
  }[];
}

/**
 * Fetches and processes revenue data
 */
export const fetchRevenueData = async (): Promise<RevenueItem[]> => {
  try {
    // Try to use local fallback data first for development
    const useLocalData = false; // Set to false to use API instead of local data

    if (useLocalData) {
      console.log('Using local revenue data');
      return Promise.resolve(FALLBACK_REVENUE_DATA);
    }

    const url = getRevenueDataUrl();
    console.log('Fetching revenue data from:', url);
    const response = await axios.get(url, {
      headers: {
        'Accept': 'text/csv; charset=utf-8',
      }
    });

    // Log the first part of the response to debug
    console.log('Revenue data response preview:', response.data.substring(0, 200) + '...');

    return new Promise((resolve, reject) => {
      Papa.parse(response.data, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          if (results.data && Array.isArray(results.data)) {
            console.log('Revenue raw data sample:', results.data.slice(0, 2));
            console.log('Revenue columns:', results.meta.fields);

            // Transform data to match our interface while preserving all original columns
            const revenueData = results.data.map((item: any, index: number) => {
              // Create a base object with the ID
              const baseItem: RevenueItem = {
                id: `rev-${index + 1}`,
                customerName: '',
                salesman: '',
                materialDescription: '',
                matGrpDesc: '',
                matGrp1Desc: '',
                matGrp3Desc: '',
                matGrp5Desc: '',
                revenueInDocCurr: 0,
                revenueInLocCurr: 0,
                billingDate: ''
              };

              // Add all original columns from the CSV
              Object.keys(item).forEach(key => {
                // Convert numeric values to numbers
                const value = item[key];
                if (!isNaN(value) && value !== '' &&
                   (key.includes('Revenue') || key.includes('revenue') || key.includes('Amount'))) {
                  baseItem[key] = parseFloat(value);
                } else {
                  baseItem[key] = value;
                }
              });

              // Map the specific fields we need for our interface
              baseItem.customerName = item['Customer Name'] || '';
              baseItem.salesman = item['Salesman'] || '';
              baseItem.materialDescription = item['Material Description'] || '';
              baseItem.matGrpDesc = item['Mat Grp Desc.'] || '';
              baseItem.matGrp1Desc = item['Mat Grp1 Desc.'] || '';
              baseItem.matGrp3Desc = item['Mat Grp3 Desc.'] || '';
              baseItem.matGrp5Desc = item['Mat Grp5 Desc.'] || '';

              // Parse revenue values
              const docRevenue = item['Revenue in Doc Curr.'];
              baseItem.revenueInDocCurr = docRevenue ? parseFloat(docRevenue.replace(/,/g, '')) : 0;

              const locRevenue = item['Revenue in Loc Curr.'];
              baseItem.revenueInLocCurr = locRevenue ? parseFloat(locRevenue.replace(/,/g, '')) : 0;

              // Format billing date
              baseItem.billingDate = item['Billing Date'] || '';

              return baseItem;
            });

            console.log(`Successfully parsed ${revenueData.length} revenue records`);
            if (revenueData.length > 0) {
              console.log('Sample transformed revenue data:', revenueData[0]);
            }

            resolve(revenueData);
          } else {
            console.error('Invalid revenue data structure:', results);
            reject(new Error('Failed to parse revenue CSV data'));
          }
        },
        error: (error) => {
          console.error('Error parsing revenue CSV:', error);
          reject(error);
        }
      });
    });
  } catch (error) {
    console.error('Error fetching revenue data:', error);
    console.log('Falling back to local revenue data');
    return FALLBACK_REVENUE_DATA;
  }
};



/**
 * Generate summary statistics from revenue data
 */
export const generateRevenueSummary = (data: RevenueItem[]): RevenueSummary => {
  // Calculate total revenue (using document currency)
  const totalRevenue = data.reduce((sum, item) => sum + (item.revenueInDocCurr || 0), 0);

  // Count unique customers
  const uniqueCustomers = new Set(data.map(item => item.customerName).filter(Boolean));

  // Count unique salesmen
  const uniqueSalesmen = new Set(data.map(item => item.salesman).filter(Boolean));

  // Get top customers by revenue
  const customerRevenue: { [key: string]: number } = {};
  data.forEach(item => {
    if (item.customerName) {
      if (!customerRevenue[item.customerName]) {
        customerRevenue[item.customerName] = 0;
      }
      customerRevenue[item.customerName] += (item.revenueInDocCurr || 0);
    }
  });

  const topCustomers = Object.entries(customerRevenue)
    .map(([customer, revenue]) => ({ customer, revenue }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  // Get top salesmen by revenue
  const salesmanRevenue: { [key: string]: number } = {};
  data.forEach(item => {
    if (item.salesman) {
      if (!salesmanRevenue[item.salesman]) {
        salesmanRevenue[item.salesman] = 0;
      }
      salesmanRevenue[item.salesman] += (item.revenueInDocCurr || 0);
    }
  });

  const topSalesmen = Object.entries(salesmanRevenue)
    .map(([salesman, revenue]) => ({ salesman, revenue }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  // Get material group distribution
  const matGrpMap: { [key: string]: { count: number, revenue: number } } = {};
  data.forEach(item => {
    if (item.matGrpDesc) {
      if (!matGrpMap[item.matGrpDesc]) {
        matGrpMap[item.matGrpDesc] = { count: 0, revenue: 0 };
      }
      matGrpMap[item.matGrpDesc].count += 1;
      matGrpMap[item.matGrpDesc].revenue += (item.revenueInDocCurr || 0);
    }
  });

  const materialGroupDistribution = Object.entries(matGrpMap)
    .map(([matGrpDesc, { count, revenue }]) => ({ matGrpDesc, count, revenue }))
    .sort((a, b) => b.revenue - a.revenue);

  // Get monthly revenue
  const monthlyRevenueMap: { [key: string]: number } = {};
  data.forEach(item => {
    if (item.billingDate) {
      // Extract month from billing date (assuming format YYYY-MM-DD)
      let month = 'Unknown';
      try {
        const date = new Date(item.billingDate);
        month = date.toLocaleString('default', { month: 'long', year: 'numeric' });
      } catch (e) {
        // If date parsing fails, use the raw value
        month = item.billingDate.substring(0, 7); // Get YYYY-MM part
      }

      if (!monthlyRevenueMap[month]) {
        monthlyRevenueMap[month] = 0;
      }
      monthlyRevenueMap[month] += (item.revenueInDocCurr || 0);
    }
  });

  const monthlyRevenue = Object.entries(monthlyRevenueMap)
    .map(([month, revenue]) => ({ month, revenue }))
    .sort((a, b) => {
      // Try to sort by date if possible
      const aDate = new Date(a.month);
      const bDate = new Date(b.month);
      if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
        return aDate.getTime() - bDate.getTime();
      }
      return a.month.localeCompare(b.month);
    });

  return {
    totalRevenue,
    totalCustomers: uniqueCustomers.size,
    totalSalesmen: uniqueSalesmen.size,
    topCustomers,
    topSalesmen,
    materialGroupDistribution,
    monthlyRevenue
  };
};

// Fallback data in case the API fails
export const FALLBACK_REVENUE_DATA: RevenueItem[] = [
  {
    id: 'rev-1',
    customerName: 'PT. SAPTAINDRA SEJATI',
    salesman: 'TOMMY INDRA ALDINY RAMBE',
    materialDescription: 'RADIAL 27.00 X 49',
    matGrpDesc: 'EARTHMOVER TIRES R49',
    matGrp1Desc: 'CP REPAIR/RETREAD',
    matGrp3Desc: 'CP BIAS TUBE TYPE',
    matGrp5Desc: 'CP EARTHMOVER',
    revenueInDocCurr: 8000000,
    revenueInLocCurr: 494.04,
    billingDate: '2025-01-07'
  },
  {
    id: 'rev-2',
    customerName: 'PT.MANDALA KARYA PRIMA',
    salesman: 'YEAN ALAN FABIAN ANTONIO M',
    materialDescription: '27.00 R 49 XD GRIP B E4T TL **',
    matGrpDesc: 'EARTHMOVER TIRES R49',
    matGrp1Desc: 'CP TIRE',
    matGrp3Desc: 'CP RADIAL TUBELESS',
    matGrp5Desc: 'CP EARTHMOVER',
    revenueInDocCurr: 209700000,
    revenueInLocCurr: 12950.04,
    billingDate: '2025-01-07'
  },
  {
    id: 'rev-3',
    customerName: 'PT. SAPTAINDRA SEJATI',
    salesman: 'YEAN ALAN FABIAN ANTONIO M',
    materialDescription: '37.00 R 57 XDR3 B E4R TL**',
    matGrpDesc: 'EARTHMOVER TIRES R57',
    matGrp1Desc: 'CP TIRE',
    matGrp3Desc: 'CP RADIAL TUBELESS',
    matGrp5Desc: 'CP EARTHMOVER',
    revenueInDocCurr: 3807072060,
    revenueInLocCurr: 235106.04,
    billingDate: '2025-01-07'
  }
];
