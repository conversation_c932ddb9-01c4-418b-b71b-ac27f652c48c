import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import { BundleItem, Product } from '../types';

// Add autotable to jsPDF
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

// Define the calculation result interface
interface CalculationResult {
  bundlingPrice: number;
  suggestedQty: number;
  marginPercentage: number;
  mainProductCost: number;
  secondaryProductPrice: number;
  totalCost: number;
  availableBudget: number;
  totalCostWithSuggestedQty: number;
  actualMarginAmount: number;
  minimumMainProductQty: number;
  optimalSellPrice?: number;
  optimalMargin?: number;
  profitMargin?: number;
  pricePerPiece?: number;
  totalProfit?: number;
  mainProductPrice?: number;
}

/**
 * Generate a PDF for bundling calculations
 */
export function generateBundlingPDF(
  mainProduct: BundleItem,
  secondaryProducts: BundleItem[],
  calculationResult: CalculationResult,
  targetMargin: number,
  title: string = 'Bundling Calculation'
): void {
  try {
    console.log('Generating bundling PDF...');

    // Create a new PDF document
    const doc = new jsPDF();

    // Add header with company info and title
    addHeader(doc, title);

    // Add calculation summary
    addCalculationSummary(doc, mainProduct, secondaryProducts, calculationResult, targetMargin);

    // Add product details
    addProductDetails(doc, mainProduct, secondaryProducts, calculationResult);

    // Add business interpretation
    addBusinessInterpretation(doc, mainProduct, secondaryProducts, calculationResult, targetMargin);

    // Add footer
    addFooter(doc);

    // Save the PDF
    const filename = `Chitra-${title.replace(/\s+/g, '-')}.pdf`;
    doc.save(filename);
    console.log(`Bundling PDF saved successfully as ${filename}`);

    // Show success message
    alert('PDF berhasil diekspor!');
  } catch (error) {
    console.error('Error generating bundling PDF:', error);
    alert('Gagal membuat PDF. Silakan periksa konsol untuk detail.');
  }
}

function addHeader(doc: jsPDF, title: string): void {
  try {
    // Add company logo from file
    const logoPath = './assets/cp_logo.png';
    doc.addImage(logoPath, 'PNG', 20, 15, 45, 15);
  } catch (error) {
    console.error('Error adding logo:', error);

    // Fallback if logo loading fails - just show a colored rectangle
    doc.setDrawColor(0, 120, 200);
    doc.setFillColor(0, 120, 200);
    doc.rect(20, 15, 30, 15, 'F');
  }

  // Add title on the right
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(80, 80, 200); // Purple-blue color
  doc.setFontSize(18);
  doc.text(title.toUpperCase(), 200, 22, { align: 'right' });

  // Add date
  const today = new Date();
  const formattedDate = today.toLocaleDateString('id-ID', {
    day: '2-digit',
    month: 'long',
    year: 'numeric'
  });
  doc.setFontSize(10);
  doc.text(`Tanggal: ${formattedDate}`, 200, 28, { align: 'right' });

  // Add horizontal line
  doc.setDrawColor(220, 220, 220);
  doc.setLineWidth(0.5);
  doc.line(20, 35, 190, 35);
}

function addCalculationSummary(
  doc: jsPDF,
  mainProduct: BundleItem,
  secondaryProducts: BundleItem[],
  calculationResult: CalculationResult,
  targetMargin: number
): void {
  // Add section title
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(14);
  doc.text('Ringkasan Kalkulasi', 20, 45);

  // Add summary table
  const tableData = [
    ['Harga Bundling', formatCurrency(calculationResult.bundlingPrice)],
    ['Margin Target', `${targetMargin}%`],
    ['Margin Aktual', `${calculationResult.marginPercentage.toFixed(2)}%`],
    ['Jumlah Produk Sekunder', `${calculationResult.suggestedQty} unit`],
    ['Min. Jumlah Produk Utama', `${calculationResult.minimumMainProductQty} unit`]
  ];

  // Add the table
  (doc as any).autoTable({
    startY: 50,
    head: [['Parameter', 'Nilai']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: [65, 105, 225],
      textColor: [255, 255, 255],
      fontStyle: 'bold'
    },
    styles: {
      fontSize: 10
    },
    columnStyles: {
      0: { cellWidth: 80 },
      1: { cellWidth: 80 }
    }
  });
}

function addProductDetails(
  doc: jsPDF,
  mainProduct: BundleItem,
  secondaryProducts: BundleItem[],
  calculationResult: CalculationResult
): void {
  // Get the Y position after the previous table
  const startY = (doc as any).lastAutoTable.finalY + 15;

  // Add section title
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(14);
  doc.text('Detail Produk', 20, startY);

  // Prepare product data
  const productData = [
    [
      'Produk Utama',
      mainProduct.product.materialDescription,
      mainProduct.quantity.toString(),
      formatCurrency(mainProduct.product.price)
    ]
  ];

  // Add secondary products
  secondaryProducts.forEach((item, index) => {
    productData.push([
      `Produk Sekunder ${index + 1}`,
      item.product.materialDescription,
      calculationResult.suggestedQty.toString(),
      formatCurrency(item.product.price)
    ]);
  });

  // Add the table
  (doc as any).autoTable({
    startY: startY + 5,
    head: [['Tipe', 'Deskripsi', 'Jumlah', 'Harga Satuan']],
    body: productData,
    theme: 'grid',
    headStyles: {
      fillColor: [65, 105, 225],
      textColor: [255, 255, 255],
      fontStyle: 'bold'
    },
    styles: {
      fontSize: 10
    }
  });
}

function addBusinessInterpretation(
  doc: jsPDF,
  mainProduct: BundleItem,
  secondaryProducts: BundleItem[],
  calculationResult: CalculationResult,
  targetMargin: number
): void {
  // Get the Y position after the previous table
  const startY = (doc as any).lastAutoTable.finalY + 15;

  // Add section title
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(14);
  doc.text('Interpretasi Bisnis', 20, startY);

  // Add business interpretation text
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);

  const secondaryProduct = secondaryProducts[0];
  const interpretationText = [
    `Dengan menjual ${calculationResult.minimumMainProductQty} unit ${mainProduct.product.materialDescription}, `,
    `Anda bisa menutupi biaya ${secondaryProduct.quantity} unit ${secondaryProduct.product.materialDescription} `,
    `sambil tetap mempertahankan margin ${targetMargin}%.`,
    '',
    'Ini memungkinkan Anda membuat promosi bundling yang menarik seperti:',
  ].join('');

  doc.text(interpretationText, 20, startY + 10, { maxWidth: 170 });

  // Add promotion example in a box
  doc.setFillColor(240, 250, 240);
  doc.setDrawColor(0, 150, 0);
  doc.roundedRect(40, startY + 25, 130, 20, 3, 3, 'FD');

  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 100, 0);
  doc.setFontSize(12);

  const promoText = `"Beli ${calculationResult.minimumMainProductQty} ${mainProduct.product.materialDescription}, GRATIS ${secondaryProduct.quantity} ${secondaryProduct.product.materialDescription}!"`;

  doc.text(promoText, 105, startY + 37, { align: 'center', maxWidth: 120 });

  // Add additional business tips
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(10);

  const tipsY = startY + 55;
  doc.text('Tips Bisnis:', 20, tipsY);

  const tips = [
    '• Strategi ini efektif untuk meningkatkan volume penjualan produk utama dan menghabiskan stok produk sekunder.',
    '• Pelanggan merasa mendapatkan nilai lebih dengan "produk gratis", sementara Anda tetap mencapai target margin.',
    '• Untuk meningkatkan margin, pertimbangkan untuk menggunakan produk sekunder dengan harga modal yang lebih rendah.',
    '• Alternatif lain adalah meningkatkan jumlah minimum produk utama yang dijual dalam bundling.'
  ];

  tips.forEach((tip, index) => {
    doc.text(tip, 20, tipsY + 7 + (index * 7), { maxWidth: 170 });
  });
}

function addFooter(doc: jsPDF): void {
  const pageHeight = doc.internal.pageSize.height;

  // Add horizontal line
  doc.setDrawColor(220, 220, 220);
  doc.setLineWidth(0.5);
  doc.line(20, pageHeight - 25, 190, pageHeight - 25);

  // Add company info
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.text('PT. CHITRA PARATAMA', 20, pageHeight - 18);
  doc.text('Dokumen ini dibuat oleh Chitra Marketing Tools', 20, pageHeight - 13);

  // Add page number
  doc.text(`Halaman ${doc.getCurrentPageInfo().pageNumber}/${doc.getNumberOfPages()}`, 190, pageHeight - 13, { align: 'right' });
}

// Format currency for display
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}
