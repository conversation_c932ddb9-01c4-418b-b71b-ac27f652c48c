import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { initializeDataHub, DataChangeEvent, subscribeToDataChanges } from '../services/dataHubService';
import { Product } from '../types';
import { Customer } from '../types/customer';
import { FleetData } from '../services/fleetDataService';
import { CoalPrice, CoalPriceTrend } from '../services/coalPriceService';
import { Holiday } from '../services/holidayService';

// Define the context type
interface DataHubContextType {
  isInitialized: boolean;
  isLoading: {
    products: boolean;
    customers: boolean;
    fleetData: boolean;
    coalPrices: boolean;
    holidays: boolean;
  };
  lastUpdated: {
    products: Date | null;
    customers: Date | null;
    fleetData: Date | null;
    coalPrices: Date | null;
    holidays: Date | null;
  };
  refreshData: (dataType?: string) => Promise<void>;
}

// Create the context with default values
const DataHubContext = createContext<DataHubContextType>({
  isInitialized: false,
  isLoading: {
    products: false,
    customers: false,
    fleetData: false,
    coalPrices: false,
    holidays: false,
  },
  lastUpdated: {
    products: null,
    customers: null,
    fleetData: null,
    coalPrices: null,
    holidays: null,
  },
  refreshData: async () => {},
});

// Provider props
interface DataHubProviderProps {
  children: ReactNode;
}

// DataHubProvider component
export function DataHubProvider({ children }: DataHubProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState({
    products: false,
    customers: false,
    fleetData: false,
    coalPrices: false,
    holidays: false,
  });
  const [lastUpdated, setLastUpdated] = useState({
    products: null as Date | null,
    customers: null as Date | null,
    fleetData: null as Date | null,
    coalPrices: null as Date | null,
    holidays: null as Date | null,
  });

  // Initialize the Data Hub
  useEffect(() => {
    console.log('DataHubProvider: Initializing Data Hub');
    initializeDataHub();
    setIsInitialized(true);

    // Set up event listeners for data changes
    const unsubscribeProducts = subscribeToDataChanges(
      DataChangeEvent.PRODUCTS,
      () => setLastUpdated(prev => ({ ...prev, products: new Date() }))
    );

    const unsubscribeCustomers = subscribeToDataChanges(
      DataChangeEvent.CUSTOMERS,
      () => setLastUpdated(prev => ({ ...prev, customers: new Date() }))
    );

    const unsubscribeFleetData = subscribeToDataChanges(
      DataChangeEvent.FLEET_DATA,
      () => setLastUpdated(prev => ({ ...prev, fleetData: new Date() }))
    );

    const unsubscribeCoalPrices = subscribeToDataChanges(
      DataChangeEvent.COAL_PRICES,
      () => setLastUpdated(prev => ({ ...prev, coalPrices: new Date() }))
    );

    const unsubscribeHolidays = subscribeToDataChanges(
      DataChangeEvent.HOLIDAYS,
      () => setLastUpdated(prev => ({ ...prev, holidays: new Date() }))
    );

    // Clean up event listeners
    return () => {
      unsubscribeProducts();
      unsubscribeCustomers();
      unsubscribeFleetData();
      unsubscribeCoalPrices();
      unsubscribeHolidays();
    };
  }, []);

  // Function to refresh data
  const refreshData = async (dataType?: string) => {
    try {
      if (!dataType || dataType === 'products') {
        setIsLoading(prev => ({ ...prev, products: true }));
        const dataHub = await import('../services/dataHubService');
        await dataHub.getProducts(true);
        setIsLoading(prev => ({ ...prev, products: false }));
        setLastUpdated(prev => ({ ...prev, products: new Date() }));
      }

      if (!dataType || dataType === 'customers') {
        setIsLoading(prev => ({ ...prev, customers: true }));
        const dataHub = await import('../services/dataHubService');
        dataHub.getCustomers(true);
        setIsLoading(prev => ({ ...prev, customers: false }));
        setLastUpdated(prev => ({ ...prev, customers: new Date() }));
      }

      if (!dataType || dataType === 'fleetData') {
        setIsLoading(prev => ({ ...prev, fleetData: true }));
        const dataHub = await import('../services/dataHubService');
        await dataHub.getFleetData(true);
        setIsLoading(prev => ({ ...prev, fleetData: false }));
        setLastUpdated(prev => ({ ...prev, fleetData: new Date() }));
      }

      if (!dataType || dataType === 'coalPrices') {
        setIsLoading(prev => ({ ...prev, coalPrices: true }));
        const dataHub = await import('../services/dataHubService');
        await dataHub.getCoalPrices(true);
        setIsLoading(prev => ({ ...prev, coalPrices: false }));
        setLastUpdated(prev => ({ ...prev, coalPrices: new Date() }));
      }

      if (!dataType || dataType === 'holidays') {
        setIsLoading(prev => ({ ...prev, holidays: true }));
        const dataHub = await import('../services/dataHubService');
        await dataHub.getHolidays(true);
        setIsLoading(prev => ({ ...prev, holidays: false }));
        setLastUpdated(prev => ({ ...prev, holidays: new Date() }));
      }
    } catch (error) {
      console.error('DataHubProvider: Error refreshing data', error);
      // Reset loading state in case of error
      setIsLoading({
        products: false,
        customers: false,
        fleetData: false,
        coalPrices: false,
        holidays: false,
      });
    }
  };

  // Context value
  const contextValue: DataHubContextType = {
    isInitialized,
    isLoading,
    lastUpdated,
    refreshData,
  };

  return (
    <DataHubContext.Provider value={contextValue}>
      {children}
    </DataHubContext.Provider>
  );
}

// Custom hook to use the Data Hub context
export function useDataHub() {
  const context = useContext(DataHubContext);
  if (context === undefined) {
    throw new Error('useDataHub must be used within a DataHubProvider');
  }
  return context;
}

// Hook for accessing products data
export function useProducts(autoFetch = true) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<Error | null>(null);
  const { lastUpdated } = useDataHub();

  useEffect(() => {
    if (autoFetch) {
      fetchProducts();
    }
  }, [autoFetch, lastUpdated.products]);

  const fetchProducts = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const data = await dataHub.getProducts(forceRefresh);
      setProducts(data);
    } catch (err) {
      console.error('Error fetching products:', err);
      setError(err instanceof Error ? err : new Error('Unknown error fetching products'));
    } finally {
      setLoading(false);
    }
  };

  return { products, loading, error, fetchProducts };
}

// Hook for accessing customers data
export function useCustomers(autoFetch = true) {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<Error | null>(null);
  const { lastUpdated } = useDataHub();

  useEffect(() => {
    if (autoFetch) {
      fetchCustomers();
    }
  }, [autoFetch, lastUpdated.customers]);

  const fetchCustomers = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const data = dataHub.getCustomers(forceRefresh);
      setCustomers(data);
    } catch (err) {
      console.error('Error fetching customers:', err);
      setError(err instanceof Error ? err : new Error('Unknown error fetching customers'));
    } finally {
      setLoading(false);
    }
  };

  return { customers, loading, error, fetchCustomers };
}

// Hook for accessing fleet data
export function useFleetData(autoFetch = true) {
  const [fleetData, setFleetData] = useState<FleetData[]>([]);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<Error | null>(null);
  const { lastUpdated } = useDataHub();

  useEffect(() => {
    if (autoFetch) {
      fetchFleetData();
    }
  }, [autoFetch, lastUpdated.fleetData]);

  const fetchFleetData = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const data = await dataHub.getFleetData(forceRefresh);
      setFleetData(data);
    } catch (err) {
      console.error('Error fetching fleet data:', err);
      setError(err instanceof Error ? err : new Error('Unknown error fetching fleet data'));
    } finally {
      setLoading(false);
    }
  };

  return { fleetData, loading, error, fetchFleetData };
}

export default DataHubProvider;
