import React, { useState, useEffect } from 'react';
// Import react-helmet conditionally to handle cases where it might not be available
let Helmet: any;
try {
  Helmet = require('react-helmet').Helmet;
} catch (error) {
  // Create a fallback component if react-helmet is not available
  Helmet = ({ children }: { children: React.ReactNode }) => null;
}

import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Calendar, PlusCircle } from 'lucide-react';
import MonthlyContentPlanGenerator from '../components/MonthlyContentPlanGenerator';
import { ContentType, MonthlyContentPlanPost, PostStatus, SocialMediaPlatform, SocialMediaPost } from '../types/socialMedia';
import { createSocialMediaPost } from '../services/socialMediaService';
import { useToast } from '../components/ui/use-toast';
import InstagramContentList from '../components/InstagramContentList';

export default function MonthlyContentPlanPage() {
  const [activeTab, setActiveTab] = useState('generator');
  const [refreshInstagramList, setRefreshInstagramList] = useState(0);
  const { toast } = useToast();

  const handleSavePost = async (post: MonthlyContentPlanPost) => {
    try {
      // Convert MonthlyContentPlanPost to SocialMediaPost
      const scheduledDate = new Date(post.date);

      const newPost: Omit<SocialMediaPost, 'id' | 'createdAt' | 'updatedAt'> = {
        platform: SocialMediaPlatform.INSTAGRAM,
        contentType: post.contentType,
        title: post.title,
        caption: post.description,
        hashtags: post.recommendedHashtags || [],
        imageDescription: post.suggestedImageDescription,
        scheduledDate,
        status: PostStatus.SCHEDULED,
        targetAudience: 'Instagram Followers',
        content: post.description,
        mediaUrls: [],
        metadata: {
          originalCategory: post.contentCategory,
          knowledgeBaseReference: post.knowledgeBaseReference
        }
      };

      // Validasi field wajib
      if (!newPost.platform || !newPost.contentType || !newPost.caption) {
        throw new Error('Field wajib tidak boleh kosong!');
      }

      // Ambil value style preset dari post
      const stylePreset = post.stylePreset;

      // Bangun prompt utama
      let prompt = "Create a humorous poster featuring a person ...";
      if (stylePreset && stylePreset !== 'None') {
        prompt += ` in ${stylePreset.toLowerCase()} style`;
      }

      // Simpan post ke localStorage
      await createSocialMediaPost(newPost);

      // Tampilkan notifikasi sukses
      toast({
        title: 'Post berhasil disimpan',
        description: `Post "${post.title}" telah dijadwalkan untuk ${scheduledDate.toLocaleDateString('id-ID')}`,
        variant: 'default',
      });
      setRefreshInstagramList((v) => v + 1);
    } catch (error) {
      console.error('Error saving post:', error);
      toast({
        title: 'Gagal menyimpan post',
        description: 'Terjadi kesalahan saat menyimpan post. Silakan coba lagi.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="container mx-auto py-6">
      <Helmet>
        <title>Rencana Konten Bulanan | Chitra Marketing Tools</title>
      </Helmet>

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Rencana Konten Bulanan</h1>
          <p className="text-gray-500">
            Buat dan kelola rencana konten Instagram yang bervariasi untuk meningkatkan engagement
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="generator" className="flex items-center gap-1">
            <PlusCircle className="h-4 w-4" />
            Generator Konten
          </TabsTrigger>
          <TabsTrigger value="about" className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            Tentang Fitur
          </TabsTrigger>
          <TabsTrigger value="instagram-list" className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            Daftar Konten Instagram
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generator">
          <MonthlyContentPlanGenerator onSavePost={handleSavePost} />
        </TabsContent>

        <TabsContent value="about">
          <Card>
            <CardHeader>
              <CardTitle>Tentang Generator Rencana Konten Bulanan</CardTitle>
              <CardDescription>
                Fitur ini membantu Anda membuat rencana konten Instagram yang bervariasi dan tidak monoton
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Apa itu Generator Rencana Konten Bulanan?</h3>
                <p>
                  Generator Rencana Konten Bulanan adalah fitur yang membantu Anda membuat rencana konten Instagram yang bervariasi untuk satu bulan penuh.
                  Fitur ini menggunakan AI untuk menghasilkan ide konten yang beragam berdasarkan berbagai kategori seperti produk, keselamatan, edukasi, kuis,
                  engagement, layanan, testimoni, behind the scenes, berita industri, konten musiman, dan promosi.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Manfaat Menggunakan Fitur Ini</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Menghasilkan ide konten yang bervariasi dan tidak monoton</li>
                  <li>Meningkatkan engagement dengan konten yang beragam</li>
                  <li>Menghemat waktu dalam perencanaan konten</li>
                  <li>Memastikan konten terdistribusi secara merata sepanjang bulan</li>
                  <li>Memanfaatkan momen hari libur dan hari penting untuk konten yang relevan</li>
                  <li>Menyeimbangkan berbagai kategori konten untuk menjaga ketertarikan audiens</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Cara Menggunakan</h3>
                <ol className="list-decimal pl-5 space-y-1">
                  <li>Pilih tahun dan bulan untuk rencana konten</li>
                  <li>Tentukan jumlah post per minggu yang diinginkan</li>
                  <li>Pilih kategori konten yang ingin diutamakan (opsional)</li>
                  <li>Pilih kategori konten yang ingin dikecualikan (opsional)</li>
                  <li>Tentukan apakah ingin menyertakan konten untuk hari libur dan hari penting</li>
                  <li>Pilih bahasa untuk konten (Indonesia atau Inggris)</li>
                  <li>Klik "Buat Rencana Konten" untuk menghasilkan rencana</li>
                  <li>Lihat hasil rencana konten dan distribusi kategori</li>
                  <li>Simpan post yang diinginkan ke dalam jadwal konten Instagram</li>
                </ol>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Kategori Konten</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li><strong>Produk</strong>: Konten tentang fitur produk, manfaat, dan spesifikasi ban</li>
                  <li><strong>Keselamatan</strong>: Tips keselamatan dan praktik terbaik terkait penggunaan ban</li>
                  <li><strong>Edukasi</strong>: Konten edukatif tentang ban, industri, dan teknologi</li>
                  <li><strong>Kuis</strong>: Kuis interaktif dan pertanyaan untuk melibatkan audiens</li>
                  <li><strong>Engagement</strong>: Konten yang dirancang untuk meningkatkan engagement (pertanyaan, polling)</li>
                  <li><strong>Layanan</strong>: Layanan yang ditawarkan, tips perawatan ban</li>
                  <li><strong>Testimoni</strong>: Testimoni pelanggan, kisah sukses</li>
                  <li><strong>Behind the Scenes</strong>: Konten di balik layar, proses produksi, tim</li>
                  <li><strong>Berita Industri</strong>: Berita industri, pembaruan, tren</li>
                  <li><strong>Musiman</strong>: Konten musiman, terkait hari libur</li>
                  <li><strong>Promosi</strong>: Penawaran khusus, promosi</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="instagram-list">
          <InstagramContentList refreshTrigger={refreshInstagramList} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
