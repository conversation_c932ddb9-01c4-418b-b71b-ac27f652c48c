import axios from 'axios';

// Interface untuk data hari libur
export interface Holiday {
  id: string;
  name: string;
  description: string;
  date: string; // ISO date string
  country: string;
  type: HolidayType;
  isGlobal: boolean;
  tags: string[];
  color?: string;
}

// Tipe hari libur
export enum HolidayType {
  NATIONAL = 'Nasional',
  RELIGIOUS = 'Keagamaan',
  CULTURAL = 'Budaya',
  REGIONAL = 'Daerah'
}

// Local storage key
const HOLIDAYS_STORAGE_KEY = 'holidays';
const CUSTOM_HOLIDAYS_STORAGE_KEY = 'customHolidays';

// URL untuk API hari libur (menggunakan API eksternal yang valid)
const API_URL = 'https://dayoffapi.vercel.app/api';

/**
 * Data hari libur dan hari penting di Indonesia
 */
const INDONESIA_HOLIDAYS: Holiday[] = [
  // Hari Libur Nasional 2025
  {
    id: 'id-new-year-2025',
    name: '<PERSON><PERSON> Bar<PERSON>',
    description: 'Tahun Baru 2025',
    date: '2025-01-01',
    country: 'Indonesia',
    type: HolidayType.NATIONAL,
    isGlobal: true,
    tags: ['tahun baru', 'libur nasional'],
    color: 'blue'
  },
  {
    id: 'id-isra-miraj-2025',
    name: 'Isra Miraj',
    description: 'Peringatan Isra Miraj Nabi Muhammad SAW',
    date: '2025-01-27',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['islam', 'libur nasional'],
    color: 'green'
  },
  {
    id: 'id-chinese-new-year-2025',
    name: 'Tahun Baru Imlek',
    description: 'Tahun Baru Imlek 2576 Kongzili',
    date: '2025-01-29',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['imlek', 'china', 'libur nasional'],
    color: 'red'
  },
  {
    id: 'id-nyepi-2025',
    name: 'Hari Raya Nyepi',
    description: 'Tahun Baru Saka 1947',
    date: '2025-03-29',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['hindu', 'bali', 'libur nasional'],
    color: 'purple'
  },
  {
    id: 'id-eid-al-fitr-2025-1',
    name: 'Hari Raya Idul Fitri',
    description: 'Hari Raya Idul Fitri 1446 Hijriah (Hari Pertama)',
    date: '2025-03-31',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['islam', 'lebaran', 'libur nasional'],
    color: 'green'
  },
  {
    id: 'id-eid-al-fitr-2025-2',
    name: 'Hari Raya Idul Fitri',
    description: 'Hari Raya Idul Fitri 1446 Hijriah (Hari Kedua)',
    date: '2025-04-01',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['islam', 'lebaran', 'libur nasional'],
    color: 'green'
  },
  {
    id: 'id-good-friday-2025',
    name: 'Wafat Isa Almasih',
    description: 'Jumat Agung',
    date: '2025-04-18',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['kristen', 'paskah', 'libur nasional'],
    color: 'blue'
  },
  {
    id: 'id-labor-day-2025',
    name: 'Hari Buruh Internasional',
    description: 'Hari Buruh Internasional',
    date: '2025-05-01',
    country: 'Indonesia',
    type: HolidayType.NATIONAL,
    isGlobal: true,
    tags: ['buruh', 'pekerja', 'libur nasional'],
    color: 'red'
  },
  {
    id: 'id-vesak-2025',
    name: 'Hari Raya Waisak 2569 BE',
    description: 'Hari Raya Waisak 2569 BE',
    date: '2025-05-12',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['buddha', 'libur nasional'],
    color: 'orange'
  },

  {
    id: 'id-ascension-day-2025',
    name: 'Kenaikan Yesus Kristus',
    description: 'Kenaikan Yesus Kristus',
    date: '2025-05-29',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['kristen', 'libur nasional'],
    color: 'blue'
  },

  {
    id: 'id-pancasila-day-2025',
    name: 'Hari Lahir Pancasila',
    description: 'Hari Lahir Pancasila',
    date: '2025-06-01',
    country: 'Indonesia',
    type: HolidayType.NATIONAL,
    isGlobal: false,
    tags: ['pancasila', 'nasional', 'libur nasional'],
    color: 'red'
  },
  {
    id: 'id-eid-al-adha-2025',
    name: 'Hari Raya Idul Adha 1446H',
    description: 'Hari Raya Idul Adha 1446H',
    date: '2025-06-06',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['islam', 'kurban', 'libur nasional'],
    color: 'green'
  },

  {
    id: 'id-islamic-new-year-2025',
    name: 'Tahun Baru Islam 1 Muharram 1447H',
    description: 'Tahun Baru Islam 1 Muharram 1447H',
    date: '2025-06-27',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['islam', 'muharram', 'libur nasional'],
    color: 'green'
  },
  {
    id: 'id-independence-day-2025',
    name: 'Hari Kemerdekaan Republik Indonesia ke 80',
    description: 'Hari Kemerdekaan Republik Indonesia ke 80',
    date: '2025-08-17',
    country: 'Indonesia',
    type: HolidayType.NATIONAL,
    isGlobal: false,
    tags: ['kemerdekaan', 'nasional', 'libur nasional'],
    color: 'red'
  },
  {
    id: 'id-mawlid-2025',
    name: 'Maulid Nabi Muhammad SAW',
    description: 'Maulid Nabi Muhammad SAW',
    date: '2025-09-05',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['islam', 'maulid', 'libur nasional'],
    color: 'green'
  },
  {
    id: 'id-christmas-2025',
    name: 'Hari Raya Natal',
    description: 'Hari Raya Natal',
    date: '2025-12-25',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: true,
    tags: ['kristen', 'natal', 'libur nasional'],
    color: 'red'
  }

];

/**
 * Data hari penting internasional dan komersial
 */
const INTERNATIONAL_HOLIDAYS: Holiday[] = [
  {
    id: 'int-valentines-day-2025',
    name: 'Hari Valentine',
    description: 'Hari kasih sayang',
    date: '2025-02-14',
    country: 'Global',
    type: HolidayType.RELIGIOUS,
    isGlobal: true,
    tags: ['valentine', 'kasih sayang', 'komersial'],
    color: 'pink'
  },
  {
    id: 'int-earth-day-2025',
    name: 'Hari Bumi',
    description: 'Hari Bumi Internasional',
    date: '2025-04-22',
    country: 'Global',
    type: HolidayType.RELIGIOUS,
    isGlobal: true,
    tags: ['lingkungan', 'bumi', 'internasional'],
    color: 'green'
  },
  {
    id: 'int-environment-day-2025',
    name: 'Hari Lingkungan Hidup',
    description: 'Hari Lingkungan Hidup Sedunia',
    date: '2025-06-05',
    country: 'Global',
    type: HolidayType.RELIGIOUS,
    isGlobal: true,
    tags: ['lingkungan', 'internasional'],
    color: 'green'
  },
  {
    id: 'int-black-friday-2025',
    name: 'Black Friday',
    description: 'Hari belanja dengan diskon besar',
    date: '2025-11-28',
    country: 'Global',
    type: HolidayType.RELIGIOUS,
    isGlobal: true,
    tags: ['diskon', 'belanja', 'komersial'],
    color: 'black'
  },
  {
    id: 'int-cyber-monday-2025',
    name: 'Cyber Monday',
    description: 'Hari belanja online dengan diskon besar',
    date: '2025-12-01',
    country: 'Global',
    type: HolidayType.RELIGIOUS,
    isGlobal: true,
    tags: ['diskon', 'belanja', 'online', 'komersial'],
    color: 'blue'
  }
];

/**
 * Data hari penting industri
 */
const INDUSTRY_HOLIDAYS: Holiday[] = [
  // Hari penting pertambangan
  {
    id: 'ind-mining-safety-day-2025',
    name: 'Hari Keselamatan Pertambangan',
    description: 'Hari Keselamatan Pertambangan Internasional',
    date: '2025-04-28',
    country: 'Global',
    type: HolidayType.RELIGIOUS,
    isGlobal: true,
    tags: ['pertambangan', 'keselamatan', 'industri'],
    color: 'orange'
  },
  {
    id: 'ind-mining-day-2025',
    name: 'Hari Pertambangan Indonesia',
    description: 'Hari Pertambangan dan Energi Indonesia',
    date: '2025-09-28',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['pertambangan', 'energi', 'industri'],
    color: 'brown'
  },
  {
    id: 'ind-transportation-day-2025',
    name: 'Hari Perhubungan Nasional',
    description: 'Hari Perhubungan Nasional Indonesia',
    date: '2025-09-17',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['transportasi', 'perhubungan', 'industri'],
    color: 'blue'
  },

  // Pameran dan konferensi pertambangan
  {
    id: 'ind-mining-indo-2025',
    name: 'Mining Indonesia 2025',
    description: 'Pameran Pertambangan Internasional di Jakarta',
    date: '2025-09-10',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['pertambangan', 'pameran', 'industri', 'jakarta'],
    color: 'brown'
  },
  {
    id: 'ind-mining-indo-2025-end',
    name: 'Mining Indonesia 2025 (Akhir)',
    description: 'Hari terakhir Pameran Pertambangan Internasional di Jakarta',
    date: '2025-09-13',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['pertambangan', 'pameran', 'industri', 'jakarta'],
    color: 'brown'
  },
  {
    id: 'ind-coal-indo-2025',
    name: 'Coal & Mining Indonesia 2025',
    description: 'Pameran Batubara dan Pertambangan di Jakarta',
    date: '2025-06-18',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['pertambangan', 'batubara', 'pameran', 'industri'],
    color: 'black'
  },
  {
    id: 'ind-coal-indo-2025-end',
    name: 'Coal & Mining Indonesia 2025 (Akhir)',
    description: 'Hari terakhir Pameran Batubara dan Pertambangan di Jakarta',
    date: '2025-06-21',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['pertambangan', 'batubara', 'pameran', 'industri'],
    color: 'black'
  },

  // Musim dan periode penting untuk pertambangan
  {
    id: 'ind-rainy-season-start-2025',
    name: 'Awal Musim Hujan',
    description: 'Perkiraan awal musim hujan di sebagian besar wilayah pertambangan Indonesia',
    date: '2025-10-01',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['pertambangan', 'musim hujan', 'operasional'],
    color: 'blue'
  },
  {
    id: 'ind-dry-season-start-2025',
    name: 'Awal Musim Kemarau',
    description: 'Perkiraan awal musim kemarau di sebagian besar wilayah pertambangan Indonesia',
    date: '2025-04-01',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['pertambangan', 'musim kemarau', 'operasional'],
    color: 'orange'
  },

  // Periode anggaran dan pengadaan
  {
    id: 'ind-budget-planning-q4-2025',
    name: 'Perencanaan Anggaran Q4',
    description: 'Periode perencanaan anggaran kuartal 4 untuk perusahaan pertambangan',
    date: '2025-09-01',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['pertambangan', 'anggaran', 'pengadaan'],
    color: 'green'
  },
  {
    id: 'ind-fiscal-year-end-2025',
    name: 'Akhir Tahun Fiskal',
    description: 'Akhir tahun fiskal untuk sebagian besar perusahaan pertambangan',
    date: '2025-12-31',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['pertambangan', 'anggaran', 'keuangan'],
    color: 'green'
  },

  // Periode maintenance besar
  {
    id: 'ind-major-maintenance-2025',
    name: 'Periode Maintenance Besar',
    description: 'Periode umum untuk maintenance besar peralatan tambang',
    date: '2025-07-15',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['pertambangan', 'maintenance', 'operasional'],
    color: 'purple'
  },
  {
    id: 'ind-major-maintenance-end-2025',
    name: 'Akhir Periode Maintenance Besar',
    description: 'Akhir periode umum untuk maintenance besar peralatan tambang',
    date: '2025-08-15',
    country: 'Indonesia',
    type: HolidayType.RELIGIOUS,
    isGlobal: false,
    tags: ['pertambangan', 'maintenance', 'operasional'],
    color: 'purple'
  }
];

/**
 * Mendapatkan semua hari libur dan hari penting
 * Fungsi ini tidak async untuk kompatibilitas dengan kode yang sudah ada
 */
export const getAllHolidays = (): Holiday[] => {
  try {
    // Cek apakah ada data di local storage
    const storedHolidays = localStorage.getItem(HOLIDAYS_STORAGE_KEY);
    if (storedHolidays) {
      const holidays = JSON.parse(storedHolidays);
      console.log(`Retrieved ${holidays.length} holidays from localStorage`);
      return holidays;
    }

    // Jika tidak ada, gunakan data default dan simpan ke local storage
    console.log('No holidays found in localStorage, using default data');
    const defaultHolidays = [
      ...INDONESIA_HOLIDAYS,
      ...INTERNATIONAL_HOLIDAYS,
      ...INDUSTRY_HOLIDAYS
    ];

    localStorage.setItem(HOLIDAYS_STORAGE_KEY, JSON.stringify(defaultHolidays));

    // Trigger API fetch in the background to update localStorage for next time
    // This is done asynchronously so it doesn't block the UI
    fetchHolidaysFromAPI().then(apiHolidays => {
      console.log(`Fetched ${apiHolidays.length} holidays from API in background`);
    }).catch(error => {
      console.error('Background API fetch failed:', error);
    });

    return defaultHolidays;
  } catch (error) {
    console.error('Error getting holidays:', error);
    return [
      ...INDONESIA_HOLIDAYS,
      ...INTERNATIONAL_HOLIDAYS,
      ...INDUSTRY_HOLIDAYS
    ];
  }
};

/**
 * Mendapatkan hari libur berdasarkan rentang tanggal
 */
export async function getHolidaysByDateRange(startDate: string, endDate: string): Promise<Holiday[]> {
  try {
    // Extract year from startDate
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    const year = startDateObj.getFullYear();

    console.log(`Getting holidays for date range: ${startDate} to ${endDate} (Year: ${year})`);

    // Clear cache for testing purposes
    // Uncomment this line if you want to force refresh from API
    // localStorage.removeItem(`holidays_${startDate}_${endDate}`);

    // Check if we have cached data for this date range
    const cacheKey = `holidays_${startDate}_${endDate}`;
    const cachedData = localStorage.getItem(cacheKey);

    if (cachedData) {
      console.log(`Using cached holiday data for range ${startDate} to ${endDate}`);
      const parsedData = JSON.parse(cachedData);
      console.log(`Cached holidays: ${parsedData.length} items`);
      return parsedData;
    }

    // Try to fetch from API first
    try {
      // Construct API URL with year parameter to get holidays for the specific year
      const apiUrl = `${API_URL}?year=${year}`;
      console.log(`Fetching holidays from API: ${apiUrl}`);

      const response = await axios.get(apiUrl);
      const holidays = response.data;
      console.log(`API returned ${holidays.length} holidays for year ${year}:`, holidays);

      // Normalize and fix date formats for all holidays
      const normalizedHolidays = holidays.map((holiday: any) => {
        // Fix date format (some APIs return dates like "2025-1-1" instead of "2025-01-01")
        const dateParts = holiday.tanggal.split('-');
        const fixedDate = `${dateParts[0]}-${dateParts[1].padStart(2, '0')}-${dateParts[2].padStart(2, '0')}`;

        return {
          ...holiday,
          tanggal: fixedDate
        };
      });

      // Filter holidays within date range and exclude "Cuti Bersama"
      const filteredHolidays = normalizedHolidays.filter((holiday: any) => {
        // Exclude "Cuti Bersama" entries
        if (holiday.is_cuti) {
          return false;
        }

        // Create date without timezone issues by using YYYY-MM-DD format
        const dateParts = holiday.tanggal.split('-');
        const year = parseInt(dateParts[0]);
        const month = parseInt(dateParts[1]) - 1; // JavaScript months are 0-indexed
        const day = parseInt(dateParts[2]);

        // Create date objects using local time
        const holidayDate = new Date(year, month, day);
        const startDateLocal = new Date(startDateObj.getFullYear(), startDateObj.getMonth(), startDateObj.getDate());
        const endDateLocal = new Date(endDateObj.getFullYear(), endDateObj.getMonth(), endDateObj.getDate());

        return holidayDate >= startDateLocal && holidayDate <= endDateLocal;
      });

      console.log(`Found ${filteredHolidays.length} holidays in date range ${startDate} to ${endDate}:`, filteredHolidays);

      // Transform API response to our Holiday interface
      if (filteredHolidays.length > 0) {
        const transformedHolidays = filteredHolidays.map((holiday: any) => {
          // All holidays are national holidays since we filtered out "Cuti Bersama"
          const isNationalHoliday = true;

          return {
            id: `api-${holiday.tanggal}`,
            date: holiday.tanggal,
            name: holiday.keterangan,
            description: holiday.keterangan,
            country: 'Indonesia',
            type: isNationalHoliday ? HolidayType.NATIONAL : HolidayType.RELIGIOUS,
            isGlobal: false,
            tags: [
              'holiday',
              'api',
              isNationalHoliday ? 'nasional' : 'keagamaan'
            ].filter(tag => tag !== ''),
            color: isNationalHoliday ? 'red' : 'green'
          };
        });

        // Save to localStorage for caching
        localStorage.setItem(cacheKey, JSON.stringify(transformedHolidays));
        console.log(`Saved ${transformedHolidays.length} holidays to cache for range ${startDate} to ${endDate}`);

        return transformedHolidays;
      }
    } catch (apiError) {
      console.error('Error fetching holidays from API, using local data:', apiError);
    }

    // If API fails or returns no results, use local data
    console.log('Using local holiday data as fallback');
    const allHolidays = getAllHolidays();
    const customHolidays = getCustomHolidays();
    const combinedHolidays = [...allHolidays, ...customHolidays];
    console.log(`Local data contains ${combinedHolidays.length} holidays`);

    // Filter by date range
    const filteredLocalHolidays = combinedHolidays.filter(holiday => {
      const holidayDate = new Date(holiday.date);
      return holidayDate >= startDateObj && holidayDate <= endDateObj;
    });

    console.log(`Found ${filteredLocalHolidays.length} local holidays in date range ${startDate} to ${endDate}`);

    // Cache the result
    if (filteredLocalHolidays.length > 0) {
      localStorage.setItem(cacheKey, JSON.stringify(filteredLocalHolidays));
    }

    return filteredLocalHolidays;
  } catch (error) {
    console.error('Error fetching holidays:', error);
    return [];
  }
}

/**
 * Mendapatkan hari libur untuk tanggal tertentu
 */
export async function getHolidaysForDate(date: string): Promise<Holiday[]> {
  try {
    // Check if we have cached data for this date
    const cacheKey = `holiday_${date}`;
    const cachedData = localStorage.getItem(cacheKey);

    if (cachedData) {
      console.log(`Using cached holiday data for ${date}`);
      return JSON.parse(cachedData);
    }

    // Extract year from date
    const dateObj = new Date(date);
    const year = dateObj.getFullYear();

    console.log(`Getting holidays for date: ${date} (Year: ${year})`);

    // Try to fetch from API first
    try {
      // Construct API URL with year parameter
      const apiUrl = `${API_URL}?year=${year}`;
      console.log(`Fetching holidays from API for date: ${date}, using URL: ${apiUrl}`);

      const response = await axios.get(apiUrl);
      const holidays = response.data;
      console.log(`API returned ${holidays.length} holidays for year ${year}`);

      // Normalize and fix date formats for all holidays
      const normalizedHolidays = holidays.map((holiday: any) => {
        // Fix date format (some APIs return dates like "2025-1-1" instead of "2025-01-01")
        const dateParts = holiday.tanggal.split('-');
        const fixedDate = `${dateParts[0]}-${dateParts[1].padStart(2, '0')}-${dateParts[2].padStart(2, '0')}`;

        return {
          ...holiday,
          tanggal: fixedDate
        };
      });

      // Filter holidays for specific date and exclude "Cuti Bersama"
      const filteredHolidays = normalizedHolidays.filter((holiday: any) => {
        // Exclude "Cuti Bersama" entries
        if (holiday.is_cuti) {
          return false;
        }

        // Create date without timezone issues by using YYYY-MM-DD format
        const dateParts = holiday.tanggal.split('-');
        const year = parseInt(dateParts[0]);
        const month = parseInt(dateParts[1]) - 1; // JavaScript months are 0-indexed
        const day = parseInt(dateParts[2]);

        // Create date objects using local time
        const holidayDate = new Date(year, month, day);

        // Parse the target date
        const targetDateParts = date.split('-');
        const targetYear = parseInt(targetDateParts[0]);
        const targetMonth = parseInt(targetDateParts[1]) - 1;
        const targetDay = parseInt(targetDateParts[2]);
        const targetDate = new Date(targetYear, targetMonth, targetDay);

        // Compare year, month, and day
        return holidayDate.getFullYear() === targetDate.getFullYear() &&
               holidayDate.getMonth() === targetDate.getMonth() &&
               holidayDate.getDate() === targetDate.getDate();
      });

      console.log(`Found ${filteredHolidays.length} holidays for date ${date}:`, filteredHolidays);

      // Transform API response to our Holiday interface
      if (filteredHolidays.length > 0) {
        const transformedHolidays = filteredHolidays.map((holiday: any) => {
          // All holidays are national holidays since we filtered out "Cuti Bersama"
          const isNationalHoliday = true;

          return {
            id: `api-${holiday.tanggal}`,
            date: holiday.tanggal,
            name: holiday.keterangan,
            description: holiday.keterangan,
            country: 'Indonesia',
            type: isNationalHoliday ? HolidayType.NATIONAL : HolidayType.RELIGIOUS,
            isGlobal: false,
            tags: [
              'holiday',
              'api',
              isNationalHoliday ? 'nasional' : 'keagamaan'
            ].filter(tag => tag !== ''),
            color: isNationalHoliday ? 'red' : 'green'
          };
        });

        // Save to localStorage for caching
        localStorage.setItem(cacheKey, JSON.stringify(transformedHolidays));
        console.log(`Saved ${transformedHolidays.length} holidays to cache for date ${date}`);

        return transformedHolidays;
      }
    } catch (apiError) {
      console.error('Error fetching holidays from API, using local data:', apiError);
    }

    // If API fails or returns no results, use local data
    console.log('Using local holiday data as fallback for date:', date);
    const allHolidays = getAllHolidays();
    const customHolidays = getCustomHolidays();
    const combinedHolidays = [...allHolidays, ...customHolidays];

    // Filter by date
    const localHolidays = combinedHolidays.filter(holiday => holiday.date === date);
    console.log(`Found ${localHolidays.length} local holidays for date ${date}`);

    // Cache the result
    if (localHolidays.length > 0) {
      localStorage.setItem(cacheKey, JSON.stringify(localHolidays));
    }

    return localHolidays;
  } catch (error) {
    console.error('Error fetching holidays:', error);
    return [];
  }
}

/**
 * Mendapatkan hari libur kustom yang ditambahkan pengguna
 */
export const getCustomHolidays = (): Holiday[] => {
  try {
    const storedHolidays = localStorage.getItem(CUSTOM_HOLIDAYS_STORAGE_KEY);
    if (storedHolidays) {
      return JSON.parse(storedHolidays);
    }
    return [];
  } catch (error) {
    console.error('Error getting custom holidays:', error);
    return [];
  }
};

/**
 * Menyimpan hari libur kustom
 */
export const saveCustomHoliday = (holiday: Holiday): Holiday => {
  try {
    const customHolidays = getCustomHolidays();

    // Cek apakah ini update atau hari libur baru
    const index = customHolidays.findIndex(h => h.id === holiday.id);

    if (index >= 0) {
      // Update hari libur yang sudah ada
      customHolidays[index] = holiday;
    } else {
      // Tambahkan hari libur baru dengan ID yang dibuat
      if (!holiday.id) {
        holiday.id = `custom-${Date.now()}`;
      }
      customHolidays.push(holiday);
    }

    // Simpan ke local storage
    localStorage.setItem(CUSTOM_HOLIDAYS_STORAGE_KEY, JSON.stringify(customHolidays));
    return holiday;
  } catch (error) {
    console.error('Error saving custom holiday:', error);
    throw new Error('Gagal menyimpan hari libur kustom');
  }
};

/**
 * Menghapus hari libur kustom
 */
export const deleteCustomHoliday = (id: string): boolean => {
  try {
    const customHolidays = getCustomHolidays();
    const updatedHolidays = customHolidays.filter(h => h.id !== id);

    if (updatedHolidays.length === customHolidays.length) {
      return false; // Tidak ada hari libur yang dihapus
    }

    localStorage.setItem(CUSTOM_HOLIDAYS_STORAGE_KEY, JSON.stringify(updatedHolidays));
    return true;
  } catch (error) {
    console.error('Error deleting custom holiday:', error);
    return false;
  }
};

/**
 * Membersihkan cache hari libur
 */
export const clearHolidayCache = (): void => {
  try {
    // Get all localStorage keys
    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('holidays_') ||
        key.startsWith('holiday_') ||
        key === HOLIDAYS_STORAGE_KEY ||
        key.startsWith(`${HOLIDAYS_STORAGE_KEY}_`)
      )) {
        keys.push(key);
      }
    }

    // Remove all holiday cache keys
    keys.forEach(key => {
      localStorage.removeItem(key);
    });

    console.log(`Cleared ${keys.length} holiday cache entries`);
  } catch (error) {
    console.error('Error clearing holiday cache:', error);
  }
};

/**
 * Mendapatkan hari libur dari API eksternal dan menyimpannya ke localStorage
 */
export const fetchHolidaysFromAPI = async (year: number = new Date().getFullYear()): Promise<Holiday[]> => {
  try {
    // Construct API URL with year parameter
    const apiUrl = `${API_URL}?year=${year}`;
    console.log(`Fetching all holidays from API: ${apiUrl}`);

    const response = await axios.get(apiUrl);
    const holidays = response.data;
    console.log(`API returned ${holidays.length} holidays for year ${year}`);

    // Normalize and fix date formats for all holidays
    const normalizedHolidays = holidays.map((holiday: any) => {
      // Fix date format (some APIs return dates like "2025-1-1" instead of "2025-01-01")
      const dateParts = holiday.tanggal.split('-');
      const fixedDate = `${dateParts[0]}-${dateParts[1].padStart(2, '0')}-${dateParts[2].padStart(2, '0')}`;

      return {
        ...holiday,
        tanggal: fixedDate
      };
    });

    // Filter out "Cuti Bersama" entries
    const filteredHolidays = normalizedHolidays.filter((item: any) => !item.is_cuti);

    // Transform API response to our Holiday format
    const apiHolidays: Holiday[] = filteredHolidays.map((item: any) => {
      // All items are national holidays since we filtered out "Cuti Bersama"
      const isNationalHoliday = true;

      return {
        id: `api-${item.tanggal}`,
        name: item.keterangan,
        description: item.keterangan,
        date: item.tanggal,
        country: 'Indonesia',
        type: isNationalHoliday ? HolidayType.NATIONAL : HolidayType.RELIGIOUS,
        isGlobal: false,
        tags: [
          'holiday',
          'api',
          isNationalHoliday ? 'nasional' : 'keagamaan'
        ].filter(tag => tag !== ''),
        color: isNationalHoliday ? 'red' : 'green'
      };
    });

    // Save to localStorage for future use
    const storageKey = `${HOLIDAYS_STORAGE_KEY}_${year}`;
    localStorage.setItem(storageKey, JSON.stringify(apiHolidays));
    console.log(`Saved ${apiHolidays.length} holidays for year ${year} to localStorage`);

    return apiHolidays;
  } catch (error) {
    console.error('Error fetching holidays from API:', error);
    return [];
  }
};
