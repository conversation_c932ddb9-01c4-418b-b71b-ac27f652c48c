<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Seasonal Marketing Calendar</h1>
                    <p class="mt-2 text-gray-600"><PERSON><PERSON><PERSON> pemasaran musiman dengan analisis cuaca, harga komoditas, dan faktor industri</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="refreshData"
                        :disabled="isLoading"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
                    >
                        <RefreshCw :class="['h-4 w-4 mr-2', isLoading && 'animate-spin']" />
                        Refresh Data
                    </button>
                    <button
                        @click="openSettingsModal"
                        class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center"
                    >
                        <Settings class="h-4 w-4 mr-2" />
                        Settings
                    </button>
                </div>
            </div>

            <!-- Calendar Controls -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <button
                            @click="previousMonth"
                            class="p-2 hover:bg-gray-100 rounded-md"
                        >
                            <ChevronLeft class="h-5 w-5" />
                        </button>
                        <h2 class="text-xl font-semibold">
                            {{ formatMonthYear(currentDate) }}
                        </h2>
                        <button
                            @click="nextMonth"
                            class="p-2 hover:bg-gray-100 rounded-md"
                        >
                            <ChevronRight class="h-5 w-5" />
                        </button>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- Region Filter -->
                        <select 
                            v-model="selectedRegion" 
                            @change="loadCalendarData"
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="All">Semua Region</option>
                            <option value="Java">Jawa</option>
                            <option value="Sumatra">Sumatera</option>
                            <option value="Kalimantan">Kalimantan</option>
                            <option value="Sulawesi">Sulawesi</option>
                            <option value="Papua">Papua</option>
                        </select>

                        <!-- Product Category Filter -->
                        <select 
                            v-model="selectedCategory" 
                            @change="loadCalendarData"
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="All">Semua Kategori</option>
                            <option value="Earthmover">Earthmover</option>
                            <option value="OTR">OTR</option>
                            <option value="Truck">Truck</option>
                            <option value="Light Truck">Light Truck</option>
                            <option value="Passenger">Passenger</option>
                        </select>
                    </div>
                </div>

                <!-- Legend -->
                <div class="flex items-center space-x-6 mb-4 text-sm">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-green-100 border border-green-500 rounded mr-2"></div>
                        <span>Sangat Direkomendasikan</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-blue-100 border border-blue-500 rounded mr-2"></div>
                        <span>Direkomendasikan</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-yellow-100 border border-yellow-500 rounded mr-2"></div>
                        <span>Netral</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-red-100 border border-red-500 rounded mr-2"></div>
                        <span>Hindari</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 border-l-4 border-l-red-500 rounded mr-2"></div>
                        <span>Hari Libur</span>
                    </div>
                </div>

                <!-- Calendar Grid -->
                <div v-if="isLoading" class="flex justify-center items-center h-96">
                    <Loader2 class="h-8 w-8 animate-spin text-blue-600" />
                    <span class="ml-2 text-gray-600">Loading calendar data...</span>
                </div>

                <div v-else class="border rounded-lg overflow-hidden">
                    <!-- Weekday Headers -->
                    <div class="grid grid-cols-7 bg-gray-50">
                        <div 
                            v-for="day in weekdays" 
                            :key="day"
                            class="p-3 text-center text-sm font-medium text-gray-700 border-r last:border-r-0"
                        >
                            {{ day }}
                        </div>
                    </div>

                    <!-- Calendar Days -->
                    <div class="grid grid-cols-7">
                        <!-- Empty cells for days before month start -->
                        <div 
                            v-for="n in firstDayOfMonth" 
                            :key="`empty-${n}`"
                            class="p-2 border-r border-b bg-gray-50 min-h-[100px]"
                        ></div>

                        <!-- Calendar days -->
                        <div 
                            v-for="day in calendarDays" 
                            :key="day.date"
                            :class="[
                                'p-2 border-r border-b min-h-[100px] cursor-pointer transition-all hover:shadow-md relative',
                                getRecommendationColor(day.recommendationLevel),
                                day.isToday && 'ring-2 ring-blue-500',
                                day.hasHolidays && 'border-l-4 border-l-red-500'
                            ]"
                            @click="selectDay(day)"
                        >
                            <div class="flex justify-between items-start">
                                <span :class="[
                                    'text-sm font-medium',
                                    day.isToday && 'text-blue-700',
                                    day.hasHolidays && 'text-red-700 font-bold'
                                ]">
                                    {{ new Date(day.date).getDate() }}
                                </span>
                                <div class="flex items-center">
                                    <component 
                                        :is="getRecommendationIcon(day.recommendationLevel)" 
                                        class="h-4 w-4"
                                    />
                                    <span class="ml-1 text-xs">{{ day.score.toFixed(0) }}</span>
                                </div>
                            </div>

                            <!-- Day content -->
                            <div class="mt-2 space-y-1">
                                <!-- Weather info -->
                                <div v-if="day.weatherInfo" class="text-xs text-gray-600">
                                    <CloudRain class="h-3 w-3 inline mr-1" />
                                    {{ day.weatherInfo.condition }}
                                </div>

                                <!-- Holidays -->
                                <div v-if="day.holidays && day.holidays.length > 0" class="text-xs text-red-600">
                                    {{ day.holidays[0].name }}
                                </div>

                                <!-- Scheduled promotions -->
                                <div v-if="day.scheduledPromotions && day.scheduledPromotions.length > 0" class="text-xs text-purple-600">
                                    <Calendar class="h-3 w-3 inline mr-1" />
                                    {{ day.scheduledPromotions.length }} promo
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Month Summary -->
            <div v-if="!isLoading && monthSummary" class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <ThumbsUp class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Hari Terbaik</p>
                            <p class="text-2xl font-bold text-gray-900">{{ monthSummary.bestDays }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <AlertTriangle class="h-8 w-8 text-yellow-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Hari Netral</p>
                            <p class="text-2xl font-bold text-gray-900">{{ monthSummary.neutralDays }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <ThumbsDown class="h-8 w-8 text-red-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Hari Hindari</p>
                            <p class="text-2xl font-bold text-gray-900">{{ monthSummary.avoidDays }}</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Calendar class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Promo</p>
                            <p class="text-2xl font-bold text-gray-900">{{ monthSummary.totalPromotions }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Selected Day Details -->
            <div v-if="selectedDay" class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        Detail {{ formatDate(selectedDay.date) }}
                    </h3>
                    <button
                        @click="selectedDay = null"
                        class="text-gray-400 hover:text-gray-500"
                    >
                        <X class="h-5 w-5" />
                    </button>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Recommendation -->
                    <div>
                        <h4 class="font-medium text-gray-900 mb-2">Rekomendasi Marketing</h4>
                        <div :class="[
                            'p-3 rounded-lg',
                            getRecommendationColor(selectedDay.recommendationLevel)
                        ]">
                            <div class="flex items-center">
                                <component 
                                    :is="getRecommendationIcon(selectedDay.recommendationLevel)" 
                                    class="h-5 w-5 mr-2"
                                />
                                <span class="font-medium">{{ getRecommendationText(selectedDay.recommendationLevel) }}</span>
                                <span class="ml-2 text-sm">(Score: {{ selectedDay.score.toFixed(1) }})</span>
                            </div>
                        </div>
                    </div>

                    <!-- Weather Info -->
                    <div v-if="selectedDay.weatherInfo">
                        <h4 class="font-medium text-gray-900 mb-2">Informasi Cuaca</h4>
                        <div class="p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-center">
                                <CloudRain class="h-5 w-5 text-blue-600 mr-2" />
                                <span>{{ selectedDay.weatherInfo.condition }}</span>
                            </div>
                            <div class="mt-2 text-sm text-blue-700">
                                Temp: {{ selectedDay.weatherInfo.temperature }}°C | 
                                Humidity: {{ selectedDay.weatherInfo.humidity }}%
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Seasonal Factors -->
                <div v-if="selectedDay.seasonalFactors && selectedDay.seasonalFactors.length > 0" class="mt-6">
                    <h4 class="font-medium text-gray-900 mb-3">Faktor Musiman</h4>
                    <div class="space-y-2">
                        <div 
                            v-for="factor in selectedDay.seasonalFactors" 
                            :key="factor.id"
                            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                        >
                            <div>
                                <span class="font-medium">{{ factor.name }}</span>
                                <p class="text-sm text-gray-600">{{ factor.description }}</p>
                            </div>
                            <div :class="[
                                'px-2 py-1 rounded text-sm font-medium',
                                factor.impact > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            ]">
                                {{ factor.impact > 0 ? '+' : '' }}{{ factor.impact }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-6 flex space-x-3">
                    <button
                        @click="schedulePromotion(selectedDay)"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center"
                    >
                        <Plus class="h-4 w-4 mr-2" />
                        Jadwalkan Promo
                    </button>
                    <button
                        @click="generateContent(selectedDay)"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Sparkles class="h-4 w-4 mr-2" />
                        Generate Content
                    </button>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    ChevronLeft,
    ChevronRight,
    RefreshCw,
    Settings,
    Loader2,
    CloudRain,
    Calendar,
    ThumbsUp,
    ThumbsDown,
    AlertTriangle,
    X,
    Plus,
    Sparkles,
    CheckCircle,
    Info,
    AlertCircle
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Types
interface WeatherInfo {
    condition: string;
    temperature: number;
    humidity: number;
    rainfall: number;
}

interface Holiday {
    name: string;
    type: 'national' | 'religious' | 'regional';
}

interface SeasonalFactor {
    id: string;
    name: string;
    description: string;
    impact: number;
    type: string;
}

interface ScheduledPromotion {
    id: string;
    title: string;
    description: string;
    type: string;
}

interface CalendarDay {
    date: string;
    score: number;
    recommendationLevel: 'excellent' | 'good' | 'neutral' | 'avoid';
    isToday: boolean;
    hasHolidays: boolean;
    weatherInfo?: WeatherInfo;
    holidays?: Holiday[];
    seasonalFactors?: SeasonalFactor[];
    scheduledPromotions?: ScheduledPromotion[];
}

interface MonthSummary {
    bestDays: number;
    neutralDays: number;
    avoidDays: number;
    totalPromotions: number;
}

// Reactive state
const currentDate = ref(new Date());
const selectedRegion = ref('All');
const selectedCategory = ref('All');
const isLoading = ref(false);
const calendarDays = ref<CalendarDay[]>([]);
const selectedDay = ref<CalendarDay | null>(null);

// Constants
const weekdays = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];

// Computed properties
const firstDayOfMonth = computed(() => {
    const firstDay = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth(), 1);
    return firstDay.getDay();
});

const monthSummary = computed<MonthSummary>(() => {
    if (calendarDays.value.length === 0) {
        return { bestDays: 0, neutralDays: 0, avoidDays: 0, totalPromotions: 0 };
    }

    const summary = calendarDays.value.reduce((acc, day) => {
        switch (day.recommendationLevel) {
            case 'excellent':
            case 'good':
                acc.bestDays++;
                break;
            case 'neutral':
                acc.neutralDays++;
                break;
            case 'avoid':
                acc.avoidDays++;
                break;
        }
        acc.totalPromotions += day.scheduledPromotions?.length || 0;
        return acc;
    }, { bestDays: 0, neutralDays: 0, avoidDays: 0, totalPromotions: 0 });

    return summary;
});

// Utility functions
const formatMonthYear = (date: Date): string => {
    return date.toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'long'
    });
};

const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('id-ID', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};

const getRecommendationColor = (level: string): string => {
    switch (level) {
        case 'excellent':
            return 'bg-green-100 border-green-500';
        case 'good':
            return 'bg-blue-100 border-blue-500';
        case 'neutral':
            return 'bg-yellow-100 border-yellow-500';
        case 'avoid':
            return 'bg-red-100 border-red-500';
        default:
            return 'bg-gray-100 border-gray-300';
    }
};

const getRecommendationIcon = (level: string) => {
    switch (level) {
        case 'excellent':
            return CheckCircle;
        case 'good':
            return ThumbsUp;
        case 'neutral':
            return Info;
        case 'avoid':
            return AlertCircle;
        default:
            return Info;
    }
};

const getRecommendationText = (level: string): string => {
    switch (level) {
        case 'excellent':
            return 'Sangat Direkomendasikan';
        case 'good':
            return 'Direkomendasikan';
        case 'neutral':
            return 'Netral';
        case 'avoid':
            return 'Hindari';
        default:
            return 'Tidak Diketahui';
    }
};

// Calendar navigation
const previousMonth = () => {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1);
    loadCalendarData();
};

const nextMonth = () => {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1);
    loadCalendarData();
};

// Data loading
const loadCalendarData = async () => {
    isLoading.value = true;

    try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Generate calendar data for the current month
        const year = currentDate.value.getFullYear();
        const month = currentDate.value.getMonth();
        const daysInMonth = new Date(year, month + 1, 0).getDate();

        const days: CalendarDay[] = [];
        const today = new Date();

        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(year, month, day);
            const dateString = date.toISOString().split('T')[0];

            // Generate realistic seasonal data
            const dayOfYear = Math.floor((date.getTime() - new Date(year, 0, 0).getTime()) / (1000 * 60 * 60 * 24));
            const seasonalScore = generateSeasonalScore(dayOfYear, day, month);

            const calendarDay: CalendarDay = {
                date: dateString,
                score: seasonalScore.score,
                recommendationLevel: seasonalScore.level,
                isToday: date.toDateString() === today.toDateString(),
                hasHolidays: seasonalScore.hasHolidays,
                weatherInfo: generateWeatherInfo(dayOfYear),
                holidays: seasonalScore.hasHolidays ? [{ name: 'Hari Libur Nasional', type: 'national' }] : [],
                seasonalFactors: generateSeasonalFactors(dayOfYear, month),
                scheduledPromotions: Math.random() > 0.8 ? [{
                    id: `promo-${day}`,
                    title: 'Flash Sale Ban',
                    description: 'Diskon 20% untuk semua kategori ban',
                    type: 'flash_sale'
                }] : []
            };

            days.push(calendarDay);
        }

        calendarDays.value = days;

    } catch (error) {
        console.error('Error loading calendar data:', error);
        alert('Gagal memuat data kalender. Silakan coba lagi.');
    } finally {
        isLoading.value = false;
    }
};

// Generate seasonal score based on various factors
const generateSeasonalScore = (dayOfYear: number, day: number, month: number) => {
    let score = 50; // Base score

    // Weather factor (rainy season impact)
    if (month >= 9 || month <= 2) { // Oct-Mar (rainy season)
        score -= 15;
    } else {
        score += 10;
    }

    // Mining operations factor (Q4 boost)
    if (month >= 9) { // Q4
        score += 20;
    }

    // Weekend factor
    const date = new Date(2024, month, day);
    if (date.getDay() === 0 || date.getDay() === 6) {
        score -= 10;
    }

    // Holiday factor
    const hasHolidays = Math.random() > 0.9; // 10% chance of holiday
    if (hasHolidays) {
        score -= 25;
    }

    // Random variation
    score += (Math.random() - 0.5) * 20;

    // Ensure score is within bounds
    score = Math.max(0, Math.min(100, score));

    // Determine recommendation level
    let level: 'excellent' | 'good' | 'neutral' | 'avoid';
    if (score >= 80) level = 'excellent';
    else if (score >= 60) level = 'good';
    else if (score >= 40) level = 'neutral';
    else level = 'avoid';

    return { score, level, hasHolidays };
};

// Generate weather information
const generateWeatherInfo = (dayOfYear: number): WeatherInfo => {
    const isRainySeason = dayOfYear < 90 || dayOfYear > 270; // Roughly Oct-Mar

    return {
        condition: isRainySeason ? 'Berawan' : 'Cerah',
        temperature: isRainySeason ? 26 + Math.random() * 4 : 28 + Math.random() * 6,
        humidity: isRainySeason ? 75 + Math.random() * 20 : 60 + Math.random() * 15,
        rainfall: isRainySeason ? Math.random() * 50 : Math.random() * 10
    };
};

// Generate seasonal factors
const generateSeasonalFactors = (dayOfYear: number, month: number): SeasonalFactor[] => {
    const factors: SeasonalFactor[] = [];

    // Rainy season factor
    if (month >= 9 || month <= 2) {
        factors.push({
            id: 'rainy-season',
            name: 'Musim Hujan',
            description: 'Musim hujan dapat mempengaruhi operasi pertambangan dan kebutuhan ban',
            impact: -7,
            type: 'weather'
        });
    }

    // Q4 production boost
    if (month >= 9) {
        factors.push({
            id: 'q4-boost',
            name: 'Peningkatan Produksi Q4',
            description: 'Peningkatan produksi pertambangan pada kuartal 4 untuk memenuhi target tahunan',
            impact: 8,
            type: 'mining_operations'
        });
    }

    // Budget cycle
    if (month === 0 || month === 11) { // Jan or Dec
        factors.push({
            id: 'budget-cycle',
            name: 'Siklus Anggaran',
            description: 'Periode perencanaan dan alokasi anggaran tahunan',
            impact: month === 0 ? 5 : -3,
            type: 'budget_cycle'
        });
    }

    return factors;
};

// Event handlers
const selectDay = (day: CalendarDay) => {
    selectedDay.value = day;
};

const refreshData = () => {
    loadCalendarData();
};

const openSettingsModal = () => {
    alert('Settings modal akan dibuka (belum diimplementasi)');
};

const schedulePromotion = (day: CalendarDay) => {
    alert(`Jadwalkan promo untuk ${formatDate(day.date)} (belum diimplementasi)`);
};

const generateContent = (day: CalendarDay) => {
    alert(`Generate content untuk ${formatDate(day.date)} (belum diimplementasi)`);
};

// Initialize data on mount
onMounted(() => {
    loadCalendarData();
});
</script>
