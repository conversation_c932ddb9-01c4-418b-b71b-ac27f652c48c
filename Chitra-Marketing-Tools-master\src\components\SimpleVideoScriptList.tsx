import React, { useState, useEffect } from 'react';
import { VideoScriptFormData, getAllVideoScriptForms, deleteVideoScriptForm } from '../types/videoScriptForm';
import { Button } from './ui/button';
import { Input } from './ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from './ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from './ui/dialog';
import {
  Eye,
  Edit,
  Trash2,
  MoreVertical,
  Search,
  Video,
  FileText
} from 'lucide-react';
import { useToast } from './ui/use-toast';
import { format } from 'date-fns';
import { id } from 'date-fns/locale';
import { getVideoTypeName, getPurposeName, getPlatformName, getTargetAudienceName } from '../utils/videoScriptUtils';

interface SimpleVideoScriptListProps {
  onNavigateToGenerator: () => void;
  onLoadForm: (form: VideoScriptFormData) => void;
}

const SimpleVideoScriptList: React.FC<SimpleVideoScriptListProps> = ({
  onNavigateToGenerator,
  onLoadForm
}) => {
  const [forms, setForms] = useState<VideoScriptFormData[]>([]);
  const [filteredForms, setFilteredForms] = useState<VideoScriptFormData[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [formToDelete, setFormToDelete] = useState<VideoScriptFormData | null>(null);

  const { toast } = useToast();

  // Load saved forms
  useEffect(() => {
    const loadForms = () => {
      try {
        const savedForms = getAllVideoScriptForms();
        setForms(savedForms);
        setFilteredForms(savedForms);
      } catch (error) {
        console.error('Error loading saved forms:', error);
        toast({
          title: 'Error',
          description: 'Gagal memuat daftar form video script',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadForms();
  }, [toast]);

  // Filter forms when search term changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredForms(forms);
      return;
    }

    const lowerSearchTerm = searchTerm.toLowerCase();
    const filtered = forms.filter(form =>
      form.title.toLowerCase().includes(lowerSearchTerm) ||
      getVideoTypeName(form.videoType).toLowerCase().includes(lowerSearchTerm) ||
      getPurposeName(form.purpose).toLowerCase().includes(lowerSearchTerm) ||
      (form.productName && form.productName.toLowerCase().includes(lowerSearchTerm))
    );

    setFilteredForms(filtered);
  }, [searchTerm, forms]);

  // Handle edit form
  const handleEditForm = (form: VideoScriptFormData) => {
    onLoadForm(form);
    onNavigateToGenerator();
  };

  // Handle delete form
  const handleDeleteClick = (form: VideoScriptFormData) => {
    setFormToDelete(form);
    setDeleteDialogOpen(true);
  };

  // Confirm delete form
  const handleConfirmDelete = () => {
    if (!formToDelete) return;

    try {
      deleteVideoScriptForm(formToDelete.id);

      // Update the forms list
      const updatedForms = forms.filter(form => form.id !== formToDelete.id);
      setForms(updatedForms);
      setFilteredForms(updatedForms);

      toast({
        title: 'Form berhasil dihapus',
        description: `Form "${formToDelete.title}" telah dihapus`,
        variant: 'default',
      });
    } catch (error) {
      console.error('Error deleting form:', error);
      toast({
        title: 'Error',
        description: 'Gagal menghapus form video script',
        variant: 'destructive',
      });
    } finally {
      setDeleteDialogOpen(false);
      setFormToDelete(null);
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return format(date, 'dd MMM yyyy, HH:mm', { locale: id });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-4">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onNavigateToGenerator}
          className="flex items-center gap-1"
        >
          <Video className="h-4 w-4" />
          Buat Script Baru
        </Button>
        <div className="text-lg font-semibold">Daftar Form Video Script</div>
        <div className="text-sm text-gray-500">
          {filteredForms.length} form ditemukan
        </div>
      </div>
      
      <div className="flex items-center justify-between">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Cari form video script..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"></div>
        </div>
      ) : filteredForms.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <FileText className="h-16 w-16 text-gray-300 mb-4" />
          <h3 className="text-lg font-medium mb-2">Belum Ada Form</h3>
          <p className="text-gray-500 mb-4">Buat form video script baru di tab Generator Script.</p>
        </div>
      ) : (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Judul</TableHead>
                <TableHead>Jenis</TableHead>
                <TableHead>Tujuan</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Dibuat</TableHead>
                <TableHead className="text-right">Aksi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredForms.map((form) => (
                <TableRow key={form.id} className="hover:bg-gray-50">
                  <TableCell>
                    <div className="font-medium">{form.title}</div>
                    {form.productName && (
                      <div className="text-sm text-gray-500">
                        Produk: {form.productName}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>{getVideoTypeName(form.videoType)}</TableCell>
                  <TableCell>{getPurposeName(form.purpose)}</TableCell>
                  <TableCell>{getPlatformName(form.platform)}</TableCell>
                  <TableCell>{formatDate(form.createdAt)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEditForm(form)}
                        title="Edit & Generate"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <div className="px-2 py-1.5 text-sm font-semibold">Aksi</div>
                          <div className="h-px my-1 bg-gray-200" />
                          <DropdownMenuItem onClick={() => handleEditForm(form)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit & Generate
                          </DropdownMenuItem>
                          <div className="h-px my-1 bg-gray-200" />
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeleteClick(form)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Hapus
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Hapus Form Video Script</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus form "{formToDelete?.title}"? Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Batal
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete}>
              Hapus
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SimpleVideoScriptList;
