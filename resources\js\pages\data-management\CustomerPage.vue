<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Customer Management</h1>
                    <p class="mt-2 text-gray-600">Kelola informasi dan hubungan customer</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="exportCustomers"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export
                    </button>
                    <button
                        @click="openAddModal"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <Plus class="h-4 w-4 mr-2" />
                        Tambah Customer
                    </button>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Users class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Customers</p>
                            <p class="text-2xl font-bold text-gray-900">{{ customers.length }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Building class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Companies</p>
                            <p class="text-2xl font-bold text-gray-900">{{ uniqueCompanies }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <UserCheck class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Active This Month</p>
                            <p class="text-2xl font-bold text-gray-900">{{ activeThisMonth }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Calendar class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">New This Month</p>
                            <p class="text-2xl font-bold text-gray-900">{{ newThisMonth }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex flex-col md:flex-row gap-4">
                    <!-- Search -->
                    <div class="flex-1">
                        <div class="relative">
                            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                            <input
                                v-model="searchQuery"
                                type="text"
                                placeholder="Cari customer, email, telepon, atau perusahaan..."
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="flex gap-3">
                        <select
                            v-model="filterCompany"
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="">Semua Perusahaan</option>
                            <option
                                v-for="company in companyOptions"
                                :key="company"
                                :value="company"
                            >
                                {{ company }}
                            </option>
                        </select>

                        <select
                            v-model="itemsPerPage"
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="10">10 per halaman</option>
                            <option value="25">25 per halaman</option>
                            <option value="50">50 per halaman</option>
                            <option value="100">100 per halaman</option>
                        </select>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div v-if="selectedCustomers.length > 0" class="mt-4 p-3 bg-blue-50 rounded-lg">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-blue-700">
                            {{ selectedCustomers.length }} customer dipilih
                        </span>
                        <div class="flex space-x-2">
                            <button
                                @click="bulkExport"
                                class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                            >
                                Export Terpilih
                            </button>
                            <button
                                @click="bulkDelete"
                                class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                            >
                                Hapus
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        Customer List ({{ filteredCustomers.length }})
                    </h3>
                </div>

                <div v-if="isLoading" class="p-8 text-center">
                    <Loader2 class="h-8 w-8 animate-spin mx-auto text-blue-600" />
                    <p class="mt-2 text-gray-600">Loading customers...</p>
                </div>

                <div v-else-if="filteredCustomers.length === 0" class="p-8 text-center">
                    <Users class="h-12 w-12 mx-auto text-gray-300 mb-4" />
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada customer</h3>
                    <p class="text-gray-600 mb-4">
                        {{ searchQuery ? 'Tidak ada customer yang sesuai dengan pencarian.' : 'Belum ada customer yang ditambahkan.' }}
                    </p>
                    <button
                        @click="openAddModal"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                        Tambah Customer Pertama
                    </button>
                </div>

                <div v-else class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="w-12 px-6 py-3 text-left">
                                    <input
                                        type="checkbox"
                                        :checked="selectedCustomers.length === paginatedCustomers.length && paginatedCustomers.length > 0"
                                        @change="toggleSelectAll"
                                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Customer
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Contact
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Company
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Address
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Created
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr
                                v-for="customer in paginatedCustomers"
                                :key="customer.id"
                                class="hover:bg-gray-50"
                            >
                                <td class="px-6 py-4">
                                    <input
                                        type="checkbox"
                                        :checked="selectedCustomers.includes(customer.id)"
                                        @change="toggleCustomerSelection(customer.id)"
                                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <span class="text-sm font-medium text-blue-600">
                                                {{ customer.name.charAt(0).toUpperCase() }}
                                            </span>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">{{ customer.name }}</div>
                                            <div class="text-sm text-gray-500">ID: {{ customer.id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ customer.email }}</div>
                                    <div class="text-sm text-gray-500">{{ customer.phone }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ customer.company || '-' }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900 max-w-xs truncate">{{ customer.address }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">{{ formatDate(customer.createdAt) }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button
                                            @click="viewCustomer(customer)"
                                            class="text-blue-600 hover:text-blue-900"
                                            title="View Details"
                                        >
                                            <Eye class="h-4 w-4" />
                                        </button>
                                        <button
                                            @click="editCustomer(customer)"
                                            class="text-green-600 hover:text-green-900"
                                            title="Edit"
                                        >
                                            <Edit class="h-4 w-4" />
                                        </button>
                                        <button
                                            @click="deleteCustomer(customer.id)"
                                            :disabled="isDeleting === customer.id"
                                            class="text-red-600 hover:text-red-900 disabled:opacity-50"
                                            title="Delete"
                                        >
                                            <Loader2 v-if="isDeleting === customer.id" class="h-4 w-4 animate-spin" />
                                            <Trash2 v-else class="h-4 w-4" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div v-if="totalPages > 1" class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Menampilkan {{ ((currentPage - 1) * parseInt(itemsPerPage)) + 1 }} -
                            {{ Math.min(currentPage * parseInt(itemsPerPage), filteredCustomers.length) }}
                            dari {{ filteredCustomers.length }} customer
                        </div>
                        <div class="flex space-x-2">
                            <button
                                @click="currentPage--"
                                :disabled="currentPage === 1"
                                class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                            >
                                Previous
                            </button>
                            <span class="px-3 py-1 text-sm text-gray-700">
                                {{ currentPage }} / {{ totalPages }}
                            </span>
                            <button
                                @click="currentPage++"
                                :disabled="currentPage === totalPages"
                                class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                            >
                                Next
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Success/Error Message -->
            <div v-if="message" :class="[
                'p-4 rounded-md',
                message.includes('Error') || message.includes('Gagal')
                    ? 'bg-red-50 text-red-700 border border-red-200'
                    : 'bg-green-50 text-green-700 border border-green-200'
            ]">
                {{ message }}
            </div>
        </div>

        <!-- Add/Edit Customer Modal -->
        <div v-if="isModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        {{ editingCustomer ? 'Edit Customer' : 'Tambah Customer Baru' }}
                    </h3>
                </div>

                <form @submit.prevent="saveCustomer" class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Nama Customer *
                            </label>
                            <input
                                v-model="formData.name"
                                type="text"
                                required
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Masukkan nama customer..."
                            />
                        </div>

                        <!-- Email -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Email *
                            </label>
                            <input
                                v-model="formData.email"
                                type="email"
                                required
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="<EMAIL>"
                            />
                        </div>

                        <!-- Phone -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Telepon *
                            </label>
                            <input
                                v-model="formData.phone"
                                type="tel"
                                required
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="+62 812-3456-7890"
                            />
                        </div>

                        <!-- Company -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Perusahaan
                            </label>
                            <input
                                v-model="formData.company"
                                type="text"
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Nama perusahaan..."
                            />
                        </div>

                        <!-- Address -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Alamat *
                            </label>
                            <textarea
                                v-model="formData.address"
                                rows="3"
                                required
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Masukkan alamat lengkap..."
                            ></textarea>
                        </div>

                        <!-- Notes -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Catatan
                            </label>
                            <textarea
                                v-model="formData.notes"
                                rows="3"
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Catatan tambahan tentang customer..."
                            ></textarea>
                        </div>
                    </div>

                    <!-- Modal Actions -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                        <button
                            type="button"
                            @click="closeModal"
                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                        >
                            Batal
                        </button>
                        <button
                            type="submit"
                            :disabled="isSaving"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
                        >
                            <Loader2 v-if="isSaving" class="h-4 w-4 mr-2 animate-spin" />
                            {{ isSaving ? 'Menyimpan...' : (editingCustomer ? 'Update' : 'Simpan') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Customer Detail Modal -->
        <div v-if="isDetailModalOpen && viewingCustomer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">Detail Customer</h3>
                        <button
                            @click="closeDetailModal"
                            class="text-gray-400 hover:text-gray-500"
                        >
                            <X class="h-5 w-5" />
                        </button>
                    </div>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Customer Info -->
                        <div class="lg:col-span-2">
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h4 class="text-lg font-semibold text-gray-900 mb-4">Informasi Customer</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Nama</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ viewingCustomer.name }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Email</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ viewingCustomer.email }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Telepon</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ viewingCustomer.phone }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Perusahaan</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ viewingCustomer.company || '-' }}</p>
                                    </div>
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-500">Alamat</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ viewingCustomer.address }}</p>
                                    </div>
                                    <div class="md:col-span-2">
                                        <label class="block text-sm font-medium text-gray-500">Catatan</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ viewingCustomer.notes || '-' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Stats -->
                        <div>
                            <div class="bg-blue-50 p-6 rounded-lg">
                                <h4 class="text-lg font-semibold text-blue-900 mb-4">Statistik</h4>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-blue-600">Customer ID</label>
                                        <p class="mt-1 text-sm text-blue-900">{{ viewingCustomer.id }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-blue-600">Tanggal Bergabung</label>
                                        <p class="mt-1 text-sm text-blue-900">{{ formatDate(viewingCustomer.createdAt) }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-blue-600">Terakhir Update</label>
                                        <p class="mt-1 text-sm text-blue-900">{{ formatDate(viewingCustomer.updatedAt) }}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-blue-600">Status</label>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="mt-6 space-y-3">
                                <button
                                    @click="editCustomer(viewingCustomer)"
                                    class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center"
                                >
                                    <Edit class="h-4 w-4 mr-2" />
                                    Edit Customer
                                </button>
                                <button
                                    @click="exportSingleCustomer(viewingCustomer)"
                                    class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center justify-center"
                                >
                                    <Download class="h-4 w-4 mr-2" />
                                    Export Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Download,
    Plus,
    Search,
    Edit,
    Trash2,
    Loader2,
    Users,
    Building,
    UserCheck,
    Calendar,
    Eye,
    X
} from 'lucide-vue-next';
import { ref, computed, onMounted, watch } from 'vue';

// Types
interface Customer {
    id: string;
    name: string;
    email: string;
    phone: string;
    address: string;
    company?: string;
    notes?: string;
    createdAt: Date;
    updatedAt: Date;
}

interface FormData {
    name: string;
    email: string;
    phone: string;
    address: string;
    company: string;
    notes: string;
}

// Reactive state
const customers = ref<Customer[]>([]);
const isLoading = ref(false);
const isSaving = ref(false);
const isDeleting = ref<string | null>(null);
const message = ref('');

// Modal state
const isModalOpen = ref(false);
const isDetailModalOpen = ref(false);
const editingCustomer = ref<Customer | null>(null);
const viewingCustomer = ref<Customer | null>(null);

// Form data
const formData = ref<FormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    company: '',
    notes: ''
});

// Filters and search
const searchQuery = ref('');
const filterCompany = ref('');
const selectedCustomers = ref<string[]>([]);

// Pagination
const currentPage = ref(1);
const itemsPerPage = ref('10');

// Sample data
const sampleCustomers: Customer[] = [
    {
        id: '1',
        name: 'PT Maju Bersama',
        email: '<EMAIL>',
        phone: '+62 812-3456-7890',
        address: 'Jl. Sudirman No. 123, Jakarta Pusat, DKI Jakarta 10110',
        company: 'PT Maju Bersama',
        notes: 'Regular customer, orders monthly. Prefers premium products.',
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2023-06-20')
    },
    {
        id: '2',
        name: 'Bengkel Sejahtera',
        email: '<EMAIL>',
        phone: '+62 878-9012-3456',
        address: 'Jl. Gatot Subroto No. 45, Bandung, Jawa Barat 40262',
        company: 'Bengkel Sejahtera',
        notes: 'Prefers bulk orders with discount.',
        createdAt: new Date('2023-02-10'),
        updatedAt: new Date('2023-05-15')
    },
    {
        id: '3',
        name: 'CV Transportasi Nusantara',
        email: '<EMAIL>',
        phone: '+62 821-7890-1234',
        address: 'Jl. Ahmad Yani No. 78, Surabaya, Jawa Timur 60234',
        company: 'CV Transportasi Nusantara',
        notes: 'Fleet customer with 50+ vehicles.',
        createdAt: new Date('2023-03-05'),
        updatedAt: new Date('2023-07-01')
    },
    {
        id: '4',
        name: 'Toko Ban Mandiri',
        email: '<EMAIL>',
        phone: '+62 856-4567-8901',
        address: 'Jl. Diponegoro No. 156, Medan, Sumatera Utara 20152',
        company: 'Toko Ban Mandiri',
        notes: 'Retail customer, frequent small orders.',
        createdAt: new Date('2023-04-12'),
        updatedAt: new Date('2023-06-30')
    },
    {
        id: '5',
        name: 'PT Logistik Prima',
        email: '<EMAIL>',
        phone: '+62 813-2345-6789',
        address: 'Jl. Raya Bekasi No. 234, Bekasi, Jawa Barat 17141',
        company: 'PT Logistik Prima',
        notes: 'Large corporate customer, quarterly contracts.',
        createdAt: new Date('2023-05-20'),
        updatedAt: new Date('2023-07-15')
    }
];

// Utility functions
const formatDate = (date: Date): string => {
    return new Date(date).toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};

const showMessage = (msg: string, duration = 5000) => {
    message.value = msg;
    setTimeout(() => {
        message.value = '';
    }, duration);
};

// Computed properties
const filteredCustomers = computed(() => {
    let filtered = customers.value;

    // Search filter
    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        filtered = filtered.filter(customer =>
            customer.name.toLowerCase().includes(query) ||
            customer.email.toLowerCase().includes(query) ||
            customer.phone.toLowerCase().includes(query) ||
            (customer.company && customer.company.toLowerCase().includes(query))
        );
    }

    // Company filter
    if (filterCompany.value) {
        filtered = filtered.filter(customer => customer.company === filterCompany.value);
    }

    return filtered;
});

const totalPages = computed(() => {
    return Math.ceil(filteredCustomers.value.length / parseInt(itemsPerPage.value));
});

const paginatedCustomers = computed(() => {
    const start = (currentPage.value - 1) * parseInt(itemsPerPage.value);
    const end = start + parseInt(itemsPerPage.value);
    return filteredCustomers.value.slice(start, end);
});

const uniqueCompanies = computed(() => {
    const companies = customers.value
        .map(c => c.company)
        .filter(c => c && c.trim() !== '');
    return new Set(companies).size;
});

const companyOptions = computed(() => {
    const companies = customers.value
        .map(c => c.company)
        .filter(c => c && c.trim() !== '');
    return [...new Set(companies)].sort();
});

const activeThisMonth = computed(() => {
    const thisMonth = new Date();
    thisMonth.setDate(1);
    return customers.value.filter(c => new Date(c.updatedAt) >= thisMonth).length;
});

const newThisMonth = computed(() => {
    const thisMonth = new Date();
    thisMonth.setDate(1);
    return customers.value.filter(c => new Date(c.createdAt) >= thisMonth).length;
});

// Load customers from localStorage
const loadCustomers = () => {
    isLoading.value = true;
    try {
        const saved = localStorage.getItem('chitraCustomers');
        if (saved) {
            const parsed = JSON.parse(saved);
            // Convert date strings back to Date objects
            customers.value = parsed.map((c: any) => ({
                ...c,
                createdAt: new Date(c.createdAt),
                updatedAt: new Date(c.updatedAt)
            }));
        } else {
            // Load sample data on first visit
            customers.value = [...sampleCustomers];
            saveCustomers();
        }
    } catch (error) {
        console.error('Error loading customers:', error);
        customers.value = [...sampleCustomers];
    } finally {
        isLoading.value = false;
    }
};

// Save customers to localStorage
const saveCustomers = () => {
    try {
        localStorage.setItem('chitraCustomers', JSON.stringify(customers.value));
    } catch (error) {
        console.error('Error saving customers:', error);
        showMessage('Error: Gagal menyimpan data customer.');
    }
};

// Modal functions
const openAddModal = () => {
    editingCustomer.value = null;
    formData.value = {
        name: '',
        email: '',
        phone: '',
        address: '',
        company: '',
        notes: ''
    };
    isModalOpen.value = true;
};

const editCustomer = (customer: Customer) => {
    editingCustomer.value = customer;
    formData.value = {
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        company: customer.company || '',
        notes: customer.notes || ''
    };
    isDetailModalOpen.value = false; // Close detail modal if open
    isModalOpen.value = true;
};

const closeModal = () => {
    isModalOpen.value = false;
    editingCustomer.value = null;
};

const viewCustomer = (customer: Customer) => {
    viewingCustomer.value = customer;
    isDetailModalOpen.value = true;
};

const closeDetailModal = () => {
    isDetailModalOpen.value = false;
    viewingCustomer.value = null;
};

// CRUD operations
const saveCustomer = async () => {
    isSaving.value = true;

    try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (editingCustomer.value) {
            // Update existing customer
            const index = customers.value.findIndex(c => c.id === editingCustomer.value!.id);
            if (index > -1) {
                customers.value[index] = {
                    ...editingCustomer.value,
                    ...formData.value,
                    updatedAt: new Date()
                };
                showMessage(`Customer ${formData.value.name} berhasil diupdate.`);
            }
        } else {
            // Add new customer
            const newCustomer: Customer = {
                id: Date.now().toString(),
                ...formData.value,
                createdAt: new Date(),
                updatedAt: new Date()
            };
            customers.value.push(newCustomer);
            showMessage(`Customer ${formData.value.name} berhasil ditambahkan.`);
        }

        saveCustomers();
        closeModal();

    } catch (error) {
        console.error('Error saving customer:', error);
        showMessage('Error: Gagal menyimpan customer.');
    } finally {
        isSaving.value = false;
    }
};

const deleteCustomer = async (customerId: string) => {
    const customer = customers.value.find(c => c.id === customerId);
    if (!customer) return;

    if (!confirm(`Yakin ingin menghapus customer ${customer.name}?`)) {
        return;
    }

    isDeleting.value = customerId;

    try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        const index = customers.value.findIndex(c => c.id === customerId);
        if (index > -1) {
            customers.value.splice(index, 1);
            saveCustomers();
            showMessage(`Customer ${customer.name} berhasil dihapus.`);

            // Remove from selected if it was selected
            const selectedIndex = selectedCustomers.value.indexOf(customerId);
            if (selectedIndex > -1) {
                selectedCustomers.value.splice(selectedIndex, 1);
            }
        }

    } catch (error) {
        console.error('Error deleting customer:', error);
        showMessage('Error: Gagal menghapus customer.');
    } finally {
        isDeleting.value = null;
    }
};

// Selection functions
const toggleCustomerSelection = (customerId: string) => {
    const index = selectedCustomers.value.indexOf(customerId);
    if (index > -1) {
        selectedCustomers.value.splice(index, 1);
    } else {
        selectedCustomers.value.push(customerId);
    }
};

const toggleSelectAll = () => {
    if (selectedCustomers.value.length === paginatedCustomers.value.length) {
        selectedCustomers.value = [];
    } else {
        selectedCustomers.value = paginatedCustomers.value.map(c => c.id);
    }
};

// Bulk operations
const bulkDelete = async () => {
    if (selectedCustomers.value.length === 0) return;

    if (!confirm(`Yakin ingin menghapus ${selectedCustomers.value.length} customer yang dipilih?`)) {
        return;
    }

    try {
        selectedCustomers.value.forEach(customerId => {
            const index = customers.value.findIndex(c => c.id === customerId);
            if (index > -1) {
                customers.value.splice(index, 1);
            }
        });

        saveCustomers();
        showMessage(`${selectedCustomers.value.length} customer berhasil dihapus.`);
        selectedCustomers.value = [];

    } catch (error) {
        console.error('Error deleting customers:', error);
        showMessage('Error: Gagal menghapus customer.');
    }
};

// Export functions
const exportCustomers = () => {
    try {
        const csvContent = [
            // Header
            ['Name', 'Email', 'Phone', 'Company', 'Address', 'Notes', 'Created Date', 'Updated Date'].join(','),
            // Data
            ...customers.value.map(customer => [
                `"${customer.name}"`,
                customer.email,
                customer.phone,
                `"${customer.company || ''}"`,
                `"${customer.address}"`,
                `"${customer.notes || ''}"`,
                formatDate(customer.createdAt),
                formatDate(customer.updatedAt)
            ].join(','))
        ].join('\n');

        downloadCSV(csvContent, `customers_${new Date().toISOString().split('T')[0]}.csv`);
        showMessage('Data customer berhasil diekspor ke CSV.');

    } catch (error) {
        console.error('Error exporting customers:', error);
        showMessage('Error: Gagal mengekspor data customer.');
    }
};

const bulkExport = () => {
    if (selectedCustomers.value.length === 0) return;

    try {
        const selectedData = customers.value.filter(c => selectedCustomers.value.includes(c.id));

        const csvContent = [
            // Header
            ['Name', 'Email', 'Phone', 'Company', 'Address', 'Notes', 'Created Date', 'Updated Date'].join(','),
            // Data
            ...selectedData.map(customer => [
                `"${customer.name}"`,
                customer.email,
                customer.phone,
                `"${customer.company || ''}"`,
                `"${customer.address}"`,
                `"${customer.notes || ''}"`,
                formatDate(customer.createdAt),
                formatDate(customer.updatedAt)
            ].join(','))
        ].join('\n');

        downloadCSV(csvContent, `selected_customers_${new Date().toISOString().split('T')[0]}.csv`);
        showMessage(`${selectedCustomers.value.length} customer berhasil diekspor ke CSV.`);

    } catch (error) {
        console.error('Error exporting selected customers:', error);
        showMessage('Error: Gagal mengekspor customer terpilih.');
    }
};

const exportSingleCustomer = (customer: Customer) => {
    try {
        const csvContent = [
            // Header
            ['Name', 'Email', 'Phone', 'Company', 'Address', 'Notes', 'Created Date', 'Updated Date'].join(','),
            // Data
            [
                `"${customer.name}"`,
                customer.email,
                customer.phone,
                `"${customer.company || ''}"`,
                `"${customer.address}"`,
                `"${customer.notes || ''}"`,
                formatDate(customer.createdAt),
                formatDate(customer.updatedAt)
            ].join(',')
        ].join('\n');

        downloadCSV(csvContent, `customer_${customer.name.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`);
        showMessage(`Data customer ${customer.name} berhasil diekspor.`);

    } catch (error) {
        console.error('Error exporting customer:', error);
        showMessage('Error: Gagal mengekspor data customer.');
    }
};

const downloadCSV = (csvContent: string, filename: string) => {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

// Watch for pagination reset when filters change
watch([searchQuery, filterCompany], () => {
    currentPage.value = 1;
});

// Initialize data on mount
onMounted(() => {
    loadCustomers();
});
</script>
