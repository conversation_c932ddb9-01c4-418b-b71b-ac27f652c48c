<!DOCTYPE html>
<html lang="id">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Proposal Generator - Chitra Marketing Tools</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f5f7fa;
    }
    .container {
      max-width: 1000px;
      margin: 0 auto;
      padding: 20px;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 24px;
      margin-bottom: 24px;
      border: 1px solid #e5e7eb;
    }
    .btn {
      display: inline-flex;
      align-items: center;
      padding: 8px 16px;
      border-radius: 6px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }
    .btn-primary {
      background-color: #2563eb;
      color: white;
    }
    .btn-primary:hover {
      background-color: #1d4ed8;
    }
    .btn-secondary {
      background-color: #e5e7eb;
      color: #374151;
    }
    .btn-secondary:hover {
      background-color: #d1d5db;
    }
    .form-group {
      margin-bottom: 16px;
    }
    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 4px;
    }
    .form-input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
    }
    .form-input:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
    }
    .grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
    }
    @media (max-width: 640px) {
      .grid {
        grid-template-columns: 1fr;
      }
    }
    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-blue-600">
          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
          <polyline points="14 2 14 8 20 8"></polyline>
          <line x1="16" y1="13" x2="8" y2="13"></line>
          <line x1="16" y1="17" x2="8" y2="17"></line>
          <line x1="10" y1="9" x2="8" y2="9"></line>
        </svg>
        Proposal Generator
      </h1>
      <a href="/" class="btn btn-secondary">
        Kembali ke Dashboard
      </a>
    </header>

    <form id="proposalForm">
      <div class="card">
        <h2 class="text-lg font-semibold mb-4">Pilih Jenis Proposal</h2>
        <select id="proposalType" class="form-input w-full md:w-1/3">
          <option value="bundling">Proposal Bundling</option>
          <option value="consignment">Proposal Konsinyasi</option>
          <option value="trade-in">Proposal Trade-In</option>
        </select>
      </div>
      
      <div class="card">
        <h2 class="text-lg font-semibold mb-4">Upload Template</h2>
        <p class="text-gray-600 mb-4">
          Upload file DOCX yang berisi tag seperti {{CustomerName}}, {{ProductName}}, dsb.
        </p>
        
        <div class="flex items-center space-x-3">
          <input type="file" id="templateFile" class="hidden" accept=".docx">
          
          <button type="button" id="uploadButton" class="btn btn-secondary">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="17 8 12 3 7 8"></polyline>
              <line x1="12" y1="3" x2="12" y2="15"></line>
            </svg>
            Pilih File
          </button>
          
          <span id="fileName" class="text-sm text-gray-600"></span>
        </div>
      </div>
      
      <div class="card">
        <h2 class="text-lg font-semibold mb-4">Data Pelanggan</h2>
        
        <div class="grid">
          <div class="form-group">
            <label for="customerName" class="form-label">
              Nama Pelanggan <span class="text-red-500">*</span>
            </label>
            <input type="text" id="customerName" class="form-input" required>
          </div>
          
          <div class="form-group">
            <label for="customerAddress" class="form-label">
              Alamat
            </label>
            <input type="text" id="customerAddress" class="form-input">
          </div>
          
          <div class="form-group">
            <label for="contactPerson" class="form-label">
              Contact Person
            </label>
            <input type="text" id="contactPerson" class="form-input">
          </div>
          
          <div class="form-group">
            <label for="phoneNumber" class="form-label">
              Nomor Telepon
            </label>
            <input type="text" id="phoneNumber" class="form-input">
          </div>
        </div>
      </div>
      
      <!-- Form Bundling -->
      <div id="bundlingForm" class="card">
        <h2 class="text-lg font-semibold mb-4">Data Produk Bundling</h2>
        
        <div class="grid">
          <div class="form-group">
            <label for="productName" class="form-label">
              Nama Produk Utama <span class="text-red-500">*</span>
            </label>
            <input type="text" id="productName" class="form-input" required>
          </div>
          
          <div class="form-group">
            <label for="productPrice" class="form-label">
              Harga Produk <span class="text-red-500">*</span>
            </label>
            <input type="number" id="productPrice" class="form-input" required>
          </div>
          
          <div class="form-group">
            <label for="secondaryProduct" class="form-label">
              Produk Sekunder
            </label>
            <input type="text" id="secondaryProduct" class="form-input">
          </div>
          
          <div class="form-group">
            <label for="secondaryQuantity" class="form-label">
              Jumlah Produk Sekunder
            </label>
            <input type="number" id="secondaryQuantity" class="form-input" value="1" min="1">
          </div>
        </div>
      </div>
      
      <!-- Form Konsinyasi -->
      <div id="consignmentForm" class="card hidden">
        <h2 class="text-lg font-semibold mb-4">Data Konsinyasi</h2>
        
        <div class="grid">
          <div class="form-group">
            <label for="consignmentProduct" class="form-label">
              Nama Produk <span class="text-red-500">*</span>
            </label>
            <input type="text" id="consignmentProduct" class="form-input" required>
          </div>
          
          <div class="form-group">
            <label for="consignmentPrice" class="form-label">
              Harga Produk <span class="text-red-500">*</span>
            </label>
            <input type="number" id="consignmentPrice" class="form-input" required>
          </div>
          
          <div class="form-group">
            <label for="consignmentPeriod" class="form-label">
              Periode Konsinyasi
            </label>
            <input type="text" id="consignmentPeriod" class="form-input" placeholder="Contoh: 3 bulan">
          </div>
          
          <div class="form-group">
            <label for="paymentTerms" class="form-label">
              Syarat Pembayaran
            </label>
            <input type="text" id="paymentTerms" class="form-input" placeholder="Contoh: 30 hari setelah penjualan">
          </div>
        </div>
      </div>
      
      <!-- Form Trade-In -->
      <div id="tradeInForm" class="card hidden">
        <h2 class="text-lg font-semibold mb-4">Data Trade-In</h2>
        
        <div class="grid">
          <div class="form-group">
            <label for="newProduct" class="form-label">
              Produk Baru <span class="text-red-500">*</span>
            </label>
            <input type="text" id="newProduct" class="form-input" required>
          </div>
          
          <div class="form-group">
            <label for="newProductPrice" class="form-label">
              Harga Produk Baru <span class="text-red-500">*</span>
            </label>
            <input type="number" id="newProductPrice" class="form-input" required>
          </div>
          
          <div class="form-group">
            <label for="oldProduct" class="form-label">
              Produk Lama <span class="text-red-500">*</span>
            </label>
            <input type="text" id="oldProduct" class="form-input" required>
          </div>
          
          <div class="form-group">
            <label for="tradeInValue" class="form-label">
              Nilai Trade-In <span class="text-red-500">*</span>
            </label>
            <input type="number" id="tradeInValue" class="form-input" required>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end mt-6">
        <button type="submit" id="generateButton" class="btn btn-primary">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="7 10 12 15 17 10"></polyline>
            <line x1="12" y1="15" x2="12" y2="3"></line>
          </svg>
          Generate Proposal
        </button>
      </div>
    </form>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Form elements
      const proposalType = document.getElementById('proposalType');
      const uploadButton = document.getElementById('uploadButton');
      const templateFile = document.getElementById('templateFile');
      const fileName = document.getElementById('fileName');
      const bundlingForm = document.getElementById('bundlingForm');
      const consignmentForm = document.getElementById('consignmentForm');
      const tradeInForm = document.getElementById('tradeInForm');
      const proposalForm = document.getElementById('proposalForm');
      const generateButton = document.getElementById('generateButton');
      
      // Handle file upload
      uploadButton.addEventListener('click', function() {
        templateFile.click();
      });
      
      templateFile.addEventListener('change', function() {
        if (this.files && this.files.length > 0) {
          fileName.textContent = this.files[0].name;
        }
      });
      
      // Handle proposal type change
      proposalType.addEventListener('change', function() {
        const type = this.value;
        
        // Hide all forms
        bundlingForm.classList.add('hidden');
        consignmentForm.classList.add('hidden');
        tradeInForm.classList.add('hidden');
        
        // Show selected form
        if (type === 'bundling') {
          bundlingForm.classList.remove('hidden');
        } else if (type === 'consignment') {
          consignmentForm.classList.remove('hidden');
        } else if (type === 'trade-in') {
          tradeInForm.classList.remove('hidden');
        }
      });
      
      // Handle form submission
      proposalForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        const originalText = generateButton.innerHTML;
        generateButton.innerHTML = '<div class="animate-spin h-4 w-4 border-t-2 border-white mr-2"></div> Generating...';
        generateButton.disabled = true;
        
        // Simulate proposal generation
        setTimeout(function() {
          // Show success state
          generateButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><polyline points="20 6 9 17 4 12"></polyline></svg> Berhasil!';
          
          // Reset button after 2 seconds
          setTimeout(function() {
            generateButton.innerHTML = originalText;
            generateButton.disabled = false;
            
            // Show alert
            alert('Proposal berhasil dibuat!');
          }, 2000);
        }, 1500);
      });
    });
  </script>
</body>
</html>
