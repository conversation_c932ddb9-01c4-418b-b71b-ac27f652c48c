import {
  PromoItem,
  PromoDiscount,
  PromoResult,
  PromoConfig,
  DiscountType
} from '../types/promotion';

/**
 * Calculate the total cost of all items
 */
export const calculateTotalCost = (items: PromoItem[]): number => {
  return items.reduce((total, item) => {
    return total + (item.product.cost * item.quantity);
  }, 0);
};

/**
 * Calculate the total selling price of all items
 */
export const calculateTotalSellingPrice = (items: PromoItem[]): number => {
  return items.reduce((total, item) => {
    return total + (item.product.sellingPrice * item.quantity);
  }, 0);
};

/**
 * Calculate the price after applying the discount
 */
export const calculatePriceAfterDiscount = (
  items: PromoItem[],
  discount: PromoDiscount
): number => {
  const totalSellingPrice = calculateTotalSellingPrice(items);

  switch (discount.type) {
    case DiscountType.PERCENTAGE:
      return totalSellingPrice * (1 - discount.value / 100);

    case DiscountType.FIXED_AMOUNT:
      return Math.max(0, totalSellingPrice - discount.value);

    case DiscountType.CASHBACK:
      // For cashback, the customer still pays the full price initially
      return totalSellingPrice;

    case DiscountType.BONUS_UNIT:
      // For bonus units, the price doesn't change
      return totalSellingPrice;

    default:
      return totalSellingPrice;
  }
};

/**
 * Calculate the total discount amount
 */
export const calculateTotalDiscount = (
  items: PromoItem[],
  discount: PromoDiscount
): number => {
  const totalSellingPrice = calculateTotalSellingPrice(items);
  const priceAfterDiscount = calculatePriceAfterDiscount(items, discount);

  if (discount.type === DiscountType.CASHBACK) {
    return discount.value;
  } else if (discount.type === DiscountType.BONUS_UNIT) {
    // For bonus units, calculate the value of the free items
    // Assuming the bonus is of the same product as the first item
    if (items.length > 0) {
      const firstItem = items[0];
      return firstItem.product.sellingPrice * discount.value;
    }
    return 0;
  } else {
    return totalSellingPrice - priceAfterDiscount;
  }
};

/**
 * Calculate the margin after applying the discount
 */
export const calculateMarginAfterDiscount = (
  items: PromoItem[],
  discount: PromoDiscount,
  config: PromoConfig
): number => {
  const totalCost = calculateTotalCost(items);
  const priceAfterDiscount = calculatePriceAfterDiscount(items, discount);
  const additionalCosts = config.additionalCosts.marketing +
                          config.additionalCosts.shipping +
                          config.additionalCosts.other;

  // For cashback, we need to subtract the cashback amount from the revenue
  const effectiveRevenue = discount.type === DiscountType.CASHBACK
    ? priceAfterDiscount - discount.value
    : priceAfterDiscount;

  // For bonus units, we need to add the cost of the bonus units
  const effectiveCost = discount.type === DiscountType.BONUS_UNIT && items.length > 0
    ? totalCost + (items[0].product.cost * discount.value)
    : totalCost;

  const profit = effectiveRevenue - effectiveCost - additionalCosts;

  // Avoid division by zero
  if (effectiveRevenue === 0) {
    return 0;
  }

  return (profit / effectiveRevenue) * 100;
};

/**
 * Calculate the break-even point (minimum units to sell to cover costs)
 */
export const calculateBreakEvenPoint = (
  items: PromoItem[],
  discount: PromoDiscount,
  config: PromoConfig
): number | undefined => {
  // Only calculate BEP if we have at least one item
  if (items.length === 0) {
    return undefined;
  }

  // For simplicity, we'll calculate BEP based on the first item
  const item = items[0];
  const unitSellingPrice = item.product.sellingPrice;
  const unitCost = item.product.cost;

  // Calculate the price after discount for a single unit
  let effectiveUnitPrice = unitSellingPrice;

  if (discount.type === DiscountType.PERCENTAGE) {
    effectiveUnitPrice = unitSellingPrice * (1 - discount.value / 100);
  } else if (discount.type === DiscountType.FIXED_AMOUNT) {
    // For fixed amount, we distribute the discount proportionally
    const totalSellingPrice = calculateTotalSellingPrice(items);
    const discountRatio = discount.value / totalSellingPrice;
    effectiveUnitPrice = unitSellingPrice * (1 - discountRatio);
  } else if (discount.type === DiscountType.CASHBACK) {
    // For cashback, we distribute the cashback proportionally
    const totalSellingPrice = calculateTotalSellingPrice(items);
    const cashbackRatio = discount.value / totalSellingPrice;
    effectiveUnitPrice = unitSellingPrice * (1 - cashbackRatio);
  } else if (discount.type === DiscountType.BONUS_UNIT) {
    // For bonus units, we need to account for the free units
    // If we give 1 free for every N purchased, the effective price is:
    // (N * unitSellingPrice) / (N + 1)
    const N = Math.ceil(items.reduce((total, i) => total + i.quantity, 0) / discount.value);
    effectiveUnitPrice = (N * unitSellingPrice) / (N + discount.value);
  }

  // Calculate fixed costs
  const fixedCosts = config.additionalCosts.marketing +
                     config.additionalCosts.shipping +
                     config.additionalCosts.other +
                     discount.marketingCost;

  // Calculate contribution margin per unit
  const contributionMargin = effectiveUnitPrice - unitCost;

  // Avoid division by zero or negative margin
  if (contributionMargin <= 0) {
    return undefined; // Cannot reach break-even with negative or zero contribution margin
  }

  // Calculate BEP in units
  return Math.ceil(fixedCosts / contributionMargin);
};

/**
 * Calculate the estimated profit based on target sales
 */
export const calculateEstimatedProfit = (
  items: PromoItem[],
  discount: PromoDiscount,
  config: PromoConfig
): number => {
  const totalCost = calculateTotalCost(items);
  const priceAfterDiscount = calculatePriceAfterDiscount(items, discount);
  const additionalCosts = config.additionalCosts.marketing +
                          config.additionalCosts.shipping +
                          config.additionalCosts.other +
                          discount.marketingCost;

  // For cashback, we need to subtract the cashback amount from the revenue
  const effectiveRevenue = discount.type === DiscountType.CASHBACK
    ? priceAfterDiscount - discount.value
    : priceAfterDiscount;

  // For bonus units, we need to add the cost of the bonus units
  const effectiveCost = discount.type === DiscountType.BONUS_UNIT && items.length > 0
    ? totalCost + (items[0].product.cost * discount.value)
    : totalCost;

  // Calculate profit per unit
  const unitProfit = (effectiveRevenue - effectiveCost) /
                     items.reduce((total, item) => total + item.quantity, 0);

  // Calculate total profit based on target sales
  return (unitProfit * discount.targetSales) - additionalCosts;
};

/**
 * Calculate profit per unit
 */
export const calculateProfitPerUnit = (
  items: PromoItem[],
  discount: PromoDiscount,
  config: PromoConfig
): number => {
  if (items.length === 0) {
    return 0;
  }

  const totalCost = calculateTotalCost(items);
  const priceAfterDiscount = calculatePriceAfterDiscount(items, discount);

  // For cashback, we need to subtract the cashback amount from the revenue
  const effectiveRevenue = discount.type === DiscountType.CASHBACK
    ? priceAfterDiscount - discount.value
    : priceAfterDiscount;

  // For bonus units, we need to add the cost of the bonus units
  const effectiveCost = discount.type === DiscountType.BONUS_UNIT && items.length > 0
    ? totalCost + (items[0].product.cost * discount.value)
    : totalCost;

  const totalUnits = items.reduce((total, item) => total + item.quantity, 0);

  // Calculate profit per unit
  return (effectiveRevenue - effectiveCost) / totalUnits;
};

/**
 * Calculate total promotion cost
 */
export const calculateTotalPromoCost = (
  items: PromoItem[],
  discount: PromoDiscount,
  config: PromoConfig
): number => {
  const totalDiscount = calculateTotalDiscount(items, discount);
  const marketingCost = discount.marketingCost;

  return totalDiscount + marketingCost;
};

/**
 * Calculate promotion cost as percentage of revenue
 */
export const calculatePromoCostPercentage = (
  items: PromoItem[],
  discount: PromoDiscount,
  config: PromoConfig
): number => {
  const totalPromoCost = calculateTotalPromoCost(items, discount, config);
  const estimatedRevenue = calculatePriceAfterDiscount(items, discount) *
                          (discount.targetSales / items.reduce((total, item) => total + item.quantity, 0));

  if (estimatedRevenue === 0) {
    return 0;
  }

  return (totalPromoCost / estimatedRevenue) * 100;
};

/**
 * Calculate target sales needed to reach a specific margin
 */
export const calculateTargetSalesForMargin = (
  items: PromoItem[],
  discount: PromoDiscount,
  config: PromoConfig,
  targetMargin: number = 10
): number | undefined => {
  if (items.length === 0) {
    return undefined;
  }

  const totalCost = calculateTotalCost(items);
  const priceAfterDiscount = calculatePriceAfterDiscount(items, discount);
  const fixedCosts = config.additionalCosts.marketing +
                     config.additionalCosts.shipping +
                     config.additionalCosts.other +
                     discount.marketingCost;

  // For cashback, we need to subtract the cashback amount from the revenue
  const effectiveUnitPrice = discount.type === DiscountType.CASHBACK
    ? priceAfterDiscount - discount.value
    : priceAfterDiscount;

  // For bonus units, we need to add the cost of the bonus units
  const effectiveUnitCost = discount.type === DiscountType.BONUS_UNIT && items.length > 0
    ? totalCost + (items[0].product.cost * discount.value)
    : totalCost;

  const totalUnits = items.reduce((total, item) => total + item.quantity, 0);

  // Calculate unit cost and price
  const unitCost = effectiveUnitCost / totalUnits;
  const unitPrice = effectiveUnitPrice / totalUnits;

  // Calculate profit per unit needed to achieve target margin
  // Formula: margin = (profit / revenue) * 100
  // Therefore: profit = (margin * revenue) / 100
  // For a single unit: unitProfit = (targetMargin * unitPrice) / 100
  const targetUnitProfit = (targetMargin * unitPrice) / 100;

  // Current unit profit
  const currentUnitProfit = unitPrice - unitCost;

  // If current unit profit is already higher than target, return current target sales
  if (currentUnitProfit >= targetUnitProfit) {
    return discount.targetSales;
  }

  // Calculate how many units needed to cover fixed costs and achieve target margin
  // Formula: totalProfit = (unitPrice - unitCost) * units - fixedCosts
  // For target margin: targetProfit = (targetMargin * totalRevenue) / 100
  // Where totalRevenue = unitPrice * units
  // So: targetProfit = (targetMargin * unitPrice * units) / 100
  // Setting these equal: (targetMargin * unitPrice * units) / 100 = (unitPrice - unitCost) * units - fixedCosts
  // Solving for units:
  // units = fixedCosts / ((unitPrice - unitCost) - (targetMargin * unitPrice / 100))

  const denominator = (unitPrice - unitCost) - (targetMargin * unitPrice / 100);

  // If denominator is zero or negative, it's impossible to reach target margin
  if (denominator <= 0) {
    return undefined;
  }

  return Math.ceil(fixedCosts / denominator);
};

/**
 * Generate a warning message if the margin is too low
 */
export const generateWarningMessage = (marginAfterPromo: number): string | undefined => {
  if (marginAfterPromo < 0) {
    return 'PERHATIAN: Promo ini akan menghasilkan kerugian! Margin negatif.';
  } else if (marginAfterPromo < 5) {
    return 'PERHATIAN: Margin sangat rendah (< 5%). Pertimbangkan untuk menyesuaikan promo.';
  } else if (marginAfterPromo < 10) {
    return 'PERHATIAN: Margin di bawah 10%. Pastikan volume penjualan cukup tinggi.';
  }

  return undefined;
};

/**
 * Calculate the complete promotion result
 */
export const calculatePromoResult = (
  items: PromoItem[],
  discount: PromoDiscount,
  config: PromoConfig
): PromoResult => {
  const priceAfterPromo = calculatePriceAfterDiscount(items, discount);
  const totalDiscount = calculateTotalDiscount(items, discount);
  const marginAfterPromo = calculateMarginAfterDiscount(items, discount, config);
  const breakEvenPoint = calculateBreakEvenPoint(items, discount, config);
  const estimatedProfit = calculateEstimatedProfit(items, discount, config);
  const warning = generateWarningMessage(marginAfterPromo);
  const profitPerUnit = calculateProfitPerUnit(items, discount, config);
  const totalPromoCost = calculateTotalPromoCost(items, discount, config);
  const promoCostPercentage = calculatePromoCostPercentage(items, discount, config);
  const targetSalesForMargin = calculateTargetSalesForMargin(items, discount, config, 10);

  // Determine margin safety status
  let marginSafetyStatus: 'safe' | 'thin' | 'unsafe';
  if (marginAfterPromo >= 10) {
    marginSafetyStatus = 'safe';
  } else if (marginAfterPromo >= 5) {
    marginSafetyStatus = 'thin';
  } else {
    marginSafetyStatus = 'unsafe';
  }

  return {
    priceAfterPromo,
    totalDiscount,
    marginAfterPromo,
    breakEvenPoint,
    estimatedProfit,
    warning,
    profitPerUnit,
    totalPromoCost,
    promoCostPercentage,
    targetSalesForMargin,
    marginSafetyStatus
  };
};
