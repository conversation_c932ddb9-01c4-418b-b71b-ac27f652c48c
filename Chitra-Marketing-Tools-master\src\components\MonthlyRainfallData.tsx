import React from 'react';
import { CloudRain, Droplets, AlertTriangle } from 'lucide-react';

// Interface for monthly rainfall data
interface MonthlyRainfall {
  month: string;
  kalimantan: number;
  sumatra: number;
  impact: 'high' | 'medium' | 'low';
}

// Static monthly rainfall data for Kalimantan and Sumatra
// Data is in mm of rainfall per month (average)
const MONTHLY_RAINFALL_DATA: MonthlyRainfall[] = [
  { 
    month: 'Januari', 
    kalimantan: 345, 
    sumatra: 320, 
    impact: 'high' 
  },
  { 
    month: 'Februari', 
    kalimantan: 310, 
    sumatra: 290, 
    impact: 'high' 
  },
  { 
    month: 'Maret', 
    kalimantan: 280, 
    sumatra: 260, 
    impact: 'medium' 
  },
  { 
    month: 'April', 
    kalimantan: 230, 
    sumatra: 210, 
    impact: 'medium' 
  },
  { 
    month: 'Mei', 
    kalimantan: 180, 
    sumatra: 170, 
    impact: 'low' 
  },
  { 
    month: 'Juni', 
    kalimantan: 120, 
    sumatra: 140, 
    impact: 'low' 
  },
  { 
    month: 'Juli', 
    kalimantan: 100, 
    sumatra: 120, 
    impact: 'low' 
  },
  { 
    month: 'Agustus', 
    kalimantan: 90, 
    sumatra: 110, 
    impact: 'low' 
  },
  { 
    month: 'September', 
    kalimantan: 130, 
    sumatra: 150, 
    impact: 'low' 
  },
  { 
    month: 'Oktober', 
    kalimantan: 190, 
    sumatra: 210, 
    impact: 'medium' 
  },
  { 
    month: 'November', 
    kalimantan: 270, 
    sumatra: 290, 
    impact: 'high' 
  },
  { 
    month: 'Desember', 
    kalimantan: 330, 
    sumatra: 350, 
    impact: 'high' 
  }
];

// Get the current month's data
const getCurrentMonthData = (): MonthlyRainfall => {
  const currentMonth = new Date().getMonth(); // 0-11
  return MONTHLY_RAINFALL_DATA[currentMonth];
};

// Get the next 3 months' data
const getNextMonthsData = (count: number = 3): MonthlyRainfall[] => {
  const currentMonth = new Date().getMonth(); // 0-11
  const result: MonthlyRainfall[] = [];
  
  for (let i = 1; i <= count; i++) {
    const monthIndex = (currentMonth + i) % 12;
    result.push(MONTHLY_RAINFALL_DATA[monthIndex]);
  }
  
  return result;
};

// Calculate the impact on mining operations
const calculateMiningImpact = (rainfall: number): string => {
  if (rainfall > 300) {
    return 'Sangat tinggi - Operasi pertambangan terhambat signifikan, kondisi jalan buruk, risiko keselamatan tinggi';
  } else if (rainfall > 250) {
    return 'Tinggi - Operasi pertambangan terhambat, produktivitas menurun';
  } else if (rainfall > 200) {
    return 'Sedang - Beberapa hambatan pada operasi pertambangan';
  } else if (rainfall > 150) {
    return 'Rendah - Dampak minimal pada operasi pertambangan';
  } else {
    return 'Sangat rendah - Operasi pertambangan berjalan normal';
  }
};

// Calculate the impact on tire demand
const calculateTireDemand = (rainfall: number): string => {
  if (rainfall > 300) {
    return 'Sangat rendah - Permintaan ban menurun signifikan karena aktivitas pertambangan berkurang';
  } else if (rainfall > 250) {
    return 'Rendah - Permintaan ban menurun';
  } else if (rainfall > 200) {
    return 'Sedang - Permintaan ban stabil';
  } else if (rainfall > 150) {
    return 'Tinggi - Permintaan ban meningkat';
  } else {
    return 'Sangat tinggi - Permintaan ban meningkat signifikan karena aktivitas pertambangan optimal';
  }
};

// Get the impact color
const getImpactColor = (impact: 'high' | 'medium' | 'low'): string => {
  switch (impact) {
    case 'high':
      return 'text-red-600 bg-red-50 border-red-200';
    case 'medium':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'low':
      return 'text-green-600 bg-green-50 border-green-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
};

// Get the rainfall intensity description
const getRainfallIntensity = (rainfall: number): string => {
  if (rainfall > 300) {
    return 'Sangat Tinggi';
  } else if (rainfall > 250) {
    return 'Tinggi';
  } else if (rainfall > 200) {
    return 'Sedang';
  } else if (rainfall > 150) {
    return 'Rendah';
  } else {
    return 'Sangat Rendah';
  }
};

interface MonthlyRainfallDataProps {
  // Add any props if needed
}

const MonthlyRainfallData: React.FC<MonthlyRainfallDataProps> = () => {
  const currentMonthData = getCurrentMonthData();
  const nextMonthsData = getNextMonthsData(3);
  
  return (
    <div className="space-y-6">
      {/* Current month summary */}
      <div className={`p-4 rounded-lg border ${getImpactColor(currentMonthData.impact)}`}>
        <h3 className="text-lg font-bold mb-2 flex items-center">
          <Droplets className="h-5 w-5 mr-2" />
          <span>Bulan Ini: {currentMonthData.month}</span>
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium mb-1">Curah Hujan Rata-rata:</h4>
            <div className="flex items-center justify-between mb-2">
              <span>Kalimantan:</span>
              <span className="font-bold">{currentMonthData.kalimantan} mm</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Sumatra:</span>
              <span className="font-bold">{currentMonthData.sumatra} mm</span>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium mb-1">Dampak:</h4>
            <div className="space-y-1">
              <div className="text-sm">
                <span className="font-medium">Intensitas:</span>{' '}
                <span>{getRainfallIntensity(currentMonthData.kalimantan)}</span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Operasi Pertambangan:</span>{' '}
                <span>{calculateMiningImpact(currentMonthData.kalimantan)}</span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Permintaan Ban:</span>{' '}
                <span>{calculateTireDemand(currentMonthData.kalimantan)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Monthly forecast */}
      <div>
        <h3 className="text-lg font-bold mb-3">Prakiraan 3 Bulan Ke Depan</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {nextMonthsData.map((data, index) => (
            <div 
              key={index} 
              className={`p-3 rounded-lg border ${getImpactColor(data.impact)}`}
            >
              <h4 className="font-medium mb-2">{data.month}</h4>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Kalimantan:</span>
                  <span className="font-bold">{data.kalimantan} mm</span>
                </div>
                <div className="flex justify-between">
                  <span>Sumatra:</span>
                  <span className="font-bold">{data.sumatra} mm</span>
                </div>
                <div>
                  <span className="font-medium">Intensitas:</span>{' '}
                  <span>{getRainfallIntensity(data.kalimantan)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Monthly rainfall table */}
      <div>
        <h3 className="text-lg font-bold mb-3">Data Curah Hujan Bulanan</h3>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bulan
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Kalimantan (mm)
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sumatra (mm)
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Intensitas
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Dampak
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {MONTHLY_RAINFALL_DATA.map((data, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                    {data.month}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                    {data.kalimantan}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                    {data.sumatra}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      data.impact === 'high' ? 'bg-red-100 text-red-800' :
                      data.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {getRainfallIntensity(data.kalimantan)}
                    </span>
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                    {data.impact === 'high' ? 'Tinggi' : 
                     data.impact === 'medium' ? 'Sedang' : 'Rendah'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start">
          <AlertTriangle className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-800 mb-1">Catatan Penting:</h4>
            <p className="text-sm text-blue-700">
              Data curah hujan di atas adalah rata-rata historis dan dapat bervariasi dari tahun ke tahun. 
              Musim hujan di Indonesia umumnya berlangsung dari November hingga Maret, dengan intensitas 
              tertinggi pada Desember-Januari. Selama musim hujan, operasi pertambangan sering terhambat, 
              yang berdampak pada permintaan ban.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MonthlyRainfallData;
