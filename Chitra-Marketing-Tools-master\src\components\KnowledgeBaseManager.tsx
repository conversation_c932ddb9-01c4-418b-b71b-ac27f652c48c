import React, { useState, useEffect } from 'react';
import {
  KnowledgeEntry,
  KnowledgeEntryCreateRequest,
  KnowledgeCategory,
  KnowledgeSearchRequest
} from '../types/knowledgeBase';
import {
  getAllKnowledgeEntries,
  createKnowledgeEntry,
  updateKnowledgeEntry,
  deleteKnowledgeEntry,
  searchKnowledgeEntries
} from '../services/knowledgeBaseService';
import {
  PlusCircle,
  Search,
  Edit,
  Trash2,
  Save,
  X,
  Tag,
  BookOpen,
  Filter,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface KnowledgeBaseManagerProps {
  onSelectEntries?: (entries: KnowledgeEntry[]) => void;
  selectable?: boolean;
  selectedEntries?: KnowledgeEntry[];
}

export default function KnowledgeBaseManager({
  onSelectEntries,
  selectable = false,
  selectedEntries = []
}: KnowledgeBaseManagerProps) {
  // State for knowledge entries
  const [entries, setEntries] = useState<KnowledgeEntry[]>([]);
  const [filteredEntries, setFilteredEntries] = useState<KnowledgeEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // State for editing/creating
  const [isEditing, setIsEditing] = useState(false);
  const [currentEntry, setCurrentEntry] = useState<KnowledgeEntryCreateRequest>({
    title: '',
    content: '',
    category: KnowledgeCategory.PRODUCT_KNOWLEDGE,
    tags: []
  });
  const [editingId, setEditingId] = useState<string | null>(null);
  
  // State for search/filter
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<KnowledgeCategory | ''>('');
  const [selectedTag, setSelectedTag] = useState('');
  
  // State for selection (when selectable is true)
  const [selected, setSelected] = useState<string[]>(selectedEntries.map(e => e.id));
  
  // State for notifications
  const [notification, setNotification] = useState<{
    message: string;
    type: 'success' | 'error';
  } | null>(null);

  // Load entries on component mount
  useEffect(() => {
    loadEntries();
  }, []);
  
  // Update filtered entries when search/filter changes
  useEffect(() => {
    filterEntries();
  }, [entries, searchQuery, selectedCategory, selectedTag]);
  
  // Load all knowledge entries
  const loadEntries = async () => {
    setIsLoading(true);
    try {
      const loadedEntries = await getAllKnowledgeEntries();
      setEntries(loadedEntries);
    } catch (error) {
      console.error('Error loading knowledge entries:', error);
      showNotification('Gagal memuat data pengetahuan', 'error');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Filter entries based on search query and filters
  const filterEntries = () => {
    let filtered = [...entries];
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(entry => 
        entry.title.toLowerCase().includes(query) ||
        entry.content.toLowerCase().includes(query) ||
        entry.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(entry => entry.category === selectedCategory);
    }
    
    // Filter by tag
    if (selectedTag) {
      filtered = filtered.filter(entry => 
        entry.tags.includes(selectedTag)
      );
    }
    
    setFilteredEntries(filtered);
  };
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setCurrentEntry(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle tag input
  const handleTagInput = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && e.currentTarget.value) {
      e.preventDefault();
      const newTag = e.currentTarget.value.trim();
      if (newTag && !currentEntry.tags.includes(newTag)) {
        setCurrentEntry(prev => ({
          ...prev,
          tags: [...prev.tags, newTag]
        }));
        e.currentTarget.value = '';
      }
    }
  };
  
  // Remove a tag
  const removeTag = (tagToRemove: string) => {
    setCurrentEntry(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };
  
  // Save entry (create or update)
  const saveEntry = async () => {
    try {
      if (!currentEntry.title || !currentEntry.content) {
        showNotification('Judul dan konten harus diisi', 'error');
        return;
      }
      
      if (editingId) {
        // Update existing entry
        const updated = await updateKnowledgeEntry({
          id: editingId,
          ...currentEntry
        });
        
        if (updated) {
          setEntries(prev => prev.map(entry => 
            entry.id === editingId ? updated : entry
          ));
          showNotification('Pengetahuan berhasil diperbarui', 'success');
        }
      } else {
        // Create new entry
        const newEntry = await createKnowledgeEntry(currentEntry);
        setEntries(prev => [...prev, newEntry]);
        showNotification('Pengetahuan baru berhasil ditambahkan', 'success');
      }
      
      resetForm();
    } catch (error) {
      console.error('Error saving knowledge entry:', error);
      showNotification('Gagal menyimpan pengetahuan', 'error');
    }
  };
  
  // Delete an entry
  const deleteEntry = async (id: string) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus pengetahuan ini?')) {
      try {
        const success = await deleteKnowledgeEntry(id);
        if (success) {
          setEntries(prev => prev.filter(entry => entry.id !== id));
          showNotification('Pengetahuan berhasil dihapus', 'success');
        }
      } catch (error) {
        console.error('Error deleting knowledge entry:', error);
        showNotification('Gagal menghapus pengetahuan', 'error');
      }
    }
  };
  
  // Edit an entry
  const editEntry = (entry: KnowledgeEntry) => {
    setCurrentEntry({
      title: entry.title,
      content: entry.content,
      category: entry.category,
      tags: [...entry.tags]
    });
    setEditingId(entry.id);
    setIsEditing(true);
  };
  
  // Reset form
  const resetForm = () => {
    setCurrentEntry({
      title: '',
      content: '',
      category: KnowledgeCategory.PRODUCT_KNOWLEDGE,
      tags: []
    });
    setEditingId(null);
    setIsEditing(false);
  };
  
  // Show notification
  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 3000);
  };
  
  // Toggle selection of an entry
  const toggleSelect = (id: string) => {
    if (selected.includes(id)) {
      setSelected(prev => prev.filter(entryId => entryId !== id));
    } else {
      setSelected(prev => [...prev, id]);
    }
  };
  
  // Update parent component with selected entries
  useEffect(() => {
    if (onSelectEntries && selectable) {
      const selectedEntryObjects = entries.filter(entry => selected.includes(entry.id));
      onSelectEntries(selectedEntryObjects);
    }
  }, [selected, entries, onSelectEntries, selectable]);

  return (
    <div className="container mx-auto p-4">
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 p-4 rounded-md shadow-md ${
          notification.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          <div className="flex items-center">
            {notification.type === 'success' ? (
              <CheckCircle className="mr-2 h-5 w-5" />
            ) : (
              <AlertCircle className="mr-2 h-5 w-5" />
            )}
            <span>{notification.message}</span>
          </div>
        </div>
      )}
      
      <div className="flex flex-col md:flex-row gap-6">
        {/* Left side - Entry list and search */}
        <div className="md:w-2/3">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold flex items-center">
                <BookOpen className="mr-2 text-blue-600" />
                Knowledge Base
              </h2>
              <button
                onClick={() => setIsEditing(true)}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <PlusCircle className="mr-1 h-4 w-4" />
                Tambah Baru
              </button>
            </div>
            
            {/* Search and filters */}
            <div className="mb-6 space-y-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Cari pengetahuan..."
                  className="w-full pl-10 pr-4 py-2 border rounded-md"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              </div>
              
              <div className="flex flex-wrap gap-2">
                <select
                  className="px-3 py-2 border rounded-md bg-white"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value as KnowledgeCategory | '')}
                >
                  <option value="">Semua Kategori</option>
                  {Object.values(KnowledgeCategory).map(category => (
                    <option key={category} value={category}>
                      {category.replace(/_/g, ' ')}
                    </option>
                  ))}
                </select>
                
                {/* We could add tag filter here if needed */}
              </div>
            </div>
            
            {/* Entry list */}
            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : filteredEntries.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                Tidak ada data pengetahuan yang ditemukan
              </div>
            ) : (
              <div className="space-y-4">
                {filteredEntries.map(entry => (
                  <div key={entry.id} className="border rounded-md p-4 hover:bg-gray-50">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center">
                          {selectable && (
                            <input
                              type="checkbox"
                              checked={selected.includes(entry.id)}
                              onChange={() => toggleSelect(entry.id)}
                              className="mr-2 h-4 w-4"
                            />
                          )}
                          <h3 className="font-medium text-lg">{entry.title}</h3>
                        </div>
                        <div className="mt-1 text-sm text-gray-500">
                          {entry.category.replace(/_/g, ' ')}
                        </div>
                        <div className="mt-2 text-sm line-clamp-3">
                          {entry.content}
                        </div>
                        <div className="mt-2 flex flex-wrap gap-1">
                          {entry.tags.map(tag => (
                            <span
                              key={tag}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                            >
                              <Tag className="mr-1 h-3 w-3" />
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <button
                          onClick={() => editEntry(entry)}
                          className="p-1 text-blue-600 hover:text-blue-800"
                        >
                          <Edit className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => deleteEntry(entry.id)}
                          className="p-1 text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* Right side - Entry form */}
        {isEditing && (
          <div className="md:w-1/3">
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">
                  {editingId ? 'Edit Pengetahuan' : 'Tambah Pengetahuan Baru'}
                </h2>
                <button
                  onClick={resetForm}
                  className="p-1 text-gray-500 hover:text-gray-700"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Judul</label>
                  <input
                    type="text"
                    name="title"
                    value={currentEntry.title}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-md"
                    placeholder="Judul pengetahuan"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Kategori</label>
                  <select
                    name="category"
                    value={currentEntry.category}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-md"
                  >
                    {Object.values(KnowledgeCategory).map(category => (
                      <option key={category} value={category}>
                        {category.replace(/_/g, ' ')}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Konten</label>
                  <textarea
                    name="content"
                    value={currentEntry.content}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-md h-32"
                    placeholder="Isi pengetahuan"
                  ></textarea>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">Tags</label>
                  <div className="flex flex-wrap gap-1 mb-2">
                    {currentEntry.tags.map(tag => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                      >
                        {tag}
                        <button
                          onClick={() => removeTag(tag)}
                          className="ml-1 text-blue-800 hover:text-blue-900"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border rounded-md"
                    placeholder="Ketik tag dan tekan Enter"
                    onKeyDown={handleTagInput}
                  />
                </div>
                
                <div className="pt-4">
                  <button
                    onClick={saveEntry}
                    className="w-full flex justify-center items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    <Save className="mr-2 h-4 w-4" />
                    {editingId ? 'Perbarui' : 'Simpan'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
