<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">AI Image Generator</h1>
                    <p class="mt-2 text-gray-600">Create professional marketing images and visuals using AI technology</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="saveToLibrary"
                        :disabled="!generatedImages.length"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                        <Save class="h-4 w-4 mr-2" />
                        Save to Library
                    </button>
                    <button
                        @click="downloadImages"
                        :disabled="!generatedImages.length"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Download All
                    </button>
                </div>
            </div>

            <!-- Image Generation Form -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Generate New Image</h3>
                
                <div class="space-y-6">
                    <!-- Prompt Input -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Image Description</label>
                        <textarea
                            v-model="imagePrompt"
                            rows="3"
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Describe the image you want to generate... e.g., 'Professional photo of heavy mining truck with large tires in industrial setting, high quality, realistic'"
                        ></textarea>
                        <p class="text-sm text-gray-500 mt-1">Be specific about style, setting, and details for better results</p>
                    </div>

                    <!-- Generation Settings -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Style</label>
                            <select 
                                v-model="generationSettings.style" 
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="photorealistic">Photorealistic</option>
                                <option value="professional">Professional Photography</option>
                                <option value="industrial">Industrial/Technical</option>
                                <option value="marketing">Marketing/Commercial</option>
                                <option value="illustration">Digital Illustration</option>
                                <option value="technical">Technical Diagram</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Aspect Ratio</label>
                            <select 
                                v-model="generationSettings.aspectRatio" 
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="1:1">Square (1:1)</option>
                                <option value="16:9">Landscape (16:9)</option>
                                <option value="9:16">Portrait (9:16)</option>
                                <option value="4:3">Standard (4:3)</option>
                                <option value="3:2">Photo (3:2)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Quality</label>
                            <select 
                                v-model="generationSettings.quality" 
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="standard">Standard</option>
                                <option value="high">High Quality</option>
                                <option value="ultra">Ultra High Quality</option>
                            </select>
                        </div>
                    </div>

                    <!-- Quick Templates -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Quick Templates</label>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <button
                                v-for="template in imageTemplates"
                                :key="template.id"
                                @click="useTemplate(template)"
                                class="p-3 text-left border border-gray-300 rounded-md hover:border-blue-500 hover:bg-blue-50 transition-colors"
                            >
                                <div class="flex items-center mb-2">
                                    <component :is="template.icon" class="h-5 w-5 text-blue-600 mr-2" />
                                    <span class="font-medium text-gray-900 text-sm">{{ template.name }}</span>
                                </div>
                                <p class="text-xs text-gray-600">{{ template.description }}</p>
                            </button>
                        </div>
                    </div>

                    <!-- Generate Button -->
                    <div class="flex flex-col items-center space-y-4">
                        <button
                            @click="generateImage"
                            :disabled="!imagePrompt.trim() || isGenerating"
                            class="px-8 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-lg"
                        >
                            <Loader2 v-if="isGenerating" class="h-5 w-5 mr-2 animate-spin" />
                            <Sparkles v-else class="h-5 w-5 mr-2" />
                            {{ isGenerating ? 'Generating...' : 'Generate Image' }}
                        </button>

                        <!-- Progress Bar -->
                        <div v-if="isGenerating" class="w-full max-w-md">
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span>Generating image...</span>
                                <span>{{ generationProgress }}%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" :style="{ width: generationProgress + '%' }"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Generated Images -->
            <div v-if="generatedImages.length" class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Generated Images</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div v-for="image in generatedImages" :key="image.id" class="relative group">
                        <div class="aspect-square bg-gray-200 rounded-lg overflow-hidden">
                            <img
                                v-if="image.imageUrl"
                                :src="image.imageUrl"
                                :alt="image.prompt"
                                class="w-full h-full object-cover"
                                @error="handleImageError"
                            />
                            <div v-else class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-purple-100">
                                <div class="text-center">
                                    <ImageIcon class="h-16 w-16 text-gray-400 mx-auto mb-2" />
                                    <p class="text-sm text-gray-600">{{ image.prompt.substring(0, 50) }}...</p>
                                    <p class="text-xs text-gray-500 mt-1">{{ image.style }} • {{ image.aspectRatio }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Image Actions -->
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <div class="flex space-x-2">
                                <button
                                    @click="downloadImage(image)"
                                    class="p-2 bg-white text-gray-900 rounded-full hover:bg-gray-100 transition-colors"
                                >
                                    <Download class="h-4 w-4" />
                                </button>
                                <button
                                    @click="editImage(image)"
                                    class="p-2 bg-white text-gray-900 rounded-full hover:bg-gray-100 transition-colors"
                                >
                                    <Edit class="h-4 w-4" />
                                </button>
                                <button
                                    @click="shareImage(image)"
                                    class="p-2 bg-white text-gray-900 rounded-full hover:bg-gray-100 transition-colors"
                                >
                                    <Share class="h-4 w-4" />
                                </button>
                            </div>
                        </div>

                        <!-- Image Info -->
                        <div class="mt-3">
                            <p class="text-sm font-medium text-gray-900">{{ image.prompt.substring(0, 60) }}...</p>
                            <div class="flex items-center justify-between mt-2">
                                <span class="text-xs text-gray-500">{{ image.createdAt }}</span>
                                <div class="flex items-center space-x-1">
                                    <Star class="h-4 w-4 text-yellow-400" />
                                    <span class="text-xs text-gray-600">{{ image.rating }}/5</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Image Library -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Image Library</h3>
                    <div class="flex space-x-3">
                        <input
                            v-model="searchQuery"
                            type="text"
                            placeholder="Search images..."
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center">
                            <Filter class="h-4 w-4 mr-2" />
                            Filter
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <div v-for="libraryImage in filteredLibraryImages" :key="libraryImage.id" class="relative group cursor-pointer">
                        <div class="aspect-square bg-gray-200 rounded-lg overflow-hidden">
                            <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
                                <ImageIcon class="h-8 w-8 text-gray-400" />
                            </div>
                        </div>
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                            <button class="p-2 bg-white text-gray-900 rounded-full hover:bg-gray-100">
                                <Eye class="h-4 w-4" />
                            </button>
                        </div>
                        <p class="text-xs text-gray-600 mt-1 truncate">{{ libraryImage.name }}</p>
                    </div>
                </div>
            </div>

            <!-- Usage Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <ImageIcon class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Images Generated</p>
                            <p class="text-2xl font-bold text-gray-900">{{ usageStats.generated }}</p>
                            <p class="text-sm text-blue-600">{{ usageStats.thisMonth }} this month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Save class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Saved to Library</p>
                            <p class="text-2xl font-bold text-gray-900">{{ usageStats.saved }}</p>
                            <p class="text-sm text-green-600">{{ usageStats.saveRate }}% save rate</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Download class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Downloads</p>
                            <p class="text-2xl font-bold text-gray-900">{{ usageStats.downloads }}</p>
                            <p class="text-sm text-purple-600">{{ usageStats.downloadRate }}% download rate</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Star class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Avg Rating</p>
                            <p class="text-2xl font-bold text-gray-900">{{ usageStats.avgRating }}/5</p>
                            <p class="text-sm text-orange-600">{{ usageStats.totalRatings }} ratings</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-purple-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-purple-900">Advanced AI Image Features Coming Soon</h3>
                        <p class="text-purple-700 mt-1">
                            Enhanced image generation features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-purple-700 mt-2 space-y-1">
                            <li>Advanced image editing and manipulation tools</li>
                            <li>Brand-consistent image generation with style guides</li>
                            <li>Batch image generation for campaigns</li>
                            <li>Integration with social media scheduling</li>
                            <li>Custom model training for company-specific styles</li>
                            <li>Real-time collaboration and feedback tools</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Save,
    Download,
    Loader2,
    Sparkles,
    ImageIcon,
    Edit,
    Share,
    Star,
    Filter,
    Eye,
    Truck,
    Settings,
    Camera,
    Palette,
    Info
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Types
interface GeneratedImage {
    id: string;
    prompt: string;
    style: string;
    aspectRatio: string;
    quality: string;
    createdAt: string;
    rating: number;
    imageUrl?: string;
}

interface LibraryImage {
    id: string;
    name: string;
    category: string;
    createdAt: string;
}

// Reactive state
const imagePrompt = ref('');
const isGenerating = ref(false);
const searchQuery = ref('');
const generatedImages = ref<GeneratedImage[]>([]);
const generationProgress = ref(0);
const notifications = ref([]);

const generationSettings = ref({
    style: 'photorealistic',
    aspectRatio: '16:9',
    quality: 'high'
});

// Image templates
const imageTemplates = ref([
    {
        id: 'mining-truck',
        name: 'Mining Truck',
        description: 'Heavy mining truck with tires',
        icon: Truck,
        prompt: 'Professional photo of heavy mining truck with large industrial tires in open pit mine, realistic, high quality, industrial setting'
    },
    {
        id: 'tire-closeup',
        name: 'Tire Close-up',
        description: 'Detailed tire photography',
        icon: Settings,
        prompt: 'Close-up professional photo of heavy equipment tire tread pattern, detailed texture, studio lighting, high resolution'
    },
    {
        id: 'industrial-scene',
        name: 'Industrial Scene',
        description: 'Industrial workplace setting',
        icon: Camera,
        prompt: 'Industrial workplace with heavy equipment and tires, professional photography, safety equipment visible, modern facility'
    },
    {
        id: 'marketing-banner',
        name: 'Marketing Banner',
        description: 'Marketing visual design',
        icon: Palette,
        prompt: 'Professional marketing banner design featuring tire products, clean modern design, corporate branding, high quality'
    }
]);

// Library images
const libraryImages = ref<LibraryImage[]>([
    { id: '1', name: 'Mining Truck Hero', category: 'Marketing', createdAt: '2024-01-15' },
    { id: '2', name: 'Tire Detail Shot', category: 'Product', createdAt: '2024-01-14' },
    { id: '3', name: 'Industrial Background', category: 'Background', createdAt: '2024-01-13' },
    { id: '4', name: 'Safety Equipment', category: 'Safety', createdAt: '2024-01-12' },
    { id: '5', name: 'Team Photo', category: 'People', createdAt: '2024-01-11' },
    { id: '6', name: 'Facility Overview', category: 'Facility', createdAt: '2024-01-10' }
]);

// Usage statistics
const usageStats = ref({
    generated: 156,
    thisMonth: 23,
    saved: 89,
    saveRate: 57,
    downloads: 134,
    downloadRate: 86,
    avgRating: 4.2,
    totalRatings: 78
});

// Computed properties
const filteredLibraryImages = computed(() => {
    if (!searchQuery.value) return libraryImages.value;
    return libraryImages.value.filter(image =>
        image.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        image.category.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
});

// Event handlers
const useTemplate = (template: any) => {
    imagePrompt.value = template.prompt;
    alert(`Template "${template.name}" applied!\n\nYou can now modify the prompt and generate the image.`);
};

const generateImage = async () => {
    if (!imagePrompt.value.trim()) return;

    isGenerating.value = true;
    generationProgress.value = 0;

    try {
        // Simulate AI image generation with progress
        for (let i = 0; i <= 100; i += 10) {
            generationProgress.value = i;
            await new Promise(resolve => setTimeout(resolve, 300));
        }

        const newImage: GeneratedImage = {
            id: Date.now().toString(),
            prompt: imagePrompt.value,
            style: generationSettings.value.style,
            aspectRatio: generationSettings.value.aspectRatio,
            quality: generationSettings.value.quality,
            createdAt: new Date().toLocaleDateString('id-ID'),
            rating: Math.floor(Math.random() * 2) + 4, // 4-5 rating
            imageUrl: generatePlaceholderImage(generationSettings.value.aspectRatio)
        };

        generatedImages.value.unshift(newImage);

        // Update usage stats
        usageStats.value.generated++;
        usageStats.value.thisMonth++;

        // Clear prompt for next generation
        imagePrompt.value = '';

        // Show success message
        showNotification('Image generated successfully!', 'success');

    } catch (error) {
        console.error('Error generating image:', error);
        showNotification('Failed to generate image. Please try again.', 'error');
    } finally {
        isGenerating.value = false;
        generationProgress.value = 0;
    }
};

const generatePlaceholderImage = (aspectRatio: string): string => {
    // Generate a unique placeholder image URL based on aspect ratio
    const ratios = {
        '1:1': '400x400',
        '16:9': '800x450',
        '9:16': '450x800',
        '4:3': '800x600',
        '3:2': '600x400'
    };

    const size = ratios[aspectRatio] || '400x400';
    const seed = Math.floor(Math.random() * 1000);
    return `https://picsum.photos/${size}?random=${seed}`;
};

const showNotification = (message: string, type: 'success' | 'error' | 'info') => {
    const notification = {
        id: Date.now(),
        message,
        type,
        timestamp: new Date()
    };

    notifications.value.unshift(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        const index = notifications.value.findIndex(n => n.id === notification.id);
        if (index > -1) {
            notifications.value.splice(index, 1);
        }
    }, 5000);
};

const downloadImage = (image: GeneratedImage) => {
    try {
        // Create a download link for the placeholder image
        const link = document.createElement('a');
        link.href = image.imageUrl || generatePlaceholderImage(image.aspectRatio);
        link.download = `generated_image_${image.id}.jpg`;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Update download stats
        usageStats.value.downloads++;

        showNotification('Image download started!', 'success');
    } catch (error) {
        console.error('Download error:', error);
        showNotification('Failed to download image.', 'error');
    }
};

const editImage = (image: GeneratedImage) => {
    alert(`Opening image editor for: "${image.prompt.substring(0, 30)}..."\n\nFeatures would include:\n- Crop and resize\n- Color adjustments\n- Text overlay\n- Brand elements`);
};

const shareImage = (image: GeneratedImage) => {
    alert(`Sharing options for: "${image.prompt.substring(0, 30)}..."\n\nShare to:\n- Social media platforms\n- Email\n- Direct link\n- Embed code`);
};

const saveToLibrary = () => {
    if (generatedImages.value.length === 0) return;

    try {
        // Add generated images to library
        const newLibraryImages = generatedImages.value.map(img => ({
            id: `lib_${img.id}`,
            name: img.prompt.substring(0, 30) + '...',
            category: getCategoryFromStyle(img.style),
            createdAt: img.createdAt,
            imageUrl: img.imageUrl,
            originalPrompt: img.prompt
        }));

        libraryImages.value.unshift(...newLibraryImages);

        // Update stats
        usageStats.value.saved += generatedImages.value.length;
        usageStats.value.saveRate = Math.round((usageStats.value.saved / usageStats.value.generated) * 100);

        // Save to localStorage
        localStorage.setItem('image_library', JSON.stringify(libraryImages.value));

        showNotification(`${generatedImages.value.length} images saved to library!`, 'success');

        // Clear generated images
        generatedImages.value = [];

    } catch (error) {
        console.error('Error saving to library:', error);
        showNotification('Failed to save images to library.', 'error');
    }
};

const downloadImages = () => {
    if (generatedImages.value.length === 0) return;

    try {
        // Download each image individually (in a real app, this would create a ZIP)
        generatedImages.value.forEach((image, index) => {
            setTimeout(() => {
                downloadImage(image);
            }, index * 500); // Stagger downloads
        });

        showNotification(`Downloading ${generatedImages.value.length} images...`, 'info');

    } catch (error) {
        console.error('Error downloading images:', error);
        showNotification('Failed to download images.', 'error');
    }
};

const getCategoryFromStyle = (style: string): string => {
    const categoryMap = {
        'photorealistic': 'Photography',
        'professional': 'Professional',
        'industrial': 'Industrial',
        'marketing': 'Marketing',
        'illustration': 'Illustration',
        'technical': 'Technical'
    };
    return categoryMap[style] || 'General';
};

const handleImageError = (event: Event) => {
    const img = event.target as HTMLImageElement;
    // Replace with a fallback placeholder
    img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkdlbmVyYXRlZCBJbWFnZTwvdGV4dD48L3N2Zz4=';
};

// Initialize on mount
onMounted(() => {
    // Load saved images and settings
    try {
        const savedImages = localStorage.getItem('generated_images');
        if (savedImages) {
            generatedImages.value = JSON.parse(savedImages);
        }
    } catch (error) {
        console.error('Error loading saved images:', error);
    }
});

// Save images to localStorage when they change
const saveImagesToStorage = () => {
    try {
        localStorage.setItem('generated_images', JSON.stringify(generatedImages.value));
    } catch (error) {
        console.error('Error saving images:', error);
    }
};

// Watch for changes and save
import { watch } from 'vue';
watch(generatedImages, saveImagesToStorage, { deep: true });
</script>
