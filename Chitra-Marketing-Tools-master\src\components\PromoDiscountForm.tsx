import React from 'react';
import { PromoDiscount, DiscountType, PromoType } from '../types/promotion';

interface PromoDiscountFormProps {
  discount: PromoDiscount;
  onDiscountChange: (discount: PromoDiscount) => void;
  promoType: PromoType;
}

const PromoDiscountForm: React.FC<PromoDiscountFormProps> = ({ 
  discount, 
  onDiscountChange,
  promoType
}) => {
  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'type') {
      onDiscountChange({
        ...discount,
        [name]: value as DiscountType,
      });
    } else {
      onDiscountChange({
        ...discount,
        [name]: name === 'value' ? Number(value) : Number(value),
      });
    }
  };

  // Get available discount types based on promo type
  const getAvailableDiscountTypes = () => {
    switch (promoType) {
      case PromoType.DISCOUNT_PERCENTAGE:
        return [
          { value: DiscountType.PERCENTAGE, label: 'Diskon %' },
          { value: DiscountType.FIXED_AMOUNT, label: 'Diskon Rp (Nominal)' }
        ];
      case PromoType.CASHBACK:
        return [
          { value: DiscountType.CASHBACK, label: 'Cashback (Rp)' },
          { value: DiscountType.PERCENTAGE, label: 'Cashback %' }
        ];
      case PromoType.BUY_GET:
        return [
          { value: DiscountType.BONUS_UNIT, label: 'Bonus Unit' }
        ];
      case PromoType.BUNDLING:
        return [
          { value: DiscountType.PERCENTAGE, label: 'Diskon Bundle %' },
          { value: DiscountType.FIXED_AMOUNT, label: 'Diskon Bundle Rp' }
        ];
      default:
        return [
          { value: DiscountType.PERCENTAGE, label: 'Diskon %' },
          { value: DiscountType.FIXED_AMOUNT, label: 'Diskon Rp (Nominal)' },
          { value: DiscountType.CASHBACK, label: 'Cashback' },
          { value: DiscountType.BONUS_UNIT, label: 'Bonus Unit' }
        ];
    }
  };

  // Get value label based on discount type
  const getValueLabel = () => {
    switch (discount.type) {
      case DiscountType.PERCENTAGE:
        return 'Persentase Diskon (%)';
      case DiscountType.FIXED_AMOUNT:
        return 'Nilai Diskon (Rp)';
      case DiscountType.CASHBACK:
        return 'Nilai Cashback (Rp)';
      case DiscountType.BONUS_UNIT:
        return 'Jumlah Unit Bonus';
      default:
        return 'Nilai Diskon';
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-semibold">Simulasi Promo</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Bentuk Promo */}
        <div>
          <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
            Bentuk Promo
          </label>
          <select
            id="type"
            name="type"
            value={discount.type}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            {getAvailableDiscountTypes().map(type => (
              <option key={type.value} value={type.value}>{type.label}</option>
            ))}
          </select>
        </div>

        {/* Nilai Diskon */}
        <div>
          <label htmlFor="value" className="block text-sm font-medium text-gray-700 mb-1">
            {getValueLabel()}
          </label>
          <input
            type="number"
            id="value"
            name="value"
            value={discount.value}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Estimasi Penjualan */}
        <div>
          <label htmlFor="targetSales" className="block text-sm font-medium text-gray-700 mb-1">
            Estimasi Penjualan (Unit)
          </label>
          <input
            type="number"
            id="targetSales"
            name="targetSales"
            value={discount.targetSales}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Estimasi Biaya Promosi */}
        <div>
          <label htmlFor="marketingCost" className="block text-sm font-medium text-gray-700 mb-1">
            Estimasi Biaya Promosi (Rp)
          </label>
          <input
            type="number"
            id="marketingCost"
            name="marketingCost"
            value={discount.marketingCost}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Explanation based on promo type */}
      <div className="mt-4 p-4 bg-blue-50 rounded-md border border-blue-100">
        <h3 className="text-sm font-medium text-blue-800 mb-2">Informasi Bentuk Promo</h3>
        <p className="text-sm text-blue-700">
          {discount.type === DiscountType.PERCENTAGE && (
            <>
              Diskon persentase akan mengurangi harga produk sebesar {discount.value}% dari harga normal.
              Contoh: Jika harga normal Rp 1.000.000, maka harga setelah diskon {discount.value}% adalah Rp {(1000000 * (1 - discount.value / 100)).toLocaleString()}.
            </>
          )}
          {discount.type === DiscountType.FIXED_AMOUNT && (
            <>
              Diskon nominal akan mengurangi harga produk sebesar Rp {discount.value.toLocaleString()} dari harga normal.
              Contoh: Jika harga normal Rp 1.000.000, maka harga setelah diskon Rp {discount.value.toLocaleString()} adalah Rp {(1000000 - discount.value).toLocaleString()}.
            </>
          )}
          {discount.type === DiscountType.CASHBACK && (
            <>
              Cashback akan memberikan pengembalian uang sebesar Rp {discount.value.toLocaleString()} setelah pembelian.
              Customer tetap membayar harga normal, namun mendapatkan cashback setelahnya.
            </>
          )}
          {discount.type === DiscountType.BONUS_UNIT && (
            <>
              Bonus unit akan memberikan {discount.value} unit produk tambahan secara gratis.
              Contoh: Buy 3 Get {discount.value} Free - Customer membeli 3 unit dengan harga normal dan mendapatkan {discount.value} unit gratis.
            </>
          )}
        </p>
      </div>
    </div>
  );
};

export default PromoDiscountForm;
