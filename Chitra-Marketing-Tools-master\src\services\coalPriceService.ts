import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

// Interface for coal price data
export interface CoalPrice {
  date: string;
  price: number;
  currency: string;
}

// Interface for coal price forecast
export interface CoalPriceForecast {
  month: string;
  price: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
}

// Interface for coal price trend
export interface CoalPriceTrend {
  currentPrice: number;
  previousPrice: number;
  change: number;
  percentChange: number;
  trend: 'up' | 'down' | 'stable';
  lastUpdated: string;
  forecast: 'increasing' | 'decreasing' | 'stable';
  impact: number; // Impact score from -10 to 10
  forecasts?: CoalPriceForecast[]; // Next 3 months forecast
}

// Local storage keys
const COAL_PRICE_KEY = 'coalPrices';
const COAL_PRICE_LAST_UPDATED_KEY = 'coalPriceLastUpdated';
const COAL_PRICE_FORECAST_KEY = 'coalPriceForecast';

// OpenRouter API key
const API_KEY = '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';
const FORECAST_MODEL = 'openai/gpt-4.1-nano-2025-04-14';

// Path to ESDM CSV file
const ESDM_CSV_PATH = 'Data ESDM mei.csv';

// Fallback coal price data in case the file read fails
const FALLBACK_COAL_PRICES: CoalPrice[] = [
  { date: '2024-06-01', price: 123.00, currency: 'USD' },
  { date: '2024-05-01', price: 121.15, currency: 'USD' },
  { date: '2024-04-01', price: 120.20, currency: 'USD' },
  { date: '2024-03-01', price: 117.76, currency: 'USD' },
  { date: '2024-02-01', price: 124.24, currency: 'USD' },
  { date: '2024-01-01', price: 124.01, currency: 'USD' }
];

/**
 * Read and parse the ESDM CSV file
 */
export const readESDMData = (): { months: string[], prices: number[] } => {
  try {
    // Read the CSV file content
    const csvContent = localStorage.getItem('esdmCsvData');

    if (!csvContent) {
      console.error('ESDM CSV data not found in localStorage');
      return { months: [], prices: [] };
    }

    // Parse the CSV content
    const lines = csvContent.split('\n');
    const headers = lines[0].split(';');
    const batubara = lines[1].split(';');

    // Extract months and prices
    const months = headers.slice(1); // Skip the first column (Komoditas)
    const prices = batubara.slice(1).map(price => parseFloat(price.replace(',', '.'))); // Skip the first column (Batubara)

    return { months, prices };
  } catch (error) {
    console.error('Error reading ESDM CSV file:', error);
    return { months: [], prices: [] };
  }
};

/**
 * Load the ESDM CSV data into localStorage
 */
export const loadESDMDataToLocalStorage = async (csvContent: string): Promise<void> => {
  try {
    localStorage.setItem('esdmCsvData', csvContent);
    console.log('ESDM CSV data loaded into localStorage');
  } catch (error) {
    console.error('Error loading ESDM CSV data to localStorage:', error);
  }
};

/**
 * Fetch coal price data from localStorage
 */
export const fetchCoalPrices = async (): Promise<CoalPrice[]> => {
  try {
    // First, try to get data from the CRUD component
    const crudData = localStorage.getItem('coalPrices');

    if (crudData) {
      const prices = JSON.parse(crudData) as CoalPrice[];
      if (prices.length > 0) {
        console.log(`Using coal price data from CRUD component (${prices.length} records)`);
        return prices;
      }
    }

    // If no CRUD data, check if we have cached data
    const lastUpdated = localStorage.getItem(COAL_PRICE_LAST_UPDATED_KEY);
    const cachedData = localStorage.getItem(COAL_PRICE_KEY);

    if (lastUpdated && cachedData) {
      const lastUpdateTime = new Date(lastUpdated).getTime();
      const currentTime = new Date().getTime();
      const hoursSinceUpdate = (currentTime - lastUpdateTime) / (1000 * 60 * 60);

      if (hoursSinceUpdate < 24) {
        console.log(`Using cached coal price data (${hoursSinceUpdate.toFixed(1)} hours old)`);
        return JSON.parse(cachedData);
      }
    }

    console.log('Fetching coal price data from ESDM CSV file');

    // Read ESDM data from CSV file
    const { months, prices } = readESDMData();

    if (months.length === 0 || prices.length === 0) {
      console.warn('No data found in ESDM CSV file, using fallback data');
      return FALLBACK_COAL_PRICES;
    }

    // Convert to CoalPrice format
    const coalPrices: CoalPrice[] = [];

    // Process the first 6 months (most recent data)
    for (let i = 0; i < Math.min(6, months.length); i++) {
      // Parse month string to date
      let date: Date;
      const monthStr = months[i].trim();

      if (monthStr.includes('-')) {
        // Format: "Sep-24"
        const [month, year] = monthStr.split('-');
        const monthMap: { [key: string]: number } = {
          'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
          'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
        };
        date = new Date(2000 + parseInt(year), monthMap[month], 1);
      } else {
        // Format: "Juni 2024"
        const parts = monthStr.split(' ');
        const monthMap: { [key: string]: number } = {
          'Januari': 0, 'Februari': 1, 'Maret': 2, 'April': 3, 'Mei': 4, 'Juni': 5,
          'Juli': 6, 'Agustus': 7, 'September': 8, 'Oktober': 9, 'November': 10, 'Desember': 11
        };
        date = new Date(parseInt(parts[1]), monthMap[parts[0]], 1);
      }

      // Format date as ISO string and extract YYYY-MM-DD
      const dateStr = date.toISOString().split('T')[0].substring(0, 7) + '-01';

      coalPrices.push({
        date: dateStr,
        price: prices[i],
        currency: 'USD'
      });
    }

    // Sort by date (newest first)
    coalPrices.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // Cache the data
    localStorage.setItem(COAL_PRICE_KEY, JSON.stringify(coalPrices));
    localStorage.setItem(COAL_PRICE_LAST_UPDATED_KEY, new Date().toISOString());

    return coalPrices;
  } catch (error) {
    console.error('Error fetching coal prices:', error);

    // Return cached data if available, even if it's old
    const cachedData = localStorage.getItem(COAL_PRICE_KEY);
    if (cachedData) {
      console.log('Using old cached coal price data due to API error');
      return JSON.parse(cachedData);
    }

    // Return fallback data if no cached data
    return FALLBACK_COAL_PRICES;
  }
};

/**
 * Forecast coal prices for the next 3 months using AI
 */
export const forecastCoalPrices = async (historicalPrices: CoalPrice[]): Promise<CoalPriceForecast[]> => {
  try {
    // Check if we have cached forecast data that's less than 24 hours old
    const lastUpdated = localStorage.getItem(COAL_PRICE_LAST_UPDATED_KEY);
    const cachedForecast = localStorage.getItem(COAL_PRICE_FORECAST_KEY);

    if (lastUpdated && cachedForecast) {
      const lastUpdateTime = new Date(lastUpdated).getTime();
      const currentTime = new Date().getTime();
      const hoursSinceUpdate = (currentTime - lastUpdateTime) / (1000 * 60 * 60);

      if (hoursSinceUpdate < 24) {
        console.log(`Using cached coal price forecast (${hoursSinceUpdate.toFixed(1)} hours old)`);
        return JSON.parse(cachedForecast);
      }
    }

    console.log('Generating coal price forecast using AI...');

    // Get ESDM data for more context
    const { months, prices } = readESDMData();

    // Sort prices by date (oldest first for time series analysis)
    const sortedPrices = [...historicalPrices].sort((a, b) =>
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    // Prepare the prompt for AI analysis
    const requestBody = {
      model: FORECAST_MODEL,
      messages: [
        {
          role: 'system',
          content: `You are an expert in coal price forecasting and market analysis. You analyze historical coal price data to provide accurate forecasts for future prices. Your forecasts should be data-driven and consider market trends, seasonal patterns, and economic indicators.`
        },
        {
          role: 'user',
          content: `I need you to forecast coal prices for the next 3 months based on the following historical data:

Historical Coal Prices (USD/ton):
${sortedPrices.map(p => `${p.date}: $${p.price.toFixed(2)}`).join('\n')}

Additional ESDM Coal Price Data:
${months.map((month, i) => `${month}: $${prices[i].toFixed(2)}`).join('\n')}

Please provide your forecast in JSON format with these fields for each of the next 3 months:
- month: Month name and year (e.g., "Juli 2024")
- price: Forecasted price in USD/ton
- change: Percentage change from previous month
- trend: "up", "down", or "stable"

Your response should ONLY contain valid JSON that can be parsed.`
        }
      ]
    };

    // Call the OpenRouter API
    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chitraparatama.co.id',
          'X-Title': 'Chitra Marketing Tools'
        }
      }
    );

    // Extract the AI response
    const aiResponse = response.data.choices[0].message.content;

    // Parse the JSON response
    try {
      // Extract JSON from the response (in case there's any text before or after)
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in the response');
      }

      const jsonResponse = JSON.parse(jsonMatch[0]);

      // Ensure the response has the expected format
      if (!jsonResponse.forecasts || !Array.isArray(jsonResponse.forecasts)) {
        throw new Error('Invalid forecast format in AI response');
      }

      const forecasts: CoalPriceForecast[] = jsonResponse.forecasts;

      // Cache the forecasts
      localStorage.setItem(COAL_PRICE_FORECAST_KEY, JSON.stringify(forecasts));

      return forecasts;
    } catch (parseError) {
      console.error('Error parsing AI forecast response:', parseError);
      console.log('Raw AI response:', aiResponse);

      // Return a fallback forecast
      return generateFallbackForecast(historicalPrices);
    }
  } catch (error) {
    console.error('Error forecasting coal prices:', error);

    // Return a fallback forecast
    return generateFallbackForecast(historicalPrices);
  }
};

/**
 * Generate a fallback forecast when AI fails
 */
const generateFallbackForecast = (historicalPrices: CoalPrice[]): CoalPriceForecast[] => {
  // Sort prices by date (newest first)
  const sortedPrices = [...historicalPrices].sort((a, b) =>
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  // Get the latest price
  const latestPrice = sortedPrices[0].price;

  // Calculate average change over the last few months
  let avgChange = 0;
  if (sortedPrices.length > 1) {
    let totalChange = 0;
    for (let i = 0; i < sortedPrices.length - 1; i++) {
      const change = sortedPrices[i].price - sortedPrices[i + 1].price;
      totalChange += change;
    }
    avgChange = totalChange / (sortedPrices.length - 1);
  }

  // Generate forecasts for the next 3 months
  const forecasts: CoalPriceForecast[] = [];
  let currentPrice = latestPrice;

  // Get the current month and year
  const currentDate = new Date();
  const monthNames = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ];

  for (let i = 0; i < 3; i++) {
    // Calculate next month
    const forecastDate = new Date(currentDate);
    forecastDate.setMonth(currentDate.getMonth() + i + 1);

    // Calculate forecasted price with some randomness
    const randomFactor = 0.9 + Math.random() * 0.2; // 0.9 to 1.1
    const priceChange = avgChange * randomFactor;
    const newPrice = currentPrice + priceChange;

    // Calculate percent change
    const percentChange = (priceChange / currentPrice) * 100;

    // Determine trend
    let trend: 'up' | 'down' | 'stable' = 'stable';
    if (percentChange > 1) {
      trend = 'up';
    } else if (percentChange < -1) {
      trend = 'down';
    }

    // Add forecast
    forecasts.push({
      month: `${monthNames[forecastDate.getMonth()]} ${forecastDate.getFullYear()}`,
      price: parseFloat(newPrice.toFixed(2)),
      change: parseFloat(percentChange.toFixed(2)),
      trend
    });

    // Update current price for next iteration
    currentPrice = newPrice;
  }

  return forecasts;
};

/**
 * Analyze coal price trend
 */
export const analyzeCoalPriceTrend = async (): Promise<CoalPriceTrend> => {
  try {
    // Fetch coal prices
    const prices = await fetchCoalPrices();

    // Sort prices by date (newest first)
    prices.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // Get current and previous prices
    const currentPrice = prices[0].price;
    const previousPrice = prices[1].price;

    // Calculate change and percent change
    const change = currentPrice - previousPrice;
    const percentChange = (change / previousPrice) * 100;

    // Determine trend
    let trend: 'up' | 'down' | 'stable' = 'stable';
    if (percentChange > 1) {
      trend = 'up';
    } else if (percentChange < -1) {
      trend = 'down';
    }

    // Analyze longer-term trend for forecast
    const pricesCount = prices.length;
    const recentPrices = prices.slice(0, Math.min(6, pricesCount));
    const olderPrices = prices.slice(Math.min(6, pricesCount));

    const recentAvg = recentPrices.reduce((sum, p) => sum + p.price, 0) / recentPrices.length;
    const olderAvg = olderPrices.length > 0
      ? olderPrices.reduce((sum, p) => sum + p.price, 0) / olderPrices.length
      : recentAvg;

    // Determine forecast
    let forecast: 'increasing' | 'decreasing' | 'stable' = 'stable';
    const longTermChange = recentAvg - olderAvg;
    const longTermPercentChange = (longTermChange / olderAvg) * 100;

    if (longTermPercentChange > 3) {
      forecast = 'increasing';
    } else if (longTermPercentChange < -3) {
      forecast = 'decreasing';
    }

    // Calculate impact score (-10 to 10)
    // Higher coal prices generally mean more mining activity and more tire demand
    // But extremely high prices can lead to market instability
    let impact = 0;

    // Base impact on current price level
    if (currentPrice > 130) {
      impact = 8; // Very high prices - strong positive impact
    } else if (currentPrice > 110) {
      impact = 6; // High prices - positive impact
    } else if (currentPrice > 90) {
      impact = 4; // Moderate prices - slightly positive impact
    } else if (currentPrice > 70) {
      impact = 0; // Normal prices - neutral impact
    } else if (currentPrice > 50) {
      impact = -4; // Low prices - negative impact
    } else {
      impact = -8; // Very low prices - strong negative impact
    }

    // Adjust impact based on trend
    if (trend === 'up') {
      impact += 2; // Rising prices increase impact
    } else if (trend === 'down') {
      impact -= 2; // Falling prices decrease impact
    }

    // Ensure impact is within -10 to 10 range
    impact = Math.max(-10, Math.min(10, impact));

    // Get price forecasts for the next 3 months
    const forecasts = await forecastCoalPrices(prices);

    return {
      currentPrice,
      previousPrice,
      change,
      percentChange,
      trend,
      lastUpdated: new Date().toISOString(),
      forecast,
      impact,
      forecasts
    };
  } catch (error) {
    console.error('Error analyzing coal price trend:', error);

    // Return a neutral trend if analysis fails
    return {
      currentPrice: 110,
      previousPrice: 110,
      change: 0,
      percentChange: 0,
      trend: 'stable',
      lastUpdated: new Date().toISOString(),
      forecast: 'stable',
      impact: 0
    };
  }
};

/**
 * Get coal price impact description
 */
export const getCoalPriceImpactDescription = (trend: CoalPriceTrend): string => {
  const { currentPrice, trend: priceTrend, forecast, impact } = trend;

  let description = '';

  // Price level description
  if (currentPrice > 130) {
    description += 'Harga batu bara sangat tinggi, yang umumnya mendorong aktivitas pertambangan dan permintaan ban. ';
  } else if (currentPrice > 110) {
    description += 'Harga batu bara tinggi, yang mendukung aktivitas pertambangan dan permintaan ban. ';
  } else if (currentPrice > 90) {
    description += 'Harga batu bara moderat, yang cukup mendukung aktivitas pertambangan. ';
  } else if (currentPrice > 70) {
    description += 'Harga batu bara normal, dengan dampak netral pada aktivitas pertambangan. ';
  } else if (currentPrice > 50) {
    description += 'Harga batu bara rendah, yang dapat mengurangi aktivitas pertambangan dan permintaan ban. ';
  } else {
    description += 'Harga batu bara sangat rendah, yang berpotensi menekan aktivitas pertambangan secara signifikan. ';
  }

  // Trend description
  if (priceTrend === 'up') {
    description += 'Tren harga saat ini menunjukkan kenaikan, ';
  } else if (priceTrend === 'down') {
    description += 'Tren harga saat ini menunjukkan penurunan, ';
  } else {
    description += 'Tren harga saat ini relatif stabil, ';
  }

  // Forecast description
  if (forecast === 'increasing') {
    description += 'dengan perkiraan akan terus meningkat dalam jangka menengah. ';
  } else if (forecast === 'decreasing') {
    description += 'dengan perkiraan akan terus menurun dalam jangka menengah. ';
  } else {
    description += 'dengan perkiraan akan tetap stabil dalam jangka menengah. ';
  }

  // Impact description
  if (impact > 7) {
    description += 'Kondisi ini sangat menguntungkan bagi permintaan ban pertambangan.';
  } else if (impact > 3) {
    description += 'Kondisi ini cukup menguntungkan bagi permintaan ban pertambangan.';
  } else if (impact > -3) {
    description += 'Kondisi ini memiliki dampak netral pada permintaan ban pertambangan.';
  } else if (impact > -7) {
    description += 'Kondisi ini cukup merugikan bagi permintaan ban pertambangan.';
  } else {
    description += 'Kondisi ini sangat merugikan bagi permintaan ban pertambangan.';
  }

  return description;
};
