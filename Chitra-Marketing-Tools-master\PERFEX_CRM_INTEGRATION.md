# Perfex CRM Integration

This document explains how to use the Perfex CRM integration in the Customer Management section of the Chitra Marketing Tools application.

## Overview

The Customer Management section now supports fetching customer data directly from your Perfex CRM system. This integration allows you to:

1. View customer data from Perfex CRM
2. Add new customers to Perfex CRM
3. Edit existing customers in Perfex CRM
4. Delete customers from Perfex CRM
5. Export customer data from Perfex CRM to Excel

## Setup

To use the Perfex CRM integration, you need to run a proxy server to handle CORS (Cross-Origin Resource Sharing) issues. This is because the Perfex CRM API does not have CORS headers enabled, which prevents direct access from a web application.

### Running the Proxy Server

1. Open a terminal/command prompt
2. Navigate to the application directory
3. Run the following command:

```
npm run proxy
```

This will start a proxy server on port 3001 that forwards requests to the Perfex CRM API.

### Running the Application with Proxy

To run both the proxy server and the application at the same time, you can use:

```
npm run dev:with-proxy
```

Or for Electron:

```
npm run electron:dev:with-proxy
```

## Using the Integration

1. Open the Customer Management section
2. Click on the "Local Storage" button next to "Customer Management" to toggle to "Perfex CRM" mode
3. The application will attempt to connect to the proxy server and fetch customer data from Perfex CRM
4. If successful, you will see the customer data from Perfex CRM
5. You can now add, edit, delete, and export customers as usual

## Troubleshooting

If you see the error "Could not connect to the proxy server", make sure:

1. The proxy server is running (`npm run proxy`)
2. There are no other applications using port 3001
3. Your network allows connections to the Perfex CRM API (https://gohse.id/crm/api)

If you see "Failed to load customers", check:

1. The Perfex CRM API token is valid
2. The Perfex CRM API is accessible
3. The proxy server logs for any errors

## API Configuration

The Perfex CRM API configuration is located in `src/services/perfexCrmService.ts`. If you need to change the API URL or token, you can edit this file.

Current configuration:
- API URL: https://gohse.id/crm/api
- API Token: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyIjoiQWZpIiwibmFtZSI6ImFmaSIsIkFQSV9USU1FIjoxNzQ2NTM1MzcxfQ.nQtXFvJJgcv5SgId_1EtD7vW3Qf0WCwggTX7vw1tDq4

## Switching Between Data Sources

You can switch between local storage and Perfex CRM at any time by clicking the data source button next to "Customer Management". This allows you to work with local data when you don't have access to the Perfex CRM API.

## Exporting Data

When you export customer data, the export will include the current data source (Local Storage or Perfex CRM) in the filename and in the exported data.
