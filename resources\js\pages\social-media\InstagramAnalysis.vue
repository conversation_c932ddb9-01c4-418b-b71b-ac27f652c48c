<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Instagram Analysis</h1>
                    <p class="mt-2 text-gray-600">Comprehensive Instagram performance analytics and insights</p>
                </div>
                <div class="flex space-x-3">
                    <select v-model="selectedPeriod" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                        <option value="7days">Last 7 Days</option>
                        <option value="30days">Last 30 Days</option>
                        <option value="90days">Last 90 Days</option>
                    </select>
                    <button
                        @click="refreshData"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <RefreshCw class="h-4 w-4 mr-2" />
                        Refresh
                    </button>
                    <button
                        @click="exportReport"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Report
                    </button>
                </div>
            </div>

            <!-- Instagram Metrics Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-pink-100 rounded-lg">
                            <Users class="h-6 w-6 text-pink-600" />
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Followers</p>
                            <p class="text-2xl font-bold text-gray-900">{{ instagramMetrics.followers.toLocaleString() }}</p>
                            <p class="text-sm text-green-600">+{{ instagramMetrics.followerGrowth }}% this period</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-red-100 rounded-lg">
                            <Heart class="h-6 w-6 text-red-600" />
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Engagement Rate</p>
                            <p class="text-2xl font-bold text-gray-900">{{ instagramMetrics.engagementRate }}%</p>
                            <p class="text-sm text-green-600">+{{ instagramMetrics.engagementGrowth }}% vs last period</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <Eye class="h-6 w-6 text-blue-600" />
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Reach</p>
                            <p class="text-2xl font-bold text-gray-900">{{ instagramMetrics.reach.toLocaleString() }}</p>
                            <p class="text-sm text-blue-600">{{ instagramMetrics.reachGrowth }}% increase</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <BarChart3 class="h-6 w-6 text-purple-600" />
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Impressions</p>
                            <p class="text-2xl font-bold text-gray-900">{{ instagramMetrics.impressions.toLocaleString() }}</p>
                            <p class="text-sm text-purple-600">{{ instagramMetrics.impressionGrowth }}% growth</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Engagement Trend -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Engagement Trend</h3>
                    <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                        <div class="text-center">
                            <BarChart3 class="h-12 w-12 text-gray-400 mx-auto mb-2" />
                            <p class="text-gray-600">Engagement Trend Chart</p>
                            <p class="text-sm text-gray-500">Chart.js integration</p>
                        </div>
                    </div>
                </div>

                <!-- Audience Demographics -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Audience Demographics</h3>
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Age Groups</h4>
                            <div class="space-y-2">
                                <div v-for="age in audienceDemographics.age" :key="age.range" class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">{{ age.range }}</span>
                                    <div class="flex items-center">
                                        <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-pink-500 h-2 rounded-full" :style="{ width: age.percentage + '%' }"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">{{ age.percentage }}%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Gender</h4>
                            <div class="space-y-2">
                                <div v-for="gender in audienceDemographics.gender" :key="gender.type" class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">{{ gender.type }}</span>
                                    <span class="text-sm font-medium text-gray-900">{{ gender.percentage }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Performing Posts -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Top Performing Posts</h3>
                    <div class="flex space-x-3">
                        <select v-model="selectedMetric" class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option value="engagement">Engagement</option>
                            <option value="reach">Reach</option>
                            <option value="likes">Likes</option>
                            <option value="comments">Comments</option>
                        </select>
                        <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center text-sm">
                            <Filter class="h-4 w-4 mr-2" />
                            Filter
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div v-for="post in topPosts" :key="post.id" class="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg">
                            <div class="flex-shrink-0">
                                <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <Image class="h-8 w-8 text-gray-400" />
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-medium text-gray-900 truncate">{{ post.caption }}</h4>
                                    <span class="text-sm text-gray-500">{{ post.publishedAt }}</span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">{{ post.type }} • {{ post.hashtags }} hashtags</p>
                                <div class="flex items-center space-x-6 mt-3 text-sm text-gray-500">
                                    <div class="flex items-center">
                                        <Heart class="h-4 w-4 mr-1 text-red-500" />
                                        {{ post.likes.toLocaleString() }}
                                    </div>
                                    <div class="flex items-center">
                                        <MessageSquare class="h-4 w-4 mr-1 text-blue-500" />
                                        {{ post.comments }}
                                    </div>
                                    <div class="flex items-center">
                                        <Share class="h-4 w-4 mr-1 text-green-500" />
                                        {{ post.shares }}
                                    </div>
                                    <div class="flex items-center">
                                        <Eye class="h-4 w-4 mr-1 text-purple-500" />
                                        {{ post.reach.toLocaleString() }}
                                    </div>
                                    <div class="flex items-center">
                                        <TrendingUp class="h-4 w-4 mr-1 text-orange-500" />
                                        {{ post.engagementRate }}%
                                    </div>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <button class="text-blue-600 hover:text-blue-800 text-sm">View Details</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hashtag Performance -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Top Hashtags -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Performing Hashtags</h3>
                    <div class="space-y-3">
                        <div v-for="hashtag in topHashtags" :key="hashtag.tag" class="flex items-center justify-between">
                            <div class="flex items-center">
                                <span class="text-blue-600 font-medium">#{{ hashtag.tag }}</span>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">{{ hashtag.reach.toLocaleString() }}</p>
                                <p class="text-xs text-gray-500">{{ hashtag.posts }} posts</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Optimal Posting Times -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Optimal Posting Times</h3>
                    <div class="space-y-4">
                        <div v-for="day in optimalTimes" :key="day.day" class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-900">{{ day.day }}</span>
                            <div class="text-right">
                                <p class="text-sm text-gray-900">{{ day.bestTime }}</p>
                                <p class="text-xs text-gray-500">{{ day.engagementRate }}% avg engagement</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Insights & Recommendations -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">AI Insights & Recommendations</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Content Insights</h4>
                        <div v-for="insight in contentInsights" :key="insight.id" class="flex items-start p-3 bg-blue-50 rounded-lg">
                            <Lightbulb class="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <p class="text-sm font-medium text-blue-900">{{ insight.title }}</p>
                                <p class="text-sm text-blue-700">{{ insight.description }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Growth Opportunities</h4>
                        <div v-for="opportunity in growthOpportunities" :key="opportunity.id" class="flex items-start p-3 bg-green-50 rounded-lg">
                            <TrendingUp class="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <p class="text-sm font-medium text-green-900">{{ opportunity.title }}</p>
                                <p class="text-sm text-green-700">{{ opportunity.description }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-pink-50 border border-pink-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-pink-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-pink-900">Advanced Instagram Analytics Coming Soon</h3>
                        <p class="text-pink-700 mt-1">
                            Enhanced Instagram analytics features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-pink-700 mt-2 space-y-1">
                            <li>Real-time Instagram API integration</li>
                            <li>Competitor analysis and benchmarking</li>
                            <li>Story and Reel performance analytics</li>
                            <li>Automated content optimization suggestions</li>
                            <li>Influencer collaboration tracking</li>
                            <li>ROI measurement and attribution</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    RefreshCw,
    Download,
    Users,
    Heart,
    Eye,
    BarChart3,
    Image,
    Filter,
    MessageSquare,
    Share,
    TrendingUp,
    Lightbulb,
    Info
} from 'lucide-vue-next';
import { ref, onMounted } from 'vue';

// Reactive state
const selectedPeriod = ref('30days');
const selectedMetric = ref('engagement');

// Instagram metrics
const instagramMetrics = ref({
    followers: 18500,
    followerGrowth: 12.5,
    engagementRate: 5.2,
    engagementGrowth: 8.3,
    reach: 125000,
    reachGrowth: 15.2,
    impressions: 185000,
    impressionGrowth: 18.7
});

// Audience demographics
const audienceDemographics = ref({
    age: [
        { range: '18-24', percentage: 25 },
        { range: '25-34', percentage: 35 },
        { range: '35-44', percentage: 28 },
        { range: '45-54', percentage: 12 }
    ],
    gender: [
        { type: 'Male', percentage: 68 },
        { type: 'Female', percentage: 32 }
    ]
});

// Top performing posts
const topPosts = ref([
    {
        id: 1,
        caption: 'New 27.00 R 49 XD GRIP tire launch - Built for extreme mining conditions',
        type: 'Carousel',
        hashtags: 12,
        publishedAt: '2 days ago',
        likes: 1250,
        comments: 89,
        shares: 45,
        reach: 15600,
        engagementRate: 8.5
    },
    {
        id: 2,
        caption: 'Behind the scenes: Quality testing process at our facility',
        type: 'Video',
        hashtags: 8,
        publishedAt: '5 days ago',
        likes: 890,
        comments: 67,
        shares: 23,
        reach: 12300,
        engagementRate: 7.8
    },
    {
        id: 3,
        caption: 'Customer success story: PT Mining Sejahtera shares their experience',
        type: 'Image',
        hashtags: 10,
        publishedAt: '1 week ago',
        likes: 756,
        comments: 45,
        shares: 18,
        reach: 9800,
        engagementRate: 6.9
    },
    {
        id: 4,
        caption: 'Tire maintenance tips for heavy equipment operators',
        type: 'Carousel',
        hashtags: 15,
        publishedAt: '1 week ago',
        likes: 623,
        comments: 34,
        shares: 28,
        reach: 8900,
        engagementRate: 6.2
    }
]);

// Top hashtags
const topHashtags = ref([
    { tag: 'ChitraTires', reach: 45000, posts: 28 },
    { tag: 'MiningTires', reach: 38000, posts: 15 },
    { tag: 'HeavyEquipment', reach: 32000, posts: 12 },
    { tag: 'QualityTires', reach: 28000, posts: 18 },
    { tag: 'Indonesia', reach: 25000, posts: 22 },
    { tag: 'Mining', reach: 22000, posts: 10 }
]);

// Optimal posting times
const optimalTimes = ref([
    { day: 'Monday', bestTime: '9:00 AM', engagementRate: 5.8 },
    { day: 'Tuesday', bestTime: '10:00 AM', engagementRate: 6.2 },
    { day: 'Wednesday', bestTime: '2:00 PM', engagementRate: 7.1 },
    { day: 'Thursday', bestTime: '11:00 AM', engagementRate: 6.5 },
    { day: 'Friday', bestTime: '3:00 PM', engagementRate: 5.9 },
    { day: 'Saturday', bestTime: '12:00 PM', engagementRate: 4.8 },
    { day: 'Sunday', bestTime: '7:00 PM', engagementRate: 4.2 }
]);

// Content insights
const contentInsights = ref([
    {
        id: 1,
        title: 'Video Content Performs Best',
        description: 'Video posts generate 40% higher engagement than image posts'
    },
    {
        id: 2,
        title: 'Educational Content Resonates',
        description: 'How-to and educational posts receive 25% more saves and shares'
    },
    {
        id: 3,
        title: 'Customer Stories Drive Trust',
        description: 'User-generated content and testimonials boost credibility'
    }
]);

// Growth opportunities
const growthOpportunities = ref([
    {
        id: 1,
        title: 'Increase Story Usage',
        description: 'Stories have 60% higher reach potential - post 3-5 stories daily'
    },
    {
        id: 2,
        title: 'Leverage Reels',
        description: 'Reels get 22% more reach than regular posts - create 2-3 weekly'
    },
    {
        id: 3,
        title: 'Engage with Comments',
        description: 'Responding to comments within 1 hour increases future engagement by 15%'
    }
]);

// Event handlers
const refreshData = () => {
    alert('Refreshing Instagram data...\n\nFetching latest metrics from Instagram API:\n- Follower count and growth\n- Engagement rates\n- Post performance\n- Audience insights');
};

const exportReport = () => {
    try {
        const reportData = {
            period: selectedPeriod.value,
            metrics: instagramMetrics.value,
            topPosts: topPosts.value,
            hashtags: topHashtags.value,
            demographics: audienceDemographics.value,
            insights: contentInsights.value,
            opportunities: growthOpportunities.value,
            generatedAt: new Date().toISOString()
        };

        const csvContent = [
            ['Metric', 'Value', 'Growth'].join(','),
            ['Followers', instagramMetrics.value.followers, `${instagramMetrics.value.followerGrowth}%`].join(','),
            ['Engagement Rate', `${instagramMetrics.value.engagementRate}%`, `${instagramMetrics.value.engagementGrowth}%`].join(','),
            ['Reach', instagramMetrics.value.reach, `${instagramMetrics.value.reachGrowth}%`].join(','),
            ['Impressions', instagramMetrics.value.impressions, `${instagramMetrics.value.impressionGrowth}%`].join(','),
            [],
            ['Top Posts'].join(','),
            ['Caption', 'Likes', 'Comments', 'Engagement Rate'].join(','),
            ...topPosts.value.map(post => [
                `"${post.caption}"`,
                post.likes,
                post.comments,
                `${post.engagementRate}%`
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `instagram_analysis_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Instagram analysis report exported successfully!');
    } catch (error) {
        console.error('Error exporting report:', error);
        alert('Failed to export report.');
    }
};

// Initialize on mount
onMounted(() => {
    // Load Instagram data
});
</script>
