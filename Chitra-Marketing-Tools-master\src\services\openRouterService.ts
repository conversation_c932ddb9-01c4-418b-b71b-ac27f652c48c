/**
 * Service for interacting with the OpenRouter API
 */

// OpenRouter API key
const OPENROUTER_API_KEY = '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';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Models
export const MODELS = {
  DEEPSEEK: 'openai/gpt-4.1-nano',
  CLAUDE_OPUS: 'openai/gpt-4.1-nano',
  CLAUDE_SONNET: 'openai/gpt-4.1-nano',
  CLAUDE_HAIKU: 'openai/gpt-4.1-nano',
  MISTRAL_LARGE: 'openai/gpt-4.1-nano',
  MISTRAL_SMALL: 'openai/gpt-4.1-nano',
  OPENCHAT: 'openai/gpt-4.1-nano',
  GPT35: 'openai/gpt-4.1-nano',
  GPT4: 'openai/gpt-4.1-nano',
};

/**
 * Interface for OpenRouter API request
 */
interface OpenRouterRequest {
  model: string;
  messages: {
    role: 'system' | 'user' | 'assistant';
    content: string;
  }[];
  temperature?: number;
  max_tokens?: number;
}

/**
 * Interface for OpenRouter API response
 */
interface OpenRouterResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

/**
 * Generate a promo name using AI
 */
export async function generatePromoName(
  promoType: string,
  products: string[],
  region: string,
  startDate: string,
  endDate: string
): Promise<string> {
  try {
    const prompt = `
    Buatkan SATU nama promo marketing yang paling menarik dan catchy untuk promo penjualan ban.

    Detail promo:
    - Tipe promo: ${promoType}
    - Produk: ${products.join(', ')}
    - Wilayah: ${region || 'Nasional'}
    - Periode: ${new Date(startDate).toLocaleDateString('id-ID')} - ${new Date(endDate).toLocaleDateString('id-ID')}

    Nama promo harus:
    1. Singkat (maksimal 5 kata)
    2. Menarik dan mudah diingat
    3. Mencerminkan value proposition dari promo tersebut
    4. Relevan dengan produk ban dan tipe promo
    5. Menggunakan kata-kata yang kuat dan persuasif

    PENTING: Berikan HANYA SATU nama promo terbaik saja tanpa penjelasan tambahan, tanpa tanda kutip, dan tanpa nomor atau bullet point.
    `;

    // Use GPT-3.5 for more reliable results
    const response = await callOpenRouter(MODELS.GPT35, prompt, 0.5, 30);
    // Clean up the response to ensure we only get the name
    return response.trim()
      .replace(/^["']|["']$/g, '') // Remove quotes if present
      .replace(/^\d+\.\s*/, '')    // Remove numbering if present
      .replace(/^[-•*]\s*/, '')    // Remove bullet points if present
      .trim();
  } catch (error) {
    console.error('Error generating promo name:', error);
    return 'Promo Ban Special';
  }
}

/**
 * Generate a marketing email for a promotion
 */
export async function generateMarketingEmail(
  promoName: string,
  promoType: string,
  products: string[],
  discount: string,
  normalPrice: string,
  promoPrice: string,
  region: string,
  startDate: string,
  endDate: string,
  customerName?: string,
  customerCompany?: string
): Promise<string> {
  try {
    const prompt = `
    Buatkan email marketing profesional untuk penawaran promo ban yang sesuai dengan nama promo "${promoName}" dan tipe promo "${promoType}".

    Detail promo:
    - Nama promo: ${promoName}
    - Tipe promo: ${promoType}
    - Produk: ${products.join(', ')}
    - Diskon: ${discount}
    - Harga normal: ${normalPrice}
    - Harga promo: ${promoPrice}
    - Wilayah: ${region || 'Nasional'}
    - Periode: ${new Date(startDate).toLocaleDateString('id-ID')} - ${new Date(endDate).toLocaleDateString('id-ID')}
    ${customerName ? `- Customer: ${customerName}` : ''}
    ${customerCompany ? `- Perusahaan: ${customerCompany}` : ''}

    PENTING: Email harus sangat sesuai dengan nama promo "${promoName}" dan tipe promo "${promoType}". Gunakan tema, tone, dan messaging yang konsisten dengan nama promo tersebut.

    Email harus profesional, menarik, dan persuasif. Gunakan bahasa yang formal namun tetap ramah.

    Sertakan:
    1. Subject email yang menarik dan sesuai dengan nama promo
    2. Salam pembuka (personalisasi jika ada nama customer)
    3. Pengenalan singkat tentang promo yang menekankan nama promo dan value proposition
    4. Detail penawaran dengan fokus pada produk ban yang ditawarkan
    5. Penjelasan tentang diskon atau benefit yang didapat
    6. Call to action yang kuat
    7. Informasi kontak untuk follow-up
    8. Salam penutup

    Format email dalam HTML sederhana dengan:
    - <h2> untuk subject email
    - <p> untuk paragraf
    - <strong> untuk penekanan
    - <ul> dan <li> untuk daftar
    - <div style="text-align: center;"> untuk bagian yang perlu dicentrakan

    Pastikan email terlihat profesional dan menarik, dengan fokus pada nama promo "${promoName}" dan produk ban yang ditawarkan.
    `;

    // Try with GPT-3.5 first as it's more reliable for this task
    const response = await callOpenRouter(MODELS.GPT35, prompt, 0.7, 1500);
    return response;
  } catch (error) {
    console.error('Error generating marketing email:', error);
    return 'Gagal membuat email marketing. Silakan coba lagi nanti.';
  }
}

/**
 * Call the OpenRouter API
 */
async function callOpenRouter(
  model: string,
  prompt: string,
  temperature: number = 0.7,
  maxTokens: number = 500
): Promise<string> {
  try {
    console.log(`Calling OpenRouter API with model: ${model}`);

    // Create the request payload
    const request: OpenRouterRequest = {
      model,
      messages: [
        {
          role: 'system',
          content: 'Anda adalah asisten marketing profesional yang ahli dalam membuat konten promosi untuk produk ban. Berikan respons yang singkat, padat, dan profesional.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature,
      max_tokens: maxTokens
    };

    console.log('Request payload:', JSON.stringify(request, null, 2));

    // Make the API call
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools'
      },
      body: JSON.stringify(request)
    });

    console.log(`Response status: ${response.status}`);

    // Handle error responses
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Error Response:', errorText);

      // Return a fallback response instead of throwing
      if (prompt.includes('email marketing')) {
        return `<h2>Penawaran Spesial Ban Premium</h2>
        <p>Yth. Bapak/Ibu,</p>
        <p>Kami dengan senang hati ingin menawarkan <strong>Promo Spesial</strong> untuk produk ban premium kami.</p>
        <p>Dapatkan diskon menarik untuk pembelian ban berkualitas tinggi yang akan meningkatkan performa kendaraan Anda.</p>
        <p>Silakan hubungi tim sales kami untuk informasi lebih lanjut.</p>
        <p>Hormat kami,<br>Tim Marketing<br>PT Chitra Paratama</p>`;
      } else {
        return "Promo Ban Special"; // Fallback for promo name
      }
    }

    // Parse the successful response
    const data = await response.json() as OpenRouterResponse;
    console.log('API Response data:', data);

    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      console.error('Invalid response format:', data);
      throw new Error('Invalid response format from OpenRouter API');
    }

    return data.choices[0].message.content;
  } catch (error) {
    console.error('Error calling OpenRouter API:', error);

    // Return fallback content instead of throwing
    if (prompt.includes('email marketing')) {
      return `<h2>Penawaran Spesial Ban Premium</h2>
      <p>Yth. Bapak/Ibu,</p>
      <p>Kami dengan senang hati ingin menawarkan <strong>Promo Spesial</strong> untuk produk ban premium kami.</p>
      <p>Dapatkan diskon menarik untuk pembelian ban berkualitas tinggi yang akan meningkatkan performa kendaraan Anda.</p>
      <p>Silakan hubungi tim sales kami untuk informasi lebih lanjut.</p>
      <p>Hormat kami,<br>Tim Marketing<br>PT Chitra Paratama</p>`;
    } else {
      return "Promo Ban Special"; // Fallback for promo name
    }
  }
}
