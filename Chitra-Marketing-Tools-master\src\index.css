@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
}

/* Iframe container styles */
.iframe-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 0.375rem;
}

.iframe-container iframe {
  width: 100%;
  border: none;
}

/* Sidebar transition styles */
.sidebar-transition {
  transition: width 0.3s ease-in-out;
}

.content-transition {
  transition: margin-left 0.3s ease-in-out;
}

/* Mobile sidebar styles */
@media (max-width: 768px) {
  .sidebar-mobile {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .sidebar-mobile.open {
    transform: translateX(0);
  }
}

/* Prevent navigation for search inputs */
.no-navigation {
  pointer-events: auto !important;
}

.no-navigation input {
  pointer-events: auto !important;
}

.no-navigation button {
  pointer-events: auto !important;
}

/* Specific style for search input */
.search-input {
  pointer-events: auto !important;
}

/* Additional styles to prevent form submission */
form.no-navigation,
form .no-navigation {
  pointer-events: auto !important;
}

form.no-navigation input[type="text"],
form.no-navigation input[type="search"],
form .no-navigation input[type="text"],
form .no-navigation input[type="search"] {
  pointer-events: auto !important;
}

/* Prevent default form submission */
form.no-navigation button[type="submit"],
form .no-navigation button[type="submit"] {
  display: none;
}

/* ContentEditable placeholder */
[contenteditable=true]:empty:before {
  content: attr(data-placeholder);
  color: #9ca3af;
  pointer-events: none;
}
