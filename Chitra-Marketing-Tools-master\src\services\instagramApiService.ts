/**
 * Instagram API Service
 *
 * This service provides functions for fetching real Instagram data using the Instagram Graph API.
 * It requires a valid Instagram access token with appropriate permissions.
 */

// Define fallback types in case the import fails
interface InstagramMediaContainerFallback {
  id: string;
  media_type: string;
  media_url: string;
  permalink: string;
  thumbnail_url?: string;
  timestamp: string;
  caption?: string;
  username: string;
  children?: {
    data: {
      id: string;
      media_type: string;
      media_url: string;
      thumbnail_url?: string;
    }[];
  };
}

interface InstagramAPIResponseFallback<T> {
  data?: T;
  error?: {
    code: number;
    message: string;
    type: string;
    fbtrace_id: string;
  };
}

// Try to import the types, use fallbacks if it fails
let InstagramMediaContainer: any;
let InstagramAPIResponse: any;

try {
  const types = require('../types/socialMediaEnhanced');
  InstagramMediaContainer = types.InstagramMediaContainer;
  InstagramAPIResponse = types.InstagramAPIResponse;
  console.log('Successfully imported Instagram API types');
} catch (error) {
  console.error('Error importing Instagram API types, using fallbacks:', error);
  InstagramMediaContainer = InstagramMediaContainerFallback;
  InstagramAPIResponse = InstagramAPIResponseFallback;
}

// Instagram Graph API base URL
const INSTAGRAM_API_BASE_URL = 'https://graph.instagram.com/v18.0';

// Instagram API configuration
// Note: In a production environment, these should be stored securely
// and not hardcoded in the source code
const INSTAGRAM_ACCESS_TOKEN = process.env.INSTAGRAM_ACCESS_TOKEN || '';

/**
 * Fetch Instagram user profile and recent posts
 * @param username Instagram username (not used directly, but kept for UI consistency)
 * @returns JSON string with Instagram data
 */
export const fetchInstagramData = async (username: string): Promise<string> => {
  try {
    console.log(`Fetching real Instagram data for user: ${username}`);

    // First, get the user's Instagram Business Account ID
    // Note: This requires a valid Instagram Business Account connected to a Facebook Page
    // and a valid access token with appropriate permissions

    // For this implementation, we'll use the Instagram Graph API to fetch:
    // 1. Basic profile information
    // 2. Recent media (posts)

    // Fetch user profile information
    const profileResponse = await fetch(`${INSTAGRAM_API_BASE_URL}/me?fields=id,username,media_count,account_type&access_token=${INSTAGRAM_ACCESS_TOKEN}`);

    if (!profileResponse.ok) {
      throw new Error(`Instagram API error: ${profileResponse.status} ${profileResponse.statusText}`);
    }

    const profileData = await profileResponse.json();

    // Fetch user's media (recent posts)
    const mediaResponse = await fetch(`${INSTAGRAM_API_BASE_URL}/me/media?fields=id,caption,media_type,media_url,permalink,thumbnail_url,timestamp,username,children{id,media_type,media_url,thumbnail_url}&limit=30&access_token=${INSTAGRAM_ACCESS_TOKEN}`);

    if (!mediaResponse.ok) {
      throw new Error(`Instagram API error: ${mediaResponse.status} ${mediaResponse.statusText}`);
    }

    const mediaData = await mediaResponse.json();

    // Fetch engagement metrics for each post
    // Note: This requires additional permissions and a higher rate limit
    const posts = await Promise.all(mediaData.data.map(async (post: InstagramMediaContainer) => {
      try {
        const insightsResponse = await fetch(`${INSTAGRAM_API_BASE_URL}/${post.id}/insights?metric=engagement,impressions,reach,saved&access_token=${INSTAGRAM_ACCESS_TOKEN}`);

        if (!insightsResponse.ok) {
          // If insights fail, return post without insights
          return {
            ...post,
            likes: 0,
            comments: 0,
            shares: 0,
            saves: 0
          };
        }

        const insightsData = await insightsResponse.json();

        // Extract metrics from insights data
        const engagement = insightsData.data.find((item: any) => item.name === 'engagement')?.values[0]?.value || 0;
        const impressions = insightsData.data.find((item: any) => item.name === 'impressions')?.values[0]?.value || 0;
        const reach = insightsData.data.find((item: any) => item.name === 'reach')?.values[0]?.value || 0;
        const saves = insightsData.data.find((item: any) => item.name === 'saved')?.values[0]?.value || 0;

        // Calculate likes and comments based on engagement
        // This is an approximation since Instagram API doesn't directly provide these metrics
        const likes = Math.floor(engagement * 0.7);
        const comments = Math.floor(engagement * 0.2);
        const shares = Math.floor(engagement * 0.1);

        return {
          ...post,
          likes,
          comments,
          shares,
          saves,
          impressions,
          reach
        };
      } catch (error) {
        console.error(`Error fetching insights for post ${post.id}:`, error);
        // Return post without insights if there's an error
        return {
          ...post,
          likes: 0,
          comments: 0,
          shares: 0,
          saves: 0
        };
      }
    }));

    // Fetch follower count
    const followersResponse = await fetch(`${INSTAGRAM_API_BASE_URL}/${profileData.id}/insights?metric=follower_count&period=lifetime&access_token=${INSTAGRAM_ACCESS_TOKEN}`);

    let followers = 0;
    if (followersResponse.ok) {
      const followersData = await followersResponse.json();
      followers = followersData.data[0]?.values[0]?.value || 0;
    }

    // Create the final data object
    const instagramData = {
      username: profileData.username,
      followers,
      following: 0, // Instagram API doesn't provide this information
      posts,
      isRealData: true // Flag to indicate this is real data
    };

    // Convert to JSON string
    return JSON.stringify(instagramData, null, 2);
  } catch (error) {
    console.error('Error fetching Instagram data:', error);
    throw error;
  }
};

/**
 * Fallback function to generate dummy Instagram data
 * This is used when the real API connection fails or for testing
 * @param username Instagram username
 * @returns JSON string with dummy Instagram data
 */
export const generateDummyInstagramData = (username: string): string => {
  // Generate random follower and following counts
  const followers = Math.floor(Math.random() * 5000) + 1000;
  const following = Math.floor(Math.random() * 1000) + 200;

  // Generate 30 dummy posts
  const posts = Array.from({ length: 30 }, (_, i) => {
    // Post types
    const postTypes = ['IMAGE', 'CAROUSEL_ALBUM', 'VIDEO', 'REELS'];
    const selectedType = postTypes[Math.floor(Math.random() * postTypes.length)];

    // Random dates for the last 6 months (newest first)
    const date = new Date();
    date.setDate(date.getDate() - (i * 3)); // Every 3 days
    const timestamp = date.toISOString();

    // Random engagement metrics
    const likes = Math.floor(Math.random() * 500) + 50;
    const comments = Math.floor(Math.random() * 50) + 5;
    const shares = Math.floor(Math.random() * 30) + 2;
    const saves = Math.floor(Math.random() * 40) + 3;

    // Random hashtags
    const hashtags = ['#tires', '#mining', '#heavyduty', '#equipment', '#safety', '#quality', '#performance'];
    const selectedHashtags = [];
    const hashtagCount = Math.floor(Math.random() * 5) + 1;
    for (let j = 0; j < hashtagCount; j++) {
      const randomIndex = Math.floor(Math.random() * hashtags.length);
      selectedHashtags.push(hashtags[randomIndex]);
    }

    return {
      id: `dummy_${i}`,
      media_type: selectedType,
      media_url: 'https://via.placeholder.com/1080',
      permalink: `https://instagram.com/p/dummy_${i}`,
      thumbnail_url: selectedType === 'VIDEO' ? 'https://via.placeholder.com/1080' : undefined,
      timestamp,
      caption: `This is a sample post about tires and mining equipment. ${selectedHashtags.join(' ')}`,
      username,
      likes,
      comments,
      shares,
      saves,
      impressions: likes + comments + shares + saves + Math.floor(Math.random() * 1000),
      reach: Math.floor(Math.random() * 2000) + 500
    };
  });

  // Create the final data object
  const instagramData = {
    username,
    followers,
    following,
    posts,
    isRealData: false // Flag to indicate this is dummy data
  };

  // Convert to JSON string
  return JSON.stringify(instagramData, null, 2);
};
