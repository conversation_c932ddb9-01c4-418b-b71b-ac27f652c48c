const express = require('express');
const { google } = require('googleapis');
const bodyParser = require('body-parser');
const cors = require('cors');
const app = express();
app.use(cors());
app.use(bodyParser.json());

// Ganti path ini dengan path file service account Anda
const SERVICE_ACCOUNT_FILE = './service-account.json';
const SCOPES = [
  'https://www.googleapis.com/auth/documents',
  'https://www.googleapis.com/auth/drive'
];

const auth = new google.auth.GoogleAuth({
  keyFile: SERVICE_ACCOUNT_FILE,
  scopes: SCOPES,
});

app.post('/api/create-proposal', async (req, res) => {
  try {
    const { title, content } = req.body;
    const client = await auth.getClient();
    const docs = google.docs({ version: 'v1', auth: client });

    // 1. Buat dokumen baru
    const createRes = await docs.documents.create({
      requestBody: { title },
    });
    const documentId = createRes.data.documentId;

    // 2. Tambahkan konten ke dokumen
    await docs.documents.batchUpdate({
      documentId,
      requestBody: {
        requests: [
          {
            insertText: {
              location: { index: 1 },
              text: content,
            },
          },
        ],
      },
    });

    // 3. Share dokumen ke email user
    const drive = google.drive({ version: 'v3', auth: client });
    await drive.permissions.create({
      fileId: documentId,
      requestBody: {
        type: 'user',
        role: 'writer', // atau 'reader' jika hanya ingin bisa melihat
        emailAddress: '<EMAIL>', // GANTI jika ingin share ke email lain
      },
    });

    res.json({ url: `https://docs.google.com/document/d/${documentId}/edit` });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Gagal membuat dokumen' });
  }
});

app.listen(3001, () => {
  console.log('Backend berjalan di http://localhost:3001');
}); 