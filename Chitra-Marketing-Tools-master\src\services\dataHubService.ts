/**
 * Data Hub Service
 * 
 * This service acts as a central hub for all data in the application.
 * It provides a unified interface for accessing and manipulating data
 * from various sources (localStorage, Firebase, Supabase, APIs, etc.)
 * and ensures data consistency across the application.
 */

import { Product } from '../types';
import { Customer } from '../types/customer';
import { FleetData } from './fleetDataService';
import { FleetlistItem } from './fleetlistService';
import { SocialMediaPost, SocialMediaKnowledgeEntry } from '../types/socialMedia';
import { CoalPrice, CoalPriceTrend } from './coalPriceService';
import { Holiday } from './holidayService';
import { KnowledgeEntry } from '../types/knowledgeBase';
import { CustomerPricing } from '../types/customerPricing';
import { PromoSimulation } from '../types/promotion';
import { MarketingInsight } from '../pages/MarketingInsightsHub';

// Import services
import * as productService from './productService';
import * as customerService from './customerService';
import * as fleetDataService from './fleetDataService';
import * as fleetlistService from './fleetlistService';
import * as socialMediaService from './socialMediaService';
import * as coalPriceService from './coalPriceService';
import * as holidayService from './holidayService';
import * as customerPricingService from './customerPricingService';
import * as promoLibraryService from './promoLibraryService';
import * as fleetRevenueService from './fleetRevenueService';

// Cache configuration
const CACHE_EXPIRY = {
  SHORT: 5 * 60 * 1000, // 5 minutes
  MEDIUM: 30 * 60 * 1000, // 30 minutes
  LONG: 24 * 60 * 60 * 1000, // 24 hours
};

// Data cache
interface DataCache<T> {
  data: T[] | null;
  lastFetched: number;
  expiry: number;
}

// Main cache object
const cache: {
  products: DataCache<Product>;
  customers: DataCache<Customer>;
  fleetData: DataCache<FleetData>;
  fleetlist: DataCache<FleetlistItem>;
  socialMediaPosts: DataCache<SocialMediaPost>;
  socialMediaKnowledge: DataCache<SocialMediaKnowledgeEntry>;
  coalPrices: DataCache<CoalPrice>;
  holidays: DataCache<Holiday>;
  customerPricing: DataCache<CustomerPricing>;
  promoSimulations: DataCache<PromoSimulation>;
  marketingInsights: DataCache<MarketingInsight>;
} = {
  products: { data: null, lastFetched: 0, expiry: CACHE_EXPIRY.MEDIUM },
  customers: { data: null, lastFetched: 0, expiry: CACHE_EXPIRY.MEDIUM },
  fleetData: { data: null, lastFetched: 0, expiry: CACHE_EXPIRY.MEDIUM },
  fleetlist: { data: null, lastFetched: 0, expiry: CACHE_EXPIRY.MEDIUM },
  socialMediaPosts: { data: null, lastFetched: 0, expiry: CACHE_EXPIRY.SHORT },
  socialMediaKnowledge: { data: null, lastFetched: 0, expiry: CACHE_EXPIRY.MEDIUM },
  coalPrices: { data: null, lastFetched: 0, expiry: CACHE_EXPIRY.LONG },
  holidays: { data: null, lastFetched: 0, expiry: CACHE_EXPIRY.LONG },
  customerPricing: { data: null, lastFetched: 0, expiry: CACHE_EXPIRY.MEDIUM },
  promoSimulations: { data: null, lastFetched: 0, expiry: CACHE_EXPIRY.MEDIUM },
  marketingInsights: { data: null, lastFetched: 0, expiry: CACHE_EXPIRY.MEDIUM },
};

// Event names for data changes
export enum DataChangeEvent {
  PRODUCTS = 'products-changed',
  CUSTOMERS = 'customers-changed',
  FLEET_DATA = 'fleet-data-changed',
  FLEETLIST = 'fleetlist-changed',
  SOCIAL_MEDIA_POSTS = 'social-media-posts-changed',
  SOCIAL_MEDIA_KNOWLEDGE = 'social-media-knowledge-changed',
  COAL_PRICES = 'coal-prices-changed',
  HOLIDAYS = 'holidays-changed',
  CUSTOMER_PRICING = 'customer-pricing-changed',
  PROMO_SIMULATIONS = 'promo-simulations-changed',
  MARKETING_INSIGHTS = 'marketing-insights-changed',
}

/**
 * Dispatch a data change event
 */
export function dispatchDataChangeEvent(eventType: DataChangeEvent, data?: any): void {
  const event = new CustomEvent(eventType, { detail: data });
  window.dispatchEvent(event);
  console.log(`DataHub: Dispatched ${eventType} event`);
}

/**
 * Subscribe to data change events
 */
export function subscribeToDataChanges(
  eventType: DataChangeEvent,
  callback: (data?: any) => void
): () => void {
  const handler = (event: Event) => {
    const customEvent = event as CustomEvent;
    callback(customEvent.detail);
  };

  window.addEventListener(eventType, handler);
  
  // Return unsubscribe function
  return () => window.removeEventListener(eventType, handler);
}

/**
 * Check if cache is valid
 */
function isCacheValid<T>(cache: DataCache<T>): boolean {
  if (!cache.data) return false;
  return Date.now() - cache.lastFetched < cache.expiry;
}

/**
 * Update cache with new data
 */
function updateCache<T>(cacheObj: DataCache<T>, data: T[]): void {
  cacheObj.data = data;
  cacheObj.lastFetched = Date.now();
}

/**
 * Clear specific cache
 */
export function clearCache(cacheType: keyof typeof cache): void {
  cache[cacheType].data = null;
  cache[cacheType].lastFetched = 0;
  console.log(`DataHub: Cleared ${cacheType} cache`);
}

/**
 * Clear all caches
 */
export function clearAllCaches(): void {
  Object.keys(cache).forEach(key => {
    const cacheKey = key as keyof typeof cache;
    cache[cacheKey].data = null;
    cache[cacheKey].lastFetched = 0;
  });
  console.log('DataHub: Cleared all caches');
}

// Products
export async function getProducts(forceRefresh = false): Promise<Product[]> {
  if (!forceRefresh && isCacheValid(cache.products)) {
    console.log('DataHub: Using cached products data');
    return cache.products.data!;
  }

  console.log('DataHub: Fetching products data');
  const products = await productService.fetchProducts(forceRefresh);
  updateCache(cache.products, products);
  return products;
}

export async function saveProduct(product: Product): Promise<boolean> {
  const result = await productService.updateProduct(product);
  if (result) {
    // Update cache with the new product
    if (cache.products.data) {
      cache.products.data = cache.products.data.map(p => 
        p.id === product.id ? product : p
      );
    }
    dispatchDataChangeEvent(DataChangeEvent.PRODUCTS, { product, action: 'update' });
  }
  return result;
}

export async function saveProducts(products: Product[]): Promise<boolean> {
  const result = await productService.saveProducts(products);
  if (result) {
    updateCache(cache.products, products);
    dispatchDataChangeEvent(DataChangeEvent.PRODUCTS, { products, action: 'save-all' });
  }
  return result;
}

export async function deleteProduct(productId: string): Promise<boolean> {
  const result = await productService.deleteProduct(productId);
  if (result && cache.products.data) {
    cache.products.data = cache.products.data.filter(p => p.id !== productId);
    dispatchDataChangeEvent(DataChangeEvent.PRODUCTS, { productId, action: 'delete' });
  }
  return result;
}

// Customers
export function getCustomers(forceRefresh = false): Customer[] {
  if (!forceRefresh && isCacheValid(cache.customers)) {
    console.log('DataHub: Using cached customers data');
    return cache.customers.data!;
  }

  console.log('DataHub: Fetching customers data');
  const customers = customerService.getAllCustomers();
  updateCache(cache.customers, customers);
  return customers;
}

export function getCustomerById(id: string): Customer | undefined {
  // Try cache first
  if (cache.customers.data) {
    const cachedCustomer = cache.customers.data.find(c => c.id === id);
    if (cachedCustomer) return cachedCustomer;
  }
  
  // Fallback to service
  return customerService.getCustomerById(id);
}

export function createCustomer(customerData: any): Customer {
  const newCustomer = customerService.createCustomer(customerData);
  
  // Update cache
  if (cache.customers.data) {
    cache.customers.data.push(newCustomer);
  }
  
  dispatchDataChangeEvent(DataChangeEvent.CUSTOMERS, { customer: newCustomer, action: 'create' });
  return newCustomer;
}

export function updateCustomer(id: string, customerData: any): Customer | null {
  const updatedCustomer = customerService.updateCustomer(id, customerData);
  
  // Update cache
  if (updatedCustomer && cache.customers.data) {
    cache.customers.data = cache.customers.data.map(c => 
      c.id === id ? updatedCustomer : c
    );
  }
  
  if (updatedCustomer) {
    dispatchDataChangeEvent(DataChangeEvent.CUSTOMERS, { customer: updatedCustomer, action: 'update' });
  }
  
  return updatedCustomer;
}

export function deleteCustomer(id: string): boolean {
  const result = customerService.deleteCustomer(id);
  
  // Update cache
  if (result && cache.customers.data) {
    cache.customers.data = cache.customers.data.filter(c => c.id !== id);
  }
  
  if (result) {
    dispatchDataChangeEvent(DataChangeEvent.CUSTOMERS, { customerId: id, action: 'delete' });
  }
  
  return result;
}

export function searchCustomers(query: string): Customer[] {
  return customerService.searchCustomers(query);
}

// Fleet Data
export async function getFleetData(forceRefresh = false): Promise<FleetData[]> {
  if (!forceRefresh && isCacheValid(cache.fleetData)) {
    console.log('DataHub: Using cached fleet data');
    return cache.fleetData.data!;
  }

  console.log('DataHub: Fetching fleet data');
  const fleetData = await fleetDataService.fetchFleetData();
  updateCache(cache.fleetData, fleetData);
  return fleetData;
}

// Coal Prices
export async function getCoalPrices(forceRefresh = false): Promise<CoalPrice[]> {
  if (!forceRefresh && isCacheValid(cache.coalPrices)) {
    console.log('DataHub: Using cached coal prices data');
    return cache.coalPrices.data!;
  }

  console.log('DataHub: Fetching coal prices data');
  const coalPrices = await coalPriceService.fetchCoalPrices();
  updateCache(cache.coalPrices, coalPrices);
  return coalPrices;
}

export async function getCoalPriceTrend(): Promise<CoalPriceTrend> {
  return coalPriceService.analyzeCoalPriceTrend();
}

// Holidays
export async function getHolidays(forceRefresh = false): Promise<Holiday[]> {
  if (!forceRefresh && isCacheValid(cache.holidays)) {
    console.log('DataHub: Using cached holidays data');
    return cache.holidays.data!;
  }

  console.log('DataHub: Fetching holidays data');
  const holidays = await holidayService.getAllHolidays();
  updateCache(cache.holidays, holidays);
  return holidays;
}

// Initialize the Data Hub
export function initializeDataHub(): void {
  console.log('DataHub: Initializing...');
  
  // Set up event listeners for cross-tab communication
  window.addEventListener('storage', (event) => {
    if (event.key === 'bundleBoostProducts') {
      clearCache('products');
      dispatchDataChangeEvent(DataChangeEvent.PRODUCTS);
    }
  });
  
  // Listen for product updates from other services
  window.addEventListener('productDataUpdated', () => {
    clearCache('products');
    dispatchDataChangeEvent(DataChangeEvent.PRODUCTS);
  });
  
  console.log('DataHub: Initialization complete');
}

// Export default for easy importing
export default {
  getProducts,
  saveProduct,
  saveProducts,
  deleteProduct,
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  searchCustomers,
  getFleetData,
  getCoalPrices,
  getCoalPriceTrend,
  getHolidays,
  clearCache,
  clearAllCaches,
  subscribeToDataChanges,
  dispatchDataChangeEvent,
  initializeDataHub,
};
