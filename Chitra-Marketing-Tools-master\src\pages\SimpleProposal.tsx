import React, { useState, useRef } from 'react';
import { FileText, Upload, Download, Check } from 'lucide-react';

const SimpleProposal: React.FC = () => {
  // State untuk jenis proposal yang dipilih
  const [proposalType, setProposalType] = useState<string>('bundling');
  
  // State untuk file template
  const [templateFile, setTemplateFile] = useState<File | null>(null);
  
  // State untuk data form
  const [customerName, setCustomerName] = useState<string>('');
  const [productName, setProductName] = useState<string>('');
  
  // State untuk status generasi proposal
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  
  // Ref untuk input file
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Handler untuk perubahan jenis proposal
  const handleProposalTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setProposalType(e.target.value);
  };
  
  // Handler untuk upload file template
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setTemplateFile(e.target.files[0]);
    }
  };
  
  // Handler untuk submit form
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsGenerating(true);
    
    // Simulasi proses generasi proposal
    setTimeout(() => {
      setIsGenerating(false);
      setIsSuccess(true);
      
      // Reset status sukses setelah 3 detik
      setTimeout(() => {
        setIsSuccess(false);
      }, 3000);
    }, 1500);
  };
  
  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold flex items-center">
          <FileText className="mr-2 text-blue-600" />
          Simple Proposal Generator
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold mb-4">Pilih Jenis Proposal</h2>
          <select
            value={proposalType}
            onChange={handleProposalTypeChange}
            className="w-full md:w-1/3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="bundling">Proposal Bundling</option>
            <option value="consignment">Proposal Konsinyasi</option>
            <option value="trade-in">Proposal Trade-In</option>
          </select>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold mb-4">Upload Template</h2>
          <p className="text-gray-600 mb-4">
            Upload file DOCX yang berisi tag seperti {{CustomerName}}, {{ProductName}}, dsb.
          </p>
          
          <div className="flex items-center space-x-3">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileUpload}
              accept=".docx"
              className="hidden"
            />
            
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 flex items-center"
            >
              <Upload className="mr-2 h-4 w-4" />
              Pilih File
            </button>
            
            {templateFile && (
              <span className="text-sm text-gray-600">
                {templateFile.name}
              </span>
            )}
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold mb-4">Data Proposal</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-1">
                Nama Pelanggan <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="customerName"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div>
              <label htmlFor="productName" className="block text-sm font-medium text-gray-700 mb-1">
                Nama Produk <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="productName"
                value={productName}
                onChange={(e) => setProductName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
          </div>
          
          {/* Form khusus untuk jenis proposal tertentu */}
          {proposalType === 'bundling' && (
            <div className="mt-4 p-4 bg-blue-50 rounded-md">
              <h3 className="font-medium text-blue-800 mb-2">Informasi Bundling</h3>
              <p className="text-sm text-blue-700">
                Form ini untuk proposal bundling. Anda dapat menambahkan field khusus bundling di sini.
              </p>
            </div>
          )}
          
          {proposalType === 'consignment' && (
            <div className="mt-4 p-4 bg-green-50 rounded-md">
              <h3 className="font-medium text-green-800 mb-2">Informasi Konsinyasi</h3>
              <p className="text-sm text-green-700">
                Form ini untuk proposal konsinyasi. Anda dapat menambahkan field khusus konsinyasi di sini.
              </p>
            </div>
          )}
          
          {proposalType === 'trade-in' && (
            <div className="mt-4 p-4 bg-amber-50 rounded-md">
              <h3 className="font-medium text-amber-800 mb-2">Informasi Trade-In</h3>
              <p className="text-sm text-amber-700">
                Form ini untuk proposal trade-in. Anda dapat menambahkan field khusus trade-in di sini.
              </p>
            </div>
          )}
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isGenerating || !templateFile || !customerName || !productName}
            className={`px-6 py-3 rounded-md flex items-center ${
              isGenerating || !templateFile || !customerName || !productName
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-white mr-2" />
                Generating...
              </>
            ) : isSuccess ? (
              <>
                <Check className="mr-2 h-4 w-4" />
                Berhasil!
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Generate Proposal
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default SimpleProposal;
