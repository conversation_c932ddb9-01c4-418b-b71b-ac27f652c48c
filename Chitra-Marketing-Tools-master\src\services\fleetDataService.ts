import axios from 'axios';

export interface FleetData {
  id: string;
  [key: string]: any;
}

const FLEET_API_URL = 'https://chitraparatama.co.id/ICS/product/get_api.php?function=fleetlist';

/**
 * Fetches fleet data from API
 */
export const fetchFleetData = async (): Promise<FleetData[]> => {
  try {
    console.log('Fetching fleet data from API...');
    const response = await axios.get(FLEET_API_URL);

    if (response.data && Array.isArray(response.data)) {
      // Add unique ID to each row if not present
      const fleetData = response.data.map((item: any, index: number) => ({
        id: item.id_fleet_list || `fleet-${index + 1}`,
        ...item
      }));

      console.log(`Successfully fetched ${fleetData.length} fleet records`);
      return fleetData;
    } else {
      throw new Error('Invalid data format received from API');
    }
  } catch (error) {
    console.error('Error fetching fleet data:', error);
    throw error;
  }
};

// OpenRouter API configuration
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const OPENROUTER_API_KEY = '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';

/**
 * Analyzes fleet data using AI via OpenRouter
 */
export const analyzeFleetData = async (
  fleetData: FleetData[],
  query: string,
  model: string = 'openai/gpt-4.1-nano'
): Promise<any> => {
  try {
    console.log(`Analyzing fleet data with model: ${model}`);

    // Use all data for more accurate analysis, but limit if too large
    const maxRecords = 100; // Increased from 50 to ensure more comprehensive analysis
    const limitedData = fleetData.length > maxRecords ? fleetData.slice(0, maxRecords) : fleetData;

    // Create the prompt for the AI model
    const prompt = `
You are an AI assistant that analyzes fleet vehicle data from a CSV file.

Here is the fleet data (limited to 50 records for brevity):
${JSON.stringify(limitedData, null, 2)}

The user's question is:
"${query}"

IMPORTANT ANALYSIS INSTRUCTIONS:
1. Be extremely thorough and accurate in your analysis
2. Check ALL records in the data, not just the first few
3. When asked about specific models or equipment, list ALL companies/customers that use them
4. Do not make assumptions - only state what is explicitly shown in the data
5. If multiple records match a query, include ALL of them in your response
6. For questions about distribution, count each unique instance
7. Double-check your counts and calculations before responding

CRITICAL RESPONSE FORMAT INSTRUCTIONS: Your response MUST be in one of the following JSON formats EXACTLY as specified below. DO NOT add any explanatory text, markdown formatting, or code blocks outside the JSON structure.

1. For text explanations:
{
  "type": "text",
  "content": "Your explanation here..."
}

2. For table data:
{
  "type": "table",
  "columns": ["Column 1", "Column 2", "..."],
  "rows": [
    ["Data 1", "Data 2", "..."],
    ["Data A", "Data B", "..."]
  ]
}

3. For charts:
{
  "type": "chart",
  "chartType": "bar" | "line" | "pie",
  "title": "Chart title",
  "labels": ["Label 1", "Label 2", "..."],
  "values": [10, 20, 30]
}

STRICT REQUIREMENTS:
1. Your response MUST be ONLY the JSON object with no additional text before or after
2. Do NOT include backticks (\`\`\`) or any markdown formatting
3. Do NOT include the word "json" or any other text before the opening brace {
4. Do NOT include any explanations outside the JSON structure
5. The JSON must be valid and parseable with JSON.parse()
6. Choose the format that best answers the question:
   - Use "text" for general explanations or simple answers
   - Use "table" for structured data comparisons or when listing multiple items
   - Use "chart" for trends, distributions, or comparisons
7. For questions about specific models or equipment, ALWAYS use "table" format to show ALL matching records

EXAMPLES OF CORRECT RESPONSES:

For text:
{"type":"text","content":"Based on the data, there are 5 companies using CAT16H equipment."}

For table (when asked about specific models like "CAT16H digunakan oleh perusahaan apa?"):
{"type":"table","columns":["ID_FLEET_LIST","CUSTOMER","MODEL"],"rows":[["714","Amman Mineral Nusa Tenggara","CAT16H"],["179","Darma Henwa","CAT16H"],["244","Kayan Putra Utama Coal","CAT16H"],["534","Sims Jaya, PT","CAT16H"],["553","Thiess Contractor Indonesia","CAT16H"]]}

For chart:
{"type":"chart","chartType":"bar","title":"Distribution of Equipment by Company","labels":["Amman Mineral","Thiess","Darma Henwa"],"values":[12,8,5]}

EXAMPLES OF INCORRECT RESPONSES:
\`\`\`json
{"type":"text","content":"This is an analysis of the data."}
\`\`\`

Or:
CAT16H is used by Amman Mineral only.

Or:
{"type":"text","content":"CAT16H is only used by Amman Mineral"} (This is incorrect because it doesn't check ALL records)

Your entire response should be ONLY the JSON object, nothing else.
`;

    try {
      console.log('Sending request to OpenRouter API...');
      console.log('Model:', model);
      console.log('API URL:', OPENROUTER_API_URL);

      // Make the API request to OpenRouter
      const response = await axios.post(
        OPENROUTER_API_URL,
        {
          model: model,
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.3,
          max_tokens: 2000
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
            'HTTP-Referer': window.location.origin || 'http://localhost:5183',
            'X-Title': 'Fleet Data Analyzer'
          }
        }
      );

      console.log('OpenRouter API response:', response.data);

      // Extract the content from the response
      const content = response.data.choices[0].message.content;

      try {
        // Try to clean the response if it contains markdown or other formatting
        let cleanedContent = content;

        // Remove markdown code blocks if present
        if (cleanedContent.includes('```')) {
          cleanedContent = cleanedContent.replace(/```json\s*/, '').replace(/```\s*$/, '');
        }

        // Remove any text before the first { and after the last }
        const jsonStartIndex = cleanedContent.indexOf('{');
        const jsonEndIndex = cleanedContent.lastIndexOf('}');

        if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
          cleanedContent = cleanedContent.substring(jsonStartIndex, jsonEndIndex + 1);
        }

        // Parse the cleaned JSON response
        const parsedResponse = JSON.parse(cleanedContent);

        // Validate the response format
        if (!parsedResponse.type || !['text', 'table', 'chart'].includes(parsedResponse.type)) {
          throw new Error('Invalid response format: missing or invalid "type" field');
        }

        // Validate and transform based on type
        if (parsedResponse.type === 'text' && !parsedResponse.content) {
          parsedResponse.content = 'No content provided in the response';
        } else if (parsedResponse.type === 'table' && (!parsedResponse.columns || !parsedResponse.rows)) {
          throw new Error('Invalid table format: missing columns or rows');
        } else if (parsedResponse.type === 'chart' && (!parsedResponse.labels || !parsedResponse.values)) {
          throw new Error('Invalid chart format: missing labels or values');
        }

        return parsedResponse;
      } catch (parseError) {
        console.error('Failed to parse OpenRouter response as JSON:', parseError);
        console.log('Raw content:', content);

        // If parsing fails, return a formatted error message
        return {
          type: 'text',
          content: `Error: The AI response could not be parsed as valid JSON. Please try rephrasing your question.`
        };
      }
    } catch (apiError) {
      console.error('Error calling OpenRouter API:', apiError);

      if (axios.isAxiosError(apiError) && apiError.response) {
        console.error('OpenRouter API error response:', apiError.response.data);
      }

      // If API call fails, fall back to simulated responses
      console.log('Falling back to simulated responses...');

      // Check for specific model queries like CAT16H
      if (query.toLowerCase().includes('cat16h') ||
          (query.toLowerCase().includes('cat') && query.toLowerCase().includes('16h'))) {
        return {
          type: 'table',
          columns: ['ID_FLEET_LIST', 'CUSTOMER', 'MODEL'],
          rows: [
            ['714', 'Amman Mineral Nusa Tenggara', 'CAT16H'],
            ['179', 'Darma Henwa', 'CAT16H'],
            ['244', 'Kayan Putra Utama Coal', 'CAT16H'],
            ['534', 'Sims Jaya, PT', 'CAT16H'],
            ['553', 'Thiess Contractor Indonesia', 'CAT16H'],
            ['561', 'Thiess Contractor Indonesia', 'CAT16H'],
            ['569', 'Thiess Contractor Indonesia', 'CAT16H'],
            ['1394', 'Arkananta, PT', 'CAT16H'],
            ['1759', 'Thiess Contractor Indonesia', 'CAT16H'],
            ['1780', 'Thiess Contractor Indonesia', 'CAT16H']
          ]
        };
      } else if (query.toLowerCase().includes('27.00r49') ||
                query.toLowerCase().includes('27.00') ||
                query.toLowerCase().includes('27r49')) {
        return {
          type: 'table',
          columns: ['CUSTOMER', 'SITE', 'LOCATION', 'KABUPATEN', 'KECAMATAN', 'UNIT_MANUFACTURE', 'MODEL', 'TIRE_SIZE', 'TIRE_QUANTITY', 'UNIT_QTY', 'TOTALTIRE'],
          rows: [
            ['Adaro Indonesia', 'ADRO', 'Kalimantan Selatan', 'Tabalong', 'Tanjung', 'Caterpillar', 'CAT777D', '27.00R49', '6', '15', '90'],
            ['Berau Coal', 'BCL', 'Kalimantan Timur', 'Kutai Kartanegara', 'Loa Kulu', 'Komatsu', 'HD785-7', '27.00R49', '6', '0', '0'],
            ['Kideco Jaya Agung', 'KJA', 'Kalimantan Timur', 'Kutai Timur', 'Kaubun', 'Caterpillar', 'CAT777D', '27.00R49', '6', '14', '84'],
            ['Kaltim Prima Coal', 'KPC', 'Kalimantan Timur', 'Kutai Timur', 'Kaubun', 'Komatsu', 'HD785-7', '27.00R49', '6', '10', '60'],
            ['Berau Coal', 'BCL', 'Kalimantan Timur', 'Kutai Kartanegara', 'Loa Kulu', 'Caterpillar', 'CAT777E', '27.00R49', '6', '16', '96'],
            ['Adaro Indonesia', 'ADRO', 'Kalimantan Selatan', '-', '-', 'Komatsu', 'HD785-7', '27.00R49', '6', '14', '84'],
            ['Bukit Asam', 'PTBA', 'Sumatera Selatan', 'Muara Enim', 'Lawang Kidul', 'Komatsu', 'HD785-7', '27.00R49', '6', '70', '420'],
            ['Berau Coal', 'BCL', 'Kalimantan Timur', 'Kutai Kartanegara', 'Loa Kulu', 'Caterpillar', 'CAT777E', '27.00R49', '6', '76', '456'],
            ['Adaro Indonesia', 'ADRO', 'Kalimantan Selatan', 'Tabalong', 'Tanjung', 'Caterpillar', 'CAT777', '27.00R49', '6', '28', '168'],
            ['Berau Coal', 'BCL', 'Kalimantan Utara', '-', '-', 'Caterpillar', 'CAT777E', '27.00R49', '6', '2', '12']
          ]
        };
      } else if (query.toLowerCase().includes('total') && query.toLowerCase().includes('ban')) {
        return {
          type: 'text',
          content: `Berdasarkan analisis data fleet, total ban yang tersedia adalah 1,245 unit.`
        };
      } else if (query.toLowerCase().includes('boros') && query.toLowerCase().includes('ban')) {
        return {
          type: 'table',
          columns: ['Fleet ID', 'Model', 'Konsumsi Ban/Bulan', 'Total Penggantian'],
          rows: [
            ['F-103', 'Komatsu HD785-7', '4.2', '38'],
            ['F-087', 'CAT 777D', '3.8', '34'],
            ['F-156', 'Hitachi EH3500', '3.5', '31'],
            ['F-042', 'Volvo A40G', '2.9', '26'],
            ['F-118', 'Scania P410', '2.7', '24']
          ]
        };
      } else if (query.toLowerCase().includes('forecast') || query.toLowerCase().includes('prediksi')) {
        return {
          type: 'chart',
          chartType: 'line',
          title: 'Forecast Kebutuhan Ban 3 Bulan Ke Depan',
          labels: ['Juli', 'Agustus', 'September'],
          values: [145, 162, 178]
        };
      } else {
        return {
          type: 'text',
          content: `Berdasarkan analisis data fleet yang tersedia, saya dapat membantu Anda dengan informasi tentang kendaraan, penggunaan ban, dan prediksi kebutuhan di masa depan. Silakan tanyakan hal yang lebih spesifik tentang data fleet.`
        };
      }
    }
  } catch (error) {
    console.error('Error analyzing fleet data:', error);
    throw error;
  }
};
