import {
  WhatsAppChatAnalysisRequest,
  WhatsAppChatAnalysisResponse,
  WhatsAppChatEvaluation,
  WhatsAppChatSession,
  WhatsAppMessage
} from '../types/whatsappChat';

// OpenRouter API key - using the existing key from the project
const OPENROUTER_API_KEY = '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';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Models
const MODELS = {
  GPT4: 'openai/gpt-4',
  CLAUDE_OPUS: 'anthropic/claude-3-opus',
  CLAUDE_SONNET: 'anthropic/claude-3-sonnet',
  DEEPSEEK: 'deepseek-ai/deepseek-coder-33b-instruct'
};

// Default model for chat analysis
const DEFAULT_MODEL = MODELS.CLAUDE_SONNET;

// Local storage key for WhatsApp chat sessions
const WHATSAPP_CHAT_STORAGE_KEY = 'chitraMarketingTools_whatsappChats';

/**
 * Parse WhatsApp chat export text
 * Format: [DD/MM/YY, HH:MM:SS] Sender: Message
 */
export const parseWhatsAppChat = (
  chatText: string,
  salesName: string,
  customerName: string
): WhatsAppMessage[] => {
  const lines = chatText.split('\n');
  const messages: WhatsAppMessage[] = [];
  const messagePattern = /\[(\d{1,2}\/\d{1,2}\/\d{2,4}),\s*(\d{1,2}:\d{2}(?::\d{2})?)\]\s*([^:]+):\s*(.*)/;

  lines.forEach((line, index) => {
    // Skip empty lines
    if (!line.trim()) return;

    // Try to match the pattern
    const match = line.match(messagePattern);
    if (match) {
      const [, datePart, timePart, sender, content] = match;

      // Parse date and time
      const [day, month, year] = datePart.split('/').map(Number);
      const [hour, minute] = timePart.split(':').map(Number);

      // Create a date object (assuming 20xx for 2-digit years)
      const fullYear = year < 100 ? 2000 + year : year;
      const timestamp = new Date(fullYear, month - 1, day, hour, minute);

      // Create message object
      const message: WhatsAppMessage = {
        id: `msg-${index}`,
        sender: sender.trim(),
        content,
        timestamp,
        isSales: sender.trim().toLowerCase().includes(salesName.toLowerCase())
      };

      messages.push(message);
    } else {
      // If this line doesn't match the pattern, it might be a continuation of the previous message
      if (messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        lastMessage.content += '\n' + line;
      }
    }
  });

  return messages;
};

/**
 * Create a new WhatsApp chat session
 */
export const createWhatsAppChatSession = (
  customerName: string,
  salesName: string,
  messages: WhatsAppMessage[]
): WhatsAppChatSession => {
  return {
    id: `chat-${Date.now()}`,
    importedAt: new Date(),
    customerName,
    salesName,
    messages,
    status: 'imported'
  };
};

/**
 * Save WhatsApp chat session to local storage
 */
export const saveWhatsAppChatSession = (session: WhatsAppChatSession): void => {
  const sessions = loadWhatsAppChatSessions();
  const updatedSessions = [...sessions, session];
  localStorage.setItem(WHATSAPP_CHAT_STORAGE_KEY, JSON.stringify(updatedSessions));
};

/**
 * Load WhatsApp chat sessions from local storage
 */
export const loadWhatsAppChatSessions = (): WhatsAppChatSession[] => {
  const sessionsJson = localStorage.getItem(WHATSAPP_CHAT_STORAGE_KEY);
  if (!sessionsJson) return [];

  try {
    const sessions = JSON.parse(sessionsJson) as WhatsAppChatSession[];

    // Convert string dates back to Date objects
    return sessions.map(session => ({
      ...session,
      importedAt: new Date(session.importedAt),
      analyzedAt: session.analyzedAt ? new Date(session.analyzedAt) : undefined,
      messages: session.messages.map(msg => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }))
    }));
  } catch (error) {
    console.error('Error parsing WhatsApp chat sessions:', error);
    return [];
  }
};

/**
 * Analyze WhatsApp chat
 */
export const analyzeWhatsAppChat = async (
  session: WhatsAppChatSession
): Promise<WhatsAppChatAnalysisResponse> => {
  // Prepare analysis request
  const analysisRequest: WhatsAppChatAnalysisRequest = {
    messages: session.messages,
    customerName: session.customerName,
    salesName: session.salesName,
    isEvaluation: true
  };

  // Get AI analysis
  const analysisResponse = await callAI(analysisRequest);

  // Update session with evaluation
  if (analysisResponse.evaluation) {
    session.evaluation = analysisResponse.evaluation;
    session.status = 'analyzed';
    session.analyzedAt = new Date();

    // Save updated session
    const sessions = loadWhatsAppChatSessions();
    const updatedSessions = sessions.map(s =>
      s.id === session.id ? session : s
    );
    localStorage.setItem(WHATSAPP_CHAT_STORAGE_KEY, JSON.stringify(updatedSessions));
  }

  return analysisResponse;
};

/**
 * Call AI for WhatsApp chat analysis
 */
const callAI = async (
  request: WhatsAppChatAnalysisRequest
): Promise<WhatsAppChatAnalysisResponse> => {
  try {
    console.log('Calling AI for WhatsApp chat analysis');

    // Prepare system prompt
    let systemPrompt = `Bertindaklah Seperti Ahli Negosiasi kelas dunia untuk menganalisa dan memberi masukan kepada tim sales, dimana kamu sudah dilatih dalam negosiasi sandera FBI dan kesepakatan bisnis bernilai tinggi, Tugasmu adalah mengembangkan strategi negosiasi untuk hasil chat whatsapp dan memberikan evalusi saran dan cara yang matang.`;

    // Prepare user prompt
    let userPrompt = `Analisis percakapan WhatsApp berikut antara sales (${request.salesName}) dan pelanggan (${request.customerName}):\n\n`;

    // Add messages to the prompt
    request.messages.forEach(msg => {
      const formattedTime = msg.timestamp.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit'
      });
      userPrompt += `[${formattedTime}] ${msg.sender}: ${msg.content}\n`;
    });

    // If this is an evaluation request, add evaluation instructions
    if (request.isEvaluation) {
      userPrompt += `\nBerikan evaluasi mendetail tentang percakapan ini. Fokus pada:
1. Apakah closing berhasil dicapai
2. Efektivitas closing (0-100)
3. Waktu respons rata-rata dan penilaiannya
4. Gaya komunikasi (kejelasan, profesionalisme, persuasif)
5. Penanganan keberatan
6. Proposisi nilai
7. Skor keseluruhan (0-100)
8. Kekuatan dalam percakapan
9. Area yang perlu ditingkatkan
10. Wawasan kunci
11. Strategi alternatif yang bisa digunakan

Berikan respons dalam format JSON dengan struktur berikut:
{
  "closingAchieved": boolean,
  "closingEffectiveness": number,
  "responseTime": {
    "average": number,
    "rating": "excellent"|"good"|"average"|"poor"
  },
  "communicationStyle": {
    "clarity": number,
    "professionalism": number,
    "persuasiveness": number
  },
  "objectionHandling": {
    "effectiveness": number,
    "missedOpportunities": [string]
  },
  "valueProposition": {
    "clarity": number,
    "relevance": number
  },
  "overallScore": number,
  "strengths": [string],
  "improvements": [string],
  "keyInsights": string,
  "alternativeStrategies": [string]
}`;
    }

    // Make the API call to OpenRouter
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - WhatsApp Chat Analysis'
      },
      body: JSON.stringify({
        model: DEFAULT_MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 1500
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('AI response:', data);

    // Extract the content from the response
    const content = data.choices[0].message.content;

    if (request.isEvaluation) {
      try {
        // Try to parse the JSON evaluation
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        const jsonString = jsonMatch ? jsonMatch[0] : content;
        const evaluation = JSON.parse(jsonString);

        return {
          message: 'Evaluation completed',
          evaluation
        };
      } catch (error) {
        console.error('Error parsing evaluation JSON:', error);
        return {
          message: content,
          evaluation: undefined
        };
      }
    }

    return {
      message: content
    };
  } catch (error) {
    console.error('Error calling AI service:', error);

    // Return a fallback response
    if (request.isEvaluation) {
      return {
        message: 'Tidak dapat menganalisis percakapan karena kesalahan teknis.',
        evaluation: {
          closingAchieved: false,
          closingEffectiveness: 0,
          responseTime: {
            average: 0,
            rating: 'poor'
          },
          communicationStyle: {
            clarity: 0,
            professionalism: 0,
            persuasiveness: 0
          },
          objectionHandling: {
            effectiveness: 0,
            missedOpportunities: ['Tidak dapat menganalisis karena kesalahan teknis']
          },
          valueProposition: {
            clarity: 0,
            relevance: 0
          },
          overallScore: 0,
          strengths: [],
          improvements: ['Coba lagi ketika layanan tersedia'],
          keyInsights: 'Terjadi kesalahan teknis selama analisis',
          alternativeStrategies: ['Coba lagi nanti']
        }
      };
    }

    return {
      message: 'Tidak dapat menganalisis percakapan karena kesalahan teknis.'
    };
  }
};
