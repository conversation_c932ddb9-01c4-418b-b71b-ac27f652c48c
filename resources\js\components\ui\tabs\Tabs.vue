<script setup lang="ts">
import { cn } from '@/lib/utils';
import { provide, ref } from 'vue';

const props = defineProps({
  defaultValue: {
    type: String,
    default: undefined,
  },
  value: {
    type: String,
    default: undefined,
  },
});

const emit = defineEmits(['update:value']);

const selectedValue = ref(props.value || props.defaultValue);

const onValueChange = (value: string) => {
  selectedValue.value = value;
  emit('update:value', value);
};

provide('tabs', {
  selectedValue,
  onValueChange,
});
</script>

<template>
  <div>
    <slot></slot>
  </div>
</template>