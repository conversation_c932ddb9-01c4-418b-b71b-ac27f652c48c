import React, { useState, useEffect } from 'react';
import { Customer, CustomerFormData } from '../types';
import {
  getAllCustomers,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  searchCustomers,
  importCustomersFromFleetlist
} from '../services/customerService';
import { fetchClientsFromPerfexCrm, createClientInPerfexCrm, updateClientInPerfexCrm, deleteClientInPerfexCrm } from '../services/perfexCrmService';
import CustomerForm from './CustomerForm';
import FieldMappingModal from './FieldMappingModal';
import { Search, Plus, Edit, Trash2, X, Upload, Download, RefreshCw, Database, Truck } from 'lucide-react';
import * as XLSX from 'xlsx';

export default function CustomerList() {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [dataSource, setDataSource] = useState<'local' | 'perfex'>('local');

  // Field mapping for import
  const [fieldMapping, setFieldMapping] = useState<Record<string, string>>({
    name: 'Name',
    email: 'Email',
    phone: 'Phone',
    address: 'Address',
    company: 'Company',
    notes: 'Notes'
  });

  // Field mapping modal state
  const [showMappingModal, setShowMappingModal] = useState(false);
  const [availableFields, setAvailableFields] = useState<string[]>([]);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [sampleData, setSampleData] = useState<Record<string, any> | null>(null);

  // Load customers on component mount
  useEffect(() => {
    loadCustomers();
  }, [dataSource]);

  // Load customers from service
  const loadCustomers = async () => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      if (dataSource === 'perfex') {
        // Load from Perfex CRM
        const perfexCustomers = await fetchClientsFromPerfexCrm();
        setCustomers(perfexCustomers);
      } else {
        // Load from local storage
        const allCustomers = getAllCustomers();
        setCustomers(allCustomers);
      }
    } catch (error) {
      console.error('Error loading customers:', error);
      setErrorMessage('Failed to load customers. Please try again.');
      // Fallback to local data if Perfex CRM fails
      if (dataSource === 'perfex') {
        const allCustomers = getAllCustomers();
        setCustomers(allCustomers);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    const results = searchCustomers(query);
    setCustomers(results);
  };

  // Open form for creating a new customer
  const handleAddNew = () => {
    setSelectedCustomer(null);
    setIsFormOpen(true);
  };

  // Open form for editing a customer
  const handleEdit = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsFormOpen(true);
  };

  // Open delete confirmation modal
  const handleDeleteClick = (customer: Customer) => {
    setCustomerToDelete(customer);
    setIsDeleteModalOpen(true);
  };

  // Confirm and execute customer deletion
  const confirmDelete = async () => {
    if (customerToDelete) {
      setIsLoading(true);
      setErrorMessage(null);

      try {
        if (dataSource === 'perfex') {
          // Delete from Perfex CRM
          await deleteClientInPerfexCrm(customerToDelete.id);
        } else {
          // Delete from local storage
          deleteCustomer(customerToDelete.id);
        }

        await loadCustomers();
        setIsDeleteModalOpen(false);
        setCustomerToDelete(null);
      } catch (error) {
        console.error('Error deleting customer:', error);
        setErrorMessage('Failed to delete customer. Please try again.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Cancel deletion
  const cancelDelete = () => {
    setIsDeleteModalOpen(false);
    setCustomerToDelete(null);
  };

  // Handle form submission (create or update)
  const handleFormSubmit = async (formData: CustomerFormData) => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      if (dataSource === 'perfex') {
        if (selectedCustomer) {
          // Update existing customer in Perfex CRM
          await updateClientInPerfexCrm(selectedCustomer.id, formData);
        } else {
          // Create new customer in Perfex CRM
          await createClientInPerfexCrm(formData);
        }
      } else {
        if (selectedCustomer) {
          // Update existing customer in local storage
          updateCustomer(selectedCustomer.id, formData);
        } else {
          // Create new customer in local storage
          createCustomer(formData);
        }
      }

      // Refresh customer list and close form
      await loadCustomers();
      setIsFormOpen(false);
      setSelectedCustomer(null);
    } catch (error) {
      console.error('Error saving customer:', error);
      setErrorMessage('Failed to save customer. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle data source between local storage and Perfex CRM
  const toggleDataSource = async () => {
    const newDataSource = dataSource === 'local' ? 'perfex' : 'local';
    setDataSource(newDataSource);

    // If switching to Perfex CRM, check if the proxy server is running
    if (newDataSource === 'perfex') {
      setIsLoading(true);
      setErrorMessage(null);

      try {
        // Try to fetch from the proxy server
        const response = await fetch('http://localhost:3001/test');
        if (!response.ok) {
          throw new Error('Proxy server is not responding properly');
        }

        // If successful, load customers from Perfex CRM
        await loadCustomers();
      } catch (error) {
        console.error('Error connecting to proxy server:', error);
        setErrorMessage(
          'Could not connect to the proxy server. Please make sure it is running by executing "npm run proxy" in a terminal, then try again.'
        );
        // Fallback to local data
        setDataSource('local');
        const allCustomers = getAllCustomers();
        setCustomers(allCustomers);
      } finally {
        setIsLoading(false);
      }
    } else {
      // If switching to local, just load the local customers
      loadCustomers();
    }
  };

  // Cancel form
  const handleFormCancel = () => {
    setIsFormOpen(false);
    setSelectedCustomer(null);
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle export customers to Excel
  const handleExportCustomers = () => {
    try {
      // Convert customers to a format suitable for Excel
      const customersForExport = customers.map(customer => ({
        Name: customer.name,
        Email: customer.email,
        Phone: customer.phone,
        Address: customer.address,
        Company: customer.company || '',
        Notes: customer.notes || '',
        'Date Added': formatDate(customer.createdAt),
        'Source': dataSource === 'perfex' ? 'Perfex CRM' : 'Local Storage',
        'ID': customer.id
      }));

      // Create a worksheet
      const worksheet = XLSX.utils.json_to_sheet(customersForExport);

      // Create a workbook
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Customers');

      // Generate Excel file
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

      // Create a Blob from the buffer
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      // Create a download link and trigger the download
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Customers_${dataSource === 'perfex' ? 'PerfexCRM' : 'Local'}_${new Date().toISOString().split('T')[0]}.xlsx`;
      link.click();

      // Clean up
      URL.revokeObjectURL(url);

      // Show success message
      setErrorMessage(null);
    } catch (error) {
      console.error('Error exporting customers:', error);
      setErrorMessage('Failed to export customers. Please try again.');
    }
  };

  // Handle file selection for import
  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    setImportFile(file);
    setIsLoading(true);
    setErrorMessage(null);

    try {
      // Read the Excel file
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data);

      // Get the first worksheet
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];

      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      if (jsonData.length === 0) {
        throw new Error('No data found in the imported file');
      }

      console.log('Imported data sample:', jsonData[0]);

      // Get all possible field names from the first row
      const fields = Object.keys(jsonData[0]);
      setAvailableFields(fields);
      setSampleData(jsonData[0] as Record<string, any>);

      // Try to auto-map fields
      const newMapping: Record<string, string> = {};

      // Define field alternatives for auto-mapping
      const alternatives: Record<string, string[]> = {
        name: ['Name', 'FullName', 'CustomerName', 'Client', 'ClientName', 'nama', 'customer_name', 'full_name', 'customer'],
        email: ['Email', 'EmailAddress', 'E-mail', 'Mail', 'email_address', 'email_id', 'email'],
        phone: ['Phone', 'PhoneNumber', 'Telephone', 'Tel', 'Mobile', 'Contact', 'phone_number', 'mobile_number', 'telepon', 'hp', 'handphone'],
        address: ['Address', 'FullAddress', 'Location', 'alamat', 'full_address', 'address_line'],
        company: ['Company', 'CompanyName', 'Organization', 'Business', 'perusahaan', 'company_name', 'organization_name'],
        notes: ['Notes', 'Comments', 'Description', 'Additional', 'catatan', 'keterangan', 'description', 'comment']
      };

      // Auto-map fields based on name similarity
      Object.keys(alternatives).forEach(fieldType => {
        // First try exact match (case-insensitive)
        const exactMatch = fields.find(f =>
          alternatives[fieldType].some(alt => alt.toLowerCase() === f.toLowerCase())
        );

        if (exactMatch) {
          newMapping[fieldType] = exactMatch;
        } else {
          // Try partial match
          const partialMatch = fields.find(f =>
            alternatives[fieldType].some(alt => f.toLowerCase().includes(alt.toLowerCase()))
          );

          if (partialMatch) {
            newMapping[fieldType] = partialMatch;
          }
        }
      });

      // Update field mapping with auto-mapped fields
      setFieldMapping(prev => ({
        ...prev,
        ...newMapping
      }));

      // Show the mapping modal
      setShowMappingModal(true);
    } catch (error) {
      console.error('Error reading import file:', error);
      setErrorMessage(`Failed to read import file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setImportFile(null);

      // Reset the file input
      e.target.value = '';
    } finally {
      setIsLoading(false);
    }
  };

  // Process import after field mapping is confirmed
  const processImport = async () => {
    if (!importFile) {
      setErrorMessage('No file selected for import');
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      // Read the Excel file
      const data = await importFile.arrayBuffer();
      const workbook = XLSX.read(data);

      // Get the first worksheet
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];

      // Convert to JSON
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      // Process the data
      let importedCount = 0;
      let updatedCount = 0;
      let skippedCount = 0;
      let errorRows: number[] = [];

      // Function to get value using field mapping
      const getFieldValue = (row: any, fieldType: string): string => {
        // Get the mapped field name
        const mappedField = fieldMapping[fieldType];

        if (mappedField && row[mappedField] !== undefined) {
          return String(row[mappedField] || '');
        }

        return '';
      };

      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i];

        // Get values using the field mapping
        const name = getFieldValue(row, 'name');
        const email = getFieldValue(row, 'email');
        const phone = getFieldValue(row, 'phone');
        const address = getFieldValue(row, 'address');
        const company = getFieldValue(row, 'company');
        const notes = getFieldValue(row, 'notes');

        // Validate required fields
        if (!name) {
          console.warn(`Row ${i + 1}: Missing name`);
          skippedCount++;
          errorRows.push(i + 1);
          continue;
        }

        // Create customer data object
        const customerData: CustomerFormData = {
          name,
          email: email || `${name.replace(/\s+/g, '.').toLowerCase()}@example.com`, // Generate email if missing
          phone: phone || '',
          address: address || '',
          company: company || '',
          notes: notes || ''
        };

        // Check if customer with this email or name already exists
        const existingCustomerByEmail = email ? customers.find(c => c.email.toLowerCase() === email.toLowerCase()) : null;
        const existingCustomerByName = customers.find(c => c.name.toLowerCase() === name.toLowerCase());
        const existingCustomer = existingCustomerByEmail || existingCustomerByName;

        if (existingCustomer) {
          // Update existing customer
          updateCustomer(existingCustomer.id, customerData);
          updatedCount++;
        } else {
          // Create new customer
          createCustomer(customerData);
          importedCount++;
        }
      }

      // Refresh customer list
      await loadCustomers();

      // Show detailed success message
      const message = `
        Import completed:
        - ${importedCount} new customers added
        - ${updatedCount} existing customers updated
        - ${skippedCount} rows skipped due to missing required data
        ${errorRows.length > 0 ? `\nRows with errors: ${errorRows.join(', ')}` : ''}
      `;

      setErrorMessage(null);
      alert(message);

      // Reset import state
      setImportFile(null);
      setSampleData(null);

    } catch (error) {
      console.error('Error importing customers:', error);
      setErrorMessage(`Failed to import customers: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle import customers from Excel
  const handleImportCustomers = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e);
  };

  // Handle import customers from Fleetlist
  const handleImportFromFleetlist = async () => {
    setIsLoading(true);
    setErrorMessage(null);

    try {
      const result = await importCustomersFromFleetlist();

      // Refresh customer list
      await loadCustomers();

      // Show success message
      const message = `
        Import from Fleetlist completed:
        - ${result.added} new customers added
        - ${result.skipped} existing customers skipped
        - ${result.customerNames.length} total customer names found
      `;

      alert(message);
    } catch (error) {
      console.error('Error importing customers from Fleetlist:', error);
      setErrorMessage('Failed to import customers from Fleetlist. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <h1 className="text-2xl font-semibold">Customer Management</h1>
          <div className="ml-4 flex items-center">
            <span className="text-sm text-gray-500 mr-2">Data Source:</span>
            <button
              onClick={toggleDataSource}
              className={`inline-flex items-center px-3 py-1 border rounded-md text-sm font-medium ${
                dataSource === 'perfex'
                  ? 'bg-purple-100 text-purple-700 border-purple-300'
                  : 'bg-gray-100 text-gray-700 border-gray-300'
              }`}
            >
              <Database size={16} className="mr-1" />
              {dataSource === 'perfex' ? 'Perfex CRM' : 'Local Storage'}
            </button>
            <button
              onClick={loadCustomers}
              className="ml-2 inline-flex items-center p-1 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100"
              title="Refresh Data"
            >
              <RefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
            </button>
          </div>
        </div>

        <div className="flex space-x-2">
          {/* Import Button - Only show for local storage */}
          {dataSource === 'local' && (
            <>
              <label className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 cursor-pointer">
                <Upload className="mr-2 h-4 w-4" />
                Import Customers
                <input
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  onChange={handleImportCustomers}
                  className="hidden"
                />
              </label>

              {/* Import from Fleetlist Button */}
              <button
                onClick={handleImportFromFleetlist}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
                disabled={isLoading}
              >
                <Truck className="mr-2 h-4 w-4" />
                Import from Fleetlist
              </button>
            </>
          )}

          {/* Export Button */}
          <button
            onClick={handleExportCustomers}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            disabled={customers.length === 0}
          >
            <Download className="mr-2 h-4 w-4" />
            Export Customers
          </button>

          {/* Add New Button */}
          <button
            onClick={handleAddNew}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add New Customer
          </button>
        </div>
      </div>

      {/* Error message */}
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">
          {errorMessage}
        </div>
      )}

      {/* Search bar */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search customers..."
          value={searchQuery}
          onChange={handleSearch}
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      {/* Customer form */}
      {isFormOpen && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">
              {selectedCustomer ? 'Edit Customer' : 'Add New Customer'}
            </h2>
            <button
              onClick={handleFormCancel}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          <CustomerForm
            customer={selectedCustomer || undefined}
            onSubmit={handleFormSubmit}
            onCancel={handleFormCancel}
          />
        </div>
      )}

      {/* Customer table */}
      <div className="bg-white shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Address
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date Added
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {isLoading ? (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center">
                  <div className="flex justify-center items-center text-blue-600">
                    <RefreshCw className="animate-spin mr-2 h-5 w-5" />
                    <span>Loading customers...</span>
                  </div>
                </td>
              </tr>
            ) : customers.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                  {searchQuery
                    ? 'No customers found matching your search.'
                    : dataSource === 'perfex'
                      ? 'No customers found in Perfex CRM.'
                      : 'No customers added yet.'}
                </td>
              </tr>
            ) : (
              customers.map((customer) => (
                <tr key={customer.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                    <div className="text-sm text-gray-500">{customer.company}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{customer.email}</div>
                    <div className="text-sm text-gray-500">{customer.phone}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{customer.address}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(customer.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleEdit(customer)}
                      className="text-blue-600 hover:text-blue-900 mr-4"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteClick(customer)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Delete confirmation modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
            <p className="text-sm text-gray-500 mb-4">
              Are you sure you want to delete <span className="font-medium">{customerToDelete?.name}</span>?
              This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelDelete}
                className="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Field Mapping Modal */}
      <FieldMappingModal
        isOpen={showMappingModal}
        onClose={() => setShowMappingModal(false)}
        availableFields={availableFields}
        initialMapping={fieldMapping}
        onSave={(mapping) => {
          setFieldMapping(mapping);
          setShowMappingModal(false);
          processImport();
        }}
        sampleData={sampleData || undefined}
      />
    </div>
  );
}
