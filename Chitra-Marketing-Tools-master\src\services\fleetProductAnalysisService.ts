import { FleetData } from './fleetDataService';
import { Product } from '../types';
import { fetchProducts } from './productService';
import { MODELS } from './openRouterService';

// OpenRouter API configuration
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const OPENROUTER_API_KEY = '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';

// Default model for analysis
const DEFAULT_MODEL = MODELS.CLAUDE_OPUS; // Using GPT-4.1-nano for better analysis

interface MatchedProduct {
  tireSize: string;
  products: Product[];
}

interface PromotionRecommendation {
  title: string;
  description: string;
  targetCustomers?: string[];
  targetLocations?: string[];
}

interface AIAnalysisResult {
  matchedProducts: MatchedProduct[];
  promotionRecommendations: PromotionRecommendation[];
  analysisText: string;
}

/**
 * Analyzes fleet data and matches with products to provide recommendations
 */
export const analyzeFleetAndProducts = async (
  fleetData: FleetData[],
  tireSizeFilter: string[] = []
): Promise<AIAnalysisResult> => {
  try {
    console.log('Analyzing fleet data and matching with products...');

    // Get all products
    const products = await fetchProducts();

    // Extract unique tire sizes from fleet data
    const tireSizes = tireSizeFilter.length > 0
      ? tireSizeFilter
      : [...new Set(fleetData.map(item => item.tire_size).filter(Boolean))];

    console.log('Tire sizes to analyze:', tireSizes);

    // Match products with tire sizes
    const matchedProducts: MatchedProduct[] = [];

    for (const tireSize of tireSizes) {
      // Find products that match or are similar to this tire size
      const matchingProducts = findMatchingProducts(products, tireSize);

      matchedProducts.push({
        tireSize,
        products: matchingProducts
      });
    }

    // Get AI recommendations based on fleet data and matched products
    const aiRecommendations = await getAIRecommendations(fleetData, matchedProducts, products);

    return {
      matchedProducts,
      promotionRecommendations: aiRecommendations.promotionRecommendations,
      analysisText: aiRecommendations.analysisText
    };
  } catch (error) {
    console.error('Error analyzing fleet and products:', error);
    throw error;
  }
};

/**
 * Find products that match or are similar to the given tire size
 */
function findMatchingProducts(products: Product[], tireSize: string): Product[] {
  if (!tireSize) return [];

  // Normalize the tire size for comparison
  const normalizedTireSize = normalizeTireSize(tireSize);

  // First try exact matches in material description
  let matches = products.filter(product => {
    const normalizedDescription = normalizeTireSize(product.materialDescription);
    return normalizedDescription.includes(normalizedTireSize);
  });

  // If no exact matches, try fuzzy matching
  if (matches.length === 0) {
    matches = products.filter(product => {
      // Extract potential tire size patterns from the description
      const description = product.materialDescription.toLowerCase();

      // Check for common tire size patterns
      return (
        // Check for R pattern (e.g., 27.00R49)
        description.includes(normalizedTireSize.replace('.', '').replace(' ', '')) ||

        // Check for dash pattern (e.g., 27.00-49)
        description.includes(normalizedTireSize.replace('r', '-').replace('.', '').replace(' ', '')) ||

        // Check for space pattern (e.g., 27.00 49)
        description.includes(normalizedTireSize.replace('r', ' ').replace('.', ''))
      );
    });
  }

  // If still no matches, try matching just the numbers
  if (matches.length === 0) {
    // Extract numbers from the tire size (e.g., "27.00R49" -> "2700" and "49")
    const sizeNumbers = normalizedTireSize.match(/\d+/g) || [];

    if (sizeNumbers.length >= 2) {
      const mainSize = sizeNumbers[0].replace('.', '');
      const rimSize = sizeNumbers[sizeNumbers.length - 1];

      matches = products.filter(product => {
        const description = product.materialDescription.toLowerCase();
        return description.includes(mainSize) && description.includes(rimSize);
      });
    }
  }

  return matches;
}

/**
 * Normalize tire size for better matching
 */
function normalizeTireSize(tireSize: string): string {
  return tireSize
    .toLowerCase()
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Get AI recommendations based on fleet data and matched products
 */
async function getAIRecommendations(
  fleetData: FleetData[],
  matchedProducts: MatchedProduct[],
  allProducts: Product[]
): Promise<{ promotionRecommendations: PromotionRecommendation[], analysisText: string }> {
  try {
    // Prepare data for AI analysis
    const fleetSummary = summarizeFleetData(fleetData);
    const productSummary = summarizeMatchedProducts(matchedProducts);

    // Create the prompt for the AI model
    const prompt = `
Anda adalah asisten AI yang ahli dalam rekomendasi produk ban dan promosi pemasaran untuk armada pertambangan dan alat berat.

RINGKASAN DATA ARMADA:
${JSON.stringify(fleetSummary, null, 2)}

PRODUK BAN YANG COCOK:
${JSON.stringify(productSummary, null, 2)}

Berdasarkan data armada dan produk ban yang cocok, silakan:
1. Analisis komposisi armada dan kebutuhan ban
2. Rekomendasikan produk ban yang sesuai dari daftar produk yang cocok
3. Sarankan 2-3 promosi yang efektif untuk profil armada ini

Respons Anda harus dalam format JSON berikut:
{
  "analysisText": "Analisis singkat 2-3 paragraf tentang data armada dan kebutuhan ban",
  "promotionRecommendations": [
    {
      "title": "Judul promosi yang menarik dan catchy",
      "description": "Deskripsi strategi promosi yang detail dan persuasif",
      "targetCustomers": ["Pelanggan1", "Pelanggan2"],
      "targetLocations": ["Lokasi1", "Lokasi2"],
      "estimatedROI": "Perkiraan ROI dalam persentase",
      "implementationSteps": ["Langkah 1", "Langkah 2", "Langkah 3"],
      "marketingCopy": "Contoh teks marketing yang bisa langsung digunakan untuk promosi ini"
    }
  ]
}

PEDOMAN PENTING:
- Fokus pada ukuran ban dalam data armada dan produk yang cocok
- Pertimbangkan lokasi pelanggan dan komposisi armada saat merekomendasikan promosi
- Sarankan peluang bundling yang sesuai dengan kebutuhan pelanggan
- Rekomendasikan promosi yang menargetkan segmen pelanggan tertentu
- Buat analisis yang ringkas namun mendalam
- Pastikan semua bidang JSON diformat dengan benar
- GUNAKAN BAHASA INDONESIA yang persuasif dan menarik untuk semua teks
- Buat judul promosi yang catchy dan mudah diingat
- Berikan contoh teks marketing yang bisa langsung digunakan
- Sertakan langkah-langkah implementasi yang jelas
- Berikan perkiraan ROI untuk setiap promosi
`;

    // Call the OpenRouter API
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'http://localhost:5183',
        'X-Title': 'Fleet Product Analysis'
      },
      body: JSON.stringify({
        model: DEFAULT_MODEL,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
        max_tokens: 2000
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content;

    // Parse the JSON response
    try {
      // Clean up the response if needed
      let cleanedContent = content;

      // Remove markdown code blocks if present
      if (cleanedContent.includes('```')) {
        cleanedContent = cleanedContent.replace(/```json\s*/, '').replace(/```\s*$/, '');
      }

      // Remove any text before the first { and after the last }
      const jsonStartIndex = cleanedContent.indexOf('{');
      const jsonEndIndex = cleanedContent.lastIndexOf('}');

      if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
        cleanedContent = cleanedContent.substring(jsonStartIndex, jsonEndIndex + 1);
      }

      const parsedResponse = JSON.parse(cleanedContent);

      return {
        promotionRecommendations: parsedResponse.promotionRecommendations || [],
        analysisText: parsedResponse.analysisText || 'No analysis provided'
      };
    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError);

      // Return fallback data
      return {
        promotionRecommendations: [
          {
            title: "Bundling Promotion for Heavy Equipment Tires",
            description: "Special bundling offer for customers with multiple units using the same tire size.",
            targetCustomers: fleetSummary.topCustomers.slice(0, 3)
          }
        ],
        analysisText: "Based on the fleet data, we recommend focusing on the most common tire sizes in your fleet. Consider bundling promotions for customers with multiple units using the same tire size."
      };
    }
  } catch (error) {
    console.error('Error getting AI recommendations:', error);

    // Return fallback data
    return {
      promotionRecommendations: [
        {
          title: "Volume Discount for Mining Operations",
          description: "Offer volume discounts for mining customers with large fleets.",
          targetCustomers: fleetData.slice(0, 3).map(item => item.customer).filter(Boolean)
        }
      ],
      analysisText: "The fleet data shows various tire sizes being used across different customers and locations. Consider targeting high-volume customers with special promotions."
    };
  }
}

/**
 * Summarize fleet data for AI analysis
 */
function summarizeFleetData(fleetData: FleetData[]) {
  // Count unique customers
  const customers = [...new Set(fleetData.map(item => item.customer).filter(Boolean))];

  // Count unique locations
  const locations = [...new Set(fleetData.map(item => item.location).filter(Boolean))];

  // Count unique tire sizes
  const tireSizes = [...new Set(fleetData.map(item => item.tire_size).filter(Boolean))];

  // Count tire quantities by size
  const tireSizeCounts: Record<string, number> = {};
  fleetData.forEach(item => {
    if (item.tire_size && item.totaltire) {
      const size = item.tire_size;
      const quantity = parseInt(item.totaltire, 10) || 0;

      tireSizeCounts[size] = (tireSizeCounts[size] || 0) + quantity;
    }
  });

  // Get top customers by total tire quantity
  const customerTireCounts: Record<string, number> = {};
  fleetData.forEach(item => {
    if (item.customer && item.totaltire) {
      const customer = item.customer;
      const quantity = parseInt(item.totaltire, 10) || 0;

      customerTireCounts[customer] = (customerTireCounts[customer] || 0) + quantity;
    }
  });

  const topCustomers = Object.entries(customerTireCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([customer]) => customer);

  return {
    totalFleetRecords: fleetData.length,
    uniqueCustomers: customers.length,
    uniqueLocations: locations.length,
    uniqueTireSizes: tireSizes.length,
    tireSizeCounts,
    topCustomers,
    tireSizes
  };
}

/**
 * Summarize matched products for AI analysis
 */
function summarizeMatchedProducts(matchedProducts: MatchedProduct[]) {
  return matchedProducts.map(match => ({
    tireSize: match.tireSize,
    matchCount: match.products.length,
    priceRange: match.products.length > 0 ? {
      min: Math.min(...match.products.map(p => p.price)),
      max: Math.max(...match.products.map(p => p.price)),
      avg: Math.round(match.products.reduce((sum, p) => sum + p.price, 0) / match.products.length)
    } : null,
    productDescriptions: match.products.map(p => p.materialDescription)
  }));
}
