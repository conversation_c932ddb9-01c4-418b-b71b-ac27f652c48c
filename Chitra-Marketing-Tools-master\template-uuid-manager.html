<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template UUID Manager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2, h3 {
            color: #333;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 400px;
        }
        .container {
            margin-top: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.delete {
            background-color: #f44336;
        }
        button.delete:hover {
            background-color: #d32f2f;
        }
        button.secondary {
            background-color: #2196F3;
        }
        button.secondary:hover {
            background-color: #0b7dda;
        }
        input, select, textarea {
            padding: 8px;
            width: 100%;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        .tab.active {
            background-color: #4CAF50;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .template-row:hover {
            background-color: #f1f1f1;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>Template UUID Manager</h1>
    
    <div class="tabs">
        <div class="tab active" onclick="switchTab('list')">List Templates</div>
        <div class="tab" onclick="switchTab('check')">Check UUID</div>
        <div class="tab" onclick="switchTab('create')">Create/Edit Template</div>
    </div>
    
    <div id="list-tab" class="tab-content active">
        <h2>All Templates</h2>
        <button onclick="refreshTemplateList()">Refresh List</button>
        <div id="templates-table"></div>
    </div>
    
    <div id="check-tab" class="tab-content">
        <h2>Check Template by UUID</h2>
        <div class="form-group">
            <label for="check-uuid">UUID to check:</label>
            <input type="text" id="check-uuid" value="2a08939d-dfb6-4b63-b0da-4ea510947b7b">
            <button onclick="checkTemplate()">Check Template</button>
        </div>
        <pre id="check-results">Enter a UUID and click 'Check Template'.</pre>
    </div>
    
    <div id="create-tab" class="tab-content">
        <h2>Create/Edit Template</h2>
        <div class="form-group">
            <label for="create-uuid">UUID:</label>
            <input type="text" id="create-uuid" value="2a08939d-dfb6-4b63-b0da-4ea510947b7b">
        </div>
        
        <div class="form-group">
            <label for="create-name">Template Name:</label>
            <input type="text" id="create-name" placeholder="Enter template name">
        </div>
        
        <div class="form-group">
            <label for="create-type">Template Type:</label>
            <select id="create-type">
                <option value="bundling">Bundling</option>
                <option value="consignment">Konsinyasi</option>
                <option value="trade-in">Trade-In</option>
                <option value="performance-guarantee">Performance Guarantee</option>
                <option value="performance-warranty">Performance Warranty</option>
                <option value="first-michelin">First Michelin</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="create-variables">Variables (comma-separated):</label>
            <input type="text" id="create-variables" placeholder="e.g., customerName,proposalTitle,validUntil">
        </div>
        
        <button onclick="createOrUpdateTemplate()">Save Template</button>
        <button class="secondary" onclick="loadTemplateForEdit()">Load Selected UUID</button>
        
        <pre id="create-results">Fill in the form and click 'Save Template'.</pre>
    </div>

    <script>
        const TEMPLATES_STORAGE_KEY = 'chitra_templates';

        // Switch between tabs
        function switchTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(`${tabName}-tab`).classList.add('active');
            document.querySelector(`.tab:nth-child(${tabName === 'list' ? 1 : tabName === 'check' ? 2 : 3})`).classList.add('active');
            
            // Refresh data if switching to list tab
            if (tabName === 'list') {
                refreshTemplateList();
            }
        }

        // Get templates from localStorage
        function getAllTemplates() {
            const templatesJson = localStorage.getItem(TEMPLATES_STORAGE_KEY);
            if (!templatesJson) {
                return [];
            }

            try {
                return JSON.parse(templatesJson);
            } catch (error) {
                console.error('Error parsing templates from localStorage:', error);
                return [];
            }
        }

        // Find template by ID
        function getTemplateById(id) {
            const templates = getAllTemplates();
            const template = templates.find(t => t.id === id);
            return template || null;
        }

        // Check for a specific template
        function checkTemplate() {
            const targetUuid = document.getElementById('check-uuid').value.trim();
            const resultsElement = document.getElementById('check-results');
            
            let output = `Checking for template with UUID: ${targetUuid}\n\n`;
            
            const template = getTemplateById(targetUuid);
            
            if (template) {
                output += "Template found!\n";
                output += "Template details:\n";
                output += `Name: ${template.name}\n`;
                output += `Type: ${template.type}\n`;
                output += `Created: ${new Date(template.createdAt).toLocaleString()}\n`;
                output += `Updated: ${new Date(template.updatedAt).toLocaleString()}\n`;
                output += `Variables: ${JSON.stringify(template.detectedVariables, null, 2)}\n`;
                output += `File URL: ${template.fileUrl || 'None'}\n`;
            } else {
                output += "No template found with this UUID\n";
            }
            
            resultsElement.textContent = output;
        }

        // Create or update a template
        function createOrUpdateTemplate() {
            const uuid = document.getElementById('create-uuid').value.trim();
            const name = document.getElementById('create-name').value.trim();
            const type = document.getElementById('create-type').value;
            const variablesInput = document.getElementById('create-variables').value.trim();
            
            const resultsElement = document.getElementById('create-results');
            
            if (!uuid || !name) {
                resultsElement.textContent = "UUID and name are required!";
                return;
            }
            
            // Parse variables
            const detectedVariables = variablesInput ? 
                variablesInput.split(',').map(v => v.trim()).filter(v => v) : 
                [];
            
            // Get existing templates
            const templates = getAllTemplates();
            
            // Check if template with this UUID already exists
            const existingIndex = templates.findIndex(t => t.id === uuid);
            
            // Create new template object
            const newTemplate = {
                id: uuid,
                name: name,
                type: type,
                file: null,
                detectedVariables: detectedVariables,
                createdAt: existingIndex >= 0 ? templates[existingIndex].createdAt : new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            // Either update existing or add new
            if (existingIndex >= 0) {
                templates[existingIndex] = newTemplate;
                resultsElement.textContent = `Updated existing template with UUID: ${uuid}`;
            } else {
                templates.push(newTemplate);
                resultsElement.textContent = `Created new template with UUID: ${uuid}`;
            }
            
            // Save to localStorage
            localStorage.setItem(TEMPLATES_STORAGE_KEY, JSON.stringify(templates));
            
            // Show template details
            resultsElement.textContent += `\n\nTemplate details:\n${JSON.stringify(newTemplate, null, 2)}`;
            
            // Refresh the list
            refreshTemplateList();
        }

        // Load a template for editing
        function loadTemplateForEdit() {
            const uuid = document.getElementById('create-uuid').value.trim();
            const template = getTemplateById(uuid);
            
            if (!template) {
                document.getElementById('create-results').textContent = `No template found with UUID: ${uuid}`;
                return;
            }
            
            // Fill the form with template data
            document.getElementById('create-name').value = template.name || '';
            document.getElementById('create-type').value = template.type || 'bundling';
            document.getElementById('create-variables').value = template.detectedVariables ? template.detectedVariables.join(', ') : '';
            
            document.getElementById('create-results').textContent = `Loaded template: ${template.name}`;
        }

        // Delete a template
        function deleteTemplate(uuid) {
            if (!confirm(`Are you sure you want to delete the template with UUID: ${uuid}?`)) {
                return;
            }
            
            const templates = getAllTemplates();
            const filteredTemplates = templates.filter(t => t.id !== uuid);
            
            if (filteredTemplates.length === templates.length) {
                alert(`No template with UUID ${uuid} found to delete.`);
                return;
            }
            
            localStorage.setItem(TEMPLATES_STORAGE_KEY, JSON.stringify(filteredTemplates));
            alert(`Template with UUID ${uuid} has been deleted.`);
            
            // Refresh the list
            refreshTemplateList();
        }

        // Select a template for editing
        function selectTemplate(uuid) {
            document.getElementById('create-uuid').value = uuid;
            document.getElementById('check-uuid').value = uuid;
            
            // Switch to edit tab
            switchTab('create');
            
            // Load the template data
            loadTemplateForEdit();
        }

        // Refresh the templates list
        function refreshTemplateList() {
            const templates = getAllTemplates();
            const tableContainer = document.getElementById('templates-table');
            
            if (templates.length === 0) {
                tableContainer.innerHTML = '<p>No templates found in localStorage.</p>';
                return;
            }
            
            let tableHtml = `
                <table>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Type</th>
                            <th>UUID</th>
                            <th>Created</th>
                            <th>Variables</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            templates.forEach(template => {
                tableHtml += `
                    <tr class="template-row" onclick="selectTemplate('${template.id}')">
                        <td>${template.name}</td>
                        <td>${template.type}</td>
                        <td>${template.id}</td>
                        <td>${new Date(template.createdAt).toLocaleDateString()}</td>
                        <td>${template.detectedVariables.length} variables</td>
                        <td>
                            <button class="delete" onclick="event.stopPropagation(); deleteTemplate('${template.id}')">Delete</button>
                        </td>
                    </tr>
                `;
            });
            
            tableHtml += `
                    </tbody>
                </table>
            `;
            
            tableContainer.innerHTML = tableHtml;
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            refreshTemplateList();
        });
    </script>
</body>
</html>
