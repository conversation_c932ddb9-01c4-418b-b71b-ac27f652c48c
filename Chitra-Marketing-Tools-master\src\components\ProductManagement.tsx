import React, { useState, useEffect, useMemo } from 'react';
import { read, utils, write } from 'xlsx';
import { Product } from '../types';
import { saveProducts, fetchProducts, updateProduct, deleteProduct } from '../services/productService';
import { Pencil, Trash2, X, Check, ChevronLeft, ChevronRight, Search, Loader2, Download, Plus, RefreshCw } from 'lucide-react';
import FieldMappingModal from './FieldMappingModal';
import { getExchangeRate } from '../services/exchangeRateService';

export default function ProductManagement() {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  // Enhanced search state
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Edit state
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [editFormData, setEditFormData] = useState<EditFormDataType>({});
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  // Add product modal state
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [newProductData, setNewProductData] = useState<Partial<Product> & { [key: string]: any }>({
    oldMaterialNo: '',
    materialDescription: '',
    description: '',
    price: 0,
    priceUSD: 0,
    exchangeRate: 0,
    slowMoving: false
  });

  // Global Exchange Rate state
  const [globalExchangeRate, setGlobalExchangeRate] = useState<number>(15000);
  const [isUpdatingPrices, setIsUpdatingPrices] = useState(false);

  // Field mapping for import
  const [fieldMapping, setFieldMapping] = useState<Record<string, string>>({
    oldMaterialNo: 'Material No.',
    materialDescription: 'Material Description',
    description: 'Description',
    price: 'Price (IDR)',
    priceUSD: 'USD Price',
    exchangeRate: 'Exchange Rate',
    slowMoving: 'Slow Moving'
  });

  // Field mapping modal state
  const [showMappingModal, setShowMappingModal] = useState(false);
  const [availableFields, setAvailableFields] = useState<string[]>([]);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [sampleData, setSampleData] = useState<Record<string, any> | null>(null);

  // Bulk operations state
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [showBulkEditModal, setShowBulkEditModal] = useState(false);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  type EditFormDataType = Partial<Product> & { [key: string]: any };
  type BulkEditDataType = {
    price?: number;
    priceUSD?: number;
    exchangeRate?: number;
    description?: string;
    slowMoving?: boolean;
    [key: string]: any;
  };
  const [bulkEditData, setBulkEditData] = useState<BulkEditDataType>({});

  const [isLoadingRate, setIsLoadingRate] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState<string | null>(null);

  // Load products on component mount
  useEffect(() => {
    loadProducts();
  }, []);

  // Fetch exchange rate on component mount
  useEffect(() => {
    fetchLatestExchangeRate();
  }, []);

  // Function to fetch latest exchange rate
  const fetchLatestExchangeRate = async () => {
    try {
      setIsLoadingRate(true);
      const rate = await getExchangeRate();
      setGlobalExchangeRate(rate);
      setLastUpdateTime(new Date().toLocaleString());
    } catch (error) {
      console.error('Error fetching exchange rate:', error);
      setMessage('Gagal mengambil nilai tukar mata uang. Menggunakan nilai default.');
    } finally {
      setIsLoadingRate(false);
    }
  };

  // Enhanced filter products based on search term with category support and error handling
  const filteredProducts = useMemo(() => {
    try {
      if (!searchTerm || !searchTerm.trim()) {
        return products;
      }

      const lowerSearchTerm = searchTerm.toLowerCase();

      // Check for category-specific searches with format "category:term"
      const categoryMatch = lowerSearchTerm.match(/^(code|name|desc|price|usd|status):(.+)$/);

      if (categoryMatch) {
        const [, category, term] = categoryMatch;

        return products.filter(product => {
          try {
            switch (category) {
              case 'code':
                return ((product.oldMaterialNo || '').toLowerCase()).includes(term);
              case 'name':
                return ((product.materialDescription || '').toLowerCase()).includes(term);
              case 'desc':
                return ((product.description || '').toLowerCase()).includes(term);
              case 'price':
                // Allow searching for price ranges or specific prices
                if (term.includes('-')) {
                  const [min, max] = term.split('-').map(Number);
                  return !isNaN(min) && !isNaN(max) &&
                         (product.price !== undefined && product.price !== null) &&
                         product.price >= min && product.price <= max;
                } else {
                  const price = Number(term);
                  return !isNaN(price) &&
                         (product.price !== undefined && product.price !== null) &&
                         product.price.toString().includes(term);
                }
              case 'usd':
                return (product.priceUSD !== undefined && product.priceUSD !== null) &&
                       product.priceUSD.toString().includes(term);
              case 'status':
                if (term === 'slow' || term === 'slowmoving' || term === 'slow moving') {
                  return product.slowMoving === true;
                } else if (term === 'normal') {
                  return product.slowMoving === false || product.slowMoving === undefined;
                }
                return false;
              default:
                return false;
            }
          } catch (err) {
            console.error('Error filtering product:', product, err);
            return false;
          }
        });
      }

      // Handle special status searches
      if (lowerSearchTerm === "slow moving" || lowerSearchTerm === "slowmoving") {
        return products.filter(product => product.slowMoving === true);
      } else if (lowerSearchTerm === "normal") {
        return products.filter(product => product.slowMoving === false || product.slowMoving === undefined);
      }

      // Regular text search across all fields with error handling
      return products.filter(product => {
        try {
          return (
            ((product.oldMaterialNo || '').toLowerCase()).includes(lowerSearchTerm) ||
            ((product.materialDescription || '').toLowerCase()).includes(lowerSearchTerm) ||
            ((product.description || '').toLowerCase()).includes(lowerSearchTerm) ||
            (product.price !== undefined && product.price !== null && product.price.toString().includes(lowerSearchTerm)) ||
            (product.priceUSD !== undefined && product.priceUSD !== null && product.priceUSD.toString().includes(lowerSearchTerm))
          );
        } catch (err) {
          console.error('Error filtering product:', product, err);
          return false;
        }
      });
    } catch (err) {
      console.error('Error in filteredProducts:', err);
      return products; // Return all products if there's an error
    }
  }, [products, searchTerm]);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Prevent default behavior for search input
  const handleSearchInputClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // Handle search input change with proper event handling
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setSearchTerm(e.target.value);
  };

  // Handle key down events in search input
  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    e.stopPropagation();
    // Prevent form submission on Enter key
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };

  // Handle clear search button click
  const handleClearSearch = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setSearchTerm('');
  };

  // Reset selection when products change
  useEffect(() => {
    setSelectedProducts(new Set());
    setSelectAll(false);
  }, [products.length]);

  const loadProducts = async () => {
    setIsLoading(true);
    try {
      const data = await fetchProducts();
      setProducts(data);
      setMessage(data.length > 0 ? `Loaded ${data.length} products.` : 'No products found.');
    } catch (error) {
      console.error('Error loading products:', error);
      setMessage('Error loading products. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    setIsLoading(true);
    setMessage('Processing file...');
    setImportFile(file);

    try {
      // Read the Excel file
      const data = await file.arrayBuffer();
      const workbook = read(data);

      // Get the first worksheet
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];

      // Convert to JSON
      const jsonData = utils.sheet_to_json(worksheet);

      if (jsonData.length === 0) {
        throw new Error('No data found in the imported file');
      }

      console.log('Imported data sample:', jsonData[0]);

      // Get all possible field names from the first row
      const fields = Object.keys(jsonData[0] as object);
      setAvailableFields(fields);
      setSampleData(jsonData[0] as Record<string, any>);

      // Try to auto-map fields
      const newMapping: Record<string, string> = {};

      // Define field alternatives for auto-mapping
      const alternatives: Record<string, string[]> = {
        oldMaterialNo: ['Material No.', 'Old material no.', 'oldMaterialNo', 'PN', 'MaterialNo', 'material_no', 'part_number', 'part_no', 'kode', 'code', 'item_code', 'product_code'],
        materialDescription: ['Material Description', 'materialDescription', 'Description', 'Material Name', 'Product Name', 'nama_produk', 'product_name', 'nama', 'name', 'item_name'],
        description: ['Description', 'Additional Description', 'description', 'SLoc', 'Additional Info', 'Notes', 'keterangan', 'deskripsi', 'lokasi', 'location'],
        price: ['Price (IDR)', 'Price', 'price', 'IDR', 'idr', 'harga', 'ValStockValue', 'nilai', 'harga_idr', 'price_idr', 'idr_price'],
        priceUSD: ['USD Price', 'priceUSD', 'USD', 'usd', 'Price (USD)', 'harga_usd', 'usd_price', 'dollar_price', 'price_usd'],
        exchangeRate: ['Exchange Rate', 'exchangeRate', 'Rate', 'kurs', 'nilai_tukar', 'exchange_rate', 'rate', 'conversion_rate', 'kurs_dollar'],
        slowMoving: ['Slow Moving', 'slowMoving', 'Slow_Moving', 'slow_moving', 'SlowMoving', 'Is Slow Moving', 'Slow', 'Status', 'Movement Status', 'Movement', 'Pergerakan', 'Lambat']
      };

      // Auto-map fields based on name similarity
      Object.keys(alternatives).forEach(fieldType => {
        // First try exact match (case-insensitive)
        const exactMatch = fields.find(f =>
          alternatives[fieldType].some(alt => alt.toLowerCase() === f.toLowerCase())
        );

        if (exactMatch) {
          newMapping[fieldType] = exactMatch;
        } else {
          // Try partial match
          const partialMatch = fields.find(f =>
            alternatives[fieldType].some(alt => f.toLowerCase().includes(alt.toLowerCase()))
          );

          if (partialMatch) {
            newMapping[fieldType] = partialMatch;
          }
        }
      });

      // Update field mapping with auto-mapped fields
      setFieldMapping(prev => ({
        ...prev,
        ...newMapping
      }));

      // Show the mapping modal
      setShowMappingModal(true);
    } catch (error) {
      console.error('Error reading import file:', error);
      setMessage(`Failed to read import file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setImportFile(null);
      setIsLoading(false);

      // Reset the file input
      e.target.value = '';
    }
  };

  // Process import after field mapping is confirmed
  const processImport = async () => {
    if (!importFile) {
      setMessage('No file selected for import');
      return;
    }

    setIsLoading(true);
    setMessage('Processing file with mapped fields...');

    try {
      // Read the Excel file
      const data = await importFile.arrayBuffer();
      const workbook = read(data);

      // Get the first worksheet
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];

      // Convert to JSON
      const jsonData = utils.sheet_to_json(worksheet);

      // Process the data with the mapped fields
      const importedProducts: Product[] = [];
      const uniqueProducts = new Map<string, Product>();

      // Create a map of existing products by material number for easy lookup
      const existingProductsByMaterialNo = new Map<string, Product>();
      products.forEach(product => {
        existingProductsByMaterialNo.set(product.oldMaterialNo, product);
      });

      // Track replaced products for reporting
      let replacedCount = 0;
      let newCount = 0;
      let skippedCount = 0;
      let errorRows: number[] = [];

      // Function to get value using field mapping
      const getFieldValue = (row: any, fieldType: string): any => {
        // Get the mapped field name
        const mappedField = fieldMapping[fieldType];

        if (mappedField && row[mappedField] !== undefined) {
          return row[mappedField];
        }

        return null;
      };

      // Process each row
      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i];

        // Get values using the field mapping
        const oldMaterialNo = getFieldValue(row, 'oldMaterialNo');
        const materialDescription = getFieldValue(row, 'materialDescription');
        const description = getFieldValue(row, 'description');
        let price = getFieldValue(row, 'price');
        let priceUSD = getFieldValue(row, 'priceUSD');
        let exchangeRate = getFieldValue(row, 'exchangeRate');
        let slowMoving = getFieldValue(row, 'slowMoving');

        // Validate required fields
        if (!oldMaterialNo || !materialDescription) {
          console.warn(`Row ${i + 1}: Missing required fields`);
          skippedCount++;
          errorRows.push(i + 1);
          continue;
        }

        // Check if this product already exists
        const existingProduct = existingProductsByMaterialNo.get(oldMaterialNo);

        // Use existing ID if product exists, otherwise create a new one
        const id = existingProduct ? existingProduct.id : `${oldMaterialNo}-${Date.now()}-${i}`;

        // Convert price to a number
        if (typeof price === 'string') {
          price = parseFloat(price.replace(/[^\d.-]/g, ''));
        } else if (price !== null) {
          price = Number(price);
        } else {
          price = 0;
        }

        // Convert USD price to a number if it exists
        if (priceUSD !== null) {
          if (typeof priceUSD === 'string') {
            priceUSD = parseFloat(priceUSD.replace(/[^\d.-]/g, ''));
          } else {
            priceUSD = Number(priceUSD);
          }
          if (isNaN(priceUSD)) priceUSD = undefined;
        } else {
          priceUSD = existingProduct?.priceUSD;
        }

        // Convert exchange rate to a number if it exists
        if (exchangeRate !== null) {
          if (typeof exchangeRate === 'string') {
            exchangeRate = parseFloat(exchangeRate.replace(/[^\d.-]/g, ''));
          } else {
            exchangeRate = Number(exchangeRate);
          }
          if (isNaN(exchangeRate)) exchangeRate = undefined;
        } else {
          exchangeRate = existingProduct?.exchangeRate;
        }

        // Process slowMoving value
        let slowMovingValue: boolean | undefined;
        if (slowMoving !== null) {
          if (typeof slowMoving === 'boolean') {
            slowMovingValue = slowMoving;
          } else if (typeof slowMoving === 'string') {
            const lowerValue = slowMoving.toLowerCase();
            slowMovingValue = lowerValue === 'true' || lowerValue === 'yes' || lowerValue === 'y' || lowerValue === '1' || lowerValue === 'slow moving';
          } else if (typeof slowMoving === 'number') {
            slowMovingValue = slowMoving === 1;
          } else {
            slowMovingValue = existingProduct?.slowMoving;
          }
        } else {
          slowMovingValue = existingProduct?.slowMoving;
        }

        // Create the product object
        const product: Product = {
          id,
          oldMaterialNo,
          materialDescription,
          description: description || '',
          price: isNaN(price) ? 0 : price,
          priceUSD: priceUSD,
          exchangeRate: exchangeRate,
          slowMoving: slowMovingValue
        };

        // Add to our products array
        importedProducts.push(product);

        // Keep track of unique products by material number
        if (!uniqueProducts.has(oldMaterialNo)) {
          uniqueProducts.set(oldMaterialNo, product);

          // Track if this is a replacement or a new product
          if (existingProduct) {
            replacedCount++;
          } else {
            newCount++;
          }
        }
      }

      // Merge with existing products - replace products with the same material number
      const updatedProducts = products.filter(p => !uniqueProducts.has(p.oldMaterialNo));
      updatedProducts.push(...Array.from(uniqueProducts.values()));

      // Update state with the merged products immediately
      setProducts(updatedProducts);
      setIsLoading(false);

      let resultMessage = `Import selesai: ${newCount} produk baru ditambahkan, ${replacedCount} produk yang ada diperbarui.`;
      if (skippedCount > 0) {
        resultMessage += ` ${skippedCount} baris dilewati karena data tidak lengkap.`;
      }
      setMessage(resultMessage + ' Menyimpan data...');

      // Save products to storage in background
      saveProducts(updatedProducts).then(success => {
        if (success) {
          setMessage(resultMessage);
        } else {
          setMessage('Data berhasil diimpor tetapi gagal disimpan secara permanen. Silakan coba lagi.');
        }
      }).catch(error => {
        console.error('Error saving products:', error);
        setMessage('Data berhasil diimpor tetapi gagal disimpan secara permanen. Silakan coba lagi.');
      });

      // Reset pagination to first page
      setCurrentPage(1);

      // Reset import state
      setImportFile(null);

    } catch (error) {
      console.error('Error processing file:', error);
      setIsLoading(false);
      setMessage('Error processing file. Please check the format and try again.');
    }
  };

  // Handle edit button click
  const handleEditClick = (product: Product) => {
    console.log('Editing product:', product);

    // Make sure we have default values for all fields
    setEditingProduct(product);
    setEditFormData({
      oldMaterialNo: product.oldMaterialNo,
      materialDescription: product.materialDescription,
      description: product.description || '',
      price: product.price || 0,
      // Initialize USD price and exchange rate with defaults if not present
      priceUSD: product.priceUSD || 0,
      // Use global exchange rate if product doesn't have one
      exchangeRate: product.exchangeRate || globalExchangeRate,
      // Initialize slowMoving with default if not present
      slowMoving: product.slowMoving || false
    });
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingProduct(null);
    setEditFormData({});
  };

  // Handle form field changes
  const handleEditFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Create a new form data object to work with
    const newFormData = { ...editFormData };

    // Handle the current field change
    if (name === 'price' || name === 'priceUSD' || name === 'exchangeRate') {
      const numValue = parseFloat(value);
      newFormData[name] = isNaN(numValue) ? 0 : numValue;
    } else {
      newFormData[name] = value;
    }

    // If USD price changes, calculate IDR price using the current exchange rate
    if (name === 'priceUSD') {
      const usdPrice = parseFloat(value);
      const exchangeRate = newFormData.exchangeRate || globalExchangeRate;

      if (!isNaN(usdPrice) && exchangeRate) {
        newFormData.price = usdPrice * exchangeRate;
      }
    }
    // If exchange rate changes, calculate IDR price using the current USD price
    else if (name === 'exchangeRate') {
      const exchangeRate = parseFloat(value);
      const usdPrice = newFormData.priceUSD;

      if (!isNaN(exchangeRate) && usdPrice) {
        newFormData.price = usdPrice * exchangeRate;
      }
    }
    // If IDR price changes directly, and there's a USD price, recalculate exchange rate
    else if (name === 'price' && newFormData.priceUSD && newFormData.priceUSD > 0) {
      const idrPrice = parseFloat(value);
      if (!isNaN(idrPrice) && idrPrice > 0) {
        newFormData.exchangeRate = idrPrice / newFormData.priceUSD;
      }
    }

    // Update the state with all changes at once
    setEditFormData(newFormData);
  };

  // Handle save edit
  const handleSaveEdit = async () => {
    if (!editingProduct) return;

    setIsLoading(true);

    try {
      // Create the updated product with all fields
      const updatedProduct: Product = {
        ...editingProduct,
        oldMaterialNo: editFormData.oldMaterialNo || editingProduct.oldMaterialNo,
        materialDescription: editFormData.materialDescription || editingProduct.materialDescription,
        description: editFormData.description || editingProduct.description,
        price: typeof editFormData.price === 'number' ? editFormData.price : editingProduct.price,
        priceUSD: typeof editFormData.priceUSD === 'number' ? editFormData.priceUSD : editingProduct.priceUSD,
        exchangeRate: typeof editFormData.exchangeRate === 'number' ? editFormData.exchangeRate : editingProduct.exchangeRate,
        slowMoving: typeof editFormData.slowMoving === 'boolean' ? editFormData.slowMoving : editingProduct.slowMoving
      };

      // Update local state immediately for better UX
      setProducts(products.map(p =>
        p.id === updatedProduct.id ? updatedProduct : p
      ));

      // Clear edit state immediately
      setEditingProduct(null);
      setEditFormData({});

      // Show temporary success message
      setMessage(`Menyimpan perubahan untuk ${updatedProduct.oldMaterialNo}...`);

      // Save to storage in background
      updateProduct(updatedProduct).then(success => {
        if (success) {
          setMessage(`Product ${updatedProduct.oldMaterialNo} berhasil diperbarui. Harga baru: ${updatedProduct.price.toLocaleString()} IDR`);
        } else {
          setMessage('Gagal menyimpan perubahan. Silakan coba lagi.');
        }
      }).catch(error => {
        console.error('Error updating product:', error);
        setMessage('Gagal menyimpan perubahan. Silakan coba lagi.');
      });

    } catch (error) {
      console.error('Error updating product:', error);
      setMessage('Gagal menyimpan perubahan. Silakan coba lagi.');
      setEditingProduct(null);
      setEditFormData({});
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete button click
  const handleDeleteClick = (productId: string) => {
    setIsDeleting(productId);
  };

  // Handle cancel delete
  const handleCancelDelete = () => {
    setIsDeleting(null);
  };

  // Handle confirm delete
  const handleConfirmDelete = async (productId: string) => {
    setIsLoading(true);

    try {
      // Find the product to be deleted (for the message)
      const productToDelete = products.find(p => p.id === productId);
      const productName = productToDelete ? productToDelete.oldMaterialNo : 'Product';

      // Update local state immediately for better UX
      setProducts(products.filter(p => p.id !== productId));
      setIsDeleting(null);

      // Show temporary message
      setMessage(`Menghapus ${productName}...`);

      // Adjust current page if needed
      const totalPages = Math.ceil((products.length - 1) / itemsPerPage);
      if (currentPage > totalPages && totalPages > 0) {
        setCurrentPage(totalPages);
      }

      // Delete from storage in background
      deleteProduct(productId).then(success => {
        if (success) {
          setMessage(`${productName} berhasil dihapus.`);
        } else {
          // If delete fails, add the product back to the list
          if (productToDelete) {
            setProducts(prev => [...prev, productToDelete]);
          }
          setMessage('Gagal menghapus produk. Silakan coba lagi.');
        }
      }).catch(error => {
        console.error('Error deleting product:', error);
        // If delete fails, add the product back to the list
        if (productToDelete) {
          setProducts(prev => [...prev, productToDelete]);
        }
        setMessage('Gagal menghapus produk. Silakan coba lagi.');
      });

    } catch (error) {
      console.error('Error deleting product:', error);
      setMessage('Gagal menghapus produk. Silakan coba lagi.');
      setIsDeleting(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle opening the add product modal
  const handleOpenAddModal = () => {
    setNewProductData({
      oldMaterialNo: '',
      materialDescription: '',
      description: '',
      price: 0,
      priceUSD: 0,
      exchangeRate: globalExchangeRate // Use the global exchange rate as default
    });
    setIsAddModalOpen(true);
  };

  // Handle closing the add product modal
  const handleCloseAddModal = () => {
    setIsAddModalOpen(false);
  };

  // Handle form field changes for new product
  const handleNewProductChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Create a new form data object to work with
    const newData = { ...newProductData };

    // Handle the current field change
    if (name === 'price' || name === 'priceUSD' || name === 'exchangeRate') {
      const numValue = parseFloat(value);
      newData[name] = isNaN(numValue) ? 0 : numValue;
    } else {
      newData[name] = value;
    }

    // If USD price changes, calculate IDR price using the current exchange rate
    if (name === 'priceUSD') {
      const usdPrice = parseFloat(value);
      const exchangeRate = newData.exchangeRate || globalExchangeRate;

      if (!isNaN(usdPrice) && exchangeRate) {
        newData.price = usdPrice * exchangeRate;
      }
    }
    // If exchange rate changes, calculate IDR price using the current USD price
    else if (name === 'exchangeRate') {
      const exchangeRate = parseFloat(value);
      const usdPrice = newData.priceUSD;

      if (!isNaN(exchangeRate) && usdPrice) {
        newData.price = usdPrice * exchangeRate;
      }
    }
    // If IDR price changes directly, and there's a USD price, recalculate exchange rate
    else if (name === 'price' && newData.priceUSD && newData.priceUSD > 0) {
      const idrPrice = parseFloat(value);
      if (!isNaN(idrPrice) && idrPrice > 0) {
        newData.exchangeRate = idrPrice / newData.priceUSD;
      }
    }

    // Update the state with all changes at once
    setNewProductData(newData);
  };

  // Handle adding a new product
  const handleAddProduct = async () => {
    // Validate required fields
    if (!newProductData.oldMaterialNo || !newProductData.materialDescription) {
      setMessage('Material No. and Material Description are required.');
      return;
    }

    setIsLoading(true);

    try {
      // Create a new product with a unique ID
      const newProduct: Product = {
        id: `${newProductData.oldMaterialNo}-${Date.now()}`,
        oldMaterialNo: newProductData.oldMaterialNo || '',
        materialDescription: newProductData.materialDescription || '',
        description: newProductData.description || '',
        price: typeof newProductData.price === 'number' ? newProductData.price : 0,
        priceUSD: typeof newProductData.priceUSD === 'number' ? newProductData.priceUSD : undefined,
        exchangeRate: typeof newProductData.exchangeRate === 'number' ? newProductData.exchangeRate : undefined
      };

      // Update local state immediately for better UX
      const updatedProducts = [...products, newProduct];
      setProducts(updatedProducts);

      // Close the modal
      setIsAddModalOpen(false);

      // Show temporary success message
      setMessage(`Menambahkan produk baru ${newProduct.oldMaterialNo}...`);

      // Save to storage in background
      saveProducts(updatedProducts).then(success => {
        if (success) {
          setMessage(`Produk ${newProduct.oldMaterialNo} berhasil ditambahkan.`);
        } else {
          setMessage('Gagal menyimpan produk baru. Silakan coba lagi.');
        }
      }).catch(error => {
        console.error('Error saving new product:', error);
        setMessage('Gagal menyimpan produk baru. Silakan coba lagi.');
      });

    } catch (error) {
      console.error('Error adding new product:', error);
      setMessage('Gagal menambahkan produk baru. Silakan coba lagi.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle global exchange rate change with improved error handling
  const handleGlobalExchangeRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      e.preventDefault();
      e.stopPropagation();

      const value = parseFloat(e.target.value);
      if (!isNaN(value) && value > 0) {
        setGlobalExchangeRate(value);
      }
    } catch (error) {
      console.error('Error updating exchange rate:', error);
      // Don't update state if there's an error
    }
  };

  // Update all product prices based on global exchange rate with improved error handling
  const updateAllPrices = async () => {
    try {
      // Validate exchange rate
      if (isNaN(globalExchangeRate) || globalExchangeRate <= 0) {
        setMessage('Exchange Rate harus berupa angka positif.');
        return;
      }

      setIsUpdatingPrices(true);
      setMessage('Memperbarui harga semua produk...');

      // Create updated products array with error handling for each product
      const updatedProducts = products.map(product => {
        try {
          // Only update products with valid USD price
          if (product.priceUSD !== undefined && product.priceUSD !== null && product.priceUSD > 0) {
            return {
              ...product,
              price: product.priceUSD * globalExchangeRate,
              exchangeRate: globalExchangeRate
            };
          }
          return product;
        } catch (productError) {
          console.error('Error updating individual product:', product, productError);
          return product; // Return original product if there's an error
        }
      });

      // Update state
      setProducts(updatedProducts);

      // Save to storage
      const success = await saveProducts(updatedProducts);

      if (success) {
        const updatedCount = updatedProducts.filter(p =>
          p.priceUSD !== undefined && p.priceUSD !== null && p.priceUSD > 0
        ).length;
        setMessage(`Berhasil memperbarui harga ${updatedCount} produk dengan Exchange Rate: ${globalExchangeRate.toLocaleString()}`);
      } else {
        setMessage('Gagal menyimpan perubahan harga. Silakan coba lagi.');
      }
    } catch (error) {
      console.error('Error updating prices:', error);
      setMessage('Terjadi kesalahan saat memperbarui harga. Silakan coba lagi.');
    } finally {
      setIsUpdatingPrices(false);
    }
  };

  // Handle select all checkbox
  const handleSelectAll = () => {
    if (selectAll) {
      // If already all selected, deselect all
      setSelectedProducts(new Set());
    } else {
      // Select all filtered products
      const newSelected = new Set<string>();
      filteredProducts.forEach(product => {
        newSelected.add(product.id);
      });
      setSelectedProducts(newSelected);
    }
    setSelectAll(!selectAll);
  };

  // Handle individual product selection
  const handleSelectProduct = (productId: string) => {
    const newSelected = new Set(selectedProducts);
    if (newSelected.has(productId)) {
      newSelected.delete(productId);
      setSelectAll(false);
    } else {
      newSelected.add(productId);
      // Check if all filtered products are now selected
      if (newSelected.size === filteredProducts.length) {
        setSelectAll(true);
      }
    }
    setSelectedProducts(newSelected);
  };

  // Open bulk edit modal
  const handleOpenBulkEdit = () => {
    setBulkEditData({});
    setShowBulkEditModal(true);
  };

  // Handle bulk edit form changes
  const handleBulkEditChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const newData = { ...bulkEditData };

    if (name === 'price' || name === 'priceUSD' || name === 'exchangeRate') {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        newData[name] = numValue;
      } else {
        delete newData[name];
      }
    } else {
      newData[name] = value;
    }

    setBulkEditData(newData);
  };

  // Apply bulk edit to selected products
  const handleApplyBulkEdit = async () => {
    if (Object.keys(bulkEditData).length === 0) {
      setMessage('No changes to apply.');
      setShowBulkEditModal(false);
      return;
    }

    setIsLoading(true);

    try {
      // Create updated products array
      const updatedProducts = products.map(product => {
        if (selectedProducts.has(product.id)) {
          // Apply bulk edits to this product
          const updatedProduct = { ...product };

          // Apply each field if it exists in bulkEditData
          if (bulkEditData.description !== undefined) {
            updatedProduct.description = bulkEditData.description;
          }

          if (bulkEditData.price !== undefined) {
            updatedProduct.price = bulkEditData.price;
          }

          if (bulkEditData.priceUSD !== undefined) {
            updatedProduct.priceUSD = bulkEditData.priceUSD;
          }

          if (bulkEditData.exchangeRate !== undefined) {
            updatedProduct.exchangeRate = bulkEditData.exchangeRate;

            // If we're updating exchange rate and product has USD price, recalculate IDR price
            if (updatedProduct.priceUSD) {
              updatedProduct.price = updatedProduct.priceUSD * bulkEditData.exchangeRate;
            }
          }

          if (bulkEditData.slowMoving !== undefined) {
            updatedProduct.slowMoving = bulkEditData.slowMoving;
          }

          return updatedProduct;
        }
        return product;
      });

      // Update state
      setProducts(updatedProducts);

      // Save to storage
      const success = await saveProducts(updatedProducts);

      if (success) {
        setMessage(`Successfully updated ${selectedProducts.size} products.`);
        // Clear selection after successful update
        setSelectedProducts(new Set());
        setSelectAll(false);
      } else {
        setMessage('Failed to save changes. Please try again.');
      }
    } catch (error) {
      console.error('Error applying bulk edit:', error);
      setMessage('An error occurred while updating products.');
    } finally {
      setIsLoading(false);
      setShowBulkEditModal(false);
    }
  };

  // Open bulk delete confirmation
  const handleOpenBulkDelete = () => {
    setShowBulkDeleteConfirm(true);
  };

  // Confirm and execute bulk delete
  const handleConfirmBulkDelete = async () => {
    setIsLoading(true);

    try {
      // Filter out selected products
      const remainingProducts = products.filter(product => !selectedProducts.has(product.id));

      // Update state immediately for better UX
      setProducts(remainingProducts);

      // Save to storage
      const success = await saveProducts(remainingProducts);

      if (success) {
        setMessage(`Successfully deleted ${selectedProducts.size} products.`);
        // Clear selection after successful delete
        setSelectedProducts(new Set());
        setSelectAll(false);
      } else {
        setMessage('Failed to delete products. Please try again.');
        // Restore products if save failed
        setProducts(products);
      }
    } catch (error) {
      console.error('Error deleting products:', error);
      setMessage('An error occurred while deleting products.');
      // Restore products if error
      setProducts(products);
    } finally {
      setIsLoading(false);
      setShowBulkDeleteConfirm(false);
    }
  };

  // Handle export products to Excel
  const handleExportProducts = () => {
    try {
      // Convert products to a format suitable for Excel
      const productsForExport = products.map(product => ({
        'Material No.': product.oldMaterialNo,
        'Material Description': product.materialDescription,
        'Description': product.description || '',
        'Price (IDR)': product.price,
        'USD Price': product.priceUSD || '',
        'Exchange Rate': product.exchangeRate || ''
      }));

      // Create a worksheet
      const worksheet = utils.json_to_sheet(productsForExport);

      // Create a workbook
      const workbook = utils.book_new();
      utils.book_append_sheet(workbook, worksheet, 'Products');

      // Generate Excel file
      const excelBuffer = write(workbook, { bookType: 'xlsx', type: 'array' });

      // Create a Blob from the buffer
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      // Create a download link and trigger the download
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Products_${new Date().toISOString().split('T')[0]}.xlsx`;
      link.click();

      // Clean up
      URL.revokeObjectURL(url);

      // Show success message
      setMessage('Products exported successfully.');
    } catch (error) {
      console.error('Error exporting products:', error);
      setMessage('Failed to export products. Please try again.');
    }
  };

  return (
    <div className="space-y-6 bg-white p-6 rounded-lg shadow-sm border">
      <h2 className="text-xl font-semibold">Product Management</h2>

      <div className="space-y-4">
        {/* Global Exchange Rate Section */}
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h3 className="text-lg font-medium text-blue-800 mb-2">Global Exchange Rate USD to IDR</h3>
          <div className="flex flex-col md:flex-row md:items-end gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Exchange Rate (USD to IDR)
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  value={globalExchangeRate}
                  onChange={handleGlobalExchangeRateChange}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      e.stopPropagation();
                    }
                  }}
                  className="w-full p-2 border rounded-md no-navigation"
                  placeholder="Contoh: 15000"
                  min="1"
                />
                <button
                  type="button"
                  onClick={fetchLatestExchangeRate}
                  disabled={isLoadingRate}
                  className="p-2 text-blue-600 hover:text-blue-800 focus:outline-none"
                  title="Refresh exchange rate"
                >
                  {isLoadingRate ? (
                    <Loader2 className="animate-spin" size={20} />
                  ) : (
                    <RefreshCw size={20} />
                  )}
                </button>
              </div>
              <p className="mt-1 text-sm text-gray-500">
                {lastUpdateTime ? `Terakhir diperbarui: ${lastUpdateTime}` : 'Belum ada data nilai tukar'}
              </p>
            </div>
            
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                updateAllPrices();
              }}
              disabled={isUpdatingPrices}
              className="w-full md:w-auto flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300"
            >
              {isUpdatingPrices ? (
                <>
                  <Loader2 className="animate-spin mr-2" size={16} />
                  <span>Memperbarui...</span>
                </>
              ) : (
                <span>Update Semua Harga IDR</span>
              )}
            </button>
          </div>
          <p className="mt-2 text-xs text-blue-600">
            Catatan: Hanya produk dengan harga USD yang akan diperbarui. Harga IDR akan dihitung sebagai USD × Exchange Rate.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Import Products from Excel/CSV
            </label>
            <input
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileUpload}
              className="w-full p-2 border rounded-md"
            />
            <p className="mt-1 text-sm text-gray-500">
              Upload an Excel or CSV file with product data
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Add Product Manually
            </label>
            <button
              onClick={handleOpenAddModal}
              className="w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <Plus className="mr-2 h-4 w-4" />
              Tambah Produk Baru
            </button>
            <p className="mt-1 text-sm text-gray-500">
              Add a new product manually with a form
            </p>
          </div>
        </div>

        {isLoading && (
          <div className="flex items-center space-x-2 text-blue-600 p-3 bg-blue-50 rounded-md">
            <Loader2 className="animate-spin" size={20} />
            <span>Memproses data...</span>
          </div>
        )}

        {message && (
          <div className={`p-3 rounded-md ${message.includes('Error') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
            {message}
          </div>
        )}

        {products.length > 0 && (
          <div className="mt-4">
            <h3 className="text-lg font-medium mb-2">Product List ({products.length})</h3>

            {/* Search, Export, Add, and Items per page controls */}
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center space-x-4">
                {/* Enhanced Search Component */}
                <form
                  className="relative w-80 flex flex-col no-navigation"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onSubmit={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                  }}
                >
                  <div className="relative flex items-center">
                    <div className="absolute left-0 pl-3 flex items-center pointer-events-none">
                      <Search size={18} className="text-blue-500" />
                    </div>
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        setSearchTerm(e.target.value);
                      }}
                      onFocus={(e) => {
                        e.preventDefault();
                        setIsSearchFocused(true);
                      }}
                      onBlur={(e) => {
                        e.preventDefault();
                        // Delay hiding the dropdown to allow clicking on suggestions
                        setTimeout(() => setIsSearchFocused(false), 200);
                      }}
                      onKeyDown={e => {
                        e.stopPropagation();
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          setIsSearchFocused(false);
                        } else if (e.key === 'Escape') {
                          e.preventDefault();
                          setIsSearchFocused(false);
                        }
                      }}
                      className={`pl-10 pr-10 py-2.5 w-full border-2 ${isSearchFocused ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200'} rounded-lg shadow-sm outline-none transition-all duration-200 no-navigation search-input`}
                      placeholder="Cari produk, kode, atau status..."
                    />
                    <div className="absolute right-0 flex items-center">
                      {searchTerm && (
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            setSearchTerm('');
                          }}
                          onMouseDown={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                          }}
                          className="p-1.5 mr-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
                          aria-label="Clear search"
                        >
                          <X size={16} className="text-gray-500 hover:text-gray-700" />
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Search dropdown */}
                  {isSearchFocused && (
                    <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                      <div className="p-2 border-b border-gray-100">
                        <h4 className="text-xs font-semibold text-gray-500 uppercase mb-1">Kategori Pencarian</h4>
                        <div className="grid grid-cols-2 gap-1">
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setSearchTerm('code:');
                            }}
                            className="text-left text-sm px-2 py-1.5 hover:bg-gray-100 rounded"
                          >
                            <span className="font-medium">Kode</span>
                            <span className="text-xs text-gray-500 block">code:226</span>
                          </button>
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setSearchTerm('name:');
                            }}
                            className="text-left text-sm px-2 py-1.5 hover:bg-gray-100 rounded"
                          >
                            <span className="font-medium">Nama</span>
                            <span className="text-xs text-gray-500 block">name:miller</span>
                          </button>
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setSearchTerm('price:');
                            }}
                            className="text-left text-sm px-2 py-1.5 hover:bg-gray-100 rounded"
                          >
                            <span className="font-medium">Harga</span>
                            <span className="text-xs text-gray-500 block">price:1000000-2000000</span>
                          </button>
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setSearchTerm('status:');
                            }}
                            className="text-left text-sm px-2 py-1.5 hover:bg-gray-100 rounded"
                          >
                            <span className="font-medium">Status</span>
                            <span className="text-xs text-gray-500 block">status:slow</span>
                          </button>
                        </div>
                      </div>
                      <div className="p-2">
                        <h4 className="text-xs font-semibold text-gray-500 uppercase mb-1">Filter Cepat</h4>
                        <div className="flex flex-wrap gap-1">
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setSearchTerm('slow moving');
                              setIsSearchFocused(false);
                            }}
                            className="px-2 py-1 text-sm bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 transition-colors"
                          >
                            Slow Moving
                          </button>
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setSearchTerm('normal');
                              setIsSearchFocused(false);
                            }}
                            className="px-2 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200 transition-colors"
                          >
                            Normal
                          </button>
                          <button
                            type="button"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              setSearchTerm('price:1000000-2000000');
                              setIsSearchFocused(false);
                            }}
                            className="px-2 py-1 text-sm bg-blue-100 text-blue-800 rounded hover:bg-blue-200 transition-colors"
                          >
                            1-2 Juta
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Search tips */}
                  <div className="mt-1.5 text-xs text-gray-500">
                    <div className="flex items-center flex-wrap">
                      <span className="mr-1 mb-1">Filter cepat:</span>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setSearchTerm('slow moving');
                        }}
                        className="mr-1.5 mb-1 px-1.5 py-0.5 bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 transition-colors"
                      >
                        slow moving
                      </button>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setSearchTerm('normal');
                        }}
                        className="mr-1.5 mb-1 px-1.5 py-0.5 bg-green-100 text-green-800 rounded hover:bg-green-200 transition-colors"
                      >
                        normal
                      </button>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setSearchTerm('price:1000000-2000000');
                        }}
                        className="mr-1.5 mb-1 px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded hover:bg-blue-200 transition-colors"
                      >
                        1-2 juta
                      </button>
                    </div>
                    <div className="mt-1">
                      <span className="mr-1">Pencarian kategori:</span>
                      <span className="inline-flex flex-wrap gap-1.5 mt-1">
                        <span className="px-1.5 py-0.5 bg-gray-100 text-gray-700 rounded">code:226</span>
                        <span className="px-1.5 py-0.5 bg-gray-100 text-gray-700 rounded">name:miller</span>
                        <span className="px-1.5 py-0.5 bg-gray-100 text-gray-700 rounded">price:1000000-2000000</span>
                        <span className="px-1.5 py-0.5 bg-gray-100 text-gray-700 rounded">status:slow</span>
                      </span>
                    </div>
                  </div>
                </form>

                {/* Add Product Button */}
                <button
                  onClick={handleOpenAddModal}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Tambah Produk
                </button>

                {/* Export Button */}
                <button
                  onClick={handleExportProducts}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  disabled={products.length === 0}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Export Products
                </button>

                {/* Bulk Action Buttons - only show when items are selected */}
                {selectedProducts.size > 0 && (
                  <div className="flex items-center space-x-2 ml-4 pl-4 border-l border-gray-300">
                    <span className="text-sm text-gray-600">{selectedProducts.size} selected</span>
                    <button
                      onClick={handleOpenBulkEdit}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      disabled={isLoading}
                    >
                      <Pencil className="mr-1 h-4 w-4" />
                      Bulk Edit
                    </button>
                    <button
                      onClick={handleOpenBulkDelete}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      disabled={isLoading}
                    >
                      <Trash2 className="mr-1 h-4 w-4" />
                      Bulk Delete
                    </button>
                  </div>
                )}
              </div>

              {/* Items per page selector */}
              <label className="flex items-center text-sm text-gray-600">
                Items per page:
                <select
                  value={itemsPerPage}
                  onChange={(e) => {
                    setItemsPerPage(Number(e.target.value));
                    setCurrentPage(1); // Reset to first page when changing items per page
                  }}
                  className="ml-2 border rounded p-1"
                >
                  <option value={10}>10</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
              </label>
            </div>

            {/* Enhanced Search results display */}
            {searchTerm && (
              <div className="mb-3 p-2.5 bg-blue-50 border border-blue-100 rounded-lg">
                <div className="flex items-center">
                  <div className="flex-shrink-0 mr-2">
                    {filteredProducts.length > 0 ? (
                      <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                        <Search size={14} className="text-blue-600" />
                      </div>
                    ) : (
                      <div className="w-6 h-6 rounded-full bg-amber-100 flex items-center justify-center">
                        <X size={14} className="text-amber-600" />
                      </div>
                    )}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">
                      {filteredProducts.length > 0
                        ? `Ditemukan ${filteredProducts.length} produk yang cocok dengan pencarian "${searchTerm}"`
                        : `Tidak ditemukan produk yang cocok dengan pencarian "${searchTerm}"`
                      }
                    </p>
                    {filteredProducts.length > 0 && (
                      <p className="text-xs text-gray-500 mt-0.5">
                        Menampilkan halaman {currentPage} dari {Math.ceil(filteredProducts.length / itemsPerPage)}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={handleSelectAll}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material No.</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price (IDR)</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">USD Price</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Slow Moving</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {/* Calculate pagination */}
                  {filteredProducts
                    .slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
                    .map((product) => (
                      <tr key={product.id} className={isDeleting === product.id ? 'bg-red-50' : ''}>
                        {editingProduct && editingProduct.id === product.id ? (
                          // Edit mode row
                          <>
                            <td className="px-3 py-4 whitespace-nowrap">
                              {/* No checkbox during edit */}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <input
                                type="text"
                                name="oldMaterialNo"
                                value={editFormData.oldMaterialNo || ''}
                                onChange={(e) => handleEditFormChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                                className="w-full p-1 border rounded text-sm"
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <input
                                type="text"
                                name="materialDescription"
                                value={editFormData.materialDescription || ''}
                                onChange={(e) => handleEditFormChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                                className="w-full p-1 border rounded text-sm"
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <input
                                type="number"
                                name="price"
                                value={editFormData.price || 0}
                                onChange={(e) => handleEditFormChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                                className="w-full p-1 border rounded text-sm"
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex space-x-2">
                                <input
                                  type="number"
                                  name="priceUSD"
                                  value={editFormData.priceUSD || 0}
                                  onChange={(e) => handleEditFormChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                                  placeholder="USD Price"
                                  className="w-20 p-1 border rounded text-sm"
                                />
                                <input
                                  type="number"
                                  name="exchangeRate"
                                  value={editFormData.exchangeRate || 0}
                                  onChange={(e) => handleEditFormChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                                  placeholder="Exchange Rate"
                                  className="w-24 p-1 border rounded text-sm"
                                />
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <select
                                name="slowMoving"
                                value={editFormData.slowMoving ? "true" : "false"}
                                onChange={(e) => {
                                  handleEditFormChange(e as unknown as React.ChangeEvent<HTMLInputElement>);
                                }}
                                className="w-full p-1 border rounded text-sm"
                              >
                                <option value="false">Normal</option>
                                <option value="true">Slow Moving</option>
                              </select>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex space-x-2">
                                <button
                                  onClick={handleSaveEdit}
                                  className="p-1 text-white bg-green-500 rounded hover:bg-green-600"
                                  disabled={isLoading}
                                >
                                  <Check size={16} />
                                </button>
                                <button
                                  onClick={handleCancelEdit}
                                  className="p-1 text-white bg-gray-500 rounded hover:bg-gray-600"
                                  disabled={isLoading}
                                >
                                  <X size={16} />
                                </button>
                              </div>
                            </td>
                          </>
                        ) : isDeleting === product.id ? (
                          // Delete confirmation row
                          <>
                            <td className="px-3 py-4 whitespace-nowrap">
                              {/* No checkbox during delete confirmation */}
                            </td>
                            <td colSpan={5} className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                              Are you sure you want to delete this product?
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => handleConfirmDelete(product.id)}
                                  className="p-1 text-white bg-red-500 rounded hover:bg-red-600"
                                  disabled={isLoading}
                                >
                                  <Check size={16} />
                                </button>
                                <button
                                  onClick={handleCancelDelete}
                                  className="p-1 text-white bg-gray-500 rounded hover:bg-gray-600"
                                  disabled={isLoading}
                                >
                                  <X size={16} />
                                </button>
                              </div>
                            </td>
                          </>
                        ) : (
                          // Normal display row
                          <>
                            <td className="px-3 py-4 whitespace-nowrap">
                              <input
                                type="checkbox"
                                checked={selectedProducts.has(product.id)}
                                onChange={() => handleSelectProduct(product.id)}
                                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                disabled={isLoading || !!editingProduct || !!isDeleting}
                              />
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{product.oldMaterialNo}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.materialDescription}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{product.price.toLocaleString()}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {product.priceUSD ? `$${product.priceUSD.toLocaleString()}` : '-'}
                              {product.exchangeRate ? ` (Rate: ${product.exchangeRate})` : ''}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                              {product.slowMoving ? (
                                <span className="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                  Slow Moving
                                </span>
                              ) : (
                                <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                  Normal
                                </span>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => handleEditClick(product)}
                                  className="p-1 text-white bg-blue-500 rounded hover:bg-blue-600"
                                  disabled={isLoading || !!editingProduct || !!isDeleting}
                                >
                                  <Pencil size={16} />
                                </button>
                                <button
                                  onClick={() => handleDeleteClick(product.id)}
                                  className="p-1 text-white bg-red-500 rounded hover:bg-red-600"
                                  disabled={isLoading || !!editingProduct || !!isDeleting}
                                >
                                  <Trash2 size={16} />
                                </button>
                              </div>
                            </td>
                          </>
                        )}
                      </tr>
                  ))}

                  {filteredProducts.length > 0 && filteredProducts.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage).length === 0 && (
                    <tr>
                      <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                        No products on this page. Go to a previous page.
                      </td>
                    </tr>
                  )}

                  {filteredProducts.length === 0 && (
                    <tr>
                      <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                        {searchTerm ? `No products found matching "${searchTerm}".` : 'No products available.'}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination controls */}
            {filteredProducts.length > itemsPerPage && (
              <div className="flex justify-between items-center mt-4">
                <div className="text-sm text-gray-500">
                  Showing {Math.min((currentPage - 1) * itemsPerPage + 1, filteredProducts.length)} to {Math.min(currentPage * itemsPerPage, filteredProducts.length)} of {filteredProducts.length}
                </div>
                <div className="flex space-x-1">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className={`p-2 rounded ${
                      currentPage === 1
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    <ChevronLeft size={16} />
                  </button>

                  {Array.from({ length: Math.min(5, Math.ceil(filteredProducts.length / itemsPerPage)) }).map((_, i) => {
                    // Show pages around current page
                    let pageNum;
                    const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={i}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-1 rounded ${
                          currentPage === pageNum
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}

                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, Math.ceil(filteredProducts.length / itemsPerPage)))}
                    disabled={currentPage === Math.ceil(filteredProducts.length / itemsPerPage)}
                    className={`p-2 rounded ${
                      currentPage === Math.ceil(filteredProducts.length / itemsPerPage)
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    <ChevronRight size={16} />
                  </button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Add Product Modal */}
      {isAddModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Tambah Produk Baru</h3>
              <button
                onClick={handleCloseAddModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-4">
              {/* Material No */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Material No. <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="oldMaterialNo"
                  value={newProductData.oldMaterialNo || ''}
                  onChange={(e) => handleNewProductChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                  className="w-full p-2 border rounded-md"
                  placeholder="Contoh: 226-10.00-20 GT"
                  required
                />
              </div>

              {/* Material Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Material Description <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="materialDescription"
                  value={newProductData.materialDescription || ''}
                  onChange={(e) => handleNewProductChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                  className="w-full p-2 border rounded-md"
                  placeholder="Contoh: 10.00 - 20 GT MILLER"
                  required
                />
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  name="description"
                  value={newProductData.description || ''}
                  onChange={(e) => handleNewProductChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                  className="w-full p-2 border rounded-md"
                  placeholder="Contoh: Jakarta"
                />
              </div>

              {/* Price (IDR) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price (IDR)
                </label>
                <input
                  type="number"
                  name="price"
                  value={newProductData.price || 0}
                  onChange={(e) => handleNewProductChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                  className="w-full p-2 border rounded-md"
                  placeholder="Contoh: 3000000"
                />
              </div>

              {/* USD Price and Exchange Rate */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    USD Price
                  </label>
                  <input
                    type="number"
                    name="priceUSD"
                    value={newProductData.priceUSD || 0}
                    onChange={(e) => handleNewProductChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                    className="w-full p-2 border rounded-md"
                    placeholder="Contoh: 200"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Exchange Rate
                  </label>
                  <input
                    type="number"
                    name="exchangeRate"
                    value={newProductData.exchangeRate || 0}
                    onChange={(e) => handleNewProductChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                    className="w-full p-2 border rounded-md"
                    placeholder="Contoh: 15000"
                  />
                </div>
              </div>

              {/* Slow Moving Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Slow Moving Status
                </label>
                <select
                  name="slowMoving"
                  value={newProductData.slowMoving ? "true" : "false"}
                  onChange={(e) => {
                    handleNewProductChange(e as unknown as React.ChangeEvent<HTMLInputElement>);
                  }}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="false">Normal</option>
                  <option value="true">Slow Moving</option>
                </select>
                <p className="mt-1 text-xs text-gray-500">
                  Pilih "Slow Moving" untuk produk dengan pergerakan lambat
                </p>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={handleCloseAddModal}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Batal
                </button>
                <button
                  onClick={handleAddProduct}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <Loader2 className="animate-spin mr-2" size={16} />
                      <span>Menyimpan...</span>
                    </div>
                  ) : (
                    'Simpan Produk'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Field Mapping Modal */}
      <FieldMappingModal
        isOpen={showMappingModal}
        onClose={() => {
          setShowMappingModal(false);
          setImportFile(null);
          setIsLoading(false);
        }}
        availableFields={availableFields}
        initialMapping={fieldMapping}
        onSave={(mapping) => {
          setFieldMapping(mapping);
          setShowMappingModal(false);
          processImport();
        }}
        sampleData={sampleData || undefined}
      />

      {/* Bulk Edit Modal */}
      {showBulkEditModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Bulk Edit {selectedProducts.size} Products</h3>
              <button
                onClick={() => setShowBulkEditModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-4">
              <p className="text-sm text-gray-500">
                Leave fields blank to keep their current values. Only filled fields will be updated.
              </p>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  name="description"
                  value={bulkEditData.description || ''}
                  onChange={(e) => handleBulkEditChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                  className="w-full p-2 border rounded-md"
                  placeholder="Update description for all selected products"
                />
              </div>

              {/* Price (IDR) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Price (IDR)
                </label>
                <input
                  type="number"
                  name="price"
                  value={bulkEditData.price || ''}
                  onChange={(e) => handleBulkEditChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                  className="w-full p-2 border rounded-md"
                  placeholder="Update price for all selected products"
                />
              </div>

              {/* USD Price and Exchange Rate */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    USD Price
                  </label>
                  <input
                    type="number"
                    name="priceUSD"
                    value={bulkEditData.priceUSD || ''}
                    onChange={(e) => handleBulkEditChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                    className="w-full p-2 border rounded-md"
                    placeholder="Update USD price"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Exchange Rate
                  </label>
                  <input
                    type="number"
                    name="exchangeRate"
                    value={bulkEditData.exchangeRate || ''}
                    onChange={(e) => handleBulkEditChange(e as unknown as React.ChangeEvent<HTMLInputElement>)}
                    className="w-full p-2 border rounded-md"
                    placeholder="Update exchange rate"
                  />
                </div>
              </div>

              {/* Slow Moving Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Slow Moving Status
                </label>
                <select
                  name="slowMoving"
                  value={bulkEditData.slowMoving === undefined ? "" : bulkEditData.slowMoving ? "true" : "false"}
                  onChange={(e) => {
                    if (e.target.value === "") {
                      // Create a new object without the slowMoving property
                      const { slowMoving, ...rest } = bulkEditData;
                      setBulkEditData(rest);
                    } else {
                      handleBulkEditChange(e as unknown as React.ChangeEvent<HTMLInputElement>);
                    }
                  }}
                  className="w-full p-2 border rounded-md"
                >
                  <option value="">No change</option>
                  <option value="false">Normal</option>
                  <option value="true">Slow Moving</option>
                </select>
                <p className="mt-1 text-xs text-gray-500">
                  Pilih status untuk semua produk yang dipilih
                </p>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowBulkEditModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleApplyBulkEdit}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  disabled={isLoading || Object.keys(bulkEditData).length === 0}
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <Loader2 className="animate-spin mr-2" size={16} />
                      <span>Updating...</span>
                    </div>
                  ) : (
                    'Apply to Selected Products'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Delete Confirmation */}
      {showBulkDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-red-600">Delete {selectedProducts.size} Products</h3>
              <button
                onClick={() => setShowBulkDeleteConfirm(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-4">
              <p className="text-sm text-gray-700">
                Are you sure you want to delete {selectedProducts.size} selected products? This action cannot be undone.
              </p>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowBulkDeleteConfirm(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleConfirmBulkDelete}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <Loader2 className="animate-spin mr-2" size={16} />
                      <span>Deleting...</span>
                    </div>
                  ) : (
                    'Delete Selected Products'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
