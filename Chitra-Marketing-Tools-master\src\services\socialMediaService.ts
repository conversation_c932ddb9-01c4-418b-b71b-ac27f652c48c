import {
  SocialMediaPost,
  SocialMediaKnowledgeEntry,
  SocialMediaKnowledgeEntryCreateRequest,
  SocialMediaKnowledgeEntryUpdateRequest,
  SocialMediaKnowledgeSearchRequest,
  SocialMediaKnowledgeSearchResponse,
  SocialMediaKnowledgeCategory,
  SocialMediaPlatform,
  ContentType,
  ContentGenerationRequest,
  ContentGenerationResponse,
  InstagramCalendarMonth,
  InstagramCalendarDay,
  HashtagAnalysis,
  ContentCategory,
  MonthlyContentPlan,
  MonthlyContentPlanRequest,
  MonthlyContentPlanPost
} from '../types/socialMedia';
import { Product } from '../types';

// OpenRouter API key
const OPENROUTER_API_KEY = '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';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Models
export const MODELS = {
  DEEPSEEK: 'openai/gpt-4.1-nano',
  CLAUDE_OPUS: 'openai/gpt-4.1-nano',
  CLAUDE_SONNET: 'openai/gpt-4.1-nano',
  CLAUDE_HAIKU: 'openai/gpt-4.1-nano',
  MISTRAL_LARGE: 'openai/gpt-4.1-nano',
  MISTRAL_SMALL: 'openai/gpt-4.1-nano',
  OPENCHAT: 'openai/gpt-4.1-nano',
  GPT35: 'openai/gpt-4.1-nano',
  GPT4: 'openai/gpt-4.1-nano',
  GEMINI_PRO: 'openai/gpt-4.1-nano',
  GEMINI_FLASH: 'openai/gpt-4.1-nano',
};

// Note: We're using MODELS.GEMINI_FLASH for marketing content generation
// as it's free and optimized for marketing content

// Local storage keys
const SOCIAL_MEDIA_POSTS_KEY = 'chitraMarketingTools_socialMediaPosts';
const SOCIAL_MEDIA_KNOWLEDGE_KEY = 'chitraMarketingTools_socialMediaKnowledge';

// Sample knowledge entries for initial data
const SAMPLE_KNOWLEDGE_ENTRIES: SocialMediaKnowledgeEntry[] = [
  {
    id: 'smkb-1',
    title: 'Strategi Hashtag untuk Industri Ban',
    content: 'Gunakan kombinasi hashtag populer dan spesifik industri. Hashtag populer seperti #TireSafety #RoadSafety akan meningkatkan jangkauan, sementara hashtag spesifik seperti #MichelinTires #EarthMoverTires akan menarik audiens yang tepat. Selalu sertakan hashtag brand seperti #ChitraParatama dan hashtag kampanye khusus untuk melacak performa.',
    category: SocialMediaKnowledgeCategory.HASHTAG_STRATEGIES,
    tags: ['hashtag', 'instagram', 'reach', 'engagement'],
    platform: SocialMediaPlatform.INSTAGRAM,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    isActive: true
  },
  {
    id: 'smkb-2',
    title: 'Ide Konten Instagram untuk Produk Ban',
    content: '1. Before/After penggunaan ban di lokasi tambang\n2. Testimonial pelanggan dengan hasil nyata\n3. Infografis perbandingan umur pakai ban\n4. Behind-the-scenes proses instalasi ban besar\n5. Tips perawatan ban untuk performa optimal\n6. Spotlight karyawan/teknisi terbaik\n7. Case study penghematan biaya dengan ban premium\n8. Video time-lapse penggunaan ban di kondisi ekstrem',
    category: SocialMediaKnowledgeCategory.CONTENT_IDEAS,
    tags: ['content ideas', 'instagram', 'engagement', 'mining'],
    platform: SocialMediaPlatform.INSTAGRAM,
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-20'),
    isActive: true
  }
];

/**
 * Load social media posts from localStorage
 */
const loadSocialMediaPosts = (): SocialMediaPost[] => {
  try {
    const storedData = localStorage.getItem(SOCIAL_MEDIA_POSTS_KEY);
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      // Convert date strings to Date objects
      return parsedData.map((post: any) => ({
        ...post,
        scheduledDate: post.scheduledDate ? new Date(post.scheduledDate) : undefined,
        publishedDate: post.publishedDate ? new Date(post.publishedDate) : undefined,
        createdAt: new Date(post.createdAt),
        updatedAt: new Date(post.updatedAt)
      }));
    }
    return [];
  } catch (error) {
    console.error('Error loading social media posts:', error);
    return [];
  }
};

/**
 * Save social media posts to localStorage
 */
const saveSocialMediaPosts = (posts: SocialMediaPost[]): void => {
  try {
    localStorage.setItem(SOCIAL_MEDIA_POSTS_KEY, JSON.stringify(posts));
  } catch (error) {
    console.error('Error saving social media posts:', error);
  }
};

/**
 * Load social media knowledge entries from localStorage
 */
const loadSocialMediaKnowledge = (): SocialMediaKnowledgeEntry[] => {
  try {
    const storedData = localStorage.getItem(SOCIAL_MEDIA_KNOWLEDGE_KEY);
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      // Convert date strings to Date objects
      return parsedData.map((entry: any) => ({
        ...entry,
        createdAt: new Date(entry.createdAt),
        updatedAt: new Date(entry.updatedAt)
      }));
    }
    // Return sample data if no data exists
    return SAMPLE_KNOWLEDGE_ENTRIES;
  } catch (error) {
    console.error('Error loading social media knowledge:', error);
    return SAMPLE_KNOWLEDGE_ENTRIES;
  }
};

/**
 * Save social media knowledge entries to localStorage
 */
const saveSocialMediaKnowledge = (entries: SocialMediaKnowledgeEntry[]): void => {
  try {
    localStorage.setItem(SOCIAL_MEDIA_KNOWLEDGE_KEY, JSON.stringify(entries));
  } catch (error) {
    console.error('Error saving social media knowledge:', error);
  }
};

/**
 * Get all social media posts
 */
export const getAllSocialMediaPosts = async (): Promise<SocialMediaPost[]> => {
  return loadSocialMediaPosts();
};

/**
 * Get social media posts for a specific date
 */
export const getSocialMediaPostsByDate = async (date: string): Promise<SocialMediaPost[]> => {
  const posts = loadSocialMediaPosts();

  // Filter posts for the specified date
  return posts.filter(post => {
    const postDate = post.scheduledDate || post.publishedDate;
    if (!postDate) return false;

    const dateString = postDate.toISOString().split('T')[0];
    return dateString === date;
  });
};

/**
 * Get social media post by ID
 */
export const getSocialMediaPostById = async (id: string): Promise<SocialMediaPost | null> => {
  const posts = loadSocialMediaPosts();
  return posts.find(post => post.id === id) || null;
};

/**
 * Create a new social media post
 */
export const createSocialMediaPost = async (post: Omit<SocialMediaPost, 'id' | 'createdAt' | 'updatedAt'>): Promise<SocialMediaPost> => {
  try {
    // Validate required fields
    if (!post.platform || !post.contentType || !post.caption) {
      throw new Error('Missing required fields: platform, contentType, and caption are required');
    }

    const posts = loadSocialMediaPosts();

    const newPost: SocialMediaPost = {
      id: `post-${Date.now()}`,
      ...post,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const updatedPosts = [...posts, newPost];
    saveSocialMediaPosts(updatedPosts);

    return newPost;
  } catch (error) {
    console.error('Error creating social media post:', error);
    throw new Error('Failed to create social media post: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
};

/**
 * Update a social media post
 */
export const updateSocialMediaPost = async (updatedPost: SocialMediaPost): Promise<SocialMediaPost> => {
  const posts = loadSocialMediaPosts();
  const postIndex = posts.findIndex(post => post.id === updatedPost.id);

  if (postIndex === -1) {
    throw new Error(`Post with ID ${updatedPost.id} not found`);
  }

  // Ensure updatedAt is set to current time
  const finalUpdatedPost = {
    ...updatedPost,
    updatedAt: new Date()
  };

  posts[postIndex] = finalUpdatedPost;
  saveSocialMediaPosts(posts);

  return finalUpdatedPost;
};

/**
 * Delete a social media post
 */
export const deleteSocialMediaPost = async (id: string): Promise<boolean> => {
  const posts = loadSocialMediaPosts();
  const updatedPosts = posts.filter(post => post.id !== id);

  if (updatedPosts.length === posts.length) {
    return false;
  }

  saveSocialMediaPosts(updatedPosts);
  return true;
};

/**
 * Get all social media knowledge entries
 */
export const getAllSocialMediaKnowledge = async (): Promise<SocialMediaKnowledgeEntry[]> => {
  return loadSocialMediaKnowledge();
};

/**
 * Get social media knowledge entry by ID
 */
export const getSocialMediaKnowledgeById = async (id: string): Promise<SocialMediaKnowledgeEntry | null> => {
  const entries = loadSocialMediaKnowledge();
  return entries.find(entry => entry.id === id) || null;
};

/**
 * Create a new social media knowledge entry
 */
export const createSocialMediaKnowledge = async (request: SocialMediaKnowledgeEntryCreateRequest): Promise<SocialMediaKnowledgeEntry> => {
  const entries = loadSocialMediaKnowledge();

  const newEntry: SocialMediaKnowledgeEntry = {
    id: `smkb-${Date.now()}`,
    ...request,
    createdAt: new Date(),
    updatedAt: new Date(),
    isActive: true
  };

  const updatedEntries = [...entries, newEntry];
  saveSocialMediaKnowledge(updatedEntries);

  return newEntry;
};

/**
 * Update a social media knowledge entry
 */
export const updateSocialMediaKnowledge = async (request: SocialMediaKnowledgeEntryUpdateRequest): Promise<SocialMediaKnowledgeEntry | null> => {
  const entries = loadSocialMediaKnowledge();
  const entryIndex = entries.findIndex(entry => entry.id === request.id);

  if (entryIndex === -1) {
    return null;
  }

  const updatedEntry = {
    ...entries[entryIndex],
    ...request,
    updatedAt: new Date()
  };

  entries[entryIndex] = updatedEntry;
  saveSocialMediaKnowledge(entries);

  return updatedEntry;
};

/**
 * Delete a social media knowledge entry
 */
export const deleteSocialMediaKnowledge = async (id: string): Promise<boolean> => {
  const entries = loadSocialMediaKnowledge();
  const updatedEntries = entries.filter(entry => entry.id !== id);

  if (updatedEntries.length === entries.length) {
    return false;
  }

  saveSocialMediaKnowledge(updatedEntries);
  return true;
};

/**
 * Search social media knowledge entries
 */
export const searchSocialMediaKnowledge = async (request: SocialMediaKnowledgeSearchRequest): Promise<SocialMediaKnowledgeSearchResponse> => {
  const entries = loadSocialMediaKnowledge();

  let filteredEntries = entries;

  // Filter by active status if specified
  if (request.isActive !== undefined) {
    filteredEntries = filteredEntries.filter(entry => entry.isActive === request.isActive);
  }

  // Filter by category if specified
  if (request.category) {
    filteredEntries = filteredEntries.filter(entry => entry.category === request.category);
  }

  // Filter by platform if specified
  if (request.platform) {
    filteredEntries = filteredEntries.filter(entry => entry.platform === request.platform);
  }

  // Filter by tags if specified
  if (request.tags && request.tags.length > 0) {
    filteredEntries = filteredEntries.filter(entry =>
      request.tags!.some(tag => entry.tags.includes(tag))
    );
  }

  // Filter by search query if specified
  if (request.query) {
    const query = request.query.toLowerCase();
    filteredEntries = filteredEntries.filter(entry =>
      entry.title.toLowerCase().includes(query) ||
      entry.content.toLowerCase().includes(query) ||
      entry.tags.some(tag => tag.toLowerCase().includes(query))
    );
  }

  return {
    entries: filteredEntries,
    total: filteredEntries.length
  };
};

/**
 * Convert products to knowledge base entries
 * This function takes products from the product management system and converts them to knowledge base entries
 */
export const convertProductsToKnowledge = async (products: Product[]): Promise<SocialMediaKnowledgeEntry[]> => {
  try {
    console.log(`Converting ${products.length} products to knowledge base entries`);

    const knowledgeEntries: SocialMediaKnowledgeEntry[] = [];

    for (const product of products) {
      // Create a knowledge entry for each product
      const entry: SocialMediaKnowledgeEntry = {
        id: `product-${product.id}-${Date.now()}`,
        title: `Produk: ${product.materialDescription}`,
        content: generateProductContent(product),
        category: SocialMediaKnowledgeCategory.CONTENT_IDEAS,
        tags: generateProductTags(product),
        platform: SocialMediaPlatform.INSTAGRAM,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true
      };

      knowledgeEntries.push(entry);
    }

    return knowledgeEntries;
  } catch (error) {
    console.error('Error converting products to knowledge:', error);
    return [];
  }
};

/**
 * Generate content for a product knowledge entry
 */
function generateProductContent(product: Product): string {
  // Format price with commas
  const formattedPrice = product.price.toLocaleString('id-ID');

  // Generate content with product details
  return `INFORMASI PRODUK:
Kode Material: ${product.oldMaterialNo}
Deskripsi: ${product.materialDescription}
Lokasi/Catatan: ${product.description || 'N/A'}
Harga: Rp ${formattedPrice}

IDE KONTEN INSTAGRAM:
1. Post foto produk dengan highlight fitur unggulan
2. Infografis perbandingan dengan produk sejenis
3. Testimonial pelanggan yang menggunakan produk ini
4. Video singkat menunjukkan produk dalam penggunaan
5. Post edukasi tentang keunggulan teknologi produk
6. Konten Q&A tentang spesifikasi dan penggunaan produk

CONTOH CAPTION:
"✨ SOLUSI ANDAL UNTUK PERFORMA MAKSIMAL! ✨
${product.materialDescription} hadir dengan teknologi terdepan untuk memastikan kinerja optimal di segala medan. Hubungi kami untuk informasi lebih lanjut!
#ChitraParatama #BanPremium #PerformaMaksimal"`;
}

/**
 * Generate tags for a product knowledge entry
 */
function generateProductTags(product: Product): string[] {
  // Base tags that apply to all products
  const baseTags = ['product', 'instagram', 'content', 'marketing'];

  // Extract potential tags from product description
  const descriptionTags: string[] = [];

  // Check for common tire types and sizes in the material description
  const materialDesc = product.materialDescription.toLowerCase();

  if (materialDesc.includes('r 22.5')) {
    descriptionTags.push('22.5');
  }

  if (materialDesc.includes('r 20')) {
    descriptionTags.push('20');
  }

  if (materialDesc.includes('michelin')) {
    descriptionTags.push('michelin');
  }

  if (materialDesc.includes('xdr')) {
    descriptionTags.push('xdr');
  }

  if (materialDesc.includes('xdm')) {
    descriptionTags.push('xdm');
  }

  if (materialDesc.includes('xz')) {
    descriptionTags.push('xz');
  }

  // Add location tag if available
  if (product.description) {
    const location = product.description.toLowerCase();
    if (location.includes('jakarta')) {
      descriptionTags.push('jakarta');
    } else if (location.includes('surabaya')) {
      descriptionTags.push('surabaya');
    } else if (location.includes('palembang')) {
      descriptionTags.push('palembang');
    } else if (location.includes('kalimantan')) {
      descriptionTags.push('kalimantan');
    } else if (location.includes('sumatra') || location.includes('sumatera')) {
      descriptionTags.push('sumatra');
    }
  }

  // Combine all tags and remove duplicates
  return [...new Set([...baseTags, ...descriptionTags])];
}

/**
 * Import products to knowledge base
 * This function fetches products and adds them as knowledge entries
 */
export const importProductsToKnowledge = async (productIds: string[]): Promise<number> => {
  try {
    // Import the product service
    const { fetchProducts } = await import('../services/productService');

    // Fetch all products
    const allProducts = await fetchProducts();

    // Filter products by the selected IDs
    const selectedProducts = allProducts.filter(product => productIds.includes(product.id));

    if (selectedProducts.length === 0) {
      console.log('No products selected for import');
      return 0;
    }

    console.log(`Importing ${selectedProducts.length} products to knowledge base`);

    // Convert products to knowledge entries
    const knowledgeEntries = await convertProductsToKnowledge(selectedProducts);

    if (knowledgeEntries.length === 0) {
      console.log('No knowledge entries generated from products');
      return 0;
    }

    // Get existing knowledge entries
    const existingEntries = loadSocialMediaKnowledge();

    // Add new entries
    const updatedEntries = [...existingEntries, ...knowledgeEntries];

    // Save updated entries
    saveSocialMediaKnowledge(updatedEntries);

    console.log(`Successfully imported ${knowledgeEntries.length} products to knowledge base`);

    return knowledgeEntries.length;
  } catch (error) {
    console.error('Error importing products to knowledge base:', error);
    return 0;
  }
};

/**
 * Generate Instagram content using OpenRouter API
 */
export const generateInstagramContent = async (request: ContentGenerationRequest): Promise<ContentGenerationResponse> => {
  try {
    console.log('Generating Instagram content with OpenRouter API');

    // Prepare system prompt
    const systemPrompt = `Bertindaklah Seperti Copywriter direct respond kelas dunia dengan pengalaman lebih dari 20 tahun dalam membuat kampanye penjualan dengan konversi tinggi di bidang Distributor ban tambang, Tire Repair, Tire mangement, dan teknologi Tire di Indonesia, Tugasmu adalah menulis konten yang menarik untuk produk yang saya masukan kedalam knowladgebase, dengan target audiensi sesuai form.`;

    // Prepare user prompt
    let userPrompt = `Buatkan konten Instagram untuk produk ban dengan detail berikut:

Platform: ${request.platform}
Tipe Konten: ${request.contentType}
${request.productDetails ? `Detail Produk: ${request.productDetails}` : ''}
${request.targetAudience ? `Target Audiens: ${request.targetAudience}` : ''}
${request.campaignGoals ? `Tujuan Kampanye: ${request.campaignGoals}` : ''}
${request.tone ? `Tone: ${request.tone}` : 'Tone: Profesional namun ramah'}
Panjang: ${request.length || 'medium'}
Bahasa: ${request.language === 'en' ? 'Inggris' : 'Indonesia'}

Berikan respons dalam format JSON berikut:
{
  "caption": "Teks caption Instagram lengkap",
  "hashtags": ["hashtag1", "hashtag2", "hashtag3", ...],
  "suggestedImageDescription": "Deskripsi gambar yang direkomendasikan untuk post ini"
}

Caption harus menarik, relevan dengan industri ban, dan menggunakan emoji secara tepat. Hashtag harus campuran antara hashtag populer dan spesifik industri. Deskripsi gambar harus memberikan ide visual yang jelas.`;

    // Call OpenRouter API with Gemini model for marketing content
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Social Media'
      },
      body: JSON.stringify({
        model: MODELS.GEMINI_FLASH, // Using Gemini Flash which is free and good for marketing content
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('OpenRouter API response:', data);

    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      try {
        const content = JSON.parse(data.choices[0].message.content);
        return {
          caption: content.caption || '',
          hashtags: content.hashtags || [],
          suggestedImageDescription: content.suggestedImageDescription || ''
        };
      } catch (error) {
        console.error('Error parsing JSON response:', error);
        throw new Error('Failed to parse API response');
      }
    }

    throw new Error('Unexpected API response format');
  } catch (error) {
    console.error('Error generating Instagram content:', error);
    // Return fallback content
    return {
      caption: "✨ PERFORMA TANGGUH UNTUK MEDAN BERAT! ✨\n\nBan Michelin XDR2 dirancang khusus untuk kendaraan tambang dengan teknologi terdepan yang memberikan umur pakai lebih lama dan perlindungan maksimal.\n\nKunjungi showroom kami atau hubungi sales representative untuk informasi lebih lanjut tentang produk premium ini!",
      hashtags: ["#MichelinTires", "#BanTambang", "#ChitraParatama", "#PerformaTangguh", "#BanPremium", "#MiningTires"],
      suggestedImageDescription: "Foto ban Michelin XDR2 di lokasi tambang dengan kendaraan berat di latar belakang, menunjukkan ketangguhan ban di medan ekstrem."
    };
  }
};

/**
 * Generate Instagram calendar for a month
 */
export const generateInstagramCalendarMonth = async (year: number, month: number): Promise<InstagramCalendarMonth> => {
  // Get all posts
  const allPosts = await getAllSocialMediaPosts();

  // Filter posts for the specified month
  const monthPosts = allPosts.filter(post => {
    const postDate = post.scheduledDate || post.publishedDate;
    if (!postDate) return false;

    return postDate.getFullYear() === year && postDate.getMonth() + 1 === month;
  });

  // Create date for the first day of the month
  const startDate = new Date(year, month - 1, 1);

  // Get the number of days in the month
  const daysInMonth = new Date(year, month, 0).getDate();

  // Get the last day of the month for date range
  const endDate = new Date(year, month - 1, daysInMonth);

  // Format dates for API calls
  const startDateString = startDate.toISOString().split('T')[0];
  const endDateString = endDate.toISOString().split('T')[0];

  // Import the holiday service
  const { getHolidaysByDateRange } = await import('../services/holidayService');

  // Fetch holidays for this month
  let holidays: import('../services/holidayService').Holiday[] = [];
  try {
    holidays = await getHolidaysByDateRange(startDateString, endDateString);
    console.log(`Loaded ${holidays.length} holidays for ${year}-${month}`);
  } catch (error) {
    console.error('Error loading holidays:', error);
  }

  // Create calendar days
  const days: InstagramCalendarDay[] = [];

  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month - 1, day);
    const dateString = date.toISOString().split('T')[0];

    // Filter posts for this day
    const dayPosts = monthPosts.filter(post => {
      const postDate = post.scheduledDate || post.publishedDate;
      if (!postDate) return false;

      return postDate.getDate() === day;
    });

    // Find holidays for this date (using direct date comparison to avoid timezone issues)
    const dayHolidays = holidays.filter(holiday => {
      // Parse the holiday date
      const holidayDateParts = holiday.date.split('-');
      const holidayYear = parseInt(holidayDateParts[0]);
      const holidayMonth = parseInt(holidayDateParts[1]) - 1;
      const holidayDay = parseInt(holidayDateParts[2]);

      // Compare with the current day
      return holidayYear === year && holidayMonth === (month - 1) && holidayDay === day;
    });

    const isHoliday = dayHolidays.length > 0;
    const holidayName = isHoliday ? dayHolidays.map(h => h.name).join(', ') : undefined;

    // Check if it's a weekend (for recommendation score)
    const isWeekend = date.getDay() === 0 || date.getDay() === 6;

    // Recommendation score algorithm
    // Base score depends on weekday/weekend
    let baseScore = isWeekend ? 65 : 85;

    // Adjust score for holidays
    // National holidays get a higher score (more engagement potential)
    if (isHoliday) {
      const isNationalHoliday = dayHolidays.some(h => h.type === 'Nasional');
      baseScore = isNationalHoliday ? 95 : 90; // National holidays get highest priority
    }

    // Add some random variation for natural feel
    const randomVariation = Math.floor(Math.random() * 10) - 5; // -5 to +5
    const recommendationScore = Math.max(0, Math.min(100, baseScore + randomVariation));

    days.push({
      date: dateString,
      posts: dayPosts,
      recommendationScore,
      isHoliday,
      holidayName
    });
  }

  return {
    year,
    month,
    days
  };
};

/**
 * Generate monthly content plan for Instagram
 */
export const generateMonthlyContentPlan = async (request: MonthlyContentPlanRequest): Promise<MonthlyContentPlan> => {
  try {
    console.log('Generating monthly content plan with OpenRouter API');

    const { year, month, postsPerWeek = 3, preferredCategories = [], excludedCategories = [], includeHolidays = true, language = 'id' } = request;

    // Get all content categories except excluded ones
    const allCategories = Object.values(ContentCategory);
    const availableCategories = allCategories.filter(category => !excludedCategories.includes(category as ContentCategory));

    // Prioritize preferred categories
    const sortedCategories = [
      ...preferredCategories.filter(category => availableCategories.includes(category)),
      ...availableCategories.filter(category => !preferredCategories.includes(category as ContentCategory))
    ];

    // Get holidays for the month to incorporate into the content plan
    const startDate = new Date(year, month - 1, 1);
    const daysInMonth = new Date(year, month, 0).getDate();
    const endDate = new Date(year, month - 1, daysInMonth);

    // Format dates for API calls
    const startDateString = startDate.toISOString().split('T')[0];
    const endDateString = endDate.toISOString().split('T')[0];

    // Import the holiday service
    const { getHolidaysByDateRange } = await import('../services/holidayService');

    // Fetch holidays for this month
    let holidays: import('../services/holidayService').Holiday[] = [];
    try {
      holidays = await getHolidaysByDateRange(startDateString, endDateString);
      console.log(`Loaded ${holidays.length} holidays for content plan ${year}-${month}`);
    } catch (error) {
      console.error('Error loading holidays for content plan:', error);
    }

    // Get knowledge base entries to incorporate into the content plan
    const knowledgeEntries = await getAllSocialMediaKnowledge();
    const activeEntries = knowledgeEntries.filter(entry => entry.isActive);

    // Prepare system prompt
    const systemPrompt = `Anda adalah spesialis perencanaan konten media sosial profesional yang ahli dalam membuat rencana konten Instagram untuk industri ban dan otomotif. Anda membuat rencana konten yang bervariasi dan menarik untuk meningkatkan engagement.`;

    // Create a list of categories with descriptions for the AI
    const categoryDescriptions = Object.entries(ContentCategory).map(([key, value]) => {
      let description = '';
      switch (value) {
        case ContentCategory.PRODUCT:
          description = 'Konten tentang fitur produk, manfaat, dan spesifikasi ban';
          break;
        case ContentCategory.SAFETY:
          description = 'Tips keselamatan dan praktik terbaik terkait penggunaan ban';
          break;
        case ContentCategory.EDUCATIONAL:
          description = 'Konten edukatif tentang ban, industri, dan teknologi';
          break;
        case ContentCategory.QUIZ:
          description = 'Kuis interaktif dan pertanyaan untuk melibatkan audiens';
          break;
        case ContentCategory.ENGAGEMENT:
          description = 'Konten yang dirancang untuk meningkatkan engagement (pertanyaan, polling)';
          break;
        case ContentCategory.SERVICE:
          description = 'Layanan yang ditawarkan, tips perawatan ban';
          break;
        case ContentCategory.TESTIMONIAL:
          description = 'Testimoni pelanggan, kisah sukses';
          break;
        case ContentCategory.BEHIND_SCENES:
          description = 'Konten di balik layar, proses produksi, tim';
          break;
        case ContentCategory.INDUSTRY_NEWS:
          description = 'Berita industri, pembaruan, tren';
          break;
        case ContentCategory.SEASONAL:
          description = 'Konten musiman, terkait hari libur';
          break;
        case ContentCategory.PROMOTION:
          description = 'Penawaran khusus, promosi';
          break;
      }
      return `${key}: ${description}`;
    }).join('\n');

    // Create a list of holidays for the AI
    const holidaysList = holidays.map(holiday =>
      `${holiday.date}: ${holiday.name} (${holiday.description})`
    ).join('\n');

    // Create a list of knowledge base entries for the AI to reference
    const knowledgeBaseList = activeEntries.map(entry =>
      `ID: ${entry.id} | Kategori: ${entry.category} | Judul: ${entry.title} | Platform: ${entry.platform}`
    ).join('\n');

    // Prepare user prompt
    const userPrompt = `Buatkan rencana konten Instagram bulanan untuk industri ban dengan detail berikut:

Tahun: ${year}
Bulan: ${month}
Jumlah Post per Minggu: ${postsPerWeek}
Bahasa: ${language === 'id' ? 'Indonesia' : 'Inggris'}

KATEGORI KONTEN YANG TERSEDIA:
${categoryDescriptions}

${sortedCategories.length < allCategories.length ? `KATEGORI YANG DIUTAMAKAN: ${preferredCategories.join(', ')}` : ''}
${excludedCategories.length > 0 ? `KATEGORI YANG DIKECUALIKAN: ${excludedCategories.join(', ')}` : ''}

${includeHolidays && holidays.length > 0 ? `HARI LIBUR DAN HARI PENTING DI BULAN INI:
${holidaysList}` : ''}

${activeEntries.length > 0 ? `REFERENSI KNOWLEDGE BASE:
${knowledgeBaseList}` : ''}

Buatkan rencana konten yang bervariasi dengan mempertimbangkan kategori konten yang berbeda-beda. Pastikan konten tidak monoton dan dapat meningkatkan engagement. Untuk hari libur atau hari penting, buatkan konten yang relevan dengan momen tersebut.

Berikan respons dalam format JSON berikut:
{
  "posts": [
    {
      "date": "YYYY-MM-DD",
      "contentCategory": "kategori_konten",
      "contentType": "image/video/carousel/reel/story",
      "title": "Judul post",
      "description": "Deskripsi singkat tentang konten post",
      "knowledgeBaseReference": "ID referensi knowledge base (opsional)",
      "recommendedHashtags": ["hashtag1", "hashtag2", "hashtag3"],
      "suggestedImageDescription": "Deskripsi gambar yang direkomendasikan"
    },
    ...
  ]
}

Pastikan:
1. Tanggal post terdistribusi merata sepanjang bulan (${postsPerWeek} post per minggu)
2. Kategori konten bervariasi dan tidak monoton
3. Tipe konten bervariasi (image, video, carousel, reel, story)
4. Untuk hari libur atau hari penting, buatkan konten yang relevan dengan kategori SEASONAL
5. Referensikan knowledge base yang relevan jika ada
6. Berikan rekomendasi hashtag yang relevan (5-10 hashtag)
7. Berikan deskripsi gambar yang jelas untuk setiap post`;

    // Call OpenRouter API with Gemini model for marketing content
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Social Media'
      },
      body: JSON.stringify({
        model: MODELS.GEMINI_FLASH, // Using Gemini Flash which is free and good for marketing content
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 4000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('OpenRouter API response for content plan:', data);

    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      try {
        const content = JSON.parse(data.choices[0].message.content);

        // Process the posts to ensure they have all required fields and correct types
        const processedPosts: MonthlyContentPlanPost[] = content.posts.map((post: any) => ({
          date: post.date,
          contentCategory: post.contentCategory as ContentCategory,
          contentType: post.contentType as ContentType,
          title: post.title || '',
          description: post.description || '',
          knowledgeBaseReference: post.knowledgeBaseReference || undefined,
          recommendedHashtags: post.recommendedHashtags || [],
          suggestedImageDescription: post.suggestedImageDescription || ''
        }));

        // Calculate category distribution
        const categoryDistribution: Record<ContentCategory, number> = {} as Record<ContentCategory, number>;

        // Initialize all categories with 0
        Object.values(ContentCategory).forEach((category: ContentCategory) => {
          categoryDistribution[category] = 0;
        });

        // Count posts by category
        processedPosts.forEach((post: MonthlyContentPlanPost) => {
          categoryDistribution[post.contentCategory] = (categoryDistribution[post.contentCategory] || 0) + 1;
        });

        return {
          year,
          month,
          posts: processedPosts,
          categoryDistribution
        };
      } catch (error) {
        console.error('Error parsing JSON response for content plan:', error);
        throw new Error('Failed to parse API response for content plan');
      }
    }

    throw new Error('Unexpected API response format for content plan');
  } catch (error) {
    console.error('Error generating monthly content plan:', error);

    // Return fallback content plan with some basic posts
    const daysInMonth = new Date(request.year, request.month, 0).getDate();
    const fallbackPosts: MonthlyContentPlanPost[] = [];

    // Generate some fallback posts (one per week)
    const weeksInMonth = Math.ceil(daysInMonth / 7);
    for (let week = 0; week < weeksInMonth; week++) {
      const day = Math.min(7 * week + 3, daysInMonth); // Wednesday of each week or last day
      const date = new Date(request.year, request.month - 1, day);
      const dateString = date.toISOString().split('T')[0];

      // Alternate between product and safety posts
      const category = week % 2 === 0 ? ContentCategory.PRODUCT : ContentCategory.SAFETY;
      const contentType = week % 3 === 0 ? ContentType.CAROUSEL : (week % 3 === 1 ? ContentType.IMAGE : ContentType.REEL);

      fallbackPosts.push({
        date: dateString,
        contentCategory: category,
        contentType: contentType,
        title: category === ContentCategory.PRODUCT ? 'Fitur Unggulan Ban Michelin' : 'Tips Keselamatan Ban',
        description: category === ContentCategory.PRODUCT
          ? 'Post tentang fitur unggulan ban Michelin untuk kendaraan tambang'
          : 'Tips keselamatan dan perawatan ban untuk performa optimal',
        recommendedHashtags: ['#MichelinTires', '#ChitraParatama', '#BanTambang', '#MiningTires'],
        suggestedImageDescription: category === ContentCategory.PRODUCT
          ? 'Foto ban Michelin dengan highlight pada fitur unggulan'
          : 'Infografis tentang tips keselamatan ban'
      });
    }

    // Calculate category distribution
    const categoryDistribution: Record<ContentCategory, number> = {} as Record<ContentCategory, number>;

    // Initialize all categories with 0
    Object.values(ContentCategory).forEach((category: ContentCategory) => {
      categoryDistribution[category] = 0;
    });

    // Count posts by category
    fallbackPosts.forEach((post: MonthlyContentPlanPost) => {
      categoryDistribution[post.contentCategory] = (categoryDistribution[post.contentCategory] || 0) + 1;
    });

    return {
      year: request.year,
      month: request.month,
      posts: fallbackPosts,
      categoryDistribution
    };
  }
};

/**
 * Analyze hashtags using OpenRouter API
 */
export const analyzeHashtags = async (hashtags: string[]): Promise<HashtagAnalysis[]> => {
  try {
    console.log('Analyzing hashtags with OpenRouter API');

    // Prepare system prompt
    const systemPrompt = `Anda adalah spesialis analisis hashtag Instagram yang ahli dalam industri ban dan otomotif. Anda akan menganalisis hashtag dan memberikan skor popularitas, relevansi, dan tingkat kompetisi, serta rekomendasi penggunaan.`;

    // Prepare user prompt
    const userPrompt = `Analisis hashtag berikut untuk industri ban dan berikan skor popularitas, relevansi, dan tingkat kompetisi (0-100), serta rekomendasi penggunaan (high/medium/low):

${hashtags.join(', ')}

Berikan respons dalam format JSON berikut:
[
  {
    "hashtag": "nama hashtag",
    "popularity": skor popularitas (0-100),
    "relevance": skor relevansi untuk industri ban (0-100),
    "competition": skor tingkat kompetisi (0-100),
    "recommendation": "high/medium/low"
  },
  ...
]`;

    // Call OpenRouter API with Gemini model for marketing content
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Social Media'
      },
      body: JSON.stringify({
        model: MODELS.GEMINI_FLASH, // Using Gemini Flash which is free and good for marketing content
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 1000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('OpenRouter API response:', data);

    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      try {
        return JSON.parse(data.choices[0].message.content);
      } catch (error) {
        console.error('Error parsing JSON response:', error);
        throw new Error('Failed to parse API response');
      }
    }

    throw new Error('Unexpected API response format');
  } catch (error) {
    console.error('Error analyzing hashtags:', error);
    // Return fallback analysis
    return hashtags.map(hashtag => ({
      hashtag,
      popularity: Math.floor(Math.random() * 100),
      relevance: Math.floor(Math.random() * 100),
      competition: Math.floor(Math.random() * 100),
      recommendation: ['high', 'medium', 'low'][Math.floor(Math.random() * 3)] as 'high' | 'medium' | 'low'
    }));
  }
};
