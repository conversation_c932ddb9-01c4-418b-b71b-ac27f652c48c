import React, { useState, useEffect } from 'react';
import { KnowledgeEntry } from '../types/knowledgeBase';
import KnowledgeBaseManager from './KnowledgeBaseManager';
import { Database, Check } from 'lucide-react';

interface KnowledgeBaseSelectorProps {
  onKnowledgeEntriesSelected: (entries: KnowledgeEntry[]) => void;
  initialSelectedEntries?: KnowledgeEntry[];
}

export default function KnowledgeBaseSelector({
  onKnowledgeEntriesSelected,
  initialSelectedEntries = []
}: KnowledgeBaseSelectorProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedEntries, setSelectedEntries] = useState<KnowledgeEntry[]>(initialSelectedEntries);

  // Handle selection of knowledge entries
  const handleSelectEntries = (entries: KnowledgeEntry[]) => {
    setSelectedEntries(entries);
    onKnowledgeEntriesSelected(entries);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-medium flex items-center">
          <Database className="mr-2 text-indigo-500" />
          Knowledge Base
        </h2>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          {isExpanded ? 'Collapse' : 'Expand'}
        </button>
      </div>

      <p className="text-sm text-gray-600 mb-4">
        Pilih pengetahuan tambahan yang akan digunakan oleh AI dalam simulasi negosiasi ini.
        Pengetahuan yang dipilih akan membantu AI memahami konteks dan memberikan respons yang lebih relevan.
      </p>

      {selectedEntries.length > 0 && (
        <div className="mb-4">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Pengetahuan Terpilih:</h3>
          <div className="space-y-2">
            {selectedEntries.map(entry => (
              <div key={entry.id} className="flex items-center p-2 bg-blue-50 rounded-md border border-blue-100">
                <Check className="h-4 w-4 text-blue-600 mr-2" />
                <div>
                  <p className="text-sm font-medium">{entry.title}</p>
                  <p className="text-xs text-gray-500">{entry.category.replace(/_/g, ' ')}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {isExpanded ? (
        <div className="mt-4 border-t pt-4">
          <KnowledgeBaseManager
            selectable={true}
            onSelectEntries={handleSelectEntries}
            selectedEntries={selectedEntries}
          />
        </div>
      ) : (
        <button
          onClick={() => setIsExpanded(true)}
          className="w-full py-2 px-4 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 flex items-center justify-center"
        >
          <Database className="mr-2 h-4 w-4" />
          Pilih Pengetahuan
        </button>
      )}
    </div>
  );
}
