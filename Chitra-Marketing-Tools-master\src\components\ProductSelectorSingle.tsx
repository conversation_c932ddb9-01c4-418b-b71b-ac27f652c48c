import React, { useState, useEffect, useCallback } from 'react';
import { Search } from 'lucide-react';
import { Product } from '../types';
import { fetchProducts } from '../services/productService';

interface ProductSelectorSingleProps {
  selectedProduct: Product | null;
  onProductSelect: (product: Product) => void;
  label?: string;
}

export default function ProductSelectorSingle({
  selectedProduct,
  onProductSelect,
  label = "Select Product"
}: ProductSelectorSingleProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  // Load products on component mount
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        const productsData = await fetchProducts();
        setProducts(productsData);
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, []);

  // Filter products based on search term with error handling
  useEffect(() => {
    try {
      if (!products) return;

      if (!searchTerm || searchTerm.trim() === '') {
        setFilteredProducts(products);
      } else {
        const lowerSearchTerm = searchTerm.toLowerCase();
        const filtered = products.filter(product => {
          try {
            return (
              ((product.materialDescription || '').toLowerCase()).includes(lowerSearchTerm) ||
              ((product.description || '').toLowerCase()).includes(lowerSearchTerm) ||
              ((product.oldMaterialNo || '').toLowerCase()).includes(lowerSearchTerm)
            );
          } catch (err) {
            console.error('Error filtering product:', product, err);
            return false;
          }
        });
        setFilteredProducts(filtered);
      }
    } catch (err) {
      console.error('Error in filtering products:', err);
      setFilteredProducts(products); // Return all products if there's an error
    }
  }, [searchTerm, products]);

  // Handle product selection
  const handleSelectProduct = (product: Product) => {
    onProductSelect(product);
    setIsDropdownOpen(false);
    setSearchTerm('');
  };

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return <div>Loading products...</div>;
  }

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label}
      </label>

      {/* Product Search and Dropdown */}
      <form
        className="relative"
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          return false;
        }}
      >
        <div className="flex items-center border rounded-lg overflow-hidden">
          <div className="px-3 text-gray-400">
            <Search size={18} />
          </div>
          <input
            type="text"
            placeholder="Search products by name, description or code"
            value={searchTerm}
            onChange={e => {
              e.preventDefault();
              e.stopPropagation();
              setSearchTerm(e.target.value);
              setIsDropdownOpen(true);
            }}
            onFocus={(e) => {
              e.preventDefault();
              setIsDropdownOpen(true);
            }}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault();
                e.stopPropagation();
              }
            }}
            className="flex-1 py-2 px-2 outline-none no-navigation"
          />
          {searchTerm && (
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setSearchTerm('');
              }}
              className="px-3 text-gray-400 hover:text-gray-600"
            >
              <Search size={16} />
            </button>
          )}
        </div>

        {isDropdownOpen && filteredProducts.length > 0 && (
          <div className="absolute z-10 mt-1 w-full bg-white border rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {filteredProducts.map(product => (
              <div
                key={product.id}
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleSelectProduct(product);
                }}
              >
                <div className="font-medium">{product.materialDescription || 'No Name'}</div>
                <div className="text-sm text-gray-600">{product.description || 'No Description'}</div>
                <div className="text-sm text-gray-500">
                  Code: {product.oldMaterialNo || 'N/A'} •
                  Price: {formatCurrency(product.price || 0)}
                </div>
              </div>
            ))}
          </div>
        )}
      </form>

      {/* Selected Product */}
      {selectedProduct && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="font-medium text-green-800">{selectedProduct.materialDescription}</div>
          <div className="text-sm text-green-700">Code: {selectedProduct.oldMaterialNo}</div>
          <div className="text-sm font-bold text-green-900 mt-1">Price: {formatCurrency(selectedProduct.price)}</div>
        </div>
      )}
    </div>
  );
}
