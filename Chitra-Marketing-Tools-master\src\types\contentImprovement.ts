/**
 * Types for AI-Powered Content Improvement
 */

import { ContentType, SocialMediaPlatform } from './socialMedia';

/**
 * Content Improvement Target
 */
export enum ContentImprovementTarget {
  ENGAGEMENT = 'engagement',
  REACH = 'reach',
  CONVERSION = 'conversion',
  BRAND_AWARENESS = 'brand_awareness',
  EDUCATION = 'education',
  COMMUNITY_BUILDING = 'community_building'
}

/**
 * Content Improvement Tone
 */
export enum ContentImprovementTone {
  PROFESSIONAL = 'professional',
  CASUAL = 'casual',
  FRIENDLY = 'friendly',
  AUTHORITATIVE = 'authoritative',
  HUMOROUS = 'humorous',
  INSPIRATIONAL = 'inspirational',
  EDUCATIONAL = 'educational'
}

/**
 * Content Analysis Request
 */
export interface ContentAnalysisRequest {
  content: string;
  platform: SocialMediaPlatform;
  contentType: ContentType;
  target?: ContentImprovementTarget;
  language?: 'id' | 'en';
}

/**
 * Content Analysis Response
 */
export interface ContentAnalysisResponse {
  score: number; // 0-100 overall score
  readability: {
    score: number; // 0-100
    level: 'easy' | 'medium' | 'difficult';
    suggestions: string[];
  };
  engagement: {
    score: number; // 0-100
    strengths: string[];
    weaknesses: string[];
  };
  seo: {
    score: number; // 0-100
    suggestions: string[];
  };
  tone: {
    detected: ContentImprovementTone;
    consistency: number; // 0-100
    suggestions: string[];
  };
  structure: {
    score: number; // 0-100
    suggestions: string[];
  };
  callToAction: {
    present: boolean;
    strength: number; // 0-100
    suggestions: string[];
  };
  improvements: {
    priority: 'high' | 'medium' | 'low';
    description: string;
    suggestion: string;
  }[];
  improvedVersion: string;
}

/**
 * Content Improvement Request
 */
export interface ContentImprovementRequest {
  content: string;
  platform: SocialMediaPlatform;
  contentType: ContentType;
  target: ContentImprovementTarget;
  tone: ContentImprovementTone;
  language: 'id' | 'en';
  focusKeywords?: string[];
  maxLength?: number;
  includeEmojis?: boolean;
  includeHashtags?: boolean;
  improvementAreas?: ('readability' | 'engagement' | 'seo' | 'tone' | 'structure' | 'callToAction')[];
}

/**
 * Content Improvement Response
 */
export interface ContentImprovementResponse {
  originalContent: string;
  improvedContent: string;
  improvements: {
    type: 'readability' | 'engagement' | 'seo' | 'tone' | 'structure' | 'callToAction';
    description: string;
  }[];
  score: {
    before: number; // 0-100
    after: number; // 0-100
  };
  suggestedHashtags?: string[];
}

/**
 * A/B Test Content Variation
 */
export interface ContentVariation {
  id: string;
  content: string;
  description: string;
  focus: 'readability' | 'engagement' | 'seo' | 'tone' | 'structure' | 'callToAction';
  score: number; // 0-100
}

/**
 * A/B Test Request
 */
export interface ContentABTestRequest {
  originalContent: string;
  platform: SocialMediaPlatform;
  contentType: ContentType;
  target: ContentImprovementTarget;
  language: 'id' | 'en';
  variationCount: number; // Number of variations to generate (2-4)
  focusAreas: ('readability' | 'engagement' | 'seo' | 'tone' | 'structure' | 'callToAction')[];
}

/**
 * A/B Test Response
 */
export interface ContentABTestResponse {
  originalContent: string;
  variations: ContentVariation[];
  recommendedVariationId: string;
}

/**
 * Content Improvement History Entry
 */
export interface ContentImprovementHistoryEntry {
  id: string;
  originalContent: string;
  improvedContent: string;
  platform: SocialMediaPlatform;
  contentType: ContentType;
  target: ContentImprovementTarget;
  tone: ContentImprovementTone;
  score: {
    before: number;
    after: number;
  };
  createdAt: Date;
  appliedToPostId?: string;
}
