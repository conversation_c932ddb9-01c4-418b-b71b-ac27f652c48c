// Script to check if a template with the specified UUID exists
const targetUuid = "2a08939d-dfb6-4b63-b0da-4ea510947b7b";
const TEMPLATES_STORAGE_KEY = 'chitra_templates';

// Get templates from localStorage
function getAllTemplates() {
  const templatesJson = localStorage.getItem(TEMPLATES_STORAGE_KEY);
  if (!templatesJson) {
    console.log("No templates found in localStorage");
    return [];
  }

  try {
    return JSON.parse(templatesJson);
  } catch (error) {
    console.error('Error parsing templates from localStorage:', error);
    return [];
  }
}

// Find template by ID
function getTemplateById(id) {
  const templates = getAllTemplates();
  const template = templates.find(t => t.id === id);
  return template || null;
}

// Main function
function checkTemplate() {
  console.log(`Checking for template with UUID: ${targetUuid}`);
  
  const template = getTemplateById(targetUuid);
  
  if (template) {
    console.log("Template found!");
    console.log("Template details:");
    console.log("Name:", template.name);
    console.log("Type:", template.type);
    console.log("Created:", new Date(template.createdAt).toLocaleString());
    console.log("Updated:", new Date(template.updatedAt).toLocaleString());
    console.log("Variables:", template.detectedVariables);
    console.log("File URL:", template.fileUrl);
  } else {
    console.log("No template found with this UUID");
    
    // List all templates
    const allTemplates = getAllTemplates();
    console.log(`Found ${allTemplates.length} templates in total:`);
    allTemplates.forEach((t, index) => {
      console.log(`${index + 1}. ${t.name} (ID: ${t.id})`);
    });
  }
}

// Run the check
checkTemplate();
