import { PromoSimulation, PromoStatus } from '../types/promotion';

// Local storage key for promo simulations
const PROMO_SIMULATIONS_STORAGE_KEY = 'promoSimulations';
const PROMO_TEMPLATES_STORAGE_KEY = 'promoTemplates';

/**
 * Save a promo simulation to local storage
 */
export const savePromoSimulation = (simulation: PromoSimulation): PromoSimulation => {
  try {
    // Generate ID if not provided
    if (!simulation.id) {
      simulation.id = `promo-${Date.now()}`;
    }
    
    // Set timestamps
    simulation.createdAt = simulation.createdAt || new Date();
    simulation.updatedAt = new Date();
    
    // Get existing simulations
    const existingSimulations = getPromoSimulations();
    
    // Check if this is an update or a new simulation
    const index = existingSimulations.findIndex(s => s.id === simulation.id);
    
    if (index >= 0) {
      // Update existing simulation
      existingSimulations[index] = simulation;
    } else {
      // Add new simulation
      existingSimulations.push(simulation);
    }
    
    // Save to local storage
    localStorage.setItem(
      PROMO_SIMULATIONS_STORAGE_KEY,
      JSON.stringify(existingSimulations)
    );
    
    return simulation;
  } catch (error) {
    console.error('Error saving promo simulation:', error);
    throw error;
  }
};

/**
 * Get all promo simulations from local storage
 */
export const getPromoSimulations = (): PromoSimulation[] => {
  try {
    const simulationsJson = localStorage.getItem(PROMO_SIMULATIONS_STORAGE_KEY);
    
    if (!simulationsJson) {
      return [];
    }
    
    const simulations = JSON.parse(simulationsJson) as PromoSimulation[];
    
    // Convert string dates to Date objects
    return simulations.map(simulation => ({
      ...simulation,
      createdAt: simulation.createdAt ? new Date(simulation.createdAt) : undefined,
      updatedAt: simulation.updatedAt ? new Date(simulation.updatedAt) : undefined
    }));
  } catch (error) {
    console.error('Error getting promo simulations:', error);
    return [];
  }
};

/**
 * Get a promo simulation by ID
 */
export const getPromoSimulationById = (id: string): PromoSimulation | null => {
  try {
    const simulations = getPromoSimulations();
    const simulation = simulations.find(s => s.id === id);
    
    return simulation || null;
  } catch (error) {
    console.error('Error getting promo simulation by ID:', error);
    return null;
  }
};

/**
 * Delete a promo simulation
 */
export const deletePromoSimulation = (id: string): boolean => {
  try {
    const simulations = getPromoSimulations();
    const filteredSimulations = simulations.filter(s => s.id !== id);
    
    if (filteredSimulations.length === simulations.length) {
      // No simulation was removed
      return false;
    }
    
    localStorage.setItem(
      PROMO_SIMULATIONS_STORAGE_KEY,
      JSON.stringify(filteredSimulations)
    );
    
    return true;
  } catch (error) {
    console.error('Error deleting promo simulation:', error);
    return false;
  }
};

/**
 * Update the status of a promo simulation
 */
export const updatePromoStatus = (id: string, status: PromoStatus): PromoSimulation | null => {
  try {
    const simulation = getPromoSimulationById(id);
    
    if (!simulation) {
      return null;
    }
    
    const updatedSimulation: PromoSimulation = {
      ...simulation,
      config: {
        ...simulation.config,
        status
      },
      updatedAt: new Date()
    };
    
    savePromoSimulation(updatedSimulation);
    
    return updatedSimulation;
  } catch (error) {
    console.error('Error updating promo status:', error);
    return null;
  }
};

/**
 * Save a promo simulation as a template
 */
export const savePromoTemplate = (simulation: PromoSimulation): PromoSimulation => {
  try {
    // Generate ID if not provided
    if (!simulation.id) {
      simulation.id = `template-${Date.now()}`;
    }
    
    // Set timestamps and template flag
    simulation.createdAt = simulation.createdAt || new Date();
    simulation.updatedAt = new Date();
    simulation.isTemplate = true;
    
    // Get existing templates
    const existingTemplates = getPromoTemplates();
    
    // Check if this is an update or a new template
    const index = existingTemplates.findIndex(t => t.id === simulation.id);
    
    if (index >= 0) {
      // Update existing template
      existingTemplates[index] = simulation;
    } else {
      // Add new template
      existingTemplates.push(simulation);
    }
    
    // Save to local storage
    localStorage.setItem(
      PROMO_TEMPLATES_STORAGE_KEY,
      JSON.stringify(existingTemplates)
    );
    
    return simulation;
  } catch (error) {
    console.error('Error saving promo template:', error);
    throw error;
  }
};

/**
 * Get all promo templates from local storage
 */
export const getPromoTemplates = (): PromoSimulation[] => {
  try {
    const templatesJson = localStorage.getItem(PROMO_TEMPLATES_STORAGE_KEY);
    
    if (!templatesJson) {
      return [];
    }
    
    const templates = JSON.parse(templatesJson) as PromoSimulation[];
    
    // Convert string dates to Date objects
    return templates.map(template => ({
      ...template,
      createdAt: template.createdAt ? new Date(template.createdAt) : undefined,
      updatedAt: template.updatedAt ? new Date(template.updatedAt) : undefined
    }));
  } catch (error) {
    console.error('Error getting promo templates:', error);
    return [];
  }
};

/**
 * Get a promo template by ID
 */
export const getPromoTemplateById = (id: string): PromoSimulation | null => {
  try {
    const templates = getPromoTemplates();
    const template = templates.find(t => t.id === id);
    
    return template || null;
  } catch (error) {
    console.error('Error getting promo template by ID:', error);
    return null;
  }
};

/**
 * Delete a promo template
 */
export const deletePromoTemplate = (id: string): boolean => {
  try {
    const templates = getPromoTemplates();
    const filteredTemplates = templates.filter(t => t.id !== id);
    
    if (filteredTemplates.length === templates.length) {
      // No template was removed
      return false;
    }
    
    localStorage.setItem(
      PROMO_TEMPLATES_STORAGE_KEY,
      JSON.stringify(filteredTemplates)
    );
    
    return true;
  } catch (error) {
    console.error('Error deleting promo template:', error);
    return false;
  }
};
