import React, { useState, useEffect, useRef } from 'react';
import {
  CustomerPersona,
  CustomerPersonaType,
  NegotiationMessage,
  NegotiationSession
} from '../types/negotiation';
import { Product } from '../types';
import { KnowledgeEntry } from '../types/knowledgeBase';
import {
  CUSTOMER_PERSONAS,
  createNegotiationSession,
  sendMessageToAI,
  evaluateNegotiation
} from '../services/negotiationService';
import { fetchProducts } from '../services/productService';
import { formatCurrency } from '../utils/pricing';
import { useNavigate } from 'react-router-dom';
import {
  MessageSquare,
  Send,
  User,
  ShoppingCart,
  Percent,
  ArrowRight,
  Loader2,
  RefreshCw,
  CheckCircle2,
  AlertTriangle,
  Database
} from 'lucide-react';
import KnowledgeBaseSelector from '../components/KnowledgeBaseSelector';

export default function NegotiationSimulator() {
  const navigate = useNavigate();

  // State for products
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<{ product: Product; quantity: number }[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);

  // State for customer persona
  const [selectedPersonaType, setSelectedPersonaType] = useState<CustomerPersonaType>(CustomerPersonaType.PRICE_SENSITIVE);
  const [selectedPersona, setSelectedPersona] = useState<CustomerPersona>(CUSTOMER_PERSONAS[CustomerPersonaType.PRICE_SENSITIVE]);

  // State for knowledge base
  const [selectedKnowledgeEntries, setSelectedKnowledgeEntries] = useState<KnowledgeEntry[]>([]);

  // State for negotiation
  const [targetMargin, setTargetMargin] = useState<number>(20);
  const [negotiationSession, setNegotiationSession] = useState<NegotiationSession | null>(null);
  const [userMessage, setUserMessage] = useState('');
  const [isAiThinking, setIsAiThinking] = useState(false);
  const [isEvaluating, setIsEvaluating] = useState(false);

  // Refs
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  const isSubmittingRef = useRef<boolean>(false);

  // Load products on component mount
  useEffect(() => {
    const loadProducts = async () => {
      setIsLoadingProducts(true);
      try {
        const loadedProducts = await fetchProducts();
        setProducts(loadedProducts);
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    loadProducts();
  }, []);

  // Update selected persona when persona type changes
  useEffect(() => {
    setSelectedPersona(CUSTOMER_PERSONAS[selectedPersonaType]);
  }, [selectedPersonaType]);

  // Scroll to bottom of chat when messages change
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [negotiationSession?.messages]);

  // Start a new negotiation session
  const startNegotiation = () => {
    if (selectedProducts.length === 0) {
      alert('Please select at least one product to start the negotiation.');
      return;
    }

    const session = createNegotiationSession(
      selectedPersonaType,
      selectedProducts,
      targetMargin,
      selectedKnowledgeEntries
    );

    setNegotiationSession(session);

    // Automatically send first message from AI
    handleFirstAiMessage(session);
  };

  // Handle first AI message to start the conversation
  const handleFirstAiMessage = async (session: NegotiationSession) => {
    setIsAiThinking(true);

    try {
      // Add a placeholder "thinking" message
      const thinkingMessage: NegotiationMessage = {
        id: `thinking-${Date.now()}`,
        sender: 'ai',
        content: '...',
        timestamp: new Date(),
        isThinking: true
      };

      setNegotiationSession(prev => {
        if (!prev) return session;
        return {
          ...prev,
          messages: [...prev.messages, thinkingMessage]
        };
      });

      // Send empty message to get AI to start the conversation
      const response = await sendMessageToAI(session, '');

      // Remove the thinking message and update with actual response
      setNegotiationSession(prev => {
        if (!prev) return session;
        const updatedMessages = prev.messages.filter(msg => !msg.isThinking);
        const aiMessage: NegotiationMessage = {
          id: `msg-${Date.now()}`,
          sender: 'ai',
          content: response.message,
          timestamp: new Date()
        };
        return {
          ...prev,
          messages: [...updatedMessages, aiMessage]
        };
      });
    } catch (error) {
      console.error('Error getting first AI message:', error);

      // Remove the thinking message and add an error message
      setNegotiationSession(prev => {
        if (!prev) return session;
        const updatedMessages = prev.messages.filter(msg => !msg.isThinking);
        const errorMessage: NegotiationMessage = {
          id: `error-${Date.now()}`,
          sender: 'ai',
          content: `Halo, saya ${selectedPersona.name} dari ${selectedPersona.company}. Saya tertarik dengan produk ban ${selectedProducts[0]?.product.materialDescription || '10.00 - 20 GT MILLER'} dengan harga ${formatCurrency(selectedProducts[0]?.product.price || 3157882)} per unit, tapi sedang ada masalah koneksi. Bisa kita coba lagi sebentar?`,
          timestamp: new Date()
        };
        return {
          ...prev,
          messages: [...updatedMessages, errorMessage]
        };
      });
    } finally {
      setIsAiThinking(false);
    }
  };

  // This section has been refactored - the sendMessage functionality is now directly in the button and textarea handlers

  // End the negotiation and evaluate
  const endNegotiation = async () => {
    if (!negotiationSession) return;

    setIsEvaluating(true);

    try {
      const evaluation = await evaluateNegotiation(negotiationSession);

      // Navigate to results page with the session data
      navigate('/negotiation-results', {
        state: {
          session: negotiationSession,
          evaluation
        }
      });
    } catch (error) {
      console.error('Error evaluating negotiation:', error);
      alert('There was an error evaluating the negotiation. Please try again.');
    } finally {
      setIsEvaluating(false);
    }
  };

  // Reset the negotiation
  const resetNegotiation = () => {
    if (window.confirm('Are you sure you want to reset the negotiation? All progress will be lost.')) {
      setNegotiationSession(null);
      setUserMessage('');
    }
  };

  // Add a product to the selected products
  const addProduct = (product: Product) => {
    setSelectedProducts(prev => {
      const existing = prev.find(item => item.product.id === product.id);
      if (existing) {
        return prev.map(item =>
          item.product.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [...prev, { product, quantity: 1 }];
      }
    });
  };

  // Remove a product from the selected products
  const removeProduct = (productId: string) => {
    setSelectedProducts(prev => prev.filter(item => item.product.id !== productId));
  };

  // Update product quantity
  const updateProductQuantity = (productId: string, quantity: number) => {
    if (quantity < 1) return;

    setSelectedProducts(prev =>
      prev.map(item =>
        item.product.id === productId
          ? { ...item, quantity }
          : item
      )
    );
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-semibold mb-6">AI Negotiation Simulator</h1>

      {!negotiationSession ? (
        // Setup screen
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Customer Persona Selection */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-medium mb-4 flex items-center">
              <User className="mr-2 text-blue-500" />
              Customer Persona
            </h2>

            <div className="space-y-4">
              {Object.values(CustomerPersonaType).map(personaType => (
                <div
                  key={personaType}
                  className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                    selectedPersonaType === personaType
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50/50'
                  }`}
                  onClick={() => setSelectedPersonaType(personaType)}
                >
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium">{CUSTOMER_PERSONAS[personaType].name}</h3>
                    <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-700">
                      {personaType.replace('_', ' ')}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{CUSTOMER_PERSONAS[personaType].company}</p>
                  <p className="text-sm mt-2">{CUSTOMER_PERSONAS[personaType].description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Product Selection */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-medium mb-4 flex items-center">
              <ShoppingCart className="mr-2 text-green-500" />
              Select Products
            </h2>

            {isLoadingProducts ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="animate-spin text-blue-500 mr-2" />
                <span>Loading products...</span>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="max-h-[400px] overflow-y-auto pr-2">
                  {products.map(product => (
                    <div
                      key={product.id}
                      className="p-3 border rounded-md mb-2 hover:bg-gray-50 cursor-pointer"
                      onClick={() => addProduct(product)}
                    >
                      <div className="flex justify-between">
                        <span className="font-medium">{product.materialDescription}</span>
                        <span className="text-green-600 font-medium">{formatCurrency(product.price)}</span>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        Material: {product.materialNumber}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Negotiation Settings */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-medium mb-4 flex items-center">
              <Percent className="mr-2 text-purple-500" />
              Negotiation Settings
            </h2>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Target Margin (%)
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={targetMargin}
                  onChange={e => setTargetMargin(Number(e.target.value))}
                  className="w-full p-2 border rounded-md"
                />
                <p className="text-xs text-gray-500 mt-1">
                  This is your target profit margin. The AI will evaluate if you maintained this margin during negotiation.
                </p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Selected Products</h3>
                {selectedProducts.length === 0 ? (
                  <p className="text-sm text-gray-500">No products selected yet</p>
                ) : (
                  <div className="space-y-2 max-h-[200px] overflow-y-auto pr-2">
                    {selectedProducts.map(item => (
                      <div key={item.product.id} className="flex items-center justify-between p-2 border rounded-md">
                        <div className="flex-1">
                          <p className="font-medium text-sm">{item.product.materialDescription}</p>
                          <p className="text-xs text-gray-600">{formatCurrency(item.product.price)} per unit</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => updateProductQuantity(item.product.id, item.quantity - 1)}
                            className="p-1 rounded-md bg-gray-100 hover:bg-gray-200"
                          >
                            -
                          </button>
                          <span className="w-8 text-center">{item.quantity}</span>
                          <button
                            onClick={() => updateProductQuantity(item.product.id, item.quantity + 1)}
                            className="p-1 rounded-md bg-gray-100 hover:bg-gray-200"
                          >
                            +
                          </button>
                          <button
                            onClick={() => removeProduct(item.product.id)}
                            className="p-1 rounded-md text-red-500 hover:bg-red-50"
                          >
                            ×
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <button
                onClick={startNegotiation}
                disabled={selectedProducts.length === 0}
                className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowRight className="mr-2" />
                Start Negotiation
              </button>
            </div>
          </div>

          {/* Knowledge Base Selector */}
          <div className="lg:col-span-3">
            <KnowledgeBaseSelector
              onKnowledgeEntriesSelected={setSelectedKnowledgeEntries}
              initialSelectedEntries={selectedKnowledgeEntries}
            />
          </div>
        </div>
      ) : (
        // Negotiation chat interface
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Customer Info */}
          <div className="lg:col-span-1">
            <div className="bg-white p-6 rounded-lg shadow-sm border sticky top-20">
              <div className="text-center mb-4">
                <div className="w-20 h-20 bg-blue-100 rounded-full mx-auto flex items-center justify-center">
                  <User size={32} className="text-blue-500" />
                </div>
                <h2 className="text-lg font-medium mt-2">{selectedPersona.name}</h2>
                <p className="text-sm text-gray-600">{selectedPersona.company}</p>
              </div>

              <div className="space-y-4 text-sm">
                <div>
                  <h3 className="font-medium text-gray-700">Negotiation Style</h3>
                  <p className="mt-1 text-gray-600">{selectedPersona.negotiationStyle}</p>
                </div>

                <div>
                  <h3 className="font-medium text-gray-700">Key Concerns</h3>
                  <ul className="mt-1 list-disc pl-5 text-gray-600">
                    {selectedPersona.painPoints.map((point, index) => (
                      <li key={index}>{point}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="font-medium text-gray-700">Values</h3>
                  <ul className="mt-1 list-disc pl-5 text-gray-600">
                    {selectedPersona.valueFeatures.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>

                <div className="pt-4 border-t">
                  <h3 className="font-medium text-gray-700">Products Discussed</h3>
                  <div className="mt-2 space-y-2">
                    {selectedProducts.map(item => (
                      <div key={item.product.id} className="p-2 bg-gray-50 rounded-md">
                        <p className="font-medium text-sm">{item.product.materialDescription}</p>
                        <div className="flex justify-between text-xs mt-1">
                          <span>{item.quantity} units</span>
                          <span className="text-green-600">{formatCurrency(item.product.price)} each</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h3 className="font-medium text-gray-700">Target Margin</h3>
                  <p className="mt-1 text-gray-600">{targetMargin}%</p>
                </div>

                <div className="pt-4 flex space-x-2">
                  <button
                    onClick={endNegotiation}
                    disabled={isEvaluating || negotiationSession.messages.length < 4}
                    className="flex-1 py-2 px-3 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isEvaluating ? (
                      <>
                        <Loader2 size={16} className="mr-1 animate-spin" />
                        Evaluating...
                      </>
                    ) : (
                      <>
                        <CheckCircle2 size={16} className="mr-1" />
                        End & Evaluate
                      </>
                    )}
                  </button>

                  <button
                    onClick={resetNegotiation}
                    className="py-2 px-3 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center justify-center"
                  >
                    <RefreshCw size={16} className="mr-1" />
                    Reset
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Interface */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm border h-[calc(100vh-200px)] flex flex-col">
              {/* Chat Header */}
              <div className="p-4 border-b flex items-center">
                <MessageSquare className="text-blue-500 mr-2" />
                <h2 className="font-medium">Negotiation with {selectedPersona.name}</h2>
              </div>

              {/* Chat Messages */}
              <div
                ref={chatContainerRef}
                className="flex-1 overflow-y-auto p-4 space-y-4"
              >
                {negotiationSession.messages.map(message => (
                  <div
                    key={message.id}
                    className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${
                        message.sender === 'user'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-800'
                      } ${message.isThinking ? 'animate-pulse' : ''}`}
                    >
                      {message.isThinking ? (
                        <div className="flex items-center space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                        </div>
                      ) : (
                        <p className="whitespace-pre-line">{message.content}</p>
                      )}
                      <div
                        className={`text-xs mt-1 ${
                          message.sender === 'user' ? 'text-blue-200' : 'text-gray-500'
                        }`}
                      >
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </div>
                    </div>
                  </div>
                ))}

                {negotiationSession.messages.length === 0 && (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <p>Starting conversation with {selectedPersona.name}...</p>
                  </div>
                )}
              </div>

              {/* Chat Input */}
              <div className="p-4 border-t">
                <div className="flex items-end space-x-2">
                  <textarea
                    ref={messageInputRef}
                    value={userMessage}
                    onChange={e => setUserMessage(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey && !isAiThinking && !isSubmittingRef.current && userMessage.trim()) {
                        e.preventDefault();
                        const messageToSend = userMessage;
                        setUserMessage('');
                        isSubmittingRef.current = true;
                        setIsAiThinking(true);

                        // Add user message to the session
                        const userMsg = {
                          id: `msg-${Date.now()}`,
                          sender: 'user',
                          content: messageToSend,
                          timestamp: new Date()
                        };

                        // Add thinking message
                        const thinkingMsg = {
                          id: `thinking-${Date.now()}`,
                          sender: 'ai',
                          content: '...',
                          timestamp: new Date(),
                          isThinking: true
                        };

                        setNegotiationSession(prev => {
                          if (!prev) return prev;
                          return {
                            ...prev,
                            messages: [...prev.messages, userMsg, thinkingMsg]
                          };
                        });

                        // Call AI
                        setTimeout(() => {
                          sendMessageToAI(negotiationSession!, messageToSend)
                            .then(response => {
                              setNegotiationSession(prev => {
                                if (!prev) return prev;
                                const updatedMessages = prev.messages.filter(msg => !msg.isThinking);
                                const aiMessage = {
                                  id: `msg-${Date.now()}`,
                                  sender: 'ai',
                                  content: response.message,
                                  timestamp: new Date()
                                };
                                return {
                                  ...prev,
                                  messages: [...updatedMessages, aiMessage]
                                };
                              });
                            })
                            .catch(error => {
                              console.error('Error sending message to AI:', error);
                              setNegotiationSession(prev => {
                                if (!prev) return prev;
                                const updatedMessages = prev.messages.filter(msg => !msg.isThinking);
                                const errorMessage = {
                                  id: `error-${Date.now()}`,
                                  sender: 'ai',
                                  content: "Maaf, sepertinya ada kendala teknis di sisi saya. Bisa kita lanjutkan percakapan ini sebentar lagi?",
                                  timestamp: new Date()
                                };
                                return {
                                  ...prev,
                                  messages: [...updatedMessages, errorMessage]
                                };
                              });
                            })
                            .finally(() => {
                              setIsAiThinking(false);
                              isSubmittingRef.current = false;
                              if (messageInputRef.current) {
                                messageInputRef.current.focus();
                              }
                            });
                        }, 100);
                      }
                    }}
                    placeholder="Type your message..."
                    className="flex-1 p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                    rows={2}
                    disabled={isAiThinking}
                  />
                  <button
                    onClick={() => {
                      if (!isAiThinking && !isSubmittingRef.current && userMessage.trim()) {
                        const messageToSend = userMessage;
                        setUserMessage('');
                        isSubmittingRef.current = true;
                        setIsAiThinking(true);

                        // Add user message to the session
                        const userMsg = {
                          id: `msg-${Date.now()}`,
                          sender: 'user',
                          content: messageToSend,
                          timestamp: new Date()
                        };

                        // Add thinking message
                        const thinkingMsg = {
                          id: `thinking-${Date.now()}`,
                          sender: 'ai',
                          content: '...',
                          timestamp: new Date(),
                          isThinking: true
                        };

                        setNegotiationSession(prev => {
                          if (!prev) return prev;
                          return {
                            ...prev,
                            messages: [...prev.messages, userMsg, thinkingMsg]
                          };
                        });

                        // Call AI
                        setTimeout(() => {
                          sendMessageToAI(negotiationSession!, messageToSend)
                            .then(response => {
                              setNegotiationSession(prev => {
                                if (!prev) return prev;
                                const updatedMessages = prev.messages.filter(msg => !msg.isThinking);
                                const aiMessage = {
                                  id: `msg-${Date.now()}`,
                                  sender: 'ai',
                                  content: response.message,
                                  timestamp: new Date()
                                };
                                return {
                                  ...prev,
                                  messages: [...updatedMessages, aiMessage]
                                };
                              });
                            })
                            .catch(error => {
                              console.error('Error sending message to AI:', error);
                              setNegotiationSession(prev => {
                                if (!prev) return prev;
                                const updatedMessages = prev.messages.filter(msg => !msg.isThinking);
                                const errorMessage = {
                                  id: `error-${Date.now()}`,
                                  sender: 'ai',
                                  content: "Maaf, sepertinya ada kendala teknis di sisi saya. Bisa kita lanjutkan percakapan ini sebentar lagi?",
                                  timestamp: new Date()
                                };
                                return {
                                  ...prev,
                                  messages: [...updatedMessages, errorMessage]
                                };
                              });
                            })
                            .finally(() => {
                              setIsAiThinking(false);
                              isSubmittingRef.current = false;
                              if (messageInputRef.current) {
                                messageInputRef.current.focus();
                              }
                            });
                        }, 100);
                      }
                    }}
                    disabled={!userMessage.trim() || isAiThinking}
                    className="p-3 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isAiThinking ? (
                      <Loader2 className="animate-spin" />
                    ) : (
                      <Send />
                    )}
                  </button>
                </div>

                {negotiationSession.messages.length < 4 && (
                  <div className="mt-2 flex items-center text-xs text-amber-600">
                    <AlertTriangle size={14} className="mr-1" />
                    <span>Continue the conversation before ending the negotiation.</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
