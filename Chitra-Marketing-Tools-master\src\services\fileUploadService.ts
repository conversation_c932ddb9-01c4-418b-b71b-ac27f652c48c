/**
 * Service for handling file uploads
 */

// Maximum file size in bytes (5 MB)
export const MAX_FILE_SIZE = 5 * 1024 * 1024;

// Allowed file types
export const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'image/jpeg',
  'image/png',
  'image/gif'
];

// Local storage key for uploaded files
const UPLOADED_FILES_STORAGE_KEY = 'uploadedFiles';

// Interface for uploaded file metadata
export interface UploadedFile {
  id: string;
  name: string;
  type: string;
  size: number;
  dataUrl: string;
  uploadDate: Date;
  relatedId?: string; // ID of related entity (e.g., promo simulation)
}

/**
 * Upload a file and store it in local storage
 */
export const uploadFile = (
  file: File,
  relatedId?: string
): Promise<UploadedFile> => {
  return new Promise((resolve, reject) => {
    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      reject(new Error(`File size exceeds the maximum allowed size of ${MAX_FILE_SIZE / (1024 * 1024)} MB`));
      return;
    }
    
    // Validate file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      reject(new Error('File type not allowed. Please upload a PDF or image file.'));
      return;
    }
    
    // Read file as data URL
    const reader = new FileReader();
    
    reader.onload = (event) => {
      try {
        const dataUrl = event.target?.result as string;
        
        // Create uploaded file metadata
        const uploadedFile: UploadedFile = {
          id: `file-${Date.now()}`,
          name: file.name,
          type: file.type,
          size: file.size,
          dataUrl,
          uploadDate: new Date(),
          relatedId
        };
        
        // Get existing uploaded files
        const existingFiles = getUploadedFiles();
        
        // Add new file
        existingFiles.push(uploadedFile);
        
        // Save to local storage
        localStorage.setItem(
          UPLOADED_FILES_STORAGE_KEY,
          JSON.stringify(existingFiles)
        );
        
        resolve(uploadedFile);
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
    
    reader.readAsDataURL(file);
  });
};

/**
 * Get all uploaded files
 */
export const getUploadedFiles = (): UploadedFile[] => {
  try {
    const filesJson = localStorage.getItem(UPLOADED_FILES_STORAGE_KEY);
    
    if (!filesJson) {
      return [];
    }
    
    const files = JSON.parse(filesJson) as UploadedFile[];
    
    // Convert string dates to Date objects
    return files.map(file => ({
      ...file,
      uploadDate: new Date(file.uploadDate)
    }));
  } catch (error) {
    console.error('Error getting uploaded files:', error);
    return [];
  }
};

/**
 * Get uploaded files by related ID
 */
export const getUploadedFilesByRelatedId = (relatedId: string): UploadedFile[] => {
  try {
    const files = getUploadedFiles();
    return files.filter(file => file.relatedId === relatedId);
  } catch (error) {
    console.error('Error getting uploaded files by related ID:', error);
    return [];
  }
};

/**
 * Get an uploaded file by ID
 */
export const getUploadedFileById = (id: string): UploadedFile | null => {
  try {
    const files = getUploadedFiles();
    const file = files.find(f => f.id === id);
    
    return file || null;
  } catch (error) {
    console.error('Error getting uploaded file by ID:', error);
    return null;
  }
};

/**
 * Delete an uploaded file
 */
export const deleteUploadedFile = (id: string): boolean => {
  try {
    const files = getUploadedFiles();
    const filteredFiles = files.filter(f => f.id !== id);
    
    if (filteredFiles.length === files.length) {
      // No file was removed
      return false;
    }
    
    localStorage.setItem(
      UPLOADED_FILES_STORAGE_KEY,
      JSON.stringify(filteredFiles)
    );
    
    return true;
  } catch (error) {
    console.error('Error deleting uploaded file:', error);
    return false;
  }
};
