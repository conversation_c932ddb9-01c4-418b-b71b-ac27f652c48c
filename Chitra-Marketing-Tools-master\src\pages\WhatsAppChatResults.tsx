import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { WhatsAppChatSession, WhatsAppChatEvaluation } from '../types/whatsappChat';
import {
  ArrowLeft,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  BarChart3,
  Award,
  Lightbulb,
  MessageSquare,
  User,
  Clock,
  TrendingUp,
  TrendingDown,
  Minus,
  Download,
  Share2
} from 'lucide-react';

interface LocationState {
  session: WhatsAppChatSession;
  evaluation: WhatsAppChatEvaluation;
}

export default function WhatsAppChatResults() {
  const location = useLocation();
  const navigate = useNavigate();
  const [session, setSession] = useState<WhatsAppChatSession | null>(null);
  const [evaluation, setEvaluation] = useState<WhatsAppChatEvaluation | null>(null);

  useEffect(() => {
    // Get session and evaluation from location state
    const state = location.state as LocationState;

    if (!state || !state.session || !state.evaluation) {
      // If no session or evaluation, redirect to analysis page
      navigate('/whatsapp-chat-analysis');
      return;
    }

    setSession(state.session);
    setEvaluation(state.evaluation);
  }, [location, navigate]);

  // If no session or evaluation, show loading
  if (!session || !evaluation) {
    return (
      <div className="container mx-auto px-4 py-8 flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat hasil analisis...</p>
        </div>
      </div>
    );
  }

  // Get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Get score icon based on value
  const getScoreIcon = (score: number) => {
    if (score >= 80) return <TrendingUp className="text-green-600" />;
    if (score >= 60) return <Minus className="text-yellow-600" />;
    return <TrendingDown className="text-red-600" />;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold">Hasil Analisis Chat WhatsApp</h1>
        <button
          onClick={() => navigate('/whatsapp-chat-analysis')}
          className="flex items-center text-blue-600 hover:text-blue-800"
        >
          <ArrowLeft className="mr-1" />
          Kembali ke Analisis
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Chat Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-medium mb-4 flex items-center">
              <MessageSquare className="mr-2 text-blue-500" />
              Ringkasan Chat
            </h2>

            <div className="space-y-4">
              <div>
                <div className="flex items-center mb-2">
                  <User className="mr-2 text-gray-500" size={18} />
                  <h3 className="font-medium">Pelanggan</h3>
                </div>
                <p className="text-gray-700 ml-7">{session.customerName}</p>
              </div>

              <div>
                <div className="flex items-center mb-2">
                  <User className="mr-2 text-gray-500" size={18} />
                  <h3 className="font-medium">Sales</h3>
                </div>
                <p className="text-gray-700 ml-7">{session.salesName}</p>
              </div>

              <div>
                <div className="flex items-center mb-2">
                  <Clock className="mr-2 text-gray-500" size={18} />
                  <h3 className="font-medium">Jumlah Pesan</h3>
                </div>
                <p className="text-gray-700 ml-7">{session.messages.length} pesan</p>
              </div>

              <div>
                <div className="flex items-center mb-2">
                  <BarChart3 className="mr-2 text-gray-500" size={18} />
                  <h3 className="font-medium">Skor Keseluruhan</h3>
                </div>
                <div className="ml-7 flex items-center">
                  <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                    <div
                      className={`h-2.5 rounded-full ${
                        evaluation.overallScore >= 80
                          ? 'bg-green-600'
                          : evaluation.overallScore >= 60
                          ? 'bg-yellow-500'
                          : 'bg-red-600'
                      }`}
                      style={{ width: `${evaluation.overallScore}%` }}
                    ></div>
                  </div>
                  <span className={`font-bold ${getScoreColor(evaluation.overallScore)}`}>
                    {evaluation.overallScore}/100
                  </span>
                </div>
              </div>

              <div className="pt-4 flex space-x-2">
                <button
                  onClick={() => navigate('/whatsapp-chat-analysis')}
                  className="flex-1 py-2 px-3 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center"
                >
                  <ArrowLeft size={16} className="mr-1" />
                  Analisis Baru
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Evaluation Results */}
        <div className="lg:col-span-2">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-medium mb-4 flex items-center">
              <Award className="mr-2 text-purple-500" />
              Evaluasi Chat
            </h2>

            <div className="space-y-6">
              {/* Closing Status */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
                  <div className="flex items-center">
                    <CheckCircle2 size={20} className="text-blue-600 mr-2" />
                    <h3 className="font-medium">Target Closing</h3>
                  </div>
                  <div className="mt-2 flex items-center">
                    {evaluation.closingAchieved ? (
                      <CheckCircle2 size={20} className="text-green-600 mr-2" />
                    ) : (
                      <XCircle size={20} className="text-red-600 mr-2" />
                    )}
                    <p className="text-sm">
                      {evaluation.closingAchieved
                        ? 'Berhasil mencapai closing'
                        : 'Tidak mencapai closing'}
                    </p>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
                  <div className="flex items-center">
                    <BarChart3 size={20} className="text-blue-600 mr-2" />
                    <h3 className="font-medium">Efektivitas Closing</h3>
                  </div>
                  <div className="mt-2 flex items-center">
                    {getScoreIcon(evaluation.closingEffectiveness)}
                    <p className={`text-sm ml-2 ${getScoreColor(evaluation.closingEffectiveness)}`}>
                      {evaluation.closingEffectiveness}/100
                    </p>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-blue-50 border border-blue-200">
                  <div className="flex items-center">
                    <Clock size={20} className="text-blue-600 mr-2" />
                    <h3 className="font-medium">Waktu Respons</h3>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm">
                      Rata-rata: {evaluation.responseTime.average} menit
                    </p>
                    <p className="text-sm mt-1">
                      Penilaian:{' '}
                      <span
                        className={
                          evaluation.responseTime.rating === 'excellent'
                            ? 'text-green-600'
                            : evaluation.responseTime.rating === 'good'
                            ? 'text-blue-600'
                            : evaluation.responseTime.rating === 'average'
                            ? 'text-yellow-600'
                            : 'text-red-600'
                        }
                      >
                        {evaluation.responseTime.rating === 'excellent'
                          ? 'Sangat Baik'
                          : evaluation.responseTime.rating === 'good'
                          ? 'Baik'
                          : evaluation.responseTime.rating === 'average'
                          ? 'Rata-rata'
                          : 'Buruk'}
                      </span>
                    </p>
                  </div>
                </div>
              </div>

              {/* Communication Style */}
              <div className="p-4 bg-gray-50 rounded-lg border">
                <h3 className="font-medium flex items-center mb-3">
                  <MessageSquare size={18} className="text-gray-700 mr-2" />
                  Gaya Komunikasi
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Kejelasan</p>
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className={`h-2 rounded-full ${
                            evaluation.communicationStyle.clarity >= 80
                              ? 'bg-green-600'
                              : evaluation.communicationStyle.clarity >= 60
                              ? 'bg-yellow-500'
                              : 'bg-red-600'
                          }`}
                          style={{ width: `${evaluation.communicationStyle.clarity}%` }}
                        ></div>
                      </div>
                      <span className="text-xs font-medium">
                        {evaluation.communicationStyle.clarity}%
                      </span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Profesionalisme</p>
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className={`h-2 rounded-full ${
                            evaluation.communicationStyle.professionalism >= 80
                              ? 'bg-green-600'
                              : evaluation.communicationStyle.professionalism >= 60
                              ? 'bg-yellow-500'
                              : 'bg-red-600'
                          }`}
                          style={{ width: `${evaluation.communicationStyle.professionalism}%` }}
                        ></div>
                      </div>
                      <span className="text-xs font-medium">
                        {evaluation.communicationStyle.professionalism}%
                      </span>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">Persuasif</p>
                    <div className="flex items-center">
                      <div className="w-full bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className={`h-2 rounded-full ${
                            evaluation.communicationStyle.persuasiveness >= 80
                              ? 'bg-green-600'
                              : evaluation.communicationStyle.persuasiveness >= 60
                              ? 'bg-yellow-500'
                              : 'bg-red-600'
                          }`}
                          style={{ width: `${evaluation.communicationStyle.persuasiveness}%` }}
                        ></div>
                      </div>
                      <span className="text-xs font-medium">
                        {evaluation.communicationStyle.persuasiveness}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Key Insights */}
              <div className="p-4 rounded-lg bg-purple-50 border border-purple-200">
                <div className="flex items-center">
                  <Lightbulb size={20} className="text-purple-600 mr-2" />
                  <h3 className="font-medium">Wawasan Kunci</h3>
                </div>
                <p className="text-sm mt-2">{evaluation.keyInsights}</p>
              </div>

              {/* Strengths and Areas for Improvement */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                  <h3 className="font-medium flex items-center mb-2">
                    <CheckCircle2 size={18} className="text-green-600 mr-2" />
                    Kekuatan
                  </h3>
                  <ul className="space-y-1">
                    {evaluation.strengths.map((strength, index) => (
                      <li key={index} className="text-sm flex items-start">
                        <span className="text-green-600 mr-2">•</span>
                        {strength}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="p-4 bg-amber-50 rounded-lg border border-amber-200">
                  <h3 className="font-medium flex items-center mb-2">
                    <AlertTriangle size={18} className="text-amber-600 mr-2" />
                    Area untuk Perbaikan
                  </h3>
                  <ul className="space-y-1">
                    {evaluation.improvements.map((improvement, index) => (
                      <li key={index} className="text-sm flex items-start">
                        <span className="text-amber-600 mr-2">•</span>
                        {improvement}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Alternative Strategies */}
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h3 className="font-medium flex items-center mb-2">
                  <Lightbulb size={18} className="text-blue-600 mr-2" />
                  Strategi Alternatif
                </h3>
                <ul className="space-y-1">
                  {evaluation.alternativeStrategies.map((strategy, index) => (
                    <li key={index} className="text-sm flex items-start">
                      <span className="text-blue-600 mr-2">•</span>
                      {strategy}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
