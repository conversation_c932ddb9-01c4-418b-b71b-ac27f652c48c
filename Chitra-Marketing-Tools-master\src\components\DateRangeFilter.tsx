import React, { useState, useRef, useEffect } from 'react';
import { Calendar, X, ChevronDown, ChevronUp } from 'lucide-react';
import { format, isValid, parse } from 'date-fns';
import { id } from 'date-fns/locale';

interface DateRangeFilterProps {
  label: string;
  startDate: string;
  endDate: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  className?: string;
}

const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  label,
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Predefined date ranges
  const dateRanges = [
    { label: 'Today', getRange: () => {
      const today = new Date();
      return {
        start: format(today, 'yyyy-MM-dd'),
        end: format(today, 'yyyy-MM-dd')
      };
    }},
    { label: 'Yesterday', getRange: () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      return {
        start: format(yesterday, 'yyyy-MM-dd'),
        end: format(yesterday, 'yyyy-MM-dd')
      };
    }},
    { label: 'This Week', getRange: () => {
      const today = new Date();
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay());
      return {
        start: format(startOfWeek, 'yyyy-MM-dd'),
        end: format(today, 'yyyy-MM-dd')
      };
    }},
    { label: 'Last 7 Days', getRange: () => {
      const today = new Date();
      const last7Days = new Date(today);
      last7Days.setDate(today.getDate() - 6);
      return {
        start: format(last7Days, 'yyyy-MM-dd'),
        end: format(today, 'yyyy-MM-dd')
      };
    }},
    { label: 'This Month', getRange: () => {
      const today = new Date();
      const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      return {
        start: format(startOfMonth, 'yyyy-MM-dd'),
        end: format(today, 'yyyy-MM-dd')
      };
    }},
    { label: 'Last Month', getRange: () => {
      const today = new Date();
      const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
      return {
        start: format(startOfLastMonth, 'yyyy-MM-dd'),
        end: format(endOfLastMonth, 'yyyy-MM-dd')
      };
    }},
    { label: 'This Year', getRange: () => {
      const today = new Date();
      const startOfYear = new Date(today.getFullYear(), 0, 1);
      return {
        start: format(startOfYear, 'yyyy-MM-dd'),
        end: format(today, 'yyyy-MM-dd')
      };
    }},
    { label: 'Last Year', getRange: () => {
      const today = new Date();
      const startOfLastYear = new Date(today.getFullYear() - 1, 0, 1);
      const endOfLastYear = new Date(today.getFullYear() - 1, 11, 31);
      return {
        start: format(startOfLastYear, 'yyyy-MM-dd'),
        end: format(endOfLastYear, 'yyyy-MM-dd')
      };
    }}
  ];

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Apply predefined date range
  const applyDateRange = (range: { start: string, end: string }) => {
    onStartDateChange(range.start);
    onEndDateChange(range.end);
  };

  // Clear date range
  const clearDateRange = (e: React.MouseEvent) => {
    e.stopPropagation();
    onStartDateChange('');
    onEndDateChange('');
  };

  // Format date for display
  const formatDateForDisplay = (dateString: string) => {
    if (!dateString) return '';
    
    try {
      const date = parse(dateString, 'yyyy-MM-dd', new Date());
      if (isValid(date)) {
        return format(date, 'dd MMM yyyy', { locale: id });
      }
      return dateString;
    } catch (e) {
      return dateString;
    }
  };

  // Display selected date range
  const displayDateRange = () => {
    if (!startDate && !endDate) return 'Select date range';
    
    if (startDate && endDate) {
      return `${formatDateForDisplay(startDate)} - ${formatDateForDisplay(endDate)}`;
    }
    
    if (startDate) return `From ${formatDateForDisplay(startDate)}`;
    if (endDate) return `Until ${formatDateForDisplay(endDate)}`;
    
    return 'Select date range';
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label}
      </label>
      <div
        className="flex items-center justify-between w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white cursor-pointer"
        onClick={toggleDropdown}
      >
        <div className="flex items-center">
          <Calendar size={16} className="text-gray-400 mr-2" />
          <span className="flex-grow truncate">{displayDateRange()}</span>
        </div>
        <div className="flex items-center">
          {(startDate || endDate) && (
            <button
              onClick={clearDateRange}
              className="p-1 text-gray-400 hover:text-gray-600"
              aria-label="Clear date range"
            >
              <X size={14} />
            </button>
          )}
          <div className="ml-1 text-gray-400">
            {isOpen ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
          </div>
        </div>
      </div>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
          <div className="p-3 border-b">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs font-medium text-gray-500 mb-1">Start Date</label>
                <input
                  type="date"
                  className="w-full p-2 text-sm border border-gray-300 rounded-md"
                  value={startDate}
                  onChange={(e) => onStartDateChange(e.target.value)}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-500 mb-1">End Date</label>
                <input
                  type="date"
                  className="w-full p-2 text-sm border border-gray-300 rounded-md"
                  value={endDate}
                  onChange={(e) => onEndDateChange(e.target.value)}
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </div>
          </div>

          <div className="p-2">
            <div className="text-xs font-medium text-gray-500 mb-1 px-1">Quick Ranges</div>
            <div className="grid grid-cols-2 gap-1">
              {dateRanges.map((range) => (
                <button
                  key={range.label}
                  className="px-3 py-1 text-sm text-left hover:bg-gray-100 rounded"
                  onClick={() => applyDateRange(range.getRange())}
                >
                  {range.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangeFilter;
