// This is a simple test file to verify Context7 integration
// Run this with: node test-context7.js

console.log("Testing Context7 MCP integration");
console.log("================================");
console.log("");
console.log("To test Context7, run one of the following commands:");
console.log("");
console.log("1. Using the MCP Inspector:");
console.log("   npm run context7:test");
console.log("");
console.log("2. In VS Code:");
console.log("   Open VS Code and ask a question with 'use context7' at the end");
console.log("");
console.log("3. In Cursor:");
console.log("   Open Cursor and ask a question with 'use context7' at the end");
console.log("");
console.log("Example prompts:");
console.log("- Create a React component that fetches data from an API and displays it in a table. use context7");
console.log("- Help me implement a PostgreSQL query to find duplicate records. use context7");
console.log("");
console.log("If Context7 is working correctly, you should see up-to-date documentation and code examples in the response.");
