import React, { useState, useEffect } from 'react';
import { Truck, Search, ChevronLeft, ChevronRight, RefreshCw, Filter, FileSpreadsheet } from 'lucide-react';
import { FleetlistItem, fetchFleetlist, FALLBACK_FLEETLIST } from '../services/fleetlistService';

export default function Fleetlist() {
  const [fleetlist, setFleetlist] = useState<FleetlistItem[]>([]);
  const [filteredFleetlist, setFilteredFleetlist] = useState<FleetlistItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<string>('id_fleet_list');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterCustomer, setFilterCustomer] = useState<string>('all');
  const [uniqueCustomers, setUniqueCustomers] = useState<string[]>([]);
  const [uniqueStatuses, setUniqueStatuses] = useState<string[]>([]);

  // Load fleetlist data
  useEffect(() => {
    loadFleetlist();
  }, []);

  // Filter and sort fleetlist when dependencies change
  useEffect(() => {
    let result = [...fleetlist];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(item =>
        (item.id_fleet_list?.toLowerCase().includes(query) ||
         item.customer?.toLowerCase().includes(query) ||
         item.site?.toLowerCase().includes(query) ||
         item.unit_manufacture?.toLowerCase().includes(query) ||
         item.model?.toLowerCase().includes(query) ||
         item.location?.toLowerCase().includes(query) ||
         item.tire_size?.toLowerCase().includes(query))
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      result = result.filter(item => item.status === filterStatus);
    }

    // Apply customer filter
    if (filterCustomer !== 'all') {
      result = result.filter(item => item.customer === filterCustomer);
    }

    // Apply sorting
    result.sort((a, b) => {
      const aValue = a[sortField] || '';
      const bValue = b[sortField] || '';

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return sortDirection === 'asc'
        ? (aValue > bValue ? 1 : -1)
        : (bValue > aValue ? 1 : -1);
    });

    setFilteredFleetlist(result);
    setCurrentPage(1); // Reset to first page when filters change
  }, [fleetlist, searchQuery, sortField, sortDirection, filterStatus, filterCustomer]);

  // Extract unique values for filters
  useEffect(() => {
    if (fleetlist.length > 0) {
      // Get unique customers
      const customers = Array.from(new Set(
        fleetlist
          .map(item => item.customer)
          .filter(customer => customer) // Remove undefined/null
      )) as string[];

      // Get unique statuses
      const statuses = Array.from(new Set(
        fleetlist
          .map(item => item.status)
          .filter(status => status) // Remove undefined/null
      )) as string[];

      setUniqueCustomers(customers);
      setUniqueStatuses(statuses);
    }
  }, [fleetlist]);

  const loadFleetlist = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Loading fleetlist data...');
      const data = await fetchFleetlist();

      if (data.length === 0) {
        console.warn('No fleetlist data returned from Google Sheets, using fallback data');
        setFleetlist(FALLBACK_FLEETLIST);
        setError('Could not load data from Google Sheets. Showing sample data instead.');
      } else {
        console.log('Fleetlist data loaded successfully:', data.length, 'items');
        setFleetlist(data);

        // Check if we're using fallback data
        const isFallbackData = data === FALLBACK_FLEETLIST;
        if (isFallbackData) {
          setError('Using sample data because the Google Sheets CSV is currently unavailable.');
        }
      }
    } catch (err) {
      console.error('Failed to load fleetlist from Google Sheets:', err);
      setError('Failed to load fleetlist data from Google Sheets. Using sample data instead.');
      setFleetlist(FALLBACK_FLEETLIST);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Calculate pagination
  const totalPages = Math.ceil(filteredFleetlist.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredFleetlist.slice(indexOfFirstItem, indexOfLastItem);

  // Pagination controls
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);
  const nextPage = () => setCurrentPage(prev => Math.min(prev + 1, totalPages));
  const prevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1));

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Truck className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Fleetlist</h1>
        </div>

        <div className="flex space-x-2">
          <a
            href="https://docs.google.com/spreadsheets/d/e/2PACX-1vSRFlBtJQvRB5xMg5276wnm6-aMeR9oli3IxMAG15QHbu79qh2Le8BEUINgomb72j0l60CxExqXAXJy/pub?gid=0&single=true&output=csv"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200"
          >
            <FileSpreadsheet size={16} className="mr-2" />
            View CSV Source
          </a>

          <button
            onClick={loadFleetlist}
            className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh Data
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="col-span-1 md:col-span-2">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search fleetlist..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <div className="flex items-center space-x-2">
              <Filter size={16} className="text-gray-400" />
              <select
                className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">All Statuses</option>
                {uniqueStatuses.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Customer Filter */}
          <div>
            <div className="flex items-center space-x-2">
              <Filter size={16} className="text-gray-400" />
              <select
                className="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                value={filterCustomer}
                onChange={(e) => setFilterCustomer(e.target.value)}
              >
                <option value="all">All Customers</option>
                {uniqueCustomers.map(customer => (
                  <option key={customer} value={customer}>{customer}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Fleetlist Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden" style={{ maxWidth: '100%' }}>
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
            <p className="text-gray-600">Loading fleetlist data...</p>
          </div>
        ) : error ? (
          <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-yellow-700">{error}</p>
                <p className="text-sm text-yellow-600 mt-1">
                  Showing sample data for demonstration purposes. There may be an issue with accessing the Google Sheets CSV data.
                </p>
                <div className="mt-2 flex space-x-2">
                  <button
                    onClick={loadFleetlist}
                    className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200 text-sm font-medium"
                  >
                    Try Again
                  </button>
                  <a
                    href="https://docs.google.com/spreadsheets/d/e/2PACX-1vSRFlBtJQvRB5xMg5276wnm6-aMeR9oli3IxMAG15QHbu79qh2Le8BEUINgomb72j0l60CxExqXAXJy/pub?gid=0&single=true&output=csv"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-3 py-1 bg-green-100 text-green-800 rounded-md hover:bg-green-200 text-sm font-medium flex items-center"
                  >
                    <FileSpreadsheet size={14} className="mr-1" />
                    Check CSV Source
                  </a>
                </div>
              </div>
            </div>
          </div>
        ) : filteredFleetlist.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-600">No fleetlist data found matching your criteria.</p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto" style={{ position: 'relative' }}>
              <div className="overflow-x-auto" style={{ maxWidth: '100%', overflowY: 'visible' }}>
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('id_fleet_list')}
                      >
                        Fleet No {renderSortIndicator('id_fleet_list')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('customer')}
                      >
                        Customer {renderSortIndicator('customer')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('site')}
                      >
                        Site {renderSortIndicator('site')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('status')}
                      >
                        Status {renderSortIndicator('status')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('location')}
                      >
                        Location {renderSortIndicator('location')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('unit_manufacture')}
                      >
                        Manufacture {renderSortIndicator('unit_manufacture')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('model')}
                      >
                        Model {renderSortIndicator('model')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('tire_size')}
                      >
                        Tire Size {renderSortIndicator('tire_size')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('tire_quantity')}
                      >
                        Tire Qty {renderSortIndicator('tire_quantity')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('unit_qty')}
                      >
                        Unit Qty {renderSortIndicator('unit_qty')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('totaltire')}
                      >
                        Total Tire {renderSortIndicator('totaltire')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('annual')}
                      >
                        Annual {renderSortIndicator('annual')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 whitespace-nowrap"
                        onClick={() => handleSort('forecast')}
                      >
                        Forecast {renderSortIndicator('forecast')}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {currentItems.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.id_fleet_list || '-'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.customer || '-'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.site || '-'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            item.status === 'Active' ? 'bg-green-100 text-green-800' :
                            item.status === 'Maintenance' ? 'bg-yellow-100 text-yellow-800' :
                            item.status === 'Inactive' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {item.status || 'Unknown'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.location || '-'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.unit_manufacture || '-'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.model || '-'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.tire_size || '-'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.tire_quantity || '-'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.unit_qty || '-'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.totaltire || '-'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.annual || '-'}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{item.forecast || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Pagination */}
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={prevPage}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    currentPage === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                  className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    currentPage === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                    <span className="font-medium">
                      {Math.min(indexOfLastItem, filteredFleetlist.length)}
                    </span>{' '}
                    of <span className="font-medium">{filteredFleetlist.length}</span> results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={prevPage}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === 1
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Previous</span>
                      <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                    </button>

                    {/* Page numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                      // Show pages around current page
                      let pageNum;
                      if (totalPages <= 5) {
                        // If 5 or fewer pages, show all
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        // If near start, show first 5 pages
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        // If near end, show last 5 pages
                        pageNum = totalPages - 4 + i;
                      } else {
                        // Otherwise show current page and 2 pages on each side
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          onClick={() => paginate(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === pageNum
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}

                    <button
                      onClick={nextPage}
                      disabled={currentPage === totalPages}
                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === totalPages
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Next</span>
                      <ChevronRight className="h-5 w-5" aria-hidden="true" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
