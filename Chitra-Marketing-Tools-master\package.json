{"name": "chitra-marketing-tools", "productName": "Chitra Marketing Tools", "private": true, "version": "1.0.0", "description": "Marketing tools for Chitra Paratama", "author": "PT Chitra Paratama", "type": "module", "main": "electron/main.js", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "proxy": "node proxy-server.cjs", "negotiation-server": "node negotiation-server.mjs", "dev:with-proxy": "concurrently \"npm run proxy\" \"npm run dev\"", "dev:with-negotiation": "concurrently \"npm run negotiation-server\" \"npm run dev\"", "dev:full": "concurrently \"npm run proxy\" \"npm run negotiation-server\" \"npm run dev\"", "electron:dev": "concurrently \"npm run dev\" \"electron .\"", "electron:dev:with-proxy": "concurrently \"npm run proxy\" \"npm run dev\" \"electron .\"", "electron:dev:full": "concurrently \"npm run proxy\" \"npm run negotiation-server\" \"npm run dev\" \"electron .\"", "electron:build": "npm run build && electron-builder", "electron:package": "npm run build && electron-builder --win --publish never", "electron:portable": "npm run build && electron-builder --win portable --publish never", "electron:portable-d": "npm run build && electron-builder --win portable --publish never --config.directories.output=D:\\ChitraMarketingTools", "electron:packager": "npm run build && electron-packager . \"Chitra Marketing Tools\" --platform=win32 --arch=x64 --out=D:\\ChitraMarketingTools-Packaged --overwrite --icon=public/assets/cp_logo.png --asar --executable-name=ChitraMarketingTools", "context7:test": "npx -y @modelcontextprotocol/inspector npx @upstash/context7-mcp@latest"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@runware/sdk-js": "^1.1.38", "@supabase/supabase-js": "^2.49.4", "@types/file-saver": "^2.0.7", "@types/papaparse": "^5.3.15", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-helmet": "^6.1.11", "antd": "^5.25.1", "axios": "^1.9.0", "body-parser": "^2.2.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "docx": "^9.5.0", "express": "^4.21.2", "file-saver": "^2.0.5", "firebase": "^11.6.1", "googleapis": "^133.0.0", "html-docx-js": "^0.3.1", "html2pdf.js": "^0.10.3", "http-proxy-middleware": "^2.0.6", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.475.0", "mammoth": "^1.9.0", "mem0ai": "^2.1.25", "papaparse": "^5.5.2", "pdf-lib": "^1.17.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-helmet": "^6.1.0", "react-hook-form": "^7.56.1", "react-router-dom": "^7.5.3", "react-select": "^5.10.1", "react-toastify": "^10.0.4", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/generator": "^7.27.0", "@babel/parser": "^7.27.0", "@babel/traverse": "^7.27.0", "@babel/types": "^7.27.0", "@types/node": "^22.15.17", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@upstash/context7-mcp": "^1.0.7", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^36.0.1", "electron-builder": "^26.0.12", "electron-packager": "^17.1.2", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^15.9.0", "less": "^4.3.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^6.2.1"}, "build": {"appId": "com.chitraparatama.marketingtools", "productName": "Chitra Marketing Tools", "files": ["dist/**/*", "electron/**/*"], "directories": {"buildResources": "public", "output": "ChitraMarketingTools-Portable"}, "win": {"target": ["portable"], "icon": "public/assets/cp_logo.png"}}}