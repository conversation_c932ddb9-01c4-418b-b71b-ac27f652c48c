<script setup lang="ts">
import { provide, ref } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:modelValue']);

const open = ref(false);
const value = ref(props.modelValue);

const updateValue = (newValue: string) => {
  value.value = newValue;
  emit('update:modelValue', newValue);
  open.value = false;
};

provide('select', {
  open,
  value,
  disabled: props.disabled,
  updateValue,
  toggle: () => {
    if (!props.disabled) {
      open.value = !open.value;
    }
  },
});
</script>

<template>
  <div class="relative">
    <slot></slot>
  </div>
</template>