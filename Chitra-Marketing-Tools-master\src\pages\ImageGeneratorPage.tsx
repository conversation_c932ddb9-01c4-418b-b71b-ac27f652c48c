import React, { useState, useEffect, useRef } from 'react';
import {
  ImageGenerationRequest,
  GeneratedImage,
  SavedImage,
  ImageModel,
  IMAGE_SIZES,
  CustomModel,
  PhotoMakerRequest,
  PhotoMakerModel,
  PhotoMakerStyle,
  SAMPLERS,
  STYLE_PRESETS
} from '../types/imageGenerator';
import {
  generateImages,
  getImageHistory,
  clearImageHistory,
  deleteImageFromHistory,
  getAllModels,
  saveCustomModel,
  deleteCustomModel,
  generatePhotoMakerImages,
  getAllPhotoMakerModels
} from '../services/runwareServiceNew';
import {
  ImageIcon,
  Sparkles,
  Download,
  Trash2,
  History,
  RefreshCw,
  Loader2,
  X,
  Copy,
  CheckCircle,
  AlertCircle,
  Plus,
  Settings,
  Save,
  Edit,
  Trash,
  Camera,
  Upload,
  Sliders
} from 'lucide-react';
import { MODELS as OPENROUTER_MODELS } from '../services/openRouterService';
// Mock useToast function
const useToast = () => {
  return {
    toast: (options: any) => {
      console.log('Toast:', options);
      alert(options.title + ': ' + options.description);
    }
  };
};

const ImageGeneratorPage: React.FC = () => {
  // Tab state
  const [activeTab, setActiveTab] = useState<'generator' | 'photomaker'>('generator');

  // Common state
  const [imageHistory, setImageHistory] = useState<SavedImage[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  const { toast } = useToast();

  // Image Generator state
  const [prompt, setPrompt] = useState('');
  const [negativePrompt, setNegativePrompt] = useState('');
  const [selectedModel, setSelectedModel] = useState<string>(ImageModel.JUGGERNAUT_PRO);
  const A4_SIZE = { width: 2480, height: 3508, label: 'A4 Portrait (2480×3508)' };
  const [selectedSize, setSelectedSize] = useState(IMAGE_SIZES[0]);
  const [numberResults, setNumberResults] = useState(1);
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [availableModels, setAvailableModels] = useState<CustomModel[]>([]);
  const [showModelManager, setShowModelManager] = useState(false);
  const [newModelName, setNewModelName] = useState('');
  const [newModelValue, setNewModelValue] = useState('');
  const [editingModelId, setEditingModelId] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [steps, setSteps] = useState(30);
  const [cfgScale, setCfgScale] = useState(7.0);
  const [selectedSampler, setSelectedSampler] = useState(SAMPLERS[0]);
  const [seed, setSeed] = useState<number | undefined>(undefined);
  const [selectedStylePreset, setSelectedStylePreset] = useState(STYLE_PRESETS[0]);
  const [enhancePrompt, setEnhancePrompt] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [enhancedPrompt, setEnhancedPrompt] = useState('');
  const [autoNegativePrompt, setAutoNegativePrompt] = useState('');
  const [isEnhancing, setIsEnhancing] = useState(false);

  // PhotoMaker state
  const [photoMakerPrompt, setPhotoMakerPrompt] = useState('');
  const [photoMakerNegativePrompt, setPhotoMakerNegativePrompt] = useState('');
  const [photoMakerSelectedModel, setPhotoMakerSelectedModel] = useState<string>(PhotoMakerModel.REALISM_ENGINE_SDXL);
  const [photoMakerSelectedSize, setPhotoMakerSelectedSize] = useState(IMAGE_SIZES[0]);
  const [photoMakerNumberResults, setPhotoMakerNumberResults] = useState(1);
  const [photoMakerSelectedStyle, setPhotoMakerSelectedStyle] = useState<string>(PhotoMakerStyle.NO_STYLE);
  const [photoMakerStrength, setPhotoMakerStrength] = useState(30); // Integer value between 15 and 50
  const [photoMakerSteps, setPhotoMakerSteps] = useState(30);
  const [photoMakerCfgScale, setPhotoMakerCfgScale] = useState(7.0);
  const [photoMakerShowAdvanced, setPhotoMakerShowAdvanced] = useState(false);
  const [photoMakerGeneratedImages, setPhotoMakerGeneratedImages] = useState<GeneratedImage[]>([]);
  const [photoMakerIsGenerating, setPhotoMakerIsGenerating] = useState(false);
  const [photoMakerAvailableModels, setPhotoMakerAvailableModels] = useState<any[]>([]);

  // State for reference images
  const [referenceImages, setReferenceImages] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // View Image Modal state
  const [modalImage, setModalImage] = useState<string | null>(null);

  // Load image history, models, and check for prompt from Instagram on component mount
  useEffect(() => {
    loadImageHistory();
    loadModels();
    loadPhotoMakerModels();

    // Check if there's a prompt from Instagram in sessionStorage
    const instagramPrompt = sessionStorage.getItem('instagram_image_prompt');
    const instagramNegativePrompt = sessionStorage.getItem('instagram_negative_prompt');

    let promptsLoaded = false;

    if (instagramPrompt) {
      setPrompt(instagramPrompt);
      sessionStorage.removeItem('instagram_image_prompt');
      promptsLoaded = true;
    }

    if (instagramNegativePrompt) {
      setNegativePrompt(instagramNegativePrompt);
      sessionStorage.removeItem('instagram_negative_prompt');
      promptsLoaded = true;
    }

    if (promptsLoaded) {
      toast({
        title: "Ultra-Realistic Prompt Loaded",
        description: "Detailed image prompt from Instagram content has been loaded with optimized settings for realism",
      });
    }
  }, []);

  // Load image history from local storage
  const loadImageHistory = () => {
    const history = getImageHistory();
    setImageHistory(history);
  };

  // Load all available models (built-in + custom)
  const loadModels = () => {
    const models = getAllModels();
    setAvailableModels(models);
  };

  // Load all available PhotoMaker models
  const loadPhotoMakerModels = () => {
    const models = getAllPhotoMakerModels();
    setPhotoMakerAvailableModels(models);
  };

  // Handle saving a new custom model
  const handleSaveModel = () => {
    if (!newModelName.trim() || !newModelValue.trim()) {
      toast({
        title: "Error",
        description: "Please enter both a name and value for the model",
        variant: "destructive"
      });
      return;
    }

    try {
      const model = {
        name: newModelName,
        value: newModelValue,
        isDefault: false
      };

      const savedModel = saveCustomModel(model);

      // Reset form
      setNewModelName('');
      setNewModelValue('');
      setEditingModelId(null);

      // Reload models
      loadModels();

      toast({
        title: "Success",
        description: `Model "${savedModel.name}" saved successfully`,
      });
    } catch (error) {
      console.error('Error saving model:', error);
      toast({
        title: "Error",
        description: "Failed to save model",
        variant: "destructive"
      });
    }
  };

  // Handle editing an existing model
  const handleEditModel = (model: CustomModel) => {
    setNewModelName(model.name);
    setNewModelValue(model.value);
    setEditingModelId(model.id);
  };

  // Handle deleting a model
  const handleDeleteModel = (modelId: string) => {
    if (confirm('Are you sure you want to delete this model?')) {
      deleteCustomModel(modelId);
      loadModels();

      // If the deleted model was selected, reset to default
      const deletedModel = availableModels.find(m => m.id === modelId);
      if (deletedModel && deletedModel.value === selectedModel) {
        setSelectedModel(ImageModel.JUGGERNAUT_PRO);
      }

      toast({
        title: "Deleted",
        description: "Model deleted successfully",
      });
    }
  };

  // Fungsi untuk rewrite prompt dengan OpenRouter
  async function rewritePromptWithOpenRouter(prompt: string): Promise<string> {
    const systemPrompt = `Rewrite this prompt for an AI image generator. Make it more descriptive, visually clear, and natural in ENGLISH. Do not exaggerate or add elements outside the original context. Always reply in English.`;
    const requestBody = {
      model: OPENROUTER_MODELS.GPT4,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
      temperature: 0.5,
      max_tokens: 120
    };
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer 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',
        'HTTP-Referer': 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools'
      },
      body: JSON.stringify(requestBody)
    });
    const data = await response.json();
    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      return data.choices[0].message.content.trim();
    }
    return prompt;
  }

  // Fungsi auto enhance prompt dan negative prompt
  async function handleEnhancePrompt() {
    setIsEnhancing(true);
    let basePrompt = prompt;
    let negative = negativePrompt;
    // Auto enhance untuk kata 'kura-kura'
    if (/kura[- ]?kura/i.test(prompt)) {
      basePrompt += ' (turtle)';
      negative = 'frog, toad, kodok, katak, amphibian, bulging eyes, webbed feet';
      setAutoNegativePrompt(negative);
    }
    const rewritten = await rewritePromptWithOpenRouter(basePrompt);
    setEnhancedPrompt(rewritten);
    setPrompt(rewritten);
    setNegativePrompt(negative);
    setIsEnhancing(false);
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!prompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a prompt",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      let finalPrompt = prompt;
      let finalNegative = negativePrompt;
      if (enhancePrompt) {
        // Auto enhance untuk kata 'kura-kura'
        if (/kura[- ]?kura/i.test(prompt)) {
          finalPrompt += ' (turtle)';
          finalNegative = 'frog, toad, kodok, katak, amphibian, bulging eyes, webbed feet';
        }
        finalPrompt = await rewritePromptWithOpenRouter(finalPrompt);
      }
      const request: ImageGenerationRequest = {
        positivePrompt: finalPrompt,
        negativePrompt: finalNegative,
        width: selectedSize.width,
        height: selectedSize.height,
        model: selectedModel,
        numberResults,
        steps,
        cfgScale,
        sampler: selectedSampler,
        seed,
        stylePreset: selectedStylePreset !== 'None' ? selectedStylePreset : undefined,
        enhancePrompt
      };

      const images = await generateImages(request);

      if (images && images.length > 0) {
        setGeneratedImages(images.map(img => ({ ...img, prompt: finalPrompt })));
        loadImageHistory(); // Refresh history after generation

        toast({
          title: "Success",
          description: `Generated ${images.length} image(s)`,
        });
      } else {
        throw new Error("No images were returned from the API");
      }
    } catch (error) {
      console.error('Error generating images:', error);

      // Get a more descriptive error message
      let errorMessage = "Failed to generate images. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle image download
  const handleDownload = (imageUrl: string, index: number) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = `generated-image-${index + 1}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Downloaded",
      description: "Image downloaded successfully",
    });
  };

  // Handle clearing history
  const handleClearHistory = () => {
    if (confirm('Are you sure you want to clear all image history?')) {
      clearImageHistory();
      setImageHistory([]);
      toast({
        title: "Cleared",
        description: "Image history cleared",
      });
    }
  };

  // Handle deleting a single image from history
  const handleDeleteImage = (imageId: string) => {
    deleteImageFromHistory(imageId);
    loadImageHistory(); // Refresh history after deletion

    toast({
      title: "Deleted",
      description: "Image removed from history",
    });
  };

  // Copy prompt to clipboard
  const copyPrompt = (prompt: string) => {
    navigator.clipboard.writeText(prompt);
    toast({
      title: "Copied",
      description: "Prompt copied to clipboard",
    });
  };

  // Handle file selection for reference images
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Limit to 4 files
    const filesToProcess = Array.from(files).slice(0, 4);

    // Convert files to base64
    Promise.all(
      filesToProcess.map(file => {
        return new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            if (typeof reader.result === 'string') {
              resolve(reader.result);
            } else {
              reject(new Error('Failed to read file as base64'));
            }
          };
          reader.onerror = reject;
          reader.readAsDataURL(file);
        });
      })
    )
    .then(base64Images => {
      setReferenceImages(base64Images);
      toast({
        title: "Images Uploaded",
        description: `${base64Images.length} reference image(s) uploaded successfully`,
      });
    })
    .catch(error => {
      console.error('Error processing files:', error);
      toast({
        title: "Error",
        description: "Failed to process image files",
        variant: "destructive"
      });
    });
  };

  // Remove a reference image
  const removeReferenceImage = (index: number) => {
    setReferenceImages(prev => prev.filter((_, i) => i !== index));
  };

  // Handle PhotoMaker form submission
  const handlePhotoMakerSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!photoMakerPrompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a prompt",
        variant: "destructive"
      });
      return;
    }

    if (referenceImages.length === 0) {
      toast({
        title: "Error",
        description: "Please upload at least one reference image",
        variant: "destructive"
      });
      return;
    }

    setPhotoMakerIsGenerating(true);

    try {
      // Ensure the prompt includes the required "rwre" trigger word
      let finalPrompt = photoMakerPrompt;
      if (!finalPrompt.includes('rwre')) {
        finalPrompt = `rwre, ${finalPrompt}`;
      }

      const request: PhotoMakerRequest = {
        inputImages: referenceImages,
        style: photoMakerSelectedStyle,
        strength: photoMakerStrength,
        positivePrompt: finalPrompt,
        negativePrompt: photoMakerNegativePrompt,
        width: photoMakerSelectedSize.width,
        height: photoMakerSelectedSize.height,
        model: photoMakerSelectedModel,
        steps: photoMakerSteps,
        CFGScale: photoMakerCfgScale,
        numberResults: photoMakerNumberResults
      };

      const images = await generatePhotoMakerImages(request);

      if (images && images.length > 0) {
        setPhotoMakerGeneratedImages(images);
        loadImageHistory(); // Refresh history after generation

        toast({
          title: "Success",
          description: `Generated ${images.length} image(s)`,
        });
      } else {
        throw new Error("No images were returned from the API");
      }
    } catch (error) {
      console.error('Error generating images:', error);

      // Get a more descriptive error message
      let errorMessage = "Failed to generate images. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setPhotoMakerIsGenerating(false);
    }
  };

  useEffect(() => {
    // Preset otomatis untuk model FLUX
    if ([ImageModel.JUGGERNAUT_PRO, ImageModel.JUGGERNAUT_LIGHTNING].includes(selectedModel as ImageModel)) {
      setSelectedSampler('Euler Beta');
      setCfgScale(3);
      setSteps(30);
    }
  }, [selectedModel]);

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center mb-4">
        <ImageIcon size={24} className="text-blue-500 mr-2" />
        <h1 className="text-2xl font-bold">Image Generator</h1>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          className={`py-2 px-4 font-medium text-sm mr-2 ${
            activeTab === 'generator'
              ? 'text-blue-600 border-b-2 border-blue-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('generator')}
        >
          <div className="flex items-center">
            <Sparkles size={16} className="mr-2" />
            Image Generator
          </div>
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm ${
            activeTab === 'photomaker'
              ? 'text-blue-600 border-b-2 border-blue-500'
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('photomaker')}
        >
          <div className="flex items-center">
            <Camera size={16} className="mr-2" />
            Photo Maker
          </div>
        </button>
      </div>

      {activeTab === 'generator' ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Form Panel */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-4 border-b border-gray-200 flex items-center">
                <Sparkles size={20} className="text-blue-500 mr-2" />
                <h2 className="text-lg font-medium">Generate Images</h2>
              </div>

              <form onSubmit={handleSubmit} className="p-4 space-y-4">
              {/* Prompt Input */}
              <div>
                <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-1">
                  Prompt
                </label>
                <textarea
                  id="prompt"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Describe the image you want to generate..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={4}
                  required
                />
              </div>

              {/* Negative Prompt Input */}
              <div>
                <label htmlFor="negativePrompt" className="block text-sm font-medium text-gray-700 mb-1">
                  Negative Prompt (Optional)
                </label>
                <textarea
                  id="negativePrompt"
                  value={negativePrompt}
                  onChange={(e) => setNegativePrompt(e.target.value)}
                  placeholder="Elements to avoid in the generated image..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={2}
                />
              </div>

              {/* Model Selection */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <label htmlFor="model" className="block text-sm font-medium text-gray-700">
                    Model
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowModelManager(!showModelManager)}
                    className="text-xs text-blue-500 hover:text-blue-700 flex items-center"
                  >
                    {showModelManager ? 'Hide Model Manager' : 'Manage Models'}
                    <Settings size={12} className="ml-1" />
                  </button>
                </div>
                <select
                  id="model"
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {availableModels.map(model => (
                    <option key={model.id} value={model.value}>
                      {model.name} {model.isDefault ? '' : '(Custom)'}
                    </option>
                  ))}
                </select>
              </div>

              {/* Custom Model Manager */}
              {showModelManager && (
                <div className="bg-gray-50 p-3 rounded-md border border-gray-200 mb-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">
                    {editingModelId ? 'Edit Model' : 'Add Custom Model'}
                  </h3>

                  <div className="space-y-3">
                    <div>
                      <label htmlFor="modelName" className="block text-xs font-medium text-gray-700 mb-1">
                        Model Name
                      </label>
                      <input
                        id="modelName"
                        type="text"
                        value={newModelName}
                        onChange={(e) => setNewModelName(e.target.value)}
                        placeholder="e.g., My Custom Model"
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div>
                      <label htmlFor="modelValue" className="block text-xs font-medium text-gray-700 mb-1">
                        Model Value
                      </label>
                      <input
                        id="modelValue"
                        type="text"
                        value={newModelValue}
                        onChange={(e) => setNewModelValue(e.target.value)}
                        placeholder="e.g., civitai:12345@67890"
                        className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>

                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={handleSaveModel}
                        className="flex-1 py-1 px-3 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 flex items-center justify-center"
                      >
                        <Save size={14} className="mr-1" />
                        {editingModelId ? 'Update' : 'Save'}
                      </button>

                      {editingModelId && (
                        <button
                          type="button"
                          onClick={() => {
                            setNewModelName('');
                            setNewModelValue('');
                            setEditingModelId(null);
                          }}
                          className="py-1 px-3 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-100"
                        >
                          Cancel
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Custom Models List */}
                  {availableModels.filter(model => !model.isDefault).length > 0 && (
                    <div className="mt-4">
                      <h4 className="text-xs font-medium text-gray-700 mb-2">Your Custom Models</h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {availableModels
                          .filter(model => !model.isDefault)
                          .map(model => (
                            <div key={model.id} className="flex justify-between items-center p-2 bg-white rounded border border-gray-200 text-xs">
                              <div>
                                <div className="font-medium">{model.name}</div>
                                <div className="text-gray-500 text-xs truncate" style={{ maxWidth: '150px' }}>
                                  {model.value}
                                </div>
                              </div>
                              <div className="flex space-x-1">
                                <button
                                  onClick={() => handleEditModel(model)}
                                  className="text-blue-500 hover:text-blue-700"
                                  title="Edit model"
                                >
                                  <Edit size={14} />
                                </button>
                                <button
                                  onClick={() => handleDeleteModel(model.id)}
                                  className="text-red-500 hover:text-red-700"
                                  title="Delete model"
                                >
                                  <Trash size={14} />
                                </button>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Image Size Selection */}
              <div>
                <label htmlFor="size" className="block text-sm font-medium text-gray-700 mb-1">
                  Image Size
                </label>
                <select
                  id="size"
                  value={`${selectedSize.width}x${selectedSize.height}`}
                  onChange={(e) => {
                    const [width, height] = e.target.value.split('x').map(Number);
                    setSelectedSize({ width, height, label: e.target.options[e.target.selectedIndex].text });
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option key="a4" value="2480x3508">A4 Portrait (2480×3508)</option>
                  {IMAGE_SIZES.map((size) => (
                    <option key={`${size.width}x${size.height}`} value={`${size.width}x${size.height}`}>
                      {size.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Number of Results */}
              <div>
                <label htmlFor="numberResults" className="block text-sm font-medium text-gray-700 mb-1">
                  Number of Images
                </label>
                <select
                  id="numberResults"
                  value={numberResults}
                  onChange={(e) => setNumberResults(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={1}>1</option>
                  <option value={2}>2</option>
                  <option value={4}>4</option>
                </select>
              </div>

              {/* Advanced Settings Toggle */}
              <div>
                <button
                  type="button"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                  className="text-sm text-blue-500 hover:text-blue-700 flex items-center"
                >
                  <Sliders size={14} className="mr-1" />
                  {showAdvanced ? 'Hide Advanced Settings' : 'Show Advanced Settings'}
                </button>
              </div>

              {/* Advanced Settings */}
              {showAdvanced && (
                <div className="space-y-4 p-3 bg-gray-50 rounded-md">
                  {/* Steps Slider */}
                  <div>
                    <label htmlFor="steps" className="block text-sm font-medium text-gray-700 mb-1">
                      Steps: {steps}
                    </label>
                    <input
                      id="steps"
                      type="range"
                      min="20"
                      max="50"
                      step="1"
                      value={steps}
                      onChange={(e) => setSteps(parseInt(e.target.value))}
                      className="w-full"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Higher values (40-50) produce more detailed results but take longer
                    </p>
                  </div>

                  {/* CFG Scale Slider */}
                  <div>
                    <label htmlFor="cfgScale" className="block text-sm font-medium text-gray-700 mb-1">
                      CFG Scale: {cfgScale.toFixed(1)}
                    </label>
                    <input
                      id="cfgScale"
                      type="range"
                      min="1"
                      max="15"
                      step="0.5"
                      value={cfgScale}
                      onChange={(e) => setCfgScale(parseFloat(e.target.value))}
                      className="w-full"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Higher values (7-15) follow the prompt more closely, lower values (1-7) are more creative
                    </p>
                  </div>

                  {/* Sampler Selection */}
                  <div>
                    <label htmlFor="sampler" className="block text-sm font-medium text-gray-700 mb-1">
                      Sampler
                    </label>
                    <select
                      id="sampler"
                      value={selectedSampler}
                      onChange={(e) => setSelectedSampler(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {SAMPLERS.map((sampler) => (
                        <option key={sampler} value={sampler}>
                          {sampler}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Style Preset Selection */}
                  <div>
                    <label htmlFor="stylePreset" className="block text-sm font-medium text-gray-700 mb-1">
                      Style Preset
                    </label>
                    <select
                      id="stylePreset"
                      value={selectedStylePreset}
                      onChange={(e) => setSelectedStylePreset(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {STYLE_PRESETS.map((style) => (
                        <option key={style} value={style}>
                          {style}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Seed Input */}
                  <div>
                    <label htmlFor="seed" className="block text-sm font-medium text-gray-700 mb-1">
                      Seed (Optional)
                    </label>
                    <input
                      id="seed"
                      type="number"
                      value={seed || ''}
                      onChange={(e) => setSeed(e.target.value ? parseInt(e.target.value) : undefined)}
                      placeholder="Random seed"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Leave empty for random seed. Use the same seed to reproduce results
                    </p>
                  </div>

                  {/* Enhance Prompt Toggle */}
                  <div className="flex items-center">
                    <input
                      id="enhancePrompt"
                      type="checkbox"
                      checked={enhancePrompt}
                      onChange={(e) => setEnhancePrompt(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="enhancePrompt" className="ml-2 block text-sm text-gray-700">
                      Enhance Prompt
                    </label>
                  </div>
                </div>
              )}

              {/* Generate Button */}
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={handleEnhancePrompt}
                  className="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-300"
                  disabled={isEnhancing || !prompt.trim()}
                >
                  {isEnhancing ? 'Enhancing...' : 'Enhance Prompt'}
                </button>
                <button
                  type="submit"
                  disabled={isGenerating || !prompt.trim()}
                  className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300"
                >
                  {isGenerating ? 'Generating...' : 'Generate Images'}
                </button>
              </div>

              {enhancedPrompt && (
                <div className="mt-2 p-2 bg-blue-50 text-xs text-blue-800 rounded">
                  <b>Prompt hasil Enhance:</b> {enhancedPrompt}
                  {autoNegativePrompt && (
                    <div className="mt-1 text-gray-700"><b>Negative Prompt Otomatis:</b> {autoNegativePrompt}</div>
                  )}
                </div>
              )}

              {/* History Toggle Button */}
              <button
                type="button"
                onClick={() => setShowHistory(!showHistory)}
                className="w-full flex justify-center items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
              >
                {showHistory ? (
                  <>
                    <X size={16} className="mr-2" />
                    Hide History
                  </>
                ) : (
                  <>
                    <History size={16} className="mr-2" />
                    Show History
                  </>
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Results Panel */}
        <div className="lg:col-span-2">
          {/* Generated Images */}
          {!showHistory && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center">
                  <ImageIcon size={20} className="text-blue-500 mr-2" />
                  <h2 className="text-lg font-medium">Generated Images</h2>
                </div>
                {generatedImages.length > 0 && (
                  <button
                    onClick={() => setGeneratedImages([])}
                    className="text-gray-500 hover:text-gray-700"
                    title="Clear results"
                  >
                    <X size={18} />
                  </button>
                )}
              </div>

              <div className="p-4">
                {generatedImages.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    {isGenerating ? (
                      <div className="flex flex-col items-center">
                        <Loader2 size={40} className="animate-spin mb-4" />
                        <p>Generating images, please wait...</p>
                      </div>
                    ) : (
                      <p>No images generated yet. Fill the form and click Generate.</p>
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {generatedImages.map((image, index) => (
                      <div key={image.imageUUID} className="border border-gray-200 rounded-lg overflow-hidden">
                        <img
                          src={image.imageURL}
                          alt={`Generated image ${index + 1}`}
                          className="w-full h-auto object-cover cursor-pointer"
                          loading="lazy"
                          onClick={() => setModalImage(image.imageURL)}
                        />
                        <div className="p-3 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
                          <button
                            onClick={() => handleDownload(image.imageURL, index)}
                            className="flex items-center text-blue-500 hover:text-blue-700"
                            title="Download image"
                          >
                            <Download size={16} className="mr-1" />
                            Download
                          </button>
                          <button
                            onClick={() => copyPrompt(image.prompt)}
                            className="flex items-center text-gray-500 hover:text-gray-700"
                            title="Copy prompt"
                          >
                            <Copy size={16} className="mr-1" />
                            Copy Prompt
                          </button>
                        </div>
                        {index === 0 && (
                          <div className="text-xs text-gray-500 mt-2 mb-2">
                            <b>Prompt yang dikirim ke model:</b> {image.prompt}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Image History */}
          {showHistory && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div className="flex items-center">
                  <History size={20} className="text-blue-500 mr-2" />
                  <h2 className="text-lg font-medium">Image History</h2>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={loadImageHistory}
                    className="text-gray-500 hover:text-gray-700"
                    title="Refresh history"
                  >
                    <RefreshCw size={18} />
                  </button>
                  <button
                    onClick={handleClearHistory}
                    className="text-gray-500 hover:text-gray-700"
                    title="Clear history"
                  >
                    <Trash2 size={18} />
                  </button>
                </div>
              </div>

              <div className="p-4">
                {imageHistory.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>No image history found.</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {imageHistory.map((image) => (
                      <div key={image.id} className="border border-gray-200 rounded-lg overflow-hidden">
                        <img
                          src={image.imageURL}
                          alt={image.prompt}
                          className="w-full h-auto object-cover"
                          loading="lazy"
                        />
                        <div className="p-3 border-t border-gray-200">
                          <p className="text-sm text-gray-600 mb-2 line-clamp-2" title={image.prompt}>
                            {image.prompt}
                          </p>
                          <div className="flex justify-between items-center">
                            <button
                              onClick={() => handleDownload(image.imageURL, 0)}
                              className="flex items-center text-blue-500 hover:text-blue-700"
                              title="Download image"
                            >
                              <Download size={16} className="mr-1" />
                              Download
                            </button>
                            <button
                              onClick={() => handleDeleteImage(image.id)}
                              className="flex items-center text-red-500 hover:text-red-700"
                              title="Delete from history"
                            >
                              <Trash2 size={16} className="mr-1" />
                              Delete
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* PhotoMaker Form Panel */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-4 border-b border-gray-200 flex items-center">
                <Camera size={20} className="text-blue-500 mr-2" />
                <h2 className="text-lg font-medium">Generate Images from Photos</h2>
              </div>

              <form onSubmit={handlePhotoMakerSubmit} className="p-4 space-y-4">
                {/* Reference Images Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reference Images (Max 4)
                  </label>
                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-2">
                      {referenceImages.map((img, index) => (
                        <div key={index} className="relative w-20 h-20">
                          <img
                            src={img}
                            alt={`Reference ${index + 1}`}
                            className="w-full h-full object-cover rounded-md"
                          />
                          <button
                            type="button"
                            onClick={() => removeReferenceImage(index)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                            title="Remove image"
                          >
                            <X size={12} />
                          </button>
                        </div>
                      ))}
                      {referenceImages.length < 4 && (
                        <button
                          type="button"
                          onClick={() => fileInputRef.current?.click()}
                          className="w-20 h-20 border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center text-gray-500 hover:text-blue-500 hover:border-blue-500"
                        >
                          <Upload size={24} />
                        </button>
                      )}
                    </div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleFileChange}
                      className="hidden"
                    />
                    <p className="text-xs text-gray-500">
                      Upload 1-4 reference images of the same person/subject
                    </p>
                  </div>
                </div>

                {/* Prompt Input */}
                <div>
                  <label htmlFor="photoMakerPrompt" className="block text-sm font-medium text-gray-700 mb-1">
                    Prompt
                  </label>
                  <textarea
                    id="photoMakerPrompt"
                    value={photoMakerPrompt}
                    onChange={(e) => setPhotoMakerPrompt(e.target.value)}
                    placeholder="Describe how you want the person/subject to appear..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    The keyword "rwre" will be automatically added to your prompt if not included. This is required for PhotoMaker to work properly.
                  </p>
                </div>

                {/* Negative Prompt Input */}
                <div>
                  <label htmlFor="photoMakerNegativePrompt" className="block text-sm font-medium text-gray-700 mb-1">
                    Negative Prompt (Optional)
                  </label>
                  <textarea
                    id="photoMakerNegativePrompt"
                    value={photoMakerNegativePrompt}
                    onChange={(e) => setPhotoMakerNegativePrompt(e.target.value)}
                    placeholder="Elements to avoid in the generated image..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={2}
                  />
                </div>

                {/* Style Selection */}
                <div>
                  <label htmlFor="photoMakerStyle" className="block text-sm font-medium text-gray-700 mb-1">
                    Style
                  </label>
                  <select
                    id="photoMakerStyle"
                    value={photoMakerSelectedStyle}
                    onChange={(e) => setPhotoMakerSelectedStyle(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={PhotoMakerStyle.NO_STYLE}>No Style (Best Fidelity)</option>
                    <option value={PhotoMakerStyle.PHOTOGRAPHIC}>Photographic</option>
                    <option value={PhotoMakerStyle.CINEMATIC}>Cinematic</option>
                    <option value={PhotoMakerStyle.DISNEY_CHARACTER}>Disney Character</option>
                    <option value={PhotoMakerStyle.DIGITAL_ART}>Digital Art</option>
                    <option value={PhotoMakerStyle.FANTASY_ART}>Fantasy Art</option>
                    <option value={PhotoMakerStyle.NEONPUNK}>Neonpunk</option>
                    <option value={PhotoMakerStyle.ENHANCE}>Enhance</option>
                    <option value={PhotoMakerStyle.COMIC_BOOK}>Comic Book</option>
                    <option value={PhotoMakerStyle.LOWPOLY}>Lowpoly</option>
                    <option value={PhotoMakerStyle.LINE_ART}>Line Art</option>
                  </select>
                </div>

                {/* Strength Slider */}
                <div>
                  <label htmlFor="photoMakerStrength" className="block text-sm font-medium text-gray-700 mb-1">
                    Strength: {photoMakerStrength}
                  </label>
                  <input
                    id="photoMakerStrength"
                    type="range"
                    min="15"
                    max="50"
                    step="1"
                    value={photoMakerStrength}
                    onChange={(e) => setPhotoMakerStrength(parseInt(e.target.value))}
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Lower values (15-30) preserve more of the reference image, higher values (30-50) apply more of the prompt
                  </p>
                </div>

                {/* Advanced Settings Toggle */}
                <div>
                  <button
                    type="button"
                    onClick={() => setPhotoMakerShowAdvanced(!photoMakerShowAdvanced)}
                    className="text-sm text-blue-500 hover:text-blue-700 flex items-center"
                  >
                    <Sliders size={14} className="mr-1" />
                    {photoMakerShowAdvanced ? 'Hide Advanced Settings' : 'Show Advanced Settings'}
                  </button>
                </div>

                {/* Advanced Settings */}
                {photoMakerShowAdvanced && (
                  <div className="space-y-4 p-3 bg-gray-50 rounded-md">
                    {/* Model Selection */}
                    <div>
                      <label htmlFor="photoMakerModel" className="block text-sm font-medium text-gray-700 mb-1">
                        Model
                      </label>
                      <select
                        id="photoMakerModel"
                        value={photoMakerSelectedModel}
                        onChange={(e) => setPhotoMakerSelectedModel(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {photoMakerAvailableModels.map(model => (
                          <option key={model.id} value={model.value}>
                            {model.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Image Size Selection */}
                    <div>
                      <label htmlFor="photoMakerSize" className="block text-sm font-medium text-gray-700 mb-1">
                        Image Size
                      </label>
                      <select
                        id="photoMakerSize"
                        value={`${photoMakerSelectedSize.width}x${photoMakerSelectedSize.height}`}
                        onChange={(e) => {
                          const [width, height] = e.target.value.split('x').map(Number);
                          setPhotoMakerSelectedSize({ width, height, label: e.target.options[e.target.selectedIndex].text });
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {IMAGE_SIZES.map((size) => (
                          <option key={`${size.width}x${size.height}`} value={`${size.width}x${size.height}`}>
                            {size.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Steps Slider */}
                    <div>
                      <label htmlFor="photoMakerSteps" className="block text-sm font-medium text-gray-700 mb-1">
                        Steps: {photoMakerSteps}
                      </label>
                      <input
                        id="photoMakerSteps"
                        type="range"
                        min="20"
                        max="50"
                        step="1"
                        value={photoMakerSteps}
                        onChange={(e) => setPhotoMakerSteps(parseInt(e.target.value))}
                        className="w-full"
                      />
                    </div>

                    {/* CFG Scale Slider */}
                    <div>
                      <label htmlFor="photoMakerCfgScale" className="block text-sm font-medium text-gray-700 mb-1">
                        CFG Scale: {photoMakerCfgScale.toFixed(1)}
                      </label>
                      <input
                        id="photoMakerCfgScale"
                        type="range"
                        min="1"
                        max="15"
                        step="0.5"
                        value={photoMakerCfgScale}
                        onChange={(e) => setPhotoMakerCfgScale(parseFloat(e.target.value))}
                        className="w-full"
                      />
                    </div>

                    {/* Number of Results */}
                    <div>
                      <label htmlFor="photoMakerNumberResults" className="block text-sm font-medium text-gray-700 mb-1">
                        Number of Images
                      </label>
                      <select
                        id="photoMakerNumberResults"
                        value={photoMakerNumberResults}
                        onChange={(e) => setPhotoMakerNumberResults(Number(e.target.value))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value={1}>1</option>
                        <option value={2}>2</option>
                        <option value={4}>4</option>
                      </select>
                    </div>
                  </div>
                )}

                {/* Generate Button */}
                <button
                  type="submit"
                  disabled={photoMakerIsGenerating || referenceImages.length === 0}
                  className={`w-full py-2 px-4 rounded-md flex items-center justify-center ${
                    photoMakerIsGenerating || referenceImages.length === 0
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                  }`}
                >
                  {photoMakerIsGenerating ? (
                    <>
                      <Loader2 size={20} className="animate-spin mr-2" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles size={20} className="mr-2" />
                      Generate Images
                    </>
                  )}
                </button>
              </form>
            </div>
          </div>

          {/* Results Panel */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                <div className="flex items-center">
                  <ImageIcon size={20} className="text-blue-500 mr-2" />
                  <h2 className="text-lg font-medium">
                    {showHistory ? 'Image History' : 'Generated Images'}
                  </h2>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowHistory(!showHistory)}
                    className={`py-1 px-3 rounded-md flex items-center text-sm ${
                      showHistory
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <History size={16} className="mr-1" />
                    {showHistory ? 'Show Results' : 'Show History'}
                  </button>
                  {showHistory && (
                    <button
                      onClick={handleClearHistory}
                      className="py-1 px-3 bg-red-100 text-red-700 rounded-md flex items-center text-sm hover:bg-red-200"
                      title="Clear history"
                    >
                      <Trash2 size={16} className="mr-1" />
                      Clear
                    </button>
                  )}
                </div>
              </div>

              <div className="p-4">
                {/* Generated Images */}
                {!showHistory && (
                  <>
                    {photoMakerGeneratedImages.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {photoMakerGeneratedImages.map((image, index) => (
                          <div key={image.imageUUID} className="border border-gray-200 rounded-lg overflow-hidden">
                            <img
                              src={image.imageURL}
                              alt={`Generated image ${index + 1}`}
                              className="w-full h-auto object-cover cursor-pointer"
                              loading="lazy"
                              onClick={() => setModalImage(image.imageURL)}
                            />
                            <div className="p-3 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
                              <div className="text-sm text-gray-500 truncate" style={{ maxWidth: '200px' }}>
                                {image.prompt}
                              </div>
                              <div className="flex space-x-1">
                                <button
                                  onClick={() => copyPrompt(image.prompt)}
                                  className="p-1 text-gray-500 hover:text-blue-500"
                                  title="Copy prompt"
                                >
                                  <Copy size={16} />
                                </button>
                                <button
                                  onClick={() => handleDownload(image.imageURL, index)}
                                  className="p-1 text-gray-500 hover:text-blue-500"
                                  title="Download image"
                                >
                                  <Download size={16} />
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <Camera size={48} className="mx-auto text-gray-300 mb-4" />
                        <h3 className="text-lg font-medium text-gray-700 mb-2">No Images Generated Yet</h3>
                        <p className="text-gray-500 max-w-md mx-auto">
                          Upload reference images and enter a prompt to generate new images with PhotoMaker.
                        </p>
                      </div>
                    )}
                  </>
                )}

                {/* Image History */}
                {showHistory && (
                  <>
                    {imageHistory.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {imageHistory.map((image, index) => (
                          <div key={image.id} className="border border-gray-200 rounded-lg overflow-hidden">
                            <img
                              src={image.imageURL}
                              alt={`History image ${index + 1}`}
                              className="w-full h-auto"
                              loading="lazy"
                            />
                            <div className="p-3 border-t border-gray-200 bg-gray-50 flex justify-between items-center">
                              <div className="text-sm text-gray-500 truncate" style={{ maxWidth: '200px' }}>
                                {image.prompt}
                              </div>
                              <div className="flex space-x-1">
                                <button
                                  onClick={() => copyPrompt(image.prompt)}
                                  className="p-1 text-gray-500 hover:text-blue-500"
                                  title="Copy prompt"
                                >
                                  <Copy size={16} />
                                </button>
                                <button
                                  onClick={() => handleDownload(image.imageURL, index)}
                                  className="p-1 text-gray-500 hover:text-blue-500"
                                  title="Download image"
                                >
                                  <Download size={16} />
                                </button>
                                <button
                                  onClick={() => handleDeleteImage(image.id)}
                                  className="p-1 text-gray-500 hover:text-red-500"
                                  title="Delete from history"
                                >
                                  <Trash2 size={16} />
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <History size={48} className="mx-auto text-gray-300 mb-4" />
                        <h3 className="text-lg font-medium text-gray-700 mb-2">No Image History</h3>
                        <p className="text-gray-500">
                          Your generated images will appear here.
                        </p>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View Image Modal */}
      {modalImage && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70">
          <div className="relative bg-white rounded shadow-lg p-4 max-w-3xl w-full flex flex-col items-center">
            <button
              onClick={() => setModalImage(null)}
              className="absolute top-2 right-2 text-gray-700 hover:text-red-500 text-xl font-bold"
              aria-label="Close"
            >
              ×
            </button>
            <img src={modalImage} alt="View" className="max-h-[80vh] w-auto rounded" />
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageGeneratorPage;
