/**
 * Utility to clear all cached data and force fresh data load
 */

export function clearAllCache() {
    console.log('🧹 Clearing all cached data...');
    
    // Clear localStorage
    const localStorageKeys = [
        'fleetData',
        'fleetDummyData', 
        'fleetAnalyzerData',
        'fleetCache',
        'dummyFleetData'
    ];
    
    localStorageKeys.forEach(key => {
        if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
            console.log(`✅ Cleared localStorage: ${key}`);
        }
    });
    
    // Clear sessionStorage
    const sessionStorageKeys = [
        'fleetData',
        'fleetDummyData',
        'fleetAnalyzerData', 
        'fleetCache',
        'dummyFleetData'
    ];
    
    sessionStorageKeys.forEach(key => {
        if (sessionStorage.getItem(key)) {
            sessionStorage.removeItem(key);
            console.log(`✅ Cleared sessionStorage: ${key}`);
        }
    });
    
    // Clear any cached API responses
    if ('caches' in window) {
        caches.keys().then(cacheNames => {
            cacheNames.forEach(cacheName => {
                if (cacheName.includes('fleet') || cacheName.includes('api')) {
                    caches.delete(cacheName);
                    console.log(`✅ Cleared cache: ${cacheName}`);
                }
            });
        });
    }
    
    console.log('✨ All cache cleared successfully!');
}

export function forceReload() {
    console.log('🔄 Force reloading page...');
    clearAllCache();
    
    // Force reload without cache
    if (window.location.reload) {
        window.location.reload(true);
    } else {
        window.location.href = window.location.href;
    }
}

// Auto-clear cache on page load for fleet analyzer
if (window.location.pathname.includes('fleet-analyzer')) {
    clearAllCache();
}
