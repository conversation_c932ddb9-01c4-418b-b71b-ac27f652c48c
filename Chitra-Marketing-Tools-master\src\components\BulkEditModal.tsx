import React, { useState } from 'react';
import { X, AlertCircle, Info } from 'lucide-react';
import { SalesRevenueItem } from '../services/salesRevenue2025Service';

interface BulkEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: Partial<SalesRevenueItem>) => void;
  selectedCount: number;
  isAllDataSelected?: boolean;
  totalDataCount?: number;
}

const BulkEditModal: React.FC<BulkEditModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  selectedCount,
  isAllDataSelected = false,
  totalDataCount = 0
}) => {
  const [formData, setFormData] = useState<Partial<SalesRevenueItem>>({});
  const [fieldsToUpdate, setFieldsToUpdate] = useState<Record<string, boolean>>({
    customerName: false,
    salesman: false,
    materialDescription: false,
    qty: false,
    revenueInDocCurr: false,
    billingDate: false,
    poDate: false
  });

  if (!isOpen) return null;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;

    // Handle different input types
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: value === '' ? 0 : Number(value)
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFieldsToUpdate({
      ...fieldsToUpdate,
      [name]: checked
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Only include fields that are checked for update
    const dataToUpdate: Partial<SalesRevenueItem> = {};

    Object.keys(fieldsToUpdate).forEach(key => {
      if (fieldsToUpdate[key as keyof typeof fieldsToUpdate] && formData[key as keyof SalesRevenueItem] !== undefined) {
        dataToUpdate[key as keyof SalesRevenueItem] = formData[key as keyof SalesRevenueItem];
      }
    });

    onSubmit(dataToUpdate);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            Bulk Edit {isAllDataSelected ? totalDataCount : selectedCount} Items
            {isAllDataSelected && <span className="ml-2 text-blue-600 text-sm font-normal">(All Data)</span>}
          </h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        <div className="mb-4">
          <p className="text-gray-600">
            Select the fields you want to update and provide new values. Only checked fields will be updated.
          </p>

          {isAllDataSelected && (
            <div className="mt-2 p-3 bg-blue-50 border-l-4 border-blue-500 rounded-md flex items-start">
              <Info size={20} className="text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-blue-700">
                You are about to edit <strong>all {totalDataCount} items</strong> in the database.
                This operation cannot be undone. Please make sure you want to update all records.
              </p>
            </div>
          )}
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div className="flex items-center space-x-4 p-3 border rounded-md">
              <input
                type="checkbox"
                id="customerName-checkbox"
                name="customerName"
                checked={fieldsToUpdate.customerName}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <div className="flex-1">
                <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-1">
                  Customer Name
                </label>
                <input
                  type="text"
                  id="customerName"
                  name="customerName"
                  value={formData.customerName || ''}
                  onChange={handleChange}
                  className="w-full p-2 border rounded-md"
                  disabled={!fieldsToUpdate.customerName}
                />
              </div>
            </div>

            <div className="flex items-center space-x-4 p-3 border rounded-md">
              <input
                type="checkbox"
                id="salesman-checkbox"
                name="salesman"
                checked={fieldsToUpdate.salesman}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <div className="flex-1">
                <label htmlFor="salesman" className="block text-sm font-medium text-gray-700 mb-1">
                  Salesman
                </label>
                <input
                  type="text"
                  id="salesman"
                  name="salesman"
                  value={formData.salesman || ''}
                  onChange={handleChange}
                  className="w-full p-2 border rounded-md"
                  disabled={!fieldsToUpdate.salesman}
                />
              </div>
            </div>

            <div className="flex items-center space-x-4 p-3 border rounded-md">
              <input
                type="checkbox"
                id="materialDescription-checkbox"
                name="materialDescription"
                checked={fieldsToUpdate.materialDescription}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <div className="flex-1">
                <label htmlFor="materialDescription" className="block text-sm font-medium text-gray-700 mb-1">
                  Material Description
                </label>
                <input
                  type="text"
                  id="materialDescription"
                  name="materialDescription"
                  value={formData.materialDescription || ''}
                  onChange={handleChange}
                  className="w-full p-2 border rounded-md"
                  disabled={!fieldsToUpdate.materialDescription}
                />
              </div>
            </div>

            <div className="flex items-center space-x-4 p-3 border rounded-md">
              <input
                type="checkbox"
                id="qty-checkbox"
                name="qty"
                checked={fieldsToUpdate.qty}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <div className="flex-1">
                <label htmlFor="qty" className="block text-sm font-medium text-gray-700 mb-1">
                  Quantity
                </label>
                <input
                  type="number"
                  id="qty"
                  name="qty"
                  value={formData.qty || ''}
                  onChange={handleChange}
                  className="w-full p-2 border rounded-md"
                  disabled={!fieldsToUpdate.qty}
                  min="0"
                />
              </div>
            </div>

            <div className="flex items-center space-x-4 p-3 border rounded-md">
              <input
                type="checkbox"
                id="revenueInDocCurr-checkbox"
                name="revenueInDocCurr"
                checked={fieldsToUpdate.revenueInDocCurr}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <div className="flex-1">
                <label htmlFor="revenueInDocCurr" className="block text-sm font-medium text-gray-700 mb-1">
                  Revenue in Doc Curr
                </label>
                <input
                  type="number"
                  id="revenueInDocCurr"
                  name="revenueInDocCurr"
                  value={formData.revenueInDocCurr || ''}
                  onChange={handleChange}
                  className="w-full p-2 border rounded-md"
                  disabled={!fieldsToUpdate.revenueInDocCurr}
                  min="0"
                />
              </div>
            </div>

            <div className="flex items-center space-x-4 p-3 border rounded-md">
              <input
                type="checkbox"
                id="billingDate-checkbox"
                name="billingDate"
                checked={fieldsToUpdate.billingDate}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <div className="flex-1">
                <label htmlFor="billingDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Billing Date
                </label>
                <input
                  type="date"
                  id="billingDate"
                  name="billingDate"
                  value={formData.billingDate || ''}
                  onChange={handleChange}
                  className="w-full p-2 border rounded-md"
                  disabled={!fieldsToUpdate.billingDate}
                />
              </div>
            </div>

            <div className="flex items-center space-x-4 p-3 border rounded-md">
              <input
                type="checkbox"
                id="poDate-checkbox"
                name="poDate"
                checked={fieldsToUpdate.poDate}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <div className="flex-1">
                <label htmlFor="poDate" className="block text-sm font-medium text-gray-700 mb-1">
                  PO Date
                </label>
                <input
                  type="date"
                  id="poDate"
                  name="poDate"
                  value={formData.poDate || ''}
                  onChange={handleChange}
                  className="w-full p-2 border rounded-md"
                  disabled={!fieldsToUpdate.poDate}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Update Selected Items
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BulkEditModal;
