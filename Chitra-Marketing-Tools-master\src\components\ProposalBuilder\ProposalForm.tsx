import React, { useState } from 'react';
import { ProposalData, ProposalProduct, createProposalViaBackend } from '../../services/googleDocsService';
import { googleDocsService } from '../../services/googleDocsService';
import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  Grid,
  FormControl,
  InputLabel,
  OutlinedInput,
  InputAdornment,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { toast } from 'react-toastify';
import ProductSelector from './ProductSelector';

const ProposalForm: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [deliverable, setDeliverable] = useState('');
  const [formData, setFormData] = useState<ProposalData>({
    customerName: '',
    projectTitle: '',
    projectDescription: '',
    budget: 0,
    timeline: '',
    deliverables: [],
    termsAndConditions: '',
    products: [],
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'budget' ? Number(value) : value,
    }));
  };

  const handleAddDeliverable = () => {
    if (deliverable.trim()) {
      setFormData(prev => ({
        ...prev,
        deliverables: [...prev.deliverables, deliverable.trim()],
      }));
      setDeliverable('');
    }
  };

  const handleRemoveDeliverable = (index: number) => {
    setFormData(prev => ({
      ...prev,
      deliverables: prev.deliverables.filter((_, i) => i !== index),
    }));
  };

  const handleProductsChange = (products: ProposalProduct[]) => {
    setFormData(prev => ({
      ...prev,
      products,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const proposalContent = `
PROPOSAL ${formData.projectTitle}

Kepada Yth.
${formData.customerName}

DESKRIPSI PROYEK
${formData.projectDescription}

BUDGET
Rp ${formData.budget.toLocaleString('id-ID')}

TIMELINE
${formData.timeline}

DELIVERABLES
${formData.deliverables.map(item => `- ${item}`).join('\n')}

SYARAT DAN KETENTUAN
${formData.termsAndConditions}

Hormat kami,
[Tim Anda]
      `;
      const url = await createProposalViaBackend(formData.projectTitle, proposalContent);
      toast.success('Proposal berhasil dibuat!');
      window.open(url, '_blank');
    } catch (error) {
      toast.error('Gagal membuat proposal. Silakan coba lagi.');
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 4, maxWidth: 800, mx: 'auto', mt: 4 }}>
      <Typography variant="h4" gutterBottom align="center">
        Proposal Builder
      </Typography>

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Nama Pelanggan"
              name="customerName"
              value={formData.customerName}
              onChange={handleInputChange}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Judul Proyek"
              name="projectTitle"
              value={formData.projectTitle}
              onChange={handleInputChange}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              multiline
              rows={4}
              label="Deskripsi Proyek"
              name="projectDescription"
              value={formData.projectDescription}
              onChange={handleInputChange}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <Divider sx={{ my: 2 }} />
            <ProductSelector
              products={formData.products}
              onChange={handleProductsChange}
            />
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Budget</InputLabel>
              <OutlinedInput
                type="number"
                name="budget"
                value={formData.budget}
                onChange={handleInputChange}
                startAdornment={<InputAdornment position="start">Rp</InputAdornment>}
                required
              />
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Timeline"
              name="timeline"
              value={formData.timeline}
              onChange={handleInputChange}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <TextField
                fullWidth
                label="Deliverable"
                value={deliverable}
                onChange={(e) => setDeliverable(e.target.value)}
              />
              <IconButton
                color="primary"
                onClick={handleAddDeliverable}
                disabled={!deliverable.trim()}
              >
                <AddIcon />
              </IconButton>
            </Box>

            <List>
              {formData.deliverables.map((item, index) => (
                <ListItem key={index}>
                  <ListItemText primary={item} />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleRemoveDeliverable(index)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              multiline
              rows={4}
              label="Syarat dan Ketentuan"
              name="termsAndConditions"
              value={formData.termsAndConditions}
              onChange={handleInputChange}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              fullWidth
              size="large"
              disabled={loading}
            >
              {loading ? 'Membuat Proposal...' : 'Buat Proposal'}
            </Button>
          </Grid>
        </Grid>
      </form>
    </Paper>
  );
};

export default ProposalForm; 