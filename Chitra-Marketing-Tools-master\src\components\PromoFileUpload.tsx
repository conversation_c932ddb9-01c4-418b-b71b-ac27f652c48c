import React, { useState, useEffect, useRef } from 'react';
import { 
  Upload, 
  File, 
  Image, 
  X, 
  ExternalLink, 
  AlertCircle 
} from 'lucide-react';
import { 
  uploadFile, 
  getUploadedFilesByRelatedId, 
  deleteUploadedFile,
  UploadedFile,
  MAX_FILE_SIZE,
  ALLOWED_FILE_TYPES
} from '../services/fileUploadService';

interface PromoFileUploadProps {
  promoId?: string;
  onFilesChange?: (files: UploadedFile[]) => void;
}

const PromoFileUpload: React.FC<PromoFileUploadProps> = ({
  promoId,
  onFilesChange
}) => {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load files when promoId changes
  useEffect(() => {
    if (promoId) {
      loadFiles();
    }
  }, [promoId]);

  // Load files
  const loadFiles = () => {
    if (!promoId) return;
    
    const uploadedFiles = getUploadedFilesByRelatedId(promoId);
    setFiles(uploadedFiles);
    
    if (onFilesChange) {
      onFilesChange(uploadedFiles);
    }
  };

  // Handle file input change
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    
    if (!selectedFiles || selectedFiles.length === 0) {
      return;
    }
    
    setIsUploading(true);
    setError(null);
    
    try {
      const file = selectedFiles[0]; // Only upload one file at a time
      
      // Validate file size
      if (file.size > MAX_FILE_SIZE) {
        throw new Error(`File terlalu besar. Maksimal ukuran file adalah ${MAX_FILE_SIZE / (1024 * 1024)} MB.`);
      }
      
      // Validate file type
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        throw new Error('Tipe file tidak diizinkan. Silakan unggah file PDF atau gambar (JPEG, PNG, GIF).');
      }
      
      // Upload file
      const uploadedFile = await uploadFile(file, promoId);
      
      // Update files state
      setFiles(prevFiles => [...prevFiles, uploadedFile]);
      
      // Notify parent component
      if (onFilesChange) {
        onFilesChange([...files, uploadedFile]);
      }
      
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      setError(error instanceof Error ? error.message : 'Gagal mengunggah file.');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle delete file
  const handleDeleteFile = (id: string) => {
    const success = deleteUploadedFile(id);
    
    if (success) {
      // Update files state
      const updatedFiles = files.filter(file => file.id !== id);
      setFiles(updatedFiles);
      
      // Notify parent component
      if (onFilesChange) {
        onFilesChange(updatedFiles);
      }
    } else {
      setError('Gagal menghapus file.');
    }
  };

  // Format file size
  const formatFileSize = (size: number): string => {
    if (size < 1024) {
      return `${size} B`;
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(1)} KB`;
    } else {
      return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    }
  };

  // Get file icon
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <Image size={16} className="text-blue-500" />;
    } else if (fileType === 'application/pdf') {
      return <File size={16} className="text-red-500" />;
    } else {
      return <File size={16} className="text-gray-500" />;
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">Referensi Promo</h2>
      
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          className="hidden"
          accept={ALLOWED_FILE_TYPES.join(',')}
        />
        
        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <Upload size={16} className="mr-2" />
          {isUploading ? 'Mengunggah...' : 'Unggah File'}
        </button>
        
        <p className="mt-2 text-sm text-gray-500">
          Unggah file PDF atau gambar (maks. 5 MB)
        </p>
        
        {error && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700 flex items-start">
            <AlertCircle size={16} className="mr-1 mt-0.5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}
      </div>
      
      {files.length > 0 && (
        <div className="mt-4">
          <h3 className="text-sm font-medium text-gray-700 mb-2">File Terunggah</h3>
          <div className="space-y-2">
            {files.map(file => (
              <div 
                key={file.id} 
                className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-md"
              >
                <div className="flex items-center space-x-3">
                  {getFileIcon(file.type)}
                  <div>
                    <p className="text-sm font-medium text-gray-900">{file.name}</p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.size)} • {new Date(file.uploadDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => window.open(file.dataUrl, '_blank')}
                    className="p-1 text-gray-500 hover:text-blue-500"
                    title="Lihat file"
                  >
                    <ExternalLink size={16} />
                  </button>
                  <button
                    type="button"
                    onClick={() => handleDeleteFile(file.id)}
                    className="p-1 text-gray-500 hover:text-red-500"
                    title="Hapus file"
                  >
                    <X size={16} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default PromoFileUpload;
