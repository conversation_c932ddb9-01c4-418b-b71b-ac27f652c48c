import React from 'react';
import {
  Palette,
  Type,
  Box,
  Layers,
  Square,
  Circle,
  Triangle,
  Copy,
  Check,
  AlertCircle,
  Info,
  Search,
  Mail,
  User,
  Users,
  BarChart,
  DollarSign,
  Package,
  TrendingUp,
  ArrowRight
} from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '../components/ui/card';
import DashboardCard from '../components/dashboard/DashboardCard';
import StatCard from '../components/dashboard/StatCard';
import { colors } from '../styles/tokens';

export default function StyleGuidePage() {
  // Function to copy color code to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert(`Copied: ${text}`);
  };

  return (
    <div className="container mx-auto p-6 space-y-12">
      <div className="space-y-4">
        <h1 className="text-3xl font-bold">Chitra Marketing Tools Design System</h1>
        <p className="text-gray-600 dark:text-gray-400 max-w-3xl">
          This style guide provides a comprehensive overview of the design elements and components used throughout the Chitra Marketing Tools application.
        </p>
      </div>

      {/* Color Palette */}
      <section id="colors" className="space-y-6">
        <div className="flex items-center gap-2 border-b pb-2">
          <Palette className="h-5 w-5 text-primary-500" />
          <h2 className="text-2xl font-semibold">Color Palette</h2>
        </div>

        <div className="space-y-6">
          {/* Primary Colors */}
          <div>
            <h3 className="text-lg font-medium mb-3">Primary Colors</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              {Object.entries(colors.primary).map(([key, value]) => (
                <div
                  key={`primary-${key}`}
                  className="rounded-md overflow-hidden shadow-sm border cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => copyToClipboard(value)}
                >
                  <div className="h-20" style={{ backgroundColor: value }}></div>
                  <div className="p-2 bg-white dark:bg-gray-800">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Primary {key}</span>
                      <Copy className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{value}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Secondary Colors */}
          <div>
            <h3 className="text-lg font-medium mb-3">Secondary Colors</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              {Object.entries(colors.secondary).map(([key, value]) => (
                <div
                  key={`secondary-${key}`}
                  className="rounded-md overflow-hidden shadow-sm border cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => copyToClipboard(value)}
                >
                  <div className="h-20" style={{ backgroundColor: value }}></div>
                  <div className="p-2 bg-white dark:bg-gray-800">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Secondary {key}</span>
                      <Copy className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{value}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Accent Colors */}
          <div>
            <h3 className="text-lg font-medium mb-3">Accent Colors</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              {Object.entries(colors.accent).map(([key, value]) => (
                <div
                  key={`accent-${key}`}
                  className="rounded-md overflow-hidden shadow-sm border cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => copyToClipboard(value)}
                >
                  <div className="h-20" style={{ backgroundColor: value }}></div>
                  <div className="p-2 bg-white dark:bg-gray-800">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Accent {key}</span>
                      <Copy className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{value}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Semantic Colors */}
          <div>
            <h3 className="text-lg font-medium mb-3">Semantic Colors</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div
                className="rounded-md overflow-hidden shadow-sm border cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => copyToClipboard(colors.success[500])}
              >
                <div className="h-20" style={{ backgroundColor: colors.success[500] }}></div>
                <div className="p-2 bg-white dark:bg-gray-800">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Success</span>
                    <Copy className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{colors.success[500]}</div>
                </div>
              </div>
              <div
                className="rounded-md overflow-hidden shadow-sm border cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => copyToClipboard(colors.warning[500])}
              >
                <div className="h-20" style={{ backgroundColor: colors.warning[500] }}></div>
                <div className="p-2 bg-white dark:bg-gray-800">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Warning</span>
                    <Copy className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{colors.warning[500]}</div>
                </div>
              </div>
              <div
                className="rounded-md overflow-hidden shadow-sm border cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => copyToClipboard(colors.error[500])}
              >
                <div className="h-20" style={{ backgroundColor: colors.error[500] }}></div>
                <div className="p-2 bg-white dark:bg-gray-800">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Error</span>
                    <Copy className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{colors.error[500]}</div>
                </div>
              </div>
              <div
                className="rounded-md overflow-hidden shadow-sm border cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => copyToClipboard(colors.info[500])}
              >
                <div className="h-20" style={{ backgroundColor: colors.info[500] }}></div>
                <div className="p-2 bg-white dark:bg-gray-800">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Info</span>
                    <Copy className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{colors.info[500]}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Typography */}
      <section id="typography" className="space-y-6">
        <div className="flex items-center gap-2 border-b pb-2">
          <Type className="h-5 w-5 text-primary-500" />
          <h2 className="text-2xl font-semibold">Typography</h2>
        </div>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-3">Headings</h3>
            <div className="space-y-4 p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <div>
                <h1 className="text-5xl font-bold">Heading 1</h1>
                <div className="text-sm text-gray-500 mt-1">text-5xl font-bold</div>
              </div>
              <div>
                <h2 className="text-4xl font-bold">Heading 2</h2>
                <div className="text-sm text-gray-500 mt-1">text-4xl font-bold</div>
              </div>
              <div>
                <h3 className="text-3xl font-semibold">Heading 3</h3>
                <div className="text-sm text-gray-500 mt-1">text-3xl font-semibold</div>
              </div>
              <div>
                <h4 className="text-2xl font-semibold">Heading 4</h4>
                <div className="text-sm text-gray-500 mt-1">text-2xl font-semibold</div>
              </div>
              <div>
                <h5 className="text-xl font-medium">Heading 5</h5>
                <div className="text-sm text-gray-500 mt-1">text-xl font-medium</div>
              </div>
              <div>
                <h6 className="text-lg font-medium">Heading 6</h6>
                <div className="text-sm text-gray-500 mt-1">text-lg font-medium</div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-3">Body Text</h3>
            <div className="space-y-4 p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <div>
                <p className="text-base">
                  This is the default body text. It should be used for most paragraph content throughout the application.
                </p>
                <div className="text-sm text-gray-500 mt-1">text-base</div>
              </div>
              <div>
                <p className="text-sm">
                  This is smaller text, useful for secondary information or UI elements where space is limited.
                </p>
                <div className="text-sm text-gray-500 mt-1">text-sm</div>
              </div>
              <div>
                <p className="text-xs">
                  This is extra small text, used for captions, footnotes, or very compact UI elements.
                </p>
                <div className="text-sm text-gray-500 mt-1">text-xs</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Buttons */}
      <section id="buttons" className="space-y-6">
        <div className="flex items-center gap-2 border-b pb-2">
          <Square className="h-5 w-5 text-primary-500" />
          <h2 className="text-2xl font-semibold">Buttons</h2>
        </div>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-3">Button Variants</h3>
            <div className="flex flex-wrap gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <Button variant="default">Default</Button>
              <Button variant="primary">Primary</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="accent">Accent</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="link">Link</Button>
              <Button variant="destructive">Destructive</Button>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-3">Button Sizes</h3>
            <div className="flex flex-wrap items-center gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <Button variant="primary" size="sm">Small</Button>
              <Button variant="primary" size="default">Default</Button>
              <Button variant="primary" size="lg">Large</Button>
              <Button variant="primary" size="xl">Extra Large</Button>
              <Button variant="outline" size="icon"><Search className="h-4 w-4" /></Button>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-3">Button States</h3>
            <div className="flex flex-wrap gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <Button variant="primary" disabled>Disabled</Button>
              <Button variant="primary" isLoading>Loading</Button>
              <Button variant="primary" leftIcon={<Mail className="h-4 w-4" />}>With Icon</Button>
              <Button variant="primary" rightIcon={<ArrowRight className="h-4 w-4" />}>With Icon</Button>
            </div>
          </div>
        </div>
      </section>

      {/* Inputs */}
      <section id="inputs" className="space-y-6">
        <div className="flex items-center gap-2 border-b pb-2">
          <Type className="h-5 w-5 text-primary-500" />
          <h2 className="text-2xl font-semibold">Inputs</h2>
        </div>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-3">Input Variants</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <Input placeholder="Default input" />
              <Input variant="filled" placeholder="Filled input" />
              <Input variant="outline" placeholder="Outline input" />
              <Input variant="flushed" placeholder="Flushed input" />
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-3">Input Sizes</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <Input size="sm" placeholder="Small input" />
              <Input size="default" placeholder="Default input" />
              <Input size="lg" placeholder="Large input" />
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-3">Input States</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <Input disabled placeholder="Disabled input" />
              <Input error errorMessage="This field is required" placeholder="Error input" />
              <Input leftElement={<Search className="h-4 w-4" />} placeholder="With left icon" />
              <Input rightElement={<User className="h-4 w-4" />} placeholder="With right icon" />
            </div>
          </div>
        </div>
      </section>

      {/* Badges */}
      <section id="badges" className="space-y-6">
        <div className="flex items-center gap-2 border-b pb-2">
          <Circle className="h-5 w-5 text-primary-500" />
          <h2 className="text-2xl font-semibold">Badges</h2>
        </div>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-3">Badge Variants</h3>
            <div className="flex flex-wrap gap-2 p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <Badge variant="default">Default</Badge>
              <Badge variant="primary">Primary</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="accent">Accent</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="destructive">Destructive</Badge>
              <Badge variant="success">Success</Badge>
              <Badge variant="warning">Warning</Badge>
              <Badge variant="info">Info</Badge>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-3">Badge Sizes</h3>
            <div className="flex flex-wrap items-center gap-2 p-4 bg-white dark:bg-gray-800 rounded-lg border">
              <Badge variant="primary" size="sm">Small</Badge>
              <Badge variant="primary" size="default">Default</Badge>
              <Badge variant="primary" size="lg">Large</Badge>
            </div>
          </div>
        </div>
      </section>

      {/* Cards */}
      <section id="cards" className="space-y-6">
        <div className="flex items-center gap-2 border-b pb-2">
          <Layers className="h-5 w-5 text-primary-500" />
          <h2 className="text-2xl font-semibold">Cards</h2>
        </div>

        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-3">Card Variants</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Default Card</CardTitle>
                  <CardDescription>This is a default card with header, content, and footer.</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>This is the card content where the main information is displayed.</p>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="mr-2">Cancel</Button>
                  <Button>Submit</Button>
                </CardFooter>
              </Card>

              <Card variant="outline">
                <CardHeader>
                  <CardTitle>Outline Card</CardTitle>
                  <CardDescription>This is an outline card with no shadow.</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>This is the card content where the main information is displayed.</p>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="mr-2">Cancel</Button>
                  <Button>Submit</Button>
                </CardFooter>
              </Card>

              <Card variant="filled">
                <CardHeader>
                  <CardTitle>Filled Card</CardTitle>
                  <CardDescription>This is a filled card with a background color.</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>This is the card content where the main information is displayed.</p>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="mr-2">Cancel</Button>
                  <Button>Submit</Button>
                </CardFooter>
              </Card>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-3">Dashboard Cards</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <DashboardCard
                title="Sales Overview"
                icon={BarChart}
                actions={<Button variant="ghost" size="sm">View All</Button>}
              >
                <div className="h-40 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded">
                  Chart Placeholder
                </div>
              </DashboardCard>

              <DashboardCard
                title="Recent Orders"
                icon={Package}
                footer={<Button variant="link" size="sm" className="ml-auto">View All Orders</Button>}
              >
                <div className="space-y-2">
                  <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <span>Order #1234</span>
                    <span>$1,200</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <span>Order #1235</span>
                    <span>$850</span>
                  </div>
                  <div className="flex justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <span>Order #1236</span>
                    <span>$2,300</span>
                  </div>
                </div>
              </DashboardCard>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-3">Stat Cards</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <StatCard
                title="Total Customers"
                value="1,234"
                icon={Users}
                description="Active customers"
                trend="up"
                trendValue="12% vs last month"
                variant="primary"
              />
              <StatCard
                title="Total Products"
                value="567"
                icon={Package}
                description="Available products"
                variant="secondary"
              />
              <StatCard
                title="Total Revenue"
                value="$12,345"
                icon={DollarSign}
                description="Current month"
                trend="up"
                trendValue="8% vs last month"
                variant="accent"
              />
              <StatCard
                title="Conversion Rate"
                value="3.2%"
                icon={TrendingUp}
                description="Current month"
                trend="down"
                trendValue="0.5% vs last month"
                variant="outline"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
