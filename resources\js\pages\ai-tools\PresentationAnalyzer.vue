<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">AI Presentation Analyzer</h1>
                    <p class="mt-2 text-gray-600">AI-powered analysis and optimization of business presentations</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="uploadPresentation"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <Upload class="h-4 w-4 mr-2" />
                        Upload Presentation
                    </button>
                    <button
                        @click="exportAnalysis"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Analysis
                    </button>
                </div>
            </div>

            <!-- Upload Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Upload Presentation</h3>
                
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <Presentation class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h4 class="text-lg font-medium text-gray-900 mb-2">Drop your presentation here</h4>
                    <p class="text-gray-600 mb-4">
                        Supports PowerPoint (.pptx), PDF, and Google Slides files up to 50MB
                    </p>
                    <div class="flex justify-center space-x-4">
                        <button
                            @click="selectFile"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                        >
                            Select File
                        </button>
                        <button
                            @click="showDemo"
                            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                        >
                            View Demo Analysis
                        </button>
                    </div>
                </div>

                <div class="mt-4 text-sm text-gray-500">
                    <p><strong>What we analyze:</strong></p>
                    <ul class="list-disc list-inside mt-2 space-y-1">
                        <li>Content structure and flow</li>
                        <li>Visual design and consistency</li>
                        <li>Message clarity and impact</li>
                        <li>Audience engagement potential</li>
                        <li>Technical presentation quality</li>
                    </ul>
                </div>
            </div>

            <!-- Analysis Results -->
            <div v-if="analysisResults" class="space-y-6">
                <!-- Overall Score -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">Presentation Score</h3>
                        <div class="flex items-center space-x-4">
                            <div class="text-right">
                                <p class="text-3xl font-bold text-blue-600">{{ analysisResults.overallScore }}/100</p>
                                <p class="text-sm text-gray-600">{{ analysisResults.scoreCategory }}</p>
                            </div>
                            <div class="w-16 h-16 relative">
                                <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                                    <path
                                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="#e5e7eb"
                                        stroke-width="3"
                                    />
                                    <path
                                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                        fill="none"
                                        stroke="#3b82f6"
                                        stroke-width="3"
                                        :stroke-dasharray="`${analysisResults.overallScore}, 100`"
                                    />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Scores -->
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
                        <div v-for="metric in analysisResults.detailedScores" :key="metric.name" class="text-center">
                            <component :is="metric.icon" :class="[
                                'h-8 w-8 mx-auto mb-2',
                                metric.score >= 80 ? 'text-green-600' :
                                metric.score >= 60 ? 'text-yellow-600' :
                                'text-red-600'
                            ]" />
                            <p class="text-sm font-medium text-gray-900">{{ metric.name }}</p>
                            <p class="text-lg font-bold text-gray-900">{{ metric.score }}</p>
                            <p :class="[
                                'text-xs',
                                metric.score >= 80 ? 'text-green-600' :
                                metric.score >= 60 ? 'text-yellow-600' :
                                'text-red-600'
                            ]">
                                {{ metric.status }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Slide Analysis -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Slide-by-Slide Analysis</h3>
                    <div class="space-y-4">
                        <div v-for="slide in analysisResults.slideAnalysis" :key="slide.number" class="flex items-start p-4 border border-gray-200 rounded-lg">
                            <div class="flex-shrink-0 w-16 h-12 bg-gray-200 rounded flex items-center justify-center mr-4">
                                <span class="text-sm font-medium text-gray-600">{{ slide.number }}</span>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-gray-900">{{ slide.title }}</h4>
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        slide.score >= 80 ? 'bg-green-100 text-green-800' :
                                        slide.score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-red-100 text-red-800'
                                    ]">
                                        {{ slide.score }}/100
                                    </span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">{{ slide.feedback }}</p>
                                <div class="flex flex-wrap gap-2">
                                    <span v-for="issue in slide.issues" :key="issue" class="px-2 py-1 bg-red-100 text-red-700 text-xs rounded">
                                        {{ issue }}
                                    </span>
                                    <span v-for="strength in slide.strengths" :key="strength" class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">
                                        {{ strength }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">AI Recommendations</h3>
                    <div class="space-y-4">
                        <div v-for="recommendation in analysisResults.recommendations" :key="recommendation.id" class="flex items-start p-4 border border-gray-200 rounded-lg">
                            <Lightbulb class="h-6 w-6 text-blue-600 mt-1 mr-4 flex-shrink-0" />
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="font-medium text-gray-900">{{ recommendation.title }}</h4>
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        recommendation.priority === 'High' ? 'bg-red-100 text-red-800' :
                                        recommendation.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-green-100 text-green-800'
                                    ]">
                                        {{ recommendation.priority }} Priority
                                    </span>
                                </div>
                                <p class="text-sm text-gray-700 mb-2">{{ recommendation.description }}</p>
                                <p class="text-xs text-blue-600">Expected improvement: {{ recommendation.improvement }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Analysis -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Content Structure -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Content Structure</h3>
                        <div class="space-y-4">
                            <div v-for="section in analysisResults.contentStructure" :key="section.name" class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <component :is="section.icon" class="h-5 w-5 text-gray-600 mr-3" />
                                    <span class="text-sm font-medium text-gray-900">{{ section.name }}</span>
                                </div>
                                <div class="flex items-center">
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full mr-2',
                                        section.status === 'Good' ? 'bg-green-100 text-green-800' :
                                        section.status === 'Needs Improvement' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-red-100 text-red-800'
                                    ]">
                                        {{ section.status }}
                                    </span>
                                    <span class="text-sm text-gray-600">{{ section.slides }} slides</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Visual Design -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Visual Design Analysis</h3>
                        <div class="space-y-4">
                            <div v-for="aspect in analysisResults.visualDesign" :key="aspect.name" class="flex items-center justify-between">
                                <span class="text-sm font-medium text-gray-900">{{ aspect.name }}</span>
                                <div class="flex items-center">
                                    <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                        <div 
                                            :class="[
                                                'h-2 rounded-full',
                                                aspect.score >= 80 ? 'bg-green-500' :
                                                aspect.score >= 60 ? 'bg-yellow-500' :
                                                'bg-red-500'
                                            ]"
                                            :style="{ width: aspect.score + '%' }"
                                        ></div>
                                    </div>
                                    <span class="text-sm text-gray-600">{{ aspect.score }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Audience Engagement -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Audience Engagement Potential</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <Users class="h-8 w-8 text-blue-600 mx-auto mb-2" />
                            <h4 class="font-medium text-blue-900">Attention Score</h4>
                            <p class="text-2xl font-bold text-blue-900">{{ analysisResults.engagement.attention }}%</p>
                            <p class="text-sm text-blue-700">{{ analysisResults.engagement.attentionLevel }}</p>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <MessageSquare class="h-8 w-8 text-green-600 mx-auto mb-2" />
                            <h4 class="font-medium text-green-900">Clarity Score</h4>
                            <p class="text-2xl font-bold text-green-900">{{ analysisResults.engagement.clarity }}%</p>
                            <p class="text-sm text-green-700">{{ analysisResults.engagement.clarityLevel }}</p>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <Zap class="h-8 w-8 text-purple-600 mx-auto mb-2" />
                            <h4 class="font-medium text-purple-900">Impact Score</h4>
                            <p class="text-2xl font-bold text-purple-900">{{ analysisResults.engagement.impact }}%</p>
                            <p class="text-sm text-purple-700">{{ analysisResults.engagement.impactLevel }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-blue-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-blue-900">Advanced Presentation Analysis Coming Soon</h3>
                        <p class="text-blue-700 mt-1">
                            Enhanced presentation analysis features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-blue-700 mt-2 space-y-1">
                            <li>Real-time presentation coaching and feedback</li>
                            <li>Voice and delivery analysis for recorded presentations</li>
                            <li>Audience attention heatmaps and engagement tracking</li>
                            <li>Automated slide generation and content suggestions</li>
                            <li>Integration with presentation software (PowerPoint, Google Slides)</li>
                            <li>Multi-language presentation analysis and translation</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Upload,
    Download,
    Presentation,
    Lightbulb,
    Users,
    MessageSquare,
    Zap,
    FileText,
    Image,
    BarChart3,
    Eye,
    Palette,
    TrendingUp,
    Info
} from 'lucide-vue-next';
import { ref, onMounted } from 'vue';

// Reactive state
const analysisResults = ref(null);

// Demo analysis data
const demoAnalysis = {
    overallScore: 82,
    scoreCategory: 'Very Good',
    detailedScores: [
        { name: 'Content', score: 85, status: 'Excellent', icon: FileText },
        { name: 'Design', score: 78, status: 'Good', icon: Palette },
        { name: 'Structure', score: 80, status: 'Very Good', icon: BarChart3 },
        { name: 'Clarity', score: 88, status: 'Excellent', icon: Eye },
        { name: 'Engagement', score: 75, status: 'Good', icon: Users }
    ],
    slideAnalysis: [
        {
            number: 1,
            title: 'Title Slide - Company Introduction',
            score: 90,
            feedback: 'Strong opening with clear branding and professional design',
            issues: [],
            strengths: ['Clear branding', 'Professional design']
        },
        {
            number: 2,
            title: 'Agenda Overview',
            score: 85,
            feedback: 'Well-structured agenda with clear navigation',
            issues: [],
            strengths: ['Clear structure', 'Good navigation']
        },
        {
            number: 3,
            title: 'Market Analysis',
            score: 75,
            feedback: 'Good content but could benefit from better visual hierarchy',
            issues: ['Text heavy', 'Small fonts'],
            strengths: ['Comprehensive data']
        },
        {
            number: 4,
            title: 'Product Portfolio',
            score: 88,
            feedback: 'Excellent visual presentation of products with clear benefits',
            issues: [],
            strengths: ['Great visuals', 'Clear benefits']
        },
        {
            number: 5,
            title: 'Financial Projections',
            score: 70,
            feedback: 'Data is comprehensive but presentation could be clearer',
            issues: ['Complex charts', 'Too much data'],
            strengths: ['Comprehensive analysis']
        }
    ],
    recommendations: [
        {
            id: 1,
            title: 'Improve Visual Hierarchy',
            description: 'Use consistent font sizes and spacing to create better visual hierarchy across slides',
            priority: 'High',
            improvement: '15% clarity increase'
        },
        {
            id: 2,
            title: 'Simplify Complex Charts',
            description: 'Break down complex financial charts into simpler, more digestible visualizations',
            priority: 'High',
            improvement: '20% comprehension increase'
        },
        {
            id: 3,
            title: 'Add Call-to-Action Slides',
            description: 'Include clear next steps and call-to-action slides throughout the presentation',
            priority: 'Medium',
            improvement: '10% engagement increase'
        },
        {
            id: 4,
            title: 'Enhance Brand Consistency',
            description: 'Ensure consistent use of brand colors, fonts, and styling across all slides',
            priority: 'Medium',
            improvement: '8% professional appearance'
        }
    ],
    contentStructure: [
        { name: 'Introduction', status: 'Good', slides: 2, icon: FileText },
        { name: 'Problem Statement', status: 'Needs Improvement', slides: 1, icon: MessageSquare },
        { name: 'Solution Overview', status: 'Good', slides: 3, icon: Lightbulb },
        { name: 'Benefits & Value', status: 'Excellent', slides: 2, icon: TrendingUp },
        { name: 'Implementation', status: 'Good', slides: 2, icon: BarChart3 },
        { name: 'Conclusion', status: 'Good', slides: 1, icon: FileText }
    ],
    visualDesign: [
        { name: 'Color Consistency', score: 85 },
        { name: 'Font Usage', score: 78 },
        { name: 'Image Quality', score: 90 },
        { name: 'Layout Balance', score: 75 },
        { name: 'Brand Alignment', score: 82 }
    ],
    engagement: {
        attention: 78,
        attentionLevel: 'High',
        clarity: 85,
        clarityLevel: 'Very High',
        impact: 72,
        impactLevel: 'Good'
    }
};

// Event handlers
const uploadPresentation = () => {
    alert('Presentation upload functionality coming soon!\n\nFeatures:\n- Drag & drop PowerPoint files\n- PDF presentation analysis\n- Google Slides integration\n- Real-time analysis progress');
};

const selectFile = () => {
    // Create file input element
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pptx,.pdf,.ppt';
    input.onchange = (event) => {
        const file = (event.target as HTMLInputElement).files?.[0];
        if (file) {
            alert(`File selected: ${file.name}\n\nAnalyzing presentation...\n\nThis will analyze:\n- Content structure and flow\n- Visual design consistency\n- Message clarity\n- Audience engagement potential\n- Technical quality`);
            // Show demo analysis after "processing"
            setTimeout(() => {
                analysisResults.value = demoAnalysis;
            }, 3000);
        }
    };
    input.click();
};

const showDemo = () => {
    analysisResults.value = demoAnalysis;
    alert('Demo analysis loaded!\n\nThis shows sample analysis of a business presentation for tire company product showcase.');
};

const exportAnalysis = () => {
    if (!analysisResults.value) {
        alert('No analysis to export. Please upload a presentation first.');
        return;
    }

    try {
        const csvContent = [
            ['Presentation Analysis Report'].join(','),
            ['Generated:', new Date().toLocaleDateString('id-ID')].join(','),
            ['Overall Score:', `${analysisResults.value.overallScore}/100`].join(','),
            [],
            ['Detailed Scores'].join(','),
            ['Category', 'Score', 'Status'].join(','),
            ...analysisResults.value.detailedScores.map(score => [score.name, `${score.score}/100`, score.status].join(',')),
            [],
            ['Slide Analysis'].join(','),
            ['Slide', 'Title', 'Score', 'Feedback'].join(','),
            ...analysisResults.value.slideAnalysis.map(slide => [
                slide.number,
                `"${slide.title}"`,
                `${slide.score}/100`,
                `"${slide.feedback}"`
            ].join(',')),
            [],
            ['Recommendations'].join(','),
            ['Title', 'Description', 'Priority', 'Expected Improvement'].join(','),
            ...analysisResults.value.recommendations.map(rec => [
                `"${rec.title}"`,
                `"${rec.description}"`,
                rec.priority,
                rec.improvement
            ].join(',')),
            [],
            ['Content Structure'].join(','),
            ['Section', 'Status', 'Slides'].join(','),
            ...analysisResults.value.contentStructure.map(section => [
                section.name,
                section.status,
                section.slides
            ].join(',')),
            [],
            ['Visual Design Scores'].join(','),
            ['Aspect', 'Score'].join(','),
            ...analysisResults.value.visualDesign.map(aspect => [
                aspect.name,
                `${aspect.score}%`
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `presentation_analysis_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Presentation analysis exported successfully!');
    } catch (error) {
        console.error('Error exporting analysis:', error);
        alert('Failed to export analysis.');
    }
};

// Initialize on mount
onMounted(() => {
    // Load any saved analysis
});
</script>
