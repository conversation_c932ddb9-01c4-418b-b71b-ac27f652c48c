import React, { useState } from 'react';
import { MessageSquare, Loader2, ExternalLink, Brain, History, Copy, CheckCheck } from 'lucide-react';
import { analyzeWhatsAppWebConversation, saveWhatsAppWebConversation } from '../services/negotiationService';

const WhatsAppWebAnalysis: React.FC = () => {
  const [analysis, setAnalysis] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [question, setQuestion] = useState('');
  const [recentQuestions, setRecentQuestions] = useState<string[]>([]);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  // Function to open WhatsApp Web in external browser
  const openWhatsAppWeb = () => {
    window.open('https://web.whatsapp.com/', '_blank');
  };

  // Function to analyze the current conversation with AI
  const handleAnalyze = async () => {
    if (!question) {
      setError('<PERSON>hon masukkan pertanyaan yang ingin dianalisis.');
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      // Save the question for future reference
      saveWhatsAppWebConversation(
        question,
        "WhatsApp Web User",
        "AI Assistant"
      );

      // Add to recent questions (max 5)
      setRecentQuestions(prev => {
        const newQuestions = [question, ...prev];
        return newQuestions.slice(0, 5);
      });

      // Analyze conversation with the question
      const result = await analyzeWhatsAppWebConversation(
        "Pertanyaan: " + question,
        "Customer", // Placeholder
        "Sales" // Placeholder
      );

      setAnalysis(result.message);
    } catch (err) {
      console.error('Error analyzing WhatsApp Web conversation:', err);
      setError('Terjadi kesalahan saat menganalisis percakapan. Silakan coba lagi.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Function to use a recent question
  const useRecentQuestion = (question: string) => {
    setQuestion(question);
  };

  // Function to copy analysis to clipboard
  const copyToClipboard = (text: string, index: number) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6 flex items-center">
        <MessageSquare className="mr-2" /> WhatsApp Web AI Assistant
      </h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Panel - AI Assistant */}
        <div className="lg:col-span-2">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold flex items-center">
                <Brain className="mr-2" size={20} /> AI Assistant untuk WhatsApp
              </h2>
              <button
                onClick={openWhatsAppWeb}
                className="flex items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
              >
                <ExternalLink size={16} className="mr-2" />
                Buka WhatsApp Web
              </button>
            </div>

            <div className="mb-6">
              <p className="text-gray-700 mb-4">
                AI Assistant ini akan membantu Anda dalam percakapan WhatsApp dengan pelanggan.
                Ajukan pertanyaan tentang strategi negosiasi, cara merespons pelanggan, atau teknik penjualan.
              </p>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-md mb-4">
                <h3 className="font-medium text-blue-800 mb-2 flex items-center">
                  <MessageSquare size={16} className="mr-2" /> Contoh Pertanyaan:
                </h3>
                <ul className="text-blue-700 space-y-2 text-sm">
                  <li>• Bagaimana cara merespons pelanggan yang meminta diskon besar?</li>
                  <li>• Apa strategi terbaik untuk menawarkan bundling produk ban?</li>
                  <li>• Bagaimana cara menangani keberatan pelanggan tentang harga?</li>
                  <li>• Template pesan untuk follow-up pelanggan yang lama tidak merespons?</li>
                  <li>• Cara meyakinkan pelanggan tentang kualitas produk ban kita?</li>
                </ul>
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tanyakan AI Assistant
              </label>
              <textarea
                value={question}
                onChange={(e) => setQuestion(e.target.value)}
                className="w-full p-3 border rounded-md h-32 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Masukkan pertanyaan Anda tentang percakapan WhatsApp dengan pelanggan..."
              ></textarea>
            </div>

            {recentQuestions.length > 0 && (
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <History size={14} className="mr-1" /> Pertanyaan Terakhir:
                </h3>
                <div className="space-y-2">
                  {recentQuestions.map((q, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
                      <button
                        onClick={() => useRecentQuestion(q)}
                        className="text-sm text-left text-gray-700 hover:text-blue-600 truncate flex-1"
                      >
                        {q}
                      </button>
                      <button
                        onClick={() => copyToClipboard(q, index)}
                        className="p-1 text-gray-500 hover:text-blue-600"
                        title="Salin pertanyaan"
                      >
                        {copiedIndex === index ? <CheckCheck size={14} className="text-green-500" /> : <Copy size={14} />}
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <button
              onClick={handleAnalyze}
              disabled={isAnalyzing}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 flex items-center justify-center"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 className="animate-spin mr-2" size={18} />
                  Menganalisis...
                </>
              ) : (
                <>
                  <Brain className="mr-2" size={18} />
                  Dapatkan Jawaban AI
                </>
              )}
            </button>

            {error && (
              <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
                {error}
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Analysis Results */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow-md h-full">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold flex items-center">
                <Brain className="mr-2" size={20} /> Jawaban AI
              </h2>
              {analysis && (
                <button
                  onClick={() => copyToClipboard(analysis, -1)}
                  className="p-2 text-gray-600 hover:text-blue-600 rounded-full hover:bg-gray-100"
                  title="Salin jawaban"
                >
                  {copiedIndex === -1 ? <CheckCheck size={16} className="text-green-500" /> : <Copy size={16} />}
                </button>
              )}
            </div>

            {analysis ? (
              <div className="whitespace-pre-wrap bg-gray-50 p-4 rounded-md border overflow-y-auto" style={{ maxHeight: '700px' }}>
                {analysis}
              </div>
            ) : (
              <div className="text-gray-500 italic flex flex-col items-center justify-center h-64">
                <Brain size={48} className="text-gray-300 mb-4" />
                <p className="text-center">
                  Jawaban AI akan muncul di sini setelah Anda mengajukan pertanyaan.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppWebAnalysis;