import React, { useState, useEffect, useMemo } from 'react';
import {
  Truck, Search, ChevronLeft, ChevronRight, RefreshCw,
  Filter, AlertTriangle, BarChart2, Users, Disc, Activity
} from 'lucide-react';
import { FleetData, fetchFleetData } from '../services/fleetDataService';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS, CategoryScale, LinearScale, BarElement,
  Title, Tooltip, Legend
} from 'chart.js';
import { MultiSelectDropdown } from '../components/ui/multi-select-dropdown';
import FleetAIAnalysis from '../components/FleetAIAnalysis';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

export default function FleetAnalyzer() {
  // State for fleet data
  const [fleetData, setFleetData] = useState<FleetData[]>([]);
  const [filteredData, setFilteredData] = useState<FleetData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Fixed number of items per page

  // State for search and filters
  const [searchQuery, setSearchQuery] = useState('');
  const [customerFilter, setCustomerFilter] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [locationFilter, setLocationFilter] = useState<string[]>([]);
  const [tireSizeFilter, setTireSizeFilter] = useState<string[]>([]);
  const [modelFilter, setModelFilter] = useState<string[]>([]);
  const [unitManufactureFilter, setUnitManufactureFilter] = useState<string[]>([]);

  // Load fleet data
  useEffect(() => {
    loadFleetData();
  }, []);

  // Filter data when search query or filters change
  useEffect(() => {
    if (fleetData.length > 0) {
      filterData();
    }
  }, [fleetData, searchQuery, customerFilter, statusFilter, locationFilter, tireSizeFilter, modelFilter, unitManufactureFilter]);

  // Load fleet data from CSV
  const loadFleetData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await fetchFleetData();
      setFleetData(data);
      setFilteredData(data);
    } catch (err) {
      console.error('Failed to load fleet data:', err);
      setError('Failed to load fleet data. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  // Get unique values for filter dropdowns
  const getUniqueValues = (field: string): string[] => {
    if (!fleetData || fleetData.length === 0) return [];

    const values = fleetData
      .map(item => item[field])
      .filter(value => value !== undefined && value !== null && value !== '')
      .map(value => String(value).trim());

    return [...new Set(values)].sort();
  };

  // Memoized unique values for filters
  const uniqueCustomers = useMemo(() => getUniqueValues('customer'), [fleetData]);
  const uniqueStatuses = useMemo(() => getUniqueValues('status'), [fleetData]);
  const uniqueLocations = useMemo(() => getUniqueValues('location'), [fleetData]);
  const uniqueTireSizes = useMemo(() => getUniqueValues('tire_size'), [fleetData]);
  const uniqueModels = useMemo(() => getUniqueValues('model'), [fleetData]);
  const uniqueUnitManufactures = useMemo(() => getUniqueValues('unit_manufacture'), [fleetData]);

  // Filter data based on search query and filters
  const filterData = () => {
    let filtered = [...fleetData];

    // Apply search query filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item => {
        return Object.values(item).some(
          value => value && value.toString().toLowerCase().includes(query)
        );
      });
    }

    // Apply dropdown filters with multiple selection support
    if (customerFilter.length > 0) {
      filtered = filtered.filter(item => customerFilter.includes(item.customer));
    }

    if (statusFilter.length > 0) {
      filtered = filtered.filter(item => statusFilter.includes(item.status));
    }

    if (locationFilter.length > 0) {
      filtered = filtered.filter(item => locationFilter.includes(item.location));
    }

    if (tireSizeFilter.length > 0) {
      filtered = filtered.filter(item => tireSizeFilter.includes(item.tire_size));
    }

    if (modelFilter.length > 0) {
      filtered = filtered.filter(item => modelFilter.includes(item.model));
    }

    if (unitManufactureFilter.length > 0) {
      filtered = filtered.filter(item => unitManufactureFilter.includes(item.unit_manufacture));
    }

    setFilteredData(filtered);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Reset all filters
  const resetFilters = () => {
    setCustomerFilter([]);
    setStatusFilter([]);
    setLocationFilter([]);
    setTireSizeFilter([]);
    setModelFilter([]);
    setUnitManufactureFilter([]);
    setSearchQuery('');
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Calculate scorecard metrics
  const scorecardMetrics = useMemo(() => {
    if (!filteredData.length) return { totalCustomers: 0, totalUnits: 0, totalTires: 0, activeUnits: 0 };

    const uniqueCustomers = new Set(filteredData.map(item => item.customer)).size;

    let totalUnits = 0;
    let totalTires = 0;
    let activeUnits = 0;

    filteredData.forEach(item => {
      const unitQty = parseInt(item.unit_qty || '0', 10) || 0;
      const tireQty = parseInt(item.totaltire || '0', 10) || 0;

      totalUnits += unitQty;
      totalTires += tireQty;

      if (item.status?.toLowerCase() === 'active') {
        activeUnits += unitQty;
      }
    });

    return { totalCustomers: uniqueCustomers, totalUnits, totalTires, activeUnits };
  }, [filteredData]);

  // Prepare chart data
  const chartData = useMemo(() => {
    if (!filteredData.length) return { labels: [], unitData: [], tireData: [] };

    // Group by customer
    const customerData: Record<string, { units: number, tires: number }> = {};

    filteredData.forEach(item => {
      const customer = item.customer || 'Unknown';
      const unitQty = parseInt(item.unit_qty || '0', 10) || 0;
      const tireQty = parseInt(item.totaltire || '0', 10) || 0;

      if (!customerData[customer]) {
        customerData[customer] = { units: 0, tires: 0 };
      }

      customerData[customer].units += unitQty;
      customerData[customer].tires += tireQty;
    });

    // Sort by total tires descending and take top 10
    const sortedCustomers = Object.entries(customerData)
      .sort((a, b) => b[1].tires - a[1].tires)
      .slice(0, 10);

    const labels = sortedCustomers.map(([customer]) => customer);
    const unitData = sortedCustomers.map(([, data]) => data.units);
    const tireData = sortedCustomers.map(([, data]) => data.tires);

    return { labels, unitData, tireData };
  }, [filteredData]);

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredData.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);
  const nextPage = () => setCurrentPage(prev => Math.min(prev + 1, totalPages));
  const prevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1));

  // Get table columns from the first item
  const getTableColumns = () => {
    if (currentItems.length === 0) return [];

    // Get all keys except 'id' which is our internal identifier
    return Object.keys(currentItems[0]).filter(key => key !== 'id');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 flex items-center">
          <Truck className="h-6 w-6 mr-2" />
          Fleet Data Analyzer
        </h1>
        <button
          onClick={loadFleetData}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Data
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchChange}
              placeholder="Search fleet data..."
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Filter Sidebar */}
      <div className="flex gap-6">
        <div className="w-72 bg-white rounded-lg shadow-sm border p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filters
            </h3>
            <button
              onClick={resetFilters}
              className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded flex items-center"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Reset
            </button>
          </div>

          <div className="space-y-4">
            {/* Customer Filter */}
            <MultiSelectDropdown
              label="Customer Name"
              options={uniqueCustomers.map(customer => ({ value: customer, label: customer }))}
              selectedValues={customerFilter}
              onChange={setCustomerFilter}
              placeholder="Select customers..."
            />

            {/* Status Filter */}
            <MultiSelectDropdown
              label="Status"
              options={uniqueStatuses.map(status => ({ value: status, label: status }))}
              selectedValues={statusFilter}
              onChange={setStatusFilter}
              placeholder="Select status..."
            />

            {/* Location Filter */}
            <MultiSelectDropdown
              label="Location"
              options={uniqueLocations.map(location => ({ value: location, label: location }))}
              selectedValues={locationFilter}
              onChange={setLocationFilter}
              placeholder="Select locations..."
            />

            {/* Tire Size Filter */}
            <MultiSelectDropdown
              label="Tire Size"
              options={uniqueTireSizes.map(size => ({ value: size, label: size }))}
              selectedValues={tireSizeFilter}
              onChange={setTireSizeFilter}
              placeholder="Select tire sizes..."
            />

            {/* Model Filter */}
            <MultiSelectDropdown
              label="Model"
              options={uniqueModels.map(model => ({ value: model, label: model }))}
              selectedValues={modelFilter}
              onChange={setModelFilter}
              placeholder="Select models..."
            />

            {/* Unit Manufacture Filter */}
            <MultiSelectDropdown
              label="Unit Manufacture"
              options={uniqueUnitManufactures.map(manufacture => ({ value: manufacture, label: manufacture }))}
              selectedValues={unitManufactureFilter}
              onChange={setUnitManufactureFilter}
              placeholder="Select manufacturers..."
            />
          </div>
        </div>

        <div className="flex-1">
          {/* Scorecards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <div className="flex items-center">
                <Users className="h-5 w-5 text-blue-600 mr-2" />
                <h3 className="text-sm font-medium text-gray-500">Total Customers</h3>
              </div>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{scorecardMetrics.totalCustomers}</p>
            </div>
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <div className="flex items-center">
                <Truck className="h-5 w-5 text-green-600 mr-2" />
                <h3 className="text-sm font-medium text-gray-500">Total Units</h3>
              </div>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{scorecardMetrics.totalUnits}</p>
            </div>
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <div className="flex items-center">
                <Disc className="h-5 w-5 text-purple-600 mr-2" />
                <h3 className="text-sm font-medium text-gray-500">Total Tires</h3>
              </div>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{scorecardMetrics.totalTires}</p>
            </div>
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <div className="flex items-center">
                <Activity className="h-5 w-5 text-orange-600 mr-2" />
                <h3 className="text-sm font-medium text-gray-500">Active Units</h3>
              </div>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{scorecardMetrics.activeUnits}</p>
            </div>
          </div>

          {/* Bar Chart */}
          <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <BarChart2 className="h-5 w-5 mr-2" />
              Customer Analysis
            </h3>
            <div className="h-80">
              <Bar
                data={{
                  labels: chartData.labels,
                  datasets: [
                    {
                      label: 'Total Units',
                      data: chartData.unitData,
                      backgroundColor: 'rgba(59, 130, 246, 0.5)',
                      borderColor: 'rgb(59, 130, 246)',
                      borderWidth: 1,
                    },
                    {
                      label: 'Total Tires',
                      data: chartData.tireData,
                      backgroundColor: 'rgba(139, 92, 246, 0.5)',
                      borderColor: 'rgb(139, 92, 246)',
                      borderWidth: 1,
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'top' as const,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                    },
                  },
                }}
              />
            </div>
          </div>

          {/* Fleet Data Table */}
          <div className="bg-white rounded-lg shadow-sm border overflow-hidden" style={{ maxWidth: '100%' }}>
            {isLoading ? (
              <div className="p-8 text-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
                <p className="text-gray-600">Loading fleet data...</p>
              </div>
            ) : error ? (
              <div className="p-8 text-center">
                <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
                <p className="text-red-600">{error}</p>
                <button
                  onClick={loadFleetData}
                  className="mt-4 inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Try Again
                </button>
              </div>
            ) : filteredData.length === 0 ? (
              <div className="p-8 text-center">
                <p className="text-gray-600">No fleet data found matching your criteria.</p>
              </div>
            ) : (
              <>
                {/* Table with sticky header and horizontal scroll */}
                <div className="relative" style={{ maxHeight: '500px', overflow: 'auto' }}>
                  {/* Horizontal scrollable container */}
                  <div className="overflow-x-auto" style={{ minWidth: '100%' }}>
                    <table className="min-w-full divide-y divide-gray-200 table-fixed">
                      <thead>
                        <tr>
                          {getTableColumns().map((column) => (
                            <th
                              key={column}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 bg-gray-50 sticky top-0 z-10"
                              style={{ minWidth: '150px' }}
                            >
                              {column}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {currentItems.map((item) => (
                          <tr key={item.id} className="hover:bg-gray-50">
                            {getTableColumns().map((column) => (
                              <td
                                key={`${item.id}-${column}`}
                                className="px-6 py-4 text-sm text-gray-500"
                                style={{
                                  minWidth: '150px',
                                  maxWidth: '300px',
                                  whiteSpace: 'normal',
                                  wordBreak: 'break-word'
                                }}
                              >
                                {item[column] || '-'}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Pagination */}
                <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                  <div className="flex-1 flex justify-between sm:hidden">
                    <button
                      onClick={prevPage}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                        currentPage === 1
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-white text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      Previous
                    </button>
                    <button
                      onClick={nextPage}
                      disabled={currentPage === totalPages}
                      className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                        currentPage === totalPages
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-white text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      Next
                    </button>
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                        <span className="font-medium">
                          {Math.min(indexOfLastItem, filteredData.length)}
                        </span>{' '}
                        of <span className="font-medium">{filteredData.length}</span> results
                      </p>
                    </div>
                    <div>
                      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <button
                          onClick={prevPage}
                          disabled={currentPage === 1}
                          className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                            currentPage === 1
                              ? 'text-gray-300 cursor-not-allowed'
                              : 'text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          <span className="sr-only">Previous</span>
                          <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                        </button>

                        {/* Page numbers */}
                        {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                          // Show pages around current page
                          let pageNum;
                          if (totalPages <= 5) {
                            // If 5 or fewer pages, show all
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            // If near start, show first 5 pages
                            pageNum = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            // If near end, show last 5 pages
                            pageNum = totalPages - 4 + i;
                          } else {
                            // Otherwise show current page and 2 pages on each side
                            pageNum = currentPage - 2 + i;
                          }

                          return (
                            <button
                              key={pageNum}
                              onClick={() => paginate(pageNum)}
                              className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                currentPage === pageNum
                                  ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                  : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                              }`}
                            >
                              {pageNum}
                            </button>
                          );
                        })}

                        <button
                          onClick={nextPage}
                          disabled={currentPage === totalPages}
                          className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                            currentPage === totalPages
                              ? 'text-gray-300 cursor-not-allowed'
                              : 'text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          <span className="sr-only">Next</span>
                          <ChevronRight className="h-5 w-5" aria-hidden="true" />
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* AI Analysis Component */}
      <FleetAIAnalysis
        filteredData={filteredData}
        tireSizeFilter={tireSizeFilter}
      />
    </div>
  );
}
