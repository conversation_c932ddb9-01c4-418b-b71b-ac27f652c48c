import axios from 'axios'

// Fleet Data Interface
export interface FleetData {
  id: string
  customer?: string
  model?: string
  location?: string
  tire_size?: string
  tire_quantity?: string | number
  unit_qty?: string | number
  total_tire?: string | number
  status?: string
  [key: string]: any
}

// API Response Interface
export interface FleetApiResponse {
  success: boolean
  data: FleetData[]
  message?: string
}

// Fleet Data Service Class
export class FleetDataService {
  private static instance: FleetDataService
  private baseUrl = 'https://chitraparatama.co.id/ICS/product/get_api.php'
  private cache: { data: FleetData[], timestamp: number } | null = null
  private cacheTimeout = 5 * 60 * 1000 // 5 minutes

  static getInstance(): FleetDataService {
    if (!FleetDataService.instance) {
      FleetDataService.instance = new FleetDataService()
    }
    return FleetDataService.instance
  }

  async fetchFleetData(): Promise<FleetData[]> {
    try {
      // Check cache first
      if (this.cache && Date.now() - this.cache.timestamp < this.cacheTimeout) {
        console.log('Returning cached fleet data')
        return this.cache.data
      }

      console.log('Fetching fresh fleet data from API')
      const response = await axios.get<FleetApiResponse>(this.baseUrl, {
        params: {
          function: 'fleetlist'
        },
        timeout: 30000,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })

      if (!response.data || !response.data.success) {
        throw new Error(response.data?.message || 'Failed to fetch fleet data')
      }

      const fleetData = this.normalizeFleetData(response.data.data || [])
      
      // Update cache
      this.cache = {
        data: fleetData,
        timestamp: Date.now()
      }

      console.log(`Successfully fetched ${fleetData.length} fleet records`)
      return fleetData

    } catch (error) {
      console.error('Error fetching fleet data:', error)
      
      // Return cached data if available, otherwise return empty array
      if (this.cache) {
        console.log('API failed, returning cached data')
        return this.cache.data
      }
      
      // Return empty array if no cache available
      console.log('No cache available, returning empty array')
      return []
    }
  }

  private normalizeFleetData(rawData: any[]): FleetData[] {
    return rawData.map((item, index) => {
      // Ensure we have a valid ID
      const id = item.id || item.ID || `fleet_${index + 1}`
      
      return {
        id: String(id),
        customer: this.normalizeString(item.customer || item.Customer || item.CUSTOMER),
        model: this.normalizeString(item.model || item.Model || item.MODEL),
        location: this.normalizeString(item.location || item.Location || item.LOCATION),
        tire_size: this.normalizeString(item.tire_size || item.tireSize || item.TIRE_SIZE),
        tire_quantity: this.normalizeNumber(item.tire_quantity || item.tireQuantity || item.TIRE_QUANTITY),
        unit_qty: this.normalizeNumber(item.unit_qty || item.unitQty || item.UNIT_QTY),
        total_tire: this.normalizeNumber(item.total_tire || item.totalTire || item.TOTAL_TIRE),
        status: this.normalizeStatus(item.status || item.Status || item.STATUS),
        // Include any additional fields
        ...item
      }
    })
  }

  private normalizeString(value: any): string {
    if (value === null || value === undefined) return ''
    return String(value).trim()
  }

  private normalizeNumber(value: any): number {
    if (value === null || value === undefined || value === '') return 0
    const num = Number(value)
    return isNaN(num) ? 0 : num
  }

  private normalizeStatus(value: any): string {
    if (!value) return 'Unknown'
    const status = String(value).toLowerCase().trim()
    
    // Map common status variations
    const statusMap: Record<string, string> = {
      'active': 'Active',
      'aktif': 'Active',
      'operational': 'Active',
      'inactive': 'Inactive',
      'non-aktif': 'Inactive',
      'maintenance': 'Maintenance',
      'perawatan': 'Maintenance',
      'idle': 'Idle',
      'menganggur': 'Idle',
      'breakdown': 'Breakdown',
      'rusak': 'Breakdown'
    }

    return statusMap[status] || 'Unknown'
  }



  // Clear cache method
  clearCache(): void {
    this.cache = null
  }

  // Get cache status
  getCacheStatus(): { cached: boolean, age?: number } {
    if (!this.cache) {
      return { cached: false }
    }
    
    return {
      cached: true,
      age: Date.now() - this.cache.timestamp
    }
  }
}

// Export singleton instance
export const fleetDataService = FleetDataService.getInstance()

// Export default
export default fleetDataService