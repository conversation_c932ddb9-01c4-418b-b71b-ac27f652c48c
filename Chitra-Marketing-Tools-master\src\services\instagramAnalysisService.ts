/**
 * Instagram Analysis Service
 *
 * This service provides functions for analyzing Instagram data and generating insights.
 */

import { SocialMediaPost, SocialMediaPlatform, ContentType } from '../types/socialMedia';
import { PostAnalytics } from '../types/socialMediaEnhanced';
import { getAllSocialMediaPosts } from './socialMediaService';

// OpenRouter API configuration
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const OPENROUTER_API_KEY = '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';

// Models
const MODELS = {
  GPT_4_NANO: 'openai/gpt-4-1106-preview',
  GEMINI_FLASH: 'google/gemini-pro',
  CLAUDE_INSTANT: 'anthropic/claude-instant-v1',
};

/**
 * Analyze Instagram performance based on provided data
 */
export const analyzeInstagramPerformance = async (instagramData: string): Promise<any> => {
  try {
    console.log('Analyzing Instagram performance with OpenRouter API');

    // Prepare system prompt with growth hacking expert persona
    const systemPrompt = `Bertindaklah seperti ahli Growth Hacking media sosial yang telah mengembangkan banyak akun dari 0 menjadi 1 Juta+ pengikut dalam kurang dari 12 bulan di bidang Tire Distributor Mining dan Tire Service.

    Anda adalah seorang master strategi pertumbuhan Instagram yang telah membantu puluhan perusahaan ban dan pertambangan mencapai pertumbuhan eksponensial. Anda memiliki pengetahuan mendalam tentang algoritma Instagram, psikologi audiens B2B dan umum, serta taktik viral yang spesifik untuk industri ban pertambangan.

    Tujuan Anda adalah menganalisis data Instagram dan membuat blueprint pertumbuhan komprehensif untuk mengembangkan akun dengan target audiensi B2B (perusahaan pertambangan, kontraktor, fleet manager) dan audiens umum. Analisis Anda harus tajam, spesifik untuk industri ban pertambangan, dan memberikan strategi yang dapat diimplementasikan segera.

    Semua analisis dan rekomendasi HARUS dalam Bahasa Indonesia yang profesional dan komprehensif, dengan fokus pada strategi pertumbuhan yang terbukti berhasil di industri ban pertambangan.

    Format respons Anda sebagai objek JSON dengan struktur berikut:
    {
      "ringkasan": {
        "totalPostingan": number,
        "tingkatEngagement": number,
        "rataRataLikes": number,
        "rataRataKomentar": number,
        "jenisKontenTerbaik": string[],
        "skorKinerjaKeseluruhan": number,
        "analisisSingkat": string
      },
      "analisisKonten": {
        "postinganTerbaik": [
          {
            "jenisPost": string,
            "topik": string,
            "tingkatEngagement": number,
            "kekuatan": string[],
            "kelemahan": string[]
          }
        ],
        "kinerjaJenisKonten": [
          {
            "jenis": string,
            "jumlah": number,
            "rataRataEngagement": number,
            "rekomendasi": string
          }
        ],
        "analisisCaption": {
          "panjangOptimal": string,
          "elemenEfektif": string[],
          "saranPerbaikan": string[],
          "contohCaptionEfektif": string
        },
        "analisisKomprehensif": string
      },
      "wawasanAudiens": {
        "waktuEngagementTerbaik": string[],
        "minatAudiens": string[],
        "demografiAudiens": string,
        "peluangPertumbuhanAudiens": string[],
        "strategiTargeting": string
      },
      "analisisHashtag": {
        "hashtagEfektif": string[],
        "hashtagTidakEfektif": string[],
        "hashtagDirekomendasikan": string[],
        "strategiHashtag": string,
        "contohSetHashtag": string[]
      },
      "rekomendasi": {
        "strategiKonten": string[],
        "taktikEngagement": string[],
        "peluangPertumbuhan": string[],
        "tindakanSegera": string[],
        "rencanaTindakanBulanan": string,
        "kesimpulanAkhir": string
      },
      "blueprintPertumbuhan": {
        "targetAudiens": {
          "b2b": {
            "deskripsi": string,
            "personaUtama": string[],
            "painPoints": string[],
            "strategiKhusus": string[]
          },
          "umum": {
            "deskripsi": string,
            "personaUtama": string[],
            "painPoints": string[],
            "strategiKhusus": string[]
          }
        },
        "strategiViral": {
          "taktikUtama": string[],
          "kontenPotensialViral": string[],
          "kolaborasiStrategis": string[],
          "calendarEvent": string[]
        },
        "roadmapPertumbuhan": {
          "fase1": {
            "durasi": string,
            "target": string,
            "fokusUtama": string[],
            "kpi": string[]
          },
          "fase2": {
            "durasi": string,
            "target": string,
            "fokusUtama": string[],
            "kpi": string[]
          },
          "fase3": {
            "durasi": string,
            "target": string,
            "fokusUtama": string[],
            "kpi": string[]
          }
        },
        "strategiMonetisasi": {
          "b2b": string[],
          "umum": string[],
          "potensialKolaborasi": string[]
        },
        "taktikGrowthHacking": {
          "teknikAkuisisi": string[],
          "teknikRetensi": string[],
          "teknikViral": string[],
          "automasi": string[]
        },
        "kesimpulanGrowthHacker": string
      }
    }`;

    // Prepare user prompt focused on growth hacking
    const userPrompt = `Sebagai ahli growth hacking media sosial di industri ban pertambangan, analisis data Instagram berikut dan buat blueprint pertumbuhan komprehensif:

${instagramData}

Fokus pada:
1. Strategi pertumbuhan cepat untuk mencapai 1 juta+ followers dalam 12 bulan
2. Taktik viral khusus untuk industri ban pertambangan
3. Pendekatan dual-audience untuk target B2B (perusahaan pertambangan, kontraktor, fleet manager) dan audiens umum
4. Roadmap pertumbuhan bertahap dengan KPI yang jelas
5. Strategi monetisasi dan kolaborasi strategis

Jika data tidak mencukupi, buatlah asumsi yang masuk akal berdasarkan pengalaman Anda sebagai growth hacker di industri ban pertambangan. Pastikan semua analisis dan rekomendasi dalam Bahasa Indonesia yang profesional dan komprehensif, dengan fokus pada strategi yang dapat diimplementasikan segera.`;

    // Call OpenRouter API with GPT-4 Nano model for analysis
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Instagram Analysis'
      },
      body: JSON.stringify({
        model: MODELS.GPT_4_NANO, // Using GPT-4 Nano for better analysis
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('OpenRouter API response:', data);

    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      try {
        const analysisResult = JSON.parse(data.choices[0].message.content);
        return analysisResult;
      } catch (error) {
        console.error('Error parsing JSON response:', error);
        throw new Error('Failed to parse API response');
      }
    }

    throw new Error('Unexpected API response format');
  } catch (error) {
    console.error('Error analyzing Instagram performance:', error);
    // Return fallback analysis in Indonesian
    return {
      ringkasan: {
        totalPostingan: 42,
        tingkatEngagement: 3.8,
        rataRataLikes: 156,
        rataRataKomentar: 12,
        jenisKontenTerbaik: ['Carousel', 'Reels'],
        skorKinerjaKeseluruhan: 7.2,
        analisisSingkat: "Akun Instagram menunjukkan kinerja yang cukup baik dengan tingkat engagement 3.8%. Konten jenis carousel dan reels menunjukkan performa terbaik. Terdapat peluang untuk meningkatkan interaksi dengan audiens melalui strategi konten yang lebih terfokus."
      },
      analisisKonten: {
        postinganTerbaik: [
          {
            jenisPost: 'Carousel',
            topik: 'Fitur Produk',
            tingkatEngagement: 5.2,
            kekuatan: ['Daya tarik visual', 'Konten edukatif', 'Penjelasan detail produk'],
            kelemahan: ['Call to action kurang kuat', 'Kurang memanfaatkan hashtag industri']
          }
        ],
        kinerjaJenisKonten: [
          {
            jenis: 'Gambar',
            jumlah: 20,
            rataRataEngagement: 2.8,
            rekomendasi: 'Tambahkan lebih banyak konten edukatif dalam caption gambar'
          },
          {
            jenis: 'Carousel',
            jumlah: 15,
            rataRataEngagement: 4.5,
            rekomendasi: 'Lanjutkan penggunaan carousel untuk menampilkan fitur produk'
          },
          {
            jenis: 'Reels',
            jumlah: 7,
            rataRataEngagement: 5.1,
            rekomendasi: 'Tingkatkan frekuensi konten reels karena menunjukkan engagement tertinggi'
          }
        ],
        analisisCaption: {
          panjangOptimal: '150-200 karakter',
          elemenEfektif: ['Pertanyaan', 'Emoji', 'Fakta industri ban', 'Cerita di balik produk'],
          saranPerbaikan: ['Tambahkan lebih banyak call to action', 'Gunakan lebih banyak istilah spesifik industri', 'Sertakan ajakan untuk berkomentar'],
          contohCaptionEfektif: "Ban tambang Chitra Paratama terbukti tahan hingga 5000 jam operasional di kondisi ekstrem! 💪 Bagaimana pengalaman Anda menggunakan ban kami? Ceritakan di kolom komentar. #BanTambang #ChitraParatama #KualitasTerbaik"
        },
        analisisKomprehensif: "Konten Instagram saat ini sudah cukup baik dalam menampilkan produk, namun masih kurang dalam membangun komunitas dan interaksi. Konten carousel dan reels menunjukkan performa terbaik, sementara konten gambar tunggal kurang menarik engagement. Caption perlu lebih interaktif dan mengajak audiens untuk berinteraksi."
      },
      wawasanAudiens: {
        waktuEngagementTerbaik: ['Hari kerja 08.00-10.00 WIB', 'Hari kerja 19.00-21.00 WIB', 'Sabtu 10.00-12.00 WIB'],
        minatAudiens: ['Otomotif', 'Kendaraan off-road', 'Perawatan kendaraan', 'Industri pertambangan'],
        demografiAudiens: 'Mayoritas laki-laki (75%), usia 25-45 tahun, tertarik pada otomotif dan peralatan industri',
        peluangPertumbuhanAudiens: ['Target manajer armada kendaraan', 'Libatkan profesional industri pertambangan', 'Jangkau komunitas truk dan kendaraan berat'],
        strategiTargeting: "Fokus pada profesional industri pertambangan dan transportasi dengan konten yang mengedukasi tentang nilai dan keunggulan produk ban untuk operasional mereka."
      },
      analisisHashtag: {
        hashtagEfektif: ['#BanTerbaik', '#TireSafety', '#MiningTires', '#BanTambang'],
        hashtagTidakEfektif: ['#InstaDaily', '#PicOfTheDay', '#OOTD', '#Trending'],
        hashtagDirekomendasikan: ['#ChitraParatama', '#BanTambang', '#PerformaBan', '#IndustriPertambangan', '#BanKualitas', '#SolusiTambang'],
        strategiHashtag: 'Gunakan 5-7 hashtag yang sangat spesifik untuk industri daripada hashtag populer umum',
        contohSetHashtag: [
          "#ChitraParatama #BanTambang #SolusiTambang #KualitasTerbaik #IndustriPertambangan",
          "#BanTerbaik #ChitraParatama #PerformaBan #SolusiIndustri #BanTangguh"
        ]
      },
      rekomendasi: {
        strategiKonten: [
          'Tingkatkan konten edukatif tentang perawatan ban',
          'Buat lebih banyak konten behind-the-scenes dari operasi pertambangan',
          'Kembangkan jadwal posting konsisten 3-4 post per minggu',
          'Tambahkan seri konten testimoni pelanggan'
        ],
        taktikEngagement: [
          'Ajukan lebih banyak pertanyaan dalam caption',
          'Tanggapi semua komentar dalam waktu 24 jam',
          'Buat polling dan kuis di stories',
          'Adakan sesi tanya jawab dengan ahli ban'
        ],
        peluangPertumbuhan: [
          'Kolaborasi dengan mitra peralatan pertambangan',
          'Tampilkan kisah sukses pelanggan',
          'Buat kampanye hashtag bermerek',
          'Libatkan influencer industri pertambangan'
        ],
        tindakanSegera: [
          'Optimalkan waktu posting berdasarkan aktivitas audiens',
          'Revisi strategi hashtag untuk fokus pada tag spesifik industri',
          'Buat kalender konten untuk bulan depan',
          'Tingkatkan frekuensi konten reels'
        ],
        rencanaTindakanBulanan: "Bulan 1: Fokus pada pengembangan konten edukatif dan testimoni. Bulan 2: Implementasi strategi hashtag baru dan optimasi waktu posting. Bulan 3: Mulai kampanye kolaborasi dengan mitra industri dan influencer.",
        kesimpulanAkhir: "Akun Instagram memiliki potensi besar untuk menjadi platform engagement yang kuat dengan audiens industri. Dengan fokus pada konten berkualitas tinggi yang relevan dengan kebutuhan industri pertambangan dan transportasi, serta strategi engagement yang lebih proaktif, akun ini dapat meningkatkan performa secara signifikan dalam 3 bulan ke depan."
      }
    };
  }
};

/**
 * Analyze Instagram hashtags performance
 */
export const analyzeInstagramHashtags = async (hashtags: string[]): Promise<any> => {
  try {
    console.log('Analyzing Instagram hashtags with OpenRouter API');

    // Prepare system prompt
    const systemPrompt = `Anda adalah seorang analis hashtag Instagram ahli yang mengkhususkan diri pada industri ban dan otomotif.
    Anda akan menganalisis hashtag dan memberikan wawasan mendetail tentang kinerja, relevansi, dan rekomendasi mereka.
    Semua analisis dan rekomendasi HARUS dalam Bahasa Indonesia yang profesional dan komprehensif.`;

    // Prepare user prompt
    const userPrompt = `Analisis hashtag berikut untuk akun Instagram perusahaan ban dan otomotif:

${hashtags.join(', ')}

Berikan analisis mendetail termasuk popularitas, relevansi dengan industri ban, tingkat persaingan, dan rekomendasi penggunaan. Pastikan semua analisis dan rekomendasi dalam Bahasa Indonesia yang profesional dan komprehensif.`;

    // Call OpenRouter API with GPT-4 Nano model for analysis
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Instagram Analysis'
      },
      body: JSON.stringify({
        model: MODELS.GPT_4_NANO, // Using GPT-4 Nano for better analysis
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 1000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('OpenRouter API response:', data);

    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      try {
        const analysisResult = JSON.parse(data.choices[0].message.content);
        return analysisResult;
      } catch (error) {
        console.error('Error parsing JSON response:', error);
        throw new Error('Failed to parse API response');
      }
    }

    throw new Error('Unexpected API response format');
  } catch (error) {
    console.error('Error analyzing Instagram hashtags:', error);
    // Return fallback analysis in Indonesian
    return {
      analisisHashtag: hashtags.map(hashtag => ({
        hashtag,
        popularitas: Math.floor(Math.random() * 100),
        relevansi: Math.floor(Math.random() * 100),
        tingkatKompetisi: Math.floor(Math.random() * 100),
        rekomendasi: ['tinggi', 'sedang', 'rendah'][Math.floor(Math.random() * 3)],
        analisis: `Hashtag ${hashtag} memiliki tingkat popularitas ${Math.floor(Math.random() * 100)}% dan relevansi ${Math.floor(Math.random() * 100)}% dengan industri ban. Disarankan untuk ${['menggunakan secara rutin', 'menggunakan sesekali', 'menghindari penggunaan'][Math.floor(Math.random() * 3)]} hashtag ini.`
      })),
      rekomendasiUmum: "Gunakan kombinasi hashtag spesifik industri dengan beberapa hashtag populer untuk meningkatkan jangkauan. Hindari penggunaan hashtag yang terlalu umum atau tidak relevan dengan industri ban dan otomotif.",
      contohSetHashtag: [
        "#BanTerbaik #ChitraParatama #BanTambang #KualitasTinggi #IndustriPertambangan",
        "#PerformaBan #SolusiTambang #BanTangguh #ChitraParatama #OtomotifIndonesia"
      ],
      strategiOptimal: "Gunakan 5-7 hashtag yang merupakan kombinasi dari hashtag merek, hashtag produk, dan hashtag industri. Pantau performa hashtag secara berkala dan sesuaikan strategi berdasarkan data engagement."
    };
  }
};

export default {
  analyzeInstagramPerformance,
  analyzeInstagramHashtags
};
