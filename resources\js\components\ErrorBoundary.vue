<template>
    <div v-if="hasError" class="min-h-screen flex items-center justify-center bg-gray-50">
        <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4">
                <AlertTriangle class="h-6 w-6 text-red-600" />
            </div>
            
            <div class="text-center">
                <h1 class="text-xl font-semibold text-gray-900 mb-2">
                    Oops! Something went wrong
                </h1>
                <p class="text-gray-600 mb-6">
                    We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.
                </p>
                
                <div class="space-y-3">
                    <button
                        @click="retry"
                        class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                        Try Again
                    </button>
                    
                    <button
                        @click="goHome"
                        class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                        Go to Homepage
                    </button>
                </div>
                
                <!-- Error Details (Development Only) -->
                <div v-if="isDevelopment && errorDetails" class="mt-6 p-4 bg-gray-100 rounded-lg text-left">
                    <h3 class="text-sm font-medium text-gray-900 mb-2">Error Details:</h3>
                    <pre class="text-xs text-gray-700 overflow-auto max-h-32">{{ errorDetails }}</pre>
                </div>
            </div>
        </div>
    </div>
    
    <slot v-else />
</template>

<script setup lang="ts">
import { AlertTriangle } from 'lucide-vue-next';
import { ref, onErrorCaptured } from 'vue';
import { router } from '@inertiajs/vue3';

// Props
interface Props {
    fallback?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    fallback: true
});

// State
const hasError = ref(false);
const errorDetails = ref<string>('');
const isDevelopment = ref(import.meta.env.DEV);

// Error handling
onErrorCaptured((error: Error) => {
    console.error('Error caught by ErrorBoundary:', error);
    
    hasError.value = true;
    errorDetails.value = error.stack || error.message;
    
    // Report error to monitoring service in production
    if (!isDevelopment.value) {
        reportError(error);
    }
    
    return false; // Prevent error from propagating
});

// Error reporting (placeholder for production error tracking)
const reportError = (error: Error) => {
    // In production, you would send this to your error tracking service
    // e.g., Sentry, Bugsnag, etc.
    console.error('Reporting error to monitoring service:', error);
};

// Actions
const retry = () => {
    hasError.value = false;
    errorDetails.value = '';
    // Reload the current page
    window.location.reload();
};

const goHome = () => {
    hasError.value = false;
    errorDetails.value = '';
    router.visit('/');
};

// Global error handler for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    
    if (!hasError.value) {
        hasError.value = true;
        errorDetails.value = event.reason?.stack || event.reason?.message || 'Unknown error';
        
        if (!isDevelopment.value) {
            reportError(new Error(event.reason));
        }
    }
    
    event.preventDefault();
});
</script>
