/**
 * Types for the Proposal Marketing Builder AI feature
 */

import { Product } from './index';

/**
 * Types of proposals that can be generated
 */
export type ProposalBuilderType = 
  | 'bundling' 
  | 'michelin-first' 
  | 'product-warranty' 
  | 'custom';

/**
 * Interface for product bundling in proposal
 */
export interface ProposalProduct {
  product: Product;
  quantity: number;
  discount?: number; // Optional discount percentage
}

/**
 * Interface for optional offer details
 */
export interface OfferDetails {
  offerTitle: string;
  validUntil: string;
  specialTerms: string;
  paymentTerms: string;
  deliveryTerms: string;
}

/**
 * Interface for company information
 */
export interface CompanyInfo {
  companyName: string;
  contactPerson: string;
  contactTitle: string;
  contactEmail: string;
  contactPhone: string;
  companyAddress: string;
  industry?: string;
}

/**
 * Interface for proposal form data
 */
export interface ProposalBuilderFormData {
  // Basic information
  proposalType: ProposalBuilderType;
  proposalTitle: string;
  
  // Company information
  companyInfo: CompanyInfo;
  
  // Products
  mainProducts: ProposalProduct[];
  bundledProducts?: ProposalProduct[];
  
  // Offer details
  offerDetails: OfferDetails;
  
  // Additional information
  customerPain?: string; // Customer pain points
  competitorInfo?: string; // Information about competitors
  previousDeals?: string; // Information about previous deals
  additionalNotes?: string; // Additional notes
  
  // Custom proposal fields (only used when proposalType is 'custom')
  customProposalType?: string;
  customProposalRequirements?: string;
}

/**
 * Interface for AI-generated proposal
 */
export interface ProposalBuilderResult {
  id: string;
  proposalType: ProposalBuilderType;
  proposalTitle: string;
  generatedDate: string;
  documentUrl: string; // URL to the generated DOCX file
  previewHtml?: string; // HTML preview of the generated document
}

/**
 * Interface for AI request
 */
export interface ProposalBuilderAIRequest {
  formData: ProposalBuilderFormData;
}

/**
 * Interface for AI response
 */
export interface ProposalBuilderAIResponse {
  content: string; // The content for the proposal
  sections: {
    introduction: string;
    companyOverview: string;
    productDetails: string;
    valueProposition: string;
    offerDetails: string;
    pricing: string;
    terms: string;
    conclusion: string;
  };
}
