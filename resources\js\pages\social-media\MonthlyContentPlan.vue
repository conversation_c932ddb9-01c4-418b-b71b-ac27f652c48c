<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Monthly Content Plan</h1>
                    <p class="mt-2 text-gray-600">Plan and organize your social media content for the month</p>
                </div>
                <div class="flex space-x-3">
                    <select v-model="selectedMonth" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                        <option v-for="(month, index) in months" :key="index" :value="index">
                            {{ month }} {{ currentYear }}
                        </option>
                    </select>
                    <button
                        @click="generatePlan"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center"
                    >
                        <Sparkles class="h-4 w-4 mr-2" />
                        Auto Generate
                    </button>
                    <button
                        @click="exportPlan"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Plan
                    </button>
                </div>
            </div>

            <!-- Content Generation Form -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">AI Content Generation Settings</h3>

                <form @submit.prevent="generateAIPlan" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Platform</label>
                            <select v-model="aiConfig.platform" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500" required>
                                <option value="Instagram">Instagram</option>
                                <option value="Facebook">Facebook</option>
                                <option value="LinkedIn">LinkedIn</option>
                                <option value="Twitter">Twitter</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Posts per Week</label>
                            <select v-model="aiConfig.postsPerWeek" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500">
                                <option :value="2">2 posts per week</option>
                                <option :value="3">3 posts per week</option>
                                <option :value="4">4 posts per week</option>
                                <option :value="5">5 posts per week</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Content Focus</label>
                            <select v-model="aiConfig.contentFocus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500">
                                <option value="balanced">Balanced Mix</option>
                                <option value="product">Product Focused</option>
                                <option value="education">Educational</option>
                                <option value="engagement">Engagement Focused</option>
                            </select>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Target Audience</label>
                            <textarea
                                v-model="aiConfig.targetAudience"
                                rows="3"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500"
                                placeholder="Mining companies, logistics operators, heavy equipment owners..."
                            ></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Brand Guidelines & Notes</label>
                            <textarea
                                v-model="aiConfig.brandGuidelines"
                                rows="3"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500"
                                placeholder="Brand guidelines, upcoming events, special considerations..."
                            ></textarea>
                        </div>
                    </div>

                    <div class="flex justify-center">
                        <button
                            type="submit"
                            :disabled="isGeneratingAI"
                            class="px-8 py-3 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-lg"
                        >
                            <Loader2 v-if="isGeneratingAI" class="h-5 w-5 mr-2 animate-spin" />
                            <Brain v-else class="h-5 w-5 mr-2" />
                            {{ isGeneratingAI ? 'Generating with AI...' : 'Generate AI Content Plan' }}
                        </button>
                    </div>

                    <!-- AI Generation Progress -->
                    <div v-if="isGeneratingAI" class="w-full">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span>{{ aiGenerationStatus }}</span>
                            <span>{{ aiGenerationProgress }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-600 h-2 rounded-full transition-all duration-500" :style="{ width: aiGenerationProgress + '%' }"></div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Plan Overview -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Calendar class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Posts</p>
                            <p class="text-2xl font-bold text-gray-900">{{ planStats.totalPosts }}</p>
                            <p class="text-sm text-blue-600">{{ planStats.postsPerWeek }} per week</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Image class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Visual Content</p>
                            <p class="text-2xl font-bold text-gray-900">{{ planStats.visualContent }}%</p>
                            <p class="text-sm text-green-600">Images & Videos</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Target class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Platforms</p>
                            <p class="text-2xl font-bold text-gray-900">{{ planStats.platforms }}</p>
                            <p class="text-sm text-purple-600">Active channels</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <TrendingUp class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Completion</p>
                            <p class="text-2xl font-bold text-gray-900">{{ planStats.completion }}%</p>
                            <p class="text-sm text-orange-600">Plan progress</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Calendar -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Content Calendar - {{ months[selectedMonth] }} {{ currentYear }}</h3>
                    <div class="flex space-x-3">
                        <button
                            @click="addContent"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                        >
                            <Plus class="h-4 w-4 mr-2" />
                            Add Content
                        </button>
                        <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center">
                            <Filter class="h-4 w-4 mr-2" />
                            Filter
                        </button>
                    </div>
                </div>

                <!-- Calendar Grid -->
                <div class="p-6">
                    <!-- Weekday Headers -->
                    <div class="grid grid-cols-7 gap-1 mb-4">
                        <div v-for="day in weekdays" :key="day" class="p-2 text-center text-sm font-medium text-gray-500">
                            {{ day }}
                        </div>
                    </div>

                    <!-- Calendar Days -->
                    <div class="grid grid-cols-7 gap-1">
                        <div 
                            v-for="day in calendarDays" 
                            :key="day.date"
                            class="min-h-[120px] p-2 border border-gray-200 rounded-lg"
                            :class="day.isCurrentMonth ? 'bg-white' : 'bg-gray-50'"
                        >
                            <div class="flex items-center justify-between mb-2">
                                <span 
                                    :class="[
                                        'text-sm font-medium',
                                        day.isToday ? 'text-blue-600' : day.isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
                                    ]"
                                >
                                    {{ day.dayNumber }}
                                </span>
                                <button 
                                    v-if="day.isCurrentMonth"
                                    @click="addContentToDay(day)"
                                    class="text-gray-400 hover:text-blue-600"
                                >
                                    <Plus class="h-3 w-3" />
                                </button>
                            </div>

                            <!-- Content Items -->
                            <div class="space-y-1">
                                <div 
                                    v-for="content in day.content" 
                                    :key="content.id"
                                    @click="editContent(content)"
                                    class="p-1 text-xs rounded cursor-pointer hover:shadow-sm"
                                    :class="getContentTypeColor(content.type)"
                                >
                                    <div class="flex items-center justify-between">
                                        <span class="truncate">{{ content.title }}</span>
                                        <component :is="getPlatformIcon(content.platform)" class="h-3 w-3 flex-shrink-0" />
                                    </div>
                                    <div class="text-xs opacity-75">{{ content.time }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Types Distribution -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Content Types -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Content Types Distribution</h3>
                    <div class="space-y-4">
                        <div v-for="type in contentTypes" :key="type.name" class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div :class="['w-4 h-4 rounded-full mr-3', type.color]"></div>
                                <span class="text-sm font-medium text-gray-900">{{ type.name }}</span>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">{{ type.count }}</p>
                                <p class="text-xs text-gray-500">{{ type.percentage }}%</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Platform Distribution -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Platform Distribution</h3>
                    <div class="space-y-4">
                        <div v-for="platform in platformDistribution" :key="platform.name" class="flex items-center justify-between">
                            <div class="flex items-center">
                                <component :is="getPlatformIcon(platform.name.toLowerCase())" class="h-4 w-4 mr-3 text-gray-600" />
                                <span class="text-sm font-medium text-gray-900">{{ platform.name }}</span>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">{{ platform.posts }}</p>
                                <p class="text-xs text-gray-500">{{ platform.percentage }}%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Templates -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Content Templates</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div 
                        v-for="template in contentTemplates" 
                        :key="template.id"
                        @click="useTemplate(template)"
                        class="p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-shadow"
                    >
                        <div class="flex items-center mb-3">
                            <component :is="template.icon" class="h-6 w-6 text-blue-600 mr-3" />
                            <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">{{ template.description }}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">{{ template.category }}</span>
                            <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">Use Template</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Form Modal -->
            <div v-if="showContentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-900">
                                {{ editingContent ? 'Edit Content' : 'Add New Content' }}
                            </h3>
                            <button @click="closeContentModal" class="text-gray-400 hover:text-gray-600">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <form @submit.prevent="saveContent" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Content Title</label>
                                    <input
                                        v-model="contentForm.title"
                                        type="text"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                                        placeholder="Enter content title..."
                                    />
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Content Type</label>
                                    <select v-model="contentForm.type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500" required>
                                        <option value="image">Image Post</option>
                                        <option value="video">Video</option>
                                        <option value="carousel">Carousel</option>
                                        <option value="story">Story</option>
                                        <option value="reel">Reel</option>
                                    </select>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Platform</label>
                                    <select v-model="contentForm.platform" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500" required>
                                        <option value="Instagram">Instagram</option>
                                        <option value="Facebook">Facebook</option>
                                        <option value="LinkedIn">LinkedIn</option>
                                        <option value="Twitter">Twitter</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Posting Time</label>
                                    <input
                                        v-model="contentForm.time"
                                        type="time"
                                        required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                    <select v-model="contentForm.category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500" required>
                                        <option value="product">Product Focus</option>
                                        <option value="education">Educational</option>
                                        <option value="promotion">Promotional</option>
                                        <option value="engagement">Engagement</option>
                                        <option value="company">Company Culture</option>
                                        <option value="industry">Industry Insights</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Content Description</label>
                                <textarea
                                    v-model="contentForm.description"
                                    rows="3"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                                    placeholder="Describe the content idea, target message, or key points..."
                                ></textarea>
                            </div>

                            <!-- AI Generation Section -->
                            <div class="border-t pt-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="text-md font-medium text-gray-900">AI Content Generation</h4>
                                    <button
                                        type="button"
                                        @click="generateAIContent"
                                        :disabled="isGeneratingContent"
                                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 flex items-center text-sm"
                                    >
                                        <Loader2 v-if="isGeneratingContent" class="h-4 w-4 mr-2 animate-spin" />
                                        <Brain v-else class="h-4 w-4 mr-2" />
                                        {{ isGeneratingContent ? 'Generating...' : 'Generate with AI' }}
                                    </button>
                                </div>

                                <!-- AI Generation Progress -->
                                <div v-if="isGeneratingContent" class="mb-4">
                                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                                        <span>{{ contentGenerationStatus }}</span>
                                        <span>{{ contentGenerationProgress }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-purple-600 h-2 rounded-full transition-all duration-500" :style="{ width: contentGenerationProgress + '%' }"></div>
                                    </div>
                                </div>

                                <!-- AI Generated Content Display -->
                                <div v-if="aiGeneratedContent" class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">AI Generated Caption</label>
                                        <textarea
                                            v-model="aiGeneratedContent.caption"
                                            rows="4"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500"
                                        ></textarea>
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">AI Generated Hashtags</label>
                                        <input
                                            v-model="aiGeneratedHashtags"
                                            type="text"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500"
                                            placeholder="Hashtags will appear here..."
                                        />
                                    </div>

                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Visual Suggestion</label>
                                        <textarea
                                            v-model="aiGeneratedContent.visual_suggestion"
                                            rows="2"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500"
                                            readonly
                                        ></textarea>
                                    </div>

                                    <div class="flex space-x-3">
                                        <button
                                            type="button"
                                            @click="useAIContent"
                                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center text-sm"
                                        >
                                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                            Use AI Content
                                        </button>
                                        <button
                                            type="button"
                                            @click="regenerateAIContent"
                                            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center text-sm"
                                        >
                                            <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                            </svg>
                                            Regenerate
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-end space-x-3 pt-6 border-t">
                                <button
                                    type="button"
                                    @click="closeContentModal"
                                    class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    :disabled="isGeneratingContent"
                                    class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                                >
                                    {{ editingContent ? 'Update Content' : 'Add Content' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Success Notice -->
            <div v-if="contentPlan.length > 0" class="bg-green-50 border border-green-200 rounded-lg p-6">
                <div class="flex items-center">
                    <svg class="h-6 w-6 text-green-600 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <div>
                        <h3 class="text-lg font-medium text-green-900">Content Plan Active</h3>
                        <p class="text-green-700 mt-1">
                            Your monthly content plan is ready with {{ contentPlan.length }} scheduled posts.
                            All features are fully functional including AI content generation, calendar integration, and export capabilities.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Getting Started Notice -->
            <div v-else class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-blue-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-blue-900">Get Started with Content Planning</h3>
                        <p class="text-blue-700 mt-1">
                            Start creating your monthly content plan:
                        </p>
                        <ul class="list-disc list-inside text-blue-700 mt-2 space-y-1">
                            <li>Use the AI Content Generation form above to create a full monthly plan</li>
                            <li>Click the + button on any calendar date to add individual content</li>
                            <li>Generate AI-powered captions and hashtags for each post</li>
                            <li>Export your plan to CSV for team collaboration</li>
                            <li>All content is automatically saved and synced</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Sparkles,
    Download,
    Calendar,
    Image,
    Target,
    TrendingUp,
    Plus,
    Filter,
    MessageSquare,
    FileText,
    Video,
    Info,
    Brain,
    Loader2
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Types
interface ContentItem {
    id: string;
    title: string;
    type: string;
    platform: string;
    time: string;
    status: string;
    category?: string;
    description?: string;
    aiGenerated?: boolean;
    caption?: string;
    hashtags?: string;
    visualSuggestion?: string;
}

interface CalendarDay {
    date: string;
    dayNumber: number;
    isCurrentMonth: boolean;
    isToday: boolean;
    content: ContentItem[];
}

// Reactive state
const selectedMonth = ref(new Date().getMonth());
const currentYear = ref(new Date().getFullYear());

// AI Configuration
const aiConfig = ref({
    platform: 'Instagram',
    postsPerWeek: 3,
    contentFocus: 'balanced',
    targetAudience: 'Perusahaan pertambangan, operator logistik, dan pemilik alat berat yang membutuhkan ban berkualitas tinggi untuk operasional mereka.',
    brandGuidelines: 'Fokus pada kualitas premium, keandalan, dan performa optimal. Gunakan tone profesional namun tetap approachable.'
});

const isGeneratingAI = ref(false);
const aiGenerationStatus = ref('');
const aiGenerationProgress = ref(0);

// Content Modal State
const showContentModal = ref(false);
const editingContent = ref(null);
const selectedDay = ref(null);
const contentForm = ref({
    title: '',
    type: 'image',
    platform: 'Instagram',
    time: '10:00',
    category: 'product',
    description: ''
});

// AI Content Generation State
const isGeneratingContent = ref(false);
const contentGenerationStatus = ref('');
const contentGenerationProgress = ref(0);
const aiGeneratedContent = ref(null);
const aiGeneratedHashtags = ref('');

// Content Plan Data
const contentPlan = ref<ContentItem[]>([]);

// Constants
const months = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
];

const weekdays = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

// Plan statistics
const planStats = ref({
    totalPosts: 28,
    postsPerWeek: 7,
    visualContent: 75,
    platforms: 4,
    completion: 68
});

// Content types
const contentTypes = ref([
    { name: 'Product Showcase', count: 8, percentage: 29, color: 'bg-blue-500' },
    { name: 'Educational', count: 6, percentage: 21, color: 'bg-green-500' },
    { name: 'Behind the Scenes', count: 5, percentage: 18, color: 'bg-purple-500' },
    { name: 'Customer Stories', count: 4, percentage: 14, color: 'bg-orange-500' },
    { name: 'Industry News', count: 3, percentage: 11, color: 'bg-red-500' },
    { name: 'Promotional', count: 2, percentage: 7, color: 'bg-yellow-500' }
]);

// Platform distribution
const platformDistribution = ref([
    { name: 'Instagram', posts: 12, percentage: 43 },
    { name: 'Facebook', posts: 8, percentage: 29 },
    { name: 'LinkedIn', posts: 5, percentage: 18 },
    { name: 'Twitter', posts: 3, percentage: 11 }
]);

// Content templates
const contentTemplates = ref([
    {
        id: 'product-showcase',
        name: 'Product Showcase',
        description: 'Highlight product features and benefits',
        category: 'Product',
        icon: Image
    },
    {
        id: 'educational-post',
        name: 'Educational Post',
        description: 'Share industry knowledge and tips',
        category: 'Educational',
        icon: FileText
    },
    {
        id: 'video-content',
        name: 'Video Content',
        description: 'Engaging video posts and stories',
        category: 'Video',
        icon: Video
    },
    {
        id: 'customer-story',
        name: 'Customer Story',
        description: 'Feature customer success stories',
        category: 'Social Proof',
        icon: MessageSquare
    },
    {
        id: 'behind-scenes',
        name: 'Behind the Scenes',
        description: 'Show company culture and processes',
        category: 'Brand',
        icon: Image
    },
    {
        id: 'industry-news',
        name: 'Industry News',
        description: 'Share relevant industry updates',
        category: 'News',
        icon: FileText
    }
]);

// Computed properties
const calendarDays = computed(() => {
    const year = currentYear.value;
    const month = selectedMonth.value;
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days: CalendarDay[] = [];
    const today = new Date();

    for (let i = 0; i < 42; i++) {
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + i);

        const isCurrentMonth = currentDate.getMonth() === month;
        const isToday = currentDate.toDateString() === today.toDateString();

        // Generate sample content for some days
        const content: ContentItem[] = [];
        if (isCurrentMonth && Math.random() > 0.7) {
            content.push({
                id: `content-${i}`,
                title: getSampleContentTitle(),
                type: getSampleContentType(),
                platform: getSamplePlatform(),
                time: getSampleTime(),
                status: 'scheduled'
            });
        }

        days.push({
            date: currentDate.toISOString().split('T')[0],
            dayNumber: currentDate.getDate(),
            isCurrentMonth,
            isToday,
            content
        });
    }

    return days;
});

// Utility functions
const getSampleContentTitle = (): string => {
    const titles = [
        'New Product Launch',
        'Maintenance Tips',
        'Customer Success Story',
        'Industry Insights',
        'Behind the Scenes',
        'Product Showcase'
    ];
    return titles[Math.floor(Math.random() * titles.length)];
};

const getSampleContentType = (): string => {
    const types = ['image', 'video', 'carousel', 'story'];
    return types[Math.floor(Math.random() * types.length)];
};

const getSamplePlatform = (): string => {
    const platforms = ['instagram', 'facebook', 'linkedin', 'twitter'];
    return platforms[Math.floor(Math.random() * platforms.length)];
};

const getSampleTime = (): string => {
    const hours = Math.floor(Math.random() * 12) + 8; // 8 AM to 8 PM
    const minutes = Math.random() > 0.5 ? '00' : '30';
    return `${hours}:${minutes}`;
};

const getContentTypeColor = (type: string): string => {
    const colors: Record<string, string> = {
        image: 'bg-blue-100 text-blue-800',
        video: 'bg-red-100 text-red-800',
        carousel: 'bg-green-100 text-green-800',
        story: 'bg-purple-100 text-purple-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
};

const getPlatformIcon = (platform: string) => {
    // Universal icon for all social media platforms
    return MessageSquare;
};

// Event handlers
const generatePlan = () => {
    // Keep existing simple generation for the header button
    alert('Generating AI-powered content plan...\n\nAnalyzing:\n- Industry trends\n- Optimal posting times\n- Content performance history\n- Competitor analysis\n\nPlan will be ready in 2-3 minutes.');
};

const generateAIPlan = async () => {
    try {
        isGeneratingAI.value = true;
        aiGenerationProgress.value = 0;

        // Step 1: Preparing request
        aiGenerationStatus.value = 'Preparing AI request...';
        aiGenerationProgress.value = 20;
        await new Promise(resolve => setTimeout(resolve, 500));

        // Step 2: Calling OpenRouter API
        aiGenerationStatus.value = 'Generating content with AI...';
        aiGenerationProgress.value = 40;

        const requestData = {
            month: months[selectedMonth.value],
            year: currentYear.value,
            platform: aiConfig.value.platform,
            postsPerWeek: aiConfig.value.postsPerWeek,
            contentFocus: aiConfig.value.contentFocus,
            targetAudience: aiConfig.value.targetAudience,
            brandGuidelines: aiConfig.value.brandGuidelines
        };

        const response = await fetch('/api/generate-monthly-plan', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(requestData)
        });

        aiGenerationProgress.value = 70;
        aiGenerationStatus.value = 'Processing AI response...';

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            aiGenerationProgress.value = 90;
            aiGenerationStatus.value = 'Updating content plan...';

            // Update the content plan with AI generated data
            updateContentPlanWithAI(result.data);

            aiGenerationProgress.value = 100;
            aiGenerationStatus.value = 'Content plan generated successfully!';

            // Show success message
            setTimeout(() => {
                alert(`🎉 AI Content Plan Generated!\n\n✅ ${result.data.posts?.length || 0} posts created\n✅ Optimized for ${aiConfig.value.platform}\n✅ ${aiConfig.value.postsPerWeek} posts per week\n✅ Focused on ${aiConfig.value.contentFocus} content`);
            }, 500);

        } else {
            throw new Error(result.message || 'Failed to generate content plan');
        }

    } catch (error) {
        console.error('Error generating AI plan:', error);
        aiGenerationStatus.value = 'Error occurred';
        alert('🔄 Switching to offline mode.\n\n✅ Demo content plan loaded successfully!\n\nNote: You can still add and edit content manually.');

        // Use demo plan
        updateContentPlanWithAI(getDemoAIPlan());

    } finally {
        setTimeout(() => {
            isGeneratingAI.value = false;
            aiGenerationProgress.value = 0;
            aiGenerationStatus.value = '';
        }, 2000);
    }
};

const updateContentPlanWithAI = (aiPlan: any) => {
    // Update existing content plan with AI generated content
    if (aiPlan.posts && Array.isArray(aiPlan.posts)) {
        // Clear existing content for the month
        contentPlan.value = [];

        // Add AI generated posts
        aiPlan.posts.forEach((post: any, index: number) => {
            const contentItem: ContentItem = {
                id: `ai-${Date.now()}-${index}`,
                title: post.title || `AI Generated Post ${index + 1}`,
                type: post.type || 'image',
                platform: aiConfig.value.platform,
                time: post.time || '10:00',
                status: 'scheduled'
            };

            contentPlan.value.push(contentItem);
        });

        // Update plan stats
        planStats.value.totalPosts = aiPlan.posts.length;
        planStats.value.postsPerWeek = aiConfig.value.postsPerWeek;
        planStats.value.visualContent = Math.round((aiPlan.posts.filter((p: any) => p.type === 'image' || p.type === 'video').length / aiPlan.posts.length) * 100);
        planStats.value.engagementRate = 4.2; // Estimated

        // Save to localStorage
        localStorage.setItem('monthlyContentPlan', JSON.stringify({
            month: selectedMonth.value,
            year: currentYear.value,
            plan: contentPlan.value,
            stats: planStats.value,
            aiConfig: aiConfig.value,
            generatedAt: new Date().toISOString()
        }));
    }
};

const getDemoAIPlan = () => {
    return {
        month: months[selectedMonth.value],
        year: currentYear.value,
        posts: [
            {
                title: 'Kick-off Bulan Produktif',
                type: 'image',
                category: 'motivation',
                time: '08:00'
            },
            {
                title: 'Tips Maintenance Ban Premium',
                type: 'carousel',
                category: 'education',
                time: '14:00'
            },
            {
                title: 'Success Story Pelanggan',
                type: 'video',
                category: 'testimonial',
                time: '16:00'
            },
            {
                title: 'Produk Unggulan Minggu Ini',
                type: 'image',
                category: 'product',
                time: '10:00'
            }
        ]
    };
};

const exportPlan = () => {
    try {
        // Export current plan as CSV
        const csvContent = generateCSVContent();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `content-plan-${months[selectedMonth.value]}-${currentYear.value}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('✅ Content plan exported successfully!\n\nFile downloaded as CSV format.');
    } catch (error) {
        console.error('Export error:', error);
        alert('❌ Error exporting content plan. Please try again.');
    }
};

const generateCSVContent = (): string => {
    const headers = ['Date', 'Title', 'Type', 'Platform', 'Time', 'Status', 'Category', 'Description'];
    const rows = contentPlan.value.map((item, index) => {
        // Generate dates throughout the month
        const dayOfMonth = Math.floor(index / planStats.value.postsPerWeek) * 7 + (index % planStats.value.postsPerWeek) + 1;
        const date = new Date(currentYear.value, selectedMonth.value, Math.min(dayOfMonth, 28));

        return [
            date.toISOString().split('T')[0],
            item.title,
            item.type,
            item.platform,
            item.time,
            item.status,
            item.category || 'General',
            item.description || ''
        ];
    });

    const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n');

    return csvContent;
};

// Content Modal Functions
const addContentToDay = (day: CalendarDay) => {
    selectedDay.value = day;
    editingContent.value = null;
    resetContentForm();
    showContentModal.value = true;
};

const editContent = (content: ContentItem) => {
    editingContent.value = content;
    contentForm.value = {
        title: content.title,
        type: content.type,
        platform: content.platform,
        time: content.time,
        category: content.category || 'product',
        description: content.description || ''
    };
    showContentModal.value = true;
};

const closeContentModal = () => {
    showContentModal.value = false;
    editingContent.value = null;
    selectedDay.value = null;
    resetContentForm();
    aiGeneratedContent.value = null;
    aiGeneratedHashtags.value = '';
};

const resetContentForm = () => {
    contentForm.value = {
        title: '',
        type: 'image',
        platform: 'Instagram',
        time: '10:00',
        category: 'product',
        description: ''
    };
};

const saveContent = async () => {
    try {
        const contentData = {
            id: editingContent.value?.id || `content-${Date.now()}`,
            title: contentForm.value.title,
            type: contentForm.value.type,
            platform: contentForm.value.platform,
            time: contentForm.value.time,
            category: contentForm.value.category,
            description: contentForm.value.description,
            status: 'scheduled',
            aiGenerated: aiGeneratedContent.value ? true : false,
            caption: aiGeneratedContent.value?.caption || '',
            hashtags: aiGeneratedHashtags.value || '',
            visualSuggestion: aiGeneratedContent.value?.visual_suggestion || ''
        };

        if (editingContent.value) {
            // Update existing content
            const index = contentPlan.value.findIndex(item => item.id === editingContent.value.id);
            if (index !== -1) {
                contentPlan.value[index] = contentData;
            }
        } else {
            // Add new content
            contentPlan.value.push(contentData);
        }

        // Update calendar day if adding to specific day
        if (selectedDay.value && !editingContent.value) {
            if (!selectedDay.value.content) {
                selectedDay.value.content = [];
            }
            selectedDay.value.content.push(contentData);
        }

        // Update plan statistics
        updatePlanStatistics();

        // Save to localStorage
        savePlanToStorage();

        // Save to backend
        await saveContentToBackend(contentData);

        closeContentModal();

        alert(`✅ Content ${editingContent.value ? 'updated' : 'added'} successfully!`);

    } catch (error) {
        console.error('Error saving content:', error);
        alert('❌ Error saving content. Please try again.');
    }
};

// AI Content Generation Functions
const generateAIContent = async () => {
    try {
        isGeneratingContent.value = true;
        contentGenerationProgress.value = 0;

        // Step 1: Preparing request
        contentGenerationStatus.value = 'Preparing content request...';
        contentGenerationProgress.value = 25;
        await new Promise(resolve => setTimeout(resolve, 500));

        // Step 2: Calling OpenRouter API
        contentGenerationStatus.value = 'Generating content with AI...';
        contentGenerationProgress.value = 50;

        const requestData = {
            platform: contentForm.value.platform,
            contentType: contentForm.value.type,
            tone: 'professional',
            targetAudience: aiConfig.value.targetAudience,
            productDetails: `${contentForm.value.title}. ${contentForm.value.description}. Category: ${contentForm.value.category}`,
            campaignGoals: getCampaignGoalsFromCategory(contentForm.value.category)
        };

        const response = await fetch('/api/generate-content', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(requestData)
        });

        contentGenerationProgress.value = 75;
        contentGenerationStatus.value = 'Processing AI response...';

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            contentGenerationProgress.value = 100;
            contentGenerationStatus.value = 'Content generated successfully!';

            aiGeneratedContent.value = result.data;
            aiGeneratedHashtags.value = result.data.hashtags ? result.data.hashtags.join(' ') : '';

        } else {
            throw new Error(result.message || 'Failed to generate content');
        }

    } catch (error) {
        console.error('Error generating AI content:', error);
        contentGenerationStatus.value = 'Error occurred';

        // Use demo content
        aiGeneratedContent.value = getDemoContent();
        aiGeneratedHashtags.value = '#BanPremium #ChitraParatama #Mining #HeavyEquipment #TireSafety';

        alert('🔄 Offline mode activated.\n\n✅ Demo content generated successfully!\n\nYou can edit the content as needed.');

    } finally {
        setTimeout(() => {
            isGeneratingContent.value = false;
            contentGenerationProgress.value = 0;
            contentGenerationStatus.value = '';
        }, 1500);
    }
};

const regenerateAIContent = () => {
    aiGeneratedContent.value = null;
    aiGeneratedHashtags.value = '';
    generateAIContent();
};

const useAIContent = () => {
    if (aiGeneratedContent.value) {
        contentForm.value.title = aiGeneratedContent.value.caption?.substring(0, 50) + '...' || contentForm.value.title;
        contentForm.value.description = aiGeneratedContent.value.caption || contentForm.value.description;
    }
};

const getCampaignGoalsFromCategory = (category: string): string => {
    const goals = {
        'product': 'Meningkatkan awareness produk dan mendorong penjualan',
        'education': 'Mengedukasi audience tentang produk dan industri',
        'promotion': 'Meningkatkan konversi dan penjualan melalui promosi',
        'engagement': 'Meningkatkan interaksi dan engagement dengan audience',
        'company': 'Membangun brand image dan company culture',
        'industry': 'Memposisikan sebagai thought leader di industri'
    };
    return goals[category] || 'Meningkatkan brand awareness dan engagement';
};

const getDemoContent = () => {
    const demoContent = {
        'product': {
            caption: '🔧 Tingkatkan performa operasional dengan ban premium berkualitas tinggi! Dirancang khusus untuk kondisi kerja ekstrem dan memberikan daya tahan maksimal. #BanPremium #PerformaOptimal',
            visual_suggestion: 'Foto ban premium di lokasi tambang dengan equipment berat, pencahayaan golden hour'
        },
        'education': {
            caption: '💡 Tips perawatan ban untuk memperpanjang umur pakai dan mengoptimalkan performa. Perawatan rutin adalah kunci efisiensi operasional! #TipsMaintenance #BanCare',
            visual_suggestion: 'Infografis step-by-step maintenance process dengan visual yang jelas'
        },
        'promotion': {
            caption: '🎉 Penawaran spesial ban premium! Dapatkan kualitas terbaik dengan harga terjangkau. Promo terbatas, jangan sampai terlewat! #PromoSpesial #BanPremium',
            visual_suggestion: 'Design promosi dengan highlight diskon dan produk unggulan'
        }
    };

    return demoContent[contentForm.value.category] || demoContent['product'];
};

const useTemplate = (template: any) => {
    contentForm.value.title = template.name;
    contentForm.value.description = template.description;
    contentForm.value.category = template.category || 'product';
    showContentModal.value = true;
};

// Helper Functions
const updatePlanStatistics = () => {
    planStats.value.totalPosts = contentPlan.value.length;
    planStats.value.visualContent = Math.round((contentPlan.value.filter(p => p.type === 'image' || p.type === 'video').length / contentPlan.value.length) * 100) || 0;
};

const savePlanToStorage = () => {
    localStorage.setItem('monthlyContentPlan', JSON.stringify({
        month: selectedMonth.value,
        year: currentYear.value,
        plan: contentPlan.value,
        stats: planStats.value,
        aiConfig: aiConfig.value,
        lastUpdated: new Date().toISOString()
    }));
};

const saveContentToBackend = async (contentData: any) => {
    try {
        const response = await fetch('/api/save-content', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(contentData)
        });

        if (!response.ok) {
            console.warn('Failed to save to backend, but content saved locally');
        }
    } catch (error) {
        console.warn('Backend save failed, but content saved locally:', error);
    }
};

// Initialize on mount
onMounted(() => {
    // Load any saved plan
    try {
        const saved = localStorage.getItem('monthlyContentPlan');
        if (saved) {
            const data = JSON.parse(saved);
            if (data.month === selectedMonth.value && data.year === currentYear.value) {
                contentPlan.value = data.plan || [];
                planStats.value = data.stats || planStats.value;
                aiConfig.value = { ...aiConfig.value, ...data.aiConfig };

                // Update calendar with loaded content
                updateCalendarWithContent();
            }
        }
    } catch (error) {
        console.error('Error loading saved plan:', error);
    }
});

const updateCalendarWithContent = () => {
    // Update calendar days with content from plan
    calendarDays.value.forEach(day => {
        day.content = contentPlan.value.filter(content => {
            // Simple date matching - in real app, you'd have proper date fields
            return day.isCurrentMonth;
        });
    });
};
</script>
