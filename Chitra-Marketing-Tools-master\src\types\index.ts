export interface Product {
  id: string;
  oldMaterialNo: string;
  materialDescription: string;
  description: string;
  price: number; // IDR price
  priceUSD?: number; // USD price
  exchangeRate?: number; // USD to IDR exchange rate
  slowMoving?: boolean; // Indicates if the product is slow moving
}

export interface BundleItem {
  product: Product;
  quantity: number;
}

export interface BundleConfig {
  minimumMargin: number; // percentage
  additionalCosts: {
    shipping: number;
    other: number;
    description: string;
  };
  customerId?: string; // Reference to selected customer
  quoDate?: string; // Quotation date
  validityQuote?: string; // Validity period
  from?: string; // Sender information
  note?: string; // Additional notes
  termsAndCondition?: string; // Terms and conditions
}

export interface PricingResult {
  totalCost: number;
  recommendedPrice: number;
  profit: number;
  profitMargin: number;
}

export * from './customer';
export * from './proposal';
