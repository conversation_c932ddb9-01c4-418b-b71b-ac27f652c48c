import { jsPDF } from 'jspdf';
import { BundleItem, PricingResult, Customer } from '../types';

export function generateProfessionalQuotationPDF(
  bundleItems: BundleItem[],
  pricingResult: PricingResult,
  additionalInfo: string,
  customer?: Customer | null,
  quotationDetails?: {
    quoDate?: string;
    validityQuote?: string;
    from?: string;
    termsAndCondition?: string;
  }
): void {
  try {
    console.log('Generating professional PDF quotation...');

    // Create a new PDF document
    const doc = new jsPDF();

    // Add header with company info and quotation title
    addHeader(doc);

    // Add company and customer information
    addCompanyAndCustomerInfo(doc, customer);

    // Add quotation details
    addQuotationDetails(doc, quotationDetails);

    // Add product table (simplified version without autoTable)
    addSimpleProductTable(doc, bundleItems);

    // Add totals
    addTotals(doc, pricingResult);

    // Add notes and terms
    addNotesAndTerms(doc, additionalInfo, quotationDetails?.termsAndCondition);

    // Add footer
    addFooter(doc);

    // Save the PDF with customer name if available
    const filename = customer
      ? `Chitra-Quotation-${customer.name.replace(/\\s+/g, '_')}.pdf`
      : 'Chitra-Quotation.pdf';

    doc.save(filename);
    console.log(`Professional PDF saved successfully as ${filename}`);

    // Show success message with customer name if available
    const successMessage = customer
      ? `Quotation for ${customer.name} generated successfully!`
      : 'PDF quotation generated successfully!';

    alert(successMessage);
  } catch (error) {
    console.error('Error generating professional PDF:', error);
    alert('Failed to generate PDF. Please check the console for details.');
  }
}

function addHeader(doc: jsPDF): void {
  try {
    // Add company logo from file
    // We need to use a base64 encoded image or a URL
    // For now, we'll use a URL path to the public folder
    const logoPath = '/assets/cp_logo.png';
    doc.addImage(logoPath, 'PNG', 20, 15, 45, 15);

    // We're not showing company name and tagline text anymore, just the logo
  } catch (error) {
    console.error('Error adding logo:', error);

    // Fallback if logo loading fails - just show a colored rectangle
    doc.setDrawColor(0, 120, 200);
    doc.setFillColor(0, 120, 200);
    doc.rect(20, 15, 30, 15, 'F');
  }

  // Add QUOTATION title on the right
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(80, 80, 200); // Purple-blue color
  doc.setFontSize(18);
  doc.text('QUOTATION', 200, 22, { align: 'right' });

  // Add quotation number
  const today = new Date();
  const quotationNumber = `QUO-CP-${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;
  doc.setFontSize(10);
  doc.text(`# ${quotationNumber}`, 200, 28, { align: 'right' });

  // Add horizontal line
  doc.setDrawColor(220, 220, 220);
  doc.setLineWidth(0.5);
  doc.line(20, 35, 190, 35);
}

function addCompanyAndCustomerInfo(doc: jsPDF, customer?: Customer | null): void {
  // Company information (left side)
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(11);
  doc.text('PT Chitra Paratama', 25, 45);

  // Customer information (right side) with "To" label
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(11);
  doc.text('To', 140, 45);

  if (customer) {
    doc.setFont('helvetica', 'bold');
    doc.text(customer.company || customer.name, 140, 50);

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(9);
    doc.text(`${customer.address}`, 140, 55);
    doc.text(`${customer.email} | ${customer.phone}`, 140, 60);
  } else {
    doc.setFont('helvetica', 'normal');
    doc.text('Customer details not provided', 140, 50);
  }

  // Add "Ship to" information
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('Ship to', 140, 70);

  doc.setFont('helvetica', 'normal');
  if (customer) {
    doc.text(customer.address, 140, 75);
  } else {
    doc.text('Same as billing address', 140, 75);
  }
}

function addQuotationDetails(doc: jsPDF, details?: {
  quoDate?: string;
  validityQuote?: string;
  from?: string;
  termsAndCondition?: string;
}): void {
  // Quotation details (left side)
  const today = new Date();
  const futureDate = new Date();
  futureDate.setDate(today.getDate() + 30); // Valid for 30 days

  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('Quo Date:', 25, 70);
  doc.text('Validity Quote:', 25, 75);
  doc.text('From:', 25, 80);

  doc.setFont('helvetica', 'normal');

  // Use provided values or defaults
  const quoDate = details?.quoDate ? new Date(details.quoDate).toLocaleDateString('en-GB') : today.toLocaleDateString('en-GB');
  const validityQuote = details?.validityQuote ? new Date(details.validityQuote).toLocaleDateString('en-GB') : futureDate.toLocaleDateString('en-GB');
  const from = details?.from || 'Sales Team Chitra';

  doc.text(quoDate, 60, 70);
  doc.text(validityQuote, 60, 75);
  doc.text(from, 60, 80);
}

function addSimpleProductTable(doc: jsPDF, bundleItems: BundleItem[]): void {
  try {
    // Set starting position
    const startY = 90;
    let currentY = startY;

    // Add table header
    doc.setFillColor(65, 105, 225); // Royal blue
    doc.setDrawColor(65, 105, 225);
    doc.rect(20, currentY, 170, 10, 'F');

    // Add header text
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(10);
    doc.text('#', 25, currentY + 6);
    doc.text('Item', 40, currentY + 6);
    doc.text('Qty', 120, currentY + 6);

    // We're not showing individual prices anymore
    // doc.text('Price', 140, currentY + 6);
    // doc.text('Amount', 170, currentY + 6);

    currentY += 10;

    // Add table rows
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(9);

    let isAlternateRow = false;

    bundleItems.forEach((item, index) => {
      // Set alternate row background
      if (isAlternateRow) {
        doc.setFillColor(245, 245, 255);
        doc.setDrawColor(245, 245, 255);
        doc.rect(20, currentY, 170, 10, 'F');
      }

      // Add row content
      doc.text((index + 1).toString(), 25, currentY + 6);

      // Item description (may need to be wrapped)
      const description = item.product.materialDescription;
      if (description.length > 50) {
        doc.text(description.substring(0, 50), 40, currentY + 4);
        doc.text(description.substring(50), 40, currentY + 8);
      } else {
        doc.text(description, 40, currentY + 6);
      }

      // Part number in smaller text
      doc.setFontSize(8);
      doc.text(`Part Number: ${item.product.oldMaterialNo}`, 40, currentY + 10);
      doc.setFontSize(9);

      // Only show quantity, not individual prices
      doc.text(item.quantity.toString(), 120, currentY + 6);

      // Move to next row
      currentY += 15;
      isAlternateRow = !isAlternateRow;
    });

    // Add table border
    doc.setDrawColor(200, 200, 200);
    doc.setLineWidth(0.1);
    doc.rect(20, startY, 170, currentY - startY, 'S');

    // We're not showing bundle price at the bottom of the table anymore
    // Just add some space
    currentY += 5;

    // Store the final Y position for later use
    (doc as any).lastTableEndY = currentY;

    console.log('Simple table added successfully');
  } catch (error) {
    console.error('Error adding simple product table:', error);
    throw error;
  }
}

function addTotals(doc: jsPDF, pricingResult: PricingResult): void {
  // Get the final Y position after the table
  const finalY = (doc as any).lastTableEndY + 5;

  // Add Bundling Price instead of Total
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  doc.text('Bundling Price', 150, finalY + 5);

  // Add the amount
  doc.setFont('helvetica', 'bold');
  doc.text(`Rp${pricingResult.recommendedPrice.toLocaleString()}`, 190, finalY + 5, { align: 'right' });

  // Add a light gray background for the totals section
  doc.setFillColor(245, 245, 245);
  doc.rect(140, finalY, 50, 10, 'F');

  // Re-add the text on top of the background
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(0, 0, 0);
  doc.text('Bundling Price', 150, finalY + 5);

  doc.setFont('helvetica', 'bold');
  doc.text(`Rp${pricingResult.recommendedPrice.toLocaleString()}`, 190, finalY + 5, { align: 'right' });

  // Store the final Y position for later use
  (doc as any).lastTotalsEndY = finalY + 10;
}

function addNotesAndTerms(doc: jsPDF, additionalInfo: string, customTerms?: string): void {
  // Get the final Y position after the totals
  const finalY = (doc as any).lastTotalsEndY + 10;

  // Add Notes
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('Note:', 25, finalY);

  doc.setFont('helvetica', 'normal');
  doc.text('', 25, finalY + 5);

  if (additionalInfo && additionalInfo.trim() !== '') {
    const lines = doc.splitTextToSize(additionalInfo, 150);
    doc.text(lines, 25, finalY + 12);
  }

  // Add Terms & Conditions
  const termsY = finalY + (additionalInfo ? 25 : 15);

  doc.setFont('helvetica', 'bold');
  doc.text('Terms & Conditions:', 25, termsY);

  doc.setFont('helvetica', 'normal');

  if (customTerms && customTerms.trim() !== '') {
    // Use custom terms if provided
    const termsLines = doc.splitTextToSize(customTerms, 150);
    doc.text(termsLines, 25, termsY + 5);
  } else {
    // Use default terms
    doc.text('Payment Terms : 30 days after Date Invoice', 25, termsY + 5);
    doc.text('Stock : Balikpapan', 25, termsY + 10);
    doc.text('DDP : Site', 25, termsY + 15);
    doc.text('Exclude Tax', 25, termsY + 20);
  }

  // Add horizontal line
  const lineY = termsY + 25;
  doc.setDrawColor(100, 100, 100);
  doc.setLineWidth(0.5);
  doc.line(25, lineY, 190, lineY);

  // Store the final Y position for later use
  (doc as any).lastTermsEndY = lineY;
}

function addFooter(doc: jsPDF): void {
  // Get the final Y position after the terms
  const finalY = (doc as any).lastTermsEndY + 10;

  // Add company details in footer
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('PT. CHITRA PARATAMA', 25, finalY);
  doc.text('BANK MANDIRI', 25, finalY + 5);

  doc.setFont('helvetica', 'normal');
  doc.text('IDR A/C NO:127 - 000 - 00 - 17416', 25, finalY + 10);

  // Add horizontal line
  doc.setDrawColor(100, 100, 100);
  doc.setLineWidth(0.5);
  doc.line(25, finalY + 20, 190, finalY + 20);
}
