<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Customer Analysis</h1>
                    <p class="mt-2 text-gray-600">Customer segmentation, behavior analysis, and lifetime value insights</p>
                </div>
                <div class="flex space-x-3">
                    <select v-model="selectedPeriod" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                        <option value="3months">Last 3 Months</option>
                        <option value="6months">Last 6 Months</option>
                        <option value="12months">Last 12 Months</option>
                        <option value="24months">Last 24 Months</option>
                    </select>
                    <button
                        @click="runChurnAnalysis"
                        class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 flex items-center"
                    >
                        <AlertTriangle class="h-4 w-4 mr-2" />
                        Churn Analysis
                    </button>
                    <button
                        @click="exportReport"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Report
                    </button>
                </div>
            </div>

            <!-- Customer KPIs -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Users class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Customers</p>
                            <p class="text-2xl font-bold text-gray-900">{{ customerKPIs.totalCustomers }}</p>
                            <p class="text-sm text-blue-600">{{ customerKPIs.activeCustomers }} active</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <DollarSign class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Avg Customer LTV</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(customerKPIs.avgLTV) }}</p>
                            <p class="text-sm text-green-600">+12% vs last year</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <TrendingUp class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Retention Rate</p>
                            <p class="text-2xl font-bold text-gray-900">{{ customerKPIs.retentionRate }}%</p>
                            <p class="text-sm text-purple-600">{{ customerKPIs.churnRate }}% churn rate</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <ShoppingCart class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Avg Order Value</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(customerKPIs.avgOrderValue) }}</p>
                            <p class="text-sm text-orange-600">{{ customerKPIs.orderFrequency }}x per year</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Segmentation -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Segmentation Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Segmentation</h3>
                    <div class="space-y-4">
                        <div v-for="segment in customerSegments" :key="segment.name" class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div :class="['w-4 h-4 rounded-full mr-3', segment.color]"></div>
                                <div>
                                    <h4 class="font-medium text-gray-900">{{ segment.name }}</h4>
                                    <p class="text-sm text-gray-600">{{ segment.description }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-gray-900">{{ segment.count }}</p>
                                <p class="text-sm text-gray-500">{{ segment.percentage }}%</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Purchase Behavior -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Purchase Behavior Trends</h3>
                    <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                        <div class="text-center">
                            <BarChart3 class="h-12 w-12 text-gray-400 mx-auto mb-2" />
                            <p class="text-gray-600">Purchase Behavior Chart</p>
                            <p class="text-sm text-gray-500">Chart.js integration</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Customers Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Top Customers by Value</h3>
                    <div class="flex space-x-3">
                        <input
                            v-model="searchQuery"
                            type="text"
                            placeholder="Search customers..."
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center">
                            <Filter class="h-4 w-4 mr-2" />
                            Filter
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Segment</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">LTV</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Order</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Frequency</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Churn Risk</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="customer in filteredCustomers" :key="customer.id">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-700">{{ customer.initials }}</span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">{{ customer.name }}</p>
                                            <p class="text-sm text-gray-500">{{ customer.industry }}</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        getSegmentColor(customer.segment)
                                    ]">
                                        {{ customer.segment }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(customer.ltv) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ customer.lastOrder }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ customer.orderFrequency }}x/year</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        customer.churnRisk === 'Low' ? 'bg-green-100 text-green-800' :
                                        customer.churnRisk === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-red-100 text-red-800'
                                    ]">
                                        {{ customer.churnRisk }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                    <button class="text-green-600 hover:text-green-900">Contact</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Customer Insights -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Churn Risk Analysis -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Churn Risk Analysis</h3>
                    <div class="space-y-4">
                        <div v-for="risk in churnRiskAnalysis" :key="risk.level" class="flex items-center justify-between p-3 rounded-lg"
                             :class="risk.level === 'High' ? 'bg-red-50' : risk.level === 'Medium' ? 'bg-yellow-50' : 'bg-green-50'">
                            <div class="flex items-center">
                                <AlertTriangle v-if="risk.level === 'High'" class="h-5 w-5 text-red-600 mr-3" />
                                <AlertCircle v-else-if="risk.level === 'Medium'" class="h-5 w-5 text-yellow-600 mr-3" />
                                <CheckCircle v-else class="h-5 w-5 text-green-600 mr-3" />
                                <div>
                                    <h4 class="font-medium" :class="risk.level === 'High' ? 'text-red-900' : risk.level === 'Medium' ? 'text-yellow-900' : 'text-green-900'">
                                        {{ risk.level }} Risk
                                    </h4>
                                    <p class="text-sm" :class="risk.level === 'High' ? 'text-red-700' : risk.level === 'Medium' ? 'text-yellow-700' : 'text-green-700'">
                                        {{ risk.description }}
                                    </p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold" :class="risk.level === 'High' ? 'text-red-900' : risk.level === 'Medium' ? 'text-yellow-900' : 'text-green-900'">
                                    {{ risk.count }}
                                </p>
                                <p class="text-sm" :class="risk.level === 'High' ? 'text-red-600' : risk.level === 'Medium' ? 'text-yellow-600' : 'text-green-600'">
                                    customers
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Retention Strategies</h3>
                    <div class="space-y-4">
                        <div v-for="strategy in retentionStrategies" :key="strategy.id" class="flex items-start p-3 bg-blue-50 rounded-lg">
                            <Lightbulb class="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <h4 class="font-medium text-blue-900">{{ strategy.title }}</h4>
                                <p class="text-sm text-blue-700">{{ strategy.description }}</p>
                                <p class="text-xs text-blue-600 mt-1">Expected impact: {{ strategy.impact }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-purple-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-purple-900">Advanced Customer Analytics Coming Soon</h3>
                        <p class="text-purple-700 mt-1">
                            Enhanced customer intelligence features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-purple-700 mt-2 space-y-1">
                            <li>AI-powered customer behavior prediction</li>
                            <li>Real-time customer sentiment analysis</li>
                            <li>Automated customer journey mapping</li>
                            <li>Personalized marketing campaign recommendations</li>
                            <li>Customer satisfaction scoring and tracking</li>
                            <li>Integration with CRM and marketing automation</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    AlertTriangle,
    Download,
    Users,
    DollarSign,
    TrendingUp,
    ShoppingCart,
    BarChart3,
    Filter,
    AlertCircle,
    CheckCircle,
    Lightbulb,
    Info
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Reactive state
const selectedPeriod = ref('12months');
const searchQuery = ref('');

// Customer KPIs
const customerKPIs = ref({
    totalCustomers: 1247,
    activeCustomers: 1089,
    avgLTV: 485000000,
    retentionRate: 87.3,
    churnRate: 12.7,
    avgOrderValue: 45000000,
    orderFrequency: 3.2
});

// Customer segments
const customerSegments = ref([
    { name: 'Champions', description: 'High value, high frequency', count: 156, percentage: 12.5, color: 'bg-green-500' },
    { name: 'Loyal Customers', description: 'Regular buyers, good value', count: 312, percentage: 25.0, color: 'bg-blue-500' },
    { name: 'Potential Loyalists', description: 'Recent customers, good potential', count: 249, percentage: 20.0, color: 'bg-purple-500' },
    { name: 'New Customers', description: 'Recent first-time buyers', count: 187, percentage: 15.0, color: 'bg-yellow-500' },
    { name: 'At Risk', description: 'Declining engagement', count: 218, percentage: 17.5, color: 'bg-orange-500' },
    { name: 'Cannot Lose Them', description: 'High value but declining', count: 125, percentage: 10.0, color: 'bg-red-500' }
]);

// Customer data
const customers = ref([
    { id: 1, name: 'PT Mining Sejahtera', initials: 'MS', industry: 'Mining', segment: 'Champions', ltv: 850000000, lastOrder: '2024-12-15', orderFrequency: 6, churnRisk: 'Low' },
    { id: 2, name: 'CV Konstruksi Jaya', initials: 'KJ', industry: 'Construction', segment: 'Loyal Customers', ltv: 620000000, lastOrder: '2024-12-10', orderFrequency: 4, churnRisk: 'Low' },
    { id: 3, name: 'PT Logistik Prima', initials: 'LP', industry: 'Logistics', segment: 'Potential Loyalists', ltv: 450000000, lastOrder: '2024-11-28', orderFrequency: 3, churnRisk: 'Medium' },
    { id: 4, name: 'PT Tambang Nusantara', initials: 'TN', industry: 'Mining', segment: 'Champions', ltv: 920000000, lastOrder: '2024-12-18', orderFrequency: 8, churnRisk: 'Low' },
    { id: 5, name: 'CV Angkutan Berat', initials: 'AB', industry: 'Transportation', segment: 'At Risk', ltv: 280000000, lastOrder: '2024-10-15', orderFrequency: 2, churnRisk: 'High' },
    { id: 6, name: 'PT Industri Manufaktur', initials: 'IM', industry: 'Manufacturing', segment: 'New Customers', ltv: 180000000, lastOrder: '2024-12-05', orderFrequency: 1, churnRisk: 'Medium' },
    { id: 7, name: 'PT Pertanian Modern', initials: 'PM', industry: 'Agriculture', segment: 'Loyal Customers', ltv: 520000000, lastOrder: '2024-12-12', orderFrequency: 4, churnRisk: 'Low' },
    { id: 8, name: 'CV Eksplorasi Mineral', initials: 'EM', industry: 'Mining', segment: 'Cannot Lose Them', ltv: 750000000, lastOrder: '2024-11-20', orderFrequency: 5, churnRisk: 'High' },
    { id: 9, name: 'PT Infrastruktur Bangunan', initials: 'IB', industry: 'Construction', segment: 'Potential Loyalists', ltv: 380000000, lastOrder: '2024-12-08', orderFrequency: 3, churnRisk: 'Medium' },
    { id: 10, name: 'PT Distribusi Nasional', initials: 'DN', industry: 'Logistics', segment: 'Loyal Customers', ltv: 590000000, lastOrder: '2024-12-14', orderFrequency: 5, churnRisk: 'Low' }
]);

// Churn risk analysis
const churnRiskAnalysis = ref([
    { level: 'High', description: 'No orders in 60+ days, declining engagement', count: 89 },
    { level: 'Medium', description: 'Irregular ordering pattern, reduced frequency', count: 156 },
    { level: 'Low', description: 'Regular orders, stable engagement', count: 844 }
]);

// Retention strategies
const retentionStrategies = ref([
    {
        id: 1,
        title: 'Personalized Loyalty Program',
        description: 'Create tier-based rewards for high-value customers',
        impact: '15% increase in retention'
    },
    {
        id: 2,
        title: 'Proactive Customer Outreach',
        description: 'Contact at-risk customers with special offers',
        impact: '25% reduction in churn'
    },
    {
        id: 3,
        title: 'Product Recommendation Engine',
        description: 'AI-powered suggestions based on purchase history',
        impact: '20% increase in order frequency'
    },
    {
        id: 4,
        title: 'Customer Success Program',
        description: 'Dedicated support for high-value accounts',
        impact: '30% improvement in satisfaction'
    }
]);

// Computed properties
const filteredCustomers = computed(() => {
    if (!searchQuery.value) return customers.value;
    return customers.value.filter(customer =>
        customer.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        customer.industry.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
        customer.segment.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
});

// Utility functions
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const getSegmentColor = (segment: string): string => {
    const colors: Record<string, string> = {
        'Champions': 'bg-green-100 text-green-800',
        'Loyal Customers': 'bg-blue-100 text-blue-800',
        'Potential Loyalists': 'bg-purple-100 text-purple-800',
        'New Customers': 'bg-yellow-100 text-yellow-800',
        'At Risk': 'bg-orange-100 text-orange-800',
        'Cannot Lose Them': 'bg-red-100 text-red-800'
    };
    return colors[segment] || 'bg-gray-100 text-gray-800';
};

// Event handlers
const runChurnAnalysis = () => {
    alert('Running advanced churn prediction analysis...\n\nThis will analyze:\n- Purchase patterns\n- Engagement metrics\n- Communication history\n- Industry trends\n\nResults will be available in 5-10 minutes.');
};

const exportReport = () => {
    try {
        const csvContent = [
            ['Customer', 'Industry', 'Segment', 'LTV', 'Last Order', 'Order Frequency', 'Churn Risk'].join(','),
            ...filteredCustomers.value.map(customer => [
                `"${customer.name}"`,
                customer.industry,
                customer.segment,
                customer.ltv,
                customer.lastOrder,
                customer.orderFrequency,
                customer.churnRisk
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `customer_analysis_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Customer analysis report exported successfully!');
    } catch (error) {
        console.error('Error exporting report:', error);
        alert('Failed to export report.');
    }
};

// Initialize on mount
onMounted(() => {
    // Load customer data
});
</script>
