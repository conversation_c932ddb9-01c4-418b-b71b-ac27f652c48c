import React, { useState, useEffect, useCallback } from 'react';
import {
  Package,
  Search,
  Filter,
  BarChart3,
  ChevronDown,
  ChevronUp,
  FileText,
  Download,
  Truck,
  ShoppingBag,
  Users,
  Loader2
} from 'lucide-react';
import { Product } from '../types';
import { fetchProducts } from '../services/productService';
import {
  getIntegratedProductData,
  analyzeProductData
} from '../services/productAnalysisService';
import {
  ProductAnalysisResult,
  IntegratedProductData,
  ProductFilterOptions
} from '../types/productAnalysis';
import ProductAnalysisCharts from '../components/ProductAnalysisCharts';
import { fetchFleetData, FleetData } from '../services/fleetDataService';
import { loadSalesRevenueData, SalesRevenueItem } from '../services/salesRevenue2025Service';

const ProductAnalysisPage: React.FC = () => {
  // State for products and filtering
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Advanced filter options
  const [showFilters, setShowFilters] = useState<boolean>(false);
  const [filterOptions, setFilterOptions] = useState<ProductFilterOptions>({
    searchQuery: '',
    hasSalesHistory: false,
    hasFleetData: false,
    isSlowMoving: false
  });

  // State for analysis results
  const [integratedData, setIntegratedData] = useState<IntegratedProductData | null>(null);
  const [analysisResult, setAnalysisResult] = useState<ProductAnalysisResult | null>(null);

  // State for UI sections
  const [expandedSections, setExpandedSections] = useState<{
    salesHistory: boolean;
    marketAnalysis: boolean;
    inventoryRecommendations: boolean;
    salesStrategy: boolean;
    charts: boolean;
  }>({
    salesHistory: true,
    marketAnalysis: true,
    inventoryRecommendations: true,
    salesStrategy: true,
    charts: true
  });

  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>('');
  const [isFiltering, setIsFiltering] = useState<boolean>(false);

  const [fleetData, setFleetData] = useState<FleetData[]>([]);
  const [topFleetCustomers, setTopFleetCustomers] = useState<any[]>([]);

  const [salesRevenueData, setSalesRevenueData] = useState<SalesRevenueItem[]>([]);
  const [salesSummary, setSalesSummary] = useState<{
    totalSales: number;
    totalRevenue: number;
    salesTrend: string;
    topCustomers: { customer: string; qty: number; revenue: number }[];
  }>({ totalSales: 0, totalRevenue: 0, salesTrend: '-', topCustomers: [] });

  // Load products on component mount
  useEffect(() => {
    loadProducts();
  }, []);

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Filter products based on search query and filter options
  const filterProducts = useCallback(async () => {
    try {
      let filtered = products;

      // Apply text search filter
      if (debouncedSearchQuery.trim()) {
        const query = debouncedSearchQuery.toLowerCase().trim();
        filtered = filtered.filter(product => {
          try {
            const materialDesc = typeof product.materialDescription === 'string' ? product.materialDescription : '';
            const oldMaterialNo = typeof product.oldMaterialNo === 'string' ? product.oldMaterialNo : '';
            const desc = typeof product.description === 'string' ? product.description : '';
            return (
              materialDesc.toLowerCase().includes(query) ||
              oldMaterialNo.toLowerCase().includes(query) ||
              desc.toLowerCase().includes(query)
            );
          } catch (err) {
            console.error('Error saat filter produk:', product, err);
            return false;
          }
        });
      }

      // Apply additional filters
      if (filterOptions.hasSalesHistory) {
        filtered = filtered.filter(product => product.hasSalesHistory === true);
      }

      if (filterOptions.hasFleetData) {
        filtered = filtered.filter(product => product.hasFleetData === true);
      }

      if (filterOptions.isSlowMoving) {
        filtered = filtered.filter(product => product.slowMoving === true);
      }

      setFilteredProducts(filtered);
      setError(null); // reset error jika sukses
    } catch (error) {
      console.error('Error filtering products:', error);
      setError('Gagal memfilter produk. Silakan coba lagi.');
    }
  }, [products, debouncedSearchQuery, filterOptions]);

  // Update filtered products when dependencies change
  useEffect(() => {
    filterProducts();
  }, [filterProducts]);

  // Load products from service
  const loadProducts = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const loadedProducts = await fetchProducts();
      // Normalisasi data produk agar field pencarian selalu string
      const normalized = loadedProducts.map(product => ({
        ...product,
        materialDescription: product.materialDescription || '',
        oldMaterialNo: product.oldMaterialNo || '',
        description: product.description || '',
      }));
      setProducts(normalized);

      setIsLoading(false);
    } catch (error) {
      console.error('Error loading products:', error);
      setError('Gagal memuat data produk. Silakan coba lagi.');
      setIsLoading(false);
    }
  };

  // Handle filter changes
  const handleFilterChange = async (name: string, value: boolean | string | number) => {
    try {
      setIsFiltering(true);
      setFilterOptions(prev => ({
        ...prev,
        [name]: value
      }));
    } finally {
      setIsFiltering(false);
    }
  };

  // Toggle expanded sections
  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Handle product selection
  const handleProductSelect = async (productId: string) => {
    try {
      setSelectedProduct(productId);
      setIsAnalyzing(true);
      setError(null);

      // Get integrated data for the selected product
      const data = await getIntegratedProductData(productId);

      if (!data) {
        throw new Error('Tidak dapat memuat data terintegrasi untuk produk ini.');
      }

      setIntegratedData(data);

      // Analyze the product data
      const analysis = await analyzeProductData(data);
      setAnalysisResult(analysis);

      setIsAnalyzing(false);
    } catch (error) {
      console.error('Error analyzing product:', error);
      setError('Gagal menganalisis produk. Silakan coba lagi.');
      setIsAnalyzing(false);
    }
  };

  // Export analysis to Excel
  const exportAnalysis = () => {
    if (!analysisResult) return;

    try {
      // Create workbook
      const XLSX = require('xlsx');
      const workbook = XLSX.utils.book_new();

      // Create overview sheet
      const overviewData = [
        ['Analisis Produk', ''],
        ['Produk', analysisResult.productName],
        ['Tanggal Analisis', new Date().toLocaleDateString('id-ID')],
        ['', ''],
        ['Ringkasan Analisis', ''],
        ['Total Penjualan', analysisResult.salesHistory.totalSales],
        ['Total Revenue', analysisResult.salesHistory.totalRevenue],
        ['Tren Penjualan', analysisResult.salesHistory.salesTrend],
        ['Pelanggan Potensial', analysisResult.marketAnalysis.totalPotentialCustomers],
        ['Unit Potensial', analysisResult.marketAnalysis.totalPotentialUnits],
        ['Posisi Kompetitif', analysisResult.marketAnalysis.competitivePosition],
        ['Stok Rekomendasi', analysisResult.inventoryRecommendations.recommendedStockLevel],
        ['Safety Stock', analysisResult.inventoryRecommendations.safetyStockLevel],
        ['Frekuensi Restock', analysisResult.inventoryRecommendations.restockFrequency],
        ['', ''],
        ['Analisis Lengkap', ''],
        ['', analysisResult.analysisText]
      ];

      const overviewSheet = XLSX.utils.aoa_to_sheet(overviewData);
      XLSX.utils.book_append_sheet(workbook, overviewSheet, 'Ringkasan');

      // Create top customers sheet
      if (analysisResult.salesHistory.topCustomers.length > 0) {
        const customersData = [
          ['Pelanggan', 'Jumlah', 'Revenue']
        ];

        analysisResult.salesHistory.topCustomers.forEach(customer => {
          customersData.push([
            customer.customerName,
            customer.quantity,
            customer.revenue
          ]);
        });

        const customersSheet = XLSX.utils.aoa_to_sheet(customersData);
        XLSX.utils.book_append_sheet(workbook, customersSheet, 'Pelanggan Teratas');
      }

      // Create market segments sheet
      if (analysisResult.marketAnalysis.marketSegments.length > 0) {
        const segmentsData = [
          ['Segmen', 'Pelanggan Potensial', 'Revenue Potensial']
        ];

        analysisResult.marketAnalysis.marketSegments.forEach(segment => {
          segmentsData.push([
            segment.segment,
            segment.potentialCustomers,
            segment.potentialRevenue
          ]);
        });

        const segmentsSheet = XLSX.utils.aoa_to_sheet(segmentsData);
        XLSX.utils.book_append_sheet(workbook, segmentsSheet, 'Segmen Pasar');
      }

      // Create bundling opportunities sheet
      if (analysisResult.salesStrategyRecommendations.bundlingOpportunities.length > 0) {
        const bundlingData = [
          ['Produk Komplementer', 'Alasan', 'Revenue Potensial']
        ];

        analysisResult.salesStrategyRecommendations.bundlingOpportunities.forEach(opportunity => {
          bundlingData.push([
            opportunity.complementaryProduct,
            opportunity.reason,
            opportunity.potentialRevenue
          ]);
        });

        const bundlingSheet = XLSX.utils.aoa_to_sheet(bundlingData);
        XLSX.utils.book_append_sheet(workbook, bundlingSheet, 'Peluang Bundling');
      }

      // Generate Excel file
      XLSX.writeFile(workbook, `Analisis_Produk_${analysisResult.productName.replace(/[^a-zA-Z0-9]/g, '_')}.xlsx`);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      alert('Gagal mengekspor ke Excel. Silakan coba lagi.');
    }
  };

  // Load fleet data saat mount
  useEffect(() => {
    fetchFleetData().then(setFleetData).catch(() => setFleetData([]));
  }, []);

  // Fuzzy match function
  function fuzzyMatchTyreSize(a: string, b: string) {
    if (!a || !b) return false;
    // Hilangkan spasi, titik, strip, dan huruf besar
    const norm = (s: string) => s.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
    return norm(a).includes(norm(b)) || norm(b).includes(norm(a));
  }

  // Update Top 10 Customer Fleet setiap kali produk dipilih
  useEffect(() => {
    if (!selectedProduct || !products.length || !fleetData.length) {
      setTopFleetCustomers([]);
      return;
    }
    const selected = products.find(p => p.id === selectedProduct);
    if (!selected) {
      setTopFleetCustomers([]);
      return;
    }
    // Ambil tyre size dari produk (gunakan materialDescription atau description)
    const tyreKeywords = [selected.materialDescription, selected.description, selected.oldMaterialNo]
      .filter(Boolean)
      .map(s => String(s));
    // Filter fleet data dengan fuzzy match
    const matched = fleetData.filter(item =>
      tyreKeywords.some(keyword => fuzzyMatchTyreSize(item.tire_size, keyword))
    );
    // Group by customer + tyre size
    const group: { [key: string]: { customer: string, tyre_size: string, totalUnit: number } } = {};
    matched.forEach(item => {
      const key = `${item.customer || '-'}|${item.tire_size || '-'}`;
      if (!group[key]) {
        group[key] = {
          customer: item.customer || '-',
          tyre_size: item.tire_size || '-',
          totalUnit: 0
        };
      }
      group[key].totalUnit += parseInt(item.unit_qty || '0', 10) || 0;
    });
    // Ambil Top 10 berdasarkan totalUnit
    const top = Object.values(group)
      .sort((a, b) => b.totalUnit - a.totalUnit)
      .slice(0, 10);
    setTopFleetCustomers(top);
  }, [selectedProduct, products, fleetData]);

  // Load sales revenue data saat mount
  useEffect(() => {
    loadSalesRevenueData().then(setSalesRevenueData).catch(() => setSalesRevenueData([]));
  }, []);

  // Update sales summary setiap kali produk dipilih
  useEffect(() => {
    if (!selectedProduct || !products.length || !salesRevenueData.length) {
      setSalesSummary({ totalSales: 0, totalRevenue: 0, salesTrend: '-', topCustomers: [] });
      return;
    }
    const selected = products.find(p => p.id === selectedProduct);
    if (!selected) {
      setSalesSummary({ totalSales: 0, totalRevenue: 0, salesTrend: '-', topCustomers: [] });
      return;
    }
    // Ambil keyword pencocokan
    const keywords = [selected.materialDescription, selected.description, selected.oldMaterialNo]
      .filter(Boolean)
      .map(s => String(s));
    // Fuzzy match sales revenue
    const matched = salesRevenueData.filter(item =>
      keywords.some(keyword => fuzzyMatchTyreSize(item.materialDescription, keyword))
    );
    // Hitung total sales dan revenue
    const totalSales = matched.reduce((sum, item) => sum + (item.qty || 0), 0);
    const totalRevenue = matched.reduce((sum, item) => sum + (item.revenueInDocCurr || 0), 0);
    // Hitung tren penjualan (bandingkan revenue 3 bulan terakhir)
    let salesTrend = '-';
    if (matched.length > 2) {
      const sorted = [...matched].sort((a, b) => new Date(a.billingDate).getTime() - new Date(b.billingDate).getTime());
      const n = sorted.length;
      const rev1 = sorted[n - 3]?.revenueInDocCurr || 0;
      const rev2 = sorted[n - 2]?.revenueInDocCurr || 0;
      const rev3 = sorted[n - 1]?.revenueInDocCurr || 0;
      if (rev3 > rev2 && rev2 > rev1) salesTrend = 'Meningkat';
      else if (rev3 < rev2 && rev2 < rev1) salesTrend = 'Menurun';
      else salesTrend = 'Stabil';
    }
    // Top customers
    const customerMap: { [key: string]: { customer: string; qty: number; revenue: number } } = {};
    matched.forEach(item => {
      const key = item.customerName || '-';
      if (!customerMap[key]) customerMap[key] = { customer: key, qty: 0, revenue: 0 };
      customerMap[key].qty += item.qty || 0;
      customerMap[key].revenue += item.revenueInDocCurr || 0;
    });
    const topCustomers = Object.values(customerMap)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);
    setSalesSummary({ totalSales, totalRevenue, salesTrend, topCustomers });
  }, [selectedProduct, products, salesRevenueData]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Analisis Produk</h1>
      </div>

      <p className="text-gray-600">
        Analisis produk menggunakan AI untuk mengidentifikasi potensi penjualan, strategi pemasaran, dan rekomendasi inventaris.
      </p>

      {/* Main content area with sidebar layout */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Sidebar for product selection */}
        <div className="md:col-span-1 space-y-4">
          <div className="bg-white rounded-lg shadow p-4">
            <h2 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
              <Package className="mr-2 text-blue-600" size={20} />
              Pilih Produk
            </h2>

            {/* Search input */}
            <div className="relative mb-4">
              <input
                type="text"
                placeholder="Cari produk..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-10"
              />
              <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
            </div>

            {/* Filter toggle button */}
            <div className="mb-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <div className="flex items-center">
                  <Filter size={16} className="mr-2 text-gray-500" />
                  <span>Filter Lanjutan</span>
                </div>
                {showFilters ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
              </button>
            </div>

            {/* Advanced filters */}
            {showFilters && (
              <div className="mb-4 p-3 border border-gray-200 rounded-md bg-gray-50">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Filter Produk</h3>

                <div className="space-y-3">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="hasSalesHistory"
                      checked={filterOptions.hasSalesHistory}
                      onChange={(e) => handleFilterChange('hasSalesHistory', e.target.checked)}
                      disabled={isFiltering}
                      className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500 disabled:opacity-50"
                    />
                    <label htmlFor="hasSalesHistory" className="ml-2 text-sm text-gray-700">
                      Memiliki Riwayat Penjualan
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="hasFleetData"
                      checked={filterOptions.hasFleetData}
                      onChange={(e) => handleFilterChange('hasFleetData', e.target.checked)}
                      disabled={isFiltering}
                      className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500 disabled:opacity-50"
                    />
                    <label htmlFor="hasFleetData" className="ml-2 text-sm text-gray-700">
                      Digunakan di Armada
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isSlowMoving"
                      checked={filterOptions.isSlowMoving}
                      onChange={(e) => handleFilterChange('isSlowMoving', e.target.checked)}
                      disabled={isFiltering}
                      className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500 disabled:opacity-50"
                    />
                    <label htmlFor="isSlowMoving" className="ml-2 text-sm text-gray-700">
                      Produk Slow Moving
                    </label>
                  </div>
                </div>

                {isFiltering && (
                  <div className="mt-3 flex items-center text-sm text-gray-500">
                    <Loader2 className="animate-spin mr-2" size={16} />
                    Memperbarui filter...
                  </div>
                )}
              </div>
            )}

            {/* Product list */}
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="animate-spin text-blue-600 mr-2" size={24} />
                <span>Memuat produk...</span>
              </div>
            ) : (
              <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-md">
                {filteredProducts.length > 0 ? (
                  <ul className="divide-y divide-gray-200">
                    {filteredProducts.map((product) => (
                      <li key={product.id}>
                        <button
                          className={`w-full text-left px-4 py-3 hover:bg-gray-50 ${
                            selectedProduct === product.id ? 'bg-blue-50' : ''
                          }`}
                          onClick={() => handleProductSelect(product.id)}
                        >
                          <div className="font-medium text-gray-900 truncate">
                            {product.materialDescription}
                          </div>
                          <div className="text-sm text-gray-500 truncate">
                            {product.oldMaterialNo}
                          </div>
                        </button>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    {searchQuery ? 'Tidak ada produk yang cocok dengan pencarian' : 'Tidak ada data produk'}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Main content area */}
        <div className="md:col-span-3">
          {/* Loading state */}
          {isAnalyzing && (
            <div className="bg-white rounded-lg shadow p-6 flex flex-col items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700 mb-4"></div>
              <p className="text-gray-600">Menganalisis produk dengan AI...</p>
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="font-medium text-red-800">Error</h3>
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {/* Placeholder for analysis results */}
          {!isAnalyzing && !error && !analysisResult && (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <Package className="mx-auto text-gray-400 mb-4" size={48} />
              <h3 className="text-lg font-medium text-gray-800 mb-2">Pilih Produk untuk Analisis</h3>
              <p className="text-gray-600">
                Pilih produk dari daftar di sebelah kiri untuk melihat analisis mendalam tentang potensi penjualan,
                strategi pemasaran, dan rekomendasi inventaris.
              </p>
            </div>
          )}

          {/* Analysis results will be displayed here */}
          {analysisResult && !isAnalyzing && (
            <div className="space-y-6">
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <div className="bg-blue-600 px-6 py-4">
                  <h2 className="text-xl font-bold text-white">
                    Analisis Produk: {analysisResult.productName}
                  </h2>
                </div>

                <div className="p-6">
                  <div className="prose max-w-none">
                    <p>{analysisResult.analysisText}</p>
                  </div>

                  <div className="mt-4 flex justify-end">
                    <button
                      className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      onClick={exportAnalysis}
                    >
                      <Download size={16} className="mr-2" />
                      Ekspor Analisis
                    </button>
                  </div>
                </div>
              </div>

              {/* Sales History Section */}
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <div
                  className="px-6 py-4 bg-gray-50 flex justify-between items-center cursor-pointer"
                  onClick={() => toggleSection('salesHistory')}
                >
                  <div className="flex items-center">
                    <ShoppingBag size={20} className="text-blue-600 mr-3" />
                    <h3 className="text-lg font-medium text-gray-800">Riwayat Penjualan</h3>
                  </div>
                  {expandedSections.salesHistory ? (
                    <ChevronUp size={20} className="text-gray-500" />
                  ) : (
                    <ChevronDown size={20} className="text-gray-500" />
                  )}
                </div>

                {expandedSections.salesHistory && (
                  <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-sm text-blue-600 font-medium">Total Penjualan</p>
                        <p className="text-2xl font-bold text-blue-800">{salesSummary.totalSales}</p>
                      </div>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <p className="text-sm text-green-600 font-medium">Total Revenue</p>
                        <p className="text-2xl font-bold text-green-800">
                          {new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(salesSummary.totalRevenue)}
                        </p>
                      </div>
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <p className="text-sm text-purple-600 font-medium">Tren Penjualan</p>
                        <p className="text-2xl font-bold text-purple-800 capitalize">{salesSummary.salesTrend}</p>
                      </div>
                    </div>
                    <div className="mt-6">
                      <h4 className="text-md font-medium text-gray-700 mb-3">Pelanggan Teratas</h4>
                      {salesSummary.topCustomers.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pelanggan</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jumlah</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {salesSummary.topCustomers.map((item, idx) => (
                                <tr key={idx}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.customer}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.qty}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(item.revenue)}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">Tidak ada data pelanggan teratas</p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Market Analysis Section */}
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <div
                  className="px-6 py-4 bg-gray-50 flex justify-between items-center cursor-pointer"
                  onClick={() => toggleSection('marketAnalysis')}
                >
                  <div className="flex items-center">
                    <Users size={20} className="text-blue-600 mr-3" />
                    <h3 className="text-lg font-medium text-gray-800">Analisis Pasar</h3>
                  </div>
                  {expandedSections.marketAnalysis ? (
                    <ChevronUp size={20} className="text-gray-500" />
                  ) : (
                    <ChevronDown size={20} className="text-gray-500" />
                  )}
                </div>

                {expandedSections.marketAnalysis && (
                  <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-sm text-blue-600 font-medium">Pelanggan Potensial</p>
                        <p className="text-2xl font-bold text-blue-800">
                          {analysisResult.marketAnalysis.totalPotentialCustomers}
                        </p>
                      </div>

                      <div className="bg-green-50 p-4 rounded-lg">
                        <p className="text-sm text-green-600 font-medium">Unit Potensial</p>
                        <p className="text-2xl font-bold text-green-800">
                          {analysisResult.marketAnalysis.totalPotentialUnits}
                        </p>
                      </div>

                      <div className="bg-purple-50 p-4 rounded-lg">
                        <p className="text-sm font-medium text-purple-600">Posisi Kompetitif</p>
                        <p className="text-2xl font-bold text-purple-800 capitalize">
                          {analysisResult.marketAnalysis.competitivePosition === 'strong' ? 'Kuat' :
                            analysisResult.marketAnalysis.competitivePosition === 'moderate' ? 'Sedang' : 'Lemah'}
                        </p>
                      </div>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-md font-medium text-gray-700 mb-3">Top 10 Customer dengan Ukuran Ban Serupa (Fleet Data)</h4>
                      {topFleetCustomers.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">No</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jumlah Unit</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ukuran Ban</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {topFleetCustomers.map((item, idx) => (
                                <tr key={idx}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{idx + 1}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.customer}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.totalUnit}</td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.tyre_size}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">Tidak ada data customer dengan ukuran ban serupa di fleet</p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Inventory Recommendations Section */}
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <div
                  className="px-6 py-4 bg-gray-50 flex justify-between items-center cursor-pointer"
                  onClick={() => toggleSection('inventoryRecommendations')}
                >
                  <div className="flex items-center">
                    <Package size={20} className="text-blue-600 mr-3" />
                    <h3 className="text-lg font-medium text-gray-800">Rekomendasi Inventaris</h3>
                  </div>
                  {expandedSections.inventoryRecommendations ? (
                    <ChevronUp size={20} className="text-gray-500" />
                  ) : (
                    <ChevronDown size={20} className="text-gray-500" />
                  )}
                </div>

                {expandedSections.inventoryRecommendations && (
                  <div className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-sm text-blue-600 font-medium">Stok yang Direkomendasikan</p>
                        <p className="text-2xl font-bold text-blue-800">
                          {analysisResult.inventoryRecommendations.recommendedStockLevel}
                        </p>
                      </div>

                      <div className="bg-green-50 p-4 rounded-lg">
                        <p className="text-sm text-green-600 font-medium">Safety Stock</p>
                        <p className="text-2xl font-bold text-green-800">
                          {analysisResult.inventoryRecommendations.safetyStockLevel}
                        </p>
                      </div>

                      <div className="bg-purple-50 p-4 rounded-lg">
                        <p className="text-sm text-purple-600 font-medium">Frekuensi Restock</p>
                        <p className="text-2xl font-bold text-purple-800">
                          {analysisResult.inventoryRecommendations.restockFrequency}
                        </p>
                      </div>
                    </div>

                    <div className="mt-6">
                      <h4 className="text-md font-medium text-gray-700 mb-3">Penyesuaian Musiman</h4>
                      {analysisResult.inventoryRecommendations.seasonalAdjustments.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Musim
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Faktor Penyesuaian
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {analysisResult.inventoryRecommendations.seasonalAdjustments.map((adjustment, index) => (
                                <tr key={index}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {adjustment.season}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {adjustment.adjustmentFactor}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">Tidak ada data penyesuaian musiman</p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Sales Strategy Section */}
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <div
                  className="px-6 py-4 bg-gray-50 flex justify-between items-center cursor-pointer"
                  onClick={() => toggleSection('salesStrategy')}
                >
                  <div className="flex items-center">
                    <Truck size={20} className="text-blue-600 mr-3" />
                    <h3 className="text-lg font-medium text-gray-800">Strategi Penjualan</h3>
                  </div>
                  {expandedSections.salesStrategy ? (
                    <ChevronUp size={20} className="text-gray-500" />
                  ) : (
                    <ChevronDown size={20} className="text-gray-500" />
                  )}
                </div>

                {expandedSections.salesStrategy && (
                  <div className="p-6">
                    <div className="mb-6">
                      <h4 className="text-md font-medium text-gray-700 mb-2">Pendekatan yang Direkomendasikan</h4>
                      <p className="text-gray-800 p-3 bg-blue-50 rounded-md">
                        {analysisResult.salesStrategyRecommendations.recommendedApproach}
                      </p>
                    </div>

                    <div className="mb-6">
                      <h4 className="text-md font-medium text-gray-700 mb-2">Strategi Penetapan Harga</h4>
                      <p className="text-gray-800 p-3 bg-green-50 rounded-md">
                        {analysisResult.salesStrategyRecommendations.pricingStrategy}
                      </p>
                    </div>

                    <div className="mb-6">
                      <h4 className="text-md font-medium text-gray-700 mb-3">Pelanggan Target</h4>
                      {analysisResult.salesStrategyRecommendations.targetCustomers.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {analysisResult.salesStrategyRecommendations.targetCustomers.map((customer, index) => (
                            <div key={index} className="bg-gray-100 p-2 rounded-md text-gray-800">
                              {customer}
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">Tidak ada data pelanggan target</p>
                      )}
                    </div>

                    <div className="mt-6">
                      <h4 className="text-md font-medium text-gray-700 mb-3">Peluang Bundling</h4>
                      {analysisResult.salesStrategyRecommendations.bundlingOpportunities.length > 0 ? (
                        <div className="space-y-4">
                          {analysisResult.salesStrategyRecommendations.bundlingOpportunities.map((opportunity, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex justify-between items-start">
                                <h5 className="text-md font-medium text-gray-800">{opportunity.complementaryProduct}</h5>
                                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                  {new Intl.NumberFormat('id-ID', {
                                    style: 'currency',
                                    currency: 'IDR',
                                    minimumFractionDigits: 0
                                  }).format(opportunity.potentialRevenue)}
                                </span>
                              </div>
                              <p className="mt-2 text-sm text-gray-600">{opportunity.reason}</p>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">Tidak ada data peluang bundling</p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Charts Section */}
              <div className="bg-white rounded-lg shadow overflow-hidden">
                <div
                  className="px-6 py-4 bg-gray-50 flex justify-between items-center cursor-pointer"
                  onClick={() => toggleSection('charts')}
                >
                  <div className="flex items-center">
                    <BarChart3 size={20} className="text-blue-600 mr-3" />
                    <h3 className="text-lg font-medium text-gray-800">Visualisasi Data</h3>
                  </div>
                  {expandedSections.charts ? (
                    <ChevronUp size={20} className="text-gray-500" />
                  ) : (
                    <ChevronDown size={20} className="text-gray-500" />
                  )}
                </div>

                {expandedSections.charts && (
                  <div className="p-6">
                    <ProductAnalysisCharts analysisResult={analysisResult} />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductAnalysisPage;
