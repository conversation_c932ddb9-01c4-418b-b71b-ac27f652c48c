<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Data Harga Batu Bara</h1>
                    <p class="mt-2 text-gray-600">Monitoring dan analisis harga batu bara untuk perencanaan bisnis</p>
                </div>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                        <Download class="h-4 w-4 mr-2" />
                        Export Data
                    </button>
                    <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center">
                        <Plus class="h-4 w-4 mr-2" />
                        Tambah Data
                    </button>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <BarChart3 class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Harga Saat Ini</p>
                            <p class="text-2xl font-bold text-gray-900">$85.50</p>
                            <p class="text-sm text-green-600">+2.5% dari bulan lalu</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <TrendingUp class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Trend Bulanan</p>
                            <p class="text-2xl font-bold text-gray-900">+5.2%</p>
                            <p class="text-sm text-green-600">Naik</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Calendar class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Data Points</p>
                            <p class="text-2xl font-bold text-gray-900">365</p>
                            <p class="text-sm text-gray-600">Hari terakhir</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <AlertTriangle class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Volatilitas</p>
                            <p class="text-2xl font-bold text-gray-900">Medium</p>
                            <p class="text-sm text-orange-600">Perlu monitoring</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chart Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Trend Harga Batu Bara (12 Bulan Terakhir)</h3>
                <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                    <div class="text-center">
                        <BarChart3 class="h-12 w-12 text-gray-400 mx-auto mb-2" />
                        <p class="text-gray-600">Chart akan ditampilkan di sini</p>
                        <p class="text-sm text-gray-500">Integrasi dengan Chart.js sedang dalam pengembangan</p>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Data Harga Terbaru</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanggal</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Harga (USD/ton)</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Perubahan</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Volume</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sumber</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="i in 10" :key="i">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ new Date(Date.now() - i * 24 * 60 * 60 * 1000).toLocaleDateString('id-ID') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    ${{ (85.50 + Math.random() * 10 - 5).toFixed(2) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <span :class="Math.random() > 0.5 ? 'text-green-600' : 'text-red-600'">
                                        {{ Math.random() > 0.5 ? '+' : '-' }}{{ (Math.random() * 3).toFixed(2) }}%
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ Math.floor(Math.random() * 1000 + 500) }}K ton
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    Bloomberg
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-blue-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-blue-900">Fitur Dalam Pengembangan</h3>
                        <p class="text-blue-700 mt-1">
                            Halaman ini sedang dalam tahap pengembangan. Fitur yang akan tersedia:
                        </p>
                        <ul class="list-disc list-inside text-blue-700 mt-2 space-y-1">
                            <li>Real-time data integration dengan API eksternal</li>
                            <li>Advanced charting dengan Chart.js</li>
                            <li>Prediksi harga menggunakan AI</li>
                            <li>Alert system untuk perubahan harga signifikan</li>
                            <li>Export data ke berbagai format</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { 
    Download, 
    Plus, 
    BarChart3, 
    TrendingUp, 
    Calendar, 
    AlertTriangle,
    Info
} from 'lucide-vue-next';
</script>
