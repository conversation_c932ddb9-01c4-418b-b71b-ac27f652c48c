/**
 * Service for analyzing product data using AI
 * Integrates data from Product Management, Fleet Management, and Sales Revenue
 */

import { Product } from '../types';
import { FleetlistItem } from './fleetlistService';
import { SalesRevenueItem } from './salesRevenue2025Service';
import {
  ProductAnalysisResult,
  ProductAnalysisRequest,
  IntegratedProductData
} from '../types/productAnalysis';
import { fetchProducts } from './productService';
import { fetchFleetlist } from './fleetlistService';
import { loadSalesRevenueData } from './salesRevenue2025Service';

// OpenRouter API configuration
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const OPENROUTER_API_KEY = '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';
const MODEL = 'openai/gpt-4.1-nano';

/**
 * Get integrated product data from all sources
 */
export async function getIntegratedProductData(
  productId: string
): Promise<IntegratedProductData | null> {
  try {
    // Load data from all sources
    const products = await fetchProducts();
    const fleetData = await fetchFleetlist();
    const salesData = await loadSalesRevenueData();

    // Find the product
    const product = products.find(p => p.id === productId);
    if (!product) {
      console.log(`Product not found with ID: ${productId}`);
      return null;
    }

    console.log(`Found product: ${product.materialDescription}`);

    // Find fleet data related to this product
    // Match by tire size in the material description
    const productFleetData = fleetData.filter(item => {
      if (!item.tire_size) return false;

      // Normalize the tire size for comparison
      const fleetTireSize = item.tire_size.replace(/\s+/g, '').toUpperCase();
      const productDesc = product.materialDescription.replace(/\s+/g, '').toUpperCase();

      return productDesc.includes(fleetTireSize) || fleetTireSize.includes(productDesc);
    });

    console.log(`Found ${productFleetData.length} fleet records related to product`);

    // Find sales data related to this product
    const productSalesData = salesData.filter(item => {
      const salesDesc = item.materialDescription?.replace(/\s+/g, '').toUpperCase() || '';
      const productDesc = product.materialDescription.replace(/\s+/g, '').toUpperCase();

      return salesDesc.includes(productDesc) || productDesc.includes(salesDesc);
    });

    console.log(`Found ${productSalesData.length} sales records related to product`);

    // Find potential customers based on fleet data
    const potentialCustomers = Array.from(new Set(
      productFleetData.map(item => item.customer).filter(Boolean) as string[]
    ));

    // Extract tire size from product description
    const extractTireSize = (description: string): string | null => {
      // Common tire size patterns like 23.5R25, 27.00R49, 18.00-25, etc.
      const tireSizePattern = /\b\d+(\.\d+)?[R-]\d+\b/i;
      const match = description.match(tireSizePattern);
      return match ? match[0].toUpperCase() : null;
    };

    // Get product tire size
    const productTireSize = extractTireSize(product.materialDescription);
    console.log(`Extracted tire size from product: ${productTireSize}`);

    // Find customers with similar tire sizes
    const customerTireSizeMap = new Map<string, { unitQty: number, tireSize: string }>();

    // Group fleet data by customer and tire size
    fleetData.forEach(item => {
      if (item.customer && item.tire_size && item.unit_qty) {
        const customer = item.customer;
        const tireSize = item.tire_size.toUpperCase();
        const unitQty = parseInt(item.unit_qty) || 0;

        // Check if this tire size is similar to our product's tire size
        // If product tire size couldn't be extracted, include all customers
        const isSimilarTireSize = !productTireSize ||
                                 tireSize.includes(productTireSize) ||
                                 (productTireSize && productTireSize.includes(tireSize));

        if (isSimilarTireSize) {
          if (customerTireSizeMap.has(customer)) {
            const current = customerTireSizeMap.get(customer)!;
            customerTireSizeMap.set(customer, {
              unitQty: current.unitQty + unitQty,
              tireSize: current.tireSize
            });
          } else {
            customerTireSizeMap.set(customer, {
              unitQty: unitQty,
              tireSize: tireSize
            });
          }
        }
      }
    });

    // Convert to array and sort by unit quantity (descending)
    const topCustomersWithSimilarTireSize = Array.from(customerTireSizeMap.entries())
      .sort((a, b) => b[1].unitQty - a[1].unitQty)
      .slice(0, 10)
      .map(([customer, data]) => ({
        customer,
        unitQty: data.unitQty,
        tireSize: data.tireSize
      }));

    console.log(`Found ${potentialCustomers.length} potential customers for product`);
    console.log(`Identified ${topCustomersWithSimilarTireSize.length} customers with similar tire size`);

    return {
      productId,
      product,
      fleetData: productFleetData,
      salesData: productSalesData,
      potentialCustomers,
      topCustomersWithSimilarTireSize
    };
  } catch (error) {
    console.error('Error getting integrated product data:', error);
    return null;
  }
}

/**
 * Analyze product data using AI
 */
export async function analyzeProductData(
  productData: IntegratedProductData
): Promise<ProductAnalysisResult> {
  try {
    console.log('Analyzing product data with AI:', productData.product.materialDescription);

    // Prepare the request for AI analysis
    const request: ProductAnalysisRequest = {
      productName: productData.product.materialDescription,
      productDetails: productData.product,
      fleetData: productData.fleetData,
      salesData: productData.salesData,
      potentialCustomers: productData.potentialCustomers
    };

    // Create system prompt for AI
    const systemPrompt = `Anda adalah asisten analisis produk yang ahli untuk perusahaan ban. Tugas Anda adalah menganalisis data produk dari berbagai sumber dan memberikan wawasan yang berharga tentang riwayat penjualan, potensi pasar, rekomendasi inventaris, dan strategi penjualan.

Berikan analisis dalam format JSON dengan struktur berikut:
1. Analisis riwayat penjualan (kepada siapa produk dijual, kapan, jumlah)
2. Analisis pasar (pelanggan potensial berdasarkan data armada)
3. Rekomendasi inventaris (tingkat stok yang direkomendasikan, frekuensi pengisian ulang)
4. Rekomendasi strategi penjualan (pendekatan yang direkomendasikan, pelanggan target)
5. Teks analisis keseluruhan

Analisis Anda harus mencakup:
- Tren penjualan historis (meningkat, menurun, atau stabil)
- Segmentasi pasar berdasarkan data armada
- Rekomendasi tingkat stok berdasarkan pola penjualan
- Penyesuaian musiman untuk tingkat inventaris
- Peluang bundling dengan produk lain
- Strategi penetapan harga berdasarkan posisi kompetitif
- Pelanggan target untuk upaya penjualan

Berikan analisis yang mendalam dan wawasan yang dapat ditindaklanjuti. Gunakan data yang tersedia untuk membuat rekomendasi yang spesifik dan relevan. Pastikan analisis Anda komprehensif dan bermanfaat untuk tim penjualan dan manajemen inventaris.`;

    // Create user prompt with product data
    const userPrompt = `Analisis data produk berikut dan berikan wawasan yang komprehensif:

DETAIL PRODUK:
${JSON.stringify(productData.product, null, 2)}

DATA ARMADA TERKAIT:
${JSON.stringify(productData.fleetData, null, 2)}

DATA PENJUALAN:
${JSON.stringify(productData.salesData, null, 2)}

PELANGGAN POTENSIAL:
${JSON.stringify(productData.potentialCustomers, null, 2)}

TOP 10 CUSTOMER DENGAN UKURAN BAN SERUPA:
${JSON.stringify(productData.topCustomersWithSimilarTireSize, null, 2)}

Berikan analisis dalam format JSON dengan struktur berikut:
{
  "salesHistory": {
    "totalSales": number,
    "totalRevenue": number,
    "firstSaleDate": "string",
    "lastSaleDate": "string",
    "topCustomers": [
      {
        "customerName": "string",
        "quantity": number,
        "revenue": number
      }
    ],
    "salesTrend": "increasing" | "decreasing" | "stable"
  },
  "marketAnalysis": {
    "totalPotentialCustomers": number,
    "totalPotentialUnits": number,
    "marketSegments": [
      {
        "segment": "string",
        "potentialCustomers": number,
        "potentialRevenue": number
      }
    ],
    "competitivePosition": "strong" | "moderate" | "weak"
  },
  "inventoryRecommendations": {
    "recommendedStockLevel": number,
    "restockFrequency": "string",
    "safetyStockLevel": number,
    "seasonalAdjustments": [
      {
        "season": "string",
        "adjustmentFactor": number
      }
    ]
  },
  "salesStrategyRecommendations": {
    "recommendedApproach": "string",
    "targetCustomers": ["string"],
    "bundlingOpportunities": [
      {
        "complementaryProduct": "string",
        "reason": "string",
        "potentialRevenue": number
      }
    ],
    "pricingStrategy": "string"
  },
  "analysisText": "string"
}`;

    // Call OpenRouter API
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Product Analysis'
      },
      body: JSON.stringify({
        model: MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('AI analysis response:', data);

    // Extract the content from the response
    const analysisContent = data.choices[0].message.content;

    // Parse the JSON response
    const analysisResult = JSON.parse(analysisContent);

    // Return the analysis result with product ID and name
    return {
      productId: productData.productId,
      productName: productData.product.materialDescription,
      salesData: productData.salesData,
      ...analysisResult
    };
  } catch (error) {
    console.error('Error analyzing product data with AI:', error);

    // Return a fallback analysis result
    return {
      productId: productData.productId,
      productName: productData.product.materialDescription,
      salesData: productData.salesData,
      salesHistory: {
        totalSales: productData.salesData.length,
        totalRevenue: productData.salesData.reduce((sum, item) => sum + (item.revenueInDocCurr || 0), 0),
        firstSaleDate: productData.salesData.length > 0 ? productData.salesData[0].billingDate : '',
        lastSaleDate: productData.salesData.length > 0 ? productData.salesData[productData.salesData.length - 1].billingDate : '',
        topCustomers: [],
        salesTrend: 'stable'
      },
      marketAnalysis: {
        totalPotentialCustomers: productData.potentialCustomers.length,
        totalPotentialUnits: productData.fleetData.reduce((sum, item) => sum + (parseInt(item.unit_qty || '0') || 0), 0),
        marketSegments: [
          {
            segment: "Mining",
            potentialCustomers: Math.floor(productData.potentialCustomers.length * 0.4),
            potentialRevenue: 15000000000
          },
          {
            segment: "Construction",
            potentialCustomers: Math.floor(productData.potentialCustomers.length * 0.3),
            potentialRevenue: 5000000000
          },
          {
            segment: "Energy",
            potentialCustomers: Math.floor(productData.potentialCustomers.length * 0.3),
            potentialRevenue: 5000000000
          }
        ],
        competitivePosition: 'moderate'
      },
      inventoryRecommendations: {
        recommendedStockLevel: 50,
        restockFrequency: 'Monthly',
        safetyStockLevel: 10,
        seasonalAdjustments: []
      },
      salesStrategyRecommendations: {
        recommendedApproach: 'Direct sales to existing customers',
        targetCustomers: productData.potentialCustomers.slice(0, 5),
        bundlingOpportunities: [],
        pricingStrategy: 'Competitive pricing based on market demand'
      },
      analysisText: `Analisis untuk ${productData.product.materialDescription} tidak dapat dihasilkan. Silakan coba lagi nanti.`
    };
  }
}
