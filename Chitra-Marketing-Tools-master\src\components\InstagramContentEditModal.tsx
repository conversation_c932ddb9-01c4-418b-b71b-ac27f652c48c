import React, { useState, useEffect } from 'react';
import {
  SocialMediaPost,
  ContentType,
  PostStatus,
  ContentGenerationResponse
} from '../types/socialMedia';
import {
  updateSocialMediaPost,
  generateInstagramContent
} from '../services/socialMediaService';
import {
  X,
  Save,
  Instagram,
  Image as ImageIcon,
  Video,
  Layers,
  Clock,
  RefreshCw,
  Sparkles,
  Loader2
} from 'lucide-react';
import { useToast } from './ui/use-toast';

interface InstagramContentEditModalProps {
  post: SocialMediaPost;
  onClose: () => void;
  onSave: (updatedPost: SocialMediaPost) => void;
}

export default function InstagramContentEditModal({
  post,
  onClose,
  onSave
}: InstagramContentEditModalProps) {
  // State for form inputs
  const [title, setTitle] = useState(post.title || '');
  const [caption, setCaption] = useState(post.caption || '');
  const [hashtags, setHashtags] = useState(post.hashtags.join(' ') || '');
  const [contentType, setContentType] = useState(post.contentType);
  const [imageDescription, setImageDescription] = useState(post.imageDescription || '');
  const [status, setStatus] = useState(post.status);
  const [scheduledDate, setScheduledDate] = useState(
    post.scheduledDate ? new Date(post.scheduledDate).toISOString().split('T')[0] : ''
  );

  // State for regeneration
  const [showRegenerateForm, setShowRegenerateForm] = useState(false);
  const [regeneratePrompt, setRegeneratePrompt] = useState('');
  const [isRegenerating, setIsRegenerating] = useState(false);

  const { toast } = useToast();

  // Handle save
  const handleSave = async () => {
    try {
      // Format hashtags from space-separated string to array
      const hashtagsArray = hashtags
        .split(' ')
        .map(tag => tag.startsWith('#') ? tag.substring(1) : tag)
        .filter(tag => tag.trim() !== '');

      const updatedPost: SocialMediaPost = {
        ...post,
        title,
        caption,
        hashtags: hashtagsArray,
        contentType,
        imageDescription,
        status,
        scheduledDate: scheduledDate ? new Date(scheduledDate) : post.scheduledDate,
        updatedAt: new Date()
      };

      const savedPost = await updateSocialMediaPost(updatedPost);
      
      toast({
        title: "Berhasil",
        description: "Konten Instagram berhasil diperbarui",
      });
      
      onSave(savedPost);
      onClose();
    } catch (error) {
      console.error('Error saving post:', error);
      toast({
        title: "Error",
        description: "Gagal memperbarui konten Instagram",
        variant: "destructive"
      });
    }
  };

  // Handle regenerate content
  const handleRegenerateContent = async () => {
    if (!regeneratePrompt.trim()) {
      toast({
        title: "Error",
        description: "Prompt tidak boleh kosong",
        variant: "destructive"
      });
      return;
    }

    setIsRegenerating(true);
    try {
      // Call the API to regenerate content
      const generatedContent = await generateInstagramContent({
        platform: post.platform,
        contentType: post.contentType,
        productDetails: regeneratePrompt,
        language: 'id',
        includeHashtags: true,
        includeEmojis: true,
        length: 'medium',
        tone: 'professional'
      });

      // Update the form with the generated content
      setCaption(generatedContent.caption);
      setHashtags(generatedContent.hashtags.join(' '));
      if (generatedContent.suggestedImageDescription) {
        setImageDescription(generatedContent.suggestedImageDescription);
      }

      toast({
        title: "Berhasil",
        description: "Konten berhasil di-regenerate",
      });

      // Hide the regenerate form
      setShowRegenerateForm(false);
    } catch (error) {
      console.error('Error regenerating content:', error);
      toast({
        title: "Error",
        description: "Gagal me-regenerate konten",
        variant: "destructive"
      });
    } finally {
      setIsRegenerating(false);
    }
  };

  // Get content type icon
  const getContentTypeIcon = (type: ContentType) => {
    switch (type) {
      case ContentType.IMAGE:
        return <ImageIcon size={16} className="text-blue-500" />;
      case ContentType.VIDEO:
        return <Video size={16} className="text-red-500" />;
      case ContentType.CAROUSEL:
        return <Layers size={16} className="text-purple-500" />;
      case ContentType.REEL:
        return <Video size={16} className="text-pink-500" />;
      case ContentType.STORY:
        return <Clock size={16} className="text-orange-500" />;
      default:
        return <ImageIcon size={16} />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <div className="flex items-center">
            <Instagram size={20} className="text-pink-500 mr-2" />
            <h3 className="text-lg font-medium">Edit Konten Instagram</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-4 space-y-4">
          {/* Main form */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Judul</label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Judul konten (opsional)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Caption</label>
              <textarea
                value={caption}
                onChange={(e) => setCaption(e.target.value)}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Caption Instagram"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Hashtags</label>
              <textarea
                value={hashtags}
                onChange={(e) => setHashtags(e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Hashtags (pisahkan dengan spasi)"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tipe Konten</label>
                <select
                  value={contentType}
                  onChange={(e) => setContentType(e.target.value as ContentType)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={ContentType.IMAGE}>Gambar</option>
                  <option value={ContentType.VIDEO}>Video</option>
                  <option value={ContentType.CAROUSEL}>Carousel</option>
                  <option value={ContentType.REEL}>Reel</option>
                  <option value={ContentType.STORY}>Story</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={status}
                  onChange={(e) => setStatus(e.target.value as PostStatus)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={PostStatus.DRAFT}>Draft</option>
                  <option value={PostStatus.SCHEDULED}>Terjadwal</option>
                  <option value={PostStatus.PUBLISHED}>Dipublikasikan</option>
                  <option value={PostStatus.ARCHIVED}>Diarsipkan</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Deskripsi Gambar</label>
              <textarea
                value={imageDescription}
                onChange={(e) => setImageDescription(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Deskripsi gambar untuk konten ini"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tanggal Terjadwal</label>
              <input
                type="date"
                value={scheduledDate}
                onChange={(e) => setScheduledDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Regenerate content section */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            {!showRegenerateForm ? (
              <button
                onClick={() => setShowRegenerateForm(true)}
                className="flex items-center text-blue-600 hover:text-blue-800"
              >
                <RefreshCw size={16} className="mr-2" />
                Regenerate Konten
              </button>
            ) : (
              <div className="space-y-3">
                <h4 className="font-medium flex items-center">
                  <Sparkles size={16} className="text-blue-500 mr-2" />
                  Regenerate Konten dengan AI
                </h4>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Prompt untuk Regenerate Konten
                  </label>
                  <textarea
                    value={regeneratePrompt}
                    onChange={(e) => setRegeneratePrompt(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Masukkan detail produk, target audiens, tujuan kampanye, atau informasi lain untuk regenerate konten"
                  />
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={handleRegenerateContent}
                    disabled={isRegenerating}
                    className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300 flex items-center"
                  >
                    {isRegenerating ? (
                      <>
                        <Loader2 size={16} className="mr-2 animate-spin" />
                        Regenerating...
                      </>
                    ) : (
                      <>
                        <Sparkles size={16} className="mr-2" />
                        Regenerate
                      </>
                    )}
                  </button>
                  <button
                    onClick={() => setShowRegenerateForm(false)}
                    className="px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Batal
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="p-4 border-t border-gray-200 flex justify-end space-x-2">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Batal
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center"
          >
            <Save size={16} className="mr-2" />
            Simpan Perubahan
          </button>
        </div>
      </div>
    </div>
  );
}
