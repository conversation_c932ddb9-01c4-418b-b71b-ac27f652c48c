import React from 'react';
import { Link } from 'react-router-dom';

export default function TestPage() {
  return (
    <div className="p-8 bg-white rounded-lg shadow-sm border">
      <h1 className="text-2xl font-bold mb-4">Test Page</h1>
      <p className="mb-4">This is a test page to verify routing is working correctly.</p>
      
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">Navigation Links:</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li>
            <Link to="/" className="text-blue-600 hover:underline">
              Home
            </Link>
          </li>
          <li>
            <Link to="/analytics-dashboard" className="text-blue-600 hover:underline">
              Analytics Dashboard
            </Link>
          </li>
          <li>
            <Link to="/sales-dashboard" className="text-blue-600 hover:underline">
              Sales Dashboard
            </Link>
          </li>
          <li>
            <Link to="/products" className="text-blue-600 hover:underline">
              Products
            </Link>
          </li>
        </ul>
      </div>
    </div>
  );
}
