import React, { useState, useRef } from 'react';
import { SwotAnalysisRequest, ManualSwotItem } from '../types/swotAnalysis';
import { v4 as uuidv4 } from 'uuid';
import { Upload, Plus, Trash2, Info, ChevronDown, ChevronUp } from 'lucide-react';

interface SwotAnalysisFormProps {
  onSubmit: (request: SwotAnalysisRequest) => void;
  isLoading: boolean;
}

const SwotAnalysisForm: React.FC<SwotAnalysisFormProps> = ({ onSubmit, isLoading }) => {
  // Form state
  const [companyName, setCompanyName] = useState<string>('Chitra Paratama');
  const [includeProductData, setIncludeProductData] = useState<boolean>(true);
  const [includeFleetData, setIncludeFleetData] = useState<boolean>(true);
  const [includeSalesData, setIncludeSalesData] = useState<boolean>(true);
  const [includeKnowledgeBase, setIncludeKnowledgeBase] = useState<boolean>(true);
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');

  // Manual SWOT items
  const [manualStrengths, setManualStrengths] = useState<ManualSwotItem[]>([]);
  const [manualWeaknesses, setManualWeaknesses] = useState<ManualSwotItem[]>([]);
  const [manualOpportunities, setManualOpportunities] = useState<ManualSwotItem[]>([]);
  const [manualThreats, setManualThreats] = useState<ManualSwotItem[]>([]);

  // Expanded sections
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    strengths: false,
    weaknesses: false,
    opportunities: false,
    threats: false
  });

  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Add a new manual SWOT item
  const addManualItem = (type: 'strengths' | 'weaknesses' | 'opportunities' | 'threats') => {
    const newItem: ManualSwotItem = {
      id: uuidv4(),
      title: '',
      description: ''
    };

    switch (type) {
      case 'strengths':
        setManualStrengths([...manualStrengths, newItem]);
        break;
      case 'weaknesses':
        setManualWeaknesses([...manualWeaknesses, newItem]);
        break;
      case 'opportunities':
        setManualOpportunities([...manualOpportunities, newItem]);
        break;
      case 'threats':
        setManualThreats([...manualThreats, newItem]);
        break;
    }
  };

  // Update a manual SWOT item
  const updateManualItem = (
    type: 'strengths' | 'weaknesses' | 'opportunities' | 'threats',
    id: string,
    field: 'title' | 'description',
    value: string
  ) => {
    switch (type) {
      case 'strengths':
        setManualStrengths(manualStrengths.map(item =>
          item.id === id ? { ...item, [field]: value } : item
        ));
        break;
      case 'weaknesses':
        setManualWeaknesses(manualWeaknesses.map(item =>
          item.id === id ? { ...item, [field]: value } : item
        ));
        break;
      case 'opportunities':
        setManualOpportunities(manualOpportunities.map(item =>
          item.id === id ? { ...item, [field]: value } : item
        ));
        break;
      case 'threats':
        setManualThreats(manualThreats.map(item =>
          item.id === id ? { ...item, [field]: value } : item
        ));
        break;
    }
  };

  // Remove a manual SWOT item
  const removeManualItem = (
    type: 'strengths' | 'weaknesses' | 'opportunities' | 'threats',
    id: string
  ) => {
    switch (type) {
      case 'strengths':
        setManualStrengths(manualStrengths.filter(item => item.id !== id));
        break;
      case 'weaknesses':
        setManualWeaknesses(manualWeaknesses.filter(item => item.id !== id));
        break;
      case 'opportunities':
        setManualOpportunities(manualOpportunities.filter(item => item.id !== id));
        break;
      case 'threats':
        setManualThreats(manualThreats.filter(item => item.id !== id));
        break;
    }
  };

  // Render manual SWOT item form
  const renderManualItemForm = (
    type: 'strengths' | 'weaknesses' | 'opportunities' | 'threats',
    items: ManualSwotItem[],
    title: string,
    bgColor: string
  ) => {
    return (
      <div className={`${bgColor} p-4 rounded-lg`}>
        <div
          className="flex justify-between items-center cursor-pointer"
          onClick={() => toggleSection(type)}
        >
          <h3 className="text-lg font-medium">{title}</h3>
          {expandedSections[type] ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
        </div>

        {expandedSections[type] && (
          <div className="mt-3 space-y-4">
            {items.map(item => (
              <div key={item.id} className="bg-white p-3 rounded shadow-sm space-y-2">
                <div className="flex justify-between">
                  <input
                    type="text"
                    value={item.title}
                    onChange={(e) => updateManualItem(type, item.id, 'title', e.target.value)}
                    className="w-full p-2 border rounded-md"
                    placeholder="Judul"
                  />
                  <button
                    type="button"
                    onClick={() => removeManualItem(type, item.id)}
                    className="ml-2 text-red-600 hover:text-red-800"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
                <textarea
                  value={item.description}
                  onChange={(e) => updateManualItem(type, item.id, 'description', e.target.value)}
                  className="w-full p-2 border rounded-md"
                  rows={2}
                  placeholder="Deskripsi"
                />
              </div>
            ))}

            <button
              type="button"
              onClick={() => addManualItem(type)}
              className="flex items-center px-3 py-2 bg-white text-gray-700 rounded-md hover:bg-gray-100 w-full justify-center"
            >
              <Plus size={16} className="mr-2" />
              Tambah {title}
            </button>
          </div>
        )}
      </div>
    );
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const request: SwotAnalysisRequest = {
      companyName,
      includeProductData,
      includeFleetData,
      includeSalesData,
      includeKnowledgeBase,
      manualStrengths,
      manualWeaknesses,
      manualOpportunities,
      manualThreats,
      customPrompt: customPrompt.trim() || undefined,
      dateRange: (startDate && endDate) ? {
        startDate,
        endDate
      } : undefined
    };

    onSubmit(request);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h2 className="text-xl font-semibold text-gray-800 mb-4">SWOT Analysis Configuration</h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Company Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Nama Perusahaan
          </label>
          <input
            type="text"
            value={companyName}
            onChange={(e) => setCompanyName(e.target.value)}
            className="w-full p-2 border rounded-md"
            required
          />
        </div>

        {/* Manual SWOT Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Input Manual SWOT
          </label>

          <div className="space-y-3">
            {renderManualItemForm('strengths', manualStrengths, 'Strengths', 'bg-green-50')}
            {renderManualItemForm('weaknesses', manualWeaknesses, 'Weaknesses', 'bg-red-50')}
            {renderManualItemForm('opportunities', manualOpportunities, 'Opportunities', 'bg-blue-50')}
            {renderManualItemForm('threats', manualThreats, 'Threats', 'bg-yellow-50')}
          </div>
        </div>

        {/* Data Sources */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Sumber Data
          </label>

          <div className="space-y-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="includeProductData"
                checked={includeProductData}
                onChange={(e) => setIncludeProductData(e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded border-gray-300"
              />
              <label htmlFor="includeProductData" className="ml-2 text-sm text-gray-700">
                Data Produk
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="includeFleetData"
                checked={includeFleetData}
                onChange={(e) => setIncludeFleetData(e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded border-gray-300"
              />
              <label htmlFor="includeFleetData" className="ml-2 text-sm text-gray-700">
                Data Fleetlist Pelanggan
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="includeSalesData"
                checked={includeSalesData}
                onChange={(e) => setIncludeSalesData(e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded border-gray-300"
              />
              <label htmlFor="includeSalesData" className="ml-2 text-sm text-gray-700">
                Data Sales Revenue
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="includeKnowledgeBase"
                checked={includeKnowledgeBase}
                onChange={(e) => setIncludeKnowledgeBase(e.target.checked)}
                className="h-4 w-4 text-blue-600 rounded border-gray-300"
              />
              <label htmlFor="includeKnowledgeBase" className="ml-2 text-sm text-gray-700">
                Data Knowledge Base
              </label>
            </div>
          </div>
        </div>

        {/* Date Range */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tanggal Mulai (Opsional)
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full p-2 border rounded-md"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tanggal Akhir (Opsional)
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full p-2 border rounded-md"
            />
          </div>
        </div>

        {/* Custom Prompt */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Prompt Kustom (Opsional)
          </label>
          <textarea
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            className="w-full p-2 border rounded-md"
            rows={3}
            placeholder="Tambahkan instruksi khusus untuk analisis SWOT..."
          />
        </div>

        {/* Submit Button */}
        <div>
          <button
            type="submit"
            className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
            disabled={isLoading}
          >
            {isLoading ? 'Menganalisis...' : 'Analisis SWOT'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default SwotAnalysisForm;
