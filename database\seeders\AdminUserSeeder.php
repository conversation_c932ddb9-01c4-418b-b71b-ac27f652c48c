<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user if not exists
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]);
        }

        // Create test user if not exists
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]);
        }
    }
}
