<template>
  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900 flex items-center">
          <Truck class="h-6 w-6 mr-2" />
          Fleet Data Analyzer
        </h1>
        <div class="flex space-x-3">
          <button
            @click="refreshData"
            :disabled="isLoading"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <RefreshCw :class="['h-4 w-4 mr-2', { 'animate-spin': isLoading }]" />
            Refresh Data
          </button>
          <button
            @click="forceClearAndReload"
            class="inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <Trash2 class="h-4 w-4 mr-2" />
            Clear Cache
          </button>
        </div>
      </div>

      <!-- Search and Filters -->
      <div class="bg-white rounded-lg shadow-sm border p-4">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1 relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search fleet data..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
        </div>
      </div>

      <!-- Filter Sidebar and Main Content -->
      <div class="flex gap-6">
        <!-- Filter Sidebar -->
        <div class="w-72 bg-white rounded-lg shadow-sm border p-4">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold flex items-center">
              <Filter class="h-5 w-5 mr-2" />
              Filters
            </h3>
            <button
              @click="resetFilters"
              class="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded flex items-center"
            >
              <RefreshCw class="h-3 w-3 mr-1" />
              Reset
            </button>
          </div>

          <div class="space-y-4">
            <!-- Customer Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Customer Name</label>
              <select
                v-model="selectedCustomer"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="">Select customers...</option>
                <option v-for="customer in uniqueCustomers" :key="customer" :value="customer">
                  {{ customer }}
                </option>
              </select>
            </div>

            <!-- Status Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
              <select
                v-model="selectedStatus"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="">Select status...</option>
                <option v-for="status in uniqueStatuses" :key="status" :value="status">
                  {{ status }}
                </option>
              </select>
            </div>

            <!-- Location Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
              <select
                v-model="selectedLocation"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="">Select locations...</option>
                <option v-for="location in uniqueLocations" :key="location" :value="location">
                  {{ location }}
                </option>
              </select>
            </div>

            <!-- Tire Size Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Tire Size</label>
              <select
                v-model="selectedTireSize"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="">Select tire sizes...</option>
                <option v-for="size in uniqueTireSizes" :key="size" :value="size">
                  {{ size }}
                </option>
              </select>
            </div>

            <!-- Model Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Model</label>
              <select
                v-model="selectedModel"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              >
                <option value="">Select models...</option>
                <option v-for="model in uniqueModels" :key="model" :value="model">
                  {{ model }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <div class="flex-1">
          <!-- Loading State -->
          <div v-if="isLoading" class="flex items-center justify-center py-12">
            <div class="flex items-center space-x-3">
              <Loader2 class="h-6 w-6 animate-spin text-blue-600" />
              <span class="text-gray-600">Loading fleet data...</span>
            </div>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="p-8 text-center">
            <AlertTriangle class="h-8 w-8 text-red-500 mx-auto mb-2" />
            <p class="text-red-600 mb-4">{{ error }}</p>
            <button
              @click="refreshData"
              class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Try Again
            </button>
          </div>

          <!-- Main Content -->
          <div v-else class="space-y-6">
            <!-- Scorecards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                  <Users class="h-5 w-5 text-blue-600 mr-2" />
                  <h3 class="text-sm font-medium text-gray-500">Total Customers</h3>
                </div>
                <p class="mt-2 text-2xl font-semibold text-gray-900">{{ scorecardMetrics.totalCustomers }}</p>
              </div>
              <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                  <Truck class="h-5 w-5 text-green-600 mr-2" />
                  <h3 class="text-sm font-medium text-gray-500">Total Units</h3>
                </div>
                <p class="mt-2 text-2xl font-semibold text-gray-900">{{ scorecardMetrics.totalUnits }}</p>
              </div>
              <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                  <Disc class="h-5 w-5 text-purple-600 mr-2" />
                  <h3 class="text-sm font-medium text-gray-500">Total Tires</h3>
                </div>
                <p class="mt-2 text-2xl font-semibold text-gray-900">{{ scorecardMetrics.totalTires }}</p>
              </div>
              <div class="bg-white rounded-lg shadow-sm border p-4">
                <div class="flex items-center">
                  <Activity class="h-5 w-5 text-orange-600 mr-2" />
                  <h3 class="text-sm font-medium text-gray-500">Active Units</h3>
                </div>
                <p class="mt-2 text-2xl font-semibold text-gray-900">{{ scorecardMetrics.activeUnits }}</p>
              </div>
            </div>

            <!-- Bar Chart -->
            <div class="bg-white rounded-lg shadow-sm border p-4">
              <h3 class="text-lg font-semibold mb-4 flex items-center">
                <BarChart2 class="h-5 w-5 mr-2" />
                Customer Analysis
              </h3>
              <div class="h-80">
                <Bar :data="customerAnalysisData" :options="chartOptions" />
              </div>
            </div>

            <!-- Fleet Data Table -->
            <div class="bg-white rounded-lg shadow-sm border overflow-hidden" style="max-width: 100%">
              <div v-if="filteredData.length === 0" class="p-8 text-center">
                <p class="text-gray-600">No fleet data found matching your criteria.</p>
              </div>
              <div v-else>
                <!-- Table with sticky header and horizontal scroll -->
                <div class="relative" style="max-height: 500px; overflow: auto">
                  <!-- Horizontal scrollable container -->
                  <div class="overflow-x-auto" style="min-width: 100%">
                    <table class="min-w-full divide-y divide-gray-200 table-fixed">
                      <thead>
                        <tr>
                          <th
                            v-for="column in tableColumns"
                            :key="column"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 bg-gray-50 sticky top-0 z-10"
                            style="min-width: 150px"
                          >
                            {{ column }}
                          </th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="item in paginatedData" :key="item.id" class="hover:bg-gray-50">
                          <td
                            v-for="column in tableColumns"
                            :key="`${item.id}-${column}`"
                            class="px-6 py-4 text-sm text-gray-500"
                            style="min-width: 150px; max-width: 300px; white-space: normal; word-break: break-word"
                          >
                            {{ item[column] || '-' }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Pagination -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                  <div class="flex-1 flex justify-between sm:hidden">
                    <button
                      @click="prevPage"
                      :disabled="currentPage === 1"
                      :class="[
                        'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md',
                        currentPage === 1
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-white text-gray-700 hover:bg-gray-50'
                      ]"
                    >
                      Previous
                    </button>
                    <button
                      @click="nextPage"
                      :disabled="currentPage === totalPages"
                      :class="[
                        'ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md',
                        currentPage === totalPages
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-white text-gray-700 hover:bg-gray-50'
                      ]"
                    >
                      Next
                    </button>
                  </div>
                  <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p class="text-sm text-gray-700">
                        Showing <span class="font-medium">{{ indexOfFirstItem + 1 }}</span> to
                        <span class="font-medium">{{ Math.min(indexOfLastItem, filteredData.length) }}</span>
                        of <span class="font-medium">{{ filteredData.length }}</span> results
                      </p>
                    </div>
                    <div>
                      <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <button
                          @click="prevPage"
                          :disabled="currentPage === 1"
                          :class="[
                            'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium',
                            currentPage === 1
                              ? 'text-gray-300 cursor-not-allowed'
                              : 'text-gray-500 hover:bg-gray-50'
                          ]"
                        >
                          <span class="sr-only">Previous</span>
                          <ChevronLeft class="h-5 w-5" aria-hidden="true" />
                        </button>

                        <!-- Page numbers -->
                        <button
                          v-for="pageNum in visiblePages"
                          :key="pageNum"
                          @click="paginate(pageNum)"
                          :class="[
                            'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                            currentPage === pageNum
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          ]"
                        >
                          {{ pageNum }}
                        </button>

                        <button
                          @click="nextPage"
                          :disabled="currentPage === totalPages"
                          :class="[
                            'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium',
                            currentPage === totalPages
                              ? 'text-gray-300 cursor-not-allowed'
                              : 'text-gray-500 hover:bg-gray-50'
                          ]"
                        >
                          <span class="sr-only">Next</span>
                          <ChevronRight class="h-5 w-5" aria-hidden="true" />
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI Analysis Component -->
      <FleetAIAnalysis 
        :filteredData="filteredData" 
        :tireSizeFilter="selectedTireSizeArray" 
      />
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  Truck, Search, ChevronLeft, ChevronRight, RefreshCw,
  AlertTriangle, Loader2, Filter, Users, Disc, Activity, BarChart2,
  Trash2
} from 'lucide-vue-next'
import { Bar } from 'vue-chartjs'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'
import FleetAIAnalysis from '../../components/FleetAIAnalysis.vue'
import AppLayout from '../../layouts/AppLayout.vue'
import { clearAllCache } from '../../utils/clearCache.js'

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
)

// Types
interface FleetData {
  id: string
  customer?: string
  model?: string
  location?: string
  tire_size?: string
  tire_quantity?: string | number
  unit_qty?: string | number
  total_tire?: string | number
  status?: string
  unit_manufacture?: string
  [key: string]: any
}

// Breadcrumbs
const breadcrumbs = [
  { label: 'Dashboard', href: '/' },
  { label: 'Analytics', href: '/analytics' },
  { label: 'Fleet Analyzer', href: '/fleet-analyzer' },
]

// State
const fleetData = ref<FleetData[]>([])
const isLoading = ref(true)
const error = ref<string | null>(null)
const searchQuery = ref('')
const selectedCustomer = ref('')
const selectedStatus = ref('')
const selectedLocation = ref('')
const selectedTireSize = ref('')
const selectedModel = ref('')
const currentPage = ref(1)
const itemsPerPage = 10

// Computed properties
const uniqueCustomers = computed(() => {
  const customers = fleetData.value
    .map(item => item.customer)
    .filter(Boolean)
    .filter((value, index, self) => self.indexOf(value) === index)
  return customers.sort()
})

const uniqueStatuses = computed(() => {
  return [...new Set(fleetData.value.map(item => item.status))].filter(Boolean).sort()
})

const uniqueLocations = computed(() => {
  return [...new Set(fleetData.value.map(item => item.location))].filter(Boolean).sort()
})

const uniqueTireSizes = computed(() => {
  return [...new Set(fleetData.value.map(item => item.tire_size))].filter(Boolean).sort()
})

const uniqueModels = computed(() => {
  return [...new Set(fleetData.value.map(item => item.model))].filter(Boolean).sort()
})

const selectedTireSizeArray = computed(() => {
  return selectedTireSize.value ? [selectedTireSize.value] : []
})

const filteredData = computed(() => {
  return fleetData.value.filter(item => {
    const matchesSearch = !searchQuery.value || 
      Object.values(item).some(value => 
        String(value).toLowerCase().includes(searchQuery.value.toLowerCase())
      )
    
    const matchesCustomer = !selectedCustomer.value || item.customer === selectedCustomer.value
    const matchesStatus = !selectedStatus.value || item.status === selectedStatus.value
    const matchesLocation = !selectedLocation.value || item.location === selectedLocation.value
    const matchesTireSize = !selectedTireSize.value || item.tire_size === selectedTireSize.value
    const matchesModel = !selectedModel.value || item.model === selectedModel.value
    
    return matchesSearch && matchesCustomer && matchesStatus && matchesLocation && matchesTireSize && matchesModel
  })
})

// Scorecard metrics
const scorecardMetrics = computed(() => {
  const data = filteredData.value
  return {
    totalCustomers: uniqueCustomers.value.length,
    totalUnits: data.reduce((sum, item) => sum + (Number(item.unit_qty) || 0), 0),
    totalTires: data.reduce((sum, item) => sum + (Number(item.total_tire) || 0), 0),
    activeUnits: data.filter(item => item.status === 'Active').reduce((sum, item) => sum + (Number(item.unit_qty) || 0), 0)
  }
})

// Dynamic table columns
const tableColumns = computed(() => {
  if (fleetData.value.length === 0) return []
  const firstItem = fleetData.value[0]
  return Object.keys(firstItem).filter(key => key !== 'id')
})

// Pagination
const totalPages = computed(() => {
  return Math.ceil(filteredData.value.length / itemsPerPage)
})

const indexOfLastItem = computed(() => currentPage.value * itemsPerPage)
const indexOfFirstItem = computed(() => indexOfLastItem.value - itemsPerPage)

const paginatedData = computed(() => {
  return filteredData.value.slice(indexOfFirstItem.value, indexOfLastItem.value)
})

const visiblePages = computed(() => {
  const pages = []
  const maxVisible = 5
  let start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible - 1)
  
  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// Chart data
const customerAnalysisData = computed(() => {
  const customerData = fleetData.value.reduce((acc, item) => {
    const customer = item.customer || 'Unknown'
    if (!acc[customer]) {
      acc[customer] = { units: 0, tires: 0 }
    }
    acc[customer].units += Number(item.unit_qty) || 0
    acc[customer].tires += Number(item.total_tire) || 0
    return acc
  }, {} as Record<string, { units: number; tires: number }>)

  const labels = Object.keys(customerData)
  const unitsData = labels.map(label => customerData[label].units)
  const tiresData = labels.map(label => customerData[label].tires)

  return {
    labels,
    datasets: [
      {
        label: 'Total Units',
        data: unitsData,
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
      {
        label: 'Total Tires',
        data: tiresData,
        backgroundColor: 'rgba(147, 51, 234, 0.8)',
        borderColor: 'rgba(147, 51, 234, 1)',
        borderWidth: 1,
      },
    ],
  }
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
    title: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        precision: 0
      }
    }
  }
}

// Methods
const fetchFleetData = async (): Promise<FleetData[]> => {
  try {
    console.log('🌐 Fetching fleet data from API...')
    console.log('🔗 API URL:', 'https://chitraparatama.co.id/ICS/product/get_api.php?function=fleetlist')

    const response = await fetch('https://chitraparatama.co.id/ICS/product/get_api.php?function=fleetlist', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const rawData = await response.json()
    console.log('📥 Raw API Response:', rawData)

    // STRICT VALIDATION: Reject any data that looks like dummy data
    const validateNotDummyData = (item: any): boolean => {
      const id = String(item.id || item.id_fleet_list || '')
      const model = String(item.model || '')
      const customer = String(item.customer || '')

      // Check for dummy patterns
      if (id.includes('HE-') || id.includes('TR-') || id.includes('CN-') || id.includes('FL-')) {
        console.warn('🚫 DUMMY DATA DETECTED - REJECTING:', item)
        return false
      }

      if (model.toLowerCase().includes('excavator') ||
          model.toLowerCase().includes('dump truck') ||
          model.toLowerCase().includes('bulldozer') ||
          model.toLowerCase().includes('loader') ||
          model.toLowerCase().includes('grader')) {
        console.warn('🚫 DUMMY MODEL DETECTED - REJECTING:', item)
        return false
      }

      return true
    }

    let processedData: any[] = []

    if (Array.isArray(rawData)) {
      processedData = rawData.filter(validateNotDummyData)
    } else if (rawData && Array.isArray(rawData.data)) {
      processedData = rawData.data.filter(validateNotDummyData)
    } else {
      throw new Error('Invalid data format received from API')
    }

    console.log(`✅ Processed ${processedData.length} valid records (rejected dummy data)`)

    return processedData.map((item: any, index: number) => ({
      id: item.id_fleet_list || `fleet-${index + 1}`,
      customer: item.customer,
      model: item.model,
      location: item.location,
      tire_size: item.tire_size,
      tire_quantity: item.tire_quantity,
      unit_qty: item.unit_qty,
      total_tire: item.totaltire,
      status: item.status || 'Active',
      ...item
    }))

  } catch (error) {
    console.error('❌ Error fetching fleet data:', error)
    throw error
  }
}

const loadFleetData = async () => {
  try {
    isLoading.value = true
    error.value = null

    console.log('🔄 Loading fleet data from API...')
    const data = await fetchFleetData()
    fleetData.value = data

    console.log(`✅ Successfully loaded ${data.length} fleet records from API`)
    console.log('📊 Sample data:', data.slice(0, 2)) // Show first 2 records for debugging

    // Check for dummy data patterns
    const hasDummyData = data.some(item =>
      String(item.id || '').includes('HE-') ||
      String(item.id || '').includes('TR-') ||
      String(item.id || '').includes('CN-') ||
      String(item.model || '').toLowerCase().includes('excavator') ||
      String(item.model || '').toLowerCase().includes('dump truck') ||
      String(item.model || '').toLowerCase().includes('bulldozer')
    )

    if (hasDummyData) {
      console.error('🚨 CRITICAL: DUMMY DATA DETECTED! This should not happen.')
      console.error('🔍 Problematic records:', data.filter(item =>
        String(item.id || '').includes('HE-') ||
        String(item.id || '').includes('TR-') ||
        String(item.id || '').includes('CN-')
      ))

      // Show alert to user
      alert('⚠️ PERINGATAN: Data dummy terdeteksi! Silakan refresh halaman atau hubungi developer.')

      // Force clear dummy data
      fleetData.value = data.filter(item =>
        !String(item.id || '').includes('HE-') &&
        !String(item.id || '').includes('TR-') &&
        !String(item.id || '').includes('CN-')
      )
    } else {
      console.log('✅ No dummy data detected - all data is from API')
    }

    // Clear any cached dummy data
    localStorage.removeItem('fleetDummyData')
    sessionStorage.removeItem('fleetDummyData')

  } catch (err) {
    console.error('❌ Failed to load fleet data:', err)
    error.value = err instanceof Error ? err.message : 'Failed to load fleet data'
    fleetData.value = [] // Ensure no dummy data is used
  } finally {
    isLoading.value = false
  }
}

const refreshData = async () => {
  await loadFleetData()
}

const forceClearAndReload = () => {
  console.log('🧹 Force clearing all cache and reloading...')

  // Clear all possible cache
  clearAllCache()

  // Clear component data
  fleetData.value = []

  // Clear all storage
  localStorage.clear()
  sessionStorage.clear()

  // Show confirmation
  alert('✅ Cache cleared! Page will reload now.')

  // Force reload page
  window.location.reload()
}

const resetFilters = () => {
  selectedCustomer.value = ''
  selectedStatus.value = ''
  selectedLocation.value = ''
  selectedTireSize.value = ''
  selectedModel.value = ''
  searchQuery.value = ''
  currentPage.value = 1
}

const paginate = (pageNumber: number) => {
  currentPage.value = pageNumber
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const getStatusClass = (status?: string) => {
  const baseClass = 'inline-flex px-2 py-1 text-xs font-semibold rounded-full'
  
  switch (status?.toLowerCase()) {
    case 'active':
      return `${baseClass} bg-green-100 text-green-800`
    case 'maintenance':
      return `${baseClass} bg-yellow-100 text-yellow-800`
    case 'idle':
      return `${baseClass} bg-gray-100 text-gray-800`
    case 'breakdown':
      return `${baseClass} bg-red-100 text-red-800`
    default:
      return `${baseClass} bg-green-100 text-green-800`
  }
}

// Lifecycle
onMounted(() => {
  // Clear any cached dummy data first
  clearAllCache()

  // Force clear any potential dummy data
  fleetData.value = []

  // Add debug info
  console.log('🚀 FleetAnalyzer mounted - clearing all dummy data')
  console.log('📍 Current URL:', window.location.href)
  console.log('🔧 Component: analytics/FleetAnalyzer.vue')

  // Then load fresh data from API
  loadFleetData()
})
</script>
