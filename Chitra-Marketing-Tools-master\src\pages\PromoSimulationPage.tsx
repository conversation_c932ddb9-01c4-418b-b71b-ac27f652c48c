import React, { useState, useEffect } from 'react';
import {
  PromoConfig,
  PromoItem,
  PromoDiscount,
  PromoResult,
  PromoType,
  DiscountType,
  PromoStatus,
  PromoSimulation
} from '../types/promotion';
import PromoConfigForm from '../components/PromoConfigForm';
import PromoItemSelector from '../components/PromoItemSelector';
import PromoDiscountForm from '../components/PromoDiscountForm';
import PromoResults from '../components/PromoResults';
import PromoABSimulation from '../components/PromoABSimulation';
import PromoStatusForm from '../components/PromoStatusForm';
import PromoLibrary from '../components/PromoLibrary';
import PromoFileUpload from '../components/PromoFileUpload';
import { calculatePromoResult } from '../utils/promoCalculations';
import {
  savePromoSimulation,
  savePromoTemplate,
  updatePromoStatus
} from '../services/promoLibraryService';
import { UploadedFile } from '../services/fileUploadService';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';

const PromoSimulationPage: React.FC = () => {
  // Initial state for PromoConfig
  const [promoConfig, setPromoConfig] = useState<PromoConfig>({
    name: '',
    type: PromoType.DISCOUNT_PERCENTAGE,
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0],
    region: '',
    additionalCosts: {
      marketing: 0,
      shipping: 0,
      other: 0,
      description: '',
    },
    customerId: undefined,
    status: PromoStatus.DRAFT,
    referenceFiles: [],
  });

  // Current simulation ID
  const [currentSimulationId, setCurrentSimulationId] = useState<string | undefined>(undefined);

  // State for selected products
  const [selectedItems, setSelectedItems] = useState<PromoItem[]>([]);

  // State for discount configuration
  const [promoDiscount, setPromoDiscount] = useState<PromoDiscount>({
    type: DiscountType.PERCENTAGE,
    value: 0,
    targetSales: 0,
    marketingCost: 0,
  });

  // State for calculation results
  const [promoResult, setPromoResult] = useState<PromoResult>({
    priceAfterPromo: 0,
    totalDiscount: 0,
    marginAfterPromo: 0,
    estimatedProfit: 0,
  });

  // Calculate results whenever inputs change
  useEffect(() => {
    if (selectedItems.length > 0) {
      const result = calculatePromoResult(selectedItems, promoDiscount, promoConfig);
      setPromoResult(result);
    } else {
      setPromoResult({
        priceAfterPromo: 0,
        totalDiscount: 0,
        marginAfterPromo: 0,
        estimatedProfit: 0,
      });
    }
  }, [selectedItems, promoDiscount, promoConfig]);

  // Handle config changes
  const handleConfigChange = (newConfig: PromoConfig) => {
    setPromoConfig(newConfig);
  };

  // Handle item selection changes
  const handleItemsChange = (items: PromoItem[]) => {
    setSelectedItems(items);
  };

  // Get product descriptions for AI name generation
  const getProductDescriptions = (): string[] => {
    return selectedItems.map(item => item.product.materialDescription);
  };

  // State for uploaded files
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);

  // State for active tab
  const [activeTab, setActiveTab] = useState<string>('basic');

  // Handle file upload
  const handleFilesChange = (files: UploadedFile[]) => {
    setUploadedFiles(files);

    // Update promoConfig with file references
    setPromoConfig(prev => ({
      ...prev,
      referenceFiles: files.map(file => file.id)
    }));
  };

  // Handle status change
  const handleStatusChange = (status: PromoStatus) => {
    setPromoConfig(prev => ({
      ...prev,
      status
    }));

    // Save simulation with new status
    if (currentSimulationId) {
      updatePromoStatus(currentSimulationId, status);
    }
  };

  // Handle send for review
  const handleSendForReview = () => {
    // Update status to pending approval
    handleStatusChange(PromoStatus.PENDING_APPROVAL);

    // Save simulation
    handleSaveSimulation();

    // Show confirmation
    alert('Promo telah dikirim untuk review dan approval.');
  };

  // Handle save simulation
  const handleSaveSimulation = () => {
    if (selectedItems.length === 0) {
      alert('Silakan pilih produk terlebih dahulu sebelum menyimpan simulasi.');
      return;
    }

    try {
      // Create simulation object
      const simulation: PromoSimulation = {
        id: currentSimulationId,
        config: promoConfig,
        items: selectedItems,
        discount: promoDiscount,
        result: promoResult,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Save simulation
      const savedSimulation = savePromoSimulation(simulation);

      // Update current simulation ID
      setCurrentSimulationId(savedSimulation.id);

      alert('Simulasi promo berhasil disimpan!');
    } catch (error) {
      console.error('Error saving simulation:', error);
      alert('Gagal menyimpan simulasi. Silakan periksa konsol untuk detail.');
    }
  };

  // Handle save as template
  const handleSaveAsTemplate = () => {
    if (selectedItems.length === 0) {
      alert('Silakan pilih produk terlebih dahulu sebelum menyimpan template.');
      return;
    }

    try {
      // Create template name
      const templateName = prompt('Masukkan nama template:', promoConfig.name);

      if (!templateName) {
        return; // User cancelled
      }

      // Create simulation object
      const simulation: PromoSimulation = {
        name: templateName,
        config: promoConfig,
        items: selectedItems,
        discount: promoDiscount,
        result: promoResult,
        createdAt: new Date(),
        updatedAt: new Date(),
        isTemplate: true
      };

      // Save as template
      savePromoTemplate(simulation);

      alert('Template promo berhasil disimpan!');
    } catch (error) {
      console.error('Error saving template:', error);
      alert('Gagal menyimpan template. Silakan periksa konsol untuk detail.');
    }
  };

  // Handle use template
  const handleUseTemplate = (template: PromoSimulation) => {
    // Confirm before overwriting current data
    if (selectedItems.length > 0 && !window.confirm('Menggunakan template ini akan menggantikan data yang ada. Lanjutkan?')) {
      return;
    }

    // Set config, items, and discount from template
    setPromoConfig(template.config);
    setSelectedItems(template.items);
    setPromoDiscount(template.discount);

    // Switch to basic tab
    setActiveTab('basic');

    alert('Template berhasil diterapkan!');
  };

  // Handle discount changes
  const handleDiscountChange = (discount: PromoDiscount) => {
    setPromoDiscount(discount);
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold">Simulasi Promo Bisnis</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleSaveSimulation}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Simpan Simulasi
          </button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="w-full justify-start">
          <TabsTrigger value="basic">Simulasi Dasar</TabsTrigger>
          <TabsTrigger value="ab-simulation">Perbandingan A/B</TabsTrigger>
          <TabsTrigger value="status">Status & Approval</TabsTrigger>
          <TabsTrigger value="library">Library Promo</TabsTrigger>
          <TabsTrigger value="files">Referensi</TabsTrigger>
        </TabsList>

        {/* Basic Simulation Tab */}
        <TabsContent value="basic" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-8">
              {/* Promo Configuration Form */}
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <PromoConfigForm
                  config={promoConfig}
                  onConfigChange={handleConfigChange}
                  productDescriptions={getProductDescriptions()}
                />
              </div>

              {/* Product Selection */}
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <PromoItemSelector
                  selectedItems={selectedItems}
                  onItemsChange={handleItemsChange}
                  customerId={promoConfig.customerId}
                />
              </div>

              {/* Discount Configuration */}
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <PromoDiscountForm
                  discount={promoDiscount}
                  onDiscountChange={handleDiscountChange}
                  promoType={promoConfig.type}
                />
              </div>
            </div>

            <div className="lg:col-span-1">
              {/* Results Panel */}
              <PromoResults
                items={selectedItems}
                result={promoResult}
                config={promoConfig}
                discount={promoDiscount}
              />
            </div>
          </div>
        </TabsContent>

        {/* A/B Simulation Tab */}
        <TabsContent value="ab-simulation" className="mt-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <PromoABSimulation
              initialItems={selectedItems}
              initialConfig={promoConfig}
              initialDiscount={promoDiscount}
            />
          </div>
        </TabsContent>

        {/* Status & Approval Tab */}
        <TabsContent value="status" className="mt-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <PromoStatusForm
              status={promoConfig.status || PromoStatus.DRAFT}
              onStatusChange={handleStatusChange}
              onSendForReview={handleSendForReview}
            />
          </div>
        </TabsContent>

        {/* Library Tab */}
        <TabsContent value="library" className="mt-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <PromoLibrary
              onUseTemplate={handleUseTemplate}
              onSaveAsTemplate={handleSaveAsTemplate}
            />
          </div>
        </TabsContent>

        {/* Files Tab */}
        <TabsContent value="files" className="mt-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <PromoFileUpload
              promoId={currentSimulationId}
              onFilesChange={handleFilesChange}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PromoSimulationPage;
