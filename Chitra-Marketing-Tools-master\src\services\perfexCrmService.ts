import { Customer, CustomerFormData } from '../types';

// Perfex CRM API configuration
// Use our local proxy server to avoid CORS issues
const PERFEX_API_URL = 'http://localhost:3001/api';
// For direct access (if CORS is configured on the server)
// const PERFEX_API_URL = 'https://gohse.id/crm/api';
const PERFEX_API_TOKEN = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyIjoiQWZpIiwibmFtZSI6ImFmaSIsIkFQSV9USU1FIjoxNzQ2NTM1MzcxfQ.nQtXFvJJgcv5SgId_1EtD7vW3Qf0WCwggTX7vw1tDq4';

// Function to fetch clients from Perfex CRM
export async function fetchClientsFromPerfexCrm(): Promise<Customer[]> {
  try {
    // For testing purposes, let's log the API URL and token (masked)
    console.log(`Fetching from: ${PERFEX_API_URL}/clients`);
    console.log(`Using token: ${PERFEX_API_TOKEN.substring(0, 10)}...`);

    // Use a proxy approach to avoid CORS issues
    // This is a workaround for development - in production, proper CORS headers should be set on the server
    const response = await fetch(`${PERFEX_API_URL}/clients`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${PERFEX_API_TOKEN}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      // Add mode: 'cors' to explicitly request CORS
      mode: 'cors',
      // Add credentials if needed (if the API requires cookies)
      credentials: 'same-origin'
    });

    // Log the response status
    console.log(`Response status: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API error response: ${errorText}`);
      throw new Error(`API request failed with status ${response.status}: ${errorText}`);
    }

    // Parse the response as JSON
    let data;
    try {
      data = await response.json();
      console.log("Data from Perfex CRM:", data);
    } catch (parseError) {
      console.error("Error parsing JSON response:", parseError);
      const text = await response.text();
      console.log("Raw response:", text);
      throw new Error("Failed to parse API response as JSON");
    }

    // For debugging, let's create a sample customer if the API fails
    if (!data || (typeof data === 'object' && Object.keys(data).length === 0)) {
      console.log("No data returned from API, using sample data");
      return [
        {
          id: "sample1",
          name: "Sample Customer",
          email: "<EMAIL>",
          phone: "************",
          address: "123 Sample St, Sample City",
          company: "Sample Company",
          notes: "This is a sample customer created when the API returned no data",
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
    }

    // Map Perfex CRM client data to our Customer format
    if (data && Array.isArray(data.clients)) {
      // If the data has a 'clients' property that is an array
      return data.clients.map((client: any) => ({
        id: client.id?.toString() || Date.now().toString(),
        name: client.company || (client.firstname && client.lastname ? `${client.firstname} ${client.lastname}` : 'Unknown'),
        email: client.email || '',
        phone: client.phonenumber || client.phone || '',
        address: [client.address, client.city, client.state, client.zip].filter(Boolean).join(', '),
        company: client.company || '',
        notes: client.notes || '',
        createdAt: new Date(client.datecreated || Date.now()),
        updatedAt: new Date(client.last_login || client.datecreated || Date.now())
      }));
    } else if (data && typeof data === 'object') {
      // If the data is an object (could be a single client or an array)
      if (Array.isArray(data)) {
        // If data is already an array
        return data.map((client: any) => ({
          id: client.id?.toString() || Date.now().toString(),
          name: client.company || client.name || (client.firstname && client.lastname ? `${client.firstname} ${client.lastname}` : 'Unknown'),
          email: client.email || '',
          phone: client.phonenumber || client.phone || '',
          address: [client.address, client.city, client.state, client.zip].filter(Boolean).join(', '),
          company: client.company || '',
          notes: client.notes || '',
          createdAt: new Date(client.datecreated || Date.now()),
          updatedAt: new Date(client.last_login || client.datecreated || Date.now())
        }));
      } else {
        // If data is a single object (not an array)
        // Check if it has a property that might contain the clients
        const possibleArrayProps = ['clients', 'data', 'results', 'items'];
        for (const prop of possibleArrayProps) {
          if (data[prop] && Array.isArray(data[prop])) {
            return data[prop].map((client: any) => ({
              id: client.id?.toString() || Date.now().toString(),
              name: client.company || client.name || (client.firstname && client.lastname ? `${client.firstname} ${client.lastname}` : 'Unknown'),
              email: client.email || '',
              phone: client.phonenumber || client.phone || '',
              address: [client.address, client.city, client.state, client.zip].filter(Boolean).join(', '),
              company: client.company || '',
              notes: client.notes || '',
              createdAt: new Date(client.datecreated || Date.now()),
              updatedAt: new Date(client.last_login || client.datecreated || Date.now())
            }));
          }
        }

        // If we couldn't find an array property, treat the object itself as a single client
        return [{
          id: data.id?.toString() || Date.now().toString(),
          name: data.company || data.name || (data.firstname && data.lastname ? `${data.firstname} ${data.lastname}` : 'Unknown'),
          email: data.email || '',
          phone: data.phonenumber || data.phone || '',
          address: [data.address, data.city, data.state, data.zip].filter(Boolean).join(', '),
          company: data.company || '',
          notes: data.notes || '',
          createdAt: new Date(data.datecreated || Date.now()),
          updatedAt: new Date(data.last_login || data.datecreated || Date.now())
        }];
      }
    }

    // If we couldn't parse the data in any way, return an empty array
    console.warn("Could not parse data from API, returning empty array");
    return [];
  } catch (error) {
    console.error("Error fetching clients from Perfex CRM:", error);
    throw error;
  }
}

// Function to create a client in Perfex CRM
export async function createClientInPerfexCrm(customerData: CustomerFormData): Promise<any> {
  try {
    // Map our CustomerFormData to Perfex CRM expected format
    const perfexClientData = {
      company: customerData.company || customerData.name,
      vat: '',
      phonenumber: customerData.phone,
      country: 0,
      city: '',
      zip: '',
      state: '',
      address: customerData.address,
      website: '',
      active: 1,
      // If name is provided but no company, split name into firstname/lastname
      firstname: !customerData.company ? customerData.name.split(' ')[0] : '',
      lastname: !customerData.company ? customerData.name.split(' ').slice(1).join(' ') : '',
      email: customerData.email,
      default_currency: 0,
      default_language: '',
      billing_street: customerData.address,
      billing_city: '',
      billing_state: '',
      billing_zip: '',
      billing_country: 0,
      shipping_street: customerData.address,
      shipping_city: '',
      shipping_state: '',
      shipping_zip: '',
      shipping_country: 0,
      notes: customerData.notes || ''
    };

    const response = await fetch(`${PERFEX_API_URL}/clients`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${PERFEX_API_TOKEN}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(perfexClientData)
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    console.log("Client created in Perfex CRM:", data);
    return data;
  } catch (error) {
    console.error("Error creating client in Perfex CRM:", error);
    throw error;
  }
}

// Function to update a client in Perfex CRM
export async function updateClientInPerfexCrm(clientId: string, customerData: CustomerFormData): Promise<any> {
  try {
    // Map our CustomerFormData to Perfex CRM expected format
    const perfexClientData = {
      company: customerData.company || customerData.name,
      vat: '',
      phonenumber: customerData.phone,
      address: customerData.address,
      // If name is provided but no company, split name into firstname/lastname
      firstname: !customerData.company ? customerData.name.split(' ')[0] : '',
      lastname: !customerData.company ? customerData.name.split(' ').slice(1).join(' ') : '',
      email: customerData.email,
      notes: customerData.notes || ''
    };

    const response = await fetch(`${PERFEX_API_URL}/clients/${clientId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${PERFEX_API_TOKEN}`,
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(perfexClientData)
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    console.log("Client updated in Perfex CRM:", data);
    return data;
  } catch (error) {
    console.error("Error updating client in Perfex CRM:", error);
    throw error;
  }
}

// Function to delete a client in Perfex CRM
export async function deleteClientInPerfexCrm(clientId: string): Promise<boolean> {
  try {
    const response = await fetch(`${PERFEX_API_URL}/clients/${clientId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${PERFEX_API_TOKEN}`,
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    console.log("Client deleted from Perfex CRM");
    return true;
  } catch (error) {
    console.error("Error deleting client from Perfex CRM:", error);
    throw error;
  }
}
