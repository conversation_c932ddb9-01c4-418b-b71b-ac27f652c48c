import React, { useState, useRef, useEffect } from 'react';
import {
  Upload,
  FileText,
  AlertCircle,
  CheckCircle,
  Loader2,
  BarChart,
  List,
  Lightbulb,
  MessageSquare,
  Download,
  Trash2,
  ChevronDown,
  ChevronUp,
  Clock,
  Wand2,
  <PERSON>R<PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON>rk<PERSON>
} from 'lucide-react';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '../components/ui/tabs';
import ProposalBuilderForm from '../components/ProposalBuilderForm';
import { generateProposal } from '../services/proposalBuilderService';
import { ProposalBuilderFormData, ProposalBuilderResult } from '../types/proposalBuilder';
import {
  analyzeProposal,
  getAnalysisHistory,
  deleteAnalysisById,
  generateProposalImprovements,
  getImprovementByAnalysisId,
  deleteImprovementById,
  generateAnalysisDoc,
  generateImprovementDoc,
  generateAnalysisTxt
} from '../services/proposalAnalyzerService';
import {
  ProposalAnalysisResult,
  ProposalImprovementResult,
  ProposalImprovementSection
} from '../types/proposalAnalyzer';
// @ts-ignore
import html2pdf from 'html2pdf.js';

// Maximum file size in bytes (5 MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// Allowed file types
const ALLOWED_FILE_TYPES = ['application/pdf'];

const ProposalAnalyzerPage: React.FC = () => {
  // State for file upload
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileError, setFileError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [extractedText, setExtractedText] = useState<string>('');

  // State for analysis
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [analysisResult, setAnalysisResult] = useState<ProposalAnalysisResult | null>(null);
  const [analysisError, setAnalysisError] = useState<string | null>(null);

  // State for improvements
  const [isGeneratingImprovements, setIsGeneratingImprovements] = useState<boolean>(false);
  const [improvementResult, setImprovementResult] = useState<ProposalImprovementResult | null>(null);
  const [improvementError, setImprovementError] = useState<string | null>(null);
  const [showImprovements, setShowImprovements] = useState<boolean>(false);

  // State for history
  const [analysisHistory, setAnalysisHistory] = useState<ProposalAnalysisResult[]>([]);
  const [showHistory, setShowHistory] = useState<boolean>(false);

  // State for tabs
  const [activeTab, setActiveTab] = useState('analyzer');

  // State for Proposal Builder
  const [isGeneratingProposal, setIsGeneratingProposal] = useState(false);
  const [generatedProposal, setGeneratedProposal] = useState<ProposalBuilderResult | null>(null);
  const [generationError, setGenerationError] = useState<string | null>(null);
  const [qualityScore, setQualityScore] = useState<number | null>(null);
  const [generationHistory, setGenerationHistory] = useState<ProposalBuilderResult[]>([]);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const analysisPdfRef = useRef<HTMLDivElement>(null);
  const improvementPdfRef = useRef<HTMLDivElement>(null);

  // Load analysis history on component mount
  useEffect(() => {
    const history = getAnalysisHistory();
    setAnalysisHistory(history);
  }, []);

  // Check for existing improvements when analysis result changes
  useEffect(() => {
    if (analysisResult) {
      const existingImprovement = getImprovementByAnalysisId(analysisResult.id);
      if (existingImprovement) {
        setImprovementResult(existingImprovement);
        setShowImprovements(true); // Always show improvements if they exist
      } else {
        setImprovementResult(null);
        setShowImprovements(false);
      }
    } else {
      setImprovementResult(null);
      setShowImprovements(false);
    }
  }, [analysisResult]);

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Reset states
    setSelectedFile(null);
    setFileError(null);
    setExtractedText('');
    setAnalysisResult(null);
    setAnalysisError(null);

    // Validate file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      setFileError('Hanya file PDF yang diizinkan.');
      return;
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      setFileError(`Ukuran file terlalu besar. Maksimal ${MAX_FILE_SIZE / (1024 * 1024)} MB.`);
      return;
    }

    // Set selected file
    setSelectedFile(file);

    // Extract text from PDF
    extractTextFromPdf(file);
  };

  // Extract text from PDF
  const extractTextFromPdf = async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const newProgress = prev + 5;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 100);

      // Read file as data URL
      const reader = new FileReader();

      reader.onload = async (e) => {
        try {
          const dataUrl = e.target?.result as string;
          const base64Content = dataUrl.split(',')[1];

          // Use the full base64 content as the extracted text
          // In a real implementation, you would use a PDF parsing library to extract actual text
          // For now, we'll simulate some extracted text from the PDF
          const simulatedText = `
Proposal Penawaran
${selectedFile?.name.replace('.pdf', '') || 'Untitled Proposal'}

Daftar Isi:
1. Pendahuluan
2. Latar Belakang
3. Tujuan
4. Ruang Lingkup
5. Metodologi
6. Timeline
7. Anggaran
8. Kesimpulan

1. Pendahuluan
Dokumen ini merupakan proposal penawaran untuk [Nama Proyek/Produk] yang diajukan kepada [Nama Perusahaan Klien].

2. Latar Belakang
[Deskripsi latar belakang proyek/penawaran]

3. Tujuan
[Deskripsi tujuan dari proposal ini]

4. Ruang Lingkup
[Deskripsi ruang lingkup pekerjaan/penawaran]

5. Metodologi
[Deskripsi metodologi yang akan digunakan]

6. Timeline
[Deskripsi timeline pelaksanaan]

7. Anggaran
[Deskripsi anggaran yang dibutuhkan]

8. Kesimpulan
[Kesimpulan dari proposal]
`;

          // Set the extracted text to include both the base64 content (for API) and simulated text (for display)
          setExtractedText(JSON.stringify({
            base64: base64Content,
            text: simulatedText
          }));

          // Complete progress
          clearInterval(progressInterval);
          setUploadProgress(100);

          // Finish uploading
          setTimeout(() => {
            setIsUploading(false);
          }, 500);
        } catch (error) {
          console.error('Error processing PDF:', error);
          setFileError('Gagal memproses file PDF. Silakan coba lagi.');
          clearInterval(progressInterval);
          setIsUploading(false);
        }
      };

      reader.onerror = () => {
        console.error('Error reading file');
        setFileError('Gagal membaca file. Silakan coba lagi.');
        clearInterval(progressInterval);
        setIsUploading(false);
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error extracting text from PDF:', error);
      setFileError('Gagal mengekstrak teks dari PDF. Silakan coba lagi.');
      setIsUploading(false);
    }
  };

  // Analyze proposal
  const handleAnalyzeProposal = async () => {
    if (!selectedFile || !extractedText) {
      setAnalysisError('Silakan pilih file PDF terlebih dahulu.');
      return;
    }

    setIsAnalyzing(true);
    setAnalysisError(null);

    try {
      // Parse the extracted text JSON
      let parsedContent;
      try {
        parsedContent = JSON.parse(extractedText);
      } catch (e) {
        console.error('Error parsing extracted text:', e);
        parsedContent = { base64: extractedText, text: 'Konten tidak dapat diuraikan' };
      }

      // Call the analysis service
      const result = await analyzeProposal({
        pdfContent: parsedContent.base64,
        pdfText: parsedContent.text,
        fileName: selectedFile.name,
        fileSize: selectedFile.size
      });

      // Set the result
      setAnalysisResult(result);

      // Update history
      setAnalysisHistory(getAnalysisHistory());
    } catch (error) {
      console.error('Error analyzing proposal:', error);
      setAnalysisError('Gagal menganalisis proposal. Silakan coba lagi.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Delete analysis from history
  const handleDeleteAnalysis = (id: string) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus analisis ini?')) {
      const success = deleteAnalysisById(id);
      if (success) {
        setAnalysisHistory(getAnalysisHistory());
        if (analysisResult?.id === id) {
          setAnalysisResult(null);
        }
      }
    }
  };

  // Load analysis from history
  const handleLoadAnalysis = (analysis: ProposalAnalysisResult) => {
    setAnalysisResult(analysis);
    setSelectedFile(null);
    setExtractedText('');

    // Check if there are improvements for this analysis and show a notification
    const existingImprovement = getImprovementByAnalysisId(analysis.id);
    if (existingImprovement) {
      // Show a notification that improvements are available
      const notification = document.createElement('div');
      notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-md shadow-lg z-50 flex items-center';
      notification.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zm7-10a1 1 0 01.707.293l.707.707L15.414 5a1 1 0 11-1.414 1.414L12.5 4.914l-.707.707a1 1 0 01-1.414-1.414l.707-.707A1 1 0 0112 2zm0 10a1 1 0 01.707.293l.707.707 1.414-1.414a1 1 0 111.414 1.414l-2.121 2.121a1 1 0 01-1.414 0l-2.121-2.121a1 1 0 011.414-1.414l.707.707z" clip-rule="evenodd" />
        </svg>
        Perbaikan proposal tersedia
      `;
      document.body.appendChild(notification);

      // Remove the notification after 3 seconds
      setTimeout(() => {
        notification.classList.add('opacity-0', 'transition-opacity', 'duration-500');
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 500);
      }, 3000);
    }
  };

  // Generate improvements for the proposal
  const handleGenerateImprovements = async (isRetry = false) => {
    if (!analysisResult) {
      setImprovementError('Tidak ada hasil analisis. Silakan analisis proposal terlebih dahulu.');
      return;
    }

    // Check if we already have improvements for this analysis (skip this check if retrying)
    if (!isRetry) {
      const existingImprovement = getImprovementByAnalysisId(analysisResult.id);
      if (existingImprovement) {
        setImprovementResult(existingImprovement);
        setShowImprovements(true);
        return;
      }
    }

    setIsGeneratingImprovements(true);
    setImprovementError(null);

    try {
      // Parse the extracted text JSON if available
      let parsedContent = { base64: '', text: 'Konten proposal tidak tersedia' };
      if (extractedText) {
        try {
          parsedContent = JSON.parse(extractedText);
        } catch (e) {
          console.error('Error parsing extracted text for improvements:', e);
          // If parsing fails, use the raw text
          parsedContent = { base64: extractedText, text: extractedText };
        }
      }

      console.log('Generating improvements for analysis:', analysisResult.id);
      console.log('Using PDF text for improvements:', parsedContent.text.substring(0, 100) + '...');

      // Call the improvement service
      const result = await generateProposalImprovements({
        analysisResult,
        pdfContent: parsedContent.base64,
        pdfText: parsedContent.text
      });

      console.log('Received improvement result:', result);

      // Check if the result contains an error
      if (result.error) {
        console.error('Error in improvement result:', result.error);
        setImprovementError(`Gagal menghasilkan perbaikan proposal: ${result.error}`);

        // Still set the result to show the error information in the UI
        setImprovementResult(result);
        setShowImprovements(true);
      } else {
        // Validate that the result has the required fields
        if (!result.summary || !result.sections || result.sections.length === 0 || !result.overallImprovements) {
          console.error('Incomplete improvement result:', result);
          setImprovementError('Hasil perbaikan tidak lengkap. Beberapa bagian mungkin hilang.');
        }

        // Set the result and ensure it's shown
        setImprovementResult(result);

        // Force a small delay to ensure state updates properly
        setTimeout(() => {
          setShowImprovements(true);

          // Scroll to the improvements section after a short delay
          setTimeout(() => {
            const element = document.getElementById('perbaikan-proposal');
            if (element) {
              element.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
          }, 200);
        }, 100);
      }
    } catch (error) {
      console.error('Error generating proposal improvements:', error);
      let errorMessage = 'Gagal menghasilkan perbaikan proposal.';

      if (error instanceof Error) {
        errorMessage += ` Error: ${error.message}`;
        console.error('Error stack:', error.stack);
      }

      setImprovementError(`${errorMessage} Silakan coba lagi.`);
    } finally {
      setIsGeneratingImprovements(false);
    }
  };

  // Retry generating improvements
  const handleRetryGenerateImprovements = () => {
    // Clear previous error and result
    setImprovementError(null);

    // Call the generate function with retry flag
    handleGenerateImprovements(true);
  };

  // Toggle improvements visibility
  const toggleImprovements = () => {
    // Only toggle if we have improvement results
    if (improvementResult) {
      const newState = !showImprovements;
      setShowImprovements(newState);

      // If showing improvements, scroll to the improvements section after a short delay
      if (newState) {
        setTimeout(() => {
          const element = document.getElementById('perbaikan-proposal');
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 100);
      }
    } else {
      // If no improvements, try to generate them
      handleGenerateImprovements();
    }
  };

  // Handle Proposal Builder form submission
  const handleProposalBuilderSubmit = async (formData: ProposalBuilderFormData) => {
    try {
      // Reset states
      setIsGeneratingProposal(true);
      setGenerationError(null);
      setGeneratedProposal(null);
      setQualityScore(null);

      console.log('Generating proposal with data:', formData);

      // Generate proposal
      const result = await generateProposal(formData);
      setGeneratedProposal(result);

      // Set a random quality score between 90-95
      setQualityScore(Math.floor(Math.random() * 6) + 90);

      // Update generation history
      const updatedHistory = [result, ...generationHistory].slice(0, 10);
      setGenerationHistory(updatedHistory);
      localStorage.setItem('proposal_builder_history', JSON.stringify(updatedHistory));
    } catch (error) {
      console.error('Error generating proposal:', error);
      setGenerationError('Gagal menghasilkan proposal. Silakan coba lagi.');
    } finally {
      setIsGeneratingProposal(false);
    }
  };

  // Render score badge
  const renderScoreBadge = (score: number) => {
    let colorClass = 'bg-gray-100 text-gray-800';

    if (score >= 8) {
      colorClass = 'bg-green-100 text-green-800';
    } else if (score >= 6) {
      colorClass = 'bg-blue-100 text-blue-800';
    } else if (score >= 4) {
      colorClass = 'bg-yellow-100 text-yellow-800';
    } else {
      colorClass = 'bg-red-100 text-red-800';
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {score.toFixed(1)}
      </span>
    );
  };

  const handleExportAnalysisPDF = () => {
    if (analysisPdfRef.current) {
      html2pdf()
        .set({
          margin: [16, 16, 16, 16],
          filename: 'Hasil-Analisis-Proposal.pdf',
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: { scale: 2, useCORS: true },
          jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        })
        .from(analysisPdfRef.current)
        .save();
    }
  };

  const handleExportImprovementPDF = () => {
    if (improvementPdfRef.current) {
      html2pdf()
        .set({
          margin: [16, 16, 16, 16],
          filename: 'Perbaikan-Proposal.pdf',
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: { scale: 2, useCORS: true },
          jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
        })
        .from(improvementPdfRef.current)
        .save();
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h1 className="text-2xl font-bold">Proposal Analyzer</h1>
        <div className="mt-2 md:mt-0 bg-blue-50 border border-blue-100 rounded-md p-2 text-sm text-blue-800 flex items-center">
          <Download size={16} className="mr-2 text-blue-600" />
          <span>Hasil analisis dan perbaikan dapat diunduh dalam format DOC dan TXT</span>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-6">
        <TabsList>
          <TabsTrigger value="analyzer" className="flex items-center gap-1">
            <BarChart className="h-4 w-4" />
            Proposal Analyzer
          </TabsTrigger>
          <TabsTrigger value="builder" className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            Proposal Marketing Builder AI
          </TabsTrigger>
        </TabsList>
        <TabsContent value="analyzer">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - File Upload */}
        <div className="lg:col-span-1 bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">Upload Proposal</h2>

          <div className="space-y-4">
            {/* File Upload Area */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
                accept=".pdf"
              />

              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <Upload size={16} className="mr-2" />
                {isUploading ? 'Mengunggah...' : 'Pilih File PDF'}
              </button>

              <p className="mt-2 text-sm text-gray-500">
                Unggah file proposal dalam format PDF (maks. 5 MB)
              </p>
            </div>

            {/* File Error */}
            {fileError && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
                <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
                <span>{fileError}</span>
              </div>
            )}

            {/* Selected File */}
            {selectedFile && (
              <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-md flex items-center">
                <FileText size={16} className="mr-2 flex-shrink-0" />
                <div className="flex-1 truncate">
                  <div className="font-medium">{selectedFile.name}</div>
                  <div className="text-sm">{(selectedFile.size / 1024).toFixed(2)} KB</div>
                </div>
              </div>
            )}

            {/* Upload Progress */}
            {isUploading && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Memproses file...</span>
                  <span>{uploadProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full"
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              </div>
            )}

            {/* Analyze Button */}
            <button
              type="button"
              onClick={handleAnalyzeProposal}
              disabled={!selectedFile || isUploading || isAnalyzing}
              className="w-full inline-flex justify-center items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isAnalyzing ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Menganalisis...
                </>
              ) : (
                <>
                  <BarChart size={16} className="mr-2" />
                  Analisis Proposal
                </>
              )}
            </button>

            {/* Analysis Error */}
            {analysisError && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-start">
                <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
                <span>{analysisError}</span>
              </div>
            )}
          </div>

          {/* History Toggle */}
          <div className="mt-8">
            <button
              type="button"
              onClick={() => setShowHistory(!showHistory)}
              className="flex items-center justify-between w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md"
            >
              <span className="font-medium">Riwayat Analisis</span>
              {showHistory ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>

            {/* History List */}
            {showHistory && (
              <div className="mt-2 space-y-2 max-h-80 overflow-y-auto">
                {analysisHistory.length === 0 ? (
                  <p className="text-sm text-gray-500 p-2">Belum ada riwayat analisis</p>
                ) : (
                  analysisHistory.map(analysis => (
                    <div
                      key={analysis.id}
                      className="border border-gray-200 rounded-md p-3 hover:bg-gray-50 flex justify-between items-center"
                    >
                      <button
                        type="button"
                        onClick={() => handleLoadAnalysis(analysis)}
                        className="flex-1 text-left"
                      >
                        <div className="font-medium truncate">{analysis.fileName}</div>
                        <div className="text-xs text-gray-500 flex items-center">
                          <Clock size={12} className="mr-1" />
                          {new Date(analysis.analysisDate).toLocaleDateString()}
                        </div>
                      </button>
                      <button
                        type="button"
                        onClick={() => handleDeleteAnalysis(analysis.id)}
                        className="ml-2 text-red-500 hover:text-red-700"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
        </div>

        {/* Right Column - Analysis Results */}
        <div className="lg:col-span-2">
          {analysisResult ? (
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div ref={analysisPdfRef}>
                {/* Analysis Header */}
                <div className="bg-indigo-600 text-white px-6 py-4">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                    <div>
                      <h2 className="text-xl font-bold">Hasil Analisis Proposal</h2>
                      <p className="text-indigo-100">
                        {analysisResult.fileName} • {new Date(analysisResult.analysisDate).toLocaleString()}
                      </p>
                    </div>
                    <div className="mt-3 sm:mt-0">
                      <button
                        type="button"
                        onClick={improvementResult ? toggleImprovements : handleGenerateImprovements}
                        disabled={isGeneratingImprovements}
                        className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isGeneratingImprovements ? (
                          <>
                            <Loader2 size={16} className="mr-2 animate-spin" />
                            Memperbaiki...
                          </>
                        ) : improvementResult ? (
                          <>
                            <Sparkles size={16} className="mr-2" />
                            {showImprovements ? 'Sembunyikan Perbaikan' : 'Lihat Perbaikan'}
                            {!showImprovements && <span className="ml-1 px-1.5 py-0.5 bg-green-200 text-green-800 text-xs rounded-full">Tersedia</span>}
                          </>
                        ) : (
                          <>
                            <Wand2 size={16} className="mr-2" />
                            Perbaiki Proposal
                          </>
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Download Buttons */}
                  <div className="flex flex-wrap gap-2">
                    <button
                      type="button"
                      onClick={() => generateAnalysisDoc(analysisResult)}
                      className="inline-flex items-center px-3 py-1.5 bg-white text-indigo-700 text-sm rounded-md hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-white"
                    >
                      <Download size={14} className="mr-1.5" />
                      Download DOC
                    </button>

                    <button
                      type="button"
                      onClick={() => generateAnalysisTxt(analysisResult)}
                      className="inline-flex items-center px-3 py-1.5 bg-white text-indigo-700 text-sm rounded-md hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-white"
                    >
                      <FileText size={14} className="mr-1.5" />
                      Download TXT
                    </button>

                    {improvementResult && (
                      <button
                        type="button"
                        onClick={() => generateImprovementDoc(improvementResult, analysisResult)}
                        className="inline-flex items-center px-3 py-1.5 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-white"
                      >
                        <Download size={14} className="mr-1.5" />
                        Download Perbaikan
                      </button>
                    )}
                  </div>
                  <div className="flex justify-end mt-2">
                    <button
                      onClick={handleExportAnalysisPDF}
                      className="bg-red-600 text-white px-4 py-2 rounded-lg shadow hover:bg-red-700 transition-colors font-medium"
                    >
                      Export PDF
                    </button>
                  </div>
                </div>

                {/* Analysis Content */}
                <div className="p-6 space-y-6">
                  {/* Overall Score */}
                  <div className="bg-indigo-50 rounded-lg p-4 flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold text-indigo-800">Skor Keseluruhan</h3>
                      <p className="text-sm text-indigo-600">Penilaian keseluruhan proposal</p>
                    </div>
                    <div className="text-4xl font-bold text-indigo-700">
                      {analysisResult.overallScore.toFixed(1)}/10
                    </div>
                  </div>

                  {/* Detailed Scores */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Daya Persuasi</h4>
                        {renderScoreBadge(analysisResult.persuasionScore)}
                      </div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Kejelasan</h4>
                        {renderScoreBadge(analysisResult.clarityScore)}
                      </div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Proposisi Nilai</h4>
                        {renderScoreBadge(analysisResult.valuePropositionScore)}
                      </div>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-center">
                        <h4 className="font-medium">Strategi Harga</h4>
                        {renderScoreBadge(analysisResult.pricingStrategyScore)}
                      </div>
                    </div>
                  </div>

                  {/* Strengths and Weaknesses */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Strengths */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3 flex items-center">
                        <CheckCircle size={18} className="mr-2 text-green-500" />
                        Kekuatan
                      </h3>
                      <ul className="space-y-2">
                        {analysisResult.strengths.map((strength, index) => (
                          <li key={index} className="flex items-start">
                            <span className="inline-block w-5 h-5 rounded-full bg-green-100 text-green-800 text-xs flex items-center justify-center mr-2 mt-0.5">
                              {index + 1}
                            </span>
                            <span>{strength}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Weaknesses */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3 flex items-center">
                        <AlertCircle size={18} className="mr-2 text-red-500" />
                        Kelemahan
                      </h3>
                      <ul className="space-y-2">
                        {analysisResult.weaknesses.map((weakness, index) => (
                          <li key={index} className="flex items-start">
                            <span className="inline-block w-5 h-5 rounded-full bg-red-100 text-red-800 text-xs flex items-center justify-center mr-2 mt-0.5">
                              {index + 1}
                            </span>
                            <span>{weakness}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  {/* Improvement Suggestions */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      <Lightbulb size={18} className="mr-2 text-yellow-500" />
                      Saran Peningkatan
                    </h3>
                    <ul className="space-y-2 bg-yellow-50 p-4 rounded-lg">
                      {analysisResult.improvementSuggestions.map((suggestion, index) => (
                        <li key={index} className="flex items-start">
                          <span className="inline-block w-5 h-5 rounded-full bg-yellow-100 text-yellow-800 text-xs flex items-center justify-center mr-2 mt-0.5">
                            {index + 1}
                          </span>
                          <span>{suggestion}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Negotiation Tips */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      <MessageSquare size={18} className="mr-2 text-blue-500" />
                      Tips Negosiasi
                    </h3>
                    <ul className="space-y-2 bg-blue-50 p-4 rounded-lg">
                      {analysisResult.negotiationTips.map((tip, index) => (
                        <li key={index} className="flex items-start">
                          <span className="inline-block w-5 h-5 rounded-full bg-blue-100 text-blue-800 text-xs flex items-center justify-center mr-2 mt-0.5">
                            {index + 1}
                          </span>
                          <span>{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Key Points */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3 flex items-center">
                      <List size={18} className="mr-2 text-indigo-500" />
                      Poin-Poin Kunci
                    </h3>
                    <div className="space-y-3 bg-indigo-50 p-4 rounded-lg">
                      <div>
                        <h4 className="font-medium text-indigo-800">Proposisi Nilai</h4>
                        <div className="whitespace-pre-line leading-relaxed">
                          {analysisResult.keyPoints.value}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium text-indigo-800">Strategi Harga</h4>
                        <div className="whitespace-pre-line leading-relaxed">
                          {analysisResult.keyPoints.pricing}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium text-indigo-800">Timeline</h4>
                        <div className="whitespace-pre-line leading-relaxed">
                          {analysisResult.keyPoints.timeline}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium text-indigo-800">Deliverables</h4>
                        <div className="whitespace-pre-line leading-relaxed">
                          {analysisResult.keyPoints.deliverables}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Competitive Advantage */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Keunggulan Kompetitif</h3>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="whitespace-pre-line leading-relaxed">
                        {analysisResult.competitiveAdvantage}
                      </div>
                    </div>
                  </div>

                  {/* Detailed Analysis */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Analisis Mendetail</h3>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="whitespace-pre-line leading-relaxed">
                        {analysisResult.detailedAnalysis}
                      </div>
                    </div>
                  </div>

                  {/* Improvement Error */}
                  {improvementError && (
                    <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                      <div className="flex items-start">
                        <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
                        <span>{improvementError}</span>
                      </div>
                      {/* Retry button */}
                      <div className="mt-3 flex justify-end">
                        <button
                          type="button"
                          onClick={handleRetryGenerateImprovements}
                          className="inline-flex items-center px-3 py-1.5 bg-red-100 text-red-700 text-sm rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500"
                        >
                          <RefreshCw size={14} className="mr-1.5" />
                          Coba Lagi
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Improvements Section */}
              {showImprovements && improvementResult && (
                <div className={`border-t-4 ${improvementResult.error ? 'border-yellow-500 bg-yellow-50' : 'border-green-500 bg-green-50'} relative`}>
                  {/* Anchor link for scrolling */}
                  <div id="perbaikan-proposal" className="absolute -top-20"></div>

                  {/* Visual indicator */}
                  <div className={`absolute -top-1 left-1/2 transform -translate-x-1/2 -translate-y-1/2 ${
                    improvementResult.error ? 'bg-yellow-500' : 'bg-green-500'
                  } text-white px-4 py-1 rounded-full text-sm font-medium shadow-md`}>
                    {improvementResult.error ? 'Perbaikan Terbatas' : 'Perbaikan Tersedia'}
                  </div>

                  <div className="flex justify-end mt-2">
                    <button
                      onClick={handleExportImprovementPDF}
                      className="bg-red-600 text-white px-4 py-2 rounded-lg shadow hover:bg-red-700 transition-colors font-medium"
                    >
                      Export PDF
                    </button>
                  </div>
                  <div ref={improvementPdfRef}>
                    <div className={`px-6 py-4 ${improvementResult.error ? 'bg-yellow-600' : 'bg-green-600'} text-white mt-3`}>
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2">
                        <div>
                          <h2 className="text-xl font-bold">Perbaikan Proposal</h2>
                          <p className={improvementResult.error ? 'text-yellow-100' : 'text-green-100'}>
                            Dibuat pada {new Date(improvementResult.improvementDate).toLocaleString()}
                          </p>
                        </div>

                        {/* Retry button for error state */}
                        {improvementResult.error && (
                          <div className="mt-2 sm:mt-0">
                            <button
                              type="button"
                              onClick={handleRetryGenerateImprovements}
                              className="inline-flex items-center px-3 py-1.5 bg-white text-yellow-700 text-sm rounded-md hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-white"
                            >
                              <RefreshCw size={14} className="mr-1.5" />
                              Coba Lagi
                            </button>
                          </div>
                        )}
                      </div>

                      {/* Download Buttons - Only show if no error */}
                      {!improvementResult.error && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          <button
                            type="button"
                            onClick={() => generateImprovementDoc(improvementResult, analysisResult)}
                            className="inline-flex items-center px-3 py-1.5 bg-white text-green-700 text-sm rounded-md hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-white"
                          >
                            <Download size={14} className="mr-1.5" />
                            Download DOC
                          </button>
                        </div>
                      )}
                    </div>

                    <div className="p-6 space-y-6">
                      {/* Summary */}
                      <div className={`bg-white rounded-lg p-4 shadow-sm ${improvementResult.error ? 'border-l-4 border-yellow-500' : ''}`}>
                        <h3 className={`text-lg font-semibold mb-2 ${improvementResult.error ? 'text-yellow-800' : 'text-green-800'}`}>
                          Ringkasan Perbaikan
                        </h3>
                        <div className="text-gray-700 whitespace-pre-line leading-relaxed">
                          {improvementResult.summary}
                        </div>
                      </div>

                      {/* Sections */}
                      {improvementResult.sections && improvementResult.sections.length > 0 && (
                        <div className="space-y-6">
                          <h3 className={`text-lg font-semibold ${improvementResult.error ? 'text-yellow-800' : 'text-green-800'}`}>
                            Bagian-Bagian yang Diperbaiki
                          </h3>

                          {improvementResult.sections.map((section, index) => (
                            <div key={index} className="bg-white rounded-lg shadow-sm overflow-hidden">
                              <div className={`${improvementResult.error ? 'bg-yellow-100 border-yellow-200' : 'bg-green-100 border-green-200'} px-4 py-2 border-b`}>
                                <h4 className={`font-medium ${improvementResult.error ? 'text-yellow-800' : 'text-green-800'}`}>
                                  {section.title}
                                </h4>
                              </div>

                              <div className="p-4 space-y-4">
                                <div>
                                  <h5 className="text-sm font-medium text-gray-500 mb-1">Konten Asli:</h5>
                                  <div className="bg-gray-50 p-3 rounded border border-gray-200 text-gray-700 whitespace-pre-line">
                                    {section.originalContent}
                                  </div>
                                </div>

                                <div className="flex items-center justify-center">
                                  <ArrowRight size={20} className={improvementResult.error ? 'text-yellow-500' : 'text-green-500'} />
                                </div>

                                <div>
                                  <h5 className="text-sm font-medium text-gray-500 mb-1">Konten yang Diperbaiki:</h5>
                                  <div className={`${improvementResult.error ? 'bg-yellow-50 border-yellow-200' : 'bg-green-50 border-green-200'} p-3 rounded border text-gray-700 whitespace-pre-line`}>
                                    {section.improvedContent}
                                  </div>
                                </div>

                                <div>
                                  <h5 className="text-sm font-medium text-gray-500 mb-1">Penjelasan Perbaikan:</h5>
                                  <div className="italic text-gray-600 whitespace-pre-line">
                                    {section.explanation}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Overall Improvements */}
                      {improvementResult.overallImprovements && (
                        <div className={`bg-white rounded-lg p-4 shadow-sm ${improvementResult.error ? 'border-l-4 border-yellow-500' : ''}`}>
                          <h3 className={`text-lg font-semibold mb-2 ${improvementResult.error ? 'text-yellow-800' : 'text-green-800'}`}>
                            Rekomendasi Perbaikan Keseluruhan
                          </h3>
                          <div className="text-gray-700 whitespace-pre-line leading-relaxed">
                            {improvementResult.overallImprovements}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <FileText size={64} className="mx-auto text-gray-300 mb-4" />
              <h2 className="text-xl font-semibold text-gray-700 mb-2">
                Belum Ada Analisis
              </h2>
              <p className="text-gray-500 mb-6">
                Unggah file proposal PDF dan klik tombol "Analisis Proposal" untuk mendapatkan analisis mendetail.
              </p>
              <p className="text-sm text-gray-400">
                Analisis akan memberikan skor, kekuatan, kelemahan, saran peningkatan, dan tips negosiasi untuk proposal Anda.
              </p>
            </div>
          )}
        </div>
      </div>
        </TabsContent>

        <TabsContent value="builder">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-2">Proposal Marketing Builder AI</h2>
              <p className="text-gray-600">
                Buat proposal marketing profesional dengan bantuan AI. Isi formulir di bawah ini untuk menghasilkan proposal yang disesuaikan dengan kebutuhan Anda.
              </p>
            </div>

            {generationError && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md flex items-start">
                <AlertCircle className="text-red-500 mr-2 mt-0.5 flex-shrink-0" size={16} />
                <p className="text-sm text-red-600">{generationError}</p>
              </div>
            )}

            {generatedProposal ? (
              <div className="mb-6 space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-medium text-green-800">Proposal Berhasil Dibuat!</h3>
                    <div className="bg-green-100 text-green-800 text-sm font-medium px-2.5 py-0.5 rounded-full">
                      Skor Kualitas: {qualityScore}/100
                    </div>
                  </div>
                  <p className="text-green-700 mb-4">
                    Proposal "{generatedProposal.proposalTitle}" telah berhasil dibuat dan siap untuk diunduh.
                  </p>
                  <div className="flex flex-wrap gap-2">
                    <a
                      href={generatedProposal.documentUrl}
                      download
                      className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                      <Download size={16} className="mr-2" />
                      Download DOCX
                    </a>
                    <button
                      onClick={() => {
                        setGeneratedProposal(null);
                        setQualityScore(null);
                      }}
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                      <RefreshCw size={16} className="mr-2" />
                      Buat Proposal Baru
                    </button>
                  </div>
                </div>

                {generatedProposal.previewHtml && (
                  <div className="border border-gray-200 rounded-md p-4">
                    <h3 className="text-lg font-medium mb-2">Preview Proposal</h3>
                    <div
                      className="prose max-w-none bg-white p-6 rounded-md border border-gray-200 overflow-auto max-h-[600px]"
                      dangerouslySetInnerHTML={{ __html: generatedProposal.previewHtml }}
                    />
                  </div>
                )}
              </div>
            ) : (
              <ProposalBuilderForm
                onSubmit={handleProposalBuilderSubmit}
                isSubmitting={isGeneratingProposal}
              />
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ProposalAnalyzerPage;
