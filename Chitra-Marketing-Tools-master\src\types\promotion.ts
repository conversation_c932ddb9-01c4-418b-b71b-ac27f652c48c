export interface PromoConfig {
  name: string;                // Nama internal promo
  type: PromoType;             // Tipe promo
  startDate: string;           // Tanggal mulai
  endDate: string;             // Tanggal akhir
  region: string;              // Wilayah/segmentasi
  additionalCosts: {
    marketing: number;         // Biaya marketing/promosi
    shipping: number;          // Biaya pengiriman
    other: number;             // Biaya lainnya
    description: string;       // Deskripsi biaya tambahan
  };
  customerId?: string;         // Referensi ke customer
  status?: PromoStatus;        // Status promo
  referenceFiles?: string[];   // File referensi promo
}

export enum PromoType {
  DISCOUNT_PERCENTAGE = 'DISCOUNT_PERCENTAGE',   // Diskon persentase
  DISCOUNT_FIXED = 'DISCOUNT_FIXED',             // Diskon nominal tetap
  CASHBACK = 'CASHBACK',                         // Cashback
  BUY_GET = 'BUY_GET',                           // Buy X Get Y
  BUNDLING = 'BUNDLING',                         // Bundling produk/jasa
  LOYALTY = 'LOYALTY',                           // Program loyalitas
  SEASONAL = 'SEASONAL',                         // Promo musiman
  CLEARANCE = 'CLEARANCE',                       // Clearance sale
  TRADE_IN = 'TRADE_IN',                         // Trade-in program
  VOLUME = 'VOLUME'                              // Diskon volume
}

export interface PromoItem {
  product: {
    id: string;
    oldMaterialNo: string;
    materialDescription: string;
    cost: number;              // Harga pokok (COGS) dari product management
    margin: number;            // Margin yang diinput oleh sales (persentase)
    sellingPrice: number;      // Harga jual (dihitung dari cost + margin)
    hasSpecialPrice?: boolean; // Flag if using special customer pricing
    regularPrice?: number;     // Regular price before customer pricing
  };
  quantity: number;            // Jumlah unit
  stock: number;               // Stok tersedia
  additionalItems?: {          // Tambahan barang/jasa
    name: string;
    cost: number;
    margin: number;
    sellingPrice: number;
  }[];
}

export interface PromoDiscount {
  type: DiscountType;          // Bentuk promo
  value: number;               // Nilai diskon (persentase atau nominal)
  targetSales: number;         // Estimasi penjualan
  marketingCost: number;       // Estimasi biaya promosi
}

export enum DiscountType {
  PERCENTAGE = 'PERCENTAGE',   // Diskon persentase
  FIXED_AMOUNT = 'FIXED_AMOUNT', // Diskon nominal tetap
  CASHBACK = 'CASHBACK',       // Cashback
  BONUS_UNIT = 'BONUS_UNIT'    // Bonus unit
}

export interface PromoResult {
  priceAfterPromo: number;     // Harga setelah promo
  totalDiscount: number;       // Total nilai diskon
  marginAfterPromo: number;    // Margin setelah promo (persentase)
  breakEvenPoint?: number;     // Break-even point (opsional)
  estimatedProfit: number;     // Estimasi laba bersih
  warning?: string;            // Peringatan jika margin terlalu rendah
  totalPromoCost?: number;     // Total biaya promosi (biaya promo + total diskon)
  promoCostPercentage?: number; // Persentase biaya promosi terhadap estimasi revenue
  profitPerUnit?: number;      // Profit per unit
  targetSalesForMargin?: number; // Target penjualan untuk mencapai margin 10%
  marginSafetyStatus?: 'safe' | 'thin' | 'unsafe'; // Status keamanan margin
}

export interface PromoSimulation {
  id?: string;                 // Unique ID for saved simulations
  name?: string;               // Optional name for saved simulations
  config: PromoConfig;
  items: PromoItem[];
  discount: PromoDiscount;
  result: PromoResult;
  createdAt?: Date;            // Creation date
  updatedAt?: Date;            // Last update date
  isTemplate?: boolean;        // Whether this is a template
}

export enum PromoStatus {
  DRAFT = 'DRAFT',                     // Draft
  PENDING_APPROVAL = 'PENDING_APPROVAL', // Menunggu Approval
  APPROVED = 'APPROVED',               // Disetujui
  SENT_TO_CUSTOMER = 'SENT_TO_CUSTOMER' // Dikirim ke Customer
}
