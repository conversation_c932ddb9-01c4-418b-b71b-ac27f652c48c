/**
 * Types for Social Media Marketing features
 */

/**
 * Social Media Platform types
 */
export enum SocialMediaPlatform {
  INSTAGRAM = 'instagram',
  FACEBOOK = 'facebook',
  LINKEDIN = 'linkedin',
  TIKTOK = 'tiktok',
  TWITTER = 'twitter'
}

/**
 * Content types for social media posts
 */
export enum ContentType {
  IMAGE = 'image',
  VIDEO = 'video',
  CAROUSEL = 'carousel',
  STORY = 'story',
  REEL = 'reel'
}

/**
 * Post status
 */
export enum PostStatus {
  DRAFT = 'draft',
  SCHEDULED = 'scheduled',
  PUBLISHED = 'published',
  ARCHIVED = 'archived'
}

/**
 * Social Media Post
 */
export interface SocialMediaPost {
  id: string;
  platform: SocialMediaPlatform;
  contentType: ContentType;
  title?: string;
  content?: string;        // Full content (for improved content from AI)
  caption: string;
  hashtags: string[];
  mediaUrls?: string[];
  imageDescription?: string;
  scheduledDate?: Date;
  publishedDate?: Date;
  status: PostStatus;
  engagement?: PostEngagement;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  targetAudience?: string;
  campaignId?: string;
  insightId?: string;
  metadata?: any;          // Additional metadata (for storing improvement details)
}

/**
 * Post Engagement metrics
 */
export interface PostEngagement {
  likes: number;
  comments: number;
  shares: number;
  saves: number;
  reach: number;
  impressions: number;
}

/**
 * Social Media Campaign
 */
export interface SocialMediaCampaign {
  id: string;
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  platforms: SocialMediaPlatform[];
  targetAudience: string;
  goals: string[];
  budget?: number;
  posts: SocialMediaPost[];
  status: 'active' | 'completed' | 'planned';
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Social Media Knowledge Base Entry
 */
export interface SocialMediaKnowledgeEntry {
  id: string;
  title: string;
  content: string;
  category: SocialMediaKnowledgeCategory;
  tags: string[];
  platform: SocialMediaPlatform;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  isActive: boolean;
}

/**
 * Social Media Knowledge Base categories
 */
export enum SocialMediaKnowledgeCategory {
  CONTENT_IDEAS = 'content_ideas',
  HASHTAG_STRATEGIES = 'hashtag_strategies',
  AUDIENCE_INSIGHTS = 'audience_insights',
  BEST_PRACTICES = 'best_practices',
  COMPETITOR_ANALYSIS = 'competitor_analysis',
  CASE_STUDIES = 'case_studies',
  TREND_ANALYSIS = 'trend_analysis'
}

/**
 * Content categories for varied Instagram posts
 */
export enum ContentCategory {
  PRODUCT = 'product',           // Product features, benefits, specifications
  SAFETY = 'safety',             // Safety tips, best practices
  EDUCATIONAL = 'educational',   // Educational content about tires, industry
  QUIZ = 'quiz',                 // Interactive quizzes, questions
  ENGAGEMENT = 'engagement',     // Posts designed for engagement (questions, polls)
  SERVICE = 'service',           // Service offerings, maintenance tips
  TESTIMONIAL = 'testimonial',   // Customer testimonials, success stories
  BEHIND_SCENES = 'behind_scenes', // Behind the scenes content
  INDUSTRY_NEWS = 'industry_news', // Industry news, updates
  SEASONAL = 'seasonal',         // Seasonal content, holiday-related
  PROMOTION = 'promotion'        // Special offers, promotions
}

/**
 * Instagram Calendar Day
 */
export interface InstagramCalendarDay {
  date: string; // ISO date string
  posts: SocialMediaPost[];
  notes?: string;
  isHoliday?: boolean;
  holidayName?: string;
  recommendationScore?: number; // 0-100 score for posting recommendation
}

/**
 * Instagram Calendar Month
 */
export interface InstagramCalendarMonth {
  year: number;
  month: number; // 1-12
  days: InstagramCalendarDay[];
}

/**
 * Content Generation Request
 */
export interface ContentGenerationRequest {
  platform: SocialMediaPlatform;
  contentType: ContentType;
  productDetails?: string;
  targetAudience?: string;
  campaignGoals?: string;
  tone?: string;
  length?: 'short' | 'medium' | 'long';
  includeHashtags?: boolean;
  includeEmojis?: boolean;
  language?: 'id' | 'en';
}

/**
 * Content Generation Response
 */
export interface ContentGenerationResponse {
  caption: string;
  hashtags: string[];
  suggestedImageDescription?: string;
}

/**
 * Hashtag Analysis
 */
export interface HashtagAnalysis {
  hashtag: string;
  popularity: number; // 0-100 score
  relevance: number; // 0-100 score
  competition: number; // 0-100 score
  recommendation: 'high' | 'medium' | 'low';
}

/**
 * Social Media Knowledge Base Entry Create Request
 */
export interface SocialMediaKnowledgeEntryCreateRequest {
  title: string;
  content: string;
  category: SocialMediaKnowledgeCategory;
  tags: string[];
  platform: SocialMediaPlatform;
  createdBy?: string;
}

/**
 * Social Media Knowledge Base Entry Update Request
 */
export interface SocialMediaKnowledgeEntryUpdateRequest {
  id: string;
  title?: string;
  content?: string;
  category?: SocialMediaKnowledgeCategory;
  tags?: string[];
  platform?: SocialMediaPlatform;
  isActive?: boolean;
}

/**
 * Social Media Knowledge Base Search Request
 */
export interface SocialMediaKnowledgeSearchRequest {
  query?: string;
  category?: SocialMediaKnowledgeCategory;
  platform?: SocialMediaPlatform;
  tags?: string[];
  isActive?: boolean;
}

/**
 * Social Media Knowledge Base Search Response
 */
export interface SocialMediaKnowledgeSearchResponse {
  entries: SocialMediaKnowledgeEntry[];
  total: number;
}

/**
 * Monthly Content Plan Post
 */
export interface MonthlyContentPlanPost {
  date: string;                  // ISO date string
  contentCategory: ContentCategory;
  contentType: ContentType;
  title: string;
  description: string;
  knowledgeBaseReference?: string; // ID of knowledge base entry if applicable
  recommendedHashtags?: string[];
  suggestedImageDescription?: string;
  stylePreset?: string; // Tambahan untuk preset gaya gambar
}

/**
 * Monthly Content Plan
 */
export interface MonthlyContentPlan {
  year: number;
  month: number;
  posts: MonthlyContentPlanPost[];
  categoryDistribution: Record<ContentCategory, number>; // Count of posts by category
}

/**
 * Monthly Content Plan Generation Request
 */
export interface MonthlyContentPlanRequest {
  year: number;
  month: number;
  postsPerWeek?: number;         // Default: 3
  preferredCategories?: ContentCategory[]; // Categories to prioritize
  excludedCategories?: ContentCategory[];  // Categories to exclude
  includeHolidays?: boolean;     // Whether to include holiday-specific content
  language?: 'id' | 'en';        // Default: 'id'
}
