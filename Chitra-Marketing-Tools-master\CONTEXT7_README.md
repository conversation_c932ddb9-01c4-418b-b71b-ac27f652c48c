# Context7 MCP Integration

This project is integrated with [Context7 MCP](https://github.com/upstash/context7), a powerful tool that provides up-to-date code documentation for LLMs and AI code editors.

## What is Context7?

Context7 MCP pulls up-to-date, version-specific documentation and code examples straight from the source and places them directly into your prompt. This helps solve common issues with LLMs:

- ✅ Get up-to-date code examples instead of outdated training data
- ✅ Access accurate APIs that actually exist
- ✅ Receive version-specific answers for your packages

## How to Use Context7

Simply add `use context7` to your prompt when asking for code-related assistance. For example:

```
Create a React component that fetches data from an API and displays it in a table. use context7
```

```
Help me implement a PostgreSQL query to find duplicate records. use context7
```

## Benefits for Chitra Marketing Tools

- **More accurate code generation**: Get code that works with our specific dependencies and versions
- **Better documentation access**: Access up-to-date documentation for all libraries we use
- **Reduced hallucinations**: Prevent AI from suggesting non-existent APIs or outdated methods
- **Faster development**: Spend less time fixing AI-generated code that doesn't work

## Requirements

- Node.js v18.0.0 or higher is required to use Context7

## Configuration

Context7 is configured in the `.vscode/settings.json` file. If you need to modify the configuration, you can edit this file.

## Troubleshooting

If you encounter issues with Context7:

1. Make sure you have Node.js v18+ installed
2. Try using `bunx` instead of `npx` if you see module resolution errors
3. Check the [official Context7 documentation](https://github.com/upstash/context7) for more troubleshooting tips
