import React from 'react';
import { PromoStatus } from '../types/promotion';
import { 
  ClipboardList, 
  Clock, 
  CheckCircle, 
  Send, 
  ArrowRight 
} from 'lucide-react';

interface PromoStatusFormProps {
  status: PromoStatus;
  onStatusChange: (status: PromoStatus) => void;
  onSendForReview: () => void;
}

const PromoStatusForm: React.FC<PromoStatusFormProps> = ({
  status,
  onStatusChange,
  onSendForReview
}) => {
  // Get status icon
  const getStatusIcon = (statusType: PromoStatus) => {
    switch (statusType) {
      case PromoStatus.DRAFT:
        return <ClipboardList size={16} className="text-gray-500" />;
      case PromoStatus.PENDING_APPROVAL:
        return <Clock size={16} className="text-yellow-500" />;
      case PromoStatus.APPROVED:
        return <CheckCircle size={16} className="text-green-500" />;
      case PromoStatus.SENT_TO_CUSTOMER:
        return <Send size={16} className="text-blue-500" />;
      default:
        return <ClipboardList size={16} className="text-gray-500" />;
    }
  };

  // Get status label
  const getStatusLabel = (statusType: PromoStatus) => {
    switch (statusType) {
      case PromoStatus.DRAFT:
        return 'Draft';
      case PromoStatus.PENDING_APPROVAL:
        return 'Menunggu Approval';
      case PromoStatus.APPROVED:
        return 'Disetujui';
      case PromoStatus.SENT_TO_CUSTOMER:
        return 'Dikirim ke Customer';
      default:
        return 'Draft';
    }
  };

  // Get status color
  const getStatusColor = (statusType: PromoStatus) => {
    switch (statusType) {
      case PromoStatus.DRAFT:
        return 'bg-gray-100 text-gray-800 border-gray-300';
      case PromoStatus.PENDING_APPROVAL:
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case PromoStatus.APPROVED:
        return 'bg-green-100 text-green-800 border-green-300';
      case PromoStatus.SENT_TO_CUSTOMER:
        return 'bg-blue-100 text-blue-800 border-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  // Handle status change
  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onStatusChange(e.target.value as PromoStatus);
  };

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold">Status Promo</h2>
      
      <div className="flex items-center space-x-4">
        <div className={`px-3 py-1 rounded-full border flex items-center space-x-1 ${getStatusColor(status)}`}>
          {getStatusIcon(status)}
          <span className="text-sm font-medium">{getStatusLabel(status)}</span>
        </div>
        
        <div className="flex-1">
          <select
            value={status}
            onChange={handleStatusChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value={PromoStatus.DRAFT}>Draft</option>
            <option value={PromoStatus.PENDING_APPROVAL}>Menunggu Approval</option>
            <option value={PromoStatus.APPROVED}>Disetujui</option>
            <option value={PromoStatus.SENT_TO_CUSTOMER}>Dikirim ke Customer</option>
          </select>
        </div>
      </div>
      
      <div className="flex justify-between">
        <button
          onClick={onSendForReview}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <ArrowRight size={16} className="mr-2" />
          Kirim untuk Review
        </button>
        
        <div className="text-sm text-gray-500">
          {status === PromoStatus.DRAFT && (
            <span>Promo masih dalam tahap draft dan dapat diedit.</span>
          )}
          {status === PromoStatus.PENDING_APPROVAL && (
            <span>Promo sedang menunggu persetujuan dari manager.</span>
          )}
          {status === PromoStatus.APPROVED && (
            <span>Promo telah disetujui dan siap dikirim ke customer.</span>
          )}
          {status === PromoStatus.SENT_TO_CUSTOMER && (
            <span>Promo telah dikirim ke customer.</span>
          )}
        </div>
      </div>
    </div>
  );
};

export default PromoStatusForm;
