import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  CheckCircle,
  AlertCircle,
  Instagram,
  Hash,
  Image,
  Bar<PERSON>hart,
  Loader2,
  Calendar,
  Save,
  Clock
} from 'lucide-react';
import {
  ContentGenerationRequest,
  ContentGenerationResponse,
  SocialMediaPlatform,
  ContentType,
  HashtagAnalysis
} from '../types/socialMedia';
import {
  generateInstagramContent,
  analyzeHashtags,
  createSocialMediaPost
} from '../services/socialMediaService';
import { CalendarDay, SeasonalInsight } from '../types/seasonalMarketing';
import { convertCalendarDataToContentRequest } from '../services/calendarContentService';

interface CalendarContentGeneratorProps {
  selectedDay?: CalendarDay;
  selectedInsight?: SeasonalInsight;
  onContentSaved?: () => void;
}

export default function CalendarContentGenerator({
  selectedDay,
  selectedInsight,
  onContentSaved
}: CalendarContentGeneratorProps) {
  // State for form inputs
  const [contentRequest, setContentRequest] = useState<ContentGenerationRequest>({
    platform: SocialMediaPlatform.INSTAGRAM,
    contentType: ContentType.IMAGE,
    productDetails: '',
    targetAudience: '',
    campaignGoals: '',
    tone: 'professional',
    length: 'medium',
    includeHashtags: true,
    includeEmojis: true,
    language: 'id'
  });
  
  // State for generated content
  const [generatedContent, setGeneratedContent] = useState<ContentGenerationResponse | null>(null);
  
  // State for hashtag analysis
  const [hashtagAnalysis, setHashtagAnalysis] = useState<HashtagAnalysis[]>([]);
  
  // Loading states
  const [isGenerating, setIsGenerating] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  // Notification state
  const [notification, setNotification] = useState<{
    message: string;
    type: 'success' | 'error';
    visible: boolean;
  }>({
    message: '',
    type: 'success',
    visible: false
  });

  // Update content request when selected day or insight changes
  useEffect(() => {
    if (selectedDay || selectedInsight) {
      const newRequest = convertCalendarDataToContentRequest(selectedDay, selectedInsight);
      setContentRequest(prev => ({
        ...prev,
        ...newRequest
      }));
    }
  }, [selectedDay, selectedInsight]);
  
  // Generate content
  const generateContent = async () => {
    if (!contentRequest.productDetails && !selectedInsight) {
      showNotification('Detail produk atau insight harus diisi', 'error');
      return;
    }
    
    setIsGenerating(true);
    try {
      const content = await generateInstagramContent(contentRequest);
      setGeneratedContent(content);
      
      // Automatically analyze hashtags if they exist
      if (content.hashtags && content.hashtags.length > 0) {
        analyzeContentHashtags(content.hashtags);
      }
      
      showNotification('Konten berhasil dibuat', 'success');
    } catch (error) {
      console.error('Error generating content:', error);
      showNotification('Gagal menghasilkan konten', 'error');
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Analyze hashtags
  const analyzeContentHashtags = async (hashtags: string[]) => {
    setIsAnalyzing(true);
    try {
      const analysis = await analyzeHashtags(hashtags);
      setHashtagAnalysis(analysis);
    } catch (error) {
      console.error('Error analyzing hashtags:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };
  
  // Save content as a social media post
  const saveAsPost = async () => {
    if (!generatedContent) {
      showNotification('Tidak ada konten untuk disimpan', 'error');
      return;
    }
    
    if (!selectedDay) {
      showNotification('Pilih tanggal untuk menyimpan konten', 'error');
      return;
    }
    
    setIsSaving(true);
    try {
      const post = await createSocialMediaPost({
        platform: SocialMediaPlatform.INSTAGRAM,
        contentType: contentRequest.contentType,
        caption: generatedContent.caption,
        hashtags: generatedContent.hashtags,
        imageDescription: generatedContent.suggestedImageDescription,
        scheduledDate: selectedDay.date,
        status: 'draft',
        insightId: selectedInsight?.id
      });
      
      showNotification('Konten berhasil disimpan ke kalender', 'success');
      
      // Call the callback if provided
      if (onContentSaved) {
        onContentSaved();
      }
    } catch (error) {
      console.error('Error saving post:', error);
      showNotification('Gagal menyimpan konten', 'error');
    } finally {
      setIsSaving(false);
    }
  };
  
  // Copy content to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    showNotification('Disalin ke clipboard', 'success');
  };
  
  // Show notification
  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({
      message,
      type,
      visible: true
    });
    
    // Hide notification after 3 seconds
    setTimeout(() => {
      setNotification(prev => ({ ...prev, visible: false }));
    }, 3000);
  };
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setContentRequest(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setContentRequest(prev => ({
      ...prev,
      [name]: checked
    }));
  };
  
  // Get recommendation color for hashtag analysis
  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'high':
        return 'text-green-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Notification */}
      {notification.visible && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-md shadow-md flex items-center ${
          notification.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
        }`}>
          {notification.type === 'success' ? (
            <CheckCircle className="h-5 w-5 mr-2" />
          ) : (
            <AlertCircle className="h-5 w-5 mr-2" />
          )}
          <span>{notification.message}</span>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Input Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-4 border-b border-gray-200 flex items-center">
            <Calendar size={20} className="text-blue-500 mr-2" />
            <h3 className="text-lg font-medium">Generator Konten dari Kalender</h3>
          </div>
          
          <div className="p-4 space-y-4">
            {selectedDay && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md mb-4">
                <div className="font-medium text-blue-800">Tanggal Terpilih:</div>
                <div className="text-blue-700">{new Date(selectedDay.date).toLocaleDateString('id-ID', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' })}</div>
                <div className="text-sm text-blue-600 mt-1">Skor: {selectedDay.score}/100</div>
              </div>
            )}
            
            {selectedInsight && (
              <div className="p-3 bg-purple-50 border border-purple-200 rounded-md mb-4">
                <div className="font-medium text-purple-800">Insight Terpilih:</div>
                <div className="text-purple-700">{selectedInsight.title}</div>
                <div className="text-sm text-purple-600 mt-1">{selectedInsight.description.substring(0, 100)}...</div>
              </div>
            )}
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tipe Konten</label>
              <select
                name="contentType"
                value={contentRequest.contentType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value={ContentType.IMAGE}>Gambar</option>
                <option value={ContentType.VIDEO}>Video</option>
                <option value={ContentType.CAROUSEL}>Carousel</option>
                <option value={ContentType.REEL}>Reel</option>
                <option value={ContentType.STORY}>Story</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Detail Produk</label>
              <textarea
                name="productDetails"
                value={contentRequest.productDetails}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Masukkan detail produk yang akan dipromosikan..."
              ></textarea>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Target Audiens</label>
              <input
                type="text"
                name="targetAudience"
                value={contentRequest.targetAudience}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Contoh: Perusahaan pertambangan, kontraktor alat berat..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tujuan Kampanye</label>
              <input
                type="text"
                name="campaignGoals"
                value={contentRequest.campaignGoals}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="Contoh: Meningkatkan awareness, promosi produk baru..."
              />
            </div>
            
            <div className="pt-2">
              <button
                onClick={generateContent}
                disabled={isGenerating}
                className="w-full flex justify-center items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <>
                    <Loader2 size={16} className="mr-2 animate-spin" />
                    Menghasilkan Konten...
                  </>
                ) : (
                  <>
                    <Sparkles size={16} className="mr-2" />
                    Hasilkan Konten dari Kalender
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
        
        {/* Generated Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-4 border-b border-gray-200 flex items-center">
            <Image size={20} className="text-blue-500 mr-2" />
            <h3 className="text-lg font-medium">Hasil Konten</h3>
          </div>
          
          {generatedContent ? (
            <div className="p-4 space-y-4">
              <div>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-700">Caption</h4>
                  <button
                    onClick={() => copyToClipboard(generatedContent.caption)}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title="Salin caption"
                  >
                    <Copy size={14} />
                  </button>
                </div>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm whitespace-pre-line">
                  {generatedContent.caption}
                </div>
              </div>
              
              <div>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-700">Hashtags</h4>
                  <button
                    onClick={() => copyToClipboard(generatedContent.hashtags.join(' '))}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title="Salin hashtags"
                  >
                    <Copy size={14} />
                  </button>
                </div>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                  <div className="flex flex-wrap gap-1">
                    {generatedContent.hashtags.map((tag, index) => (
                      <span key={index} className="inline-block px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
              
              {generatedContent.suggestedImageDescription && (
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <h4 className="text-sm font-medium text-gray-700">Deskripsi Gambar</h4>
                    <button
                      onClick={() => copyToClipboard(generatedContent.suggestedImageDescription || '')}
                      className="text-blue-600 hover:text-blue-800 p-1"
                      title="Salin deskripsi gambar"
                    >
                      <Copy size={14} />
                    </button>
                  </div>
                  <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {generatedContent.suggestedImageDescription}
                  </div>
                </div>
              )}
              
              {/* Save to Calendar button */}
              <div className="pt-2">
                <button
                  onClick={saveAsPost}
                  disabled={isSaving || !selectedDay}
                  className="w-full flex justify-center items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-green-300 disabled:cursor-not-allowed"
                >
                  {isSaving ? (
                    <>
                      <Loader2 size={16} className="mr-2 animate-spin" />
                      Menyimpan...
                    </>
                  ) : (
                    <>
                      <Save size={16} className="mr-2" />
                      Simpan ke Kalender
                    </>
                  )}
                </button>
                {!selectedDay && (
                  <p className="text-xs text-red-500 mt-1">Pilih tanggal di kalender untuk menyimpan konten</p>
                )}
              </div>
              
              {/* Schedule for later button */}
              <div>
                <button
                  onClick={() => {/* Schedule functionality */}}
                  className="w-full flex justify-center items-center px-4 py-2 border border-blue-500 text-blue-500 rounded-md hover:bg-blue-50"
                >
                  <Clock size={16} className="mr-2" />
                  Jadwalkan untuk Nanti
                </button>
              </div>
            </div>
          ) : (
            <div className="p-8 text-center">
              <Instagram size={48} className="mx-auto text-gray-300 mb-3" />
              <p className="text-gray-500">Isi form dan klik "Hasilkan Konten dari Kalender" untuk membuat konten</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
