<script setup lang="ts">
import { cn } from '@/lib/utils';
import { inject, computed } from 'vue';

const props = defineProps({
  value: {
    type: String,
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const select = inject('select', {
  value: { value: '' },
  updateValue: (value: string) => {},
});

const isSelected = computed(() => select.value.value === props.value);

const handleSelect = () => {
  if (!props.disabled) {
    select.updateValue(props.value);
  }
};
</script>

<template>
  <div
    :class="
      cn(
        'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground',
        {
          'bg-accent text-accent-foreground': isSelected,
          'opacity-50 cursor-not-allowed': disabled,
        }
      )
    "
    :data-state="isSelected ? 'checked' : 'unchecked'"
    :data-disabled="disabled ? '' : undefined"
    @click="handleSelect"
  >
    <span v-if="isSelected" class="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="h-4 w-4"
      >
        <polyline points="20 6 9 17 4 12"></polyline>
      </svg>
    </span>
    <slot></slot>
  </div>
</template>