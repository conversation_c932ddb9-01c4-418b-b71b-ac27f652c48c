<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Product Analysis</h1>
                    <p class="mt-2 text-gray-600">Product performance analysis, profitability insights, and inventory optimization</p>
                </div>
                <div class="flex space-x-3">
                    <select v-model="selectedCategory" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                        <option value="all">All Categories</option>
                        <option value="heavy-equipment">Heavy Equipment Tires</option>
                        <option value="mining">Mining Tires</option>
                        <option value="truck">Truck Tires</option>
                        <option value="passenger">Passenger Tires</option>
                        <option value="lubricants">Lubricants</option>
                        <option value="parts">Parts & Accessories</option>
                    </select>
                    <button
                        @click="generateForecast"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center"
                    >
                        <TrendingUp class="h-4 w-4 mr-2" />
                        Generate Forecast
                    </button>
                    <button
                        @click="exportAnalysis"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Analysis
                    </button>
                </div>
            </div>

            <!-- Product KPIs -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Package class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Products</p>
                            <p class="text-2xl font-bold text-gray-900">{{ productKPIs.totalProducts }}</p>
                            <p class="text-sm text-blue-600">{{ productKPIs.activeProducts }} active</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <DollarSign class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Avg Margin</p>
                            <p class="text-2xl font-bold text-gray-900">{{ productKPIs.avgMargin }}%</p>
                            <p class="text-sm text-green-600">+2.1% vs last quarter</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <RotateCcw class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Inventory Turnover</p>
                            <p class="text-2xl font-bold text-gray-900">{{ productKPIs.inventoryTurnover }}x</p>
                            <p class="text-sm text-purple-600">Per year</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Star class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Top Performers</p>
                            <p class="text-2xl font-bold text-gray-900">{{ productKPIs.topPerformers }}</p>
                            <p class="text-sm text-orange-600">Products (80% revenue)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Sales Trend -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Sales Trend by Category</h3>
                    <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                        <div class="text-center">
                            <BarChart3 class="h-12 w-12 text-gray-400 mx-auto mb-2" />
                            <p class="text-gray-600">Sales Trend Chart</p>
                            <p class="text-sm text-gray-500">Chart.js integration</p>
                        </div>
                    </div>
                </div>

                <!-- Profitability Analysis -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Profitability by Category</h3>
                    <div class="space-y-4">
                        <div v-for="category in categoryProfitability" :key="category.name" class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div :class="['w-4 h-4 rounded-full mr-3', category.color]"></div>
                                <div>
                                    <h4 class="font-medium text-gray-900">{{ category.name }}</h4>
                                    <p class="text-sm text-gray-600">{{ category.products }} products</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-gray-900">{{ category.margin }}%</p>
                                <p class="text-sm text-gray-500">{{ formatCurrency(category.revenue) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Products Table -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Product Performance Analysis</h3>
                    <div class="flex space-x-3">
                        <input
                            v-model="searchQuery"
                            type="text"
                            placeholder="Search products..."
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <button class="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center">
                            <Filter class="h-4 w-4 mr-2" />
                            Filter
                        </button>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Units Sold</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Margin</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Inventory</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trend</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="product in filteredProducts" :key="product.id">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-8 w-8 bg-gray-300 rounded flex items-center justify-center">
                                            <Package class="h-4 w-4 text-gray-600" />
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">{{ product.name }}</p>
                                            <p class="text-sm text-gray-500">{{ product.sku }}</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                        {{ product.category }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(product.revenue) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ product.unitsSold.toLocaleString() }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        product.margin >= 30 ? 'bg-green-100 text-green-800' :
                                        product.margin >= 20 ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-red-100 text-red-800'
                                    ]">
                                        {{ product.margin }}%
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-900 mr-2">{{ product.inventory }}</span>
                                        <span :class="[
                                            'px-1 py-0.5 text-xs rounded',
                                            product.inventoryStatus === 'Good' ? 'bg-green-100 text-green-800' :
                                            product.inventoryStatus === 'Low' ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-red-100 text-red-800'
                                        ]">
                                            {{ product.inventoryStatus }}
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <TrendingUp v-if="product.trend === 'up'" class="h-4 w-4 text-green-600" />
                                        <TrendingDown v-else-if="product.trend === 'down'" class="h-4 w-4 text-red-600" />
                                        <Minus v-else class="h-4 w-4 text-gray-600" />
                                        <span class="ml-1 text-sm text-gray-900">{{ product.trendValue }}%</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                    <button class="text-green-600 hover:text-green-900">Optimize</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Seasonal Analysis -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Seasonal Performance Patterns</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div v-for="season in seasonalData" :key="season.season" class="text-center p-4 bg-gray-50 rounded-lg">
                        <h4 class="font-medium text-gray-900">{{ season.season }}</h4>
                        <p class="text-2xl font-bold text-blue-600 mt-2">{{ season.performance }}%</p>
                        <p class="text-sm text-gray-600 mt-1">vs average</p>
                        <div class="mt-2">
                            <p class="text-xs text-gray-500">Top category:</p>
                            <p class="text-sm font-medium text-gray-900">{{ season.topCategory }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Product Optimization Recommendations</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Inventory Optimization</h4>
                        <div v-for="recommendation in inventoryRecommendations" :key="recommendation.id" 
                             class="flex items-start p-3 bg-yellow-50 rounded-lg">
                            <AlertTriangle class="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <p class="text-sm font-medium text-yellow-900">{{ recommendation.title }}</p>
                                <p class="text-sm text-yellow-700">{{ recommendation.description }}</p>
                                <p class="text-xs text-yellow-600 mt-1">Impact: {{ recommendation.impact }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Revenue Optimization</h4>
                        <div v-for="optimization in revenueOptimizations" :key="optimization.id" 
                             class="flex items-start p-3 bg-green-50 rounded-lg">
                            <Lightbulb class="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <p class="text-sm font-medium text-green-900">{{ optimization.title }}</p>
                                <p class="text-sm text-green-700">{{ optimization.description }}</p>
                                <p class="text-xs text-green-600 mt-1">Potential: {{ optimization.potential }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-indigo-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-indigo-900">Advanced Product Intelligence Coming Soon</h3>
                        <p class="text-indigo-700 mt-1">
                            Enhanced product analytics features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-indigo-700 mt-2 space-y-1">
                            <li>AI-powered demand forecasting and inventory optimization</li>
                            <li>Real-time competitor pricing analysis</li>
                            <li>Product lifecycle management and EOL predictions</li>
                            <li>Cross-selling and upselling opportunity identification</li>
                            <li>Dynamic pricing recommendations based on market conditions</li>
                            <li>Integration with supply chain and procurement systems</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    TrendingUp,
    Download,
    Package,
    DollarSign,
    RotateCcw,
    Star,
    BarChart3,
    Filter,
    TrendingDown,
    Minus,
    AlertTriangle,
    Lightbulb,
    Info
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Reactive state
const selectedCategory = ref('all');
const searchQuery = ref('');

// Product KPIs
const productKPIs = ref({
    totalProducts: 1847,
    activeProducts: 1623,
    avgMargin: 28.5,
    inventoryTurnover: 4.2,
    topPerformers: 147
});

// Category profitability
const categoryProfitability = ref([
    { name: 'Heavy Equipment Tires', products: 245, margin: 32.5, revenue: 8500000000, color: 'bg-blue-500' },
    { name: 'Mining Tires', products: 189, margin: 35.2, revenue: 6200000000, color: 'bg-green-500' },
    { name: 'Truck Tires', products: 312, margin: 25.8, revenue: 2800000000, color: 'bg-purple-500' },
    { name: 'Passenger Tires', products: 456, margin: 18.5, revenue: 1000000000, color: 'bg-orange-500' },
    { name: 'Lubricants', products: 89, margin: 42.1, revenue: 850000000, color: 'bg-red-500' },
    { name: 'Parts & Accessories', products: 556, margin: 38.7, revenue: 650000000, color: 'bg-yellow-500' }
]);

// Product data
const products = ref([
    { id: 1, name: '27.00 R 49 XD GRIP B E4T TL', sku: 'CHT-2749-001', category: 'Heavy Equipment', revenue: 2850000000, unitsSold: 1340, margin: 35.2, inventory: 89, inventoryStatus: 'Good', trend: 'up', trendValue: 12.5 },
    { id: 2, name: '24.00 R 35 XD GRIP B E4T TL', sku: 'CHT-2435-001', category: 'Mining', revenue: 2200000000, unitsSold: 1190, margin: 33.8, inventory: 156, inventoryStatus: 'Good', trend: 'up', trendValue: 8.3 },
    { id: 3, name: '21.00 R 33 XD GRIP B E4T TL', sku: 'CHT-2133-001', category: 'Mining', revenue: 1850000000, unitsSold: 1120, margin: 31.5, inventory: 45, inventoryStatus: 'Low', trend: 'stable', trendValue: 0.2 },
    { id: 4, name: 'Engine Oil SAE 15W-40 (20L)', sku: 'LUB-1540-020', category: 'Lubricants', revenue: 420000000, unitsSold: 4940, margin: 45.2, inventory: 2340, inventoryStatus: 'Good', trend: 'up', trendValue: 15.7 },
    { id: 5, name: 'Air Filter Heavy Duty', sku: 'PRT-AFLT-001', category: 'Parts', revenue: 180000000, unitsSold: 4000, margin: 38.5, inventory: 1200, inventoryStatus: 'Good', trend: 'down', trendValue: -5.2 },
    { id: 6, name: 'Oil Filter Premium', sku: 'PRT-OFLT-001', category: 'Parts', revenue: 128000000, unitsSold: 4000, margin: 42.1, inventory: 890, inventoryStatus: 'Good', trend: 'up', trendValue: 7.8 },
    { id: 7, name: '295/80R22.5 Truck Tire', sku: 'TRK-2958-001', category: 'Truck', revenue: 950000000, unitsSold: 3200, margin: 28.3, inventory: 234, inventoryStatus: 'Good', trend: 'stable', trendValue: 1.2 },
    { id: 8, name: 'Coolant Radiator (5L)', sku: 'LUB-COOL-005', category: 'Lubricants', revenue: 82500000, unitsSold: 3000, margin: 48.7, inventory: 567, inventoryStatus: 'Good', trend: 'up', trendValue: 9.4 },
    { id: 9, name: 'Brake Fluid DOT 4 (1L)', sku: 'LUB-BRAK-001', category: 'Lubricants', revenue: 37500000, unitsSold: 3000, margin: 52.3, inventory: 12, inventoryStatus: 'Critical', trend: 'down', trendValue: -12.1 },
    { id: 10, name: '185/65R15 Passenger Tire', sku: 'PSG-1865-001', category: 'Passenger', revenue: 185000000, unitsSold: 2500, margin: 22.1, inventory: 456, inventoryStatus: 'Good', trend: 'stable', trendValue: -0.8 }
]);

// Seasonal data
const seasonalData = ref([
    { season: 'Q1 (Jan-Mar)', performance: 95, topCategory: 'Heavy Equipment' },
    { season: 'Q2 (Apr-Jun)', performance: 108, topCategory: 'Mining Tires' },
    { season: 'Q3 (Jul-Sep)', performance: 112, topCategory: 'Truck Tires' },
    { season: 'Q4 (Oct-Dec)', performance: 125, topCategory: 'Heavy Equipment' }
]);

// Recommendations
const inventoryRecommendations = ref([
    {
        id: 1,
        title: 'Critical Stock Alert: Brake Fluid DOT 4',
        description: 'Only 12 units remaining, reorder immediately',
        impact: 'Prevent stockout'
    },
    {
        id: 2,
        title: 'Low Stock: 21.00 R 33 XD GRIP',
        description: '45 units remaining, below safety threshold',
        impact: 'Maintain service level'
    },
    {
        id: 3,
        title: 'Optimize Inventory Mix',
        description: 'Increase high-margin lubricants inventory',
        impact: '8% margin improvement'
    }
]);

const revenueOptimizations = ref([
    {
        id: 1,
        title: 'Bundle High-Margin Products',
        description: 'Create bundles with lubricants and filters',
        potential: '15% revenue increase'
    },
    {
        id: 2,
        title: 'Seasonal Pricing Strategy',
        description: 'Adjust pricing based on seasonal demand patterns',
        potential: '12% margin improvement'
    },
    {
        id: 3,
        title: 'Focus on Mining Segment',
        description: 'Expand mining tire portfolio (highest margins)',
        potential: '25% category growth'
    }
]);

// Computed properties
const filteredProducts = computed(() => {
    let filtered = products.value;

    if (selectedCategory.value !== 'all') {
        const categoryMap: Record<string, string> = {
            'heavy-equipment': 'Heavy Equipment',
            'mining': 'Mining',
            'truck': 'Truck',
            'passenger': 'Passenger',
            'lubricants': 'Lubricants',
            'parts': 'Parts'
        };
        filtered = filtered.filter(product =>
            product.category === categoryMap[selectedCategory.value]
        );
    }

    if (searchQuery.value) {
        filtered = filtered.filter(product =>
            product.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            product.sku.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
            product.category.toLowerCase().includes(searchQuery.value.toLowerCase())
        );
    }

    return filtered;
});

// Utility functions
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

// Event handlers
const generateForecast = () => {
    alert('Generating AI-powered demand forecast...\n\nAnalyzing:\n- Historical sales data\n- Seasonal patterns\n- Market trends\n- Economic indicators\n\nForecast will be ready in 3-5 minutes.');
};

const exportAnalysis = () => {
    try {
        const csvContent = [
            ['Product', 'SKU', 'Category', 'Revenue', 'Units Sold', 'Margin (%)', 'Inventory', 'Status', 'Trend'].join(','),
            ...filteredProducts.value.map(product => [
                `"${product.name}"`,
                product.sku,
                product.category,
                product.revenue,
                product.unitsSold,
                product.margin,
                product.inventory,
                product.inventoryStatus,
                `${product.trend} ${product.trendValue}%`
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `product_analysis_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Product analysis exported successfully!');
    } catch (error) {
        console.error('Error exporting analysis:', error);
        alert('Failed to export analysis.');
    }
};

// Initialize on mount
onMounted(() => {
    // Load product data
});
</script>
