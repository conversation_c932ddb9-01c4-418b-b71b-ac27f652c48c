  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Instagram size={20} className="text-pink-500 mr-2" />
          <h2 className="text-xl font-semibold">Daftar Konten Instagram</h2>
          <span className="ml-2 text-sm text-gray-500">({filteredPosts.length} konten)</span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleRefresh}
            className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
            disabled={refreshing}
            title="Refresh data"
          >
            <RefreshCw size={18} className={refreshing ? "animate-spin" : ""} />
          </button>
          <button
            onClick={() => setViewMode(viewMode === 'table' ? 'grid' : 'table')}
            className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
            title={viewMode === 'table' ? 'Tampilan grid' : 'Tampilan tabel'}
          >
            {viewMode === 'table' ? <Grid size={18} /> : <TableIcon size={18} />}
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="flex-1">
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Cari konten berdasarkan judul, caption, atau hashtag..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 w-full"
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <select
              value={postsPerPage}
              onChange={(e) => setPostsPerPage(Number(e.target.value))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              title="Jumlah konten per halaman"
            >
              <option value={5}>5 per halaman</option>
              <option value={10}>10 per halaman</option>
              <option value={20}>20 per halaman</option>
              <option value={50}>50 per halaman</option>
            </select>
          </div>
        </div>

        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as PostStatus | 'all')}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="all">Semua Status</option>
              <option value={PostStatus.PUBLISHED}>Dipublikasikan</option>
              <option value={PostStatus.SCHEDULED}>Terjadwal</option>
              <option value={PostStatus.DRAFT}>Draft</option>
              <option value={PostStatus.ARCHIVED}>Diarsipkan</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Tipe Konten</label>
            <select
              value={contentTypeFilter}
              onChange={(e) => setContentTypeFilter(e.target.value as ContentType | 'all')}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="all">Semua Tipe</option>
              <option value={ContentType.IMAGE}>Gambar</option>
              <option value={ContentType.VIDEO}>Video</option>
              <option value={ContentType.CAROUSEL}>Carousel</option>
              <option value={ContentType.REEL}>Reel</option>
              <option value={ContentType.STORY}>Story</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Urutkan Berdasarkan</label>
            <div className="flex items-center">
              <select
                value={sortField}
                onChange={(e) => setSortField(e.target.value as 'createdAt' | 'scheduledDate' | 'title')}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="createdAt">Tanggal Dibuat</option>
                <option value="scheduledDate">Tanggal Publikasi</option>
                <option value="title">Judul</option>
              </select>
              <button
                onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
                className="ml-2 p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                title={sortDirection === 'asc' ? 'Urutkan menurun' : 'Urutkan menaik'}
              >
                {sortDirection === 'asc' ? <SortAsc size={18} /> : <SortDesc size={18} />}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content List */}
      {posts.length === 0 && !loading ? (
        <ErrorFallback />
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2"></div>
              <p className="text-gray-500">Memuat data...</p>
            </div>
          ) : filteredPosts.length === 0 ? (
            <div className="p-8 text-center">
              <Instagram size={32} className="mx-auto text-gray-400 mb-2" />
              <p className="text-gray-500">Tidak ada konten yang ditemukan</p>
            </div>
          ) : viewMode === 'table' ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => handleSort('title')}
                      >
                        Konten
                        {sortField === 'title' && (
                          <span className="ml-1">
                            {sortDirection === 'asc' ? <SortAsc size={14} /> : <SortDesc size={14} />}
                          </span>
                        )}
                      </button>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tipe
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => handleSort('scheduledDate')}
                      >
                        Tanggal
                        {sortField === 'scheduledDate' && (
                          <span className="ml-1">
                            {sortDirection === 'asc' ? <SortAsc size={14} /> : <SortDesc size={14} />}
                          </span>
                        )}
                      </button>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => handleSort('createdAt')}
                      >
                        Dibuat
                        {sortField === 'createdAt' && (
                          <span className="ml-1">
                            {sortDirection === 'asc' ? <SortAsc size={14} /> : <SortDesc size={14} />}
                          </span>
                        )}
                      </button>
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {getPaginatedPosts().map((post) => (
                    <tr key={post.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-start">
                          <Instagram size={16} className="text-pink-500 mt-1 mr-2" />
                          <div>
                            <div className="font-medium text-gray-900">{post.title || 'Tanpa Judul'}</div>
                            <div className="text-sm text-gray-500 truncate max-w-xs">
                              {post.caption.length > 50 ? `${post.caption.substring(0, 50)}...` : post.caption}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getContentTypeIcon(post.contentType)}
                          <span className="ml-1 text-sm text-gray-900">
                            {post.contentType.charAt(0).toUpperCase() + post.contentType.slice(1)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(post.status)}
                          <span className="ml-1 text-sm text-gray-900">
                            {getStatusLabel(post.status)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {post.status === PostStatus.PUBLISHED
                          ? formatDate(post.publishedDate)
                          : formatDate(post.scheduledDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(post.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => {
                              setSelectedPost(post);
                              setShowPostModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900"
                            title="Lihat Detail"
                          >
                            <Eye size={16} />
                          </button>
                          {onEditPost && (
                            <button
                              onClick={() => onEditPost(post)}
                              className="text-yellow-600 hover:text-yellow-900"
                              title="Edit Konten"
                            >
                              <Edit size={16} />
                            </button>
                          )}
                          {onImproveContent && (
                            <button
                              onClick={() => onImproveContent(post)}
                              className="text-green-600 hover:text-green-900"
                              title="Tingkatkan Konten"
                            >
                              <Sparkles size={16} />
                            </button>
                          )}
                          <button
                            onClick={() => confirmDelete(post.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Hapus Konten"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getPaginatedPosts().map((post) => (
                  <div key={post.id} className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                    <div className="bg-gray-50 p-3 border-b flex justify-between items-center">
                      <div className="flex items-center">
                        <Instagram size={16} className="text-pink-500 mr-2" />
                        <span className="font-medium truncate max-w-[150px]">{post.title || 'Tanpa Judul'}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        {getContentTypeIcon(post.contentType)}
                        {getStatusIcon(post.status)}
                      </div>
                    </div>
                    <div className="p-4">
                      <div className="text-sm text-gray-600 mb-3 line-clamp-3 h-[4.5em]">
                        {post.caption}
                      </div>
                      <div className="flex flex-wrap gap-1 mb-3">
                        {post.hashtags.slice(0, 3).map((tag, index) => (
                          <span key={index} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            #{tag}
                          </span>
                        ))}
                        {post.hashtags.length > 3 && (
                          <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            +{post.hashtags.length - 3}
                          </span>
                        )}
                      </div>
                      <div className="flex justify-between items-center text-xs text-gray-500">
                        <div>
                          <Calendar size={12} className="inline mr-1" />
                          {post.status === PostStatus.PUBLISHED
                            ? formatDate(post.publishedDate)
                            : formatDate(post.scheduledDate)}
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setSelectedPost(post);
                              setShowPostModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900"
                            title="Lihat Detail"
                          >
                            <Eye size={14} />
                          </button>
                          {onEditPost && (
                            <button
                              onClick={() => onEditPost(post)}
                              className="text-yellow-600 hover:text-yellow-900"
                              title="Edit Konten"
                            >
                              <Edit size={14} />
                            </button>
                          )}
                          {onImproveContent && (
                            <button
                              onClick={() => onImproveContent(post)}
                              className="text-green-600 hover:text-green-900"
                              title="Tingkatkan Konten"
                            >
                              <Sparkles size={14} />
                            </button>
                          )}
                          <button
                            onClick={() => confirmDelete(post.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Hapus Konten"
                          >
                            <Trash2 size={14} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Pagination */}
          {filteredPosts.length > 0 && (
            <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Menampilkan <span className="font-medium">{(currentPage - 1) * postsPerPage + 1}</span> sampai{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * postsPerPage, filteredPosts.length)}
                    </span>{' '}
                    dari <span className="font-medium">{filteredPosts.length}</span> konten
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === 1
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Previous</span>
                      <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>

                    {/* Page numbers */}
                    {Array.from({ length: Math.ceil(filteredPosts.length / postsPerPage) }).map((_, index) => (
                      <button
                        key={index}
                        onClick={() => handlePageChange(index + 1)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          currentPage === index + 1
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {index + 1}
                      </button>
                    ))}

                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === Math.ceil(filteredPosts.length / postsPerPage)}
                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === Math.ceil(filteredPosts.length / postsPerPage)
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Next</span>
                      <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
