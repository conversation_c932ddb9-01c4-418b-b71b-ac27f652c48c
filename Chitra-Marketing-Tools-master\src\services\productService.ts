import { Product } from '../types';

// In-memory cache for products
let productsCache: Product[] | null = null;

// Sample data based on the existing mock products
export const mockProducts: Product[] = [
  {
    id: '1',
    oldMaterialNo: '226-10.00-20 GT',
    materialDescription: '10.00 - 20 GT MILLER',
    description: 'CK SEBAMBAN',
    price: 3157882
  },
  {
    id: '2',
    oldMaterialNo: '206-10.00R20-HN10',
    materialDescription: '10.00 R 20 TT HN10',
    description: 'Palembang',
    price: 3964627
  },
  {
    id: '3',
    oldMaterialNo: '206-11R22.5-HN08',
    materialDescription: '11 R 22.5 TL HN08',
    description: 'Jakarta',
    price: 2960168
  },
  {
    id: '4',
    oldMaterialNo: '206-11R22.5-HN10',
    materialDescription: '11 R 22.5 TL HN10',
    description: 'Jakarta',
    price: 3543140
  },
  {
    id: '5',
    oldMaterialNo: '200-078266',
    materialDescription: '11 R 22.5 XMZ 2 TL 148/145LVM MI',
    description: 'Pekanbaru',
    price: 5116178
  }
];

// Load products from localStorage
function loadProductsFromStorage(): Product[] {
  const storedProducts = localStorage.getItem('bundleBoostProducts');
  if (storedProducts) {
    try {
      return JSON.parse(storedProducts);
    } catch (e) {
      console.error('Error parsing stored products:', e);
    }
  }

  // Default to mockProducts if none in storage
  return mockProducts;
}

// Fetch all products with caching
export async function fetchProducts(forceRefresh = false): Promise<Product[]> {
  // Use cache if available, unless force refresh is requested
  if (!forceRefresh && productsCache) {
    console.log('Using cached products data');
    return productsCache;
  }

  // Load from localStorage
  console.log('Loading products from localStorage...');
  const products = loadProductsFromStorage();
  
  // Update cache
  productsCache = products;
  
  return products;
}

// Save all products
export async function saveProducts(products: Product[]): Promise<boolean> {
  try {
    console.log(`Saving ${products.length} products...`);
    
    // Save to localStorage
    localStorage.setItem('bundleBoostProducts', JSON.stringify(products));
    
    // Update cache
    productsCache = [...products];
    
    // Dispatch update event
    const updateEvent = new Event('productDataUpdated');
    window.dispatchEvent(updateEvent);
    
    console.log('Products saved successfully');
    return true;
  } catch (error) {
    console.error('Error saving products:', error);
    return false;
  }
}

// Update a single product
export async function updateProduct(product: Product): Promise<boolean> {
  try {
    console.log(`Updating product ${product.oldMaterialNo}...`);
    
    // Update in cache if it exists
    if (productsCache) {
      productsCache = productsCache.map(p => p.id === product.id ? product : p);
    }
    
    // Update in localStorage
    const products = loadProductsFromStorage();
    const updatedProducts = products.map(p => p.id === product.id ? product : p);
    localStorage.setItem('bundleBoostProducts', JSON.stringify(updatedProducts));
    
    // Dispatch update event
    const updateEvent = new Event('productDataUpdated');
    window.dispatchEvent(updateEvent);
    
    console.log(`Product ${product.oldMaterialNo} updated successfully`);
    return true;
  } catch (error) {
    console.error('Error updating product:', error);
    return false;
  }
}

// Delete a product
export async function deleteProduct(productId: string): Promise<boolean> {
  try {
    console.log(`Deleting product with ID ${productId}...`);
    
    // Update cache if it exists
    if (productsCache) {
      productsCache = productsCache.filter(p => p.id !== productId);
    }
    
    // Delete from localStorage
    const products = loadProductsFromStorage();
    const updatedProducts = products.filter(p => p.id !== productId);
    localStorage.setItem('bundleBoostProducts', JSON.stringify(updatedProducts));
    
    // Dispatch update event
    const updateEvent = new Event('productDataUpdated');
    window.dispatchEvent(updateEvent);
    
    console.log('Product deleted successfully');
    return true;
  } catch (error) {
    console.error('Error deleting product:', error);
    return false;
  }
}
