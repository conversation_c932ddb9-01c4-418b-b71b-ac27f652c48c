/**
 * Custom hooks for accessing data from the Data Hub
 */

import { useState, useEffect } from 'react';
import { useDataHub } from '../components/DataHubProvider';
import { Product } from '../types';
import { Customer } from '../types/customer';
import { FleetData } from '../services/fleetDataService';
import { FleetlistItem } from '../services/fleetlistService';
import { SocialMediaPost, SocialMediaKnowledgeEntry } from '../types/socialMedia';
import { CoalPrice, CoalPriceTrend } from '../services/coalPriceService';
import { Holiday } from '../services/holidayService';
import { CustomerPricing } from '../types/customerPricing';
import { PromoSimulation } from '../types/promotion';
import { DataChangeEvent, subscribeToDataChanges } from '../services/dataHubService';

/**
 * Hook for accessing products data
 */
export function useProducts(autoFetch = true) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<Error | null>(null);
  const { lastUpdated } = useDataHub();

  useEffect(() => {
    if (autoFetch) {
      fetchProducts();
    }
  }, [autoFetch, lastUpdated.products]);

  const fetchProducts = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const data = await dataHub.getProducts(forceRefresh);
      setProducts(data);
    } catch (err) {
      console.error('Error fetching products:', err);
      setError(err instanceof Error ? err : new Error('Unknown error fetching products'));
    } finally {
      setLoading(false);
    }
  };

  const saveProduct = async (product: Product) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const success = await dataHub.saveProduct(product);
      if (success) {
        // Update local state
        setProducts(prev => prev.map(p => p.id === product.id ? product : p));
      }
      return success;
    } catch (err) {
      console.error('Error saving product:', err);
      setError(err instanceof Error ? err : new Error('Unknown error saving product'));
      return false;
    } finally {
      setLoading(false);
    }
  };

  const saveProducts = async (newProducts: Product[]) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const success = await dataHub.saveProducts(newProducts);
      if (success) {
        setProducts(newProducts);
      }
      return success;
    } catch (err) {
      console.error('Error saving products:', err);
      setError(err instanceof Error ? err : new Error('Unknown error saving products'));
      return false;
    } finally {
      setLoading(false);
    }
  };

  const deleteProduct = async (productId: string) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const success = await dataHub.deleteProduct(productId);
      if (success) {
        // Update local state
        setProducts(prev => prev.filter(p => p.id !== productId));
      }
      return success;
    } catch (err) {
      console.error('Error deleting product:', err);
      setError(err instanceof Error ? err : new Error('Unknown error deleting product'));
      return false;
    } finally {
      setLoading(false);
    }
  };

  return { 
    products, 
    loading, 
    error, 
    fetchProducts,
    saveProduct,
    saveProducts,
    deleteProduct
  };
}

/**
 * Hook for accessing customers data
 */
export function useCustomers(autoFetch = true) {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<Error | null>(null);
  const { lastUpdated } = useDataHub();

  useEffect(() => {
    if (autoFetch) {
      fetchCustomers();
    }
  }, [autoFetch, lastUpdated.customers]);

  const fetchCustomers = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const data = dataHub.getCustomers(forceRefresh);
      setCustomers(data);
    } catch (err) {
      console.error('Error fetching customers:', err);
      setError(err instanceof Error ? err : new Error('Unknown error fetching customers'));
    } finally {
      setLoading(false);
    }
  };

  const getCustomerById = (id: string): Customer | undefined => {
    return customers.find(customer => customer.id === id);
  };

  const createCustomer = async (customerData: any) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const newCustomer = dataHub.createCustomer(customerData);
      // Update local state
      setCustomers(prev => [...prev, newCustomer]);
      return newCustomer;
    } catch (err) {
      console.error('Error creating customer:', err);
      setError(err instanceof Error ? err : new Error('Unknown error creating customer'));
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateCustomer = async (id: string, customerData: any) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const updatedCustomer = dataHub.updateCustomer(id, customerData);
      if (updatedCustomer) {
        // Update local state
        setCustomers(prev => prev.map(c => c.id === id ? updatedCustomer : c));
      }
      return updatedCustomer;
    } catch (err) {
      console.error('Error updating customer:', err);
      setError(err instanceof Error ? err : new Error('Unknown error updating customer'));
      return null;
    } finally {
      setLoading(false);
    }
  };

  const deleteCustomer = async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const success = dataHub.deleteCustomer(id);
      if (success) {
        // Update local state
        setCustomers(prev => prev.filter(c => c.id !== id));
      }
      return success;
    } catch (err) {
      console.error('Error deleting customer:', err);
      setError(err instanceof Error ? err : new Error('Unknown error deleting customer'));
      return false;
    } finally {
      setLoading(false);
    }
  };

  const searchCustomers = (query: string) => {
    if (!query) return customers;
    
    const lowerQuery = query.toLowerCase();
    return customers.filter(customer =>
      customer.name.toLowerCase().includes(lowerQuery) ||
      customer.email.toLowerCase().includes(lowerQuery) ||
      customer.phone.toLowerCase().includes(lowerQuery) ||
      (customer.company && customer.company.toLowerCase().includes(lowerQuery))
    );
  };

  return { 
    customers, 
    loading, 
    error, 
    fetchCustomers,
    getCustomerById,
    createCustomer,
    updateCustomer,
    deleteCustomer,
    searchCustomers
  };
}

/**
 * Hook for accessing fleet data
 */
export function useFleetData(autoFetch = true) {
  const [fleetData, setFleetData] = useState<FleetData[]>([]);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<Error | null>(null);
  const { lastUpdated } = useDataHub();

  useEffect(() => {
    if (autoFetch) {
      fetchFleetData();
    }
  }, [autoFetch, lastUpdated.fleetData]);

  const fetchFleetData = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const data = await dataHub.getFleetData(forceRefresh);
      setFleetData(data);
    } catch (err) {
      console.error('Error fetching fleet data:', err);
      setError(err instanceof Error ? err : new Error('Unknown error fetching fleet data'));
    } finally {
      setLoading(false);
    }
  };

  return { fleetData, loading, error, fetchFleetData };
}

/**
 * Hook for accessing coal prices data
 */
export function useCoalPrices(autoFetch = true) {
  const [coalPrices, setCoalPrices] = useState<CoalPrice[]>([]);
  const [coalTrend, setCoalTrend] = useState<CoalPriceTrend | null>(null);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<Error | null>(null);
  const { lastUpdated } = useDataHub();

  useEffect(() => {
    if (autoFetch) {
      fetchCoalPrices();
    }
  }, [autoFetch, lastUpdated.coalPrices]);

  const fetchCoalPrices = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const data = await dataHub.getCoalPrices(forceRefresh);
      setCoalPrices(data);
      
      // Also fetch trend data
      const trend = await dataHub.getCoalPriceTrend();
      setCoalTrend(trend);
    } catch (err) {
      console.error('Error fetching coal prices:', err);
      setError(err instanceof Error ? err : new Error('Unknown error fetching coal prices'));
    } finally {
      setLoading(false);
    }
  };

  return { coalPrices, coalTrend, loading, error, fetchCoalPrices };
}

/**
 * Hook for accessing holidays data
 */
export function useHolidays(autoFetch = true) {
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const [loading, setLoading] = useState(autoFetch);
  const [error, setError] = useState<Error | null>(null);
  const { lastUpdated } = useDataHub();

  useEffect(() => {
    if (autoFetch) {
      fetchHolidays();
    }
  }, [autoFetch, lastUpdated.holidays]);

  const fetchHolidays = async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);
      const dataHub = await import('../services/dataHubService');
      const data = await dataHub.getHolidays(forceRefresh);
      setHolidays(data);
    } catch (err) {
      console.error('Error fetching holidays:', err);
      setError(err instanceof Error ? err : new Error('Unknown error fetching holidays'));
    } finally {
      setLoading(false);
    }
  };

  return { holidays, loading, error, fetchHolidays };
}

export default {
  useProducts,
  useCustomers,
  useFleetData,
  useCoalPrices,
  useHolidays
};
