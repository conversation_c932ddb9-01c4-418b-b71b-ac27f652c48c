import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  MonthlyContentPlanPost, 
  ContentType 
} from '../types/socialMedia';
import { 
  VideoType, 
  VideoPurpose, 
  VideoTargetAudience, 
  VideoPlatform 
} from '../types/videoScript';
import { But<PERSON> } from './ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';
import { Separator } from './ui/separator';
import { Badge } from './ui/badge';
import { Video, ArrowRight } from 'lucide-react';

interface VideoScriptFromContentProps {
  post: MonthlyContentPlanPost;
  onClose?: () => void;
}

const VideoScriptFromContent: React.FC<VideoScriptFromContentProps> = ({ 
  post,
  onClose 
}) => {
  const navigate = useNavigate();
  
  // Map content category to video type
  const getVideoType = (): VideoType => {
    switch (post.contentCategory) {
      case 'product':
        return VideoType.MARKETING_PRODUCT;
      case 'educational':
        return VideoType.EDUCATIONAL;
      case 'testimonial':
        return VideoType.TESTIMONIAL;
      case 'behind_scenes':
        return VideoType.DOCUMENTATION;
      case 'quiz':
      case 'engagement':
        return VideoType.REELS;
      default:
        return VideoType.MARKETING_PRODUCT;
    }
  };
  
  // Map content type to video platform
  const getVideoPlatform = (): VideoPlatform => {
    switch (post.contentType) {
      case ContentType.REEL:
        return VideoPlatform.INSTAGRAM;
      case ContentType.VIDEO:
        return VideoPlatform.YOUTUBE;
      case ContentType.STORY:
        return VideoPlatform.INSTAGRAM;
      default:
        return VideoPlatform.INSTAGRAM;
    }
  };
  
  // Handle create script
  const handleCreateScript = () => {
    // Store content data in sessionStorage
    sessionStorage.setItem('content_to_script', JSON.stringify({
      videoType: getVideoType(),
      purpose: VideoPurpose.PROMOTION,
      targetAudience: VideoTargetAudience.CUSTOMER,
      platform: getVideoPlatform(),
      productName: '',
      additionalInfo: `${post.title}\n\n${post.description}`,
      relatedContentId: post.date // Using date as ID
    }));
    
    // Navigate to video script generator
    navigate('/video-script-generator');
    
    // Close modal if provided
    if (onClose) {
      onClose();
    }
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Video className="h-5 w-5 text-blue-600" />
          Buat Script Video
        </CardTitle>
        <CardDescription>
          Buat script video berdasarkan konten yang dipilih
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="text-lg font-medium mb-2">{post.title}</h3>
          <p className="text-gray-600 mb-4">{post.description}</p>
          
          <div className="flex flex-wrap gap-2 mb-4">
            <Badge variant="outline">{post.contentCategory}</Badge>
            <Badge variant="outline">{post.contentType}</Badge>
            <Badge variant="outline">{new Date(post.date).toLocaleDateString('id-ID')}</Badge>
          </div>
          
          <Separator className="my-4" />
          
          <div className="space-y-2">
            <p className="text-sm text-gray-600">
              Script video akan dibuat berdasarkan konten ini dengan:
            </p>
            <ul className="list-disc pl-5 space-y-1 text-sm">
              <li>Jenis Video: <span className="font-medium">{getVideoType()}</span></li>
              <li>Platform: <span className="font-medium">{getVideoPlatform()}</span></li>
              <li>Tujuan: <span className="font-medium">Promosi</span></li>
              <li>Target Audiens: <span className="font-medium">Customer</span></li>
            </ul>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end gap-2">
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            Batal
          </Button>
        )}
        <Button onClick={handleCreateScript}>
          Buat Script Video
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
};

export default VideoScriptFromContent;
