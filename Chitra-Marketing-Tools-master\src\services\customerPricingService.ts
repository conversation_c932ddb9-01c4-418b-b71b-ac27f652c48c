import { CustomerPricing, CustomerPricingFormData } from '../types/customerPricing';

// Mock data for customer pricing
const MOCK_CUSTOMER_PRICING: CustomerPricing[] = [
  {
    id: '1',
    customerId: '1',
    productId: '1',
    specialPrice: 2900000, // Special price for PT Maju Bersama for product 1
    validFrom: '2023-01-01',
    validTo: '2025-12-31',
    notes: 'Annual contract pricing',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    id: '2',
    customerId: '1',
    productId: '2',
    specialPrice: 3700000, // Special price for PT Maju Bersama for product 2
    validFrom: '2023-01-01',
    validTo: '2025-12-31',
    notes: 'Annual contract pricing',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    id: '3',
    customerId: '2',
    productId: '3',
    specialPrice: 2800000, // Special price for Bengkel <PERSON>jahtera for product 3
    validFrom: '2023-01-01',
    validTo: '2025-12-31',
    notes: 'Premium customer discount',
    createdAt: new Date('2023-02-15'),
    updatedAt: new Date('2023-02-15')
  }
];

// In-memory storage for customer pricing
let customerPricing: CustomerPricing[] = [...MOCK_CUSTOMER_PRICING];

/**
 * Get all customer pricing entries
 */
export const getAllCustomerPricing = (): CustomerPricing[] => {
  return [...customerPricing];
};

/**
 * Get customer pricing by ID
 */
export const getCustomerPricingById = (id: string): CustomerPricing | undefined => {
  return customerPricing.find(pricing => pricing.id === id);
};

/**
 * Get customer pricing by customer ID and product ID
 */
export const getCustomerPricingByCustomerAndProduct = (
  customerId: string,
  productId: string
): CustomerPricing | undefined => {
  const today = new Date().toISOString().split('T')[0];
  
  return customerPricing.find(
    pricing => 
      pricing.customerId === customerId && 
      pricing.productId === productId &&
      pricing.validFrom <= today &&
      pricing.validTo >= today
  );
};

/**
 * Get all pricing for a specific customer
 */
export const getCustomerPricingByCustomer = (customerId: string): CustomerPricing[] => {
  return customerPricing.filter(pricing => pricing.customerId === customerId);
};

/**
 * Create a new customer pricing entry
 */
export const createCustomerPricing = (pricingData: CustomerPricingFormData): CustomerPricing => {
  const newPricing: CustomerPricing = {
    id: Date.now().toString(), // Simple ID generation
    ...pricingData,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  customerPricing.push(newPricing);
  return newPricing;
};

/**
 * Update an existing customer pricing entry
 */
export const updateCustomerPricing = (
  id: string,
  pricingData: CustomerPricingFormData
): CustomerPricing | null => {
  const index = customerPricing.findIndex(pricing => pricing.id === id);
  
  if (index === -1) {
    return null;
  }
  
  const updatedPricing: CustomerPricing = {
    ...customerPricing[index],
    ...pricingData,
    updatedAt: new Date()
  };
  
  customerPricing[index] = updatedPricing;
  return updatedPricing;
};

/**
 * Delete a customer pricing entry
 */
export const deleteCustomerPricing = (id: string): boolean => {
  const initialLength = customerPricing.length;
  customerPricing = customerPricing.filter(pricing => pricing.id !== id);
  
  return customerPricing.length < initialLength;
};

/**
 * Get special price for a product and customer, or return regular price if no special price exists
 */
export const getEffectivePrice = (
  productId: string,
  regularPrice: number,
  customerId?: string
): number => {
  if (!customerId) {
    return regularPrice;
  }
  
  const specialPricing = getCustomerPricingByCustomerAndProduct(customerId, productId);
  
  if (specialPricing) {
    return specialPricing.specialPrice;
  }
  
  return regularPrice;
};
