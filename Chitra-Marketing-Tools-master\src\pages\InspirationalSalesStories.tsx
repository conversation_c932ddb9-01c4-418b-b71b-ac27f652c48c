import React from 'react';
import { Lightbulb } from 'lucide-react';

const InspirationalSalesStories: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Kumpulan Cerita Cara Berjualan Yang Inspiratif</h1>
        <p className="text-gray-600 mb-6">
          Berikut adalah 20 kisah inspiratif tentang cara unik berjualan yang terkenal dan dapat menjadi inspirasi untuk strategi penjualan Anda.
        </p>

        <div className="space-y-8">
          {/* Story 1 */}
          <div className="bg-amber-50 p-5 rounded-lg border border-amber-100">
            <div className="flex items-start">
              <Lightbulb className="text-amber-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-amber-800 mb-3">1. <PERSON><PERSON><PERSON><PERSON> dan Layanan Pelanggan Ekstrem</h2>
                <p className="text-gray-700 mb-3">
                  Zappos, perusahaan sepatu online, terkenal dengan layanan pelanggan yang luar biasa. Salah satu cerita paling terkenal adalah ketika seorang agen layanan pelanggan Zappos berbicara dengan pelanggan selama 10 jam 43 menit. Perusahaan juga pernah mengirimkan bunga ke pelanggan yang memesan sepatu untuk ibunya yang sedang sakit.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Layanan pelanggan yang luar biasa menciptakan loyalitas dan cerita yang disebarkan dari mulut ke mulut, yang jauh lebih berharga daripada iklan berbayar.
                </p>
              </div>
            </div>
          </div>

          {/* Story 2 */}
          <div className="bg-blue-50 p-5 rounded-lg border border-blue-100">
            <div className="flex items-start">
              <Lightbulb className="text-blue-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-blue-800 mb-3">2. Kisah Blendtec "Will It Blend?"</h2>
                <p className="text-gray-700 mb-3">
                  Blendtec, produsen blender, menciptakan kampanye viral "Will It Blend?" di mana CEO mereka, Tom Dickson, menghancurkan berbagai benda (termasuk iPhone, bola golf, dan tongkat glowstick) dalam blender mereka. Video-video ini menjadi viral dan meningkatkan penjualan Blendtec hingga 700%.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Demonstrasi produk yang kreatif dan menghibur dapat menarik perhatian dan membuktikan kualitas produk dengan cara yang tak terlupakan.
                </p>
              </div>
            </div>
          </div>

          {/* Story 3 */}
          <div className="bg-green-50 p-5 rounded-lg border border-green-100">
            <div className="flex items-start">
              <Lightbulb className="text-green-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-green-800 mb-3">3. Kisah Sukses TOMS Shoes</h2>
                <p className="text-gray-700 mb-3">
                  Blake Mycoskie mendirikan TOMS Shoes dengan model bisnis "One for One" - untuk setiap pasang sepatu yang dibeli, TOMS menyumbangkan sepasang sepatu kepada anak yang membutuhkan. Model ini tidak hanya menciptakan dampak sosial tetapi juga menjadi strategi penjualan yang kuat, membangun komunitas pelanggan yang loyal.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Menggabungkan misi sosial dengan bisnis dapat menciptakan proposisi nilai yang kuat dan membangun hubungan emosional dengan pelanggan.
                </p>
              </div>
            </div>
          </div>

          {/* Story 4 */}
          <div className="bg-purple-50 p-5 rounded-lg border border-purple-100">
            <div className="flex items-start">
              <Lightbulb className="text-purple-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-purple-800 mb-3">4. Kisah Airbnb dan Fotografi Profesional</h2>
                <p className="text-gray-700 mb-3">
                  Pada awal berdirinya, Airbnb mengalami kesulitan dengan pertumbuhan. Pendiri perusahaan menyadari bahwa foto-foto properti yang buruk adalah masalah utama. Mereka kemudian menyewa fotografer profesional untuk memotret properti di New York, dan pendapatan di kota tersebut langsung meningkat dua kali lipat.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Presentasi visual yang berkualitas tinggi dapat secara dramatis meningkatkan persepsi nilai dan kepercayaan pelanggan.
                </p>
              </div>
            </div>
          </div>

          {/* Story 5 */}
          <div className="bg-red-50 p-5 rounded-lg border border-red-100">
            <div className="flex items-start">
              <Lightbulb className="text-red-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-red-800 mb-3">5. Kisah Sukses Dollar Shave Club</h2>
                <p className="text-gray-700 mb-3">
                  Dollar Shave Club meluncurkan video viral berjudul "Our Blades Are F***ing Great" yang dibintangi oleh pendirinya, Michael Dubin. Video berdurasi 90 detik ini menghabiskan biaya hanya $4.500 untuk diproduksi, tetapi menghasilkan 12.000 pendaftar dalam 48 jam pertama dan akhirnya menjadi perusahaan bernilai $1 miliar.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Humor, keaslian, dan pesan yang jelas dapat menciptakan dampak besar tanpa anggaran pemasaran yang besar.
                </p>
              </div>
            </div>
          </div>

          {/* Story 6 */}
          <div className="bg-orange-50 p-5 rounded-lg border border-orange-100">
            <div className="flex items-start">
              <Lightbulb className="text-orange-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-orange-800 mb-3">6. Kisah Sukses Alibaba dan Hari Singles</h2>
                <p className="text-gray-700 mb-3">
                  Jack Ma, pendiri Alibaba, mengubah "Hari Singles" di Tiongkok (11/11) menjadi festival belanja terbesar di dunia. Pada tahun 2020, Alibaba mencatat penjualan sebesar $74,1 miliar dalam 24 jam. Strategi ini mengubah hari yang tadinya hanya dirayakan oleh orang lajang menjadi fenomena ritel global.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Mengidentifikasi dan memanfaatkan momen budaya dapat menciptakan peluang penjualan yang luar biasa.
                </p>
              </div>
            </div>
          </div>

          {/* Story 7 */}
          <div className="bg-teal-50 p-5 rounded-lg border border-teal-100">
            <div className="flex items-start">
              <Lightbulb className="text-teal-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-teal-800 mb-3">7. Kisah Sukses Penjual Keliling Joe Girard</h2>
                <p className="text-gray-700 mb-3">
                  Joe Girard, pemegang rekor Guinness World Record sebagai "penjual mobil terbesar di dunia," menjual 1.425 mobil dalam setahun, rata-rata lebih dari 6 mobil per hari. Salah satu strateginya adalah mengirimkan kartu ucapan bulanan kepada 13.000 pelanggan dengan pesan sederhana: "I Like You."
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Membangun hubungan personal dan konsisten dengan pelanggan dapat menciptakan basis pelanggan loyal yang menghasilkan referensi dan penjualan berulang.
                </p>
              </div>
            </div>
          </div>

          {/* Story 8 */}
          <div className="bg-indigo-50 p-5 rounded-lg border border-indigo-100">
            <div className="flex items-start">
              <Lightbulb className="text-indigo-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-indigo-800 mb-3">8. Kisah Sukses Warby Parker</h2>
                <p className="text-gray-700 mb-3">
                  Warby Parker merevolusi industri kacamata dengan program "Home Try-On" mereka, di mana pelanggan dapat memilih lima bingkai kacamata untuk dicoba di rumah selama lima hari tanpa biaya. Ini mengatasi kekhawatiran utama pembelian kacamata online dan membantu perusahaan mencapai valuasi $3 miliar.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Menghilangkan hambatan pembelian dan mengurangi risiko pelanggan dapat secara dramatis meningkatkan konversi penjualan.
                </p>
              </div>
            </div>
          </div>

          {/* Story 9 */}
          <div className="bg-pink-50 p-5 rounded-lg border border-pink-100">
            <div className="flex items-start">
              <Lightbulb className="text-pink-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-pink-800 mb-3">9. Kisah Sukses Penjual Buku Zig Ziglar</h2>
                <p className="text-gray-700 mb-3">
                  Zig Ziglar, salah satu motivator penjualan paling terkenal, memulai karirnya sebagai penjual panci. Dia terkenal dengan pendekatan "membantu orang lain mendapatkan apa yang mereka inginkan." Dalam satu kesempatan, dia menjual 14 set panci dalam satu hari dengan fokus pada manfaat bagi keluarga pelanggan, bukan fitur produk.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Fokus pada bagaimana produk Anda membantu pelanggan, bukan pada fitur teknis, dapat menciptakan koneksi emosional yang mendorong penjualan.
                </p>
              </div>
            </div>
          </div>

          {/* Story 10 */}
          <div className="bg-cyan-50 p-5 rounded-lg border border-cyan-100">
            <div className="flex items-start">
              <Lightbulb className="text-cyan-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-cyan-800 mb-3">10. Kisah Sukses Penjualan Domino's Pizza</h2>
                <p className="text-gray-700 mb-3">
                  Domino's Pizza menciptakan jaminan "30 menit atau gratis" yang merevolusi industri pengiriman pizza. Meskipun akhirnya dihentikan karena masalah keselamatan, strategi ini membangun reputasi Domino's sebagai pemimpin dalam pengiriman cepat dan membantu perusahaan tumbuh dari satu toko menjadi jaringan global.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Jaminan yang berani dapat mengurangi keraguan pelanggan dan menciptakan proposisi nilai yang jelas dan berbeda.
                </p>
              </div>
            </div>
          </div>

          {/* Story 11 */}
          <div className="bg-lime-50 p-5 rounded-lg border border-lime-100">
            <div className="flex items-start">
              <Lightbulb className="text-lime-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-lime-800 mb-3">11. Kisah Sukses Penjualan Tupperware</h2>
                <p className="text-gray-700 mb-3">
                  Brownie Wise, seorang ibu rumah tangga, menciptakan konsep "Tupperware Party" pada tahun 1950-an yang merevolusi penjualan langsung. Dengan mengubah demonstrasi produk menjadi acara sosial di rumah, dia membantu Tupperware menjadi nama rumah tangga dan menjadi wanita pertama yang muncul di sampul majalah Business Week.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Mengubah penjualan menjadi pengalaman sosial yang menyenangkan dapat mengurangi resistensi dan meningkatkan penjualan melalui tekanan rekan dan rekomendasi.
                </p>
              </div>
            </div>
          </div>

          {/* Story 12 */}
          <div className="bg-yellow-50 p-5 rounded-lg border border-yellow-100">
            <div className="flex items-start">
              <Lightbulb className="text-yellow-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-yellow-800 mb-3">12. Kisah Sukses Penjualan Ikea</h2>
                <p className="text-gray-700 mb-3">
                  IKEA merevolusi ritel furnitur dengan konsep "flat-pack" dan jalur satu arah yang memaksa pelanggan untuk melihat seluruh toko. Pendiri Ingvar Kamprad juga menciptakan "efek IKEA" - di mana pelanggan menghargai barang lebih tinggi ketika mereka merakitnya sendiri, meskipun ini berarti lebih banyak kerja bagi pelanggan.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Melibatkan pelanggan dalam proses dapat meningkatkan nilai yang dirasakan dan menciptakan pengalaman belanja yang unik dan berkesan.
                </p>
              </div>
            </div>
          </div>

          {/* Story 13 */}
          <div className="bg-emerald-50 p-5 rounded-lg border border-emerald-100">
            <div className="flex items-start">
              <Lightbulb className="text-emerald-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-emerald-800 mb-3">13. Kisah Sukses Penjualan Salesforce</h2>
                <p className="text-gray-700 mb-3">
                  Marc Benioff, pendiri Salesforce, meluncurkan kampanye "No Software" yang berani pada saat perangkat lunak masih didominasi oleh model instalasi lokal. Dia bahkan menyewa "demonstran" untuk protes di konferensi pesaing dengan tanda "No Software". Strategi ini membantu mendefinisikan kategori cloud computing dan memposisikan Salesforce sebagai pemimpin inovasi.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Mengambil posisi yang berani dan berbeda dapat membantu Anda menonjol di pasar yang ramai dan menarik perhatian.
                </p>
              </div>
            </div>
          </div>

          {/* Story 14 */}
          <div className="bg-rose-50 p-5 rounded-lg border border-rose-100">
            <div className="flex items-start">
              <Lightbulb className="text-rose-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-rose-800 mb-3">14. Kisah Sukses Penjualan Avon</h2>
                <p className="text-gray-700 mb-3">
                  Mrs. P.F.E. Albee menjadi "Avon Lady" pertama pada tahun 1886, menciptakan model penjualan langsung dari pintu ke pintu untuk produk kecantikan. Model ini memberdayakan wanita untuk mendapatkan penghasilan sendiri pada masa ketika peluang kerja bagi wanita sangat terbatas, dan membangun jaringan global dengan jutaan perwakilan.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Memberdayakan orang lain melalui model bisnis Anda dapat menciptakan tenaga penjualan yang bersemangat dan loyal yang mendorong pertumbuhan.
                </p>
              </div>
            </div>
          </div>

          {/* Story 15 */}
          <div className="bg-fuchsia-50 p-5 rounded-lg border border-fuchsia-100">
            <div className="flex items-start">
              <Lightbulb className="text-fuchsia-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-fuchsia-800 mb-3">15. Kisah Sukses Penjualan Michelin</h2>
                <p className="text-gray-700 mb-3">
                  Pada tahun 1900, ketika hanya ada 3.000 mobil di Prancis, perusahaan ban Michelin menciptakan Panduan Michelin - buku panduan perjalanan yang mendorong orang untuk mengendarai mobil (dan menggunakan ban) lebih banyak. Panduan ini berkembang menjadi sistem peringkat restoran paling bergengsi di dunia dan alat pemasaran yang brilian.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Menciptakan konten berharga yang secara tidak langsung mendorong penggunaan produk Anda dapat membangun otoritas dan mendorong penjualan jangka panjang.
                </p>
              </div>
            </div>
          </div>

          {/* Story 16 */}
          <div className="bg-sky-50 p-5 rounded-lg border border-sky-100">
            <div className="flex items-start">
              <Lightbulb className="text-sky-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-sky-800 mb-3">16. Kisah Sukses Penjualan GoPro</h2>
                <p className="text-gray-700 mb-3">
                  Nick Woodman, pendiri GoPro, membangun bisnis kamera aksi dengan memanfaatkan konten yang dibuat pengguna. Alih-alih mengandalkan iklan tradisional, GoPro mendorong pengguna untuk membagikan video menakjubkan mereka, menciptakan efek demonstrasi yang kuat dan komunitas penggemar yang bersemangat.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Membiarkan pelanggan Anda menjadi juru bicara produk Anda dapat menciptakan pemasaran yang lebih otentik dan meyakinkan daripada iklan tradisional.
                </p>
              </div>
            </div>
          </div>

          {/* Story 17 */}
          <div className="bg-violet-50 p-5 rounded-lg border border-violet-100">
            <div className="flex items-start">
              <Lightbulb className="text-violet-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-violet-800 mb-3">17. Kisah Sukses Penjualan Penjual Sepatu John Bates</h2>
                <p className="text-gray-700 mb-3">
                  John Bates, seorang penjual sepatu legendaris di toko Nordstrom, terkenal karena layanan pelanggan yang luar biasa. Dia mengirimkan kartu ucapan terima kasih tulisan tangan, mengingat preferensi pelanggan, dan bahkan pernah menemui pelanggan di bandara untuk memberikan sepatu yang mereka butuhkan untuk perjalanan bisnis.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Perhatian terhadap detail dan layanan personal yang melampaui ekspektasi dapat menciptakan loyalitas pelanggan seumur hidup dan penjualan berulang.
                </p>
              </div>
            </div>
          </div>

          {/* Story 18 */}
          <div className="bg-slate-50 p-5 rounded-lg border border-slate-100">
            <div className="flex items-start">
              <Lightbulb className="text-slate-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-slate-800 mb-3">18. Kisah Sukses Penjualan Penjual Asuransi Frank Bettger</h2>
                <p className="text-gray-700 mb-3">
                  Frank Bettger, penulis "How I Raised Myself from Failure to Success in Selling," mengubah karirnya dengan mengadopsi sikap antusias. Setelah gagal sebagai penjual asuransi, dia bereksperimen dengan menunjukkan lebih banyak antusiasme dalam presentasinya dan melihat hasil yang dramatis, akhirnya menjadi salah satu penjual asuransi paling sukses di Amerika.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Antusiasme yang tulus dan energi positif dapat menjadi pembeda besar dalam penjualan, menginspirasi kepercayaan dan minat pelanggan.
                </p>
              </div>
            </div>
          </div>

          {/* Story 19 */}
          <div className="bg-neutral-50 p-5 rounded-lg border border-neutral-100">
            <div className="flex items-start">
              <Lightbulb className="text-neutral-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-neutral-800 mb-3">19. Kisah Sukses Penjualan Penjual Mobil Ali Reda</h2>
                <p className="text-gray-700 mb-3">
                  Ali Reda, penjual mobil di Les Stanford Chevrolet di Dearborn, Michigan, memecahkan rekor penjualan mobil dalam setahun dengan menjual 1.582 kendaraan pada tahun 2017. Strateginya berfokus pada membangun hubungan jangka panjang dan jaringan referensi yang luas, serta memanfaatkan media sosial untuk tetap terhubung dengan pelanggan.
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Membangun jaringan referensi yang kuat dan memanfaatkan teknologi untuk memelihara hubungan dapat menciptakan aliran pelanggan yang konsisten.
                </p>
              </div>
            </div>
          </div>

          {/* Story 20 */}
          <div className="bg-stone-50 p-5 rounded-lg border border-stone-100">
            <div className="flex items-start">
              <Lightbulb className="text-stone-600 mt-1 mr-3 flex-shrink-0" size={24} />
              <div>
                <h2 className="text-xl font-semibold text-stone-800 mb-3">20. Kisah Sukses Penjualan Penjual Buku Elmer Wheeler</h2>
                <p className="text-gray-700 mb-3">
                  Elmer Wheeler, dikenal sebagai "penjual terbesar di Amerika," menciptakan frasa terkenal "Jangan jual steak, jual sizzle!" Dia berpendapat bahwa orang membeli karena emosi (sizzle) bukan logika (steak). Salah satu contoh klasiknya adalah ketika dia meningkatkan penjualan es krim di apotek dengan melatih pelayan untuk bertanya "Mau satu sendok atau dua?" alih-alih "Mau es krim?"
                </p>
                <p className="text-gray-700">
                  <span className="font-semibold">Pelajaran:</span> Cara Anda membingkai pilihan dan menggunakan bahasa dapat secara dramatis memengaruhi keputusan pembelian pelanggan.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InspirationalSalesStories;
