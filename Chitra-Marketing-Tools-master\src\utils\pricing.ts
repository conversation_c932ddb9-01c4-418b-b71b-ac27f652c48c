import { BundleItem, BundleConfig, PricingResult } from '../types';

export function calculatePricing(
  bundleItems: BundleItem[],
  config: BundleConfig
): PricingResult {
  // Calculate base cost from all products, using editedPrice if available
  const baseCost = bundleItems.reduce(
    (sum, item) => {
      // Check if item has an editedPrice property and use it if available
      const price = (item as any).editedPrice !== undefined ? 
        (item as any).editedPrice : 
        item.product.price;
      
      return sum + (price * item.quantity);
    },
    0
  );
  
  // Add additional costs
  const additionalCosts = config.additionalCosts.shipping + config.additionalCosts.other;
  const totalCost = baseCost + additionalCosts;
  
  // Calculate minimum profit based on margin percentage
  const minimumProfit = totalCost * (config.minimumMargin / 100);
  
  // Calculate recommended price
  const recommendedPrice = Math.ceil((totalCost + minimumProfit) / 1000) * 1000; // Round up to nearest 1000
  
  // Calculate actual profit and margin
  const profit = recommendedPrice - totalCost;
  const profitMargin = (profit / totalCost) * 100;
  
  return {
    totalCost,
    recommendedPrice,
    profit,
    profitMargin
  };
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
}
