/**
 * Accessibility utilities for better user experience
 */

/**
 * Focus management utilities
 */
export class FocusManager {
    private focusStack: HTMLElement[] = [];

    /**
     * Trap focus within a container
     */
    trapFocus(container: HTMLElement): () => void {
        const focusableElements = this.getFocusableElements(container);
        
        if (focusableElements.length === 0) return () => {};

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        // Focus first element
        firstElement.focus();

        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key !== 'Tab') return;

            if (event.shiftKey) {
                // Shift + Tab
                if (document.activeElement === firstElement) {
                    event.preventDefault();
                    lastElement.focus();
                }
            } else {
                // Tab
                if (document.activeElement === lastElement) {
                    event.preventDefault();
                    firstElement.focus();
                }
            }
        };

        container.addEventListener('keydown', handleKeyDown);

        // Return cleanup function
        return () => {
            container.removeEventListener('keydown', handleKeyDown);
        };
    }

    /**
     * Get all focusable elements within a container
     */
    getFocusableElements(container: HTMLElement): HTMLElement[] {
        const focusableSelectors = [
            'button:not([disabled])',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',
            'a[href]',
            '[tabindex]:not([tabindex="-1"])',
            '[contenteditable="true"]'
        ].join(', ');

        return Array.from(container.querySelectorAll(focusableSelectors))
            .filter(el => this.isVisible(el as HTMLElement)) as HTMLElement[];
    }

    /**
     * Check if element is visible
     */
    private isVisible(element: HTMLElement): boolean {
        const style = window.getComputedStyle(element);
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               style.opacity !== '0';
    }

    /**
     * Save current focus and restore later
     */
    saveFocus(): () => void {
        const activeElement = document.activeElement as HTMLElement;
        this.focusStack.push(activeElement);

        return () => {
            const elementToFocus = this.focusStack.pop();
            if (elementToFocus && elementToFocus.focus) {
                elementToFocus.focus();
            }
        };
    }
}

/**
 * Screen reader announcements
 */
export class ScreenReaderAnnouncer {
    private liveRegion: HTMLElement | null = null;

    constructor() {
        this.createLiveRegion();
    }

    /**
     * Create ARIA live region for announcements
     */
    private createLiveRegion(): void {
        if (typeof document === 'undefined') return;

        this.liveRegion = document.createElement('div');
        this.liveRegion.setAttribute('aria-live', 'polite');
        this.liveRegion.setAttribute('aria-atomic', 'true');
        this.liveRegion.style.position = 'absolute';
        this.liveRegion.style.left = '-10000px';
        this.liveRegion.style.width = '1px';
        this.liveRegion.style.height = '1px';
        this.liveRegion.style.overflow = 'hidden';
        
        document.body.appendChild(this.liveRegion);
    }

    /**
     * Announce message to screen readers
     */
    announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
        if (!this.liveRegion) return;

        this.liveRegion.setAttribute('aria-live', priority);
        this.liveRegion.textContent = message;

        // Clear after announcement
        setTimeout(() => {
            if (this.liveRegion) {
                this.liveRegion.textContent = '';
            }
        }, 1000);
    }
}

/**
 * Keyboard navigation utilities
 */
export class KeyboardNavigation {
    /**
     * Handle arrow key navigation for lists
     */
    static handleArrowNavigation(
        event: KeyboardEvent,
        items: HTMLElement[],
        currentIndex: number,
        onIndexChange: (index: number) => void
    ): void {
        let newIndex = currentIndex;

        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                newIndex = (currentIndex + 1) % items.length;
                break;
            case 'ArrowUp':
                event.preventDefault();
                newIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
                break;
            case 'Home':
                event.preventDefault();
                newIndex = 0;
                break;
            case 'End':
                event.preventDefault();
                newIndex = items.length - 1;
                break;
            default:
                return;
        }

        onIndexChange(newIndex);
        items[newIndex]?.focus();
    }

    /**
     * Handle escape key to close modals/dropdowns
     */
    static handleEscape(event: KeyboardEvent, onEscape: () => void): void {
        if (event.key === 'Escape') {
            event.preventDefault();
            onEscape();
        }
    }
}

/**
 * Color contrast utilities
 */
export class ColorContrast {
    /**
     * Calculate relative luminance
     */
    static getRelativeLuminance(color: string): number {
        const rgb = this.hexToRgb(color);
        if (!rgb) return 0;

        const [r, g, b] = [rgb.r, rgb.g, rgb.b].map(c => {
            c = c / 255;
            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
        });

        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    }

    /**
     * Calculate contrast ratio between two colors
     */
    static getContrastRatio(color1: string, color2: string): number {
        const lum1 = this.getRelativeLuminance(color1);
        const lum2 = this.getRelativeLuminance(color2);
        
        const brightest = Math.max(lum1, lum2);
        const darkest = Math.min(lum1, lum2);
        
        return (brightest + 0.05) / (darkest + 0.05);
    }

    /**
     * Check if contrast meets WCAG standards
     */
    static meetsWCAG(color1: string, color2: string, level: 'AA' | 'AAA' = 'AA'): boolean {
        const ratio = this.getContrastRatio(color1, color2);
        return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
    }

    /**
     * Convert hex to RGB
     */
    private static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }
}

/**
 * ARIA utilities
 */
export class AriaUtils {
    /**
     * Generate unique ID for ARIA relationships
     */
    static generateId(prefix: string = 'aria'): string {
        return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Set ARIA expanded state
     */
    static setExpanded(element: HTMLElement, expanded: boolean): void {
        element.setAttribute('aria-expanded', expanded.toString());
    }

    /**
     * Set ARIA selected state
     */
    static setSelected(element: HTMLElement, selected: boolean): void {
        element.setAttribute('aria-selected', selected.toString());
    }

    /**
     * Set ARIA pressed state for toggle buttons
     */
    static setPressed(element: HTMLElement, pressed: boolean): void {
        element.setAttribute('aria-pressed', pressed.toString());
    }

    /**
     * Set ARIA describedby relationship
     */
    static setDescribedBy(element: HTMLElement, describerId: string): void {
        element.setAttribute('aria-describedby', describerId);
    }

    /**
     * Set ARIA labelledby relationship
     */
    static setLabelledBy(element: HTMLElement, labelId: string): void {
        element.setAttribute('aria-labelledby', labelId);
    }
}

// Global instances
export const focusManager = new FocusManager();
export const screenReader = new ScreenReaderAnnouncer();

/**
 * Vue composable for accessibility
 */
export function useAccessibility() {
    const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
        screenReader.announce(message, priority);
    };

    const trapFocus = (container: HTMLElement) => {
        return focusManager.trapFocus(container);
    };

    const saveFocus = () => {
        return focusManager.saveFocus();
    };

    return {
        announceToScreenReader,
        trapFocus,
        saveFocus,
        AriaUtils,
        KeyboardNavigation
    };
}

/**
 * Accessibility testing utilities
 */
export function runAccessibilityChecks(): void {
    if (typeof document === 'undefined' || !import.meta.env.DEV) return;

    console.group('♿ Accessibility Checks');

    // Check for missing alt text
    const images = document.querySelectorAll('img:not([alt])');
    if (images.length > 0) {
        console.warn(`Found ${images.length} images without alt text:`, images);
    }

    // Check for missing form labels
    const inputs = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
    const unlabeledInputs = Array.from(inputs).filter(input => {
        const id = input.getAttribute('id');
        return !id || !document.querySelector(`label[for="${id}"]`);
    });
    
    if (unlabeledInputs.length > 0) {
        console.warn(`Found ${unlabeledInputs.length} inputs without labels:`, unlabeledInputs);
    }

    // Check for missing heading hierarchy
    const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'));
    let previousLevel = 0;
    
    headings.forEach(heading => {
        const level = parseInt(heading.tagName.charAt(1));
        if (level > previousLevel + 1) {
            console.warn(`Heading hierarchy skip detected: ${heading.tagName} after h${previousLevel}`, heading);
        }
        previousLevel = level;
    });

    console.groupEnd();
}

// Auto-run accessibility checks in development
if (typeof window !== 'undefined' && import.meta.env.DEV) {
    window.addEventListener('load', () => {
        setTimeout(runAccessibilityChecks, 1000);
    });
}
