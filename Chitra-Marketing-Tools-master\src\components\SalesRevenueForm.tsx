import React, { useState } from 'react';
import { SalesRevenueItem } from '../services/salesRevenue2025Service';

interface SalesRevenueFormProps {
  item: Partial<SalesRevenueItem>;
  onSubmit: (item: Partial<SalesRevenueItem>) => void;
  onCancel: () => void;
  isNew: boolean;
}

const SalesRevenueForm: React.FC<SalesRevenueFormProps> = ({
  item,
  onSubmit,
  onCancel,
  isNew
}) => {
  const [formData, setFormData] = useState<Partial<SalesRevenueItem>>(item);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    
    // Handle different input types
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: value === '' ? 0 : Number(value)
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">
        {isNew ? 'Add New Sales Revenue Item' : 'Edit Sales Revenue Item'}
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Customer Name
          </label>
          <input
            type="text"
            name="customerName"
            value={formData.customerName || ''}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Salesman
          </label>
          <input
            type="text"
            name="salesman"
            value={formData.salesman || ''}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Material Description
          </label>
          <input
            type="text"
            name="materialDescription"
            value={formData.materialDescription || ''}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
            required
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Quantity
          </label>
          <input
            type="number"
            name="qty"
            value={formData.qty || 0}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
            required
            min="0"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Revenue in Doc Curr
          </label>
          <input
            type="number"
            name="revenueInDocCurr"
            value={formData.revenueInDocCurr || 0}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
            required
            min="0"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Billing Date
          </label>
          <input
            type="date"
            name="billingDate"
            value={formData.billingDate || ''}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            PO Date
          </label>
          <input
            type="date"
            name="poDate"
            value={formData.poDate || ''}
            onChange={handleChange}
            className="w-full p-2 border rounded-md"
          />
        </div>
      </div>
      
      <div className="flex justify-end space-x-3 mt-6">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          {isNew ? 'Create' : 'Update'}
        </button>
      </div>
    </form>
  );
};

export default SalesRevenueForm;
