/**
 * Types for Image Generator feature using Runware.ai
 */

/**
 * Task types for Runware API
 */
export enum TaskType {
  IMAGE_INFERENCE = 'imageInference',
  PHOTO_MAKER = 'photoMaker'
}

/**
 * Available image models
 */
export enum ImageModel {
  JUGGERNAUT_PRO = 'rundiffusion:130@100',
  JUGGERNAUT_LIGHTNING = 'rundiffusion:110@101',
  JUGGERNAUT_XL_V8 = 'civitai:133005@288982',
  AIRTIST_REALISTIC_XL = 'civitai:241492@486179',
  ANIME = 'civitai:56519@60938',
  TWO_D = 'civitai:150227@167895',
  CAMPAIGN_POSTER = 'civitai:334033@374192',
  REALISTIC_VISION = 'civitai:4201@130072',
  DREAMSHAPER = 'civitai:4384@128713',
  OPENJOURNEY = 'civitai:4384@128713',
  ANYTHING_V5 = 'civitai:9405@140088',
  CHILLOUTMIX = 'civitai:6424@130072',
  DELIBERATE = 'civitai:4823@128713'
}

/**
 * Available PhotoMaker models
 */
export enum PhotoMakerModel {
  REALISM_ENGINE_SDXL = 'civitai:152525@293240',
  JUGGERNAUT_XL_V4 = 'civitai:139562@344487',
  SDXL = 'civitai:101055@128078',
  DREAMSHAPER_XL = 'civitai:112902@126688',
  JUGGERNAUT_XL_V8 = 'civitai:133005@288982',
  JUGGERNAUT_XL_XI = 'civitai:133005@782002',
  JUGGERNAUT_XL_X = 'civitai:133005@456194',
  JUGGERNAUT_XL_V7 = 'civitai:133005@240840',
  JUGGERNAUT_XL_V6 = 'civitai:133005@198530',
  JUGGERNAUT_XL_V9 = 'civitai:133005@348913',
  JUGGERNAUT_XL_X_HYPER = 'civitai:133005@471120'
}

/**
 * Image size options
 */
export interface ImageSize {
  width: number;
  height: number;
  label: string;
}

/**
 * Available image sizes
 */
export const IMAGE_SIZES: ImageSize[] = [
  { width: 1024, height: 1536, label: '1024×1536 (Portrait 2:3)' },
  { width: 768, height: 1280, label: '768×1280 (Portrait 3:5)' },
  { width: 512, height: 768, label: '512×768 (Portrait 2:3)' },
  { width: 1024, height: 1024, label: '1024×1024 (1:1)' },
  { width: 1536, height: 1024, label: '1536×1024 (Landscape 3:2)' }
];

/**
 * Image generation request
 */
export interface ImageGenerationRequest {
  positivePrompt: string;
  negativePrompt?: string;
  width: number;
  height: number;
  model: string;
  numberResults: number;
  steps?: number;
  cfgScale?: number;
  sampler?: string;
  seed?: number;
  stylePreset?: string;
  enhancePrompt?: boolean;
}

/**
 * PhotoMaker request
 */
export interface PhotoMakerRequest {
  inputImages: string[];  // Base64 encoded images
  style?: string;
  strength?: number;  // Integer value between 15 and 50
  positivePrompt: string;
  negativePrompt?: string;
  height: number;
  width: number;
  model: string;
  scheduler?: string;
  steps?: number;
  CFGScale?: number;
  outputFormat?: string;
  includeCost?: boolean;
  numberResults: number;
}

/**
 * Available PhotoMaker styles
 * These values must exactly match what the Runware API expects
 */
export enum PhotoMakerStyle {
  NO_STYLE = 'No style',
  CINEMATIC = 'Cinematic',
  DISNEY_CHARACTER = 'Disney Character',
  DIGITAL_ART = 'Digital Art',
  PHOTOGRAPHIC = 'Photographic',
  FANTASY_ART = 'Fantasy art',
  NEONPUNK = 'Neonpunk',
  ENHANCE = 'Enhance',
  COMIC_BOOK = 'Comic book',
  LOWPOLY = 'Lowpoly',
  LINE_ART = 'Line art'
}

/**
 * Generated image result
 */
export interface GeneratedImage {
  imageUUID: string;
  imageURL: string;
  prompt: string;
  model: string;
  width: number;
  height: number;
  generatedAt: Date;
}

/**
 * Saved image in history
 */
export interface SavedImage extends GeneratedImage {
  id: string;
  tags?: string[];
}

/**
 * Image generation history
 */
export interface ImageGenerationHistory {
  images: SavedImage[];
}

/**
 * Custom model definition
 */
export interface CustomModel {
  id: string;
  name: string;
  value: string;
  isDefault?: boolean;
}

export const SAMPLERS = [
  'Euler a',
  'Euler',
  'LMS',
  'Heun',
  'DPM2',
  'DPM++ 2S a',
  'DPM++ 2M',
  'DPM++ SDE',
  'DPM fast',
  'DPM adaptive',
  'LMS Karras',
  'DPM2 Karras',
  'DPM++ 2S a Karras',
  'DPM++ 2M Karras',
  'DPM++ SDE Karras',
  'DDIM',
  'PLMS'
];

export const STYLE_PRESETS = [
  'None',
  'Cinematic',
  'Photographic',
  'Digital Art',
  'Anime',
  'Comic Book',
  'Fantasy Art',
  'Neonpunk',
  'Lowpoly',
  'Line Art',
  '3D Model',
  'Pixel Art',
  'Watercolor',
  'Oil Painting',
  'Sketch'
];
