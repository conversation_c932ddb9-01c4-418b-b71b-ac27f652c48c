<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Marketing Insights</h1>
                    <p class="mt-2 text-gray-600">AI-powered marketing analytics and strategic insights</p>
                </div>
                <div class="flex space-x-3">
                    <select v-model="selectedTimeframe" class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500">
                        <option value="30days">Last 30 Days</option>
                        <option value="90days">Last 90 Days</option>
                        <option value="6months">Last 6 Months</option>
                        <option value="12months">Last 12 Months</option>
                    </select>
                    <button
                        @click="generateInsights"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center"
                    >
                        <Brain class="h-4 w-4 mr-2" />
                        Generate AI Insights
                    </button>
                    <button
                        @click="exportInsights"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Report
                    </button>
                </div>
            </div>

            <!-- Key Performance Indicators -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <TrendingUp class="h-8 w-8 text-green-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Marketing ROI</p>
                            <p class="text-2xl font-bold text-gray-900">{{ marketingKPIs.roi }}%</p>
                            <p class="text-sm text-green-600">+{{ marketingKPIs.roiGrowth }}% vs last period</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Users class="h-8 w-8 text-blue-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Lead Generation</p>
                            <p class="text-2xl font-bold text-gray-900">{{ marketingKPIs.leads.toLocaleString() }}</p>
                            <p class="text-sm text-blue-600">{{ marketingKPIs.leadGrowth }}% increase</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <Target class="h-8 w-8 text-purple-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Conversion Rate</p>
                            <p class="text-2xl font-bold text-gray-900">{{ marketingKPIs.conversionRate }}%</p>
                            <p class="text-sm text-purple-600">{{ marketingKPIs.conversionGrowth }}% improvement</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <DollarSign class="h-8 w-8 text-orange-600" />
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Cost per Lead</p>
                            <p class="text-2xl font-bold text-gray-900">{{ formatCurrency(marketingKPIs.costPerLead) }}</p>
                            <p class="text-sm text-orange-600">{{ marketingKPIs.cplChange }}% decrease</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Channel Performance -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Marketing Channels -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Channel Performance</h3>
                    <div class="space-y-4">
                        <div v-for="channel in channelPerformance" :key="channel.name" class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div :class="['w-4 h-4 rounded-full mr-3', channel.color]"></div>
                                <div>
                                    <h4 class="font-medium text-gray-900">{{ channel.name }}</h4>
                                    <p class="text-sm text-gray-600">{{ channel.leads }} leads</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-gray-900">{{ channel.roi }}%</p>
                                <p class="text-sm text-gray-500">ROI</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Campaign Performance -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Campaigns</h3>
                    <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                        <div class="text-center">
                            <BarChart3 class="h-12 w-12 text-gray-400 mx-auto mb-2" />
                            <p class="text-gray-600">Campaign Performance Chart</p>
                            <p class="text-sm text-gray-500">Chart.js integration</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Insights -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">AI-Powered Insights</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Key Findings</h4>
                        <div v-for="finding in keyFindings" :key="finding.id" class="flex items-start p-3 bg-blue-50 rounded-lg">
                            <Lightbulb class="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <p class="text-sm font-medium text-blue-900">{{ finding.title }}</p>
                                <p class="text-sm text-blue-700">{{ finding.description }}</p>
                                <p class="text-xs text-blue-600 mt-1">Impact: {{ finding.impact }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">Recommendations</h4>
                        <div v-for="recommendation in recommendations" :key="recommendation.id" class="flex items-start p-3 bg-green-50 rounded-lg">
                            <CheckCircle class="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <p class="text-sm font-medium text-green-900">{{ recommendation.title }}</p>
                                <p class="text-sm text-green-700">{{ recommendation.description }}</p>
                                <p class="text-xs text-green-600 mt-1">Expected: {{ recommendation.expected }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Market Trends -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Market Trends & Opportunities</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div v-for="trend in marketTrends" :key="trend.id" class="p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center mb-3">
                            <component :is="trend.icon" class="h-6 w-6 text-blue-600 mr-3" />
                            <h4 class="font-medium text-gray-900">{{ trend.title }}</h4>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">{{ trend.description }}</p>
                        <div class="flex justify-between items-center">
                            <span :class="[
                                'px-2 py-1 text-xs font-medium rounded-full',
                                trend.impact === 'High' ? 'bg-red-100 text-red-800' :
                                trend.impact === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-green-100 text-green-800'
                            ]">
                                {{ trend.impact }} Impact
                            </span>
                            <span class="text-xs text-gray-500">{{ trend.timeframe }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Journey Analysis -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Journey Analysis</h3>
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div v-for="stage in customerJourney" :key="stage.name" class="text-center">
                        <div class="w-16 h-16 mx-auto mb-3 rounded-full flex items-center justify-center"
                             :class="stage.color">
                            <component :is="stage.icon" class="h-8 w-8 text-white" />
                        </div>
                        <h4 class="font-medium text-gray-900 mb-1">{{ stage.name }}</h4>
                        <p class="text-2xl font-bold text-gray-900 mb-1">{{ stage.conversion }}%</p>
                        <p class="text-sm text-gray-600">{{ stage.count.toLocaleString() }} users</p>
                        <p class="text-xs text-gray-500 mt-1">{{ stage.avgTime }}</p>
                    </div>
                </div>
            </div>

            <!-- Competitive Analysis -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Competitive Landscape</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Competitor</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Market Share</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Social Presence</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Content Strategy</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Opportunity</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="competitor in competitors" :key="competitor.name">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-gray-700">{{ competitor.initials }}</span>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">{{ competitor.name }}</p>
                                            <p class="text-sm text-gray-500">{{ competitor.category }}</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-blue-600 h-2 rounded-full" :style="{ width: competitor.marketShare + '%' }"></div>
                                        </div>
                                        <span class="text-sm text-gray-900">{{ competitor.marketShare }}%</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ competitor.socialFollowers.toLocaleString() }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        competitor.contentStrategy === 'Strong' ? 'bg-green-100 text-green-800' :
                                        competitor.contentStrategy === 'Moderate' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-red-100 text-red-800'
                                    ]">
                                        {{ competitor.contentStrategy }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ competitor.opportunity }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-purple-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-purple-900">Advanced Marketing Intelligence Coming Soon</h3>
                        <p class="text-purple-700 mt-1">
                            Enhanced marketing analytics features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-purple-700 mt-2 space-y-1">
                            <li>Real-time market sentiment analysis</li>
                            <li>Predictive customer behavior modeling</li>
                            <li>Automated campaign optimization</li>
                            <li>Advanced attribution modeling</li>
                            <li>Competitive intelligence automation</li>
                            <li>Marketing mix modeling and budget optimization</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Brain,
    Download,
    TrendingUp,
    Users,
    Target,
    DollarSign,
    BarChart3,
    Lightbulb,
    CheckCircle,
    Eye,
    MousePointer,
    ShoppingCart,
    UserCheck,
    Award,
    Info
} from 'lucide-vue-next';
import { ref, onMounted } from 'vue';

// Reactive state
const selectedTimeframe = ref('90days');

// Marketing KPIs
const marketingKPIs = ref({
    roi: 285,
    roiGrowth: 15.2,
    leads: 1247,
    leadGrowth: 23.5,
    conversionRate: 12.8,
    conversionGrowth: 8.7,
    costPerLead: 125000,
    cplChange: -12.3
});

// Channel performance
const channelPerformance = ref([
    { name: 'Social Media', leads: 456, roi: 320, color: 'bg-blue-500' },
    { name: 'Google Ads', leads: 389, roi: 275, color: 'bg-green-500' },
    { name: 'Email Marketing', leads: 234, roi: 410, color: 'bg-purple-500' },
    { name: 'Content Marketing', leads: 168, roi: 195, color: 'bg-orange-500' }
]);

// Key findings
const keyFindings = ref([
    {
        id: 1,
        title: 'Social Media ROI Surge',
        description: 'Instagram campaigns showing 45% higher conversion rates than Facebook',
        impact: '23% revenue increase'
    },
    {
        id: 2,
        title: 'Mobile Traffic Dominance',
        description: '78% of leads now come from mobile devices, up from 62% last quarter',
        impact: 'Mobile optimization critical'
    },
    {
        id: 3,
        title: 'Video Content Performance',
        description: 'Video content generates 3x more engagement than static images',
        impact: '40% higher lead quality'
    }
]);

// Recommendations
const recommendations = ref([
    {
        id: 1,
        title: 'Increase Video Content Budget',
        description: 'Allocate 60% of content budget to video production and promotion',
        expected: '35% engagement boost'
    },
    {
        id: 2,
        title: 'Optimize Mobile Experience',
        description: 'Implement mobile-first landing pages and faster load times',
        expected: '25% conversion improvement'
    },
    {
        id: 3,
        title: 'Expand Instagram Advertising',
        description: 'Shift 30% of Facebook ad budget to Instagram campaigns',
        expected: '20% cost reduction'
    }
]);

// Market trends
const marketTrends = ref([
    {
        id: 1,
        title: 'Digital Transformation',
        description: 'Mining companies increasingly adopting digital solutions',
        impact: 'High',
        timeframe: 'Next 6 months',
        icon: TrendingUp
    },
    {
        id: 2,
        title: 'Sustainability Focus',
        description: 'Growing demand for eco-friendly tire solutions',
        impact: 'Medium',
        timeframe: 'Next 12 months',
        icon: Target
    },
    {
        id: 3,
        title: 'Remote Monitoring',
        description: 'IoT and remote tire monitoring gaining traction',
        impact: 'High',
        timeframe: 'Next 18 months',
        icon: Eye
    }
]);

// Customer journey
const customerJourney = ref([
    { name: 'Awareness', conversion: 100, count: 10000, avgTime: '2.5 days', icon: Eye, color: 'bg-blue-500' },
    { name: 'Interest', conversion: 45, count: 4500, avgTime: '5.2 days', icon: MousePointer, color: 'bg-green-500' },
    { name: 'Consideration', conversion: 28, count: 2800, avgTime: '12.8 days', icon: Target, color: 'bg-yellow-500' },
    { name: 'Purchase', conversion: 12, count: 1200, avgTime: '8.5 days', icon: ShoppingCart, color: 'bg-orange-500' },
    { name: 'Loyalty', conversion: 8, count: 800, avgTime: '45 days', icon: Award, color: 'bg-purple-500' }
]);

// Competitors
const competitors = ref([
    {
        name: 'Bridgestone Indonesia',
        initials: 'BI',
        category: 'Global Brand',
        marketShare: 35,
        socialFollowers: 125000,
        contentStrategy: 'Strong',
        opportunity: 'Technical content gap'
    },
    {
        name: 'Michelin Indonesia',
        initials: 'MI',
        category: 'Premium Brand',
        marketShare: 28,
        socialFollowers: 89000,
        contentStrategy: 'Strong',
        opportunity: 'Local market focus'
    },
    {
        name: 'Goodyear Indonesia',
        initials: 'GI',
        category: 'Established Brand',
        marketShare: 22,
        socialFollowers: 67000,
        contentStrategy: 'Moderate',
        opportunity: 'Digital engagement'
    },
    {
        name: 'Local Competitors',
        initials: 'LC',
        category: 'Local Brands',
        marketShare: 15,
        socialFollowers: 23000,
        contentStrategy: 'Weak',
        opportunity: 'Quality positioning'
    }
]);

// Utility functions
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

// Event handlers
const generateInsights = () => {
    alert('Generating AI-powered marketing insights...\n\nAnalyzing:\n- Campaign performance data\n- Customer behavior patterns\n- Market trends and opportunities\n- Competitive landscape\n\nInsights will be ready in 3-5 minutes.');
};

const exportInsights = () => {
    try {
        const insightsData = {
            timeframe: selectedTimeframe.value,
            kpis: marketingKPIs.value,
            channels: channelPerformance.value,
            findings: keyFindings.value,
            recommendations: recommendations.value,
            trends: marketTrends.value,
            competitors: competitors.value,
            generatedAt: new Date().toISOString()
        };

        const csvContent = [
            ['Marketing Insights Report'].join(','),
            ['Generated:', new Date().toLocaleDateString('id-ID')].join(','),
            ['Timeframe:', selectedTimeframe.value].join(','),
            [],
            ['Key Performance Indicators'].join(','),
            ['Metric', 'Value', 'Growth'].join(','),
            ['Marketing ROI', `${marketingKPIs.value.roi}%`, `${marketingKPIs.value.roiGrowth}%`].join(','),
            ['Lead Generation', marketingKPIs.value.leads, `${marketingKPIs.value.leadGrowth}%`].join(','),
            ['Conversion Rate', `${marketingKPIs.value.conversionRate}%`, `${marketingKPIs.value.conversionGrowth}%`].join(','),
            ['Cost per Lead', formatCurrency(marketingKPIs.value.costPerLead), `${marketingKPIs.value.cplChange}%`].join(','),
            [],
            ['Channel Performance'].join(','),
            ['Channel', 'Leads', 'ROI'].join(','),
            ...channelPerformance.value.map(channel => [
                channel.name,
                channel.leads,
                `${channel.roi}%`
            ].join(',')),
            [],
            ['Key Findings'].join(','),
            ['Finding', 'Description', 'Impact'].join(','),
            ...keyFindings.value.map(finding => [
                `"${finding.title}"`,
                `"${finding.description}"`,
                `"${finding.impact}"`
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `marketing_insights_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Marketing insights report exported successfully!');
    } catch (error) {
        console.error('Error exporting insights:', error);
        alert('Failed to export insights report.');
    }
};

// Initialize on mount
onMounted(() => {
    // Load marketing insights data
});
</script>
