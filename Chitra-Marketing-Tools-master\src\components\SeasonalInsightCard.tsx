import React from 'react';
import {
  <PERSON>,
  Sparkles,
  Target,
  TrendingUp,
  Al<PERSON><PERSON>riangle,
  CheckCircle,
  Clock,
  ThumbsUp,
  ThumbsDown,
  Tag,
  Zap,
  Package,
  Brain,
  MessageSquare
} from 'lucide-react';
import {
  SeasonalInsight,
  RecommendationLevel,
  SeasonalFactor
} from '../types/seasonalMarketing';

interface SeasonalInsightCardProps {
  insight: SeasonalInsight;
  onSchedule?: () => void;
  onGenerateContent?: () => void;
}

export default function SeasonalInsightCard({
  insight,
  onSchedule,
  onGenerateContent
}: SeasonalInsightCardProps) {
  // Format date range
  const formatDateRange = (startDate: string, endDate: string): string => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    return `${start.toLocaleDateString('id-ID', { day: 'numeric', month: 'long' })} - ${end.toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' })}`;
  };

  // Get color for recommendation level
  const getRecommendationColor = (level: RecommendationLevel): string => {
    switch (level) {
      case RecommendationLevel.EXCELLENT:
        return 'bg-green-50 border-green-200 text-green-800';
      case RecommendationLevel.GOOD:
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case RecommendationLevel.NEUTRAL:
        return 'bg-gray-50 border-gray-200 text-gray-800';
      case RecommendationLevel.POOR:
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case RecommendationLevel.AVOID:
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  // Get icon for recommendation level
  const getRecommendationIcon = (level: RecommendationLevel) => {
    switch (level) {
      case RecommendationLevel.EXCELLENT:
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case RecommendationLevel.GOOD:
        return <ThumbsUp className="h-5 w-5 text-blue-600" />;
      case RecommendationLevel.NEUTRAL:
        return <Clock className="h-5 w-5 text-gray-600" />;
      case RecommendationLevel.POOR:
        return <ThumbsDown className="h-5 w-5 text-yellow-600" />;
      case RecommendationLevel.AVOID:
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  // Get text for recommendation level
  const getRecommendationText = (level: RecommendationLevel): string => {
    switch (level) {
      case RecommendationLevel.EXCELLENT:
        return 'Sangat Direkomendasikan';
      case RecommendationLevel.GOOD:
        return 'Direkomendasikan';
      case RecommendationLevel.NEUTRAL:
        return 'Netral';
      case RecommendationLevel.POOR:
        return 'Kurang Direkomendasikan';
      case RecommendationLevel.AVOID:
        return 'Tidak Direkomendasikan';
      default:
        return 'Netral';
    }
  };

  return (
    <div className={`p-4 rounded-lg border ${getRecommendationColor(insight.recommendationLevel)}`}>
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-center">
          <Sparkles className="h-5 w-5 text-blue-600 mr-2" />
          <h3 className="text-lg font-bold text-gray-900">{insight.title}</h3>
        </div>
        <div className="flex items-center space-x-1 px-2 py-1 rounded-full bg-gray-100 text-xs font-medium">
          {getRecommendationIcon(insight.recommendationLevel)}
          <span>{getRecommendationText(insight.recommendationLevel)}</span>
        </div>
      </div>

      {/* Date range */}
      <div className="mt-2 flex items-center text-sm text-gray-600">
        <Calendar className="h-4 w-4 mr-1" />
        <span>{formatDateRange(insight.startDate, insight.endDate)}</span>
      </div>

      {/* Description */}
      <p className="mt-3 text-gray-700">{insight.description}</p>

      {/* Factors */}
      <div className="mt-4">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Faktor yang Mempengaruhi:</h4>
        <div className="space-y-2">
          {insight.factors.slice(0, 3).map(factor => (
            <div key={factor.id} className="flex items-center text-sm">
              <span className={`inline-block w-6 text-center ${factor.impact > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {factor.impact > 0 ? '+' : ''}{factor.impact}
              </span>
              <span className="ml-2 flex-1">{factor.name}</span>
            </div>
          ))}
          {insight.factors.length > 3 && (
            <div className="text-sm text-gray-500">
              +{insight.factors.length - 3} faktor lainnya
            </div>
          )}
        </div>
      </div>

      {/* Recommendations */}
      <div className="mt-4 grid grid-cols-2 gap-4">
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
            <Tag className="h-4 w-4 mr-1" />
            <span>Tipe Promo yang Direkomendasikan:</span>
          </h4>
          <ul className="text-sm space-y-1">
            {insight.recommendedPromoTypes.map((type, index) => (
              <li key={index} className="flex items-center">
                <Zap className="h-3 w-3 text-blue-500 mr-1" />
                <span>{type}</span>
              </li>
            ))}
          </ul>
        </div>
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
            <Package className="h-4 w-4 mr-1" />
            <span>Produk yang Direkomendasikan:</span>
          </h4>
          <ul className="text-sm space-y-1">
            {insight.recommendedProducts.map((product, index) => (
              <li key={index} className="flex items-center">
                <Zap className="h-3 w-3 text-blue-500 mr-1" />
                <span>{product}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Metrics */}
      <div className="mt-4 grid grid-cols-2 gap-4">
        <div className="flex items-center">
          <TrendingUp className="h-5 w-5 text-blue-600 mr-2" />
          <div>
            <div className="text-sm text-gray-500">Potensi Dampak</div>
            <div className="text-lg font-bold">{insight.potentialImpact}/100</div>
          </div>
        </div>
        <div className="flex items-center">
          <Target className="h-5 w-5 text-blue-600 mr-2" />
          <div>
            <div className="text-sm text-gray-500">Tingkat Keyakinan</div>
            <div className="text-lg font-bold">{insight.confidence}%</div>
          </div>
        </div>
      </div>

      {/* AI Generated badge */}
      {insight.aiGenerated && (
        <div className="mt-4 flex items-center text-xs text-gray-500">
          <Brain className="h-3 w-3 mr-1" />
          <span>Dihasilkan oleh AI</span>
        </div>
      )}

      {/* Action buttons */}
      <div className="mt-4 flex justify-end space-x-2">
        <button
          className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors flex items-center"
          onClick={onGenerateContent}
        >
          <MessageSquare className="h-4 w-4 mr-1" />
          <span>Buat Konten</span>
        </button>
        <button
          className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors flex items-center"
          onClick={onSchedule}
        >
          <Calendar className="h-4 w-4 mr-1" />
          <span>Jadwalkan Promo</span>
        </button>
      </div>
    </div>
  );
}
