import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON><PERSON>, Loader2, AlertTriangle, ChevronDown, ChevronUp,
  Copy, Download, CheckCircle, Target, MapPin, BarChart3,
  ListChecks, MessageSquareQuote, Share2, <PERSON>freshC<PERSON>, Disc,
  FileSpreadsheet, FileText
} from 'lucide-react';
import { FleetData } from '../services/fleetDataService';
import { Product } from '../types';
import { analyzeFleetAndProducts } from '../services/fleetProductAnalysisService';

interface FleetAIAnalysisProps {
  filteredData: FleetData[];
  tireSizeFilter: string[];
}

interface AIAnalysisResult {
  matchedProducts: {
    tireSize: string;
    products: Product[];
  }[];
  promotionRecommendations: {
    title: string;
    description: string;
    targetCustomers?: string[];
    targetLocations?: string[];
    estimatedROI?: string;
    implementationSteps?: string[];
    marketingCopy?: string;
  }[];
  analysisText: string;
}

export default function FleetAIAnalysis({ filteredData, tireSizeFilter }: FleetAIAnalysisProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<AIAnalysisResult | null>(null);
  const [isExpanded, setIsExpanded] = useState(true);
  const [activePromoTab, setActivePromoTab] = useState<number>(0);
  const [copiedText, setCopiedText] = useState<string | null>(null);
  const [exportLoading, setExportLoading] = useState<string | null>(null);
  const downloadLinkRef = useRef<HTMLAnchorElement>(null);
  const excelLinkRef = useRef<HTMLAnchorElement>(null);
  const pdfLinkRef = useRef<HTMLAnchorElement>(null);

  const handleAnalyze = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Get the analysis from the service
      const analysisResult = await analyzeFleetAndProducts(filteredData, tireSizeFilter);
      setResult(analysisResult);
      // Reset to first promotion tab when new results come in
      setActivePromoTab(0);
    } catch (err) {
      console.error('Error analyzing fleet data:', err);
      setError('Gagal menganalisis data armada. Silakan coba lagi nanti.');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to copy text to clipboard
  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedText(label);
      setTimeout(() => setCopiedText(null), 2000);
    });
  };

  // Function to download promotion as document
  const downloadPromotion = (promo: any, index: number) => {
    const title = promo.title || 'Promosi';
    const content = `
# ${title}

## Deskripsi
${promo.description || ''}

## Target Pelanggan
${promo.targetCustomers ? promo.targetCustomers.join(', ') : 'Semua pelanggan'}

## Target Lokasi
${promo.targetLocations ? promo.targetLocations.join(', ') : 'Semua lokasi'}

## Perkiraan ROI
${promo.estimatedROI || 'Tidak tersedia'}

## Langkah-langkah Implementasi
${promo.implementationSteps ? promo.implementationSteps.map((step: string, i: number) => `${i+1}. ${step}`).join('\n') : 'Tidak tersedia'}

## Contoh Teks Marketing
${promo.marketingCopy || 'Tidak tersedia'}

---
Dibuat dengan Chitra Marketing Tools - Fleet Analyzer
Tanggal: ${new Date().toLocaleDateString('id-ID')}
    `;

    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);

    if (downloadLinkRef.current) {
      downloadLinkRef.current.href = url;
      downloadLinkRef.current.download = `promosi-${title.toLowerCase().replace(/\s+/g, '-')}.md`;
      downloadLinkRef.current.click();

      // Clean up
      URL.revokeObjectURL(url);
    }
  };

  // Function to export matched products to Excel
  const exportToExcel = (matchedProducts: any[]) => {
    setExportLoading('excel');

    try {
      // Calculate summary data from filtered fleet data
      const uniqueCustomers = [...new Set(filteredData.map(item => item.customer).filter(Boolean))];
      const uniqueLocations = [...new Set(filteredData.map(item => item.location).filter(Boolean))];

      let totalUnits = 0;
      let totalTires = 0;

      filteredData.forEach(item => {
        const unitQty = parseInt(item.unit_qty || '0', 10) || 0;
        const tireQty = parseInt(item.totaltire || '0', 10) || 0;

        totalUnits += unitQty;
        totalTires += tireQty;
      });

      // Create CSV content with summary section
      let csvContent = "RINGKASAN DATA ARMADA\n";
      csvContent += `Tanggal Ekspor,${new Date().toLocaleDateString('id-ID')}\n`;
      csvContent += `Total Pelanggan,${uniqueCustomers.length}\n`;
      csvContent += `Total Unit,${totalUnits}\n`;
      csvContent += `Total Ban,${totalTires}\n`;
      csvContent += `Lokasi,${uniqueLocations.join(', ')}\n\n`;

      // Add filter information
      csvContent += "FILTER YANG DIGUNAKAN\n";
      if (tireSizeFilter.length > 0) {
        csvContent += `Ukuran Ban,${tireSizeFilter.join(', ')}\n`;
      } else {
        csvContent += "Ukuran Ban,Semua\n";
      }
      csvContent += "\n";

      // Add customer distribution
      csvContent += "DISTRIBUSI PELANGGAN\n";
      csvContent += "Pelanggan,Jumlah Unit,Jumlah Ban\n";

      // Group by customer
      const customerData: Record<string, { units: number, tires: number }> = {};

      filteredData.forEach(item => {
        const customer = item.customer || 'Tidak Diketahui';
        const unitQty = parseInt(item.unit_qty || '0', 10) || 0;
        const tireQty = parseInt(item.totaltire || '0', 10) || 0;

        if (!customerData[customer]) {
          customerData[customer] = { units: 0, tires: 0 };
        }

        customerData[customer].units += unitQty;
        customerData[customer].tires += tireQty;
      });

      // Add customer data rows
      Object.entries(customerData)
        .sort((a, b) => b[1].tires - a[1].tires) // Sort by tire quantity descending
        .forEach(([customer, data]) => {
          csvContent += `"${customer}",${data.units},${data.tires}\n`;
        });

      csvContent += "\n";

      // Add location distribution
      csvContent += "DISTRIBUSI LOKASI\n";
      csvContent += "Lokasi,Jumlah Unit,Jumlah Ban\n";

      // Group by location
      const locationData: Record<string, { units: number, tires: number }> = {};

      filteredData.forEach(item => {
        const location = item.location || 'Tidak Diketahui';
        const unitQty = parseInt(item.unit_qty || '0', 10) || 0;
        const tireQty = parseInt(item.totaltire || '0', 10) || 0;

        if (!locationData[location]) {
          locationData[location] = { units: 0, tires: 0 };
        }

        locationData[location].units += unitQty;
        locationData[location].tires += tireQty;
      });

      // Add location data rows
      Object.entries(locationData)
        .sort((a, b) => b[1].tires - a[1].tires) // Sort by tire quantity descending
        .forEach(([location, data]) => {
          csvContent += `"${location}",${data.units},${data.tires}\n`;
        });

      csvContent += "\n";

      // Add product data section
      csvContent += "PRODUK YANG COCOK BERDASARKAN UKURAN BAN\n";
      csvContent += "Ukuran Ban,No. Material,Deskripsi,Harga (IDR)\n";

      // Add product data rows
      matchedProducts.forEach(match => {
        const tireSize = match.tireSize;

        if (match.products.length > 0) {
          match.products.forEach((product: Product) => {
            // Format price with thousand separators but without decimal
            const formattedPrice = product.price.toLocaleString('id-ID').replace(/,/g, '.');

            // Add row with tire size, material number, description, and price
            csvContent += `"${tireSize}","${product.oldMaterialNo}","${product.materialDescription}","${formattedPrice}"\n`;
          });
        } else {
          // Add row for tire size with no matching products
          csvContent += `"${tireSize}","Tidak ada produk yang cocok","",""\n`;
        }
      });

      // Create Blob and download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);

      if (excelLinkRef.current) {
        excelLinkRef.current.href = url;
        excelLinkRef.current.download = `analisis-armada-${new Date().toISOString().slice(0, 10)}.csv`;
        excelLinkRef.current.click();

        // Clean up
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      alert('Gagal mengekspor data ke Excel. Silakan coba lagi.');
    } finally {
      setTimeout(() => setExportLoading(null), 1000);
    }
  };

  // Function to export matched products to PDF
  const exportToPDF = (matchedProducts: any[]) => {
    setExportLoading('pdf');

    try {
      // Calculate summary data from filtered fleet data
      const uniqueCustomers = [...new Set(filteredData.map(item => item.customer).filter(Boolean))];
      const uniqueLocations = [...new Set(filteredData.map(item => item.location).filter(Boolean))];

      let totalUnits = 0;
      let totalTires = 0;

      filteredData.forEach(item => {
        const unitQty = parseInt(item.unit_qty || '0', 10) || 0;
        const tireQty = parseInt(item.totaltire || '0', 10) || 0;

        totalUnits += unitQty;
        totalTires += tireQty;
      });

      // Create text content for PDF with header
      let textContent = "=======================================================\n";
      textContent += "             ANALISIS ARMADA DAN PRODUK BAN               \n";
      textContent += "=======================================================\n\n";

      // Add summary section
      textContent += "RINGKASAN DATA ARMADA\n";
      textContent += "-------------------------------------------------------\n";
      textContent += `Tanggal Ekspor: ${new Date().toLocaleDateString('id-ID')}\n`;
      textContent += `Total Pelanggan: ${uniqueCustomers.length}\n`;
      textContent += `Total Unit: ${totalUnits}\n`;
      textContent += `Total Ban: ${totalTires}\n\n`;

      // Add filter information
      textContent += "FILTER YANG DIGUNAKAN\n";
      textContent += "-------------------------------------------------------\n";
      if (tireSizeFilter.length > 0) {
        textContent += `Ukuran Ban: ${tireSizeFilter.join(', ')}\n`;
      } else {
        textContent += "Ukuran Ban: Semua\n";
      }
      textContent += "\n";

      // Add location information
      textContent += "LOKASI\n";
      textContent += "-------------------------------------------------------\n";
      if (uniqueLocations.length > 0) {
        uniqueLocations.forEach(location => {
          textContent += `- ${location}\n`;
        });
      } else {
        textContent += "Tidak ada data lokasi\n";
      }
      textContent += "\n";

      // Add customer distribution
      textContent += "DISTRIBUSI PELANGGAN\n";
      textContent += "-------------------------------------------------------\n";
      textContent += "Pelanggan                  Unit        Ban\n";
      textContent += "-------------------------------------------------------\n";

      // Group by customer
      const customerData: Record<string, { units: number, tires: number }> = {};

      filteredData.forEach(item => {
        const customer = item.customer || 'Tidak Diketahui';
        const unitQty = parseInt(item.unit_qty || '0', 10) || 0;
        const tireQty = parseInt(item.totaltire || '0', 10) || 0;

        if (!customerData[customer]) {
          customerData[customer] = { units: 0, tires: 0 };
        }

        customerData[customer].units += unitQty;
        customerData[customer].tires += tireQty;
      });

      // Add customer data rows
      Object.entries(customerData)
        .sort((a, b) => b[1].tires - a[1].tires) // Sort by tire quantity descending
        .forEach(([customer, data]) => {
          // Format customer name to fixed width
          const formattedCustomer = customer.padEnd(25).substring(0, 25);
          const formattedUnits = data.units.toString().padStart(8);
          const formattedTires = data.tires.toString().padStart(10);

          textContent += `${formattedCustomer} ${formattedUnits} ${formattedTires}\n`;
        });

      textContent += "\n\n";

      // Add product data section
      textContent += "=======================================================\n";
      textContent += "       PRODUK YANG COCOK BERDASARKAN UKURAN BAN        \n";
      textContent += "=======================================================\n\n";

      // Add data for each tire size
      matchedProducts.forEach((match, index) => {
        textContent += `UKURAN BAN: ${match.tireSize}\n`;
        textContent += "-------------------------------------------------------\n";

        if (match.products.length > 0) {
          textContent += "No. Material             Deskripsi                                  Harga (IDR)\n";
          textContent += "-------------------------------------------------------\n";

          match.products.forEach((product: Product) => {
            // Format price with thousand separators
            const formattedPrice = product.price.toLocaleString('id-ID').replace(/,/g, '.');

            // Format fields to fixed width
            const formattedMaterial = product.oldMaterialNo.padEnd(25).substring(0, 25);
            const formattedDescription = product.materialDescription.padEnd(40).substring(0, 40);
            const formattedPriceAligned = formattedPrice.padStart(15);

            // Add product details
            textContent += `${formattedMaterial} ${formattedDescription} ${formattedPriceAligned}\n`;
          });
        } else {
          textContent += "Tidak ada produk yang cocok untuk ukuran ban ini.\n";
        }

        textContent += "\n\n";
      });

      // Add footer
      textContent += "=======================================================\n";
      textContent += "Dibuat dengan Chitra Marketing Tools - Fleet Analyzer\n";
      textContent += `Diekspor pada: ${new Date().toLocaleString('id-ID')}\n`;
      textContent += "=======================================================\n";

      // Create Blob and download
      const blob = new Blob([textContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);

      if (pdfLinkRef.current) {
        pdfLinkRef.current.href = url;
        pdfLinkRef.current.download = `analisis-armada-${new Date().toISOString().slice(0, 10)}.txt`;
        pdfLinkRef.current.click();

        // Clean up
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      alert('Gagal mengekspor data ke PDF. Silakan coba lagi.');
    } finally {
      setTimeout(() => setExportLoading(null), 1000);
    }
  };

  // Only show the component if there's filtered data
  if (filteredData.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-4 mt-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold flex items-center">
          <Sparkles className="h-5 w-5 mr-2 text-purple-500" />
          AI Fleet Analysis & Product Recommendations
        </h3>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="p-1 rounded-full hover:bg-gray-100"
        >
          {isExpanded ? (
            <ChevronUp className="h-5 w-5 text-gray-500" />
          ) : (
            <ChevronDown className="h-5 w-5 text-gray-500" />
          )}
        </button>
      </div>

      {isExpanded && (
        <>
          {!result && !isLoading && !error && (
            <div className="text-center py-8">
              <div className="max-w-2xl mx-auto">
                <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg border border-purple-100 mb-6">
                  <h3 className="text-lg font-semibold text-purple-800 mb-2">Analisis AI untuk Data Armada</h3>
                  <p className="text-gray-600 mb-3">
                    AI dapat menganalisis data armada yang telah difilter untuk merekomendasikan produk dan promosi berdasarkan ukuran ban.
                  </p>
                  <div className="flex flex-col space-y-2 text-left text-sm text-gray-600 mb-4">
                    <div className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                      <span>Mencocokkan ukuran ban dengan produk yang tersedia</span>
                    </div>
                    <div className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                      <span>Menganalisis kebutuhan pelanggan berdasarkan data armada</span>
                    </div>
                    <div className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                      <span>Merekomendasikan promosi yang sesuai dengan target pelanggan</span>
                    </div>
                    <div className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                      <span>Menyediakan contoh teks marketing yang siap digunakan</span>
                    </div>
                  </div>
                </div>
                <button
                  onClick={handleAnalyze}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-150"
                >
                  <Sparkles className="h-5 w-5 mr-2" />
                  Analisis Data Armada
                </button>
              </div>
            </div>
          )}

          {isLoading && (
            <div className="text-center py-12">
              <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-sm border">
                <div className="relative">
                  <div className="h-16 w-16 rounded-full bg-purple-100 mx-auto flex items-center justify-center mb-4">
                    <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
                  </div>
                  <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center opacity-20">
                    <div className="h-16 w-16 rounded-full bg-purple-100 animate-ping"></div>
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Menganalisis Data Armada</h3>
                <p className="text-gray-600 mb-4">Sedang mencocokkan data armada dengan produk yang tersedia...</p>
                <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                  <div className="bg-purple-600 h-2.5 rounded-full animate-pulse w-3/4"></div>
                </div>
                <p className="text-gray-500 text-sm">Proses ini mungkin memerlukan waktu beberapa saat</p>
              </div>
            </div>
          )}

          {error && (
            <div className="text-center py-12">
              <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-sm border">
                <div className="h-16 w-16 rounded-full bg-red-100 mx-auto flex items-center justify-center mb-4">
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Terjadi Kesalahan</h3>
                <p className="text-gray-600 mb-6">{error}</p>
                <button
                  onClick={handleAnalyze}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Coba Lagi
                </button>
              </div>
            </div>
          )}

          {result && (
            <div className="space-y-6">
              {/* Analysis Text */}
              <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-6 rounded-lg border border-purple-100 relative overflow-hidden">
                <div className="absolute top-0 right-0 w-32 h-32 -mt-8 -mr-8 opacity-10">
                  <Sparkles className="w-full h-full text-purple-500" />
                </div>
                <h3 className="text-lg font-semibold text-purple-800 mb-3 flex items-center">
                  <Sparkles className="h-5 w-5 mr-2 text-purple-600" />
                  Analisis Data Armada
                </h3>
                <div className="prose prose-purple max-w-none text-gray-700">
                  <p className="whitespace-pre-line">{result.analysisText}</p>
                </div>
                <div className="mt-4 flex justify-end">
                  <button
                    onClick={() => copyToClipboard(result.analysisText, 'analysis-text')}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  >
                    {copiedText === 'analysis-text' ? (
                      <>
                        <CheckCircle className="h-4 w-4 mr-1.5 text-green-500" />
                        Tersalin!
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4 mr-1.5" />
                        Salin Analisis
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* Matched Products */}
              {result.matchedProducts.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-md font-semibold flex items-center">
                      <Disc className="h-5 w-5 mr-2 text-blue-500" />
                      Produk yang Cocok berdasarkan Ukuran Ban
                    </h4>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500 mr-2">
                        {result.matchedProducts.reduce((total, match) => total + match.products.length, 0)} produk ditemukan
                      </span>

                      {/* Export Dropdown */}
                      <div className="relative inline-block text-left">
                        <div className="inline-flex shadow-sm rounded-md">
                          {/* Export to Excel Button */}
                          <button
                            onClick={() => exportToExcel(result.matchedProducts)}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-l-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            disabled={exportLoading !== null}
                            title="Ekspor data lengkap termasuk ringkasan armada ke format Excel"
                          >
                            {exportLoading === 'excel' ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                Ekspor...
                              </>
                            ) : (
                              <>
                                <FileSpreadsheet className="h-4 w-4 mr-2 text-green-600" />
                                Ekspor Excel
                              </>
                            )}
                          </button>

                          {/* Export to PDF Button */}
                          <button
                            onClick={() => exportToPDF(result.matchedProducts)}
                            className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 text-sm font-medium rounded-r-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            disabled={exportLoading !== null}
                            title="Ekspor data lengkap termasuk ringkasan armada ke format teks (untuk PDF)"
                          >
                            {exportLoading === 'pdf' ? (
                              <>
                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                Ekspor...
                              </>
                            ) : (
                              <>
                                <FileText className="h-4 w-4 mr-2 text-red-600" />
                                Ekspor PDF
                              </>
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {result.matchedProducts.map((match, index) => (
                      <div key={index} className="border rounded-lg overflow-hidden shadow-sm">
                        <div className="bg-blue-50 p-3 border-b border-blue-100 flex justify-between items-center">
                          <h5 className="font-medium text-blue-800 flex items-center">
                            <Disc className="h-4 w-4 mr-2 text-blue-600" />
                            Ukuran Ban: {match.tireSize}
                          </h5>
                          <span className="text-sm text-blue-600 bg-blue-100 px-2 py-0.5 rounded-full">
                            {match.products.length} produk
                          </span>
                        </div>

                        {match.products.length > 0 ? (
                          <div className="overflow-x-auto bg-white">
                            <table className="min-w-full divide-y divide-gray-200">
                              <thead className="bg-gray-50">
                                <tr>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">No. Material</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deskripsi</th>
                                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Harga (IDR)</th>
                                </tr>
                              </thead>
                              <tbody className="bg-white divide-y divide-gray-200">
                                {match.products.map((product) => (
                                  <tr key={product.id} className="hover:bg-gray-50">
                                    <td className="px-3 py-2 text-sm text-gray-500 font-mono">{product.oldMaterialNo}</td>
                                    <td className="px-3 py-2 text-sm text-gray-900">{product.materialDescription}</td>
                                    <td className="px-3 py-2 text-sm text-gray-700 font-medium">{product.price.toLocaleString('id-ID')}</td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        ) : (
                          <div className="p-4 text-center bg-white">
                            <p className="text-gray-500 italic">Tidak ditemukan produk yang cocok</p>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Promotion Recommendations */}
              {result.promotionRecommendations.length > 0 && (
                <div>
                  <h4 className="text-md font-semibold mb-3">Rekomendasi Promosi</h4>

                  {/* Promotion Tabs */}
                  <div className="flex border-b mb-4">
                    {result.promotionRecommendations.map((promo, index) => (
                      <button
                        key={index}
                        className={`px-4 py-2 text-sm font-medium ${
                          activePromoTab === index
                            ? 'border-b-2 border-purple-500 text-purple-600'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                        onClick={() => setActivePromoTab(index)}
                      >
                        Promosi {index + 1}
                      </button>
                    ))}
                  </div>

                  {/* Active Promotion Card */}
                  {result.promotionRecommendations[activePromoTab] && (
                    <div className="border rounded-lg overflow-hidden bg-white shadow-sm">
                      {/* Promotion Header */}
                      <div className="bg-gradient-to-r from-purple-600 to-blue-500 p-4 text-white">
                        <h3 className="text-xl font-bold">{result.promotionRecommendations[activePromoTab].title}</h3>
                        <p className="mt-1 text-purple-100">{result.promotionRecommendations[activePromoTab].description}</p>
                      </div>

                      {/* Promotion Content */}
                      <div className="p-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {/* Target Customers */}
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <div className="flex items-center mb-2">
                              <Target className="h-5 w-5 text-purple-500 mr-2" />
                              <h4 className="font-medium text-gray-700">Target Pelanggan</h4>
                            </div>
                            {result.promotionRecommendations[activePromoTab].targetCustomers &&
                             result.promotionRecommendations[activePromoTab].targetCustomers!.length > 0 ? (
                              <ul className="list-disc list-inside text-gray-600 pl-2">
                                {result.promotionRecommendations[activePromoTab].targetCustomers!.map((customer, idx) => (
                                  <li key={idx}>{customer}</li>
                                ))}
                              </ul>
                            ) : (
                              <p className="text-gray-500 italic">Semua pelanggan</p>
                            )}
                          </div>

                          {/* Target Locations */}
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <div className="flex items-center mb-2">
                              <MapPin className="h-5 w-5 text-purple-500 mr-2" />
                              <h4 className="font-medium text-gray-700">Target Lokasi</h4>
                            </div>
                            {result.promotionRecommendations[activePromoTab].targetLocations &&
                             result.promotionRecommendations[activePromoTab].targetLocations!.length > 0 ? (
                              <ul className="list-disc list-inside text-gray-600 pl-2">
                                {result.promotionRecommendations[activePromoTab].targetLocations!.map((location, idx) => (
                                  <li key={idx}>{location}</li>
                                ))}
                              </ul>
                            ) : (
                              <p className="text-gray-500 italic">Semua lokasi</p>
                            )}
                          </div>

                          {/* Estimated ROI */}
                          {result.promotionRecommendations[activePromoTab].estimatedROI && (
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <div className="flex items-center mb-2">
                                <BarChart3 className="h-5 w-5 text-purple-500 mr-2" />
                                <h4 className="font-medium text-gray-700">Perkiraan ROI</h4>
                              </div>
                              <p className="text-gray-600 font-semibold text-lg">
                                {result.promotionRecommendations[activePromoTab].estimatedROI}
                              </p>
                            </div>
                          )}

                          {/* Implementation Steps */}
                          {result.promotionRecommendations[activePromoTab].implementationSteps && (
                            <div className="bg-gray-50 p-3 rounded-lg md:col-span-2">
                              <div className="flex items-center mb-2">
                                <ListChecks className="h-5 w-5 text-purple-500 mr-2" />
                                <h4 className="font-medium text-gray-700">Langkah Implementasi</h4>
                              </div>
                              <ol className="list-decimal list-inside text-gray-600 pl-2">
                                {result.promotionRecommendations[activePromoTab].implementationSteps!.map((step, idx) => (
                                  <li key={idx} className="mb-1">{step}</li>
                                ))}
                              </ol>
                            </div>
                          )}

                          {/* Marketing Copy */}
                          {result.promotionRecommendations[activePromoTab].marketingCopy && (
                            <div className="bg-gray-50 p-3 rounded-lg md:col-span-2 mt-2">
                              <div className="flex items-center mb-2">
                                <MessageSquareQuote className="h-5 w-5 text-purple-500 mr-2" />
                                <h4 className="font-medium text-gray-700">Contoh Teks Marketing</h4>
                                <button
                                  onClick={() => copyToClipboard(
                                    result.promotionRecommendations[activePromoTab].marketingCopy || '',
                                    'marketing-copy'
                                  )}
                                  className="ml-auto text-purple-600 hover:text-purple-800 flex items-center text-sm"
                                >
                                  {copiedText === 'marketing-copy' ? (
                                    <>
                                      <CheckCircle className="h-4 w-4 mr-1" />
                                      Tersalin!
                                    </>
                                  ) : (
                                    <>
                                      <Copy className="h-4 w-4 mr-1" />
                                      Salin Teks
                                    </>
                                  )}
                                </button>
                              </div>
                              <div className="bg-white p-3 border border-gray-200 rounded text-gray-700 whitespace-pre-line">
                                {result.promotionRecommendations[activePromoTab].marketingCopy}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-end mt-4 space-x-2">
                          <button
                            onClick={() => copyToClipboard(
                              JSON.stringify(result.promotionRecommendations[activePromoTab], null, 2),
                              'promo-json'
                            )}
                            className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                          >
                            {copiedText === 'promo-json' ? (
                              <>
                                <CheckCircle className="h-4 w-4 mr-1.5 text-green-500" />
                                Tersalin!
                              </>
                            ) : (
                              <>
                                <Copy className="h-4 w-4 mr-1.5" />
                                Salin Data
                              </>
                            )}
                          </button>

                          <button
                            onClick={() => downloadPromotion(result.promotionRecommendations[activePromoTab], activePromoTab)}
                            className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                          >
                            <Download className="h-4 w-4 mr-1.5" />
                            Unduh Dokumen
                          </button>

                          {/* Hidden download links */}
                          <a
                            href="#"
                            ref={downloadLinkRef}
                            className="hidden"
                            aria-hidden="true"
                          >
                            Download
                          </a>
                          <a
                            href="#"
                            ref={excelLinkRef}
                            className="hidden"
                            aria-hidden="true"
                          >
                            Download Excel
                          </a>
                          <a
                            href="#"
                            ref={pdfLinkRef}
                            className="hidden"
                            aria-hidden="true"
                          >
                            Download PDF
                          </a>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Refresh Button */}
              <div className="text-center pt-6 pb-2">
                <div className="inline-flex items-center p-1 border border-gray-200 rounded-lg bg-white shadow-sm">
                  <button
                    onClick={handleAnalyze}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 shadow-sm transition-all duration-150"
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    Perbarui Analisis
                  </button>
                  <div className="px-3 text-xs text-gray-500">
                    Perbarui analisis jika Anda mengubah filter data armada
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
