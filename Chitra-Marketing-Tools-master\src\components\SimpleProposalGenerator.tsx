import React, { useState } from 'react';
import { FileText, Upload } from 'lucide-react';

const SimpleProposalGenerator: React.FC = () => {
  const [templateName, setTemplateName] = useState<string>('');
  const [customerName, setCustomerName] = useState<string>('');
  const [productName, setProductName] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate processing
    setTimeout(() => {
      alert(`Proposal dibuat untuk ${customerName} dengan produk ${productName}`);
      setIsSubmitting(false);
    }, 1000);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <h2 className="text-xl font-semibold mb-4 flex items-center">
        <FileText className="mr-2 text-blue-600" />
        Simple Proposal Generator
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Template
          </label>
          <div className="flex items-center">
            <input
              type="file"
              id="template"
              className="hidden"
              accept=".docx"
              onChange={(e) => {
                if (e.target.files && e.target.files[0]) {
                  setTemplateName(e.target.files[0].name);
                }
              }}
            />
            <button
              type="button"
              onClick={() => document.getElementById('template')?.click()}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Upload className="mr-2 h-4 w-4" />
              Upload Template
            </button>
            <span className="ml-3 text-sm text-gray-500">
              {templateName || 'No file selected'}
            </span>
          </div>
        </div>
        
        <div>
          <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-1">
            Customer Name
          </label>
          <input
            type="text"
            id="customerName"
            value={customerName}
            onChange={(e) => setCustomerName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>
        
        <div>
          <label htmlFor="productName" className="block text-sm font-medium text-gray-700 mb-1">
            Product Name
          </label>
          <input
            type="text"
            id="productName"
            value={productName}
            onChange={(e) => setProductName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>
        
        <div className="pt-2">
          <button
            type="submit"
            disabled={isSubmitting || !templateName || !customerName || !productName}
            className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${
              isSubmitting || !templateName || !customerName || !productName
                ? 'bg-blue-300 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
            }`}
          >
            {isSubmitting ? 'Processing...' : 'Generate Proposal'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default SimpleProposalGenerator;
