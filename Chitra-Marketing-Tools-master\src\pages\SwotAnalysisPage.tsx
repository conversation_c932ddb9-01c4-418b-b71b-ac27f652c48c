import React, { useState, useEffect } from 'react';
import {
  SwotAnalysisRequest,
  SwotAnalysisResult
} from '../types/swotAnalysis';
import {
  performSwotAnalysis,
  getSwotAnalyses,
  getSwotAnalysisById,
  deleteSwotAnalysis
} from '../services/swotAnalysisService';
import SwotAnalysisForm from '../components/SwotAnalysisForm';
import SwotAnalysisResults from '../components/SwotAnalysisResults';
import { History, Trash2, RefreshCw, ChevronDown, ChevronUp } from 'lucide-react';

const SwotAnalysisPage: React.FC = () => {
  // State
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<SwotAnalysisResult | null>(null);
  const [savedAnalyses, setSavedAnalyses] = useState<SwotAnalysisResult[]>([]);
  const [showHistory, setShowHistory] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Load saved analyses on mount
  useEffect(() => {
    loadSavedAnalyses();
  }, []);

  // Load saved analyses
  const loadSavedAnalyses = () => {
    try {
      const analyses = getSwotAnalyses();
      setSavedAnalyses(analyses.sort((a, b) => b.timestamp - a.timestamp));
    } catch (error) {
      console.error('Error loading saved analyses:', error);
      setError('Failed to load saved analyses');
    }
  };

  // Handle form submission
  const handleSubmitAnalysis = async (request: SwotAnalysisRequest) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await performSwotAnalysis(request);
      setCurrentAnalysis(result);
      loadSavedAnalyses(); // Refresh the list of saved analyses
    } catch (error) {
      console.error('Error performing SWOT analysis:', error);
      setError('Failed to perform SWOT analysis. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load a saved analysis
  const handleLoadAnalysis = (id: string) => {
    try {
      const analysis = getSwotAnalysisById(id);
      if (analysis) {
        setCurrentAnalysis(analysis);
      } else {
        setError('Analysis not found');
      }
    } catch (error) {
      console.error('Error loading analysis:', error);
      setError('Failed to load analysis');
    }
  };

  // Delete a saved analysis
  const handleDeleteAnalysis = (id: string) => {
    try {
      const success = deleteSwotAnalysis(id);
      if (success) {
        loadSavedAnalyses();

        // If the current analysis is deleted, clear it
        if (currentAnalysis && currentAnalysis.id === id) {
          setCurrentAnalysis(null);
        }
      } else {
        setError('Failed to delete analysis');
      }
    } catch (error) {
      console.error('Error deleting analysis:', error);
      setError('Failed to delete analysis');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">SWOT Analysis AI</h1>
          <p className="text-gray-600 mt-1">
            Analisis SWOT dinamis berdasarkan data internal Chitra Paratama
          </p>
        </div>

        <div className="mt-4 md:mt-0">
          <button
            onClick={() => setShowHistory(!showHistory)}
            className="flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            <History size={18} className="mr-2" />
            Riwayat Analisis
            {showHistory ? <ChevronUp size={18} className="ml-2" /> : <ChevronDown size={18} className="ml-2" />}
          </button>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
        </div>
      )}

      {/* Analysis History */}
      {showHistory && (
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Riwayat Analisis</h2>

          {savedAnalyses.length === 0 ? (
            <p className="text-gray-600">Belum ada analisis yang disimpan.</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tanggal
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Perusahaan
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Input Manual
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Sumber Data
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {savedAnalyses.map((analysis) => (
                    <tr key={analysis.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(analysis.timestamp).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {analysis.companyName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {analysis.manualInputUsed ? 'Ya' : 'Tidak'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {Object.entries(analysis.dataSourcesUsed)
                          .filter(([_, used]) => used)
                          .map(([source]) => source.replace('Data', ''))
                          .join(', ')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleLoadAnalysis(analysis.id)}
                          className="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          Load
                        </button>
                        <button
                          onClick={() => handleDeleteAnalysis(analysis.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 size={16} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Analysis Form */}
        <div className="lg:col-span-1">
          <SwotAnalysisForm onSubmit={handleSubmitAnalysis} isLoading={isLoading} />
        </div>

        {/* Analysis Results */}
        <div className="lg:col-span-2">
          {isLoading ? (
            <div className="bg-white p-8 rounded-lg shadow flex flex-col items-center justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700 mb-4"></div>
              <p className="text-gray-600">Menganalisis data...</p>
              <p className="text-sm text-gray-500 mt-2">Ini mungkin memerlukan waktu beberapa saat</p>
            </div>
          ) : currentAnalysis ? (
            <SwotAnalysisResults analysisResult={currentAnalysis} />
          ) : (
            <div className="bg-white p-8 rounded-lg shadow flex flex-col items-center justify-center h-full">
              <div className="bg-blue-100 p-4 rounded-full mb-4">
                <RefreshCw size={32} className="text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Belum Ada Analisis</h3>
              <p className="text-gray-600 text-center">
                Isi form di sebelah kiri untuk melakukan analisis SWOT atau muat analisis dari riwayat.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SwotAnalysisPage;
