<script setup lang="ts">
import { SidebarProvider } from '@/components/ui/sidebar';
import { usePage } from '@inertiajs/vue3';
import { onMounted } from 'vue';
import ErrorBoundary from '@/components/ErrorBoundary.vue';
import { measurePageLoad } from '@/utils/performance';

interface Props {
    variant?: 'header' | 'sidebar';
}

defineProps<Props>();

const isOpen = usePage().props.sidebarOpen;

// Measure page load performance
onMounted(() => {
    const currentRoute = usePage().url;
    measurePageLoad(currentRoute);
});
</script>

<template>
    <ErrorBoundary>
        <div v-if="variant === 'header'" class="flex min-h-screen w-full flex-col">
            <slot />
        </div>
        <SidebarProvider v-else :default-open="isOpen">
            <slot />
        </SidebarProvider>
    </ErrorBoundary>
</template>
