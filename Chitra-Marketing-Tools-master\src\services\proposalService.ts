import { ProposalFormData, Product } from '../types/proposal';
import { formatCurrency } from '../utils/pricing';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import mammoth from 'mammoth';

// Add autotable to jsPDF
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

function addHeader(doc: jsPDF, data: ProposalFormData): void {
  // Add company logo (blue circle with company name)
  doc.setDrawColor(0, 120, 200);
  doc.setFillColor(0, 120, 200);
  doc.circle(35, 25, 10, 'F');

  // Add company name
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 120, 200);
  doc.setFontSize(22);
  doc.text('BundleBoost', 50, 22);

  // Add proposal title
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(80, 80, 200);
  doc.setFontSize(18);
  doc.text(data.proposalTitle || 'Proposal', 200, 22, { align: 'right' });

  // Add horizontal line
  doc.setDrawColor(220, 220, 220);
  doc.setLineWidth(0.5);
  doc.line(20, 35, 190, 35);
}

function addCustomerInfo(doc: jsPDF, data: ProposalFormData): void {
  // Company information
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(11);
  doc.text('PT BundleBoost Indonesia', 25, 45);

  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  doc.text('Jakarta, Indonesia', 25, 50);

  // Customer information
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(11);
  doc.text('To:', 140, 45);

  doc.setFont('helvetica', 'normal');
  doc.text(data.customerName || 'Customer', 140, 50);

  // Proposal details
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  doc.text(`Date: ${data.proposalDate ? new Date(data.proposalDate).toLocaleDateString() : new Date().toLocaleDateString()}`, 25, 60);
  doc.text(`Valid Until: ${data.validUntil ? new Date(data.validUntil).toLocaleDateString() : new Date(new Date().setMonth(new Date().getMonth() + 1)).toLocaleDateString()}`, 25, 65);
}

function getProductsFromData(data: ProposalFormData): Product[] {
  switch (data.proposalType) {
    case 'bundling':
      return data.bundlingDetails?.products || [];
    case 'consignment':
      return data.consignmentDetails?.products || [];
    case 'trade-in':
      return data.tradeInDetails ? [data.tradeInDetails.newProduct] : [];
    case 'performance-guarantee':
      return data.performanceGuaranteeDetails?.products || [];
    case 'performance-warranty':
      return data.performanceWarrantyDetails?.products || [];
    case 'first-michelin':
      return data.firstMichelinDetails?.products || [];
    default:
      return [];
  }
}

function addProductTable(doc: jsPDF, data: ProposalFormData): void {
  const products = getProductsFromData(data);

  // Table headers
  const headers = [['No', 'Product', 'Quantity', 'Unit Price', 'Total']];

  // Table rows
  const rows = products.map((product, index) => [
    (index + 1).toString(),
    product.name,
    product.quantity.toString(),
    formatCurrency(product.unitPrice),
    formatCurrency(product.total)
  ]);

  // Add table
  doc.autoTable({
    head: headers,
    body: rows,
    startY: 75,
    theme: 'grid',
    styles: {
      fontSize: 9,
      cellPadding: 4
    },
    headStyles: {
      fillColor: [0, 120, 200],
      textColor: [255, 255, 255]
    }
  });
}

function addTermsAndContact(doc: jsPDF, data: ProposalFormData): void {
  const finalY = (doc as any).lastAutoTable.finalY + 10;

  // Contact Information
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(11);
  doc.text('Contact Information', 25, finalY);

  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  doc.text(`Sales: ${data.salesName || 'N/A'}`, 25, finalY + 7);
  doc.text(`Phone: ${data.salesPhone || 'N/A'}`, 25, finalY + 14);
  doc.text(`Website: ${data.website || 'www.chitraparatama.co.id'}`, 25, finalY + 21);

  // Terms and Conditions
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(11);
  doc.text('Terms and Conditions', 25, finalY + 35);

  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  doc.text('1. Payment Terms: 30 days', 25, finalY + 42);
  doc.text('2. Validity: As per specified date', 25, finalY + 49);
  doc.text('3. Prices are subject to change without prior notice', 25, finalY + 56);
}

function addSignature(doc: jsPDF, data: ProposalFormData): void {
  const finalY = (doc as any).lastAutoTable.finalY + 80;

  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);

  // Signature line
  doc.line(25, finalY, 80, finalY);

  // Signatory details
  doc.text(data.signatoryName || 'N/A', 25, finalY + 5);
  doc.text(data.signatoryTitle || 'N/A', 25, finalY + 12);
  doc.text(`Date: ${data.signatureDate ? new Date(data.signatureDate).toLocaleDateString() : new Date().toLocaleDateString()}`, 25, finalY + 19);
}

export const generateProposal = async (data: ProposalFormData): Promise<string> => {
  try {
    console.log('Generating proposal with data:', data);

    // Check if a template file is provided
    if (data.template) {
      console.log('Template file provided, using template-based generation');
      try {
        // Process the template with the form data
        return await processTemplate(data.template, data);
      } catch (templateError) {
        console.error('Error processing template:', templateError);
        console.log('Falling back to standard PDF generation');
      }
    }

    try {
      // Create a new PDF document
      console.log('Creating PDF document');
      const doc = new jsPDF();

      // Add header
      console.log('Adding header');
      addHeader(doc, data);

      // Add customer and proposal information
      console.log('Adding customer info');
      addCustomerInfo(doc, data);

      // Add product table
      console.log('Adding product table');
      addProductTable(doc, data);

      // Add terms and contact information
      console.log('Adding terms and contact');
      addTermsAndContact(doc, data);

      // Add signature section
      console.log('Adding signature');
      addSignature(doc, data);

      // Generate filename
      const fileName = `Proposal_${data.customerName || 'Customer'}_${new Date().toISOString().split('T')[0]}.pdf`;
      console.log('Generated filename:', fileName);

      // Save the PDF
      console.log('Saving PDF');
      doc.save(fileName);
      console.log('Proposal generated successfully:', fileName);
      return fileName;
    } catch (pdfError) {
      console.error('PDF generation error:', pdfError);

      // Fallback to simple PDF generation
      console.log('Falling back to simple PDF generation');
      return await generateTextProposal(data);
    }
  } catch (error) {
    console.error('Error generating proposal:', error);
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    throw new Error('Failed to generate proposal: ' + (error instanceof Error ? error.message : String(error)));
  }
};

// Function to process a DOCX template with the provided data
export const processTemplate = async (template: File, data: ProposalFormData): Promise<string> => {
  try {
    console.log('Processing template:', template.name);

    // Check if this is a dummy template file (from template selection)
    const isDummyTemplate = template.size < 100; // Dummy files are very small

    let detectedVariables: string[] = [];
    let html = '';

    if (isDummyTemplate) {
      console.log('Detected dummy template file, using pre-detected variables');

      // Use the detected variables from the form state
      detectedVariables = data.detectedVariables || [];
      console.log('Using pre-detected variables:', detectedVariables);

      // For dummy templates, we'll generate a simple HTML with the detected variables
      html = `<h1>${data.proposalTitle || 'Proposal'}</h1>
              <h2>${data.promoTitle || ''}</h2>
              <p>Customer: ${data.customerName || 'Customer'}</p>
              <p>Date: ${data.proposalDate ? new Date(data.proposalDate).toLocaleDateString() : new Date().toLocaleDateString()}</p>
              <p>Valid Until: ${data.validUntil ? new Date(data.validUntil).toLocaleDateString() : new Date(new Date().setMonth(new Date().getMonth() + 1)).toLocaleDateString()}</p>`;

      // Add product information if available
      if (data.bundlingDetails?.products && data.bundlingDetails.products.length > 0) {
        html += `<h2>Products</h2>`;
        data.bundlingDetails.products.forEach((product, index) => {
          html += `<p>${index + 1}. ${product.name || `Product ${index+1}`} x ${product.quantity || 1}: ${formatCurrency(product.unitPrice || 0)} = ${formatCurrency(product.total || 0)}</p>`;
        });
      }

      // Add contact information
      html += `<h2>Contact Information</h2>
               <p>Sales: ${data.salesName || ''}</p>
               <p>Phone: ${data.salesPhone || ''}</p>
               <p>Website: ${data.website || 'http://www.chitraparatama.co.id'}</p>`;

      // Add signature section
      html += `<h2>Signature</h2>
               <p>${data.signatoryName || ''}</p>
               <p>${data.signatoryTitle || ''}</p>
               <p>Date: ${data.signatureDate ? new Date(data.signatureDate).toLocaleDateString() : new Date().toLocaleDateString()}</p>`;
    } else {
      // This is a real template file uploaded by the user
      // Read the template file
      const arrayBuffer = await template.arrayBuffer();

      // First, extract variables from the template
      const templateText = await template.text();
      const variableRegex = /\{\{([^}]+)\}\}/g;
      let match;

      while ((match = variableRegex.exec(templateText)) !== null) {
        detectedVariables.push(match[1]);
      }

      // Convert DOCX to HTML using mammoth
      const result = await mammoth.convertToHtml({ arrayBuffer });
      html = result.value;
    }

    // Remove duplicates
    const uniqueDetectedVariables = [...new Set(detectedVariables)];
    console.log('Detected variables in template:', uniqueDetectedVariables);

    console.log('Template content prepared, length:', html.length);

    // Get all available variables from the form data
    const allVariables = getTemplateVariables(data);

    // Only use variables that are detected in the template
    const filteredVariables: Record<string, string> = {};
    uniqueDetectedVariables.forEach(varName => {
      if (allVariables[varName] !== undefined) {
        filteredVariables[varName] = allVariables[varName];
      } else {
        // If the variable is not found in our predefined list, leave it as is
        console.log(`Variable not found in form data: ${varName}`);
      }
    });

    console.log('Variables to be replaced:', filteredVariables);

    // Replace each variable in the HTML
    Object.entries(filteredVariables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      html = html.replace(regex, value || '');
    });

    console.log('Variables replaced in HTML');

    // Convert HTML to PDF using jsPDF
    const doc = new jsPDF();

    // Split HTML into lines and add to PDF
    const lines = html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .split('\n')
      .filter(line => line.trim() !== '');

    let y = 20;
    doc.setFontSize(12);

    lines.forEach(line => {
      if (line.startsWith('#')) {
        // Heading
        doc.setFontSize(18);
        doc.setFont('helvetica', 'bold');
        doc.text(line.replace('#', '').trim(), 20, y);
        y += 10;
        doc.setFontSize(12);
        doc.setFont('helvetica', 'normal');
      } else if (line.startsWith('##')) {
        // Subheading
        doc.setFontSize(16);
        doc.setFont('helvetica', 'bold');
        doc.text(line.replace('##', '').trim(), 20, y);
        y += 8;
        doc.setFontSize(12);
        doc.setFont('helvetica', 'normal');
      } else {
        // Regular text
        // Split long lines
        const words = line.split(' ');
        let currentLine = '';

        words.forEach(word => {
          const testLine = currentLine + word + ' ';
          const testWidth = doc.getStringUnitWidth(testLine) * doc.getFontSize() / doc.internal.scaleFactor;

          if (testWidth > 170) {
            doc.text(currentLine, 20, y);
            y += 7;
            currentLine = word + ' ';
          } else {
            currentLine = testLine;
          }
        });

        if (currentLine.trim() !== '') {
          doc.text(currentLine, 20, y);
          y += 7;
        }
      }

      // Add a page if we're near the bottom
      if (y > 280) {
        doc.addPage();
        y = 20;
      }
    });

    // Generate filename
    const fileName = `Proposal_${data.customerName || 'Customer'}_${new Date().toISOString().split('T')[0]}.pdf`;

    // Save the PDF
    doc.save(fileName);
    console.log('PDF generated from template:', fileName);

    return fileName;
  } catch (error) {
    console.error('Error processing template:', error);
    throw new Error('Failed to process template: ' + (error instanceof Error ? error.message : String(error)));
  }
};

// Function to generate a simple PDF proposal (used as a direct fallback)
export const generateTextProposal = async (data: ProposalFormData): Promise<string> => {
  try {
    console.log('Generating simple PDF proposal with data:', data);

    // Create a new PDF document
    const doc = new jsPDF();

    // Add title
    doc.setFontSize(18);
    doc.setFont('helvetica', 'bold');
    doc.text('Template Not Found', 105, 20, { align: 'center' });

    // Add message
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text('Please upload a template file with variables to generate a proposal.', 105, 40, { align: 'center' });
    doc.text('The template should contain variables in the format {{VariableName}}.', 105, 50, { align: 'center' });

    // Add example variables
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Example Variables:', 20, 70);

    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    let y = 80;

    const exampleVariables = [
      '{{customerName}} - Customer name',
      '{{proposalTitle}} - Proposal title',
      '{{proposalDate}} - Proposal date',
      '{{validUntil}} - Valid until date',
      '{{salesName}} - Sales name',
      '{{salesPhone}} - Sales phone',
      '{{website}} - Website (http://www.chitraparatama.co.id)',
      '{{signatoryName}} - Signatory name',
      '{{signatoryTitle}} - Signatory title',
      '{{productName}} - First product name',
      '{{productQuantity}} - First product quantity',
      '{{productUnitPrice}} - First product unit price',
      '{{productTotal}} - First product total price'
    ];

    exampleVariables.forEach(example => {
      doc.text(example, 25, y);
      y += 8;
    });

    // Generate filename
    const fileName = `Proposal_Template_Guide_${new Date().toISOString().split('T')[0]}.pdf`;

    // Save the PDF
    doc.save(fileName);
    console.log('Simple PDF guide generated successfully:', fileName);

    return fileName;
  } catch (error) {
    console.error('Error generating simple PDF guide:', error);

    // Try using saveAs as a last resort
    try {
      // Create a simple text representation of the guide
      const guideText = `
# Template Not Found

Please upload a template file with variables to generate a proposal.
The template should contain variables in the format {{VariableName}}.

## Example Variables:
- {{customerName}} - Customer name
- {{proposalTitle}} - Proposal title
- {{proposalDate}} - Proposal date
- {{validUntil}} - Valid until date
- {{salesName}} - Sales name
- {{salesPhone}} - Sales phone
- {{website}} - Website (http://www.chitraparatama.co.id)
- {{signatoryName}} - Signatory name
- {{signatoryTitle}} - Signatory title
- {{productName}} - First product name
- {{productQuantity}} - First product quantity
- {{productUnitPrice}} - First product unit price
- {{productTotal}} - First product total price
`;

      // Create a Blob with the guide text
      const blob = new Blob([guideText], { type: 'application/pdf' });
      const fileName = `Proposal_Template_Guide_${new Date().toISOString().split('T')[0]}.pdf`;

      // Use FileSaver to save the file
      const saveAs = (await import('file-saver')).saveAs;
      saveAs(blob, fileName);
      console.log('Text-based PDF guide generated as fallback:', fileName);

      return fileName;
    } catch (fallbackError) {
      console.error('All fallback methods failed:', fallbackError);
      throw new Error('Failed to generate guide: ' + (error instanceof Error ? error.message : String(error)));
    }
  }
};

// Function to get template variables from form data
export const getTemplateVariables = (data: ProposalFormData): Record<string, string> => {
  const variables: Record<string, string> = {
    // General info
    'proposalTitle': data.proposalTitle || '',
    'promoTitle': data.promoTitle || '',
    'proposalDate': data.proposalDate ? new Date(data.proposalDate).toLocaleDateString() : new Date().toLocaleDateString(),
    'validUntil': data.validUntil ? new Date(data.validUntil).toLocaleDateString() : new Date(new Date().setMonth(new Date().getMonth() + 1)).toLocaleDateString(),

    // Customer info
    'customerName': data.customerName || '',

    // Contact info
    'salesName': data.salesName || '',
    'salesPhone': data.salesPhone || '',
    'website': data.website || 'http://www.chitraparatama.co.id',

    // Signature info
    'signatoryName': data.signatoryName || '',
    'signatoryTitle': data.signatoryTitle || '',
    'signatureDate': data.signatureDate ? new Date(data.signatureDate).toLocaleDateString() : new Date().toLocaleDateString(),

    // Products info (for bundling)
    'productCount': data.bundlingDetails?.products?.length?.toString() || '0',
    'productTotal': formatCurrency(data.bundlingDetails?.products?.reduce((sum, product) => sum + (product.total || 0), 0) || 0),
  };

  // Add product details if available
  if (data.bundlingDetails?.products && data.bundlingDetails.products.length > 0) {
    data.bundlingDetails.products.forEach((product, index) => {
      variables[`product${index+1}Name`] = product.name || '';
      variables[`product${index+1}Quantity`] = (product.quantity || '').toString();
      variables[`product${index+1}UnitPrice`] = formatCurrency(product.unitPrice || 0);
      variables[`product${index+1}Total`] = formatCurrency(product.total || 0);
    });

    // Add first product as main product
    variables['productName'] = data.bundlingDetails.products[0]?.name || '';
    variables['productQuantity'] = (data.bundlingDetails.products[0]?.quantity || '').toString();
    variables['productUnitPrice'] = formatCurrency(data.bundlingDetails.products[0]?.unitPrice || 0);
    variables['productTotal'] = formatCurrency(data.bundlingDetails.products[0]?.total || 0);
  }

  return variables;
};

// Function to read the template file and extract variables
export const parseTemplateVariables = async (file: File): Promise<string[]> => {
  try {
    const text = await file.text();
    const variableRegex = /{{([^}]+)}}/g;
    const variables: string[] = [];
    let match;

    while ((match = variableRegex.exec(text)) !== null) {
      variables.push(match[1]);
    }

    return [...new Set(variables)]; // Remove duplicates
  } catch (error) {
    console.error('Error parsing template:', error);
    throw new Error('Failed to parse template file');
  }
};
