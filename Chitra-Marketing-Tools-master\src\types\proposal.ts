export type ProposalType = 'bundling' | 'consignment' | 'trade-in' | 'performance-guarantee' | 'performance-warranty' | 'first-michelin';

export interface Product {
  name: string;
  quantity: number;
  unitPrice: number;
  total: number;
  bonus?: string; // Optional bonus field
}

export interface ProposalTemplate {
  id?: string;
  name: string;
  content: string;
  variables: string[];
}

export interface BundlingDetails {
  products: Product[];
}

export interface ConsignmentDetails {
  projectLocation: string;
  evaluationPeriod: number; // in months
  products: Product[];
}

export interface TradeInDetails {
  productName: string;
  tradeInValue: number;
  newProduct: Product;
}

export interface PerformanceGuaranteeDetails {
  products: Product[];
  guaranteePeriod: number; // in months
  guaranteeTerms: string;
  performanceMetrics: string;
  compensationTerms: string;
}

export interface PerformanceWarrantyDetails {
  products: Product[];
  warrantyPeriod: number; // in months
  warrantyTerms: string;
  coverageDetails: string;
  claimProcedure: string;
}

export interface FirstMichelinDetails {
  products: Product[];
  implementationPeriod: number; // in months
  specialTerms: string;
  benefitsDescription: string;
  testimonials?: string;
}

export interface ProposalFormData {
  customerName: string;
  proposalType: ProposalType;
  proposalDate: string;
  validUntil: string;
  template?: File;
  detectedTags?: string[];
  detectedVariables?: string[];

  // General Info
  proposalTitle: string;
  promoTitle: string;
  productName: string;
  offerType: 'Bundling' | 'Konsinyasi' | 'Trade-In';

  // Contact & Company
  contactPhone: string;
  website: string;
  salesName: string;
  salesPhone: string;

  // Signature
  signatoryName: string;
  signatoryTitle: string;
  signatureDate: string;

  // Type-specific details
  bundlingDetails?: BundlingDetails;
  consignmentDetails?: ConsignmentDetails;
  tradeInDetails?: TradeInDetails;
  performanceGuaranteeDetails?: PerformanceGuaranteeDetails;
  performanceWarrantyDetails?: PerformanceWarrantyDetails;
  firstMichelinDetails?: FirstMichelinDetails;
}

// For localStorage draft saving
export interface ProposalDraft extends Omit<ProposalFormData, 'template'> {
  templateName?: string;
  lastUpdated: string;
}
