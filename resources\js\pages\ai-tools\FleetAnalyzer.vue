<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <Truck class="h-8 w-8 text-blue-600 mr-3" />
                        Fleet Data Analyzer
                    </h1>
                    <p class="text-gray-600 mt-1">Analyze and manage your fleet data with advanced filtering and insights</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="exportData"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export
                    </button>
                    <button
                        @click="loadFleetData"
                        :disabled="isLoading"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                    >
                        <RefreshCw class="h-4 w-4 mr-2" :class="{ 'animate-spin': isLoading }" />
                        {{ isLoading ? 'Loading...' : 'Refresh Data' }}
                    </button>
                </div>
            </div>

            <!-- Vehicle Performance Analysis Table -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-4">Vehicle Performance Analysis</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Site</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tire Size</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Tires</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr v-for="item in fleetData" :key="item.id_fleet_list" class="hover:bg-gray-50">
                                <td class="px-6 py-4 text-sm text-gray-900 font-semibold">{{ item.customer || '-' }}</td>
                                <td class="px-6 py-4 text-sm text-gray-500">{{ item.site || '-' }}</td>
                                <td class="px-6 py-4 text-sm text-gray-500">{{ item.location || '-' }}</td>
                                <td class="px-6 py-4 text-sm text-gray-500">{{ item.model || '-' }}</td>
                                <td class="px-6 py-4 text-sm text-gray-500">{{ item.tire_size || '-' }}</td>
                                <td class="px-6 py-4 text-sm text-gray-900 font-medium">{{ item.totaltire || '-' }}</td>
                                <td class="px-6 py-4 text-sm">
                                    <span
                                        v-if="item.status"
                                        :class="{
                                            'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs': item.status.toLowerCase() === 'active',
                                            'bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs': item.status.toLowerCase() === 'operational',
                                            'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs': item.status.toLowerCase() === 'maintenance',
                                            'bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs': item.status.toLowerCase() === 'inactive'
                                        }"
                                    >
                                        {{ item.status }}
                                    </span>
                                    <span v-else>-</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Loading State -->
                <div v-if="isLoading" class="p-8 text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
                    <p class="text-gray-600">Loading fleet data...</p>
                </div>

                <!-- Error State -->
                <div v-else-if="error" class="p-8 text-center">
                    <AlertCircle class="h-12 w-12 text-red-500 mx-auto mb-4" />
                    <p class="text-red-600">{{ error }}</p>
                    <button @click="loadFleetData" class="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                        Try Again
                    </button>
                </div>

                <!-- Empty State -->
                <div v-else-if="fleetData.length === 0" class="p-8 text-center">
                    <FileX class="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p class="text-gray-600">No fleet data available.</p>
                    <button @click="loadFleetData" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Load Data
                    </button>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Truck,
    Download,
    RefreshCw,
    AlertCircle,
    FileX
} from 'lucide-vue-next';

// Types
interface FleetItem {
    id_fleet_list: string;
    customer: string;
    site: string;
    status: string;
    location: string;
    kabupaten: string;
    kecamatan: string;
    unit_manufacture: string;
    model: string;
    tire_size: string;
    tire_quantity: string;
    unit_qty: string;
    totaltire: string;
    annual: string;
    forecast: string;
}

// Reactive state
const isLoading = ref(false);
const error = ref('');
const fleetData = ref<FleetItem[]>([]);

// Methods
const loadFleetData = async () => {
    try {
        isLoading.value = true;
        error.value = '';

        const response = await fetch('https://chitraparatama.co.id/ICS/product/get_api.php?function=fleetlist');
        if (!response.ok) throw new Error('Gagal fetch data dari API');
        const data = await response.json();

        // Jika data berupa objek dengan key "data" atau array langsung
        fleetData.value = Array.isArray(data) ? data : (data.data || []);

    } catch (err) {
        error.value = 'Failed to load fleet data. Please try again.';
        console.error('Error loading fleet data:', err);
    } finally {
        isLoading.value = false;
    }
};

const exportData = () => {
    try {
        const csvContent = generateCSVContent();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `fleet-analysis-${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (err) {
        console.error('Export error:', err);
        alert('Failed to export data. Please try again.');
    }
};

const generateCSVContent = (): string => {
    const headers = ['Fleet ID', 'Customer', 'Site', 'Location', 'Kabupaten', 'Kecamatan', 'Unit Manufacture', 'Model', 'Tire Size', 'Tire Quantity', 'Unit Qty', 'Total Tires', 'Annual', 'Forecast', 'Status'];
    const rows = fleetData.value.map(item => [
        item.id_fleet_list,
        item.customer,
        item.site,
        item.location,
        item.kabupaten,
        item.kecamatan,
        item.unit_manufacture,
        item.model,
        item.tire_size,
        item.tire_quantity,
        item.unit_qty,
        item.totaltire,
        item.annual,
        item.forecast,
        item.status
    ]);

    const csvContent = [
        headers.map(h => `"${h}"`).join(','),
        ...rows.map(row => row.map(cell => `"${cell || ''}"`).join(','))
    ].join('\n');

    return csvContent;
};

// Initialize on mount
onMounted(() => {
    loadFleetData();
});
</script>