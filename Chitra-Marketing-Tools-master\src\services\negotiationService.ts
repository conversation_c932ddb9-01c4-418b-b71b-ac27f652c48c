import {
  CustomerPersona,
  CustomerPersonaType,
  NegotiationAIRequest,
  NegotiationAIResponse,
  NegotiationEvaluation,
  NegotiationMessage,
  NegotiationSession
} from '../types/negotiation';
import { Product } from '../types';
import { KnowledgeEntry } from '../types/knowledgeBase';
import { formatCurrency } from '../utils/pricing';

// OpenRouter API key - using the existing key from the project
const OPENROUTER_API_KEY = '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';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Models
const MODELS = {
  GPT4: 'openai/gpt-4.1-nano',
  CLAUDE_OPUS: 'openai/gpt-4.1-nano',
  CLAUDE_SONNET: 'openai/gpt-4.1-nano',
  DEEPSEEK: 'openai/gpt-4.1-nano',
};

// Default model for negotiation
const DEFAULT_MODEL = MODELS.DEEPSEEK;

// Default product for negotiation
const DEFAULT_PRODUCT = {
  id: 'GT-MILLER-10-20',
  materialNumber: '10.00-20 GT MILLER',
  materialDescription: '10.00-20 GT MILLER',
  oldMaterialNo: '',
  description: '10.00-20 GT MILLER',
  price: 3157882,
  costPrice: 3000000,
  margin: 5
};

// Predefined customer personas
export const CUSTOMER_PERSONAS: Record<CustomerPersonaType, CustomerPersona> = {
  [CustomerPersonaType.PRICE_SENSITIVE]: {
    type: CustomerPersonaType.PRICE_SENSITIVE,
    name: 'Budi Santoso',
    company: 'PT Hemat Selalu',
    description: 'A price-conscious buyer who always looks for the best deal and is willing to walk away if the price isn\'t right.',
    negotiationStyle: 'Direct, focused on price, often compares with competitor offers',
    priceExpectation: 'low',
    loyaltyLevel: 'low',
    valueFeatures: ['Low price', 'Discounts', 'Payment terms'],
    painPoints: ['Budget constraints', 'Previous bad experiences with overpriced products', 'Pressure from management to reduce costs'],
    avatar: '/assets/avatars/price-sensitive.png'
  },
  [CustomerPersonaType.LOYAL_CUSTOMER]: {
    type: CustomerPersonaType.LOYAL_CUSTOMER,
    name: 'Dewi Wijaya',
    company: 'PT Setia Mining',
    description: 'A long-term customer who values relationship and quality but still expects fair pricing.',
    negotiationStyle: 'Collaborative, relationship-focused, values consistency and reliability',
    priceExpectation: 'medium',
    loyaltyLevel: 'high',
    valueFeatures: ['Quality', 'Reliability', 'After-sales support', 'Long-term partnership'],
    painPoints: ['Service inconsistency', 'Feeling taken for granted', 'Lack of special treatment despite loyalty'],
    avatar: '/assets/avatars/loyal-customer.png'
  },
  [CustomerPersonaType.AGGRESSIVE_BUYER]: {
    type: CustomerPersonaType.AGGRESSIVE_BUYER,
    name: 'Agus Pratama',
    company: 'PT Tegas Resources',
    description: 'Seorang pembeli dari perusahaan pertambangan yang sedang melakukan negosiasi pembelian ban alat berat dengan gaya tegas dan kadang konfrontatif.',
    negotiationStyle: 'Tegas, kadang konfrontatif, sering menggunakan batas waktu, tekanan kuantitas, atau ancaman pindah supplier',
    priceExpectation: 'low',
    loyaltyLevel: 'medium',
    valueFeatures: [
      'Menang dalam negosiasi (deal terbaik, harga terendah, atau bonus terbanyak)',
      'Transparansi & justifikasi yang jelas dari penjual',
      'Respons cepat dan tanggapan masuk akal, tidak basa-basi'
    ],
    painPoints: [
      'Menghindari pembelian yang tidak memberikan nilai lebih (value for money)',
      'Menginginkan penawaran yang bisa dibuktikan secara nyata meningkatkan efisiensi biaya',
      'Terbiasa membandingkan dengan supplier lain karena pasar sangat kompetitif',
      'Ingin menghindari kerugian atau margin yang terlalu kecil untuk operasional internal'
    ],
    avatar: '/assets/avatars/aggressive-buyer.png'
  }
};

/**
 * Create a new negotiation session
 */
export const createNegotiationSession = (
  customerPersonaType: CustomerPersonaType,
  selectedProducts: { product: Product; quantity: number }[],
  targetMargin: number,
  selectedKnowledgeEntries: KnowledgeEntry[] = []
): NegotiationSession => {
  const customerPersona = CUSTOMER_PERSONAS[customerPersonaType];

  // If no products are selected, use the default product
  const products = selectedProducts.length > 0
    ? selectedProducts
    : [{ product: DEFAULT_PRODUCT, quantity: 1 }];

  return {
    id: `neg-${Date.now()}`,
    startedAt: new Date(),
    customerPersona,
    selectedProducts: products.map(item => ({
      product: item.product,
      quantity: item.quantity,
      originalPrice: item.product.price
    })),
    selectedKnowledgeEntries,
    messages: [],
    targetMargin,
    status: 'in_progress'
  };
};

/**
 * Send a message to the AI and get a response
 */
export const sendMessageToAI = async (
  session: NegotiationSession,
  message: string
): Promise<NegotiationAIResponse> => {
  // Add user message to session
  const userMessage: NegotiationMessage = {
    id: `msg-${Date.now()}`,
    sender: 'user',
    content: message,
    timestamp: new Date()
  };

  session.messages.push(userMessage);

  // Calculate current margin if any prices have been negotiated
  let currentMargin = calculateCurrentMargin(session);

  // Prepare AI request
  const aiRequest: NegotiationAIRequest = {
    customerPersona: session.customerPersona,
    selectedProducts: session.selectedProducts,
    messages: session.messages,
    targetMargin: session.targetMargin,
    currentMargin,
    isFirstMessage: session.messages.length === 1,
    knowledgeEntries: session.selectedKnowledgeEntries
  };

  // Get AI response
  const aiResponse = await callAI(aiRequest);

  // Add AI message to session
  const aiMessage: NegotiationMessage = {
    id: `msg-${Date.now()}`,
    sender: 'ai',
    content: aiResponse.message,
    timestamp: new Date()
  };

  session.messages.push(aiMessage);

  return aiResponse;
};

/**
 * Evaluate the negotiation session
 */
export const evaluateNegotiation = async (
  session: NegotiationSession
): Promise<NegotiationEvaluation> => {
  // Mark session as completed
  session.status = 'completed';
  session.endedAt = new Date();

  // Calculate final margin
  session.actualMargin = calculateCurrentMargin(session);

  // Prepare AI request for evaluation
  const aiRequest: NegotiationAIRequest = {
    customerPersona: session.customerPersona,
    selectedProducts: session.selectedProducts,
    messages: session.messages,
    targetMargin: session.targetMargin,
    currentMargin: session.actualMargin,
    isEvaluation: true,
    knowledgeEntries: session.selectedKnowledgeEntries
  };

  // Get AI evaluation
  const aiResponse = await callAI(aiRequest);

  if (aiResponse.evaluation) {
    session.evaluation = aiResponse.evaluation;
    return aiResponse.evaluation;
  }

  // Fallback evaluation if AI doesn't provide one
  const fallbackEvaluation: NegotiationEvaluation = {
    marginAchieved: (session.actualMargin || 0) >= session.targetMargin,
    marginDifference: (session.actualMargin || 0) - session.targetMargin,
    discountAppropriate: true,
    discountReason: 'Evaluasi cadangan - tidak dapat menganalisis ketepatan diskon',
    alternativeStrategies: ['Pertimbangkan bundling produk', 'Tawarkan layanan nilai tambah sebagai pengganti diskon'],
    overallScore: 70,
    strengths: ['Berhasil menyelesaikan negosiasi'],
    improvements: ['Tingkatkan strategi negosiasi'],
    keyInsights: 'Evaluasi cadangan - silakan coba lagi untuk mendapatkan wawasan yang lebih detail'
  };

  session.evaluation = fallbackEvaluation;
  return fallbackEvaluation;
};

/**
 * Calculate the current margin based on negotiated prices
 */
const calculateCurrentMargin = (session: NegotiationSession): number => {
  const totalCost = session.selectedProducts.reduce(
    (sum, item) => sum + (item.product.price * item.quantity),
    0
  );

  const totalRevenue = session.selectedProducts.reduce(
    (sum, item) => sum + ((item.negotiatedPrice || item.originalPrice) * item.quantity),
    0
  );

  if (totalCost === 0) return 0;

  return ((totalRevenue - totalCost) / totalCost) * 100;
};

/**
 * Call the AI service with the negotiation request
 */
const callAI = async (request: NegotiationAIRequest): Promise<NegotiationAIResponse> => {
  try {
    console.log('Calling AI for negotiation:', request);

    // Format products for the prompt
    const productsText = request.selectedProducts.map(item =>
      `- ${item.product.materialDescription} (${item.quantity} units): ${formatCurrency(item.originalPrice)} per unit`
    ).join('\n');

    // Format negotiation history
    const negotiationHistory = request.messages.map(msg =>
      `${msg.sender === 'ai' ? request.customerPersona.name : 'Sales'}: ${msg.content}`
    ).join('\n\n');

    // Format knowledge entries
    const knowledgeText = request.knowledgeEntries && request.knowledgeEntries.length > 0
      ? request.knowledgeEntries.map(entry =>
          `### ${entry.title} (${entry.category.replace(/_/g, ' ')})\n${entry.content}`
        ).join('\n\n')
      : '';

    let systemPrompt = '';
    let userPrompt = '';

    if (request.isEvaluation) {
      // Evaluation prompt
      systemPrompt = `Bertindaklah Seperti Ahli Negosiasi kelas dunia untuk menganalisa dan memberi masukan kepada tim salesh, dimana kamu sudah dilatih dalam negosiasi sandera FBI dan kesepakatan bisnis bernilai tinggi, Tugasmu adalah mengembangkan strategi negosiasi untuk produk yang dipilih dan memberikan evalusi saran dan cara yang matang.`;

      userPrompt = `Mohon evaluasi negosiasi yang telah selesai ini antara perwakilan penjualan dan ${request.customerPersona.name} dari ${request.customerPersona.company}.

Persona Pelanggan: ${request.customerPersona.description}
Gaya Negosiasi: ${request.customerPersona.negotiationStyle}

Produk yang Dibahas:
${productsText}

Target Margin: ${request.targetMargin}%
Margin Aktual yang Dicapai: ${request.currentMargin?.toFixed(2)}%

Transkrip Negosiasi:
${negotiationHistory}

Berikan evaluasi detail dalam format JSON dengan struktur berikut (PENTING: semua teks harus dalam Bahasa Indonesia):
{
  "marginAchieved": boolean,
  "marginDifference": number,
  "discountAppropriate": boolean,
  "discountReason": "penjelasan dalam Bahasa Indonesia apakah diskon yang diberikan sudah tepat",
  "alternativeStrategies": ["daftar strategi alternatif dalam Bahasa Indonesia yang bisa digunakan"],
  "overallScore": number (0-100),
  "strengths": ["daftar kekuatan negosiasi dalam Bahasa Indonesia yang ditunjukkan"],
  "improvements": ["daftar area yang perlu ditingkatkan dalam Bahasa Indonesia"],
  "keyInsights": "wawasan utama dalam Bahasa Indonesia dari negosiasi ini"
}`;
    } else if (request.isFirstMessage) {
      // First message prompt - customer initiates the conversation in Bahasa Indonesia
      systemPrompt = `Kamu adalah ${request.customerPersona.name} dari ${request.customerPersona.company}, ${request.customerPersona.description} Kamu sedang melakukan negosiasi dengan seorang sales dari PT Chitra Paratama.

Karakter dan Gaya Negosiasi:
- ${request.customerPersona.negotiationStyle}
- Tujuan utama adalah mendapatkan harga terbaik, bonus tambahan, atau bundling yang menguntungkan
- Tidak mudah percaya dengan penawaran awal sales

Fokus & Kekhawatiran:
${request.customerPersona.painPoints.map(point => `- ${point}`).join('\n')}

Nilai yang Dipegang:
${request.customerPersona.valueFeatures.map(value => `- ${value}`).join('\n')}

Cara Komunikasi:
- Gunakan bahasa Indonesia sehari-hari, profesional tapi santai seperti pelanggan sungguhan
- Jangan gunakan bahasa Inggris kecuali untuk nama produk atau istilah teknis
- Hindari tanggapan template seperti "I'm having trouble…" atau "Let's reconnect"

Konteks Negosiasi:
- Produk yang diminati: ${request.selectedProducts[0]?.product.materialDescription || '10.00 - 20 GT MILLER'}
- Harga produk: ${formatCurrency(request.selectedProducts[0]?.originalPrice || 3157882)} per unit
- Jumlah yang diminati: ${request.selectedProducts[0]?.quantity || 1} unit (bisa bertambah kalau penawaran menarik)
- Kamu menginginkan minimal diskon 10% atau bundling dengan sensor gratis (contoh: CTS atau VBOX)

Perilaku Saat Simulasi:
- Jawab setiap pertanyaan sales seperti pembeli sungguhan
- Uji kemampuan sales dalam menawarkan alternatif: diskon, bundling, atau promosi
- Jika penawaran terlalu standar, beri tekanan: "supplier lain bisa lebih murah", "kami butuh bukti value"
- Jangan langsung menerima tawaran — buat percakapan berlangsung 3–5 putaran negosiasi

${knowledgeText ? `\nPengetahuan Tambahan (gunakan informasi ini untuk memperkaya karakter dan respons kamu):\n${knowledgeText}` : ''}`;

      userPrompt = `Mulailah percakapan dengan sales dari PT Chitra Paratama. Perkenalkan diri, tunjukkan ketertarikan pada produk ban ${request.selectedProducts[0]?.product.materialDescription || '10.00 - 20 GT MILLER'} dengan harga ${formatCurrency(request.selectedProducts[0]?.originalPrice || 3157882)} per unit, tapi langsung mulai negosiasi sesuai karakter kamu yang tegas. Gunakan bahasa Indonesia yang natural dan profesional.`;
    } else {
      // Ongoing conversation prompt in Bahasa Indonesia
      systemPrompt = `Kamu adalah ${request.customerPersona.name} dari ${request.customerPersona.company}, ${request.customerPersona.description} Kamu sedang melakukan negosiasi dengan seorang sales dari PT Chitra Paratama.

Karakter dan Gaya Negosiasi:
- ${request.customerPersona.negotiationStyle}
- Tujuan utama adalah mendapatkan harga terbaik, bonus tambahan, atau bundling yang menguntungkan
- Tidak mudah percaya dengan penawaran awal sales

Fokus & Kekhawatiran:
${request.customerPersona.painPoints.map(point => `- ${point}`).join('\n')}

Nilai yang Dipegang:
${request.customerPersona.valueFeatures.map(value => `- ${value}`).join('\n')}

Cara Komunikasi:
- Gunakan bahasa Indonesia sehari-hari, profesional tapi santai seperti pelanggan sungguhan
- Jangan gunakan bahasa Inggris kecuali untuk nama produk atau istilah teknis
- Hindari tanggapan template seperti "I'm having trouble…" atau "Let's reconnect"

Konteks Negosiasi:
- Produk yang diminati: ${request.selectedProducts[0]?.product.materialDescription || '10.00 - 20 GT MILLER'}
- Harga produk: ${formatCurrency(request.selectedProducts[0]?.originalPrice || 3157882)} per unit
- Jumlah yang diminati: ${request.selectedProducts[0]?.quantity || 1} unit (bisa bertambah kalau penawaran menarik)
- Kamu menginginkan minimal diskon 10% atau bundling dengan sensor gratis (contoh: CTS atau VBOX)

Perilaku Saat Simulasi:
- Jawab setiap pertanyaan sales seperti pembeli sungguhan
- Uji kemampuan sales dalam menawarkan alternatif: diskon, bundling, atau promosi
- Jika penawaran terlalu standar, beri tekanan: "supplier lain bisa lebih murah", "kami butuh bukti value"
- Jangan langsung menerima tawaran — buat percakapan berlangsung 3–5 putaran negosiasi`;

      userPrompt = `Ini adalah percakapan yang sudah berlangsung:
${negotiationHistory}

Tanggapi pesan terakhir dari sales dengan gaya negosiasi kamu yang tegas. Ingat untuk tetap menggunakan bahasa Indonesia yang natural. Jika negosiasi terlihat akan berakhir, kamu bisa menerima penawaran jika sudah sesuai harapan atau berikan penawaran balik terakhir.`;
    }

    // Make the API call to OpenRouter
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Negotiation Simulator'
      },
      body: JSON.stringify({
        model: DEFAULT_MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 1500
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('AI response:', data);

    // Extract the content from the response
    const content = data.choices[0].message.content;

    if (request.isEvaluation) {
      try {
        // Try to parse the JSON evaluation
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        const jsonString = jsonMatch ? jsonMatch[0] : content;
        const evaluation = JSON.parse(jsonString);

        return {
          message: 'Evaluation completed',
          evaluation
        };
      } catch (error) {
        console.error('Error parsing evaluation JSON:', error);
        return {
          message: content,
          evaluation: undefined
        };
      }
    }

    return {
      message: content
    };
  } catch (error) {
    console.error('Error calling AI service:', error);

    // Return a fallback response
    if (request.isEvaluation) {
      return {
        message: 'Tidak dapat mengevaluasi negosiasi karena kesalahan teknis.',
        evaluation: {
          marginAchieved: false,
          marginDifference: 0,
          discountAppropriate: false,
          discountReason: 'Tidak dapat mengevaluasi karena kesalahan teknis',
          alternativeStrategies: ['Coba lagi nanti'],
          overallScore: 0,
          strengths: [],
          improvements: ['Coba lagi ketika layanan tersedia'],
          keyInsights: 'Terjadi kesalahan teknis selama evaluasi'
        }
      };
    }

    return {
      message: request.isFirstMessage
        ? `Halo, saya ${request.customerPersona.name} dari ${request.customerPersona.company}. Saya tertarik dengan produk ban ${request.selectedProducts[0]?.product.materialDescription || '10.00 - 20 GT MILLER'} dengan harga ${formatCurrency(request.selectedProducts[0]?.originalPrice || 3157882)} per unit, tapi sedang ada masalah koneksi. Bisa kita coba lagi sebentar?`
        : "Maaf, sepertinya ada kendala teknis di sisi saya. Bisa kita lanjutkan percakapan ini sebentar lagi?"
    };
  }
};

/**
 * Local storage key for WhatsApp Web conversations
 */
const WHATSAPP_WEB_STORAGE_KEY = 'chitraMarketingTools_whatsappWebConversations';

/**
 * Save WhatsApp Web conversation to local storage
 */
export const saveWhatsAppWebConversation = (
  conversation: string,
  customerName: string,
  salesName: string
): void => {
  try {
    // Create conversation object
    const conversationObj = {
      id: `whatsapp-web-${Date.now()}`,
      timestamp: new Date(),
      conversation,
      customerName,
      salesName
    };

    // Get existing conversations
    const conversationsJson = localStorage.getItem(WHATSAPP_WEB_STORAGE_KEY);
    const conversations = conversationsJson ? JSON.parse(conversationsJson) : [];

    // Add new conversation
    conversations.push(conversationObj);

    // Save to local storage
    localStorage.setItem(WHATSAPP_WEB_STORAGE_KEY, JSON.stringify(conversations));
  } catch (error) {
    console.error('Error saving WhatsApp Web conversation:', error);
  }
};

/**
 * Analyze WhatsApp Web conversation and answer questions
 */
export const analyzeWhatsAppWebConversation = async (
  conversation: string,
  _customerName: string, // Unused parameter, kept for compatibility
  _salesName: string // Unused parameter, kept for compatibility
): Promise<{ message: string }> => {
  try {
    console.log('Analyzing WhatsApp Web conversation');

    // Extract the question from the conversation
    const questionMatch = conversation.match(/Pertanyaan: (.*)/);
    const question = questionMatch ? questionMatch[1] : '';

    // Prepare system prompt
    const systemPrompt = `Bertindaklah Seperti Ahli Negosiasi kelas dunia untuk menganalisa dan memberi masukan kepada tim sales, dimana kamu sudah dilatih dalam negosiasi sandera FBI dan kesepakatan bisnis bernilai tinggi.

Tugasmu adalah menjawab pertanyaan pengguna tentang strategi negosiasi, teknik penjualan, dan cara merespons pelanggan di WhatsApp. Berikan jawaban yang spesifik, praktis, dan dapat langsung diterapkan.

Kamu adalah asisten AI yang membantu sales PT Chitra Paratama (perusahaan distributor ban) untuk berkomunikasi lebih efektif dengan pelanggan melalui WhatsApp.

Beberapa informasi penting tentang PT Chitra Paratama:
1. Perusahaan adalah distributor ban premium untuk kendaraan berat, alat berat, dan industri pertambangan
2. Produk utama adalah ban untuk truk, bus, alat berat pertambangan, dan industri
3. Pelanggan utama adalah perusahaan pertambangan, logistik, transportasi, dan kontraktor
4. Keunggulan produk: kualitas premium, daya tahan tinggi, efisiensi bahan bakar, dan layanan purna jual
5. Strategi penjualan: bundling produk, diskon kuantitas, program loyalitas, dan solusi total biaya kepemilikan (TCO)

Berikan jawaban yang:
1. Praktis dan dapat langsung digunakan dalam percakapan WhatsApp
2. Menggunakan bahasa Indonesia yang profesional dan persuasif
3. Mencakup contoh template pesan yang bisa langsung disalin
4. Mempertimbangkan psikologi pelanggan dan teknik negosiasi modern
5. Membantu mencapai tujuan penjualan sambil membangun hubungan jangka panjang`;

    // Prepare user prompt
    const userPrompt = `Pertanyaan saya tentang percakapan WhatsApp dengan pelanggan adalah: ${question}

Saya adalah sales PT Chitra Paratama yang menjual ban premium untuk kendaraan berat dan alat berat pertambangan. Saya berkomunikasi dengan pelanggan melalui WhatsApp dan membutuhkan bantuan untuk:
1. Meningkatkan efektivitas komunikasi
2. Merespons pertanyaan dan keberatan dengan baik
3. Menawarkan produk dan promosi secara persuasif
4. Menindaklanjuti prospek dan menutup penjualan

Berikan jawaban dalam format berikut:
1. STRATEGI UTAMA: Penjelasan singkat tentang pendekatan terbaik
2. TEMPLATE PESAN: Contoh pesan WhatsApp yang bisa langsung disalin dan digunakan
3. POIN PENTING: 3-5 hal yang perlu diperhatikan dalam situasi ini
4. ALTERNATIF: Pendekatan lain yang bisa dicoba jika pendekatan utama tidak berhasil

Berikan jawaban dalam Bahasa Indonesia yang jelas, profesional, dan mudah diterapkan.`;

    // Make the API call to OpenRouter
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - WhatsApp Web Analysis'
      },
      body: JSON.stringify({
        model: DEFAULT_MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 1500
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('AI response:', data);

    // Extract the content from the response
    const content = data.choices[0].message.content;

    return {
      message: content
    };
  } catch (error) {
    console.error('Error analyzing WhatsApp Web conversation:', error);
    return {
      message: 'Terjadi kesalahan saat menganalisis percakapan WhatsApp Web. Silakan coba lagi nanti.'
    };
  }
};