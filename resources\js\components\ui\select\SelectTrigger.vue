<script setup lang="ts">
import { cn } from '@/lib/utils';
import { inject } from 'vue';

const props = defineProps({
  placeholder: {
    type: String,
    default: 'Select an option',
  },
});

const select = inject('select', {
  open: { value: false },
  value: { value: '' },
  disabled: false,
  updateValue: (value: string) => {},
  toggle: () => {},
});
</script>

<template>
  <button
    type="button"
    :class="
      cn(
        'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
        { 'opacity-50 cursor-not-allowed': select.disabled }
      )
    "
    :aria-expanded="select.open.value"
    :disabled="select.disabled"
    @click="select.toggle()"
  >
    <slot>
      <span v-if="select.value.value" class="text-foreground">
        <slot name="selected-value"></slot>
      </span>
      <span v-else class="text-muted-foreground">{{ placeholder }}</span>
    </slot>
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="h-4 w-4 opacity-50"
      :class="{ 'rotate-180': select.open.value }"
    >
      <polyline points="6 9 12 15 18 9"></polyline>
    </svg>
  </button>
</template>