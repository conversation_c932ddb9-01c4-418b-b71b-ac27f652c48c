import axios from 'axios';
import { MODELS } from './openRouter';
import {
  SeasonalAnalysisRequest,
  SeasonalAnalysisResponse,
  SeasonalInsight,
  RecommendationLevel,
  SeasonalFactor,
  SeasonalFactorType
} from '../types/seasonalMarketing';
import { getSeasonalFactors, calculateRecommendationLevel, saveSeasonalFactor } from './seasonalMarketingService';
import { fetchWeatherForecast, getAllRegionsWeatherForecast, getRainySeasonStatus } from './weatherService';
import { analyzeCoalPriceTrend, getCoalPriceImpactDescription } from './coalPriceService';

// Using the same API key as the other AI services
const API_KEY = '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';

// Define a specialized model for data analysis
const DATA_ANALYSIS_MODEL = MODELS.CLAUDE; // Using GPT-4.1-nano for analytical tasks

// Local storage key for insights
const SEASONAL_INSIGHTS_KEY = 'seasonalInsights';

/**
 * Get all seasonal insights
 */
export const getSeasonalInsights = (): SeasonalInsight[] => {
  try {
    const storedInsights = localStorage.getItem(SEASONAL_INSIGHTS_KEY);
    if (storedInsights) {
      return JSON.parse(storedInsights);
    }
    return [];
  } catch (error) {
    console.error('Error getting seasonal insights:', error);
    return [];
  }
};

/**
 * Save a seasonal insight
 */
export const saveSeasonalInsight = (insight: SeasonalInsight): SeasonalInsight => {
  try {
    const insights = getSeasonalInsights();

    // Check if this is an update or a new insight
    const index = insights.findIndex(i => i.id === insight.id);

    if (index >= 0) {
      // Update existing insight
      insights[index] = insight;
    } else {
      // Add new insight with generated ID if not provided
      if (!insight.id) {
        insight.id = `insight-${Date.now()}`;
      }
      insights.push(insight);
    }

    // Save to local storage
    localStorage.setItem(SEASONAL_INSIGHTS_KEY, JSON.stringify(insights));
    return insight;
  } catch (error) {
    console.error('Error saving seasonal insight:', error);
    throw new Error('Failed to save seasonal insight');
  }
};

/**
 * Delete a seasonal insight
 */
export const deleteSeasonalInsight = (id: string): boolean => {
  try {
    const insights = getSeasonalInsights();
    const updatedInsights = insights.filter(i => i.id !== id);

    if (updatedInsights.length === insights.length) {
      return false; // No insight was deleted
    }

    localStorage.setItem(SEASONAL_INSIGHTS_KEY, JSON.stringify(updatedInsights));
    return true;
  } catch (error) {
    console.error('Error deleting seasonal insight:', error);
    return false;
  }
};

/**
 * Analyze seasonal data to generate insights
 */
export const analyzeSeasonalData = async (request: SeasonalAnalysisRequest): Promise<SeasonalAnalysisResponse> => {
  try {
    console.log('Analyzing seasonal data with AI model:', DATA_ANALYSIS_MODEL);

    // Get existing seasonal factors
    const existingFactors = getSeasonalFactors();

    // Filter factors relevant to the requested timeframe
    const relevantFactors = existingFactors.filter(factor => {
      if (!factor.startDate || !factor.endDate) return true;

      const factorStart = new Date(factor.startDate);
      const factorEnd = new Date(factor.endDate);
      const requestStart = new Date(request.timeframe.startDate);
      const requestEnd = new Date(request.timeframe.endDate);

      // Check if there's any overlap between the factor's timeframe and the requested timeframe
      return factorStart <= requestEnd && factorEnd >= requestStart;
    });

    // Fetch weather forecast data
    console.log('Fetching weather forecast data...');
    let weatherData = null;
    let weatherFactors: SeasonalFactor[] = [];

    try {
      // Get weather forecasts for all mining regions
      const forecasts = await getAllRegionsWeatherForecast();
      weatherData = forecasts;

      // Create weather factors based on forecasts
      for (const forecast of forecasts) {
        if (forecast.rainyDays > 0) {
          const impact = calculateWeatherImpact(forecast.rainyDays, forecast.forecasts.length);

          // Create a weather factor
          const weatherFactor: SeasonalFactor = {
            id: `weather-${Date.now()}-${forecast.location.replace(/\s+/g, '-').toLowerCase()}`,
            type: SeasonalFactorType.WEATHER,
            name: `Curah Hujan di ${forecast.location}`,
            description: `${forecast.rainyDays} hari hujan dari ${forecast.forecasts.length} hari. Curah hujan rata-rata: ${forecast.averagePrecipitation.toFixed(1)}mm.`,
            impact,
            startDate: forecast.startDate,
            endDate: forecast.endDate,
            region: forecast.location,
            source: 'Weather API'
          };

          // Add to weather factors
          weatherFactors.push(weatherFactor);

          // Don't automatically save the factor to the database
          // saveSeasonalFactor(weatherFactor);
        }
      }

      // Get rainy season status
      const rainySeasonStatus = getRainySeasonStatus();
      if (rainySeasonStatus.isRainySeason) {
        // Create a rainy season factor
        const rainySeasonFactor: SeasonalFactor = {
          id: `rainy-season-${Date.now()}`,
          type: SeasonalFactorType.WEATHER,
          name: 'Musim Hujan Indonesia',
          description: `Musim hujan di Indonesia dengan intensitas ${rainySeasonStatus.intensity.toLowerCase()}.`,
          impact: rainySeasonStatus.intensity === 'Tinggi' ? -8 :
                 rainySeasonStatus.intensity === 'Sedang' ? -5 : -3,
          startDate: new Date(new Date().getFullYear(), rainySeasonStatus.startMonth - 1, 1).toISOString().split('T')[0],
          endDate: new Date(new Date().getFullYear() + (rainySeasonStatus.endMonth < rainySeasonStatus.startMonth ? 1 : 0), rainySeasonStatus.endMonth - 1, 28).toISOString().split('T')[0],
          region: 'Indonesia',
          source: 'Weather Analysis'
        };

        // Add to weather factors
        weatherFactors.push(rainySeasonFactor);

        // Don't automatically save the factor to the database
        // saveSeasonalFactor(rainySeasonFactor);
      }
    } catch (weatherError) {
      console.error('Error fetching weather data:', weatherError);
    }

    // Fetch coal price data
    console.log('Analyzing coal price trends...');
    let coalPriceData = null;
    let coalPriceFactor: SeasonalFactor | null = null;

    try {
      const coalPriceTrend = await analyzeCoalPriceTrend();
      coalPriceData = coalPriceTrend;

      // Create a coal price factor
      coalPriceFactor = {
        id: `coal-price-${Date.now()}`,
        type: SeasonalFactorType.ECONOMIC,
        name: `Tren Harga Batu Bara (${coalPriceTrend.trend})`,
        description: getCoalPriceImpactDescription(coalPriceTrend),
        impact: coalPriceTrend.impact,
        startDate: request.timeframe.startDate,
        endDate: request.timeframe.endDate,
        region: 'Global',
        source: 'Coal Price Analysis'
      };

      // Don't automatically save the factor to the database
      // saveSeasonalFactor(coalPriceFactor);
    } catch (coalError) {
      console.error('Error analyzing coal price data:', coalError);
    }

    // Combine all factors
    const allFactors = [
      ...relevantFactors,
      ...weatherFactors
    ];

    if (coalPriceFactor) {
      allFactors.push(coalPriceFactor);
    }

    // Prepare the prompt for AI analysis
    const requestBody = {
      model: DATA_ANALYSIS_MODEL,
      messages: [
        {
          role: 'system',
          content: `You are an expert marketing analyst specializing in the tire industry, particularly for mining and heavy equipment applications. You analyze seasonal trends, market conditions, and industry factors to provide strategic recommendations for optimal timing of marketing campaigns and promotions.

Your analysis should be data-driven, considering multiple factors including weather patterns, mining operational cycles, economic indicators, competitor activities, historical sales data, industry events, customer budget cycles, and maintenance schedules.

You will provide insights in a structured format that can be parsed as JSON.`
        },
        {
          role: 'user',
          content: `Analyze the following seasonal data for tire marketing in the mining industry and provide strategic recommendations for the timeframe from ${request.timeframe.startDate} to ${request.timeframe.endDate}${request.region ? ` in the ${request.region} region` : ''}.

Relevant seasonal factors:
${allFactors.map(factor => `- ${factor.name} (${factor.type}): ${factor.description}. Impact: ${factor.impact > 0 ? '+' : ''}${factor.impact}/10. ${factor.startDate && factor.endDate ? `Period: ${factor.startDate} to ${factor.endDate}` : ''} ${factor.region ? `Region: ${factor.region}` : ''}`).join('\n')}

${weatherData ? `Weather forecast data shows ${weatherData.reduce((total, forecast) => total + forecast.rainyDays, 0)} rainy days across all mining regions in the forecast period. This affects mining operations and tire demand.` : ''}

${coalPriceData ? `Coal price analysis: Current price is $${coalPriceData.currentPrice.toFixed(2)} USD with a ${coalPriceData.trend} trend (${coalPriceData.percentChange > 0 ? '+' : ''}${coalPriceData.percentChange.toFixed(2)}%). The forecast is ${coalPriceData.forecast}.` : ''}

${request.productCategories && request.productCategories.length > 0 ? `Focus on these product categories: ${request.productCategories.join(', ')}` : ''}

Important context:
1. PT Chitra Paratama is a distributor of heavy equipment tires, primarily for mining operations.
2. Rainy seasons in Indonesia significantly impact mining operations, reducing activity and tire demand.
3. Coal prices directly affect mining activity - higher prices generally mean more mining operations and higher tire demand.
4. Mining companies typically plan maintenance during rainy seasons when operations are already reduced.
5. The most important mining regions are in Kalimantan, Sumatra, and Papua.

Provide your analysis in JSON format with these fields:
- insights: Array of insight objects with {id, title, description, startDate, endDate, recommendationLevel, factors, recommendedPromoTypes, recommendedProducts, potentialImpact, confidence}
- recommendedTimeframes: Array of recommended timeframe objects with {startDate, endDate, score, recommendationLevel, primaryFactors}
- analysis: String with overall textual analysis

Your response should ONLY contain valid JSON that can be parsed.`
        }
      ]
    };

    // Call the OpenRouter API
    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chitraparatama.co.id',
          'X-Title': 'Chitra Marketing Tools'
        }
      }
    );

    // Extract the AI response
    const aiResponse = response.data.choices[0].message.content;

    // Parse the JSON response
    try {
      // Extract JSON from the response (in case there's any text before or after)
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in the response');
      }

      const jsonResponse = JSON.parse(jsonMatch[0]);

      // Process and save insights
      if (jsonResponse.insights && Array.isArray(jsonResponse.insights)) {
        jsonResponse.insights.forEach((insight: SeasonalInsight) => {
          // Add aiGenerated flag
          insight.aiGenerated = true;

          // Save the insight
          saveSeasonalInsight(insight);
        });
      }

      return jsonResponse;
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      console.log('Raw AI response:', aiResponse);

      // Return a fallback response
      return generateFallbackAnalysis(request, relevantFactors);
    }
  } catch (error) {
    console.error('Error analyzing seasonal data:', error);

    // Return a fallback response
    return generateFallbackAnalysis(request, getSeasonalFactors());
  }
};

/**
 * Calculate the impact of weather on mining operations and tire demand
 * @param rainyDays Number of rainy days in the forecast period
 * @param totalDays Total number of days in the forecast period
 * @returns Impact score from -10 to 10
 */
const calculateWeatherImpact = (rainyDays: number, totalDays: number): number => {
  // Calculate the percentage of rainy days
  const rainyDaysPercentage = (rainyDays / totalDays) * 100;

  // Calculate impact based on percentage of rainy days
  // More rainy days = more negative impact on mining operations and tire demand
  if (rainyDaysPercentage >= 70) {
    return -10; // Extremely rainy - severe negative impact
  } else if (rainyDaysPercentage >= 50) {
    return -8; // Very rainy - strong negative impact
  } else if (rainyDaysPercentage >= 30) {
    return -5; // Moderately rainy - moderate negative impact
  } else if (rainyDaysPercentage >= 15) {
    return -3; // Slightly rainy - slight negative impact
  } else if (rainyDaysPercentage > 0) {
    return -1; // Minimal rain - minimal negative impact
  } else {
    return 2; // No rain - positive impact
  }
};

/**
 * Generate a fallback analysis when AI fails
 */
const generateFallbackAnalysis = (
  request: SeasonalAnalysisRequest,
  factors: SeasonalFactor[]
): SeasonalAnalysisResponse => {
  // Filter factors relevant to the requested timeframe
  const relevantFactors = factors.filter(factor => {
    if (!factor.startDate || !factor.endDate) return true;

    const factorStart = new Date(factor.startDate);
    const factorEnd = new Date(factor.endDate);
    const requestStart = new Date(request.timeframe.startDate);
    const requestEnd = new Date(request.timeframe.endDate);

    // Check if there's any overlap
    return factorStart <= requestEnd && factorEnd >= requestStart;
  });

  // Sort factors by impact (highest first)
  const sortedFactors = [...relevantFactors].sort((a, b) => b.impact - a.impact);

  // Get top positive and negative factors
  const topPositiveFactors = sortedFactors.filter(f => f.impact > 0).slice(0, 2);
  const topNegativeFactors = sortedFactors.filter(f => f.impact < 0).slice(0, 2);

  // Calculate average impact
  const avgImpact = relevantFactors.length > 0
    ? relevantFactors.reduce((sum, f) => sum + f.impact, 0) / relevantFactors.length
    : 0;

  // Convert to 0-100 scale
  const score = Math.max(0, Math.min(100, 50 + avgImpact * 5));

  // Generate insights based on factor types
  const insights: SeasonalInsight[] = [];

  // Weather insight
  const weatherFactors = relevantFactors.filter(f => f.type === SeasonalFactorType.WEATHER);
  if (weatherFactors.length > 0) {
    const avgWeatherImpact = weatherFactors.reduce((sum, f) => sum + f.impact, 0) / weatherFactors.length;
    const weatherScore = Math.max(0, Math.min(100, 50 + avgWeatherImpact * 5));

    insights.push({
      id: `insight-weather-${Date.now()}`,
      title: 'Analisis Dampak Cuaca pada Operasi Pertambangan',
      description: `Berdasarkan analisis ${weatherFactors.length} faktor cuaca, kondisi cuaca ${avgWeatherImpact < 0 ? 'kurang mendukung' : 'mendukung'} operasi pertambangan pada periode ini. ${weatherFactors.map(f => f.name).join(', ')} memiliki dampak signifikan pada aktivitas pertambangan dan permintaan ban.`,
      startDate: request.timeframe.startDate,
      endDate: request.timeframe.endDate,
      recommendationLevel: calculateRecommendationLevel(weatherScore),
      factors: weatherFactors,
      recommendedPromoTypes: avgWeatherImpact < 0 ? ['Bundling dengan Diskon', 'Program Pemeliharaan'] : ['Bundling Volume', 'Paket Hemat'],
      recommendedProducts: ['Ban Earthmover', 'Ban OTR'],
      potentialImpact: weatherScore,
      confidence: 80,
      aiGenerated: false
    });
  }

  // Economic insight (coal price)
  const economicFactors = relevantFactors.filter(f => f.type === SeasonalFactorType.ECONOMIC);
  if (economicFactors.length > 0) {
    const avgEconomicImpact = economicFactors.reduce((sum, f) => sum + f.impact, 0) / economicFactors.length;
    const economicScore = Math.max(0, Math.min(100, 50 + avgEconomicImpact * 5));

    insights.push({
      id: `insight-economic-${Date.now()}`,
      title: 'Analisis Dampak Ekonomi dan Harga Batu Bara',
      description: `Berdasarkan analisis ${economicFactors.length} faktor ekonomi, kondisi ekonomi dan harga batu bara ${avgEconomicImpact < 0 ? 'kurang mendukung' : 'mendukung'} aktivitas pertambangan pada periode ini. ${economicFactors.map(f => f.name).join(', ')} memiliki dampak signifikan pada operasi pertambangan dan permintaan ban.`,
      startDate: request.timeframe.startDate,
      endDate: request.timeframe.endDate,
      recommendationLevel: calculateRecommendationLevel(economicScore),
      factors: economicFactors,
      recommendedPromoTypes: avgEconomicImpact < 0 ? ['Diskon Khusus', 'Program Loyalitas'] : ['Bundling Premium', 'Paket Nilai Tambah'],
      recommendedProducts: ['Ban Earthmover Premium', 'Ban OTR Tahan Lama'],
      potentialImpact: economicScore,
      confidence: 75,
      aiGenerated: false
    });
  }

  // General insight
  const generalInsight: SeasonalInsight = {
    id: `insight-general-${Date.now()}`,
    title: 'Analisis Musiman Komprehensif',
    description: `Analisis otomatis berdasarkan ${relevantFactors.length} faktor musiman untuk periode ${request.timeframe.startDate} hingga ${request.timeframe.endDate}${request.region ? ` di wilayah ${request.region}` : ''}. Faktor cuaca dan ekonomi memiliki pengaruh signifikan pada permintaan ban pertambangan.`,
    startDate: request.timeframe.startDate,
    endDate: request.timeframe.endDate,
    recommendationLevel: calculateRecommendationLevel(score),
    factors: relevantFactors,
    recommendedPromoTypes: ['Bundling', 'Diskon Volume', 'Program Pemeliharaan'],
    recommendedProducts: ['Ban Earthmover', 'Ban OTR', 'Ban Truck Pertambangan'],
    potentialImpact: score,
    confidence: 70,
    aiGenerated: false
  };

  insights.push(generalInsight);

  // Save all insights
  insights.forEach(insight => {
    saveSeasonalInsight(insight);
  });

  // Return the fallback analysis
  return {
    insights,
    recommendedTimeframes: [
      {
        startDate: request.timeframe.startDate,
        endDate: request.timeframe.endDate,
        score,
        recommendationLevel: calculateRecommendationLevel(score),
        primaryFactors: [...topPositiveFactors, ...topNegativeFactors]
      }
    ],
    analysis: `Berdasarkan analisis ${relevantFactors.length} faktor musiman, periode ini memiliki skor rekomendasi ${score.toFixed(1)}/100 (${calculateRecommendationLevel(score)}). ${topPositiveFactors.length > 0 ? `Faktor positif utama: ${topPositiveFactors.map(f => f.name).join(', ')}. ` : ''}${topNegativeFactors.length > 0 ? `Faktor negatif utama: ${topNegativeFactors.map(f => f.name).join(', ')}.` : ''} ${weatherFactors.length > 0 ? `\n\nFaktor cuaca: ${weatherFactors.map(f => f.name).join(', ')} memiliki dampak signifikan pada operasi pertambangan.` : ''} ${economicFactors.length > 0 ? `\n\nFaktor ekonomi: ${economicFactors.map(f => f.name).join(', ')} mempengaruhi aktivitas pertambangan dan permintaan ban.` : ''}`
  };
};
