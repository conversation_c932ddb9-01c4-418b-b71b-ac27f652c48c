/**
 * Types for the WhatsApp Chat Analysis feature
 */

/**
 * WhatsApp message structure
 */
export interface WhatsAppMessage {
  id: string;
  sender: string;
  content: string;
  timestamp: Date;
  isSales?: boolean; // Flag to identify if the sender is a sales person
}

/**
 * WhatsApp chat session
 */
export interface WhatsAppChatSession {
  id: string;
  importedAt: Date;
  analyzedAt?: Date;
  customerName: string;
  salesName: string;
  messages: WhatsAppMessage[];
  status: 'imported' | 'analyzed';
  evaluation?: WhatsAppChatEvaluation;
}

/**
 * WhatsApp chat evaluation
 */
export interface WhatsAppChatEvaluation {
  closingAchieved: boolean;
  closingEffectiveness: number; // 0-100
  responseTime: {
    average: number; // in minutes
    rating: 'excellent' | 'good' | 'average' | 'poor';
  };
  communicationStyle: {
    clarity: number; // 0-100
    professionalism: number; // 0-100
    persuasiveness: number; // 0-100
  };
  objectionHandling: {
    effectiveness: number; // 0-100
    missedOpportunities: string[];
  };
  valueProposition: {
    clarity: number; // 0-100
    relevance: number; // 0-100
  };
  overallScore: number; // 0-100
  strengths: string[];
  improvements: string[];
  keyInsights: string;
  alternativeStrategies: string[];
}

/**
 * WhatsApp chat analysis request
 */
export interface WhatsAppChatAnalysisRequest {
  messages: WhatsAppMessage[];
  customerName: string;
  salesName: string;
  isEvaluation?: boolean;
}

/**
 * WhatsApp chat analysis response
 */
export interface WhatsAppChatAnalysisResponse {
  message: string;
  evaluation?: WhatsAppChatEvaluation;
}
