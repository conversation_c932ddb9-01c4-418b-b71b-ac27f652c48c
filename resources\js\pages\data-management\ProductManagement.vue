<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Product Management</h1>
                    <p class="mt-2 text-gray-600">Ke<PERSON>la produk dan harga dengan mudah</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="exportProducts"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export
                    </button>
                    <button
                        @click="openAddModal"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <Plus class="h-4 w-4 mr-2" />
                        Tambah Produk
                    </button>
                </div>
            </div>

            <!-- Exchange Rate Card -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Global Exchange Rate USD to IDR</h3>
                        <p class="text-sm text-gray-600">{{ exchangeRate.lastUpdate }}</p>
                    </div>
                    <button
                        @click="updateExchangeRate"
                        :disabled="isUpdatingRate"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 flex items-center"
                    >
                        <Loader2 v-if="isUpdatingRate" class="h-4 w-4 mr-2 animate-spin" />
                        <RefreshCw v-else class="h-4 w-4 mr-2" />
                        Update Semua Harga IDR
                    </button>
                </div>
                <div class="text-2xl font-bold text-purple-900">{{ formatCurrency(exchangeRate.rate) }}</div>
            </div>

            <!-- Filters and Search -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex flex-col md:flex-row gap-4">
                    <!-- Search -->
                    <div class="flex-1">
                        <div class="relative">
                            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                            <input
                                v-model="searchQuery"
                                type="text"
                                placeholder="Cari produk, kode, atau deskripsi..."
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="flex gap-3">
                        <select
                            v-model="filterStatus"
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="">Semua Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>

                        <select
                            v-model="filterSlowMoving"
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="">Semua Kategori</option>
                            <option value="normal">Normal</option>
                            <option value="slow">Slow Moving</option>
                        </select>

                        <select
                            v-model="itemsPerPage"
                            class="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="10">10 per halaman</option>
                            <option value="25">25 per halaman</option>
                            <option value="50">50 per halaman</option>
                            <option value="100">100 per halaman</option>
                        </select>
                    </div>
                </div>

                <!-- Bulk Actions -->
                <div v-if="selectedProducts.length > 0" class="mt-4 p-3 bg-blue-50 rounded-lg">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-blue-700">
                            {{ selectedProducts.length }} produk dipilih
                        </span>
                        <div class="flex space-x-2">
                            <button
                                @click="bulkUpdateStatus('active')"
                                class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                            >
                                Aktifkan
                            </button>
                            <button
                                @click="bulkUpdateStatus('inactive')"
                                class="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
                            >
                                Nonaktifkan
                            </button>
                            <button
                                @click="bulkDelete"
                                class="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                            >
                                Hapus
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        Product List ({{ filteredProducts.length }})
                    </h3>
                </div>

                <div v-if="isLoading" class="p-8 text-center">
                    <Loader2 class="h-8 w-8 animate-spin mx-auto text-blue-600" />
                    <p class="mt-2 text-gray-600">Loading products...</p>
                </div>

                <div v-else-if="filteredProducts.length === 0" class="p-8 text-center">
                    <Package class="h-12 w-12 mx-auto text-gray-300 mb-4" />
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Tidak ada produk</h3>
                    <p class="text-gray-600 mb-4">
                        {{ searchQuery ? 'Tidak ada produk yang sesuai dengan pencarian.' : 'Belum ada produk yang ditambahkan.' }}
                    </p>
                    <button
                        @click="openAddModal"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                    >
                        Tambah Produk Pertama
                    </button>
                </div>

                <div v-else class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="w-12 px-6 py-3 text-left">
                                    <input
                                        type="checkbox"
                                        :checked="selectedProducts.length === paginatedProducts.length && paginatedProducts.length > 0"
                                        @change="toggleSelectAll"
                                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Material No
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Description
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Price (IDR)
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    USD Price
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr
                                v-for="product in paginatedProducts"
                                :key="product.id"
                                class="hover:bg-gray-50"
                            >
                                <td class="px-6 py-4">
                                    <input
                                        type="checkbox"
                                        :checked="selectedProducts.includes(product.id)"
                                        @change="toggleProductSelection(product.id)"
                                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ product.oldMaterialNo }}</div>
                                    <div class="text-sm text-gray-500">{{ product.materialDescription }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900">{{ product.description }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ formatCurrency(product.price) }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        {{ product.priceUSD ? formatUSD(product.priceUSD) : '-' }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span :class="[
                                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                                        product.status === 'active'
                                            ? 'bg-green-100 text-green-800'
                                            : 'bg-red-100 text-red-800'
                                    ]">
                                        {{ product.status === 'active' ? 'Active' : 'Inactive' }}
                                    </span>
                                    <div v-if="product.slowMoving" class="mt-1">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Slow Moving
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button
                                            @click="editProduct(product)"
                                            class="text-blue-600 hover:text-blue-900"
                                        >
                                            <Edit class="h-4 w-4" />
                                        </button>
                                        <button
                                            @click="deleteProduct(product.id)"
                                            :disabled="isDeleting === product.id"
                                            class="text-red-600 hover:text-red-900 disabled:opacity-50"
                                        >
                                            <Loader2 v-if="isDeleting === product.id" class="h-4 w-4 animate-spin" />
                                            <Trash2 v-else class="h-4 w-4" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div v-if="totalPages > 1" class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Menampilkan {{ ((currentPage - 1) * parseInt(itemsPerPage)) + 1 }} -
                            {{ Math.min(currentPage * parseInt(itemsPerPage), filteredProducts.length) }}
                            dari {{ filteredProducts.length }} produk
                        </div>
                        <div class="flex space-x-2">
                            <button
                                @click="currentPage--"
                                :disabled="currentPage === 1"
                                class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                            >
                                Previous
                            </button>
                            <span class="px-3 py-1 text-sm text-gray-700">
                                {{ currentPage }} / {{ totalPages }}
                            </span>
                            <button
                                @click="currentPage++"
                                :disabled="currentPage === totalPages"
                                class="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                            >
                                Next
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Success/Error Message -->
            <div v-if="message" :class="[
                'p-4 rounded-md',
                message.includes('Error') || message.includes('Gagal')
                    ? 'bg-red-50 text-red-700 border border-red-200'
                    : 'bg-green-50 text-green-700 border border-green-200'
            ]">
                {{ message }}
            </div>
        </div>

        <!-- Add/Edit Product Modal -->
        <div v-if="isModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">
                        {{ editingProduct ? 'Edit Produk' : 'Tambah Produk Baru' }}
                    </h3>
                </div>

                <form @submit.prevent="saveProduct" class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Material No -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Material No *
                            </label>
                            <input
                                v-model="formData.oldMaterialNo"
                                type="text"
                                required
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Masukkan material number..."
                            />
                        </div>

                        <!-- Material Description -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Material Description *
                            </label>
                            <input
                                v-model="formData.materialDescription"
                                type="text"
                                required
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Masukkan deskripsi material..."
                            />
                        </div>

                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Description
                            </label>
                            <textarea
                                v-model="formData.description"
                                rows="3"
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Masukkan deskripsi tambahan..."
                            ></textarea>
                        </div>

                        <!-- Price IDR -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Price (IDR) *
                            </label>
                            <input
                                v-model.number="formData.price"
                                type="number"
                                min="0"
                                step="1"
                                required
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="0"
                            />
                        </div>

                        <!-- Price USD -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Price (USD)
                            </label>
                            <input
                                v-model.number="formData.priceUSD"
                                type="number"
                                min="0"
                                step="0.01"
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="0.00"
                            />
                        </div>

                        <!-- Exchange Rate -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Exchange Rate (USD to IDR)
                            </label>
                            <input
                                v-model.number="formData.exchangeRate"
                                type="number"
                                min="0"
                                step="1"
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="15000"
                            />
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Status
                            </label>
                            <select
                                v-model="formData.status"
                                class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            >
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>

                        <!-- Slow Moving -->
                        <div class="md:col-span-2">
                            <label class="flex items-center">
                                <input
                                    v-model="formData.slowMoving"
                                    type="checkbox"
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                                <span class="ml-2 text-sm text-gray-700">Tandai sebagai Slow Moving Product</span>
                            </label>
                        </div>
                    </div>

                    <!-- Auto-calculate helper -->
                    <div v-if="formData.priceUSD && formData.exchangeRate" class="p-3 bg-blue-50 rounded-lg">
                        <p class="text-sm text-blue-700">
                            <strong>Auto-calculated IDR Price:</strong>
                            {{ formatCurrency(formData.priceUSD * formData.exchangeRate) }}
                        </p>
                        <button
                            type="button"
                            @click="formData.price = formData.priceUSD * formData.exchangeRate"
                            class="mt-2 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                        >
                            Use This Price
                        </button>
                    </div>

                    <!-- Modal Actions -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                        <button
                            type="button"
                            @click="closeModal"
                            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                        >
                            Batal
                        </button>
                        <button
                            type="submit"
                            :disabled="isSaving"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
                        >
                            <Loader2 v-if="isSaving" class="h-4 w-4 mr-2 animate-spin" />
                            {{ isSaving ? 'Menyimpan...' : (editingProduct ? 'Update' : 'Simpan') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Download,
    Plus,
    Search,
    Edit,
    Trash2,
    Loader2,
    RefreshCw,
    Package
} from 'lucide-vue-next';
import { ref, computed, onMounted, watch } from 'vue';

// Types
interface Product {
    id: string;
    oldMaterialNo: string;
    materialDescription: string;
    description: string;
    price: number;
    priceUSD?: number;
    exchangeRate?: number;
    slowMoving?: boolean;
    status: 'active' | 'inactive';
}

interface ExchangeRate {
    rate: number;
    lastUpdate: string;
}

interface FormData {
    oldMaterialNo: string;
    materialDescription: string;
    description: string;
    price: number;
    priceUSD?: number;
    exchangeRate?: number;
    slowMoving: boolean;
    status: 'active' | 'inactive';
}

// Reactive state
const products = ref<Product[]>([]);
const isLoading = ref(false);
const isSaving = ref(false);
const isUpdatingRate = ref(false);
const isDeleting = ref<string | null>(null);
const message = ref('');

// Modal state
const isModalOpen = ref(false);
const editingProduct = ref<Product | null>(null);

// Form data
const formData = ref<FormData>({
    oldMaterialNo: '',
    materialDescription: '',
    description: '',
    price: 0,
    priceUSD: 0,
    exchangeRate: 15000,
    slowMoving: false,
    status: 'active'
});

// Exchange rate
const exchangeRate = ref<ExchangeRate>({
    rate: 15000,
    lastUpdate: new Date().toLocaleDateString('id-ID')
});

// Filters and search
const searchQuery = ref('');
const filterStatus = ref('');
const filterSlowMoving = ref('');
const selectedProducts = ref<string[]>([]);

// Pagination
const currentPage = ref(1);
const itemsPerPage = ref('10');

// Sample data
const sampleProducts: Product[] = [
    {
        id: '1',
        oldMaterialNo: '226-10.00-20 GT',
        materialDescription: '10.00 - 20 GT MILLER',
        description: 'CK SEBAMBAN',
        price: 3157882,
        priceUSD: 210.52,
        exchangeRate: 15000,
        slowMoving: false,
        status: 'active'
    },
    {
        id: '2',
        oldMaterialNo: '206-10.00R20-HN10',
        materialDescription: '10.00 R 20 TT HN10',
        description: 'Palembang',
        price: 3964627,
        priceUSD: 264.31,
        exchangeRate: 15000,
        slowMoving: false,
        status: 'active'
    },
    {
        id: '3',
        oldMaterialNo: '206-11R22.5-HN08',
        materialDescription: '11 R 22.5 TL HN08',
        description: 'Jakarta',
        price: 2960168,
        priceUSD: 197.34,
        exchangeRate: 15000,
        slowMoving: true,
        status: 'active'
    },
    {
        id: '4',
        oldMaterialNo: '206-11R22.5-HN10',
        materialDescription: '11 R 22.5 TL HN10',
        description: 'Jakarta',
        price: 3543140,
        priceUSD: 236.21,
        exchangeRate: 15000,
        slowMoving: false,
        status: 'active'
    },
    {
        id: '5',
        oldMaterialNo: '200-078266',
        materialDescription: '11 R 22.5 XMZ 2 TL 148/145LVM MI',
        description: 'Pekanbaru',
        price: 5116178,
        priceUSD: 341.08,
        exchangeRate: 15000,
        slowMoving: true,
        status: 'inactive'
    }
];

// Utility functions
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const formatUSD = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
};

const showMessage = (msg: string, duration = 5000) => {
    message.value = msg;
    setTimeout(() => {
        message.value = '';
    }, duration);
};

// Computed properties
const filteredProducts = computed(() => {
    let filtered = products.value;

    // Search filter
    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        filtered = filtered.filter(product =>
            product.oldMaterialNo.toLowerCase().includes(query) ||
            product.materialDescription.toLowerCase().includes(query) ||
            product.description.toLowerCase().includes(query)
        );
    }

    // Status filter
    if (filterStatus.value) {
        filtered = filtered.filter(product => product.status === filterStatus.value);
    }

    // Slow moving filter
    if (filterSlowMoving.value) {
        if (filterSlowMoving.value === 'slow') {
            filtered = filtered.filter(product => product.slowMoving);
        } else if (filterSlowMoving.value === 'normal') {
            filtered = filtered.filter(product => !product.slowMoving);
        }
    }

    return filtered;
});

const totalPages = computed(() => {
    return Math.ceil(filteredProducts.value.length / parseInt(itemsPerPage.value));
});

const paginatedProducts = computed(() => {
    const start = (currentPage.value - 1) * parseInt(itemsPerPage.value);
    const end = start + parseInt(itemsPerPage.value);
    return filteredProducts.value.slice(start, end);
});

// Load products from localStorage
const loadProducts = () => {
    isLoading.value = true;
    try {
        const saved = localStorage.getItem('chitraProducts');
        if (saved) {
            products.value = JSON.parse(saved);
        } else {
            // Load sample data on first visit
            products.value = [...sampleProducts];
            saveProducts();
        }
    } catch (error) {
        console.error('Error loading products:', error);
        products.value = [...sampleProducts];
    } finally {
        isLoading.value = false;
    }
};

// Save products to localStorage
const saveProducts = () => {
    try {
        localStorage.setItem('chitraProducts', JSON.stringify(products.value));
    } catch (error) {
        console.error('Error saving products:', error);
        showMessage('Error: Gagal menyimpan data produk.');
    }
};

// Modal functions
const openAddModal = () => {
    editingProduct.value = null;
    formData.value = {
        oldMaterialNo: '',
        materialDescription: '',
        description: '',
        price: 0,
        priceUSD: 0,
        exchangeRate: exchangeRate.value.rate,
        slowMoving: false,
        status: 'active'
    };
    isModalOpen.value = true;
};

const editProduct = (product: Product) => {
    editingProduct.value = product;
    formData.value = {
        oldMaterialNo: product.oldMaterialNo,
        materialDescription: product.materialDescription,
        description: product.description,
        price: product.price,
        priceUSD: product.priceUSD || 0,
        exchangeRate: product.exchangeRate || exchangeRate.value.rate,
        slowMoving: product.slowMoving || false,
        status: product.status
    };
    isModalOpen.value = true;
};

const closeModal = () => {
    isModalOpen.value = false;
    editingProduct.value = null;
};

// CRUD operations
const saveProduct = async () => {
    isSaving.value = true;

    try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (editingProduct.value) {
            // Update existing product
            const index = products.value.findIndex(p => p.id === editingProduct.value!.id);
            if (index > -1) {
                products.value[index] = {
                    ...editingProduct.value,
                    ...formData.value
                };
                showMessage(`Produk ${formData.value.oldMaterialNo} berhasil diupdate.`);
            }
        } else {
            // Add new product
            const newProduct: Product = {
                id: Date.now().toString(),
                ...formData.value
            };
            products.value.push(newProduct);
            showMessage(`Produk ${formData.value.oldMaterialNo} berhasil ditambahkan.`);
        }

        saveProducts();
        closeModal();

    } catch (error) {
        console.error('Error saving product:', error);
        showMessage('Error: Gagal menyimpan produk.');
    } finally {
        isSaving.value = false;
    }
};

const deleteProduct = async (productId: string) => {
    const product = products.value.find(p => p.id === productId);
    if (!product) return;

    if (!confirm(`Yakin ingin menghapus produk ${product.oldMaterialNo}?`)) {
        return;
    }

    isDeleting.value = productId;

    try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        const index = products.value.findIndex(p => p.id === productId);
        if (index > -1) {
            products.value.splice(index, 1);
            saveProducts();
            showMessage(`Produk ${product.oldMaterialNo} berhasil dihapus.`);

            // Remove from selected if it was selected
            const selectedIndex = selectedProducts.value.indexOf(productId);
            if (selectedIndex > -1) {
                selectedProducts.value.splice(selectedIndex, 1);
            }
        }

    } catch (error) {
        console.error('Error deleting product:', error);
        showMessage('Error: Gagal menghapus produk.');
    } finally {
        isDeleting.value = null;
    }
};

// Selection functions
const toggleProductSelection = (productId: string) => {
    const index = selectedProducts.value.indexOf(productId);
    if (index > -1) {
        selectedProducts.value.splice(index, 1);
    } else {
        selectedProducts.value.push(productId);
    }
};

const toggleSelectAll = () => {
    if (selectedProducts.value.length === paginatedProducts.value.length) {
        selectedProducts.value = [];
    } else {
        selectedProducts.value = paginatedProducts.value.map(p => p.id);
    }
};

// Bulk operations
const bulkUpdateStatus = async (status: 'active' | 'inactive') => {
    if (selectedProducts.value.length === 0) return;

    if (!confirm(`Yakin ingin mengubah status ${selectedProducts.value.length} produk menjadi ${status}?`)) {
        return;
    }

    try {
        selectedProducts.value.forEach(productId => {
            const product = products.value.find(p => p.id === productId);
            if (product) {
                product.status = status;
            }
        });

        saveProducts();
        showMessage(`${selectedProducts.value.length} produk berhasil diubah statusnya menjadi ${status}.`);
        selectedProducts.value = [];

    } catch (error) {
        console.error('Error updating product status:', error);
        showMessage('Error: Gagal mengubah status produk.');
    }
};

const bulkDelete = async () => {
    if (selectedProducts.value.length === 0) return;

    if (!confirm(`Yakin ingin menghapus ${selectedProducts.value.length} produk yang dipilih?`)) {
        return;
    }

    try {
        selectedProducts.value.forEach(productId => {
            const index = products.value.findIndex(p => p.id === productId);
            if (index > -1) {
                products.value.splice(index, 1);
            }
        });

        saveProducts();
        showMessage(`${selectedProducts.value.length} produk berhasil dihapus.`);
        selectedProducts.value = [];

    } catch (error) {
        console.error('Error deleting products:', error);
        showMessage('Error: Gagal menghapus produk.');
    }
};

// Exchange rate functions
const updateExchangeRate = async () => {
    isUpdatingRate.value = true;

    try {
        // Simulate API call to get exchange rate
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Mock exchange rate update (in real app, this would be from API)
        const newRate = Math.floor(Math.random() * 1000) + 15000; // Random rate between 15000-16000

        exchangeRate.value = {
            rate: newRate,
            lastUpdate: new Date().toLocaleDateString('id-ID')
        };

        // Update all products that have USD price
        products.value.forEach(product => {
            if (product.priceUSD) {
                product.exchangeRate = newRate;
                product.price = Math.round(product.priceUSD * newRate);
            }
        });

        saveProducts();
        showMessage(`Exchange rate berhasil diupdate ke ${formatCurrency(newRate)}. Semua harga IDR telah diperbarui.`);

    } catch (error) {
        console.error('Error updating exchange rate:', error);
        showMessage('Error: Gagal mengupdate exchange rate.');
    } finally {
        isUpdatingRate.value = false;
    }
};

// Export function
const exportProducts = () => {
    try {
        const csvContent = [
            // Header
            ['Material No', 'Material Description', 'Description', 'Price IDR', 'Price USD', 'Exchange Rate', 'Status', 'Slow Moving'].join(','),
            // Data
            ...products.value.map(product => [
                product.oldMaterialNo,
                `"${product.materialDescription}"`,
                `"${product.description}"`,
                product.price,
                product.priceUSD || '',
                product.exchangeRate || '',
                product.status,
                product.slowMoving ? 'Yes' : 'No'
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `products_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        showMessage('Data produk berhasil diekspor ke CSV.');

    } catch (error) {
        console.error('Error exporting products:', error);
        showMessage('Error: Gagal mengekspor data produk.');
    }
};

// Watch for pagination reset when filters change
watch([searchQuery, filterStatus, filterSlowMoving], () => {
    currentPage.value = 1;
});

// Initialize data on mount
onMounted(() => {
    loadProducts();
});
</script>
