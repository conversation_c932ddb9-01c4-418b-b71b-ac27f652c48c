import { Product } from '../types';
import { fetchProducts } from './productService';

// Cache for product data
let productCache: Product[] = [];
let lastFetchTime: number = 0;
const CACHE_EXPIRY_MS = 60000; // 1 minute cache expiry

/**
 * Get the latest product data from Product Management
 * Uses a cache to avoid excessive fetching
 */
export const getLatestProducts = async (): Promise<Product[]> => {
  const now = Date.now();
  
  // If cache is empty or expired, fetch fresh data
  if (productCache.length === 0 || now - lastFetchTime > CACHE_EXPIRY_MS) {
    try {
      const products = await fetchProducts();
      productCache = products;
      lastFetchTime = now;
      console.log('Fetched fresh product data:', products.length, 'products');
    } catch (error) {
      console.error('Error fetching products:', error);
      // If fetch fails but we have cached data, use it
      if (productCache.length > 0) {
        console.warn('Using cached product data due to fetch error');
      } else {
        throw error; // Re-throw if we have no cached data
      }
    }
  } else {
    console.log('Using cached product data');
  }
  
  return productCache;
};

/**
 * Get a specific product by material number or description
 * @param materialNo - The material number to search for
 * @param fallbackDescription - Optional description to search for if material number not found
 * @param defaultProduct - Optional default product to return if not found
 */
export const getProductByMaterialNo = async (
  materialNo: string,
  fallbackDescription?: string,
  defaultProduct?: Product
): Promise<Product | null> => {
  try {
    const products = await getLatestProducts();
    
    // Try to find by material number first
    let product = products.find(p => p.oldMaterialNo === materialNo);
    
    // If not found and fallback description provided, try to find by description
    if (!product && fallbackDescription) {
      product = products.find(p => 
        p.materialDescription.includes(fallbackDescription)
      );
    }
    
    if (product) {
      return product;
    } else if (defaultProduct) {
      console.warn(`Product ${materialNo} not found, using default values`);
      return defaultProduct;
    } else {
      console.warn(`Product ${materialNo} not found and no default provided`);
      return null;
    }
  } catch (error) {
    console.error('Error getting product:', error);
    if (defaultProduct) {
      return defaultProduct;
    }
    return null;
  }
};

/**
 * Force refresh the product cache
 */
export const refreshProductCache = async (): Promise<void> => {
  try {
    const products = await fetchProducts();
    productCache = products;
    lastFetchTime = Date.now();
    console.log('Product cache refreshed:', products.length, 'products');
  } catch (error) {
    console.error('Error refreshing product cache:', error);
    throw error;
  }
};
