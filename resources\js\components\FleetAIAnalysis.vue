<template>
  <div class="bg-white rounded-lg shadow-sm p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center space-x-3">
        <div class="p-2 bg-gradient-to-r from-purple-500 to-blue-600 rounded-lg">
          <Sparkles class="h-6 w-6 text-white" />
        </div>
        <div>
          <h3 class="text-xl font-bold text-gray-900">AI Fleet Analysis</h3>
          <p class="text-gray-600">Analisis cerdas dan rekomendasi produk berbasis data armada</p>
        </div>
      </div>
      <div class="flex items-center space-x-3">
        <button
          @click="handleAnalyze"
          :disabled="isLoading || filteredData.length === 0"
          class="flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
        >
          <component :is="isLoading ? Loader2 : Sparkles" :class="['h-4 w-4 mr-2', { 'animate-spin': isLoading }]" />
          {{ isLoading ? 'Menganalisis...' : 'Analisis AI' }}
        </button>
        <button
          v-if="result"
          @click="isExpanded = !isExpanded"
          class="p-2 text-gray-500 hover:text-gray-700 transition-colors"
        >
          <component :is="isExpanded ? ChevronUp : ChevronDown" class="h-5 w-5" />
        </button>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="error" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
      <div class="flex items-center">
        <AlertTriangle class="h-5 w-5 text-red-500 mr-2" />
        <span class="text-red-700">{{ error }}</span>
        <button
          @click="handleAnalyze"
          class="ml-auto px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Coba Lagi
        </button>
      </div>
    </div>

    <!-- Analysis Results -->
    <div v-if="result && isExpanded" class="space-y-6">
      <!-- Analysis Summary -->
      <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200">
        <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
          <BarChart3 class="h-5 w-5 mr-2 text-blue-600" />
          Ringkasan Analisis
        </h4>
        <div class="prose prose-sm max-w-none text-gray-700">
          <p>{{ result.analysisText }}</p>
        </div>
      </div>

      <!-- Matched Products -->
      <div v-if="result.matchedProducts.length > 0" class="space-y-4">
        <h4 class="text-lg font-semibold text-gray-900 flex items-center">
          <Target class="h-5 w-5 mr-2 text-green-600" />
          Produk yang Cocok
        </h4>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div
            v-for="match in result.matchedProducts"
            :key="match.tireSize"
            class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div class="flex items-center justify-between mb-3">
              <h5 class="font-medium text-gray-900">{{ match.tireSize }}</h5>
              <span class="text-sm text-gray-500">{{ match.products.length }} produk</span>
            </div>
            <div class="space-y-2">
              <div
                v-for="product in match.products.slice(0, 3)"
                :key="product.id"
                class="text-sm text-gray-600 bg-gray-50 rounded p-2"
              >
                <div class="font-medium">{{ product.materialDescription }}</div>
                <div class="text-xs text-gray-500">{{ product.oldMaterialNo }}</div>
              </div>
              <div v-if="match.products.length > 3" class="text-xs text-gray-500 text-center py-1">
                +{{ match.products.length - 3 }} produk lainnya
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Promotion Recommendations -->
      <div v-if="result.promotionRecommendations.length > 0" class="space-y-4">
        <h4 class="text-lg font-semibold text-gray-900 flex items-center">
          <MessageSquareQuote class="h-5 w-5 mr-2 text-purple-600" />
          Rekomendasi Promosi
        </h4>
        
        <!-- Promotion Tabs -->
        <div class="border-b border-gray-200">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="(promo, index) in result.promotionRecommendations"
              :key="index"
              @click="activePromoTab = index"
              :class="[
                'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
                activePromoTab === index
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              {{ promo.title }}
            </button>
          </nav>
        </div>

        <!-- Active Promotion Content -->
        <div v-if="result.promotionRecommendations[activePromoTab]" class="space-y-4">
          <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border border-purple-200">
            <div class="space-y-4">
              <div>
                <h5 class="font-semibold text-gray-900 mb-2">Deskripsi Strategi</h5>
                <p class="text-gray-700">{{ result.promotionRecommendations[activePromoTab].description }}</p>
              </div>
              
              <div v-if="result.promotionRecommendations[activePromoTab].targetCustomers" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h6 class="font-medium text-gray-900 mb-2 flex items-center">
                    <Users class="h-4 w-4 mr-1" />
                    Target Customer
                  </h6>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li v-for="customer in result.promotionRecommendations[activePromoTab].targetCustomers" :key="customer" class="flex items-center">
                      <div class="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                      {{ customer }}
                    </li>
                  </ul>
                </div>
                
                <div v-if="result.promotionRecommendations[activePromoTab].targetLocations">
                  <h6 class="font-medium text-gray-900 mb-2 flex items-center">
                    <MapPin class="h-4 w-4 mr-1" />
                    Target Lokasi
                  </h6>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li v-for="location in result.promotionRecommendations[activePromoTab].targetLocations" :key="location" class="flex items-center">
                      <div class="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                      {{ location }}
                    </li>
                  </ul>
                </div>
              </div>

              <!-- ROI and Implementation Steps -->
              <div v-if="result.promotionRecommendations[activePromoTab].estimatedROI || result.promotionRecommendations[activePromoTab].implementationSteps" class="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-purple-200">
                <div v-if="result.promotionRecommendations[activePromoTab].estimatedROI">
                  <h6 class="font-medium text-gray-900 mb-2">Estimasi ROI</h6>
                  <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium inline-block">
                    {{ result.promotionRecommendations[activePromoTab].estimatedROI }}
                  </div>
                </div>
                
                <div v-if="result.promotionRecommendations[activePromoTab].implementationSteps">
                  <h6 class="font-medium text-gray-900 mb-2 flex items-center">
                    <ListChecks class="h-4 w-4 mr-1" />
                    Langkah Implementasi
                  </h6>
                  <ol class="text-sm text-gray-600 space-y-1">
                    <li v-for="(step, stepIndex) in result.promotionRecommendations[activePromoTab].implementationSteps" :key="stepIndex" class="flex items-start">
                      <span class="bg-purple-100 text-purple-800 text-xs rounded-full w-5 h-5 flex items-center justify-center mr-2 mt-0.5 flex-shrink-0">{{ stepIndex + 1 }}</span>
                      {{ step }}
                    </li>
                  </ol>
                </div>
              </div>

              <!-- Marketing Copy -->
              <div v-if="result.promotionRecommendations[activePromoTab].marketingCopy" class="pt-4 border-t border-purple-200">
                <div class="flex items-center justify-between mb-2">
                  <h6 class="font-medium text-gray-900">Contoh Teks Marketing</h6>
                  <button
                    @click="copyToClipboard(result.promotionRecommendations[activePromoTab].marketingCopy, 'marketing-copy')"
                    class="flex items-center text-sm text-purple-600 hover:text-purple-700 transition-colors"
                  >
                    <component :is="copiedText === 'marketing-copy' ? CheckCircle : Copy" class="h-4 w-4 mr-1" />
                    {{ copiedText === 'marketing-copy' ? 'Tersalin!' : 'Salin' }}
                  </button>
                </div>
                <div class="bg-gray-50 rounded-lg p-4 text-sm text-gray-700 italic border-l-4 border-purple-400">
                  "{{ result.promotionRecommendations[activePromoTab].marketingCopy }}"
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Export Options -->
      <div class="flex items-center justify-between pt-6 border-t border-gray-200">
        <div class="text-sm text-gray-500">
          Analisis berdasarkan {{ filteredData.length }} data armada
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="downloadAnalysis('txt')"
            :disabled="exportLoading === 'txt'"
            class="flex items-center px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 transition-colors"
          >
            <component :is="exportLoading === 'txt' ? Loader2 : FileText" :class="['h-4 w-4 mr-2', { 'animate-spin': exportLoading === 'txt' }]" />
            Export TXT
          </button>
          <button
            @click="downloadAnalysis('excel')"
            :disabled="exportLoading === 'excel'"
            class="flex items-center px-3 py-2 text-sm bg-green-100 text-green-700 rounded-lg hover:bg-green-200 disabled:opacity-50 transition-colors"
          >
            <component :is="exportLoading === 'excel' ? Loader2 : FileSpreadsheet" :class="['h-4 w-4 mr-2', { 'animate-spin': exportLoading === 'excel' }]" />
            Export Excel
          </button>
          <button
            @click="shareAnalysis"
            class="flex items-center px-3 py-2 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
          >
            <Share2 class="h-4 w-4 mr-2" />
            Bagikan
          </button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!result && !isLoading && !error" class="text-center py-12">
      <div class="mx-auto w-24 h-24 bg-gradient-to-r from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-4">
        <Sparkles class="h-12 w-12 text-purple-500" />
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">Siap untuk Analisis AI</h3>
      <p class="text-gray-600 mb-4">Klik tombol "Analisis AI" untuk mendapatkan insight dan rekomendasi produk berdasarkan data armada Anda.</p>
      <div class="text-sm text-gray-500">
        {{ filteredData.length }} data armada siap dianalisis
      </div>
    </div>

    <!-- Hidden download links -->
    <a ref="downloadLinkRef" style="display: none;"></a>
    <a ref="excelLinkRef" style="display: none;"></a>
    <a ref="pdfLinkRef" style="display: none;"></a>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  Sparkles, Loader2, AlertTriangle, ChevronDown, ChevronUp,
  Copy, CheckCircle, Target, MapPin, BarChart3,
  ListChecks, MessageSquareQuote, Share2, Users,
  FileSpreadsheet, FileText
} from 'lucide-vue-next'

// Types
interface FleetData {
  id: string
  customer?: string
  model?: string
  location?: string
  tire_size?: string
  tire_quantity?: string | number
  unit_qty?: string | number
  total_tire?: string | number
  status?: string
  [key: string]: any
}

interface Product {
  id: string
  oldMaterialNo: string
  materialDescription: string
  description: string
  price: number
  priceUSD?: number
  exchangeRate?: number
  slowMoving?: boolean
}

interface MatchedProduct {
  tireSize: string
  products: Product[]
}

interface PromotionRecommendation {
  title: string
  description: string
  targetCustomers?: string[]
  targetLocations?: string[]
  estimatedROI?: string
  implementationSteps?: string[]
  marketingCopy?: string
}

interface AIAnalysisResult {
  matchedProducts: MatchedProduct[]
  promotionRecommendations: PromotionRecommendation[]
  analysisText: string
}

// Props
interface Props {
  filteredData: FleetData[]
  tireSizeFilter: string[]
}

const props = defineProps<Props>()

// State
const isLoading = ref(false)
const error = ref<string | null>(null)
const result = ref<AIAnalysisResult | null>(null)
const isExpanded = ref(true)
const activePromoTab = ref(0)
const copiedText = ref<string | null>(null)
const exportLoading = ref<string | null>(null)
const downloadLinkRef = ref<HTMLAnchorElement | null>(null)
const excelLinkRef = ref<HTMLAnchorElement | null>(null)
const pdfLinkRef = ref<HTMLAnchorElement | null>(null)

// OpenRouter API configuration
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions'
const OPENROUTER_API_KEY = '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'
const DEFAULT_MODEL = 'anthropic/claude-3.5-sonnet'

// Methods
const handleAnalyze = async () => {
  try {
    isLoading.value = true
    error.value = null
    
    console.log('Starting AI analysis with', props.filteredData.length, 'fleet records')
    
    // Get products data from API
    const products = await fetchProducts()
    
    // Analyze fleet and products
    const analysisResult = await analyzeFleetAndProducts(props.filteredData, props.tireSizeFilter, products)
    
    result.value = analysisResult
    console.log('AI analysis completed successfully')
    
  } catch (err) {
    console.error('Error during AI analysis:', err)
    error.value = err instanceof Error ? err.message : 'Gagal melakukan analisis AI'
  } finally {
    isLoading.value = false
  }
}

const fetchProducts = async (): Promise<Product[]> => {
  try {
    const response = await fetch('https://chitraparatama.co.id/ICS/product/get_api.php?function=fleetlist')
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    // Transform API data to Product format
    const products: Product[] = data.map((item: any, index: number) => ({
      id: item.id || String(index + 1),
      oldMaterialNo: item.model || item.tire_size || `PROD${index + 1}`,
      materialDescription: `${item.model || 'Product'} ${item.tire_size || ''} - ${item.customer || 'Fleet'}`.trim(),
      description: `Fleet product for ${item.customer || 'customer'} at ${item.location || 'location'}`,
      price: 0, // Price not available in fleet API
      priceUSD: 0,
      exchangeRate: 15000
    }))
    
    return products
  } catch (error) {
    console.error('Error fetching products from API:', error)
    // Return empty array instead of mock data
    return []
  }
}

const analyzeFleetAndProducts = async (
  fleetData: FleetData[],
  tireSizeFilter: string[],
  products: Product[]
): Promise<AIAnalysisResult> => {
  try {
    // Extract unique tire sizes from fleet data
    const tireSizes = tireSizeFilter.length > 0
      ? tireSizeFilter
      : [...new Set(fleetData.map(item => item.tire_size).filter(Boolean))]

    // Match products with tire sizes
    const matchedProducts: MatchedProduct[] = []
    for (const tireSize of tireSizes) {
      const matchingProducts = findMatchingProducts(products, tireSize)
      if (matchingProducts.length > 0) {
        matchedProducts.push({
          tireSize,
          products: matchingProducts
        })
      }
    }

    // Get AI recommendations
    const aiRecommendations = await getAIRecommendations(fleetData, matchedProducts, products)

    return {
      matchedProducts,
      promotionRecommendations: aiRecommendations.promotionRecommendations,
      analysisText: aiRecommendations.analysisText
    }
  } catch (error) {
    console.error('Error analyzing fleet and products:', error)
    throw error
  }
}

const findMatchingProducts = (products: Product[], tireSize: string): Product[] => {
  if (!tireSize) return []

  const normalizedTireSize = normalizeTireSize(tireSize)
  
  // First try exact matches
  let matches = products.filter(product => {
    const normalizedDescription = normalizeTireSize(product.materialDescription)
    return normalizedDescription.includes(normalizedTireSize)
  })

  // If no exact matches, try fuzzy matching
  if (matches.length === 0) {
    matches = products.filter(product => {
      const description = product.materialDescription.toLowerCase()
      return description.includes(normalizedTireSize.replace('.', '').replace(' ', ''))
    })
  }

  return matches
}

const normalizeTireSize = (tireSize: string): string => {
  return tireSize.toLowerCase().replace(/\s+/g, ' ').trim()
}

const getAIRecommendations = async (
  fleetData: FleetData[],
  matchedProducts: MatchedProduct[],
  allProducts: Product[]
): Promise<{ promotionRecommendations: PromotionRecommendation[], analysisText: string }> => {
  try {
    const fleetSummary = summarizeFleetData(fleetData)
    const productSummary = summarizeMatchedProducts(matchedProducts)

    const prompt = `
Anda adalah asisten AI yang ahli dalam rekomendasi produk ban dan promosi pemasaran untuk armada pertambangan dan alat berat.

RINGKASAN DATA ARMADA:
${JSON.stringify(fleetSummary, null, 2)}

PRODUK BAN YANG COCOK:
${JSON.stringify(productSummary, null, 2)}

Berdasarkan data armada dan produk ban yang cocok, silakan:
1. Analisis komposisi armada dan kebutuhan ban
2. Rekomendasikan produk ban yang sesuai dari daftar produk yang cocok
3. Sarankan 2-3 promosi yang efektif untuk profil armada ini

Respons Anda harus dalam format JSON berikut:
{
  "analysisText": "Analisis singkat 2-3 paragraf tentang data armada dan kebutuhan ban",
  "promotionRecommendations": [
    {
      "title": "Judul promosi yang menarik dan catchy",
      "description": "Deskripsi strategi promosi yang detail dan persuasif",
      "targetCustomers": ["Pelanggan1", "Pelanggan2"],
      "targetLocations": ["Lokasi1", "Lokasi2"],
      "estimatedROI": "Perkiraan ROI dalam persentase",
      "implementationSteps": ["Langkah 1", "Langkah 2", "Langkah 3"],
      "marketingCopy": "Contoh teks marketing yang bisa langsung digunakan untuk promosi ini"
    }
  ]
}

PEDOMAN PENTING:
- Fokus pada ukuran ban dalam data armada dan produk yang cocok
- Pertimbangkan lokasi pelanggan dan komposisi armada saat merekomendasikan promosi
- Sarankan peluang bundling yang sesuai dengan kebutuhan pelanggan
- Rekomendasikan promosi yang menargetkan segmen pelanggan tertentu
- Buat analisis yang ringkas namun mendalam
- Pastikan semua bidang JSON diformat dengan benar
- GUNAKAN BAHASA INDONESIA yang persuasif dan menarik untuk semua teks
`

    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'http://localhost:5173',
        'X-Title': 'Fleet AI Analysis'
      },
      body: JSON.stringify({
        model: DEFAULT_MODEL,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
        max_tokens: 2000
      })
    })

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`)
    }

    const data = await response.json()
    const content = data.choices[0].message.content

    // Parse the JSON response
    let cleanedContent = content
    if (cleanedContent.includes('```')) {
      cleanedContent = cleanedContent.replace(/```json\s*/, '').replace(/```\s*$/, '')
    }

    const jsonStartIndex = cleanedContent.indexOf('{')
    const jsonEndIndex = cleanedContent.lastIndexOf('}')

    if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
      cleanedContent = cleanedContent.substring(jsonStartIndex, jsonEndIndex + 1)
    }

    const parsedResponse = JSON.parse(cleanedContent)
    return parsedResponse

  } catch (error) {
    console.error('Error calling AI API:', error)
    
    // Return error message instead of fallback data
    return {
      analysisText: 'Terjadi kesalahan saat menganalisis data dengan AI. Silakan coba lagi nanti.',
      promotionRecommendations: []
    }
  }
}

const summarizeFleetData = (fleetData: FleetData[]) => {
  const customerDistribution = fleetData.reduce((acc, item) => {
    const customer = item.customer || 'Unknown'
    acc[customer] = (acc[customer] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const tireSizeDistribution = fleetData.reduce((acc, item) => {
    const size = item.tire_size || 'Unknown'
    acc[size] = (acc[size] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const locationDistribution = fleetData.reduce((acc, item) => {
    const location = item.location || 'Unknown'
    acc[location] = (acc[location] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return {
    totalFleet: fleetData.length,
    customerDistribution,
    tireSizeDistribution,
    locationDistribution,
    topCustomers: Object.entries(customerDistribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([customer, count]) => ({ customer, count })),
    topTireSizes: Object.entries(tireSizeDistribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([size, count]) => ({ size, count }))
  }
}

const summarizeMatchedProducts = (matchedProducts: MatchedProduct[]) => {
  return matchedProducts.map(match => ({
    tireSize: match.tireSize,
    productCount: match.products.length,
    products: match.products.slice(0, 3).map(p => ({
      id: p.id,
      description: p.materialDescription,
      price: p.price
    }))
  }))
}

const copyToClipboard = async (text: string, type: string) => {
  try {
    await navigator.clipboard.writeText(text)
    copiedText.value = type
    setTimeout(() => {
      copiedText.value = null
    }, 2000)
  } catch (err) {
    console.error('Failed to copy text:', err)
  }
}

const downloadAnalysis = async (format: string) => {
  if (!result.value) return
  
  try {
    exportLoading.value = format
    
    let content = ''
    let filename = ''
    let mimeType = ''
    
    if (format === 'txt') {
      content = generateTextReport(result.value)
      filename = `fleet_analysis_${new Date().toISOString().split('T')[0]}.txt`
      mimeType = 'text/plain'
    } else if (format === 'excel') {
      content = generateCSVReport(result.value)
      filename = `fleet_analysis_${new Date().toISOString().split('T')[0]}.csv`
      mimeType = 'text/csv'
    }
    
    const blob = new Blob([content], { type: mimeType })
    const url = URL.createObjectURL(blob)
    
    if (downloadLinkRef.value) {
      downloadLinkRef.value.href = url
      downloadLinkRef.value.download = filename
      downloadLinkRef.value.click()
    }
    
    URL.revokeObjectURL(url)
  } catch (err) {
    console.error('Error downloading analysis:', err)
  } finally {
    exportLoading.value = null
  }
}

const generateTextReport = (result: AIAnalysisResult): string => {
  let report = 'LAPORAN ANALISIS AI FLEET\n'
  report += '================================\n\n'
  report += `Tanggal: ${new Date().toLocaleDateString('id-ID')}\n\n`
  
  report += 'RINGKASAN ANALISIS:\n'
  report += result.analysisText + '\n\n'
  
  if (result.matchedProducts.length > 0) {
    report += 'PRODUK YANG COCOK:\n'
    result.matchedProducts.forEach(match => {
      report += `\n${match.tireSize}:\n`
      match.products.forEach(product => {
        report += `  - ${product.materialDescription} (${product.oldMaterialNo})\n`
      })
    })
    report += '\n'
  }
  
  if (result.promotionRecommendations.length > 0) {
    report += 'REKOMENDASI PROMOSI:\n'
    result.promotionRecommendations.forEach((promo, index) => {
      report += `\n${index + 1}. ${promo.title}\n`
      report += `   ${promo.description}\n`
      if (promo.estimatedROI) {
        report += `   ROI: ${promo.estimatedROI}\n`
      }
    })
  }
  
  return report
}

const generateCSVReport = (result: AIAnalysisResult): string => {
  let csv = 'Type,Title,Description,Details\n'
  
  csv += `Analysis,Fleet Analysis Summary,"${result.analysisText.replace(/"/g, '""')}",\n`
  
  result.matchedProducts.forEach(match => {
    match.products.forEach(product => {
      csv += `Product,${match.tireSize},"${product.materialDescription.replace(/"/g, '""')}",${product.oldMaterialNo}\n`
    })
  })
  
  result.promotionRecommendations.forEach(promo => {
    csv += `Promotion,"${promo.title.replace(/"/g, '""')}","${promo.description.replace(/"/g, '""')}",${promo.estimatedROI || ''}\n`
  })
  
  return csv
}

const shareAnalysis = async () => {
  if (!result.value) return
  
  const shareText = `Fleet Analysis Results:\n\n${result.analysisText}\n\nGenerated on ${new Date().toLocaleDateString('id-ID')}`
  
  if (navigator.share) {
    try {
      await navigator.share({
        title: 'Fleet AI Analysis Results',
        text: shareText
      })
    } catch (err) {
      console.log('Error sharing:', err)
    }
  } else {
    // Fallback to clipboard
    await copyToClipboard(shareText, 'share')
  }
}
</script>