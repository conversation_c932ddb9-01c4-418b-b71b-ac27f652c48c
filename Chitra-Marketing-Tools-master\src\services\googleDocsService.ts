// Service untuk mengirim data proposal ke backend (Express)
export async function createProposalViaBackend(title: string, content: string) {
  const res = await fetch('http://localhost:3001/api/create-proposal', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ title, content }),
  });
  if (!res.ok) throw new Error('Gagal membuat proposal');
  const data = await res.json();
  return data.url;
} 