/**
 * Types for the Knowledge Base feature
 */

/**
 * Knowledge Base entry categories
 */
export enum KnowledgeCategory {
  PRODUCT_KNOWLEDGE = 'product_knowledge',
  NEGOTIATION_TACTICS = 'negotiation_tactics',
  CUSTOMER_INSIGHTS = 'customer_insights',
  PRICING_STRATEGY = 'pricing_strategy',
  OBJECTION_HANDLING = 'objection_handling',
  COMPETITOR_INFO = 'competitor_info',
  PSYCHOLOGICAL_PRINCIPLES = 'psychological_principles',
  COMPANY_GENERAL_DATA = 'company_general_data' // Data Perusahaan Umum PT Chitra Paratama
}

/**
 * Knowledge Base entry
 */
export interface KnowledgeEntry {
  id: string;
  title: string;
  content: string;
  category: KnowledgeCategory;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  isActive: boolean;
}

/**
 * Knowledge Base entry creation request
 */
export interface KnowledgeEntryCreateRequest {
  title: string;
  content: string;
  category: KnowledgeCategory;
  tags: string[];
  createdBy?: string;
}

/**
 * Knowledge Base entry update request
 */
export interface KnowledgeEntryUpdateRequest {
  id: string;
  title?: string;
  content?: string;
  category?: KnowledgeCategory;
  tags?: string[];
  isActive?: boolean;
}

/**
 * Knowledge Base search request
 */
export interface KnowledgeSearchRequest {
  query?: string;
  category?: KnowledgeCategory;
  tags?: string[];
  isActive?: boolean;
}

/**
 * Knowledge Base search response
 */
export interface KnowledgeSearchResponse {
  entries: KnowledgeEntry[];
  total: number;
}

/**
 * Knowledge Base context for negotiation
 */
export interface NegotiationKnowledgeContext {
  selectedEntries: KnowledgeEntry[];
}
