import React, { useState, useEffect, useCallback } from 'react';
import { Check, DollarSign, Minus, Plus, Search, Squircle, X, RefreshCw } from 'lucide-react';
import { Product, BundleItem } from '../types';
import { getLatestProducts, refreshProductCache } from '../services/productPriceService';

interface EditableBundleItem extends BundleItem {
  editedPrice?: number;
  isEditing?: boolean;
}

interface ProductSelectorProps {
  selectedProducts: BundleItem[];
  onProductsChange: (products: BundleItem[]) => void;
}

export default function ProductSelector({ selectedProducts, onProductsChange }: ProductSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  // useCallback to memoize the function and prevent unnecessary re-renders
  const loadProductsFromDB = useCallback(async () => {
    try {
      setLoading(true);
      // Use our productPriceService to get the latest products with caching
      const data = await getLatestProducts();
      if (data && data.length > 0) {
        setProducts(data);
      } else {
        console.log('No products found in Product Management');
      }
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Refresh product data from Product Management
  const handleRefreshProducts = useCallback(async () => {
    try {
      setLoading(true);
      await refreshProductCache();
      await loadProductsFromDB();
    } catch (error) {
      console.error('Error refreshing products:', error);
    } finally {
      setLoading(false);
    }
  }, [loadProductsFromDB]);

  useEffect(() => {
    loadProductsFromDB();

    const handleProductDataUpdated = () => {
      loadProductsFromDB();
    };

    window.addEventListener('productDataUpdated', handleProductDataUpdated);

    return () => {
      window.removeEventListener('productDataUpdated', handleProductDataUpdated);
    };
  }, [loadProductsFromDB]);

  useEffect(() => {
    try {
      if (!products) return; // Ensure products is not null or undefined

      if (!searchTerm || searchTerm.trim() === '') {
        setFilteredProducts(products);
      } else {
        const lowerSearchTerm = searchTerm.toLowerCase();
        const filtered = products.filter(product => {
          try {
            return (
              ((product.materialDescription || '').toLowerCase()).includes(lowerSearchTerm) ||
              ((product.description || '').toLowerCase()).includes(lowerSearchTerm) ||
              ((product.oldMaterialNo || '').toLowerCase()).includes(lowerSearchTerm)
            );
          } catch (err) {
            console.error('Error filtering product:', product, err);
            return false;
          }
        });
        setFilteredProducts(filtered);
      }
    } catch (err) {
      console.error('Error in filtering products:', err);
      setFilteredProducts(products); // Return all products if there's an error
    }
  }, [searchTerm, products]);

  const handleSelectProduct = (product: Product) => {
    const existingIndex = selectedProducts.findIndex(item => item.product.id === product.id);

    if (existingIndex >= 0) {
      const updatedProducts = [...selectedProducts];
      updatedProducts[existingIndex] = {
        ...updatedProducts[existingIndex],
        quantity: updatedProducts[existingIndex].quantity + 1
      };
      onProductsChange(updatedProducts);
    } else {
      onProductsChange([...selectedProducts, { product, quantity: 1 }]);
    }

    setIsDropdownOpen(false);
    setSearchTerm('');
  };

  const handleQuantityChange = (index: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    const updatedProducts = [...selectedProducts];
    updatedProducts[index] = {
      ...updatedProducts[index],
      quantity: newQuantity
    };

    onProductsChange(updatedProducts);
  };

  const handlePriceEditStart = (index: number) => {
    const updatedProducts = [...selectedProducts] as EditableBundleItem[];
    updatedProducts[index] = {
      ...updatedProducts[index],
      isEditing: true,
      editedPrice: updatedProducts[index].editedPrice || updatedProducts[index].product.price
    };

    onProductsChange(updatedProducts);
  };

  const handlePriceChange = (index: number, newPrice: string) => {
    const price = parseFloat(newPrice);
    if (isNaN(price) || price < 0) return;

    const updatedProducts = [...selectedProducts] as EditableBundleItem[];
    updatedProducts[index] = {
      ...updatedProducts[index],
      editedPrice: price
    };

    onProductsChange(updatedProducts);
  };

  const handlePriceEditSave = (index: number) => {
    const updatedProducts = [...selectedProducts] as EditableBundleItem[];
    updatedProducts[index] = {
      ...updatedProducts[index],
      isEditing: false
    };

    onProductsChange(updatedProducts);
  };

  const handleRemoveProduct = (index: number) => {
    const updatedProducts = selectedProducts.filter((_, i) => i !== index);
    onProductsChange(updatedProducts);
  };

  if (loading) {
    return <div>Loading products...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Select Products</h2>
        <button
          onClick={handleRefreshProducts}
          className="flex items-center text-sm text-green-600 hover:text-green-800 px-2 py-1 border border-green-200 rounded"
          disabled={loading}
          title="Refresh product data from Product Management"
        >
          {loading ? (
            <div className="flex items-center">
              <div className="animate-spin mr-1 h-4 w-4 border-2 border-green-600 border-t-transparent rounded-full"></div>
              <span>Updating...</span>
            </div>
          ) : (
            <>
              <RefreshCw size={16} className="mr-1" />
              <span>Update Prices</span>
            </>
          )}
        </button>
      </div>

      {/* Product Search and Dropdown */}
      <form
        className="relative"
        onSubmit={(e) => {
          e.preventDefault();
          e.stopPropagation();
          return false;
        }}
      >
        <div className="flex items-center border rounded-lg overflow-hidden">
          <div className="px-3 text-gray-400">
            <Search size={18} />
          </div>
          <input
            type="text"
            placeholder="Search products by name, description or code"
            value={searchTerm}
            onChange={e => {
              e.preventDefault();
              e.stopPropagation();
              setSearchTerm(e.target.value);
              setIsDropdownOpen(true);
            }}
            onFocus={(e) => {
              e.preventDefault();
              setIsDropdownOpen(true);
            }}
            onKeyDown={e => {
              if (e.key === 'Enter') {
                e.preventDefault();
                e.stopPropagation();
              }
            }}
            className="flex-1 py-2 px-2 outline-none no-navigation"
          />
          {searchTerm && (
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setSearchTerm('');
              }}
              className="px-3 text-gray-400 hover:text-gray-600"
            >
              <X size={16} />
            </button>
          )}
        </div>

        {isDropdownOpen && filteredProducts.length > 0 && (
          <div className="absolute z-10 mt-1 w-full bg-white border rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {filteredProducts.map(product => (
              <div
                key={product.id}
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleSelectProduct(product);
                }}
              >
                <div className="font-medium">{product.materialDescription || 'No Name'}</div>
                <div className="text-sm text-gray-600">{product.description || 'No Description'}</div>
                <div className="text-sm text-gray-500">
                  Code: {product.oldMaterialNo || 'N/A'} •
                  Price: IDR {(product.price || 0).toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </form>

      {/* Selected Products */}
      <div className="mt-4">
        <h3 className="text-lg font-medium mb-2">Selected Products</h3>

        {selectedProducts.length === 0 ? (
          <div className="text-gray-500 italic">No products selected</div>
        ) : (
          <div className="space-y-3">
            {selectedProducts.map((item, index) => (
              <div key={`${item.product.id}-${index}`} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="font-medium">{item.product.materialDescription}</div>
                  <div className="text-sm text-gray-500 flex items-center">
                    {(item as EditableBundleItem).isEditing ? (
                      <div className="flex items-center">
                        <span className="mr-1">IDR</span>
                        <input
                          type="number"
                          value={(item as EditableBundleItem).editedPrice || item.product.price}
                          onChange={(e) => handlePriceChange(index, e.target.value)}
                          className="w-24 p-1 border rounded-md text-gray-900"
                          autoFocus
                        />
                        <button
                          onClick={() => handlePriceEditSave(index)}
                          className="ml-1 text-green-600 hover:text-green-800"
                        >
                          <Check size={16} />
                        </button>
                      </div>
                    ) : (
                      <>
                        <span>IDR {((item as EditableBundleItem).editedPrice || item.product.price).toLocaleString()}</span>
                        {(item as EditableBundleItem).editedPrice &&
                          (item as EditableBundleItem).editedPrice !== item.product.price && (
                            <span className="ml-1 text-xs text-yellow-600">(Custom)</span>
                          )}
                        <button
                          onClick={() => handlePriceEditStart(index)}
                          className="ml-1 text-gray-400 hover:text-blue-600"
                        >
                          <Squircle size={14} />
                        </button>
                      </>
                    )}
                    <span className="mx-2">•</span>
                    <span>Code: {item.product.oldMaterialNo}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="flex items-center bg-white border rounded-md">
                    <button
                      onClick={() => handleQuantityChange(index, item.quantity - 1)}
                      className="px-2 py-1 text-gray-500 hover:text-gray-700"
                    >
                      <Minus size={16} />
                    </button>
                    <span className="w-10 text-center">{item.quantity}</span>
                    <button
                      onClick={() => handleQuantityChange(index, item.quantity + 1)}
                      className="px-2 py-1 text-gray-500 hover:text-gray-700"
                    >
                      <Plus size={16} />
                    </button>
                  </div>

                  <button
                    onClick={() => handleRemoveProduct(index)}
                    className="p-1 text-gray-400 hover:text-red-500"
                  >
                    <X size={18} />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
