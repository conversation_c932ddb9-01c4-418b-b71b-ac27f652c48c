import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { createHashRouter, RouterProvider, Navigate } from 'react-router-dom'
import App from './App.tsx'
import Layout from './components/Layout.tsx'
import Login from './components/Login.tsx'
import RequireAuth from './components/RequireAuth.tsx'
import { SimpleToastProvider } from './components/SimpleToastProvider'
import DataHubProvider from './components/DataHubProvider.tsx'
import { ThemeProvider } from './components/ThemeProvider'
import HomePage from './pages/HomePage.tsx'
import ErrorBoundary from './components/ErrorBoundary.tsx'
import NotFoundPage from './pages/NotFoundPage.tsx'
import PresentationAnalyzerPage from './pages/PresentationAnalyzerPage.tsx'
import ProposalBuilder from './pages/ProposalBuilder'

// Import components directly
import ProductManagement from './components/ProductManagement.tsx'
import CustomerPage from './pages/CustomerPage.tsx'
import SalesDashboardPage from './pages/SalesDashboardPage.tsx'
import BundlingQtyCalculator from './pages/BundlingQtyCalculator.tsx'
import AnalyticsDashboardPage from './pages/AnalyticsDashboardPage.tsx'
import Ban27BundlingCalculator from './pages/Ban27BundlingCalculator.tsx'
import ZeroMarginBundlingCalculator from './pages/ZeroMarginBundlingCalculator.tsx'
import FleetAnalyzer from './pages/FleetAnalyzer.tsx'
import Revenue2025Page from './pages/Revenue2025Page.tsx'
import PromoSimulationPage from './pages/PromoSimulationPage.tsx'
import NegotiationSimulator from './pages/NegotiationSimulator.tsx'
import NegotiationResults from './pages/NegotiationResults.tsx'
import WhatsAppChatAnalysis from './pages/WhatsAppChatAnalysis.tsx'
import WhatsAppChatResults from './pages/WhatsAppChatResults.tsx'
import WhatsAppWebAnalysis from './pages/WhatsAppWebAnalysis.tsx'
import BundlingProposalPage from './pages/BundlingProposalPage.tsx'
import TemplateManagement from './pages/TemplateManagement.tsx'
import KnowledgeBasePage from './pages/KnowledgeBasePage.tsx'
import SeasonalMarketingCalendarPage from './pages/SeasonalMarketingCalendarPage.tsx'
import MarketingInsightsHub from './pages/MarketingInsightsHub.tsx'
import CoalPriceDataPage from './pages/CoalPriceDataPage.tsx'
import SocialMediaMarketingPage from './pages/SocialMediaMarketingPage.tsx'
import MonthlyContentPlanPage from './pages/MonthlyContentPlanPage.tsx'
import VideoScriptGeneratorPage from './pages/VideoScriptGeneratorPage.tsx'
import InstagramAnalysisPage from './pages/InstagramAnalysisPage.tsx'
import InstagramAnalysisPageSimple from './pages/InstagramAnalysisPageSimple.tsx'
import InstagramAnalysisNew from './pages/InstagramAnalysisNew.tsx'
import TestSimplePage from './pages/TestSimplePage.tsx'
import TestPage from './pages/TestPage.tsx'
import SimpleAnalyticsDashboard from './pages/SimpleAnalyticsDashboard.tsx'
import SalesRevenue2025Simple from './pages/SalesRevenue2025Simple.tsx'
import SalesRevenue2025DataMaster from './pages/SalesRevenue2025DataMaster.tsx'
import SalesRevenue2025New from './pages/SalesRevenue2025New.tsx'
import ProposalAnalyzerPage from './pages/ProposalAnalyzerPage.tsx'
import ImageGeneratorPage from './pages/ImageGeneratorPage.tsx'
import PhotoMakerPage from './pages/PhotoMakerPage.tsx'
import TestRunwarePage from './pages/TestRunwarePage.tsx'
import BasicTestPage from './pages/BasicTestPage'
import SimpleRunwareTestPage from './pages/SimpleRunwareTestPage'
import SimpleImageGeneratorPage from './pages/SimpleImageGeneratorPage'
import CustomerAnalysisPage from './pages/CustomerAnalysisPage'
import ProductAnalysisPage from './pages/ProductAnalysisPage'
import SwotAnalysisPage from './pages/SwotAnalysisPage'
import StyleGuidePage from './pages/StyleGuidePage'
import './index.css'
import './styles/animations.css'

const router = createHashRouter([
  {
    path: '/simple-dashboard',
    element: <SimpleAnalyticsDashboard />
  },
  {
    path: '/test-page',
    element: <TestPage />
  },
  {
    path: '/login',
    element: <Navigate to="/" replace /> // Redirect to main app instead of showing login
    // element: <Login /> // Original login page (commented out)
  },
  {
    path: '/direct-test',
    element: <BasicTestPage />
  },
  {
    path: '/runware-test',
    element: <SimpleRunwareTestPage />
  },
  {
    path: '/',
    element: <RequireAuth><Layout /></RequireAuth>,
    errorElement: <ErrorBoundary><NotFoundPage /></ErrorBoundary>,
    children: [
      {
        index: true,
        element: <Navigate to="/analytics-dashboard" replace />
      },
      {
        path: 'products',
        element: <ProductManagement />
      },
      {
        path: 'customers',
        element: <CustomerPage />
      },
      {
        path: 'sales-dashboard',
        element: <SalesDashboardPage />
      },
      {
        path: 'analytics-dashboard',
        element: <SimpleAnalyticsDashboard />
      },
      {
        path: 'bundling-calculator',
        element: <BundlingQtyCalculator />
      },
      {
        path: 'zero-margin-bundling',
        element: <ZeroMarginBundlingCalculator />
      },
      {
        path: 'ban-27-bundling',
        element: <Ban27BundlingCalculator />
      },
      {
        path: 'fleet-analyzer',
        element: <FleetAnalyzer />
      },
      {
        path: 'revenue-2025',
        element: <Revenue2025Page />
      },
      {
        path: 'sales-revenue-2025-data-master',
        element: <SalesRevenue2025DataMaster />
      },
      {
        path: 'sales-revenue-2025-simple',
        element: <SalesRevenue2025Simple />
      },
      {
        path: 'sales-revenue-2025-new',
        element: <SalesRevenue2025New />
      },
      {
        path: 'promo-simulation',
        element: <PromoSimulationPage />
      },
      {
        path: 'negotiation-simulator',
        element: <NegotiationSimulator />
      },
      {
        path: 'negotiation-results',
        element: <NegotiationResults />
      },
      {
        path: 'marketing-insights',
        element: <MarketingInsightsHub />
      },
      {
        path: 'seasonal-marketing-calendar',
        element: <SeasonalMarketingCalendarPage />
      },
      {
        path: 'bundling-proposal',
        element: <BundlingProposalPage />
      },
      {
        path: 'template-management',
        element: <TemplateManagement />
      },
      {
        path: 'coal-price-data',
        element: <CoalPriceDataPage />
      },
      {
        path: 'knowledge-base',
        element: <KnowledgeBasePage />
      },
      {
        path: 'whatsapp-chat-analysis',
        element: <WhatsAppChatAnalysis />
      },
      {
        path: 'whatsapp-chat-results',
        element: <WhatsAppChatResults />
      },
      {
        path: 'whatsapp-web-analysis',
        element: <WhatsAppWebAnalysis />
      },
      {
        path: 'social-media-marketing',
        element: <SocialMediaMarketingPage />
      },
      {
        path: 'monthly-content-plan',
        element: <MonthlyContentPlanPage />
      },
      {
        path: 'video-script-generator',
        element: <VideoScriptGeneratorPage />
      },
      {
        path: 'instagram-analysis',
        element: <InstagramAnalysisNew />
      },
      {
        path: 'instagram-analysis-full',
        element: <InstagramAnalysisPage />
      },
      {
        path: 'proposal-analyzer',
        element: <ProposalAnalyzerPage />
      },
      {
        path: 'proposal-builder',
        element: <ProposalBuilder />
      },
      {
        path: 'image-generator',
        element: <ImageGeneratorPage />
      },
      {
        path: 'photo-maker',
        element: <PhotoMakerPage />
      },
      {
        path: 'test',
        element: <TestPage />
      },
      {
        path: 'test-runware',
        element: <TestRunwarePage />
      },
      {
        path: 'test-simple',
        element: <TestSimplePage />
      },
      {
        path: 'basic-test',
        element: <BasicTestPage />
      },
      {
        path: 'simple-runware-test',
        element: <SimpleRunwareTestPage />
      },
      {
        path: 'customer-analysis',
        element: <CustomerAnalysisPage />
      },
      {
        path: 'product-analysis',
        element: <ProductAnalysisPage />
      },
      {
        path: 'swot-analysis',
        element: <SwotAnalysisPage />
      },
      {
        path: 'style-guide',
        element: <StyleGuidePage />
      },
      {
        path: 'presentation-analyzer',
        element: <PresentationAnalyzerPage />
      },
      {
        path: '*',
        element: <NotFoundPage />
      }
    ]
  }
])

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <SimpleToastProvider>
      <DataHubProvider>
        <ThemeProvider defaultTheme="light">
          <RouterProvider router={router} />
        </ThemeProvider>
      </DataHubProvider>
    </SimpleToastProvider>
  </StrictMode>,
)
