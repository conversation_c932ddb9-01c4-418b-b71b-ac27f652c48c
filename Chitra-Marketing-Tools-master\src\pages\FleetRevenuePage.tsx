import React, { useState, useEffect } from 'react';
import { DollarSign, Search, ChevronLeft, ChevronRight, RefreshCw, Filter, FileSpreadsheet, Download } from 'lucide-react';
import { FleetRevenueItem, combineFleetRevenueData, FALLBACK_FLEET_REVENUE } from '../services/fleetRevenueService';

export default function FleetRevenuePage() {
  const [fleetRevenue, setFleetRevenue] = useState<FleetRevenueItem[]>([]);
  const [filteredRevenue, setFilteredRevenue] = useState<FleetRevenueItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<string>('customer');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filterCustomer, setFilterCustomer] = useState<string>('all');
  const [filterTireSize, setFilterTireSize] = useState<string>('all');
  const [uniqueCustomers, setUniqueCustomers] = useState<string[]>([]);
  const [uniqueTireSizes, setUniqueTireSizes] = useState<string[]>([]);

  // Load data on component mount
  useEffect(() => {
    loadFleetRevenue();
  }, []);

  // Filter and sort data when dependencies change
  useEffect(() => {
    filterAndSortData();
  }, [fleetRevenue, searchQuery, sortField, sortDirection, filterCustomer, filterTireSize]);

  // Filter and sort the data
  const filterAndSortData = () => {
    let filtered = [...fleetRevenue];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.customer.toLowerCase().includes(query) ||
        item.tireSize.toLowerCase().includes(query)
      );
    }

    // Apply customer filter
    if (filterCustomer !== 'all') {
      filtered = filtered.filter(item => item.customer === filterCustomer);
    }

    // Apply tire size filter
    if (filterTireSize !== 'all') {
      filtered = filtered.filter(item => item.tireSize === filterTireSize);
    }

    // Sort data
    filtered.sort((a, b) => {
      let aValue = a[sortField as keyof FleetRevenueItem] || '';
      let bValue = b[sortField as keyof FleetRevenueItem] || '';

      // Handle numeric sorting for revenue and tire quantities
      if (sortField === 'actualRevenue') {
        aValue = aValue.toString().replace(/,/g, '');
        bValue = bValue.toString().replace(/,/g, '');
        return sortDirection === 'asc'
          ? parseFloat(aValue) - parseFloat(bValue)
          : parseFloat(bValue) - parseFloat(aValue);
      } else if (sortField === 'customerTotalTire' || sortField === 'forecastTire') {
        return sortDirection === 'asc'
          ? parseInt(aValue.toString()) - parseInt(bValue.toString())
          : parseInt(bValue.toString()) - parseInt(aValue.toString());
      }

      // Default string comparison
      return sortDirection === 'asc'
        ? aValue.toString().localeCompare(bValue.toString())
        : bValue.toString().localeCompare(aValue.toString());
    });

    setFilteredRevenue(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Load fleet revenue data
  const loadFleetRevenue = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Loading fleet revenue data...');
      const data = await combineFleetRevenueData();

      if (data.length === 0) {
        console.warn('No fleet revenue data returned, using fallback data');
        setFleetRevenue(FALLBACK_FLEET_REVENUE);
        setError('Could not load data from Google Sheets. Showing sample data instead.');
      } else {
        console.log('Fleet revenue data loaded successfully:', data.length, 'items');
        setFleetRevenue(data);
      }

      // Extract unique customers and tire sizes for filters
      const customers = Array.from(new Set(data.map(item => item.customer))).sort();
      const tireSizes = Array.from(new Set(data.map(item => item.tireSize))).sort();

      setUniqueCustomers(customers);
      setUniqueTireSizes(tireSizes);
    } catch (err) {
      console.error('Failed to load fleet revenue data:', err);
      setError('Failed to load fleet revenue data. Using sample data instead.');
      setFleetRevenue(FALLBACK_FLEET_REVENUE);

      // Extract unique customers and tire sizes from fallback data
      const customers = Array.from(new Set(FALLBACK_FLEET_REVENUE.map(item => item.customer))).sort();
      const tireSizes = Array.from(new Set(FALLBACK_FLEET_REVENUE.map(item => item.tireSize))).sort();

      setUniqueCustomers(customers);
      setUniqueTireSizes(tireSizes);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Export to CSV
  const exportToCSV = () => {
    // Create CSV content
    const headers = ['Customer', 'Tire Size', 'Customer Total Tire', 'Forecast Tire', 'Actual Revenue'];
    const csvRows = [headers.join(',')];

    filteredRevenue.forEach(item => {
      const row = [
        `"${item.customer}"`,
        `"${item.tireSize}"`,
        item.customerTotalTire,
        item.forecastTire,
        `"${item.actualRevenue}"`
      ];
      csvRows.push(row.join(','));
    });

    const csvContent = csvRows.join('\\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'fleet_revenue_data.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Calculate pagination
  const totalPages = Math.ceil(filteredRevenue.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredRevenue.slice(indexOfFirstItem, indexOfLastItem);

  // Pagination controls
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);
  const nextPage = () => setCurrentPage(prev => Math.min(prev + 1, totalPages));
  const prevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1));

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  // Calculate totals for the current filtered data
  const calculateTotals = () => {
    const totalCustomerTire = filteredRevenue.reduce(
      (sum, item) => sum + (parseInt(item.customerTotalTire) || 0),
      0
    );

    const totalForecastTire = filteredRevenue.reduce(
      (sum, item) => sum + (parseInt(item.forecastTire) || 0),
      0
    );

    const totalRevenue = filteredRevenue.reduce(
      (sum, item) => sum + (parseFloat(item.actualRevenue.replace(/,/g, '')) || 0),
      0
    );

    return {
      totalCustomerTire,
      totalForecastTire,
      totalRevenue: totalRevenue.toLocaleString()
    };
  };

  const totals = calculateTotals();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <DollarSign className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Fleet Revenue</h1>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={exportToCSV}
            className="flex items-center px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200"
          >
            <Download size={16} className="mr-2" />
            Export CSV
          </button>

          <button
            onClick={loadFleetRevenue}
            className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh Data
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search customer or tire size..."
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Filter className="h-5 w-5 text-gray-400" />
          <select
            className="border border-gray-300 rounded-md py-2 px-3 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={filterCustomer}
            onChange={(e) => setFilterCustomer(e.target.value)}
          >
            <option value="all">All Customers</option>
            {uniqueCustomers.map((customer) => (
              <option key={customer} value={customer}>
                {customer}
              </option>
            ))}
          </select>
        </div>

        <div className="flex items-center space-x-2">
          <Filter className="h-5 w-5 text-gray-400" />
          <select
            className="border border-gray-300 rounded-md py-2 px-3 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={filterTireSize}
            onChange={(e) => setFilterTireSize(e.target.value)}
          >
            <option value="all">All Tire Sizes</option>
            {uniqueTireSizes.map((size) => (
              <option key={size} value={size}>
                {size}
              </option>
            ))}
          </select>
        </div>

        <div className="flex items-center space-x-2">
          <select
            className="border border-gray-300 rounded-md py-2 px-3 w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={itemsPerPage}
            onChange={(e) => setItemsPerPage(Number(e.target.value))}
          >
            <option value={5}>5 per page</option>
            <option value={10}>10 per page</option>
            <option value={20}>20 per page</option>
            <option value={50}>50 per page</option>
          </select>
        </div>
      </div>

      {/* Fleet Revenue Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-2"></div>
            <p className="text-gray-600">Loading fleet revenue data...</p>
          </div>
        ) : error ? (
          <div className="p-4 bg-yellow-50 border-l-4 border-yellow-400">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-yellow-700">{error}</p>
                <p className="text-sm text-yellow-600 mt-1">
                  Showing sample data for demonstration purposes. There may be an issue with accessing the Google Sheets data.
                </p>
                <p className="text-sm text-yellow-600 mt-1">
                  This could be due to CORS restrictions or the Google Sheets URL format. Check the browser console for more details.
                </p>
                <div className="mt-2 flex space-x-2">
                  <button
                    onClick={loadFleetRevenue}
                    className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200 text-sm font-medium"
                  >
                    Try Again
                  </button>
                  <button
                    onClick={() => {
                      // Force using local data
                      setFleetRevenue(FALLBACK_FLEET_REVENUE);
                      setError("Using local fallback data instead of Google Sheets.");

                      // Extract unique customers and tire sizes from fallback data
                      const customers = Array.from(new Set(FALLBACK_FLEET_REVENUE.map(item => item.customer))).sort();
                      const tireSizes = Array.from(new Set(FALLBACK_FLEET_REVENUE.map(item => item.tireSize))).sort();

                      setUniqueCustomers(customers);
                      setUniqueTireSizes(tireSizes);
                    }}
                    className="px-3 py-1 bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200 text-sm font-medium"
                  >
                    Use Local Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : filteredRevenue.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-gray-600">No fleet revenue data found matching your criteria.</p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('customer')}
                    >
                      Customer {renderSortIndicator('customer')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('tireSize')}
                    >
                      Tire Size {renderSortIndicator('tireSize')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('customerTotalTire')}
                    >
                      Customer Total Tire {renderSortIndicator('customerTotalTire')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('forecastTire')}
                    >
                      Forecast Tire {renderSortIndicator('forecastTire')}
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                      onClick={() => handleSort('actualRevenue')}
                    >
                      Actual Revenue {renderSortIndicator('actualRevenue')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {currentItems.map((item) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {item.customer}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.tireSize}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.customerTotalTire}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.forecastTire}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.actualRevenue}
                      </td>
                    </tr>
                  ))}

                  {/* Totals row */}
                  <tr className="bg-gray-50 font-semibold">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900" colSpan={2}>
                      TOTAL
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {totals.totalCustomerTire}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {totals.totalForecastTire}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {totals.totalRevenue}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  onClick={prevPage}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    currentPage === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Previous
                </button>
                <button
                  onClick={nextPage}
                  disabled={currentPage === totalPages}
                  className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                    currentPage === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                    <span className="font-medium">
                      {Math.min(indexOfLastItem, filteredRevenue.length)}
                    </span>{' '}
                    of <span className="font-medium">{filteredRevenue.length}</span> results
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={prevPage}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === 1
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Previous</span>
                      <ChevronLeft className="h-5 w-5" />
                    </button>

                    {/* Page numbers */}
                    {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                      // Show pages around current page
                      let pageNum;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }

                      return (
                        <button
                          key={pageNum}
                          onClick={() => paginate(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            currentPage === pageNum
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}

                    <button
                      onClick={nextPage}
                      disabled={currentPage === totalPages}
                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === totalPages
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Next</span>
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
