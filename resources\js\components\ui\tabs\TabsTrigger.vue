<script setup lang="ts">
import { cn } from '@/lib/utils';
import { inject, computed } from 'vue';

const props = defineProps({
  value: {
    type: String,
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const tabs = inject('tabs', {
  selectedValue: { value: '' },
  onValueChange: (value: string) => {},
});

const isSelected = computed(() => tabs.selectedValue.value === props.value);

const handleClick = () => {
  if (!props.disabled) {
    tabs.onValueChange(props.value);
  }
};
</script>

<template>
  <button
    :class="
      cn(
        'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
        {
          'bg-background text-foreground shadow-sm': isSelected,
          'hover:bg-muted hover:text-muted-foreground': !isSelected,
        }
      )
    "
    :data-state="isSelected ? 'active' : 'inactive'"
    :disabled="disabled"
    @click="handleClick"
  >
    <slot></slot>
  </button>
</template>