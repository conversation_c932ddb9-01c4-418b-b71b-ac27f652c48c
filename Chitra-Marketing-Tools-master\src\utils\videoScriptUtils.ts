import { VideoType, VideoPurpose, VideoTargetAudience, VideoPlatform } from '../types/videoScript';

/**
 * Get the display name for a video type
 * @param videoType The video type enum value
 * @returns The display name
 */
export const getVideoTypeName = (videoType: VideoType): string => {
  const videoTypeNames: Record<VideoType, string> = {
    [VideoType.MARKETING_PRODUCT]: 'Marketing Produk',
    [VideoType.DOCUMENTATION]: 'Dokumentasi',
    [VideoType.REELS]: 'Reels',
    [VideoType.JOKES]: 'Jokes/Humor',
    [VideoType.REVIEW]: 'Review',
    [VideoType.TESTIMONIAL]: 'Testimonial',
    [VideoType.EDUCATIONAL]: 'Eduka<PERSON>'
  };

  return videoTypeNames[videoType] || 'Unknown';
};

/**
 * Get the display name for a purpose
 * @param purpose The purpose enum value
 * @returns The display name
 */
export const getPurposeName = (purpose: VideoPurpose): string => {
  const purposeNames: Record<VideoPurpose, string> = {
    [VideoPurpose.PROMOTION]: 'Promosi',
    [VideoPurpose.EDUCATION]: '<PERSON>uka<PERSON>',
    [VideoPurpose.VIRAL]: 'Viral',
    [VideoPurpose.DOCUMENTATION]: 'Dokumentasi'
  };

  return purposeNames[purpose] || 'Unknown';
};

/**
 * Get the display name for a target audience
 * @param targetAudience The target audience enum value
 * @returns The display name
 */
export const getTargetAudienceName = (targetAudience: VideoTargetAudience): string => {
  const targetAudienceNames: Record<VideoTargetAudience, string> = {
    [VideoTargetAudience.CUSTOMER]: 'Pelanggan',
    [VideoTargetAudience.INTERNAL]: 'Internal',
    [VideoTargetAudience.GENERAL]: 'Umum'
  };

  return targetAudienceNames[targetAudience] || 'Unknown';
};

/**
 * Get the display name for a platform
 * @param platform The platform enum value
 * @returns The display name
 */
export const getPlatformName = (platform: VideoPlatform): string => {
  const platformNames: Record<VideoPlatform, string> = {
    [VideoPlatform.INSTAGRAM]: 'Instagram',
    [VideoPlatform.TIKTOK]: 'TikTok',
    [VideoPlatform.YOUTUBE]: 'YouTube',
    [VideoPlatform.WHATSAPP]: 'WhatsApp'
  };

  return platformNames[platform] || 'Unknown';
};
