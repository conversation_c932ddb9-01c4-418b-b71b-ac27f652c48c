import React, { useEffect, useState } from 'react';
import { useToast } from './use-toast';

export function Toaster() {
  const { toasts, dismiss } = useToast();
  
  return (
    <div className="fixed top-0 right-0 z-50 p-4 space-y-4 w-full max-w-sm">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={`rounded-lg shadow-lg p-4 flex items-start ${
            toast.variant === 'destructive' ? 'bg-red-100 border border-red-200' : 'bg-white border border-gray-200'
          }`}
          role="alert"
        >
          <div className="flex-1">
            {toast.title && (
              <h3 className={`font-medium ${toast.variant === 'destructive' ? 'text-red-800' : 'text-gray-900'}`}>
                {toast.title}
              </h3>
            )}
            {toast.description && (
              <div className={`mt-1 text-sm ${toast.variant === 'destructive' ? 'text-red-700' : 'text-gray-700'}`}>
                {toast.description}
              </div>
            )}
          </div>
          <button
            onClick={() => dismiss(toast.id)}
            className={`ml-4 inline-flex flex-shrink-0 ${
              toast.variant === 'destructive' ? 'text-red-500 hover:text-red-700' : 'text-gray-400 hover:text-gray-600'
            }`}
          >
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      ))}
    </div>
  );
}
