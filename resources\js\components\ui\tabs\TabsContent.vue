<script setup lang="ts">
import { cn } from '@/lib/utils';
import { inject, computed } from 'vue';

const props = defineProps({
  value: {
    type: String,
    required: true,
  },
});

const tabs = inject('tabs', {
  selectedValue: { value: '' },
  onValueChange: (value: string) => {},
});

const isSelected = computed(() => tabs.selectedValue.value === props.value);
</script>

<template>
  <div
    v-if="isSelected"
    :class="
      cn(
        'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
      )
    "
    :data-state="isSelected ? 'active' : 'inactive'"
  >
    <slot></slot>
  </div>
</template>