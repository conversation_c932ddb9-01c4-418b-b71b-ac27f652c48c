import axios from 'axios'
import type { FleetData } from './fleetDataService'

// Product Interface
export interface Product {
  id: string
  oldMaterialNo: string
  materialDescription: string
  description: string
  price: number
  priceUSD?: number
  exchangeRate?: number
  slowMoving?: boolean
}

// Matched Product Interface
export interface MatchedProduct {
  tireSize: string
  products: Product[]
  matchScore?: number
}

// Promotion Recommendation Interface
export interface PromotionRecommendation {
  title: string
  description: string
  targetCustomers?: string[]
  targetLocations?: string[]
  estimatedROI?: string
  implementationSteps?: string[]
  marketingCopy?: string
  priority?: 'high' | 'medium' | 'low'
}

// Customer Analysis Request
export interface CustomerAnalysisRequest {
  customerName: string
  fleetData: FleetData[]
  products: Product[]
}

// Product Analysis Request
export interface ProductAnalysisRequest {
  tireSize: string
  fleetData: FleetData[]
  products: Product[]
}

// Analysis Result Interface
export interface FleetProductAnalysisResult {
  matchedProducts: MatchedProduct[]
  promotionRecommendations: PromotionRecommendation[]
  analysisText: string
  confidence: number
}

// Fleet Product Analysis Service
export class FleetProductAnalysisService {
  private static instance: FleetProductAnalysisService
  private productsCache: { data: Product[], timestamp: number } | null = null
  private cacheTimeout = 10 * 60 * 1000 // 10 minutes
  
  // OpenRouter API configuration
  private readonly OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions'
  private readonly OPENROUTER_API_KEY = '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'
  private readonly DEFAULT_MODEL = 'anthropic/claude-3.5-sonnet'

  static getInstance(): FleetProductAnalysisService {
    if (!FleetProductAnalysisService.instance) {
      FleetProductAnalysisService.instance = new FleetProductAnalysisService()
    }
    return FleetProductAnalysisService.instance
  }

  async analyzeFleetProducts(
    fleetData: FleetData[],
    tireSizeFilter: string[] = []
  ): Promise<FleetProductAnalysisResult> {
    try {
      console.log('Starting fleet product analysis...')
      
      // Get products data
      const products = await this.fetchProducts()
      
      // Extract tire sizes from fleet data or use filter
      const tireSizes = tireSizeFilter.length > 0
        ? tireSizeFilter
        : this.extractUniqueTireSizes(fleetData)
      
      // Match products with tire sizes
      const matchedProducts = await this.matchProductsWithTireSizes(tireSizes, products)
      
      // Generate AI-powered recommendations
      const aiAnalysis = await this.generateAIRecommendations(fleetData, matchedProducts, products)
      
      return {
        matchedProducts,
        promotionRecommendations: aiAnalysis.promotionRecommendations,
        analysisText: aiAnalysis.analysisText,
        confidence: aiAnalysis.confidence
      }
      
    } catch (error) {
      console.error('Error in fleet product analysis:', error)
      throw error
    }
  }

  async fetchProducts(): Promise<Product[]> {
    try {
      // Check cache first
      if (this.productsCache && Date.now() - this.productsCache.timestamp < this.cacheTimeout) {
        console.log('Returning cached products data')
        return this.productsCache.data
      }

      console.log('Fetching products from API...')
      
      // Fetch from actual API
      const response = await axios.get('https://chitraparatama.co.id/ICS/product/get_api.php', {
        params: {
          function: 'productlist'
        },
        timeout: 15000
      })

      let products: Product[] = []
      
      if (response.data && response.data.success && response.data.data) {
        products = this.normalizeProductsData(response.data.data)
      } else {
        console.warn('API response does not contain expected data structure')
        products = []
      }

      // Update cache
      this.productsCache = {
        data: products,
        timestamp: Date.now()
      }

      console.log(`Successfully loaded ${products.length} products`)
      return products

    } catch (error) {
      console.error('Error fetching products:', error)
      
      // Return cached data if available
      if (this.productsCache) {
        console.log('API failed, returning cached products')
        return this.productsCache.data
      }
      
      // Return empty array if no cache available
      console.log('No cache available, returning empty array')
      return []
    }
  }

  private normalizeProductsData(rawData: any[]): Product[] {
    return rawData.map((item, index) => ({
      id: item.id || item.ID || `product_${index + 1}`,
      oldMaterialNo: item.oldMaterialNo || item.material_no || item.MATERIAL_NO || '',
      materialDescription: item.materialDescription || item.description || item.DESCRIPTION || '',
      description: item.description || item.materialDescription || '',
      price: this.parsePrice(item.price || item.PRICE),
      priceUSD: this.parsePrice(item.priceUSD || item.price_usd),
      exchangeRate: this.parsePrice(item.exchangeRate || item.exchange_rate),
      slowMoving: Boolean(item.slowMoving || item.slow_moving)
    }))
  }

  private parsePrice(value: any): number {
    if (value === null || value === undefined || value === '') return 0
    const num = Number(String(value).replace(/[^0-9.-]/g, ''))
    return isNaN(num) ? 0 : num
  }



  private extractUniqueTireSizes(fleetData: FleetData[]): string[] {
    const tireSizes = new Set<string>()
    
    fleetData.forEach(item => {
      if (item.tire_size && item.tire_size.trim()) {
        tireSizes.add(item.tire_size.trim())
      }
    })
    
    return Array.from(tireSizes)
  }

  private async matchProductsWithTireSizes(
    tireSizes: string[],
    products: Product[]
  ): Promise<MatchedProduct[]> {
    const matchedProducts: MatchedProduct[] = []
    
    for (const tireSize of tireSizes) {
      const matchingProducts = this.findMatchingProducts(products, tireSize)
      
      if (matchingProducts.length > 0) {
        matchedProducts.push({
          tireSize,
          products: matchingProducts,
          matchScore: this.calculateMatchScore(tireSize, matchingProducts)
        })
      }
    }
    
    // Sort by match score (highest first)
    return matchedProducts.sort((a, b) => (b.matchScore || 0) - (a.matchScore || 0))
  }

  private findMatchingProducts(products: Product[], tireSize: string): Product[] {
    if (!tireSize) return []

    const normalizedTireSize = this.normalizeTireSize(tireSize)
    const matches: Product[] = []
    
    // Exact matches first
    products.forEach(product => {
      const normalizedDescription = this.normalizeTireSize(product.materialDescription)
      
      if (normalizedDescription.includes(normalizedTireSize)) {
        matches.push(product)
      }
    })
    
    // If no exact matches, try fuzzy matching
    if (matches.length === 0) {
      const fuzzyPattern = normalizedTireSize.replace(/[.\s]/g, '')
      
      products.forEach(product => {
        const description = product.materialDescription.toLowerCase().replace(/[.\s]/g, '')
        
        if (description.includes(fuzzyPattern) || this.calculateSimilarity(description, fuzzyPattern) > 0.7) {
          matches.push(product)
        }
      })
    }
    
    return matches
  }

  private normalizeTireSize(tireSize: string): string {
    return tireSize.toLowerCase().replace(/\s+/g, ' ').trim()
  }

  private calculateMatchScore(tireSize: string, products: Product[]): number {
    if (products.length === 0) return 0
    
    let totalScore = 0
    const normalizedTireSize = this.normalizeTireSize(tireSize)
    
    products.forEach(product => {
      const normalizedDescription = this.normalizeTireSize(product.materialDescription)
      
      if (normalizedDescription.includes(normalizedTireSize)) {
        totalScore += 1.0 // Exact match
      } else {
        totalScore += this.calculateSimilarity(normalizedDescription, normalizedTireSize)
      }
    })
    
    return totalScore / products.length
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1
    
    if (longer.length === 0) return 1.0
    
    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = []
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  private async generateAIRecommendations(
    fleetData: FleetData[],
    matchedProducts: MatchedProduct[],
    allProducts: Product[]
  ): Promise<{ promotionRecommendations: PromotionRecommendation[], analysisText: string, confidence: number }> {
    try {
      const fleetSummary = this.summarizeFleetData(fleetData)
      const productSummary = this.summarizeMatchedProducts(matchedProducts)

      const prompt = this.buildAnalysisPrompt(fleetSummary, productSummary)
      
      const response = await axios.post(this.OPENROUTER_API_URL, {
        model: this.DEFAULT_MODEL,
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
        max_tokens: 2500
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.OPENROUTER_API_KEY}`,
          'HTTP-Referer': typeof window !== 'undefined' ? window.location.origin : 'http://localhost:5173',
          'X-Title': 'Fleet Product Analysis'
        },
        timeout: 30000
      })

      if (!response.data?.choices?.[0]?.message?.content) {
        throw new Error('Invalid AI response format')
      }

      const content = response.data.choices[0].message.content
      const parsedResponse = this.parseAIResponse(content)
      
      return {
        ...parsedResponse,
        confidence: this.calculateConfidence(fleetData, matchedProducts)
      }

    } catch (error) {
      console.error('Error calling AI API:', error)
      
      // Return error response instead of fallback data
      return {
        analysisText: 'Terjadi kesalahan saat menganalisis data. Silakan coba lagi nanti.',
        promotionRecommendations: [],
        confidence: 0
      }
    }
  }

  private buildAnalysisPrompt(fleetSummary: any, productSummary: any): string {
    return `
Anda adalah asisten AI yang ahli dalam analisis armada pertambangan dan rekomendasi produk ban.

DATA ARMADA:
${JSON.stringify(fleetSummary, null, 2)}

PRODUK BAN YANG COCOK:
${JSON.stringify(productSummary, null, 2)}

Tugas Anda:
1. Analisis komposisi armada dan pola penggunaan ban
2. Identifikasi peluang penjualan dan optimasi inventori
3. Rekomendasikan 2-3 strategi promosi yang efektif
4. Berikan insight tentang segmentasi customer dan lokasi

Format respons JSON:
{
  "analysisText": "Analisis mendalam 2-3 paragraf tentang armada dan peluang bisnis",
  "promotionRecommendations": [
    {
      "title": "Judul promosi yang menarik",
      "description": "Deskripsi strategi promosi yang detail",
      "targetCustomers": ["Customer1", "Customer2"],
      "targetLocations": ["Lokasi1", "Lokasi2"],
      "estimatedROI": "Perkiraan ROI dalam %",
      "implementationSteps": ["Langkah 1", "Langkah 2"],
      "marketingCopy": "Teks marketing yang persuasif",
      "priority": "high/medium/low"
    }
  ]
}

PEDOMAN:
- Fokus pada data aktual dari armada
- Pertimbangkan distribusi geografis dan customer
- Sarankan bundling yang menguntungkan
- Gunakan bahasa Indonesia yang profesional
- Berikan rekomendasi yang actionable
`
  }

  private parseAIResponse(content: string): { promotionRecommendations: PromotionRecommendation[], analysisText: string } {
    try {
      // Clean the response
      let cleanedContent = content.trim()
      
      // Remove markdown code blocks if present
      if (cleanedContent.includes('```')) {
        cleanedContent = cleanedContent.replace(/```json\s*/, '').replace(/```\s*$/, '')
      }
      
      // Find JSON boundaries
      const jsonStart = cleanedContent.indexOf('{')
      const jsonEnd = cleanedContent.lastIndexOf('}')
      
      if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
        cleanedContent = cleanedContent.substring(jsonStart, jsonEnd + 1)
      }
      
      const parsed = JSON.parse(cleanedContent)
      
      return {
        analysisText: parsed.analysisText || 'Analisis tidak tersedia',
        promotionRecommendations: parsed.promotionRecommendations || []
      }
      
    } catch (error) {
      console.error('Error parsing AI response:', error)
      throw new Error('Failed to parse AI response')
    }
  }

  private summarizeFleetData(fleetData: FleetData[]) {
    const customerDistribution = this.groupBy(fleetData, 'customer')
    const locationDistribution = this.groupBy(fleetData, 'location')
    const tireSizeDistribution = this.groupBy(fleetData, 'tire_size')
    const statusDistribution = this.groupBy(fleetData, 'status')
    
    return {
      totalFleet: fleetData.length,
      customerDistribution,
      locationDistribution,
      tireSizeDistribution,
      statusDistribution,
      topCustomers: this.getTopEntries(customerDistribution, 5),
      topLocations: this.getTopEntries(locationDistribution, 5),
      topTireSizes: this.getTopEntries(tireSizeDistribution, 5)
    }
  }

  private summarizeMatchedProducts(matchedProducts: MatchedProduct[]) {
    return matchedProducts.map(match => ({
      tireSize: match.tireSize,
      productCount: match.products.length,
      matchScore: match.matchScore,
      topProducts: match.products.slice(0, 3).map(p => ({
        id: p.id,
        description: p.materialDescription,
        price: p.price,
        materialNo: p.oldMaterialNo
      }))
    }))
  }

  private groupBy(array: any[], key: string): Record<string, number> {
    return array.reduce((acc, item) => {
      const value = item[key] || 'Unknown'
      acc[value] = (acc[value] || 0) + 1
      return acc
    }, {})
  }

  private getTopEntries(distribution: Record<string, number>, limit: number) {
    return Object.entries(distribution)
      .sort(([,a], [,b]) => b - a)
      .slice(0, limit)
      .map(([key, count]) => ({ key, count }))
  }

  private calculateConfidence(fleetData: FleetData[], matchedProducts: MatchedProduct[]): number {
    if (fleetData.length === 0) return 0
    
    const dataQuality = this.assessDataQuality(fleetData)
    const matchQuality = this.assessMatchQuality(matchedProducts)
    
    return Math.round((dataQuality + matchQuality) / 2 * 100) / 100
  }

  private assessDataQuality(fleetData: FleetData[]): number {
    let qualityScore = 0
    const totalFields = 5 // customer, model, location, tire_size, status
    
    fleetData.forEach(item => {
      let filledFields = 0
      if (item.customer) filledFields++
      if (item.model) filledFields++
      if (item.location) filledFields++
      if (item.tire_size) filledFields++
      if (item.status) filledFields++
      
      qualityScore += filledFields / totalFields
    })
    
    return qualityScore / fleetData.length
  }

  private assessMatchQuality(matchedProducts: MatchedProduct[]): number {
    if (matchedProducts.length === 0) return 0
    
    const totalScore = matchedProducts.reduce((sum, match) => sum + (match.matchScore || 0), 0)
    return totalScore / matchedProducts.length
  }



  // Public utility methods
  clearCache(): void {
    this.productsCache = null
  }

  getCacheStatus(): { cached: boolean, age?: number } {
    if (!this.productsCache) {
      return { cached: false }
    }
    
    return {
      cached: true,
      age: Date.now() - this.productsCache.timestamp
    }
  }
}

// Export singleton instance
export const fleetProductAnalysisService = FleetProductAnalysisService.getInstance()

// Export default
export default fleetProductAnalysisService