import React from 'react';
import { Bar, Line, Pie } from 'react-chartjs-2';
import { LucideIcon } from 'lucide-react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

interface ChartCardProps {
  title: string;
  icon?: LucideIcon;
  chartType: 'bar' | 'line' | 'pie';
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string;
    borderWidth?: number;
  }[];
  className?: string;
}

export default function ChartCard({
  title,
  icon: Icon,
  chartType,
  labels,
  datasets,
  className = ""
}: ChartCardProps) {
  // Default colors
  const defaultBackgroundColors = [
    'rgba(54, 162, 235, 0.5)',
    'rgba(255, 99, 132, 0.5)',
    'rgba(255, 206, 86, 0.5)',
    'rgba(75, 192, 192, 0.5)',
    'rgba(153, 102, 255, 0.5)',
    'rgba(255, 159, 64, 0.5)',
  ];

  const defaultBorderColors = [
    'rgba(54, 162, 235, 1)',
    'rgba(255, 99, 132, 1)',
    'rgba(255, 206, 86, 1)',
    'rgba(75, 192, 192, 1)',
    'rgba(153, 102, 255, 1)',
    'rgba(255, 159, 64, 1)',
  ];

  // Apply default colors if not provided
  const processedDatasets = datasets.map((dataset, index) => ({
    ...dataset,
    backgroundColor: dataset.backgroundColor || 
      (chartType === 'pie' 
        ? labels.map((_, i) => defaultBackgroundColors[i % defaultBackgroundColors.length])
        : defaultBackgroundColors[index % defaultBackgroundColors.length]),
    borderColor: dataset.borderColor || 
      (chartType === 'line' 
        ? defaultBorderColors[index % defaultBorderColors.length]
        : undefined),
    borderWidth: dataset.borderWidth || 1
  }));

  // Chart data
  const data = {
    labels,
    datasets: processedDatasets
  };

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: false
      },
    },
    scales: chartType !== 'pie' ? {
      x: {
        title: {
          display: false
        }
      },
      y: {
        title: {
          display: false
        },
        beginAtZero: true
      }
    } : undefined
  };

  return (
    <div className={`bg-white rounded-lg border shadow-sm overflow-hidden ${className}`}>
      <div className="px-4 py-3 border-b flex items-center">
        {Icon && <Icon className="h-5 w-5 text-blue-600 mr-2" />}
        <h3 className="font-medium text-gray-800">{title}</h3>
      </div>
      <div className="p-4">
        <div className="h-64">
          {chartType === 'bar' && <Bar data={data} options={options} />}
          {chartType === 'line' && <Line data={data} options={options} />}
          {chartType === 'pie' && <Pie data={data} options={options} />}
        </div>
      </div>
    </div>
  );
}
