import { createClient } from '@supabase/supabase-js';
import { Product } from '../types';

const supabaseUrl = 'https://jceqixjpaueotgnuqnmd.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpjZXFpeGpwYXVlb3RnbnVxbm1kIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE2NzQyMzgsImV4cCI6MjA1NzI1MDIzOH0.flFyng7CeivLbSt-d1L5nSURgDGPWZFTEdycJ3N2K9A';

export const supabase = createClient(supabaseUrl, supabaseKey);

export async function fetchProducts() {
  const { data, error } = await supabase
    .from('products')
    .select('*');
  
  if (error) {
    console.error('Error fetching products:', error);
    return [];
  }
  
  return data;
}

// Sample data based on the new CSV structure
export const mockProducts: Product[] = [
  {
    id: '1',
    oldMaterialNo: '226-10.00-20 GT',
    materialDescription: '10.00 - 20 GT MILLER',
    description: 'CK SEBAMBAN',
    price: 3157882
  },
  {
    id: '2',
    oldMaterialNo: '206-10.00R20-HN10',
    materialDescription: '10.00 R 20 TT HN10',
    description: 'Palembang',
    price: 3964627
  },
  {
    id: '3',
    oldMaterialNo: '206-11R22.5-HN08',
    materialDescription: '11 R 22.5 TL HN08',
    description: 'Jakarta',
    price: 2960168
  },
  {
    id: '4',
    oldMaterialNo: '206-11R22.5-HN10',
    materialDescription: '11 R 22.5 TL HN10',
    description: 'Jakarta',
    price: 3543140
  },
  {
    id: '5',
    oldMaterialNo: '**********',
    materialDescription: '11 R 22.5 XMZ 2 TL 148/145LVM MI',
    description: 'Pekanbaru',
    price: 5116178
  },
  {
    id: '6',
    oldMaterialNo: '209-1100R20ETFN',
    materialDescription: '11.00 R 20 ETFN',
    description: 'CP TRD BPN',
    price: 4168373
  },
  {
    id: '7',
    oldMaterialNo: '209-1200R20ETFN',
    materialDescription: '12.00 R 20 ETFN',
    description: 'Jakarta',
    price: 4908789
  },
  {
    id: '8',
    oldMaterialNo: '209-1200R24 24PR',
    materialDescription: '12.00 R 24 ETOT 24PR TT',
    description: 'CK BMB',
    price: 6208415
  },
  {
    id: '9',
    oldMaterialNo: '208-3420000886',
    materialDescription: '12.00 R 24 MAXAM MS401**TL',
    description: 'CP TRD BPN',
    price: 4482128
  },
  {
    id: '10',
    oldMaterialNo: '206-12.00R24-HN10',
    materialDescription: '12.00 R 24 TT HN10',
    description: 'Pekanbaru',
    price: 5242124
  }
];

// Load products from localStorage
function loadProducts(): Product[] {
  const storedProducts = localStorage.getItem('bundleBoostProducts');
  if (storedProducts) {
    try {
      return JSON.parse(storedProducts);
    } catch (e) {
      console.error('Error parsing stored products:', e);
    }
  }
  
  // Default to mockProducts if none in storage
  return mockProducts;
}

// Save products to localStorage
export function saveProducts(products: Product[]) {
  try {
    // Save to localStorage
    localStorage.setItem('bundleBoostProducts', JSON.stringify(products));
    console.log('Products saved to localStorage:', products.length);
    
    // Dispatch a custom event to notify components in the same tab
    const updateEvent = new Event('productDataUpdated');
    window.dispatchEvent(updateEvent);
    
    // For cross-tab communication, we can use the storage event
    // This is a hack to trigger storage events in the same tab
    // by modifying a temporary item that signals data changes
    const timestamp = new Date().getTime();
    localStorage.setItem('productUpdate', timestamp.toString());
    localStorage.removeItem('productUpdate');
    
    // Also attempt to save to Supabase in batches (will silently fail if not connected)
    const batchSize = 50; // Process in smaller batches to avoid rate limits
    
    const saveBatch = async (startIndex: number) => {
      const endIndex = Math.min(startIndex + batchSize, products.length);
      const batch = products.slice(startIndex, endIndex);
      
      if (batch.length > 0) {
        try {
          const { error } = await supabase
            .from('products')
            .upsert(batch, { onConflict: 'id' });
          
          if (error) {
            console.warn('Error saving batch to Supabase:', error);
          } else {
            console.log(`Batch ${startIndex} to ${endIndex-1} saved to Supabase`);
          }
          
          // Process next batch if there are more products
          if (endIndex < products.length) {
            setTimeout(() => saveBatch(endIndex), 300); // Add small delay between batches
          }
        } catch (error) {
          console.error('Error in Supabase batch save:', error);
        }
      }
    };
    
    // Start the batch saving process
    if (products.length > 0) {
      saveBatch(0);
    }
    
  } catch (e) {
    console.error('Error saving products:', e);
  }
}
