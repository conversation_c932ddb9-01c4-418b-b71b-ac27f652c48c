<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Upload, Download, Plus, Edit, Trash2, Search, Filter } from 'lucide-vue-next';
import { ref } from 'vue';

const searchQuery = ref('');
const selectedProducts = ref<string[]>([]);
const itemsPerPage = ref('10');

const products = ref([
    {
        id: '1',
        materialNo: 'ZOE-10-00-20 DT',
        description: 'NUDO 20 DT ML FPG',
        priceIDR: '3,957,852',
        usdPrice: '',
        slowMoving: 'Normal',
        status: 'active'
    },
    {
        id: '2',
        materialNo: 'ZOE-10-00620 HN10',
        description: 'NUDO 20 LT 1 HN10',
        priceIDR: '3,064,827',
        usdPrice: '',
        slowMoving: 'Normal',
        status: 'active'
    },
    {
        id: '3',
        materialNo: 'ZOE-TMZ22-HM05',
        description: 'TM 2,2 FL HM05',
        priceIDR: '2,080,508',
        usdPrice: '',
        slowMoving: 'Normal',
        status: 'active'
    },
    {
        id: '4',
        materialNo: 'ZOE-TMZ22-HM10',
        description: 'TM 2,2 FL HM10',
        priceIDR: '3,343,150',
        usdPrice: '',
        slowMoving: 'Normal',
        status: 'active'
    },
    {
        id: '5',
        materialNo: 'ZOE-078206',
        description: 'TM 2,2 XM 2 FL 440 SOLUM MI',
        priceIDR: '5,116,178',
        usdPrice: '',
        slowMoving: 'Normal',
        status: 'active'
    }
]);

const exchangeRate = ref({
    rate: '16,311',
    lastUpdate: 'Terakhir diperbarui: 6/30/2025, 4:20:31 PM'
});

const toggleProductSelection = (productId: string) => {
    const index = selectedProducts.value.indexOf(productId);
    if (index > -1) {
        selectedProducts.value.splice(index, 1);
    } else {
        selectedProducts.value.push(productId);
    }
};

const selectAllProducts = () => {
    if (selectedProducts.value.length === products.value.length) {
        selectedProducts.value = [];
    } else {
        selectedProducts.value = products.value.map(p => p.id);
    }
};

const addProduct = () => {
    // Logic untuk menambah produk
    console.log('Add product');
};

const exportProducts = () => {
    // Logic untuk export produk
    console.log('Export products');
};

const importProducts = () => {
    // Logic untuk import produk
    console.log('Import products');
};

const updateExchangeRate = () => {
    // Logic untuk update exchange rate
    console.log('Update exchange rate');
};
</script>

<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Product Management</h1>
                    <p class="text-muted-foreground">Kelola produk dan harga dengan mudah</p>
                </div>
            </div>

            <!-- Exchange Rate Card -->
            <Card>
                <CardHeader>
                    <div class="flex items-center justify-between">
                        <div>
                            <CardTitle class="text-lg">Global Exchange Rate USD to IDR</CardTitle>
                            <CardDescription>{{ exchangeRate.lastUpdate }}</CardDescription>
                        </div>
                        <Button @click="updateExchangeRate" variant="outline" size="sm">
                            Update Semua Harga IDR
                        </Button>
                    </div>
                </CardHeader>
                <CardContent>
                    <div class="text-2xl font-bold">{{ exchangeRate.rate }}</div>
                </CardContent>
            </Card>

            <!-- Import/Export Section -->
            <Card>
                <CardHeader>
                    <CardTitle>Import Products from Excel/CSV</CardTitle>
                    <CardDescription>Upload file Excel atau CSV dengan product data</CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="flex items-center gap-4">
                        <div class="flex-1">
                            <Label for="file-upload" class="sr-only">Choose File</Label>
                            <div class="flex items-center gap-2">
                                <Input id="file-upload" type="file" accept=".xlsx,.xls,.csv" class="flex-1" />
                                <Button @click="importProducts" variant="outline">
                                    <Upload class="h-4 w-4 mr-2" />
                                    Import
                                </Button>
                            </div>
                        </div>
                        <Button @click="addProduct" variant="default">
                            <Plus class="h-4 w-4 mr-2" />
                            Tambah Produk Baru
                        </Button>
                    </div>
                </CardContent>
            </Card>

            <!-- Product List -->
            <Card>
                <CardHeader>
                    <div class="flex items-center justify-between">
                        <div>
                            <CardTitle>Product List ({{ products.length }})</CardTitle>
                            <CardDescription>Daftar semua produk yang tersedia</CardDescription>
                        </div>
                        <div class="flex items-center gap-2">
                            <Button @click="exportProducts" variant="outline" size="sm">
                                <Download class="h-4 w-4 mr-2" />
                                Export Products
                            </Button>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <!-- Filters and Search -->
                    <div class="flex items-center gap-4 mb-6">
                        <div class="flex-1">
                            <div class="relative">
                                <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input 
                                    v-model="searchQuery" 
                                    placeholder="Cari produk, kode, atau status..." 
                                    class="pl-10"
                                />
                            </div>
                        </div>
                        <Select v-model="itemsPerPage">
                            <SelectTrigger class="w-32">
                                <SelectValue placeholder="Items per page" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="25">25</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    <!-- Product Table -->
                    <div class="border rounded-lg">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead class="w-12">
                                        <Checkbox 
                                            :checked="selectedProducts.length === products.length"
                                            @update:checked="selectAllProducts"
                                        />
                                    </TableHead>
                                    <TableHead>MATERIAL NO</TableHead>
                                    <TableHead>DESCRIPTION</TableHead>
                                    <TableHead>PRICE (IDR)</TableHead>
                                    <TableHead>USD PRICE</TableHead>
                                    <TableHead>SLOW MOVING</TableHead>
                                    <TableHead>ACTIONS</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                <TableRow v-for="product in products" :key="product.id">
                                    <TableCell>
                                        <Checkbox 
                                            :checked="selectedProducts.includes(product.id)"
                                            @update:checked="() => toggleProductSelection(product.id)"
                                        />
                                    </TableCell>
                                    <TableCell class="font-medium">{{ product.materialNo }}</TableCell>
                                    <TableCell>{{ product.description }}</TableCell>
                                    <TableCell>{{ product.priceIDR }}</TableCell>
                                    <TableCell>{{ product.usdPrice || '-' }}</TableCell>
                                    <TableCell>
                                        <Badge variant="secondary">{{ product.slowMoving }}</Badge>
                                    </TableCell>
                                    <TableCell>
                                        <div class="flex items-center gap-2">
                                            <Button variant="ghost" size="sm">
                                                <Edit class="h-4 w-4" />
                                            </Button>
                                            <Button variant="ghost" size="sm">
                                                <Trash2 class="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </TableCell>
                                </TableRow>
                            </TableBody>
                        </Table>
                    </div>

                    <!-- Pagination -->
                    <div class="flex items-center justify-between mt-4">
                        <div class="text-sm text-muted-foreground">
                            Showing {{ products.length }} of {{ products.length }} products
                        </div>
                        <div class="flex items-center gap-2">
                            <Button variant="outline" size="sm" disabled>
                                Previous
                            </Button>
                            <Button variant="outline" size="sm" disabled>
                                Next
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>