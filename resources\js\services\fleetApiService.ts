import axios from 'axios';

// Interface untuk data fleet dari API
export interface FleetApiData {
  id: string;
  vehicle_type: string;
  model: string;
  year: string;
  status: string;
  location: string;
  customer: string;
  utilization?: number;
  fuel_efficiency?: number;
  maintenance_cost?: number;
  tire_condition?: string;
  last_maintenance?: string;
  next_maintenance?: string;
  [key: string]: any;
}

// Interface untuk response API
export interface FleetApiResponse {
  success: boolean;
  data: FleetApiData[];
  message?: string;
  total?: number;
}

class FleetApiService {
  private readonly apiUrl = 'https://chitraparatama.co.id/ICS/product/get_api.php?function=fleetlist';

  /**
   * Fetch fleet data dari API eksternal
   */
  async fetchFleetData(): Promise<FleetApiData[]> {
    try {
      console.log('Fetching fleet data from API...');
      
      const response = await axios.get<FleetApiResponse>(this.apiUrl, {
        timeout: 30000, // 30 detik timeout
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        }
      });

      if (response.data && response.data.success && Array.isArray(response.data.data)) {
        console.log(`Successfully fetched ${response.data.data.length} fleet records`);
        return this.transformApiData(response.data.data);
      } else {
        console.warn('API response format unexpected:', response.data);
        // Jika format tidak sesuai, coba ambil data langsung
        if (Array.isArray(response.data)) {
          return this.transformApiData(response.data);
        }
        throw new Error('Invalid API response format');
      }
    } catch (error) {
      console.error('Error fetching fleet data:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNABORTED') {
          throw new Error('Request timeout - API server tidak merespons');
        } else if (error.response) {
          throw new Error(`API Error: ${error.response.status} - ${error.response.statusText}`);
        } else if (error.request) {
          throw new Error('Network error - Tidak dapat terhubung ke API');
        }
      }
      
      throw new Error('Gagal mengambil data fleet dari API');
    }
  }

  /**
   * Transform data dari API ke format yang digunakan aplikasi
   */
  private transformApiData(apiData: any[]): FleetApiData[] {
    return apiData.map((item, index) => {
      // Mapping field dari API ke format aplikasi
      const transformed: FleetApiData = {
        id: item.id || item.vehicle_id || item.unit_id || `FLEET-${String(index + 1).padStart(3, '0')}`,
        vehicle_type: item.vehicle_type || item.type || item.category || 'Unknown',
        model: item.model || item.vehicle_model || item.unit_model || 'N/A',
        year: item.year || item.manufacture_year || item.model_year || 'N/A',
        status: this.normalizeStatus(item.status || item.vehicle_status || 'Unknown'),
        location: item.location || item.site || item.project_location || 'N/A',
        customer: item.customer || item.client || item.customer_name || 'N/A',
        
        // Data operasional (jika tersedia dari API)
        utilization: this.parseNumber(item.utilization) || this.generateRandomUtilization(),
        fuel_efficiency: this.parseNumber(item.fuel_efficiency) || this.generateRandomFuelEfficiency(item.vehicle_type),
        maintenance_cost: this.parseNumber(item.maintenance_cost) || this.generateRandomMaintenanceCost(),
        tire_condition: this.normalizeTireCondition(item.tire_condition) || this.generateRandomTireCondition(),
        
        // Data maintenance
        last_maintenance: item.last_maintenance || item.last_service_date,
        next_maintenance: item.next_maintenance || item.next_service_date,
        
        // Simpan data asli untuk referensi
        ...item
      };
      
      return transformed;
    });
  }

  /**
   * Normalize status kendaraan
   */
  private normalizeStatus(status: string): string {
    const statusLower = status.toLowerCase();
    
    if (statusLower.includes('active') || statusLower.includes('operational') || statusLower.includes('running')) {
      return 'Active';
    } else if (statusLower.includes('maintenance') || statusLower.includes('service') || statusLower.includes('repair')) {
      return 'Maintenance';
    } else if (statusLower.includes('idle') || statusLower.includes('standby')) {
      return 'Idle';
    } else if (statusLower.includes('breakdown') || statusLower.includes('broken')) {
      return 'Breakdown';
    }
    
    return 'Unknown';
  }

  /**
   * Normalize kondisi ban
   */
  private normalizeTireCondition(condition: string | null): string {
    if (!condition) return this.generateRandomTireCondition();
    
    const conditionLower = condition.toLowerCase();
    
    if (conditionLower.includes('good') || conditionLower.includes('excellent')) {
      return 'Good';
    } else if (conditionLower.includes('fair') || conditionLower.includes('average')) {
      return 'Fair';
    } else if (conditionLower.includes('poor') || conditionLower.includes('bad')) {
      return 'Poor';
    }
    
    return 'Fair';
  }

  /**
   * Parse number dari string
   */
  private parseNumber(value: any): number | null {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const parsed = parseFloat(value.replace(/[^0-9.-]/g, ''));
      return isNaN(parsed) ? null : parsed;
    }
    return null;
  }

  /**
   * Generate random utilization untuk demo
   */
  private generateRandomUtilization(): number {
    return Math.floor(Math.random() * 40) + 60; // 60-100%
  }

  /**
   * Generate random fuel efficiency berdasarkan tipe kendaraan
   */
  private generateRandomFuelEfficiency(vehicleType: string): number {
    const type = vehicleType.toLowerCase();
    
    if (type.includes('truck') || type.includes('dump')) {
      return Math.random() * 5 + 8; // 8-13 L/h
    } else if (type.includes('excavator') || type.includes('loader')) {
      return Math.random() * 8 + 12; // 12-20 L/h
    } else if (type.includes('crane') || type.includes('bulldozer')) {
      return Math.random() * 10 + 15; // 15-25 L/h
    }
    
    return Math.random() * 10 + 10; // 10-20 L/h default
  }

  /**
   * Generate random maintenance cost
   */
  private generateRandomMaintenanceCost(): number {
    return Math.floor(Math.random() * 3000000) + 1500000; // 1.5M - 4.5M IDR
  }

  /**
   * Generate random tire condition
   */
  private generateRandomTireCondition(): string {
    const conditions = ['Good', 'Fair', 'Poor'];
    const weights = [0.5, 0.3, 0.2]; // 50% Good, 30% Fair, 20% Poor
    
    const random = Math.random();
    let cumulative = 0;
    
    for (let i = 0; i < conditions.length; i++) {
      cumulative += weights[i];
      if (random <= cumulative) {
        return conditions[i];
      }
    }
    
    return 'Fair';
  }

  /**
   * Calculate fleet KPIs dari data
   */
  calculateFleetKPIs(fleetData: FleetApiData[]) {
    const totalVehicles = fleetData.length;
    const activeVehicles = fleetData.filter(v => v.status === 'Active').length;
    const averageUtilization = fleetData.reduce((sum, v) => sum + (v.utilization || 0), 0) / totalVehicles;
    const totalMaintenanceCost = fleetData.reduce((sum, v) => sum + (v.maintenance_cost || 0), 0);
    const maintenanceDue = fleetData.filter(v => v.status === 'Maintenance').length;
    
    return {
      totalVehicles,
      activeVehicles,
      utilization: Math.round(averageUtilization * 10) / 10,
      operatingCost: totalMaintenanceCost * 12, // Estimasi tahunan
      maintenanceDue
    };
  }
}

export default new FleetApiService();