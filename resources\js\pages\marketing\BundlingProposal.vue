<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Bundling Proposal</h1>
                    <p class="mt-2 text-gray-600">Create strategic bundling proposals with automated pricing and margin calculations</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="saveProposal"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center"
                    >
                        <Save class="h-4 w-4 mr-2" />
                        Save Proposal
                    </button>
                    <button
                        @click="generatePDF"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Generate PDF
                    </button>
                </div>
            </div>

            <!-- Proposal Configuration -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Proposal Configuration</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Customer Name</label>
                        <input 
                            v-model="proposal.customerName" 
                            type="text" 
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="PT Mining Sejahtera"
                        />
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Bundle Type</label>
                        <select 
                            v-model="proposal.bundleType" 
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="tire-service">Tire + Service Bundle</option>
                            <option value="tire-parts">Tire + Parts Bundle</option>
                            <option value="complete">Complete Solution Bundle</option>
                            <option value="seasonal">Seasonal Bundle</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Target Margin (%)</label>
                        <input 
                            v-model.number="proposal.targetMargin" 
                            type="number" 
                            min="0" 
                            max="100"
                            class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>
                </div>
            </div>

            <!-- Bundle Builder -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Bundle Components</h3>
                    <button
                        @click="addBundleItem"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                    >
                        <Plus class="h-4 w-4 mr-2" />
                        Add Item
                    </button>
                </div>
                
                <div class="p-6">
                    <div class="space-y-4">
                        <div 
                            v-for="(item, index) in proposal.bundleItems" 
                            :key="index"
                            class="p-4 border border-gray-200 rounded-lg"
                        >
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="font-medium text-gray-900">Bundle Item {{ index + 1 }}</h4>
                                <button
                                    @click="removeBundleItem(index)"
                                    class="text-red-600 hover:text-red-800"
                                >
                                    <Trash2 class="h-4 w-4" />
                                </button>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Product</label>
                                    <select 
                                        v-model="item.productId" 
                                        @change="updateItemDetails(index)"
                                        class="w-full p-2 border border-gray-300 rounded text-sm"
                                    >
                                        <option value="">Select product...</option>
                                        <option 
                                            v-for="product in availableProducts" 
                                            :key="product.id" 
                                            :value="product.id"
                                        >
                                            {{ product.name }}
                                        </option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                                    <input 
                                        v-model.number="item.quantity" 
                                        type="number" 
                                        min="1"
                                        @input="calculateItemTotal(index)"
                                        class="w-full p-2 border border-gray-300 rounded text-sm"
                                    />
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Unit Price</label>
                                    <input 
                                        v-model.number="item.unitPrice" 
                                        type="number"
                                        @input="calculateItemTotal(index)"
                                        class="w-full p-2 border border-gray-300 rounded text-sm"
                                    />
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Discount (%)</label>
                                    <input 
                                        v-model.number="item.discount" 
                                        type="number"
                                        min="0"
                                        max="100"
                                        @input="calculateItemTotal(index)"
                                        class="w-full p-2 border border-gray-300 rounded text-sm"
                                    />
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Total</label>
                                    <input 
                                        :value="formatCurrency(item.total || 0)" 
                                        readonly
                                        class="w-full p-2 border border-gray-300 rounded text-sm bg-gray-50"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bundle Summary -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Pricing Summary -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Pricing Summary</h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Subtotal:</span>
                            <span class="font-medium">{{ formatCurrency(bundleSubtotal) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Bundle Discount:</span>
                            <span class="text-red-600">-{{ formatCurrency(bundleDiscount) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">After Discount:</span>
                            <span class="font-medium">{{ formatCurrency(afterDiscount) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Tax (11%):</span>
                            <span class="font-medium">{{ formatCurrency(taxAmount) }}</span>
                        </div>
                        <div class="border-t pt-3">
                            <div class="flex justify-between text-lg font-bold">
                                <span>Total Bundle Price:</span>
                                <span class="text-blue-600">{{ formatCurrency(totalBundlePrice) }}</span>
                            </div>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Estimated Margin:</span>
                            <span :class="estimatedMargin >= proposal.targetMargin ? 'text-green-600' : 'text-red-600'">
                                {{ estimatedMargin.toFixed(1) }}%
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Bundle Benefits -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Bundle Benefits</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <CheckCircle class="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <h4 class="font-medium text-gray-900">Cost Savings</h4>
                                <p class="text-sm text-gray-600">{{ formatCurrency(totalSavings) }} savings vs individual purchase</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <CheckCircle class="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <h4 class="font-medium text-gray-900">Convenience</h4>
                                <p class="text-sm text-gray-600">One-stop solution for all tire and maintenance needs</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <CheckCircle class="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <h4 class="font-medium text-gray-900">Priority Support</h4>
                                <p class="text-sm text-gray-600">Dedicated support and faster service response</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <CheckCircle class="h-5 w-5 text-green-600 mt-0.5 mr-3 flex-shrink-0" />
                            <div>
                                <h4 class="font-medium text-gray-900">Extended Warranty</h4>
                                <p class="text-sm text-gray-600">Additional 6 months warranty on bundled items</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Proposal Templates -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Templates</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div 
                        v-for="template in bundleTemplates" 
                        :key="template.id"
                        @click="applyTemplate(template)"
                        class="p-4 border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-shadow"
                    >
                        <h4 class="font-medium text-gray-900 mb-2">{{ template.name }}</h4>
                        <p class="text-sm text-gray-600 mb-3">{{ template.description }}</p>
                        <div class="flex justify-between items-center">
                            <span class="text-xs text-gray-500">{{ template.items }} items</span>
                            <span class="text-sm font-medium text-blue-600">{{ template.discount }}% discount</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coming Soon Notice -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-center">
                    <Info class="h-6 w-6 text-blue-600 mr-3" />
                    <div>
                        <h3 class="text-lg font-medium text-blue-900">Advanced Features Coming Soon</h3>
                        <p class="text-blue-700 mt-1">
                            Enhanced bundling features currently in development:
                        </p>
                        <ul class="list-disc list-inside text-blue-700 mt-2 space-y-1">
                            <li>AI-powered bundle recommendations based on customer history</li>
                            <li>Dynamic pricing optimization</li>
                            <li>Competitor analysis and pricing comparison</li>
                            <li>Automated proposal generation with custom branding</li>
                            <li>Integration with inventory management</li>
                            <li>Customer-specific pricing tiers</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Save,
    Download,
    Plus,
    Trash2,
    CheckCircle,
    Info
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Types
interface BundleItem {
    productId: string;
    quantity: number;
    unitPrice: number;
    discount: number;
    total?: number;
}

interface Proposal {
    customerName: string;
    bundleType: string;
    targetMargin: number;
    bundleItems: BundleItem[];
}

interface BundleTemplate {
    id: string;
    name: string;
    description: string;
    items: number;
    discount: number;
    template: BundleItem[];
}

// Reactive state
const proposal = ref<Proposal>({
    customerName: '',
    bundleType: 'tire-service',
    targetMargin: 25,
    bundleItems: []
});

const availableProducts = ref([
    { id: '1', name: '27.00 R 49 XD GRIP B E4T TL', price: 212874175 },
    { id: '2', name: '24.00 R 35 XD GRIP B E4T TL', price: 185000000 },
    { id: '3', name: 'Engine Oil SAE 15W-40 (20L)', price: 850000 },
    { id: '4', name: 'Air Filter Heavy Duty', price: 450000 },
    { id: '5', name: 'Oil Filter Premium', price: 320000 },
    { id: '6', name: 'Tire Installation Service', price: 150000 },
    { id: '7', name: 'Wheel Balancing Service', price: 75000 },
    { id: '8', name: 'Tire Rotation Service', price: 100000 }
]);

const bundleTemplates = ref<BundleTemplate[]>([
    {
        id: 'mining-complete',
        name: 'Mining Complete Package',
        description: 'Complete tire solution for mining operations',
        items: 4,
        discount: 15,
        template: [
            { productId: '1', quantity: 4, unitPrice: 212874175, discount: 15 },
            { productId: '3', quantity: 2, unitPrice: 850000, discount: 10 },
            { productId: '6', quantity: 4, unitPrice: 150000, discount: 20 },
            { productId: '7', quantity: 4, unitPrice: 75000, discount: 20 }
        ]
    },
    {
        id: 'maintenance-bundle',
        name: 'Maintenance Bundle',
        description: 'Essential maintenance items package',
        items: 3,
        discount: 12,
        template: [
            { productId: '3', quantity: 5, unitPrice: 850000, discount: 12 },
            { productId: '4', quantity: 2, unitPrice: 450000, discount: 12 },
            { productId: '5', quantity: 2, unitPrice: 320000, discount: 12 }
        ]
    },
    {
        id: 'service-package',
        name: 'Service Package',
        description: 'Complete tire service solution',
        items: 3,
        discount: 18,
        template: [
            { productId: '6', quantity: 4, unitPrice: 150000, discount: 18 },
            { productId: '7', quantity: 4, unitPrice: 75000, discount: 18 },
            { productId: '8', quantity: 4, unitPrice: 100000, discount: 18 }
        ]
    }
]);

// Computed properties
const bundleSubtotal = computed(() => {
    return proposal.value.bundleItems.reduce((sum, item) => {
        const itemTotal = item.quantity * item.unitPrice;
        return sum + itemTotal;
    }, 0);
});

const bundleDiscount = computed(() => {
    return proposal.value.bundleItems.reduce((sum, item) => {
        const itemTotal = item.quantity * item.unitPrice;
        const discountAmount = itemTotal * (item.discount / 100);
        return sum + discountAmount;
    }, 0);
});

const afterDiscount = computed(() => {
    return bundleSubtotal.value - bundleDiscount.value;
});

const taxAmount = computed(() => {
    return afterDiscount.value * 0.11; // 11% tax
});

const totalBundlePrice = computed(() => {
    return afterDiscount.value + taxAmount.value;
});

const totalSavings = computed(() => {
    return bundleDiscount.value;
});

const estimatedMargin = computed(() => {
    if (totalBundlePrice.value === 0) return 0;
    const estimatedCost = totalBundlePrice.value * 0.7; // Assume 70% cost ratio
    return ((totalBundlePrice.value - estimatedCost) / totalBundlePrice.value) * 100;
});

// Utility functions
const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
        style: 'currency',
        currency: 'IDR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

// Bundle management
const addBundleItem = () => {
    proposal.value.bundleItems.push({
        productId: '',
        quantity: 1,
        unitPrice: 0,
        discount: 0,
        total: 0
    });
};

const removeBundleItem = (index: number) => {
    proposal.value.bundleItems.splice(index, 1);
};

const updateItemDetails = (index: number) => {
    const item = proposal.value.bundleItems[index];
    const product = availableProducts.value.find(p => p.id === item.productId);

    if (product) {
        item.unitPrice = product.price;
        calculateItemTotal(index);
    }
};

const calculateItemTotal = (index: number) => {
    const item = proposal.value.bundleItems[index];
    const subtotal = item.quantity * item.unitPrice;
    const discountAmount = subtotal * (item.discount / 100);
    item.total = subtotal - discountAmount;
};

const applyTemplate = (template: BundleTemplate) => {
    proposal.value.bundleItems = template.template.map(item => ({ ...item }));
    proposal.value.bundleItems.forEach((_, index) => {
        updateItemDetails(index);
    });
};

// Actions
const saveProposal = () => {
    try {
        const proposalData = JSON.stringify(proposal.value, null, 2);
        localStorage.setItem('bundling_proposal', proposalData);
        alert('Proposal saved successfully!');
    } catch (error) {
        console.error('Error saving proposal:', error);
        alert('Failed to save proposal.');
    }
};

const generatePDF = () => {
    alert('PDF generation functionality coming soon. Will create professional bundling proposal PDF.');
};

// Initialize
onMounted(() => {
    // Load saved proposal if exists
    try {
        const savedProposal = localStorage.getItem('bundling_proposal');
        if (savedProposal) {
            const parsedProposal = JSON.parse(savedProposal);
            proposal.value = { ...proposal.value, ...parsedProposal };
        }
    } catch (error) {
        console.error('Error loading saved proposal:', error);
    }

    // Add initial bundle item
    if (proposal.value.bundleItems.length === 0) {
        addBundleItem();
    }
});
</script>
