/**
 * Types for the Product Analysis feature
 */

import { Product } from './index';
import { FleetlistItem } from '../services/fleetlistService';
import { SalesRevenueItem } from '../services/salesRevenue2025Service';

/**
 * Integrated product data from all sources
 */
export interface IntegratedProductData {
  productId: string;
  product: Product;
  fleetData: FleetlistItem[]; // Fleet data where this product is used/could be used
  salesData: SalesRevenueItem[]; // Sales history for this product
  potentialCustomers: string[]; // Customers who might need this product based on their fleet
  topCustomersWithSimilarTireSize: {
    customer: string;
    unitQty: number;
    tireSize: string;
  }[]; // Top 10 customers with similar tire size
}

/**
 * Product analysis result from AI
 */
export interface ProductAnalysisResult {
  productId: string;
  productName: string;

  // Raw data
  salesData: SalesRevenueItem[];

  // Sales history analysis
  salesHistory: {
    totalSales: number;
    totalRevenue: number;
    firstSaleDate: string;
    lastSaleDate: string;
    topCustomers: {
      customerName: string;
      quantity: number;
      revenue: number;
    }[];
    salesTrend: 'increasing' | 'decreasing' | 'stable';
  };

  // Market analysis
  marketAnalysis: {
    totalPotentialCustomers: number;
    totalPotentialUnits: number;
    marketSegments: {
      segment: string;
      potentialCustomers: number;
      potentialRevenue: number;
    }[];
    competitivePosition: 'strong' | 'moderate' | 'weak';
  };

  // Inventory recommendations
  inventoryRecommendations: {
    recommendedStockLevel: number;
    restockFrequency: string;
    safetyStockLevel: number;
    seasonalAdjustments: {
      season: string;
      adjustmentFactor: number;
    }[];
  };

  // Sales strategy recommendations
  salesStrategyRecommendations: {
    recommendedApproach: string;
    targetCustomers: string[];
    bundlingOpportunities: {
      complementaryProduct: string;
      reason: string;
      potentialRevenue: number;
    }[];
    pricingStrategy: string;
  };

  // Overall analysis text
  analysisText: string;
}

/**
 * Request for AI analysis
 */
export interface ProductAnalysisRequest {
  productName: string;
  productDetails: Product;
  fleetData: FleetlistItem[];
  salesData: SalesRevenueItem[];
  potentialCustomers: string[];
}

/**
 * Product filter options
 */
export interface ProductFilterOptions {
  searchQuery: string;
  hasSalesHistory: boolean;
  hasFleetData: boolean;
  minSalesAmount?: number;
  maxSalesAmount?: number;
  saleDateFrom?: string;
  saleDateTo?: string;
  productCategory?: string;
  isSlowMoving?: boolean;
}
