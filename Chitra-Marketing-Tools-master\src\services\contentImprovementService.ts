import {
  ContentAnalysisRequest,
  ContentAnalysisResponse,
  ContentImprovementRequest,
  ContentImprovementResponse,
  ContentABTestRequest,
  ContentABTestResponse,
  ContentImprovementHistoryEntry,
  ContentImprovementTarget,
  ContentImprovementTone
} from '../types/contentImprovement';
import { MODELS } from './socialMediaService';

// OpenRouter API key
const OPENROUTER_API_KEY = '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';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Default model for content improvement
const DEFAULT_MODEL = MODELS.DEEPSEEK; // Using GPT-4.1-nano

// Local storage key for content improvement history
const CONTENT_IMPROVEMENT_HISTORY_KEY = 'chitraMarketingTools_contentImprovementHistory';

/**
 * Analyze content
 */
export const analyzeContent = async (request: ContentAnalysisRequest): Promise<ContentAnalysisResponse> => {
  try {
    console.log('Analyzing content with OpenRouter API');

    // Prepare system prompt
    const systemPrompt = `Anda adalah ahli analisis konten media sosial yang berspesialisasi dalam mengoptimalkan konten untuk engagement, readability, dan SEO. Anda akan menganalisis konten dan memberikan saran peningkatan yang spesifik dan dapat ditindaklanjuti.`;

    // Prepare user prompt
    const userPrompt = `Analisis konten ${request.platform} berikut untuk tipe konten ${request.contentType} ${request.target ? `dengan target ${request.target}` : ''}:

"""
${request.content}
"""

Berikan analisis menyeluruh yang mencakup:
1. Readability (keterbacaan)
2. Engagement potential (potensi engagement)
3. SEO dan discoverability
4. Tone dan konsistensi
5. Struktur konten
6. Call to action
7. Saran peningkatan spesifik
8. Versi yang sudah ditingkatkan

Berikan respons dalam format JSON berikut:
{
  "score": 75,
  "readability": {
    "score": 80,
    "level": "medium",
    "suggestions": ["Saran 1", "Saran 2"]
  },
  "engagement": {
    "score": 70,
    "strengths": ["Kekuatan 1", "Kekuatan 2"],
    "weaknesses": ["Kelemahan 1", "Kelemahan 2"]
  },
  "seo": {
    "score": 65,
    "suggestions": ["Saran 1", "Saran 2"]
  },
  "tone": {
    "detected": "professional",
    "consistency": 85,
    "suggestions": ["Saran 1", "Saran 2"]
  },
  "structure": {
    "score": 75,
    "suggestions": ["Saran 1", "Saran 2"]
  },
  "callToAction": {
    "present": true,
    "strength": 60,
    "suggestions": ["Saran 1", "Saran 2"]
  },
  "improvements": [
    {
      "priority": "high",
      "description": "Deskripsi masalah",
      "suggestion": "Saran perbaikan"
    }
  ],
  "improvedVersion": "Versi konten yang sudah ditingkatkan"
}`;

    // Make the API call to OpenRouter
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Content Analysis'
      },
      body: JSON.stringify({
        model: DEFAULT_MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 1500
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('API response:', data);

    // Extract the content from the response
    const content = data.choices[0].message.content;

    try {
      // Try to parse the JSON response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : content;
      const parsedData = JSON.parse(jsonString);

      return parsedData;
    } catch (error) {
      console.error('Error parsing JSON response:', error);
      throw new Error('Invalid response format from API');
    }
  } catch (error) {
    console.error('Error analyzing content:', error);

    // Return fallback analysis
    return {
      score: 65,
      readability: {
        score: 60,
        level: 'medium',
        suggestions: [
          'Gunakan kalimat yang lebih pendek untuk meningkatkan keterbacaan',
          'Tambahkan lebih banyak spasi dan paragraf untuk memudahkan pembacaan'
        ]
      },
      engagement: {
        score: 70,
        strengths: ['Penggunaan emoji yang baik', 'Topik yang relevan dengan audiens'],
        weaknesses: ['Kurang pertanyaan untuk mendorong interaksi', 'Call to action kurang kuat']
      },
      seo: {
        score: 65,
        suggestions: [
          'Tambahkan lebih banyak kata kunci relevan',
          'Gunakan hashtag yang lebih spesifik untuk industri'
        ]
      },
      tone: {
        detected: ContentImprovementTone.PROFESSIONAL,
        consistency: 80,
        suggestions: ['Pertahankan tone profesional di seluruh konten']
      },
      structure: {
        score: 70,
        suggestions: ['Tambahkan pembuka yang lebih menarik', 'Akhiri dengan call to action yang lebih kuat']
      },
      callToAction: {
        present: true,
        strength: 60,
        suggestions: ['Buat call to action lebih spesifik', 'Tambahkan rasa urgensi']
      },
      improvements: [
        {
          priority: 'high',
          description: 'Call to action kurang kuat',
          suggestion: 'Tambahkan call to action yang lebih spesifik dengan rasa urgensi'
        },
        {
          priority: 'medium',
          description: 'Kurang interaktif',
          suggestion: 'Tambahkan pertanyaan untuk mendorong engagement'
        }
      ],
      improvedVersion: request.content + '\n\nBagaimana pendapat Anda tentang produk ini? Hubungi kami sekarang untuk penawaran khusus minggu ini! 🔥 #BanMichelin #ChitraParatama'
    };
  }
};

/**
 * Improve content
 */
export const improveContent = async (request: ContentImprovementRequest): Promise<ContentImprovementResponse> => {
  try {
    console.log('Improving content with OpenRouter API');

    // Prepare system prompt
    const systemPrompt = `Anda adalah ahli optimasi konten media sosial yang berspesialisasi dalam meningkatkan konten untuk ${request.target}. Anda akan meningkatkan konten dengan mempertahankan pesan utama tetapi meningkatkan readability, engagement, dan efektivitas keseluruhan.`;

    // Prepare user prompt
    const userPrompt = `Tingkatkan konten ${request.platform} berikut untuk tipe konten ${request.contentType} dengan target ${request.target} dan tone ${request.tone}:

"""
${request.content}
"""

${request.focusKeywords ? `Kata kunci fokus: ${request.focusKeywords.join(', ')}` : ''}
${request.maxLength ? `Panjang maksimum: ${request.maxLength} karakter` : ''}
${request.includeEmojis ? 'Sertakan emoji yang relevan' : 'Gunakan emoji secara minimal'}
${request.includeHashtags ? 'Sertakan hashtag yang relevan' : 'Jangan sertakan hashtag'}

${request.improvementAreas ? `Area peningkatan yang difokuskan: ${request.improvementAreas.join(', ')}` : 'Tingkatkan semua aspek konten'}

Berikan respons dalam format JSON berikut:
{
  "originalContent": "Konten asli",
  "improvedContent": "Konten yang sudah ditingkatkan",
  "improvements": [
    {
      "type": "readability",
      "description": "Deskripsi peningkatan keterbacaan"
    },
    {
      "type": "engagement",
      "description": "Deskripsi peningkatan engagement"
    }
  ],
  "score": {
    "before": 70,
    "after": 85
  },
  "suggestedHashtags": ["hashtag1", "hashtag2"]
}`;

    // Make the API call to OpenRouter
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Content Improvement'
      },
      body: JSON.stringify({
        model: DEFAULT_MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.5,
        max_tokens: 1500
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('API response:', data);

    // Extract the content from the response
    const content = data.choices[0].message.content;

    try {
      // Try to parse the JSON response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : content;
      const parsedData = JSON.parse(jsonString);

      // Save to history
      saveToHistory({
        id: `ci-${Date.now()}`,
        originalContent: request.content,
        improvedContent: parsedData.improvedContent,
        platform: request.platform,
        contentType: request.contentType,
        target: request.target,
        tone: request.tone,
        score: parsedData.score,
        createdAt: new Date()
      });

      return parsedData;
    } catch (error) {
      console.error('Error parsing JSON response:', error);
      throw new Error('Invalid response format from API');
    }
  } catch (error) {
    console.error('Error improving content:', error);

    // Return fallback improvement
    const fallbackResponse: ContentImprovementResponse = {
      originalContent: request.content,
      improvedContent: request.content + '\n\nBagaimana pendapat Anda tentang produk ini? Hubungi kami sekarang untuk penawaran khusus minggu ini! 🔥 #BanMichelin #ChitraParatama',
      improvements: [
        {
          type: 'engagement',
          description: 'Ditambahkan call to action yang lebih kuat'
        },
        {
          type: 'structure',
          description: 'Ditambahkan penutup yang mendorong interaksi'
        }
      ],
      score: {
        before: 65,
        after: 80
      },
      suggestedHashtags: ['BanMichelin', 'ChitraParatama', 'BanTambang', 'PerformaTangguh']
    };

    // Save to history
    saveToHistory({
      id: `ci-${Date.now()}`,
      originalContent: request.content,
      improvedContent: fallbackResponse.improvedContent,
      platform: request.platform,
      contentType: request.contentType,
      target: request.target,
      tone: request.tone,
      score: fallbackResponse.score,
      createdAt: new Date()
    });

    return fallbackResponse;
  }
};

/**
 * Generate A/B test variations
 */
export const generateABTestVariations = async (request: ContentABTestRequest): Promise<ContentABTestResponse> => {
  try {
    console.log('Generating A/B test variations with OpenRouter API');

    // Prepare system prompt
    const systemPrompt = `Anda adalah ahli optimasi konten media sosial yang berspesialisasi dalam membuat variasi A/B testing. Anda akan membuat beberapa variasi konten yang berbeda untuk diuji, masing-masing dengan fokus yang berbeda tetapi tetap mempertahankan pesan utama.`;

    // Prepare user prompt
    const userPrompt = `Buat ${request.variationCount} variasi konten ${request.platform} berikut untuk tipe konten ${request.contentType} dengan target ${request.target}:

"""
${request.originalContent}
"""

Buat variasi yang berbeda dengan fokus pada area berikut:
${request.focusAreas.map(area => `- ${area}`).join('\n')}

Setiap variasi harus mempertahankan pesan utama tetapi menggunakan pendekatan yang berbeda.

Berikan respons dalam format JSON berikut:
{
  "originalContent": "Konten asli",
  "variations": [
    {
      "id": "var-1",
      "content": "Variasi konten 1",
      "description": "Deskripsi pendekatan yang digunakan",
      "focus": "readability",
      "score": 85
    },
    {
      "id": "var-2",
      "content": "Variasi konten 2",
      "description": "Deskripsi pendekatan yang digunakan",
      "focus": "engagement",
      "score": 80
    }
  ],
  "recommendedVariationId": "var-1"
}`;

    // Make the API call to OpenRouter
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - A/B Test Variations'
      },
      body: JSON.stringify({
        model: DEFAULT_MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 2000
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('API response:', data);

    // Extract the content from the response
    const content = data.choices[0].message.content;

    try {
      // Try to parse the JSON response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : content;
      const parsedData = JSON.parse(jsonString);

      return parsedData;
    } catch (error) {
      console.error('Error parsing JSON response:', error);
      throw new Error('Invalid response format from API');
    }
  } catch (error) {
    console.error('Error generating A/B test variations:', error);

    // Return fallback variations
    return {
      originalContent: request.originalContent,
      variations: [
        {
          id: 'var-1',
          content: request.originalContent + '\n\nBagaimana pendapat Anda tentang produk ini? Hubungi kami sekarang! 🔥',
          description: 'Ditambahkan call to action yang lebih kuat dengan emoji',
          focus: 'engagement',
          score: 85
        },
        {
          id: 'var-2',
          content: 'Perkenalkan Ban Michelin XDR2 - Solusi terbaik untuk kendaraan tambang Anda!\n\n' + request.originalContent,
          description: 'Ditambahkan pembuka yang lebih menarik dan langsung ke inti',
          focus: 'structure',
          score: 80
        },
        {
          id: 'var-3',
          content: request.originalContent.replace(/\./g, '.\n').replace(/!/g, '!\n') + '\n\n#BanMichelin #ChitraParatama #BanTambang',
          description: 'Ditingkatkan keterbacaan dengan memecah paragraf dan menambahkan hashtag',
          focus: 'readability',
          score: 75
        }
      ],
      recommendedVariationId: 'var-1'
    };
  }
};

/**
 * Save content improvement to history
 */
const saveToHistory = (entry: ContentImprovementHistoryEntry): void => {
  const historyJson = localStorage.getItem(CONTENT_IMPROVEMENT_HISTORY_KEY);
  let history: ContentImprovementHistoryEntry[] = [];

  if (historyJson) {
    try {
      history = JSON.parse(historyJson);
    } catch (error) {
      console.error('Error parsing content improvement history:', error);
    }
  }

  history.unshift(entry); // Add to beginning of array

  // Limit history to 50 entries
  if (history.length > 50) {
    history = history.slice(0, 50);
  }

  localStorage.setItem(CONTENT_IMPROVEMENT_HISTORY_KEY, JSON.stringify(history));
};

/**
 * Get content improvement history
 */
export const getContentImprovementHistory = (): ContentImprovementHistoryEntry[] => {
  const historyJson = localStorage.getItem(CONTENT_IMPROVEMENT_HISTORY_KEY);
  if (!historyJson) return [];

  try {
    const history = JSON.parse(historyJson) as ContentImprovementHistoryEntry[];

    // Convert string dates back to Date objects
    return history.map(entry => ({
      ...entry,
      createdAt: new Date(entry.createdAt)
    }));
  } catch (error) {
    console.error('Error parsing content improvement history:', error);
    return [];
  }
};

/**
 * Clear content improvement history
 */
export const clearContentImprovementHistory = (): void => {
  localStorage.removeItem(CONTENT_IMPROVEMENT_HISTORY_KEY);
};
