/**
 * Types for the Proposal Analyzer feature
 */

/**
 * Proposal analysis request
 */
export interface ProposalAnalysisRequest {
  pdfContent: string;  // Base64 encoded PDF content
  pdfText?: string;    // Extracted text from PDF (for display purposes)
  fileName: string;    // Original file name
  fileSize: number;    // File size in bytes
}

/**
 * Proposal analysis response
 */
export interface ProposalAnalysisResponse {
  overallScore: number;           // Score from 1-10
  strengths: string[];            // List of proposal strengths
  weaknesses: string[];           // List of proposal weaknesses
  improvementSuggestions: string[]; // List of improvement suggestions
  negotiationTips: string[];      // Negotiation tips specific to this proposal
  detailedAnalysis: string;       // Detailed analysis text
  keyPoints: {                    // Key points extracted from the proposal
    value: string;                // The value proposition
    pricing: string;              // Pricing strategy
    timeline: string;             // Project timeline
    deliverables: string;         // Deliverables
  };
  competitiveAdvantage: string;   // Analysis of competitive advantage
  persuasionScore: number;        // Score for persuasiveness (1-10)
  clarityScore: number;           // Score for clarity (1-10)
  valuePropositionScore: number;  // Score for value proposition (1-10)
  pricingStrategyScore: number;   // Score for pricing strategy (1-10)
  error?: string;                 // Error message if analysis failed
}

/**
 * Proposal analysis result with metadata
 */
export interface ProposalAnalysisResult extends ProposalAnalysisResponse {
  id: string;                     // Unique ID for the analysis
  fileName: string;               // Original file name
  fileSize: number;               // File size in bytes
  analysisDate: Date;             // Date when the analysis was performed
  model: string;                  // AI model used for analysis
}

/**
 * Proposal analyzer settings
 */
export interface ProposalAnalyzerSettings {
  model: string;                  // AI model to use
  language: 'id' | 'en';          // Analysis language (Indonesian or English)
  focusAreas: string[];           // Areas to focus on in the analysis
  saveHistory: boolean;           // Whether to save analysis history
}

/**
 * Proposal improvement request
 */
export interface ProposalImprovementRequest {
  analysisResult: ProposalAnalysisResult; // The analysis result to base improvements on
  pdfContent: string;                     // Original PDF content (base64)
  pdfText?: string;                       // Extracted text from PDF (for display purposes)
}

/**
 * Proposal improvement section
 */
export interface ProposalImprovementSection {
  title: string;                  // Section title
  originalContent: string;        // Original content
  improvedContent: string;        // Improved content
  explanation: string;            // Explanation of improvements
}

/**
 * Proposal improvement response
 */
export interface ProposalImprovementResponse {
  summary: string;                // Summary of improvements
  sections: ProposalImprovementSection[]; // Improved sections
  overallImprovements: string;    // Overall improvements description
  error?: string;                 // Error message if improvement generation failed
}

/**
 * Proposal improvement result with metadata
 */
export interface ProposalImprovementResult extends ProposalImprovementResponse {
  id: string;                     // Unique ID for the improvement
  analysisId: string;             // ID of the related analysis
  fileName: string;               // Original file name
  improvementDate: Date;          // Date when the improvement was generated
  model: string;                  // AI model used for improvement
}
