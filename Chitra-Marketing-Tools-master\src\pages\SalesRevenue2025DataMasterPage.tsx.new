import React, { useState, useEffect, useRef } from 'react';
import {
  DollarSign, Search, ChevronLeft, ChevronRight, RefreshCw, Filter,
  Download, Upload, Plus, Edit, Trash2, X, Save, Calendar, User, Package, Hash
} from 'lucide-react';
import {
  SalesRevenueItem,
  SalesRevenueSummary,
  loadSalesRevenueData,
  generateSalesRevenueSummary,
  createSalesRevenueItem,
  updateSalesRevenueItem,
  deleteSalesRevenueItem,
  importSalesRevenueData,
  exportSalesRevenueData,
  saveSalesRevenueData
} from '../services/salesRevenue2025Service';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';

// Main Page Component
export default function SalesRevenue2025DataMasterPage() {
  const [salesRevenueData, setSalesRevenueData] = useState<SalesRevenueItem[]>([]);
  const [filteredData, setFilteredData] = useState<SalesRevenueItem[]>([]);
  const [summary, setSummary] = useState<SalesRevenueSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [sortField, setSortField] = useState<string>('customerName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  
  // Filter states
  const [filterCustomer, setFilterCustomer] = useState<string>('all');
  const [filterSalesman, setFilterSalesman] = useState<string>('all');
  const [filterDateFrom, setFilterDateFrom] = useState<string>('');
  const [filterDateTo, setFilterDateTo] = useState<string>('');
  
  // Unique values for filters
  const [uniqueCustomers, setUniqueCustomers] = useState<string[]>([]);
  const [uniqueSalesmen, setUniqueSalesmen] = useState<string[]>([]);
  
  // Form states
  const [showForm, setShowForm] = useState(false);
  const [currentItem, setCurrentItem] = useState<Partial<SalesRevenueItem>>({});
  const [isNewItem, setIsNewItem] = useState(true);
  
  // Import states
  const [importFile, setImportFile] = useState<File | null>(null);
  const [showMappingModal, setShowMappingModal] = useState(false);
  const [availableFields, setAvailableFields] = useState<string[]>([]);
  const [fieldMapping, setFieldMapping] = useState<Record<string, string>>({});
  const [sampleData, setSampleData] = useState<Record<string, any> | null>(null);
  const [importLoading, setImportLoading] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load data on component mount
  useEffect(() => {
    loadSalesRevenueItems();
  }, []);

  // Filter and sort data when dependencies change
  useEffect(() => {
    filterAndSortData();
  }, [salesRevenueData, searchQuery, sortField, sortDirection, filterCustomer, filterSalesman, filterDateFrom, filterDateTo]);

  // Load sales revenue data
  const loadSalesRevenueItems = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Loading sales revenue data...');
      const data = await loadSalesRevenueData();

      if (data.length === 0) {
        console.warn('No sales revenue data returned');
      } else {
        console.log('Sales revenue data loaded successfully:', data.length, 'items');
        setSalesRevenueData(data);
        
        // Generate summary
        const summaryData = generateSalesRevenueSummary(data);
        setSummary(summaryData);

        // Extract unique values for filters
        const customers = Array.from(new Set(data.map(item => item.customerName))).sort();
        const salesmen = Array.from(new Set(data.map(item => item.salesman))).sort();
        
        setUniqueCustomers(customers);
        setUniqueSalesmen(salesmen);
      }
    } catch (err) {
      console.error('Error loading sales revenue data:', err);
      setError('Failed to load sales revenue data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter and sort data
  const filterAndSortData = () => {
    let filtered = [...salesRevenueData];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item => 
        item.customerName.toLowerCase().includes(query) ||
        item.salesman.toLowerCase().includes(query) ||
        item.materialDescription.toLowerCase().includes(query)
      );
    }
    
    // Apply customer filter
    if (filterCustomer !== 'all') {
      filtered = filtered.filter(item => item.customerName === filterCustomer);
    }
    
    // Apply salesman filter
    if (filterSalesman !== 'all') {
      filtered = filtered.filter(item => item.salesman === filterSalesman);
    }
    
    // Apply date range filter
    if (filterDateFrom) {
      filtered = filtered.filter(item => {
        if (!item.billingDate) return false;
        return new Date(item.billingDate) >= new Date(filterDateFrom);
      });
    }
    
    if (filterDateTo) {
      filtered = filtered.filter(item => {
        if (!item.billingDate) return false;
        return new Date(item.billingDate) <= new Date(filterDateTo);
      });
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue) 
          : bValue.localeCompare(aValue);
      } else {
        return sortDirection === 'asc' 
          ? (aValue > bValue ? 1 : -1) 
          : (bValue > aValue ? 1 : -1);
      }
    });
    
    setFilteredData(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Reset filters
  const resetFilters = () => {
    setSearchQuery('');
    setFilterCustomer('all');
    setFilterSalesman('all');
    setFilterDateFrom('');
    setFilterDateTo('');
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  // Format date
  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString;
      return format(date, 'dd MMM yyyy');
    } catch (e) {
      return dateString;
    }
  };

  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  // Calculate pagination
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredData.slice(indexOfFirstItem, indexOfLastItem);

  // Pagination controls
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);
  const nextPage = () => setCurrentPage(prev => Math.min(prev + 1, totalPages));
  const prevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1));

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <DollarSign className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Sales Revenue 2025 Data Master</h1>
        </div>

        <div className="flex space-x-2">
          <button
            onClick={() => {}}
            className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus size={16} className="mr-2" />
            Add New
          </button>
          
          <input
            type="file"
            ref={fileInputRef}
            onChange={() => {}}
            accept=".xlsx,.xls,.csv"
            className="hidden"
          />
          
          <button
            onClick={() => fileInputRef.current?.click()}
            className="flex items-center px-3 py-2 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200"
          >
            <Upload size={16} className="mr-2" />
            Import Data
          </button>
          
          <button
            onClick={() => {}}
            className="flex items-center px-3 py-2 bg-green-100 text-green-700 rounded-md hover:bg-green-200"
          >
            <Download size={16} className="mr-2" />
            Export Excel
          </button>

          <button
            onClick={loadSalesRevenueItems}
            className="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh Data
          </button>
        </div>
      </div>

      {/* Score Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Revenue</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{formatCurrency(summary.totalRevenue)}</h3>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Customers</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{summary.totalCustomers}</h3>
              </div>
              <div className="p-3 bg-indigo-100 rounded-full">
                <User className="h-6 w-6 text-indigo-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Items</p>
                <h3 className="text-2xl font-bold text-gray-900 mt-1">{summary.totalItems.toLocaleString()} unit</h3>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <Package className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
