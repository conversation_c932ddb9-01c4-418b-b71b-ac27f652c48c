import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from './ui/card';
import { Button } from './ui/button';
import { Label } from './ui/label';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from './ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Separator } from './ui/separator';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { Switch } from './ui/switch';
import {
  Sparkles,
  Copy,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  BarChart,
  Loader2,
  Save,
  RefreshCw,
  ThumbsUp,
  MessageSquare,
  Search,
  Lightbulb,
  Zap,
  Calendar
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./ui/dialog";

import {
  ContentAnalysisRequest,
  ContentAnalysisResponse,
  ContentImprovementRequest,
  ContentImprovementResponse,
  ContentABTestRequest,
  ContentABTestResponse,
  ContentImprovementTarget,
  ContentImprovementTone
} from '../types/contentImprovement';
import {
  SocialMediaPlatform,
  ContentType,
  PostStatus
} from '../types/socialMedia';
import {
  analyzeContent,
  improveContent,
  generateABTestVariations
} from '../services/contentImprovementService';
import { createSocialMediaPost } from '../services/socialMediaService';

interface ContentImprovementToolProps {
  initialContent?: string;
}

export default function ContentImprovementTool({ initialContent = '' }: ContentImprovementToolProps) {
  // State for form inputs
  const [content, setContent] = useState(initialContent);
  const [platform, setPlatform] = useState<SocialMediaPlatform>(SocialMediaPlatform.INSTAGRAM);
  const [contentType, setContentType] = useState<ContentType>(ContentType.IMAGE);
  const [target, setTarget] = useState<ContentImprovementTarget>(ContentImprovementTarget.ENGAGEMENT);
  const [tone, setTone] = useState<ContentImprovementTone>(ContentImprovementTone.PROFESSIONAL);
  const [language, setLanguage] = useState<'id' | 'en'>('id');
  const [includeEmojis, setIncludeEmojis] = useState(true);
  const [includeHashtags, setIncludeHashtags] = useState(true);
  const [focusKeywords, setFocusKeywords] = useState('');

  // State for analysis and improvement results
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isImproving, setIsImproving] = useState(false);
  const [isGeneratingVariations, setIsGeneratingVariations] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<ContentAnalysisResponse | null>(null);
  const [improvementResult, setImprovementResult] = useState<ContentImprovementResponse | null>(null);
  const [variationsResult, setVariationsResult] = useState<ContentABTestResponse | null>(null);

  // State for saving content to content list
  const [isSavingToContentList, setIsSavingToContentList] = useState(false);
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [scheduledDate, setScheduledDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [contentTitle, setContentTitle] = useState('');

  // State for notifications
  const [notification, setNotification] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    visible: boolean;
  }>({
    message: '',
    type: 'info',
    visible: false
  });

  // Show notification
  const showNotification = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
    setNotification({
      message,
      type,
      visible: true
    });

    // Hide notification after 3 seconds
    setTimeout(() => {
      setNotification(prev => ({ ...prev, visible: false }));
    }, 3000);
  };

  // Analyze content
  const handleAnalyzeContent = async () => {
    if (!content) {
      showNotification('Masukkan konten untuk dianalisis', 'error');
      return;
    }

    setIsAnalyzing(true);
    try {
      const request: ContentAnalysisRequest = {
        content,
        platform,
        contentType,
        target,
        language
      };

      const result = await analyzeContent(request);
      setAnalysisResult(result);
      showNotification('Analisis konten berhasil', 'success');
    } catch (error) {
      console.error('Error analyzing content:', error);
      showNotification('Gagal menganalisis konten', 'error');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Improve content
  const handleImproveContent = async () => {
    if (!content) {
      showNotification('Masukkan konten untuk ditingkatkan', 'error');
      return;
    }

    setIsImproving(true);
    try {
      const request: ContentImprovementRequest = {
        content,
        platform,
        contentType,
        target,
        tone,
        language,
        includeEmojis,
        includeHashtags,
        focusKeywords: focusKeywords ? focusKeywords.split(',').map(k => k.trim()) : undefined
      };

      const result = await improveContent(request);
      setImprovementResult(result);
      showNotification('Peningkatan konten berhasil', 'success');
    } catch (error) {
      console.error('Error improving content:', error);
      showNotification('Gagal meningkatkan konten', 'error');
    } finally {
      setIsImproving(false);
    }
  };

  // Generate content variations (A/B testing)
  const handleGenerateVariations = async () => {
    if (!content) {
      showNotification('Masukkan konten untuk membuat variasi', 'error');
      return;
    }

    setIsGeneratingVariations(true);
    try {
      const request: ContentABTestRequest = {
        originalContent: content,
        platform,
        contentType,
        target,
        language,
        variationCount: 3,
        focusAreas: ['engagement', 'readability', 'structure']
      };

      const result = await generateABTestVariations(request);
      setVariationsResult(result);
      showNotification('Variasi konten berhasil dibuat', 'success');
    } catch (error) {
      console.error('Error generating content variations:', error);
      showNotification('Gagal membuat variasi konten', 'error');
    } finally {
      setIsGeneratingVariations(false);
    }
  };

  // Copy content to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    showNotification('Berhasil disalin ke clipboard', 'success');
  };

  // Save content to content list
  const handleSaveToContentList = async () => {
    if (!improvementResult) {
      showNotification('Tidak ada konten yang ditingkatkan untuk disimpan', 'error');
      return;
    }

    if (!contentTitle.trim()) {
      showNotification('Judul konten tidak boleh kosong', 'error');
      return;
    }

    setIsSavingToContentList(true);
    try {
      // Extract hashtags from improved content if available
      const hashtags = improvementResult.suggestedHashtags || [];

      // Extract caption from the improved content
      // For Instagram, the first paragraph can be used as caption
      const contentLines = improvementResult.improvedContent.split('\n');
      const caption = contentLines.length > 0 ? contentLines[0] : improvementResult.improvedContent;

      // Create a new social media post
      const newPost = {
        title: contentTitle,
        content: improvementResult.improvedContent,
        caption: caption,
        platform: platform,
        contentType: contentType,
        status: PostStatus.DRAFT,
        scheduledDate: new Date(scheduledDate),
        hashtags: hashtags,
        mediaUrls: [],
        metadata: {
          target: target,
          tone: tone,
          language: language,
          originalContent: improvementResult.originalContent,
          improvements: improvementResult.improvements
        }
      };

      await createSocialMediaPost(newPost);
      showNotification('Konten berhasil disimpan ke daftar konten', 'success');
      setSaveDialogOpen(false);
      setContentTitle('');
    } catch (error) {
      console.error('Error saving content to content list:', error);
      showNotification('Gagal menyimpan konten ke daftar konten', 'error');
    } finally {
      setIsSavingToContentList(false);
    }
  };

  // Get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Get progress color based on score
  const getProgressColor = (score: number) => {
    if (score >= 80) return 'bg-green-600';
    if (score >= 60) return 'bg-yellow-600';
    return 'bg-red-600';
  };

  // Update content when initialContent changes
  useEffect(() => {
    if (initialContent) {
      setContent(initialContent);
    }
  }, [initialContent]);

  return (
    <div className="container mx-auto p-4">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Sparkles className="h-5 w-5 mr-2 text-blue-600" />
            AI-Powered Content Improvement
          </CardTitle>
          <CardDescription>
            Analisis dan tingkatkan konten media sosial Anda dengan bantuan AI
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="content" className="mb-2 block">Konten</Label>
              <Textarea
                id="content"
                placeholder="Masukkan konten yang ingin dianalisis atau ditingkatkan..."
                className="min-h-[150px]"
                value={content}
                onChange={(e) => setContent(e.target.value)}
              />
            </div>
            <div>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="platform" className="mb-2 block">Platform</Label>
                  <Select value={platform} onValueChange={(value) => setPlatform(value as SocialMediaPlatform)}>
                    <SelectItem value={SocialMediaPlatform.INSTAGRAM}>Instagram</SelectItem>
                    <SelectItem value={SocialMediaPlatform.FACEBOOK}>Facebook</SelectItem>
                    <SelectItem value={SocialMediaPlatform.LINKEDIN}>LinkedIn</SelectItem>
                    <SelectItem value={SocialMediaPlatform.TIKTOK}>TikTok</SelectItem>
                    <SelectItem value={SocialMediaPlatform.TWITTER}>Twitter</SelectItem>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="contentType" className="mb-2 block">Tipe Konten</Label>
                  <Select value={contentType} onValueChange={(value) => setContentType(value as ContentType)}>
                    <SelectItem value={ContentType.IMAGE}>Image Post</SelectItem>
                    <SelectItem value={ContentType.VIDEO}>Video Post</SelectItem>
                    <SelectItem value={ContentType.CAROUSEL}>Carousel</SelectItem>
                    <SelectItem value={ContentType.STORY}>Story</SelectItem>
                    <SelectItem value={ContentType.REEL}>Reel</SelectItem>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="target" className="mb-2 block">Target</Label>
                  <Select value={target} onValueChange={(value) => setTarget(value as ContentImprovementTarget)}>
                    <SelectItem value={ContentImprovementTarget.ENGAGEMENT}>Engagement</SelectItem>
                    <SelectItem value={ContentImprovementTarget.REACH}>Reach</SelectItem>
                    <SelectItem value={ContentImprovementTarget.CONVERSION}>Conversion</SelectItem>
                    <SelectItem value={ContentImprovementTarget.BRAND_AWARENESS}>Brand Awareness</SelectItem>
                    <SelectItem value={ContentImprovementTarget.EDUCATION}>Education</SelectItem>
                    <SelectItem value={ContentImprovementTarget.COMMUNITY_BUILDING}>Community Building</SelectItem>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="tone" className="mb-2 block">Tone</Label>
                  <Select value={tone} onValueChange={(value) => setTone(value as ContentImprovementTone)}>
                    <SelectItem value={ContentImprovementTone.PROFESSIONAL}>Professional</SelectItem>
                    <SelectItem value={ContentImprovementTone.CASUAL}>Casual</SelectItem>
                    <SelectItem value={ContentImprovementTone.FRIENDLY}>Friendly</SelectItem>
                    <SelectItem value={ContentImprovementTone.AUTHORITATIVE}>Authoritative</SelectItem>
                    <SelectItem value={ContentImprovementTone.HUMOROUS}>Humorous</SelectItem>
                    <SelectItem value={ContentImprovementTone.INSPIRATIONAL}>Inspirational</SelectItem>
                    <SelectItem value={ContentImprovementTone.EDUCATIONAL}>Educational</SelectItem>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="language" className="mb-2 block">Bahasa</Label>
                  <Select value={language} onValueChange={(value) => setLanguage(value as 'id' | 'en')}>
                    <SelectItem value="id">Indonesia</SelectItem>
                    <SelectItem value="en">Inggris</SelectItem>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="includeEmojis">Sertakan Emoji</Label>
                  <Switch
                    id="includeEmojis"
                    checked={includeEmojis}
                    onCheckedChange={setIncludeEmojis}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="includeHashtags">Sertakan Hashtag</Label>
                  <Switch
                    id="includeHashtags"
                    checked={includeHashtags}
                    onCheckedChange={setIncludeHashtags}
                  />
                </div>

                <div>
                  <Label htmlFor="focusKeywords" className="mb-2 block">Kata Kunci Fokus (pisahkan dengan koma)</Label>
                  <Input
                    id="focusKeywords"
                    placeholder="ban, michelin, tambang, performa"
                    value={focusKeywords}
                    onChange={(e) => setFocusKeywords(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2">
          <Button
            onClick={handleAnalyzeContent}
            disabled={isAnalyzing || !content}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isAnalyzing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Menganalisis...
              </>
            ) : (
              <>
                <Search className="mr-2 h-4 w-4" />
                Analisis Konten
              </>
            )}
          </Button>

          <Button
            onClick={handleImproveContent}
            disabled={isImproving || !content}
            className="bg-green-600 hover:bg-green-700"
          >
            {isImproving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Meningkatkan...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Tingkatkan Konten
              </>
            )}
          </Button>

          <Button
            onClick={handleGenerateVariations}
            disabled={isGeneratingVariations || !content}
            className="bg-purple-600 hover:bg-purple-700"
          >
            {isGeneratingVariations ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Membuat Variasi...
              </>
            ) : (
              <>
                <Zap className="mr-2 h-4 w-4" />
                Buat Variasi (A/B Test)
              </>
            )}
          </Button>
        </CardFooter>
      </Card>

      {/* Results Tabs */}
      {(analysisResult || improvementResult || variationsResult) && (
        <Tabs defaultValue={improvementResult ? "improvement" : analysisResult ? "analysis" : "variations"} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="analysis" disabled={!analysisResult}>
              <BarChart className="h-4 w-4 mr-2" />
              Analisis
            </TabsTrigger>
            <TabsTrigger value="improvement" disabled={!improvementResult}>
              <Sparkles className="h-4 w-4 mr-2" />
              Peningkatan
            </TabsTrigger>
            <TabsTrigger value="variations" disabled={!variationsResult}>
              <Zap className="h-4 w-4 mr-2" />
              Variasi A/B
            </TabsTrigger>
          </TabsList>

          {/* Analysis Results */}
          <TabsContent value="analysis">
            {analysisResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart className="h-5 w-5 mr-2 text-blue-600" />
                    Hasil Analisis Konten
                  </CardTitle>
                  <CardDescription>
                    Skor keseluruhan: <span className={getScoreColor(analysisResult.score)}>{analysisResult.score}/100</span>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-lg font-medium mb-4">Skor Metrik</h3>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between mb-1">
                            <span>Readability</span>
                            <span className={getScoreColor(analysisResult.readability.score)}>
                              {analysisResult.readability.score}/100
                            </span>
                          </div>
                          <Progress value={analysisResult.readability.score} className={getProgressColor(analysisResult.readability.score)} />
                        </div>

                        <div>
                          <div className="flex justify-between mb-1">
                            <span>Engagement</span>
                            <span className={getScoreColor(analysisResult.engagement.score)}>
                              {analysisResult.engagement.score}/100
                            </span>
                          </div>
                          <Progress value={analysisResult.engagement.score} className={getProgressColor(analysisResult.engagement.score)} />
                        </div>

                        <div>
                          <div className="flex justify-between mb-1">
                            <span>SEO</span>
                            <span className={getScoreColor(analysisResult.seo.score)}>
                              {analysisResult.seo.score}/100
                            </span>
                          </div>
                          <Progress value={analysisResult.seo.score} className={getProgressColor(analysisResult.seo.score)} />
                        </div>

                        <div>
                          <div className="flex justify-between mb-1">
                            <span>Tone Consistency</span>
                            <span className={getScoreColor(analysisResult.tone.consistency)}>
                              {analysisResult.tone.consistency}/100
                            </span>
                          </div>
                          <Progress value={analysisResult.tone.consistency} className={getProgressColor(analysisResult.tone.consistency)} />
                        </div>

                        <div>
                          <div className="flex justify-between mb-1">
                            <span>Structure</span>
                            <span className={getScoreColor(analysisResult.structure.score)}>
                              {analysisResult.structure.score}/100
                            </span>
                          </div>
                          <Progress value={analysisResult.structure.score} className={getProgressColor(analysisResult.structure.score)} />
                        </div>

                        {analysisResult.callToAction.present && (
                          <div>
                            <div className="flex justify-between mb-1">
                              <span>Call to Action</span>
                              <span className={getScoreColor(analysisResult.callToAction.strength)}>
                                {analysisResult.callToAction.strength}/100
                              </span>
                            </div>
                            <Progress value={analysisResult.callToAction.strength} className={getProgressColor(analysisResult.callToAction.strength)} />
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium mb-4">Saran Peningkatan</h3>
                      <div className="space-y-4">
                        {analysisResult.improvements.map((improvement, index) => (
                          <div key={index} className={`p-3 rounded-md border-l-4 ${
                            improvement.priority === 'high' ? 'border-red-500 bg-red-50' :
                            improvement.priority === 'medium' ? 'border-yellow-500 bg-yellow-50' :
                            'border-blue-500 bg-blue-50'
                          }`}>
                            <p className="font-medium">{improvement.description}</p>
                            <p className="text-sm mt-1">{improvement.suggestion}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h3 className="text-lg font-medium mb-2">Versi yang Ditingkatkan</h3>
                    <div className="p-4 bg-gray-50 rounded-md border border-gray-200 whitespace-pre-wrap">
                      {analysisResult.improvedVersion}
                    </div>
                    <div className="mt-2 flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(analysisResult.improvedVersion)}
                        className="text-xs"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Salin
                      </Button>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                            onClick={() => {
                              // Pre-populate the improvement result with analysis data
                              setImprovementResult({
                                originalContent: content,
                                improvedContent: analysisResult.improvedVersion,
                                score: {
                                  before: analysisResult.score - 10,
                                  after: analysisResult.score
                                },
                                improvements: analysisResult.improvements.map(imp => ({
                                  type: imp.priority === 'high' ? 'readability' :
                                        imp.priority === 'medium' ? 'engagement' : 'structure',
                                  description: imp.suggestion
                                })),
                                suggestedHashtags: []
                              });
                              setSaveDialogOpen(true);
                            }}
                          >
                            <Save className="h-3 w-3 mr-1" />
                            Simpan ke Daftar Konten
                          </Button>
                        </DialogTrigger>
                      </Dialog>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Improvement Results */}
          <TabsContent value="improvement">
            {improvementResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Sparkles className="h-5 w-5 mr-2 text-green-600" />
                    Hasil Peningkatan Konten
                  </CardTitle>
                  <CardDescription>
                    Peningkatan skor: <span className="text-gray-600">{improvementResult.score.before}</span>
                    <ArrowRight className="h-3 w-3 mx-1 inline" />
                    <span className={getScoreColor(improvementResult.score.after)}>{improvementResult.score.after}</span>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <h3 className="text-lg font-medium mb-2">Konten Asli</h3>
                      <div className="p-4 bg-gray-50 rounded-md border border-gray-200 h-full whitespace-pre-wrap">
                        {improvementResult.originalContent}
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium mb-2">Konten yang Ditingkatkan</h3>
                      <div className="p-4 bg-green-50 rounded-md border border-green-200 h-full whitespace-pre-wrap">
                        {improvementResult.improvedContent}
                      </div>
                      <div className="mt-2 flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(improvementResult.improvedContent)}
                          className="text-xs"
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          Salin
                        </Button>
                        <Dialog open={saveDialogOpen} onOpenChange={setSaveDialogOpen}>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                            >
                              <Save className="h-3 w-3 mr-1" />
                              Simpan ke Daftar Konten
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-[425px]">
                            <DialogHeader>
                              <DialogTitle>Simpan ke Daftar Konten</DialogTitle>
                              <DialogDescription>
                                Konten yang ditingkatkan akan disimpan ke daftar konten dan muncul di kalender konten.
                              </DialogDescription>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                              <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="contentTitle" className="text-right">
                                  Judul
                                </Label>
                                <Input
                                  id="contentTitle"
                                  value={contentTitle}
                                  onChange={(e) => setContentTitle(e.target.value)}
                                  placeholder="Judul konten"
                                  className="col-span-3"
                                />
                              </div>
                              <div className="grid grid-cols-4 items-center gap-4">
                                <Label htmlFor="scheduledDate" className="text-right">
                                  Tanggal
                                </Label>
                                <Input
                                  id="scheduledDate"
                                  type="date"
                                  value={scheduledDate}
                                  onChange={(e) => setScheduledDate(e.target.value)}
                                  className="col-span-3"
                                />
                              </div>
                            </div>
                            <DialogFooter>
                              <Button
                                type="submit"
                                onClick={handleSaveToContentList}
                                disabled={isSavingToContentList}
                              >
                                {isSavingToContentList ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Menyimpan...
                                  </>
                                ) : (
                                  <>
                                    <Calendar className="mr-2 h-4 w-4" />
                                    Simpan ke Kalender
                                  </>
                                )}
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Peningkatan yang Dilakukan</h3>
                    <div className="space-y-2">
                      {improvementResult.improvements.map((improvement, index) => (
                        <div key={index} className="flex items-start">
                          <Badge variant="outline" className="mr-2 mt-0.5">
                            {improvement.type === 'readability' ? 'Keterbacaan' :
                             improvement.type === 'engagement' ? 'Engagement' :
                             improvement.type === 'seo' ? 'SEO' :
                             improvement.type === 'tone' ? 'Tone' :
                             improvement.type === 'structure' ? 'Struktur' :
                             improvement.type === 'callToAction' ? 'Call to Action' :
                             improvement.type}
                          </Badge>
                          <p>{improvement.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {improvementResult.suggestedHashtags && improvementResult.suggestedHashtags.length > 0 && (
                    <div className="mt-6">
                      <h3 className="text-lg font-medium mb-2">Hashtag yang Disarankan</h3>
                      <div className="flex flex-wrap gap-2">
                        {improvementResult.suggestedHashtags.map((hashtag, index) => (
                          <Badge key={index} variant="secondary">#{hashtag}</Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Variations Results */}
          <TabsContent value="variations">
            {variationsResult && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Zap className="h-5 w-5 mr-2 text-purple-600" />
                    Variasi Konten untuk A/B Testing
                  </CardTitle>
                  <CardDescription>
                    {variationsResult.variations.length} variasi konten untuk pengujian A/B
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {variationsResult.variations.map((variation, index) => (
                      <div key={variation.id} className={`p-4 rounded-md border ${
                        variation.id === variationsResult.recommendedVariationId
                          ? 'border-purple-300 bg-purple-50'
                          : 'border-gray-200 bg-gray-50'
                      }`}>
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="text-md font-medium flex items-center">
                            Variasi {index + 1}
                            {variation.id === variationsResult.recommendedVariationId && (
                              <Badge className="ml-2 bg-purple-600">Direkomendasikan</Badge>
                            )}
                          </h3>
                          <span className={`text-sm font-medium ${getScoreColor(variation.score)}`}>
                            Skor: {variation.score}/100
                          </span>
                        </div>

                        <div className="mb-2">
                          <Badge variant="outline">
                            {variation.focus === 'readability' ? 'Keterbacaan' :
                             variation.focus === 'engagement' ? 'Engagement' :
                             variation.focus === 'seo' ? 'SEO' :
                             variation.focus === 'tone' ? 'Tone' :
                             variation.focus === 'structure' ? 'Struktur' :
                             variation.focus === 'callToAction' ? 'Call to Action' :
                             variation.focus}
                          </Badge>
                          <p className="text-sm mt-1">{variation.description}</p>
                        </div>

                        <div className="p-3 bg-white rounded border border-gray-200 whitespace-pre-wrap">
                          {variation.content}
                        </div>

                        <div className="mt-2 flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(variation.content)}
                            className="text-xs"
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            Salin
                          </Button>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-xs bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                                onClick={() => {
                                  // Pre-populate the improvement result with variation data
                                  setImprovementResult({
                                    originalContent: content,
                                    improvedContent: variation.content,
                                    score: {
                                      before: variation.score - 10,
                                      after: variation.score
                                    },
                                    improvements: [{
                                      type: variation.focus,
                                      description: variation.description
                                    }],
                                    suggestedHashtags: []
                                  });
                                  setSaveDialogOpen(true);
                                }}
                              >
                                <Save className="h-3 w-3 mr-1" />
                                Simpan ke Daftar Konten
                              </Button>
                            </DialogTrigger>
                          </Dialog>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      )}

      {/* Notification */}
      {notification.visible && (
        <div className={`fixed bottom-4 right-4 p-4 rounded-md shadow-lg ${
          notification.type === 'success' ? 'bg-green-100 text-green-800 border-l-4 border-green-500' :
          notification.type === 'error' ? 'bg-red-100 text-red-800 border-l-4 border-red-500' :
          'bg-blue-100 text-blue-800 border-l-4 border-blue-500'
        }`}>
          <div className="flex items-center">
            {notification.type === 'success' && <CheckCircle className="h-5 w-5 mr-2" />}
            {notification.type === 'error' && <AlertCircle className="h-5 w-5 mr-2" />}
            {notification.type === 'info' && <MessageSquare className="h-5 w-5 mr-2" />}
            <p>{notification.message}</p>
          </div>
        </div>
      )}
    </div>
  );
}
