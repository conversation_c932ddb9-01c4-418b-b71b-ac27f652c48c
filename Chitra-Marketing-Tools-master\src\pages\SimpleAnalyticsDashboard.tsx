import React, { useState, useEffect, useMemo } from 'react';
import {
  BarChart3,
  RefreshCw,
  Users,
  Package,
  Truck,
  DollarSign,
  TrendingUp,
  Calendar,
  Sparkles,
  FileText,
  Activity,
  Calculator,
  ShoppingCart,
  CloudRain,
  ArrowRight
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { useDataHub } from '../components/DataHubProvider';
import {
  useProducts,
  useCustomers,
  useFleetData,
  useCoalPrices,
  useHolidays
} from '../hooks/useDataHubHooks';
import { fetchSalesData } from '../services/fleetRevenueService';
import { getSeasonalInsights } from '../services/seasonalMarketingService';
import { formatCurrency } from '../utils/pricing';
import StatCard from '../components/dashboard/StatCard';
import ChartCard from '../components/dashboard/ChartCard';
import DashboardCard from '../components/dashboard/DashboardCard';
import { SeasonalInsight } from '../types/seasonalMarketing';
import { SalesItem } from '../services/fleetRevenueService';

export default function SimpleAnalyticsDashboard() {
  // Use Data Hub hooks for real-time data
  const { refreshData } = useDataHub();
  const { products, loading: productsLoading } = useProducts();
  const { customers, loading: customersLoading } = useCustomers();
  const { fleetData, loading: fleetDataLoading } = useFleetData();
  const { coalPrices, coalTrend, loading: coalPricesLoading } = useCoalPrices();
  const { holidays, loading: holidaysLoading } = useHolidays();

  // State for other data
  const [salesData, setSalesData] = useState<SalesItem[]>([]);
  const [insights, setInsights] = useState<SeasonalInsight[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch sales data and insights in parallel
        const [salesDataResult, insightsData] = await Promise.all([
          fetchSalesData(),
          getSeasonalInsights()
        ]);

        setSalesData(salesDataResult);
        setInsights(insightsData);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Calculate summary metrics
  const totalCustomers = customers.length;
  const totalProducts = products.length;
  const totalSales = salesData.reduce((sum, item) => sum + (item.Revenue || 0), 0);
  const averagePrice = products.length > 0
    ? products.reduce((sum, product) => sum + (product.price || 0), 0) / products.length
    : 0;

  // Fleet metrics
  const totalFleetRecords = fleetData.length;
  const totalTires = fleetData.reduce((sum, item) => sum + (parseInt(item.totaltire || '0', 10) || 0), 0);
  const totalUnits = fleetData.reduce((sum, item) => sum + (parseInt(item.unit_qty || '0', 10) || 0), 0);

  // Get current coal price
  const currentCoalPrice = coalPrices.length > 0 ? coalPrices[0].price : 0;

  // Get high priority insights
  const highPriorityInsights = insights
    .filter(insight => insight.recommendationLevel === 'high')
    .slice(0, 3);

  // Prepare chart data
  const salesChartData = useMemo(() => {
    const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    const data = [
      Math.round(totalSales * 0.7),
      Math.round(totalSales * 0.8),
      Math.round(totalSales * 0.75),
      Math.round(totalSales * 0.9),
      Math.round(totalSales * 0.85),
      Math.round(totalSales)
    ];
    return { labels, data };
  }, [totalSales]);

  // Product distribution chart
  const productChartData = useMemo(() => {
    // Group products by category or type
    const categories: Record<string, number> = {};

    products.forEach(product => {
      const category = product.materialDescription?.split(' ')[0] || 'Other';
      categories[category] = (categories[category] || 0) + 1;
    });

    const labels = Object.keys(categories).slice(0, 5);
    const data = labels.map(label => categories[label]);

    return { labels, data };
  }, [products]);

  // Customer distribution chart
  const customerChartData = useMemo(() => {
    // Group customers by region or type
    const regions = ['Jakarta', 'Kalimantan', 'Sumatra', 'Sulawesi', 'Other'];
    const data = regions.map(() => Math.floor(Math.random() * totalCustomers / 2));

    return { labels: regions, data };
  }, [totalCustomers]);

  // Upcoming holidays
  const upcomingHolidays = holidays
    .filter(h => new Date(h.date) > new Date())
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .slice(0, 3);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <BarChart3 className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Dashboard Chitra Marketing Tools</h1>
        </div>
        <Button
          size="sm"
          variant="outline"
          onClick={() => {
            refreshData(); // Refresh Data Hub data
            window.location.reload(); // Also reload the page for other data
          }}
          className="flex items-center"
        >
          <RefreshCw className="h-4 w-4 mr-1" />
          Refresh Data
        </Button>
      </div>

      {/* Welcome Banner */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white rounded-lg p-6 shadow-md">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div>
            <h2 className="text-xl font-bold mb-2">Selamat Datang di Chitra Marketing Tools</h2>
            <p className="text-blue-100 mb-4">
              Pantau metrik penting, analisis data, dan kelola strategi pemasaran Anda dari satu tempat.
            </p>
            <div className="flex space-x-3">
              <Link to="/seasonal-marketing-calendar">
                <Button size="sm" className="bg-yellow-500 text-black font-medium hover:bg-yellow-400">
                  <Calendar className="h-4 w-4 mr-1" />
                  Kalender Promosi
                </Button>
              </Link>
              <Link to="/fleet-analyzer">
                <Button size="sm" className="bg-green-500 text-black font-medium hover:bg-green-400">
                  <Truck className="h-4 w-4 mr-1" />
                  Fleet Analyzer
                </Button>
              </Link>
            </div>
          </div>
          <div className="mt-4 md:mt-0">
            <img
              src="/assets/dashboard-illustration.svg"
              alt="Dashboard Illustration"
              className="h-32 w-auto"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
          </div>
        </div>
      </div>

      {/* Stats Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Link to="/customers" className="transition-transform hover:scale-105">
          <StatCard
            title="Total Pelanggan"
            value={totalCustomers}
            icon={Users}
            description="Jumlah pelanggan aktif"
          />
        </Link>
        <Link to="/products" className="transition-transform hover:scale-105">
          <StatCard
            title="Total Produk"
            value={totalProducts}
            icon={Package}
            description="Jumlah produk tersedia"
          />
        </Link>
        <Link to="/fleet-analyzer" className="transition-transform hover:scale-105">
          <StatCard
            title="Total Unit"
            value={totalUnits}
            icon={Truck}
            description={`${totalTires} ban terdaftar`}
          />
        </Link>
        <Link to="/coal-price-data" className="transition-transform hover:scale-105">
          <StatCard
            title="Harga Batu Bara"
            value={`$${currentCoalPrice.toFixed(2)}`}
            icon={TrendingUp}
            description="Harga acuan terbaru"
            trend={coalTrend?.trend || 'neutral'}
            trendValue={coalTrend ? `${coalTrend.percentChange.toFixed(1)}%` : 'Stabil'}
          />
        </Link>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartCard
          title="Penjualan 6 Bulan Terakhir"
          icon={BarChart3}
          chartType="line"
          labels={salesChartData.labels}
          datasets={[
            {
              label: 'Penjualan',
              data: salesChartData.data,
              borderColor: 'rgb(59, 130, 246)',
              backgroundColor: 'rgba(59, 130, 246, 0.1)'
            }
          ]}
        />
        <ChartCard
          title="Distribusi Produk"
          icon={Package}
          chartType="pie"
          labels={productChartData.labels}
          datasets={[
            {
              label: 'Jumlah Produk',
              data: productChartData.data,
              backgroundColor: [
                'rgba(59, 130, 246, 0.7)',
                'rgba(139, 92, 246, 0.7)',
                'rgba(16, 185, 129, 0.7)',
                'rgba(245, 158, 11, 0.7)',
                'rgba(239, 68, 68, 0.7)'
              ]
            }
          ]}
        />
      </div>

      {/* Additional Info Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Seasonal Insights */}
        <DashboardCard
          title="Insight Musiman Terbaru"
          icon={Calendar}
          footer={
            <Link to="/seasonal-marketing-calendar" className="flex items-center text-blue-600 hover:text-blue-800">
              <span>Lihat semua insight</span>
              <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          }
        >
          <div className="space-y-3">
            {highPriorityInsights.length > 0 ? (
              highPriorityInsights.map((insight, index) => (
                <div key={index} className="p-3 border rounded-md flex items-start hover:bg-blue-50 transition-colors">
                  <Sparkles className="h-5 w-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium">{insight.title}</h4>
                    <p className="text-sm text-gray-600 mt-1">{insight.description.substring(0, 100)}...</p>
                    <div className="flex items-center mt-2 text-xs text-gray-500">
                      <Calendar className="h-3 w-3 mr-1" />
                      <span>{new Date(insight.startDate).toLocaleDateString('id-ID', { day: 'numeric', month: 'long' })} - {new Date(insight.endDate).toLocaleDateString('id-ID', { day: 'numeric', month: 'long' })}</span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-4 text-gray-500">
                <Calendar className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p>Tidak ada insight musiman prioritas tinggi saat ini.</p>
              </div>
            )}
          </div>
        </DashboardCard>

        {/* Upcoming Holidays */}
        <DashboardCard
          title="Hari Libur Mendatang"
          icon={Calendar}
          footer={
            <Link to="/seasonal-marketing-calendar" className="flex items-center text-blue-600 hover:text-blue-800">
              <span>Lihat kalender lengkap</span>
              <ArrowRight className="h-4 w-4 ml-1" />
            </Link>
          }
        >
          <div className="space-y-3">
            {upcomingHolidays.length > 0 ? (
              upcomingHolidays.map((holiday, index) => (
                <div key={index} className="p-3 border rounded-md flex items-start hover:bg-blue-50 transition-colors">
                  <Calendar className="h-5 w-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium">{holiday.name}</h4>
                    <div className="flex items-center mt-2 text-xs text-gray-500">
                      <Calendar className="h-3 w-3 mr-1" />
                      <span>{new Date(holiday.date).toLocaleDateString('id-ID', { day: 'numeric', month: 'long', year: 'numeric' })}</span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-4 text-gray-500">
                <Calendar className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p>Tidak ada hari libur dalam waktu dekat.</p>
              </div>
            )}
          </div>
        </DashboardCard>
      </div>

      {/* Quick Access Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Link to="/bundling-proposal" className="bg-white rounded-lg border shadow-sm p-4 flex items-center hover:bg-blue-50 transition-colors">
          <div className="p-3 rounded-full bg-blue-100 mr-4">
            <FileText className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="font-medium">Proposal Marketing</h3>
            <p className="text-sm text-gray-500">Buat proposal bundling dan promosi</p>
          </div>
        </Link>
        <Link to="/promo-simulation" className="bg-white rounded-lg border shadow-sm p-4 flex items-center hover:bg-blue-50 transition-colors">
          <div className="p-3 rounded-full bg-purple-100 mr-4">
            <Sparkles className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h3 className="font-medium">Simulasi Promo</h3>
            <p className="text-sm text-gray-500">Simulasikan promosi bisnis</p>
          </div>
        </Link>
        <Link to="/bundling-calculator" className="bg-white rounded-lg border shadow-sm p-4 flex items-center hover:bg-blue-50 transition-colors">
          <div className="p-3 rounded-full bg-green-100 mr-4">
            <Calculator className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h3 className="font-medium">Kalkulator Bundling</h3>
            <p className="text-sm text-gray-500">Hitung bundling produk</p>
          </div>
        </Link>
      </div>
    </div>
  );
}
