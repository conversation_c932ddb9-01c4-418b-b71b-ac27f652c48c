import React from 'react';
import { BundleConfig } from '../types';
import CustomerSelector from './CustomerSelector';

interface BundleConfiguratorProps {
  config: BundleConfig;
  onConfigChange: (config: BundleConfig) => void;
}

export default function BundleConfigurator({ config, onConfigChange }: BundleConfiguratorProps) {
  const handleMinimumMarginChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = Math.max(0, Number(e.target.value));
    onConfigChange({
      ...config,
      minimumMargin: newValue
    });
  };

  const handleShippingCostChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = Math.max(0, Number(e.target.value));
    onConfigChange({
      ...config,
      additionalCosts: {
        ...config.additionalCosts,
        shipping: newValue
      }
    });
  };

  const handleOtherCostChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = Math.max(0, Number(e.target.value));
    onConfigChange({
      ...config,
      additionalCosts: {
        ...config.additionalCosts,
        other: newValue
      }
    });
  };

  const handleDescriptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onConfigChange({
      ...config,
      additionalCosts: {
        ...config.additionalCosts,
        description: e.target.value
      }
    });
  };

  const handleCustomerSelect = (customerId: string | undefined) => {
    onConfigChange({
      ...config,
      customerId
    });
  };

  const handleQuoDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onConfigChange({
      ...config,
      quoDate: e.target.value
    });
  };

  const handleValidityQuoteChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onConfigChange({
      ...config,
      validityQuote: e.target.value
    });
  };

  const handleFromChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onConfigChange({
      ...config,
      from: e.target.value
    });
  };

  const handleNoteChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onConfigChange({
      ...config,
      note: e.target.value
    });
  };

  const handleTermsAndConditionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onConfigChange({
      ...config,
      termsAndCondition: e.target.value
    });
  };

  return (
    <div className="space-y-6 bg-white p-6 rounded-lg shadow-sm border">
      <h2 className="text-xl font-semibold">Bundle Configuration</h2>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Minimum Profit Margin (%)
          </label>
          <input
            type="number"
            min="0"
            value={config.minimumMargin}
            onChange={handleMinimumMarginChange}
            className="w-full p-2 border rounded-md"
          />
          <p className="mt-1 text-sm text-gray-500">
            Set the minimum profit margin you want to achieve with this bundle
          </p>
        </div>

        <div className="pt-4 border-t">
          <CustomerSelector
            selectedCustomerId={config.customerId}
            onCustomerSelect={handleCustomerSelect}
          />
        </div>

        <div className="pt-4 border-t">
          <h3 className="text-lg font-medium mb-3">Quotation Details</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Quo Date
              </label>
              <input
                type="date"
                value={config.quoDate || new Date().toISOString().split('T')[0]}
                onChange={handleQuoDateChange}
                className="w-full p-2 border rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Validity Quote
              </label>
              <input
                type="date"
                value={config.validityQuote || ''}
                onChange={handleValidityQuoteChange}
                className="w-full p-2 border rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                From
              </label>
              <input
                type="text"
                value={config.from || ''}
                onChange={handleFromChange}
                placeholder="e.g., Sales Team"
                className="w-full p-2 border rounded-md"
              />
            </div>
          </div>
        </div>

        <div className="pt-4 border-t">
          <h3 className="text-lg font-medium mb-3">Additional Costs</h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Shipping Cost (IDR)
              </label>
              <input
                type="number"
                min="0"
                value={config.additionalCosts.shipping}
                onChange={handleShippingCostChange}
                className="w-full p-2 border rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Other Costs (IDR)
              </label>
              <input
                type="number"
                min="0"
                value={config.additionalCosts.other}
                onChange={handleOtherCostChange}
                className="w-full p-2 border rounded-md"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Additional Costs Description
              </label>
              <input
                type="text"
                value={config.additionalCosts.description}
                onChange={handleDescriptionChange}
                placeholder="e.g., Packaging, handling, insurance"
                className="w-full p-2 border rounded-md"
              />
            </div>
          </div>
        </div>

        <div className="pt-4 border-t">
          <h3 className="text-lg font-medium mb-3">Notes & Terms</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Note
              </label>
              <textarea
                value={config.note || ''}
                onChange={handleNoteChange}
                placeholder="Additional notes for the quotation"
                className="w-full p-2 border rounded-md"
                rows={3}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Terms And Condition
              </label>
              <textarea
                value={config.termsAndCondition || ''}
                onChange={handleTermsAndConditionChange}
                placeholder="Terms and conditions for the quotation"
                className="w-full p-2 border rounded-md"
                rows={3}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
