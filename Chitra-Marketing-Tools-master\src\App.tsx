import { HashRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { AppBar, Toolbar, Typography, Button, Container } from '@mui/material';
import ProposalBuilder from './pages/ProposalBuilder';
import ImageGeneratorPage from './pages/ImageGeneratorPage';

function App() {
  return (
    <Router>
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            BundleBoost
          </Typography>
          <Button color="inherit" component={Link} to="/proposal-builder">
            Proposal Builder
          </Button>
          <Button color="inherit" component={Link} to="/image-generator">
            Image Generator
          </Button>
        </Toolbar>
      </AppBar>

      <Container>
        <Routes>
          <Route path="/proposal-builder" element={<ProposalBuilder />} />
          <Route path="/image-generator" element={<ImageGeneratorPage />} />
        </Routes>
      </Container>
    </Router>
  );
}

export default App;
