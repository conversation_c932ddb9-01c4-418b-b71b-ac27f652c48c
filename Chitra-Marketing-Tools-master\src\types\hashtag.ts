/**
 * Types for Enhanced Hashtag Management
 */

/**
 * Hashtag Category
 */
export enum HashtagCategory {
  PRODUCT = 'Produk',
  BRAND = 'Brand',
  INDUSTRY = 'Industri',
  CAMPAIGN = 'Kampanye',
  TRENDING = 'Trending',
  LOCATION = 'Lokasi',
  EVENT = 'Event',
  SEASONAL = 'Musiman',
  ENGAGEMENT = 'Engagement',
  MINING = 'Pertambangan',
  SAFETY = 'Keselamatan',
  AUTOMOTIVE = 'Otomotif'
}

/**
 * Hashtag Performance Metrics
 */
export interface HashtagPerformance {
  popularity: number; // 0-100 score
  relevance: number; // 0-100 score
  competition: number; // 0-100 score
  recommendation: 'high' | 'medium' | 'low';
  usageCount?: number; // How many times this hashtag has been used
  averageEngagement?: number; // Average engagement rate when this hashtag is used
  lastUpdated: Date; // When the performance was last updated
}

/**
 * Hashtag Entry
 */
export interface HashtagEntry {
  id: string;
  name: string; // Without the # symbol
  category: HashtagCategory;
  description?: string;
  performance?: HashtagPerformance;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  relatedHashtags?: string[]; // IDs of related hashtags
}

/**
 * Hashtag Create Request
 */
export interface HashtagCreateRequest {
  name: string;
  category: HashtagCategory;
  description?: string;
  isActive?: boolean;
  relatedHashtags?: string[];
}

/**
 * Hashtag Update Request
 */
export interface HashtagUpdateRequest {
  id: string;
  name?: string;
  category?: HashtagCategory;
  description?: string;
  performance?: Partial<HashtagPerformance>;
  isActive?: boolean;
  relatedHashtags?: string[];
}

/**
 * Hashtag Group
 */
export interface HashtagGroup {
  id: string;
  name: string;
  description?: string;
  hashtagIds: string[];
  category?: HashtagCategory;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Hashtag Analysis Request
 */
export interface HashtagAnalysisRequest {
  hashtags: string[];
  industry?: string;
  platform?: string;
}

/**
 * Hashtag Analysis Response
 */
export interface HashtagAnalysisResponse {
  results: {
    hashtag: string;
    popularity: number;
    relevance: number;
    competition: number;
    recommendation: 'high' | 'medium' | 'low';
  }[];
  suggestedHashtags?: string[];
}

/**
 * Hashtag Search Request
 */
export interface HashtagSearchRequest {
  searchTerm?: string;
  category?: HashtagCategory;
  recommendationLevel?: 'high' | 'medium' | 'low';
  isActive?: boolean;
  sortBy?: 'name' | 'popularity' | 'relevance' | 'competition' | 'createdAt';
  sortDirection?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

/**
 * Hashtag Search Response
 */
export interface HashtagSearchResponse {
  hashtags: HashtagEntry[];
  total: number;
}
