import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  SocialMediaPost,
  SocialMediaPlatform,
  ContentType,
  PostStatus
} from '../types/socialMedia';
import {
  getAllSocialMediaPosts,
  deleteSocialMediaPost,
  updateSocialMediaPost
} from '../services/socialMediaService';
import {
  List,
  Instagram,
  Image as ImageIcon,
  Video,
  Layers,
  Clock,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  Trash2,
  Edit,
  Eye,
  Calendar,
  Search,
  Filter,
  X,
  Save,
  Copy,
  RefreshCw,
  ArrowUpDown,
  SortAsc,
  SortDesc,
  Grid,
  Table as TableIcon,
  Sparkles,
  Wand2
} from 'lucide-react';
import { useToast } from '../components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../components/ui/alert-dialog";

interface InstagramContentListProps {
  onEditPost?: (post: SocialMediaPost) => void;
  onImproveContent?: (post: SocialMediaPost) => void;
  refreshTrigger?: number;
}

export default function InstagramContentList({ onEditPost, onImproveContent, refreshTrigger }: InstagramContentListProps) {
  // State for posts
  const [posts, setPosts] = useState<SocialMediaPost[]>([]);
  const [filteredPosts, setFilteredPosts] = useState<SocialMediaPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<PostStatus | 'all'>('all');
  const [contentTypeFilter, setContentTypeFilter] = useState<ContentType | 'all'>('all');

  // State for post view modal
  const [selectedPost, setSelectedPost] = useState<SocialMediaPost | null>(null);
  const [showPostModal, setShowPostModal] = useState(false);

  // State for delete confirmation
  const [postToDelete, setPostToDelete] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // State for view mode
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');

  // State for sorting
  const [sortField, setSortField] = useState<'createdAt' | 'scheduledDate' | 'title'>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [postsPerPage, setPostsPerPage] = useState(10);

  // Navigation
  const navigate = useNavigate();

  // Toast notifications
  const { toast } = useToast();

  // Load posts function
  const loadPosts = async () => {
    setLoading(true);
    try {
      const allPosts = await getAllSocialMediaPosts();
      // Filter only Instagram posts
      const instagramPosts = allPosts.filter(post => post.platform === SocialMediaPlatform.INSTAGRAM);
      setPosts(instagramPosts);

      // Initialize filtered posts to prevent empty state
      setFilteredPosts(instagramPosts);

      console.log(`Loaded ${instagramPosts.length} Instagram posts`);
    } catch (error) {
      console.error('Error loading posts:', error);
      toast({
        title: "Error",
        description: "Gagal memuat data konten Instagram",
        variant: "destructive"
      });

      // Set empty arrays to prevent undefined errors
      setPosts([]);
      setFilteredPosts([]);
    } finally {
      setLoading(false);
    }
  };

  // Load posts on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Loading Instagram content list data...');
        await loadPosts();
        console.log('Instagram content list data loaded successfully');
      } catch (error) {
        console.error('Error loading Instagram content list data:', error);
        // Set empty arrays to prevent undefined errors
        setPosts([]);
        setFilteredPosts([]);

        toast({
          title: "Error",
          description: "Gagal memuat data konten Instagram. Silakan coba lagi.",
          variant: "destructive"
        });
      }
    };

    fetchData();
  }, []);

  // Tambahkan efek untuk refreshTrigger
  useEffect(() => {
    if (refreshTrigger !== undefined) {
      loadPosts();
    }
  }, [refreshTrigger]);

  // Refresh posts
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadPosts();
    setRefreshing(false);
    toast({
      title: "Berhasil",
      description: "Data konten Instagram berhasil diperbarui",
    });
  };

  // Apply filters and sorting when dependencies change
  useEffect(() => {
    let filtered = [...posts];

    // Apply search term filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(post =>
        (post.title?.toLowerCase().includes(term) || false) ||
        post.caption.toLowerCase().includes(term) ||
        post.hashtags.some(tag => tag.toLowerCase().includes(term))
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(post => post.status === statusFilter);
    }

    // Apply content type filter
    if (contentTypeFilter !== 'all') {
      filtered = filtered.filter(post => post.contentType === contentTypeFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let valueA, valueB;

      // Handle different sort fields
      if (sortField === 'title') {
        valueA = a.title || '';
        valueB = b.title || '';
      } else if (sortField === 'scheduledDate') {
        valueA = (a.scheduledDate || a.publishedDate || a.createdAt).getTime();
        valueB = (b.scheduledDate || b.publishedDate || b.createdAt).getTime();
      } else {
        // Default to createdAt
        valueA = a.createdAt.getTime();
        valueB = b.createdAt.getTime();
      }

      // Apply sort direction
      if (sortDirection === 'asc') {
        if (typeof valueA === 'string' && typeof valueB === 'string') {
          return valueA.localeCompare(valueB);
        } else if (typeof valueA === 'number' && typeof valueB === 'number') {
          return valueA - valueB;
        } else {
          return 0;
        }
      } else {
        if (typeof valueA === 'string' && typeof valueB === 'string') {
          return valueB.localeCompare(valueA);
        } else if (typeof valueA === 'number' && typeof valueB === 'number') {
          return valueB - valueA;
        } else {
          return 0;
        }
      }
    });

    setFilteredPosts(filtered);

    // Reset to first page when filters change
    setCurrentPage(1);
  }, [posts, searchTerm, statusFilter, contentTypeFilter, sortField, sortDirection]);

  // Open delete confirmation dialog
  const confirmDelete = (id: string) => {
    setPostToDelete(id);
    setShowDeleteDialog(true);
  };

  // Handle post deletion
  const handleDeletePost = async () => {
    if (!postToDelete) return;

    try {
      await deleteSocialMediaPost(postToDelete);
      setPosts(prevPosts => prevPosts.filter(post => post.id !== postToDelete));

      toast({
        title: "Berhasil",
        description: "Konten berhasil dihapus",
      });
    } catch (error) {
      console.error('Error deleting post:', error);
      toast({
        title: "Error",
        description: "Gagal menghapus konten",
        variant: "destructive"
      });
    } finally {
      setShowDeleteDialog(false);
      setPostToDelete(null);
    }
  };

  // Handle post status change
  const handleStatusChange = async (post: SocialMediaPost, newStatus: PostStatus) => {
    try {
      const updatedPost = await updateSocialMediaPost({
        ...post,
        status: newStatus,
        publishedDate: newStatus === PostStatus.PUBLISHED ? new Date() : post.publishedDate
      });

      setPosts(prevPosts =>
        prevPosts.map(p => p.id === updatedPost.id ? updatedPost : p)
      );

      toast({
        title: "Berhasil",
        description: `Status konten berhasil diubah menjadi ${getStatusLabel(newStatus)}`,
      });

      // Close modal if open
      if (showPostModal) {
        setShowPostModal(false);
      }
    } catch (error) {
      console.error('Error updating post status:', error);
      toast({
        title: "Error",
        description: "Gagal mengubah status konten",
        variant: "destructive"
      });
    }
  };

  // Get content type icon
  const getContentTypeIcon = (contentType: ContentType) => {
    switch (contentType) {
      case ContentType.IMAGE:
        return <ImageIcon size={16} className="text-blue-500" />;
      case ContentType.VIDEO:
        return <Video size={16} className="text-red-500" />;
      case ContentType.CAROUSEL:
        return <Layers size={16} className="text-purple-500" />;
      case ContentType.STORY:
        return <Clock size={16} className="text-orange-500" />;
      case ContentType.REEL:
        return <Video size={16} className="text-pink-500" />;
      default:
        return <ImageIcon size={16} />;
    }
  };

  // Get status icon and color
  const getStatusIcon = (status: PostStatus) => {
    switch (status) {
      case PostStatus.PUBLISHED:
        return <CheckCircle size={16} className="text-green-500" />;
      case PostStatus.SCHEDULED:
        return <Clock size={16} className="text-blue-500" />;
      case PostStatus.DRAFT:
        return <AlertCircle size={16} className="text-yellow-500" />;
      case PostStatus.ARCHIVED:
        return <AlertCircle size={16} className="text-gray-500" />;
      default:
        return null;
    }
  };

  // Get status label
  const getStatusLabel = (status: PostStatus): string => {
    switch (status) {
      case PostStatus.PUBLISHED:
        return 'Dipublikasikan';
      case PostStatus.SCHEDULED:
        return 'Terjadwal';
      case PostStatus.DRAFT:
        return 'Draft';
      case PostStatus.ARCHIVED:
        return 'Diarsipkan';
      default:
        return '';
    }
  };

  // Format date
  const formatDate = (date: Date | undefined): string => {
    if (!date) return 'N/A';
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  // Copy content to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Berhasil",
      description: "Teks berhasil disalin ke clipboard",
    });
  };

  // Generate detailed and realistic image prompt and navigate to Image Generator
  const generateImagePrompt = (post: SocialMediaPost) => {
    // Prompt sederhana, panjang, dan kaya konten, semua tulisan dibungkus tanda petik
    let simplePrompt = 'Instagram post visual, social media marketing, banyak tulisan, desain menarik, cocok untuk feed Instagram. ';
    if (post.title) simplePrompt += `"${post.title}". `;
    if (post.caption) simplePrompt += `"${post.caption}" `;
    if (post.imageDescription) simplePrompt += `"${post.imageDescription}" `;
    if (post.hashtags && post.hashtags.length > 0) simplePrompt += `"${post.hashtags.map(tag => `#${tag}`).join(' ')}" `;
    simplePrompt += 'Tampilkan lebih banyak tulisan dan informasi pada gambar, layout profesional, mudah dibaca, warna menarik.';

    // Negative prompt tetap sama
    const negativePrompt = "deformed, blurry, bad anatomy, disfigured, poorly drawn face, mutation, mutated, extra limb, ugly, poorly drawn hands, missing limb, floating limbs, disconnected limbs, malformed hands, blurry, out of focus, long neck, long body, distorted, bad proportions, extra limbs, cloned face, disfigured, gross proportions, missing arms, missing legs, extra arms, extra legs, fused fingers, too many fingers, watermark, signature, cut off, low contrast, underexposed, overexposed, bad art, beginner, amateur, low quality, pixelated, jpeg artifacts, artificial, AI generated, unnatural lighting, unnatural shadows, unrealistic, cartoon, anime, drawing, painting, sketch, illustration, CGI, 3D render, low resolution, stock photo, cropped image, frame, border, collage, montage, photoshop, edited, heavily edited, over-saturated, under-saturated, illegible text, poorly rendered text, misspelled text, nonsense text, gibberish text, random letters, unreadable text";

    sessionStorage.setItem('instagram_image_prompt', simplePrompt);
    sessionStorage.setItem('instagram_negative_prompt', negativePrompt);
    navigate('/image-generator');
    toast({
      title: "Berhasil",
      description: "Prompt gambar sederhana dan kaya konten berhasil dibuat, mengarahkan ke Image Generator",
    });
  };

  // Get paginated posts
  const getPaginatedPosts = () => {
    const indexOfLastPost = currentPage * postsPerPage;
    const indexOfFirstPost = indexOfLastPost - postsPerPage;
    return filteredPosts.slice(indexOfFirstPost, indexOfLastPost);
  };

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Toggle sort direction
  const handleSort = (field: 'createdAt' | 'scheduledDate' | 'title') => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to descending
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // Error fallback component
  const ErrorFallback = () => (
    <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200 text-center">
      <AlertTriangle size={48} className="mx-auto text-red-500 mb-4" />
      <h3 className="text-xl font-semibold text-gray-800 mb-2">Terjadi Kesalahan</h3>
      <p className="text-gray-600 mb-6">Maaf, terjadi kesalahan saat memuat data konten Instagram.</p>
      <button
        onClick={handleRefresh}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 inline-flex items-center"
      >
        <RefreshCw size={16} className="mr-2" />
        Coba Lagi
      </button>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Instagram size={20} className="text-pink-500 mr-2" />
          <h2 className="text-xl font-semibold">Daftar Konten Instagram</h2>
          <span className="ml-2 text-sm text-gray-500">({filteredPosts.length} konten)</span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleRefresh}
            className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
            disabled={refreshing}
            title="Refresh data"
          >
            <RefreshCw size={18} className={refreshing ? "animate-spin" : ""} />
          </button>
          <button
            onClick={() => setViewMode(viewMode === 'table' ? 'grid' : 'table')}
            className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
            title={viewMode === 'table' ? 'Tampilan grid' : 'Tampilan tabel'}
          >
            {viewMode === 'table' ? <Grid size={18} /> : <TableIcon size={18} />}
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          <div className="flex-1">
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Cari konten berdasarkan judul, caption, atau hashtag..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 w-full"
              />
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <select
              value={postsPerPage}
              onChange={(e) => setPostsPerPage(Number(e.target.value))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              title="Jumlah konten per halaman"
            >
              <option value={5}>5 per halaman</option>
              <option value={10}>10 per halaman</option>
              <option value={20}>20 per halaman</option>
              <option value={50}>50 per halaman</option>
            </select>
          </div>
        </div>

        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as PostStatus | 'all')}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="all">Semua Status</option>
              <option value={PostStatus.PUBLISHED}>Dipublikasikan</option>
              <option value={PostStatus.SCHEDULED}>Terjadwal</option>
              <option value={PostStatus.DRAFT}>Draft</option>
              <option value={PostStatus.ARCHIVED}>Diarsipkan</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Tipe Konten</label>
            <select
              value={contentTypeFilter}
              onChange={(e) => setContentTypeFilter(e.target.value as ContentType | 'all')}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="all">Semua Tipe</option>
              <option value={ContentType.IMAGE}>Gambar</option>
              <option value={ContentType.VIDEO}>Video</option>
              <option value={ContentType.CAROUSEL}>Carousel</option>
              <option value={ContentType.REEL}>Reel</option>
              <option value={ContentType.STORY}>Story</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Urutkan Berdasarkan</label>
            <div className="flex items-center">
              <select
                value={sortField}
                onChange={(e) => setSortField(e.target.value as 'createdAt' | 'scheduledDate' | 'title')}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="createdAt">Tanggal Dibuat</option>
                <option value="scheduledDate">Tanggal Publikasi</option>
                <option value="title">Judul</option>
              </select>
              <button
                onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
                className="ml-2 p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                title={sortDirection === 'asc' ? 'Urutkan menurun' : 'Urutkan menaik'}
              >
                {sortDirection === 'asc' ? <SortAsc size={18} /> : <SortDesc size={18} />}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content List */}
      {posts.length === 0 && !loading ? (
        <ErrorFallback />
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2"></div>
              <p className="text-gray-500">Memuat data...</p>
            </div>
          ) : filteredPosts.length === 0 ? (
            <div className="p-8 text-center">
              <Instagram size={32} className="mx-auto text-gray-400 mb-2" />
              <p className="text-gray-500">Tidak ada konten yang ditemukan</p>
            </div>
          ) : viewMode === 'table' ? (
            <div className="overflow-x-auto">
              {/* Table View */}
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => handleSort('title')}
                      >
                        Konten
                        {sortField === 'title' && (
                          <span className="ml-1">
                            {sortDirection === 'asc' ? <SortAsc size={14} /> : <SortDesc size={14} />}
                          </span>
                        )}
                      </button>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tipe
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => handleSort('scheduledDate')}
                      >
                        Tanggal
                        {sortField === 'scheduledDate' && (
                          <span className="ml-1">
                            {sortDirection === 'asc' ? <SortAsc size={14} /> : <SortDesc size={14} />}
                          </span>
                        )}
                      </button>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => handleSort('createdAt')}
                      >
                        Dibuat
                        {sortField === 'createdAt' && (
                          <span className="ml-1">
                            {sortDirection === 'asc' ? <SortAsc size={14} /> : <SortDesc size={14} />}
                          </span>
                        )}
                      </button>
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {getPaginatedPosts().map((post) => (
                    <tr key={post.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-start">
                          <Instagram size={16} className="text-pink-500 mt-1 mr-2" />
                          <div>
                            <div className="font-medium text-gray-900">{post.title || 'Tanpa Judul'}</div>
                            <div className="text-sm text-gray-500 truncate max-w-xs">
                              {post.caption.length > 50 ? `${post.caption.substring(0, 50)}...` : post.caption}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getContentTypeIcon(post.contentType)}
                          <span className="ml-1 text-sm text-gray-900">
                            {post.contentType.charAt(0).toUpperCase() + post.contentType.slice(1)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(post.status)}
                          <span className="ml-1 text-sm text-gray-900">
                            {getStatusLabel(post.status)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {post.status === PostStatus.PUBLISHED
                          ? formatDate(post.publishedDate)
                          : formatDate(post.scheduledDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(post.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button
                            onClick={() => {
                              setSelectedPost(post);
                              setShowPostModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900"
                            title="Lihat Detail"
                          >
                            <Eye size={16} />
                          </button>
                          {onEditPost && (
                            <button
                              onClick={() => onEditPost(post)}
                              className="text-yellow-600 hover:text-yellow-900"
                              title="Edit Konten"
                            >
                              <Edit size={16} />
                            </button>
                          )}
                          {onImproveContent && (
                            <button
                              onClick={() => onImproveContent(post)}
                              className="text-green-600 hover:text-green-900"
                              title="Tingkatkan Konten"
                            >
                              <Sparkles size={16} />
                            </button>
                          )}
                          <button
                            onClick={() => generateImagePrompt(post)}
                            className="text-purple-600 hover:text-purple-900"
                            title="Generate Image Prompt"
                          >
                            <Wand2 size={16} />
                          </button>
                          <button
                            onClick={() => confirmDelete(post.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Hapus Konten"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="p-4">
              {/* Grid View */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getPaginatedPosts().map((post) => (
                  <div key={post.id} className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                    <div className="bg-gray-50 p-3 border-b flex justify-between items-center">
                      <div className="flex items-center">
                        <Instagram size={16} className="text-pink-500 mr-2" />
                        <span className="font-medium truncate max-w-[150px]">{post.title || 'Tanpa Judul'}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        {getContentTypeIcon(post.contentType)}
                        {getStatusIcon(post.status)}
                      </div>
                    </div>
                    <div className="p-4">
                      <div className="text-sm text-gray-600 mb-3 line-clamp-3 h-[4.5em]">
                        {post.caption}
                      </div>
                      <div className="flex flex-wrap gap-1 mb-3">
                        {post.hashtags.slice(0, 3).map((tag, index) => (
                          <span key={index} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            #{tag}
                          </span>
                        ))}
                        {post.hashtags.length > 3 && (
                          <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            +{post.hashtags.length - 3}
                          </span>
                        )}
                      </div>
                      <div className="flex justify-between items-center text-xs text-gray-500">
                        <div>
                          <Calendar size={12} className="inline mr-1" />
                          {post.status === PostStatus.PUBLISHED
                            ? formatDate(post.publishedDate)
                            : formatDate(post.scheduledDate)}
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setSelectedPost(post);
                              setShowPostModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900"
                            title="Lihat Detail"
                          >
                            <Eye size={14} />
                          </button>
                          {onEditPost && (
                            <button
                              onClick={() => onEditPost(post)}
                              className="text-yellow-600 hover:text-yellow-900"
                              title="Edit Konten"
                            >
                              <Edit size={14} />
                            </button>
                          )}
                          {onImproveContent && (
                            <button
                              onClick={() => onImproveContent(post)}
                              className="text-green-600 hover:text-green-900"
                              title="Tingkatkan Konten"
                            >
                              <Sparkles size={14} />
                            </button>
                          )}
                          <button
                            onClick={() => generateImagePrompt(post)}
                            className="text-purple-600 hover:text-purple-900"
                            title="Generate Image Prompt"
                          >
                            <Wand2 size={14} />
                          </button>
                          <button
                            onClick={() => confirmDelete(post.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Hapus Konten"
                          >
                            <Trash2 size={14} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Pagination */}
          {filteredPosts.length > 0 && (
            <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Menampilkan <span className="font-medium">{(currentPage - 1) * postsPerPage + 1}</span> sampai{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * postsPerPage, filteredPosts.length)}
                    </span>{' '}
                    dari <span className="font-medium">{filteredPosts.length}</span> konten
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === 1
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Previous</span>
                      <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>

                    {/* Page numbers */}
                    {Array.from({ length: Math.ceil(filteredPosts.length / postsPerPage) }).map((_, index) => (
                      <button
                        key={index}
                        onClick={() => handlePageChange(index + 1)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          currentPage === index + 1
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {index + 1}
                      </button>
                    ))}

                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === Math.ceil(filteredPosts.length / postsPerPage)}
                      className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                        currentPage === Math.ceil(filteredPosts.length / postsPerPage)
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className="sr-only">Next</span>
                      <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Modal and Dialog Components */}
      {showPostModal && selectedPost && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <div className="flex items-center">
                <Instagram size={20} className="text-pink-500 mr-2" />
                <h3 className="text-lg font-medium">Detail Konten Instagram</h3>
              </div>
              <button
                onClick={() => setShowPostModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>

            <div className="p-4 space-y-4">
              {/* Post preview */}
              <div className="border rounded-lg overflow-hidden mb-4">
                <div className="bg-gray-50 p-3 border-b flex items-center">
                  <Instagram size={16} className="text-pink-500 mr-2" />
                  <span className="font-medium">Preview Instagram Post</span>
                </div>
                <div className="p-4 bg-white">
                  <div className="flex items-center mb-3">
                    <div className="w-8 h-8 rounded-full bg-pink-100 flex items-center justify-center text-pink-500 mr-2">
                      <Instagram size={16} />
                    </div>
                    <div>
                      <div className="font-medium text-sm">chitraparatama</div>
                      <div className="text-xs text-gray-500">PT Chitra Paratama</div>
                    </div>
                  </div>

                  <div className="bg-gray-100 rounded-lg h-48 flex items-center justify-center mb-3">
                    {getContentTypeIcon(selectedPost.contentType)}
                    <span className="ml-2 text-gray-500 text-sm">
                      {selectedPost.contentType === ContentType.IMAGE ? 'Preview Gambar' :
                       selectedPost.contentType === ContentType.VIDEO ? 'Preview Video' :
                       selectedPost.contentType === ContentType.CAROUSEL ? 'Preview Carousel' :
                       selectedPost.contentType === ContentType.REEL ? 'Preview Reel' : 'Preview Story'}
                    </span>
                  </div>

                  <div className="text-sm mb-2 whitespace-pre-line">
                    {selectedPost.caption}
                  </div>

                  <div className="text-xs text-blue-500">
                    {selectedPost.hashtags.map(tag => `#${tag}`).join(' ')}
                  </div>
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-700">Judul</h4>
                  <button
                    onClick={() => copyToClipboard(selectedPost.title || 'Tanpa Judul')}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title="Salin judul"
                  >
                    <Copy size={14} />
                  </button>
                </div>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                  {selectedPost.title || 'Tanpa Judul'}
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-700">Caption</h4>
                  <button
                    onClick={() => copyToClipboard(selectedPost.caption)}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title="Salin caption"
                  >
                    <Copy size={14} />
                  </button>
                </div>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm whitespace-pre-line">
                  {selectedPost.caption}
                </div>
              </div>

              <div>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-700">Hashtag</h4>
                  <button
                    onClick={() => copyToClipboard(selectedPost.hashtags.map(tag => `#${tag}`).join(' '))}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title="Salin hashtag"
                  >
                    <Copy size={14} />
                  </button>
                </div>
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                  <div className="flex flex-wrap gap-1">
                    {selectedPost.hashtags.map((tag, index) => (
                      <span key={index} className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>

              {selectedPost.imageDescription && (
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <h4 className="text-sm font-medium text-gray-700">Deskripsi Gambar</h4>
                    <button
                      onClick={() => copyToClipboard(selectedPost.imageDescription || '')}
                      className="text-blue-600 hover:text-blue-800 p-1"
                      title="Salin deskripsi gambar"
                    >
                      <Copy size={14} />
                    </button>
                  </div>
                  <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {selectedPost.imageDescription}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">Tipe Konten</h4>
                  <div className="flex items-center p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {getContentTypeIcon(selectedPost.contentType)}
                    <span className="ml-2">
                      {selectedPost.contentType.charAt(0).toUpperCase() + selectedPost.contentType.slice(1)}
                    </span>
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">Status</h4>
                  <div className="flex items-center p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {getStatusIcon(selectedPost.status)}
                    <span className="ml-2">{getStatusLabel(selectedPost.status)}</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">Tanggal Dibuat</h4>
                  <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {formatDate(selectedPost.createdAt)}
                  </div>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-1">
                    {selectedPost.status === PostStatus.PUBLISHED ? 'Tanggal Publikasi' : 'Tanggal Terjadwal'}
                  </h4>
                  <div className="p-3 bg-gray-50 border border-gray-200 rounded-md text-sm">
                    {selectedPost.status === PostStatus.PUBLISHED
                      ? formatDate(selectedPost.publishedDate)
                      : formatDate(selectedPost.scheduledDate)}
                  </div>
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex justify-between pt-4 border-t border-gray-200">
                <div className="space-x-2">
                  {selectedPost.status === PostStatus.DRAFT && (
                    <button
                      onClick={() => handleStatusChange(selectedPost, PostStatus.SCHEDULED)}
                      className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                    >
                      <Clock size={16} className="inline mr-1" />
                      Jadwalkan
                    </button>
                  )}
                  {(selectedPost.status === PostStatus.DRAFT || selectedPost.status === PostStatus.SCHEDULED) && (
                    <button
                      onClick={() => handleStatusChange(selectedPost, PostStatus.PUBLISHED)}
                      className="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                    >
                      <CheckCircle size={16} className="inline mr-1" />
                      Publikasikan
                    </button>
                  )}
                  {selectedPost.status !== PostStatus.ARCHIVED && (
                    <button
                      onClick={() => handleStatusChange(selectedPost, PostStatus.ARCHIVED)}
                      className="px-3 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
                    >
                      Arsipkan
                    </button>
                  )}
                </div>
                <div className="space-x-2">
                  {onEditPost && (
                    <button
                      onClick={() => {
                        onEditPost(selectedPost);
                        setShowPostModal(false);
                      }}
                      className="px-3 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                    >
                      <Edit size={16} className="inline mr-1" />
                      Edit
                    </button>
                  )}
                  {onImproveContent && (
                    <button
                      onClick={() => {
                        onImproveContent(selectedPost);
                        setShowPostModal(false);
                      }}
                      className="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                    >
                      <Sparkles size={16} className="inline mr-1" />
                      Tingkatkan Konten
                    </button>
                  )}
                  <button
                    onClick={() => {
                      generateImagePrompt(selectedPost);
                      setShowPostModal(false);
                    }}
                    className="px-3 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
                  >
                    <Wand2 size={16} className="inline mr-1" />
                    Generate Image
                  </button>
                  <button
                    onClick={() => setShowPostModal(false)}
                    className="px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Tutup
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Konfirmasi Hapus Konten</AlertDialogTitle>
            <AlertDialogDescription>
              Apakah Anda yakin ingin menghapus konten ini? Tindakan ini tidak dapat dibatalkan.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Batal</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeletePost} className="bg-red-500 hover:bg-red-600">
              Hapus
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
