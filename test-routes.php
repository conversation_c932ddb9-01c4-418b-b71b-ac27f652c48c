<?php

// Test script untuk mengecek semua routes
$routes = [
    // Dashboard
    '/dashboard',
    
    // Kalkulator & Simulasi
    '/ban-27-bundling',
    '/bundling-calculator',
    '/zero-margin-bundling',
    '/promo-simulation',
    
    // Manajemen Data
    '/products',
    '/customers',
    '/coal-price-data',
    '/template-management',
    '/sales-revenue-2025-data-master',
    '/proposal-builder',
    
    // Analisis & Dashboard
    '/analytics-dashboard',
    '/sales-dashboard',
    '/fleet-analyzer',
    '/customer-analysis',
    '/product-analysis',
    
    // Marketing & Proposal
    '/seasonal-marketing-calendar',
    '/bundling-proposal',
    '/marketing-insights',
    '/content-planning-tools',
    
    // Social Media Marketing
    '/social-media-marketing',
    '/monthly-content-plan',
    '/video-script-generator',
    '/instagram-analysis',
    
    // AI Tools
    '/negotiation-simulator',
    '/whatsapp-chat-analysis',
    '/knowledge-base',
    '/proposal-analyzer',
    '/image-generator',
    '/swot-analysis',
    '/presentation-analyzer',
    
    // Style Guide
    '/style-guide'
];

$baseUrl = 'http://chitra-marketing-tools-laravel.test';
$results = [];

echo "Testing all routes...\n\n";

foreach ($routes as $route) {
    $url = $baseUrl . $route;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $status = '';
    if ($httpCode == 200) {
        $status = '✅ OK';
    } elseif ($httpCode == 302) {
        $status = '🔄 REDIRECT (needs auth)';
    } elseif ($httpCode == 404) {
        $status = '❌ NOT FOUND';
    } elseif ($httpCode == 500) {
        $status = '💥 SERVER ERROR';
    } else {
        $status = "⚠️  HTTP $httpCode";
    }
    
    echo sprintf("%-40s %s\n", $route, $status);
    $results[$route] = $httpCode;
}

echo "\n\nSummary:\n";
$ok = count(array_filter($results, fn($code) => $code == 200));
$redirect = count(array_filter($results, fn($code) => $code == 302));
$notFound = count(array_filter($results, fn($code) => $code == 404));
$error = count(array_filter($results, fn($code) => $code >= 500));

echo "✅ OK: $ok\n";
echo "🔄 Redirect: $redirect\n";
echo "❌ Not Found: $notFound\n";
echo "💥 Errors: $error\n";
echo "Total: " . count($routes) . "\n";
