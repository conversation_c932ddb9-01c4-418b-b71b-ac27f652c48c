/**
 * Service for analyzing customer data using AI
 * Integrates data from Product Management, Fleet Management, and Sales Revenue
 */

import { Customer } from '../types/customer';
import { Product } from '../types';
import { FleetlistItem } from './fleetlistService';
import { SalesRevenueItem } from './salesRevenue2025Service';
import {
  CustomerAnalysisResult,
  CustomerAnalysisRequest,
  IntegratedCustomerData
} from '../types/customerAnalysis';
import {
  findCustomerMatches,
  getAllPossibleCustomerNames,
  normalizeCustomerName
} from './customerMatchingService';
import { getAllCustomers } from './customerService';
import { fetchProducts } from './productService';
import { fetchFleetlist } from './fleetlistService';
import { loadSalesRevenueData } from './salesRevenue2025Service';

// OpenRouter API configuration
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const OPENROUTER_API_KEY = typeof process !== 'undefined' && process.env && process.env.REACT_APP_OPENROUTER_API_KEY
  ? process.env.REACT_APP_OPENROUTER_API_KEY
  : (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.VITE_OPENROUTER_API_KEY
      ? import.meta.env.VITE_OPENROUTER_API_KEY
      : '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');
const MODEL = 'openai/gpt-4.1-nano';
const API_TIMEOUT = 30000; // 30 detik timeout

/**
 * Get integrated customer data from all sources
 */
export async function getIntegratedCustomerData(
  customerName: string,
  similarityThreshold: number = 70
): Promise<IntegratedCustomerData | null> {
  try {
    // Load data from all sources
    const customers = getAllCustomers();
    const products = await fetchProducts();
    const fleetData = await fetchFleetlist();
    const salesData = await loadSalesRevenueData();

    // Find customer matches across all data sources
    const matches = findCustomerMatches(
      customerName,
      customers,
      fleetData,
      salesData,
      similarityThreshold
    );

    if (matches.matchedNames.length === 0) {
      console.log(`No matches found for customer: ${customerName}`);
      return null;
    }

    // Get all matched names
    const matchedNames = matches.matchedNames.map(match => match.name);
    console.log(`Found ${matchedNames.length} matches for customer: ${customerName}`, matchedNames);

    // Find customer in customer database
    const customerMatch = customers.find(c =>
      matchedNames.some(name => normalizeCustomerName(c.name) === normalizeCustomerName(name))
    );

    const customerId = customerMatch?.id || 'unknown';

    // Filter fleet data for this customer
    const customerFleetData = fleetData.filter(item =>
      matchedNames.some(name => normalizeCustomerName(item.customer || '') === normalizeCustomerName(name))
    );

    // Filter sales data for this customer
    const customerSalesData = salesData.filter(item =>
      matchedNames.some(name => normalizeCustomerName(item.customerName) === normalizeCustomerName(name))
    );

    // Get recommended products based on fleet data
    const recommendedProducts = getRecommendedProducts(customerFleetData, products);

    return {
      customerId,
      customerName: customerMatch?.name || customerName,
      matchedNames,
      fleetData: customerFleetData,
      salesData: customerSalesData,
      recommendedProducts
    };
  } catch (error) {
    console.error('Error getting integrated customer data:', error);
    return null;
  }
}

/**
 * Get recommended products based on fleet data
 */
function getRecommendedProducts(fleetData: FleetlistItem[], products: Product[]): Product[] {
  // Extract unique tire sizes from fleet data
  const tireSizes = [...new Set(fleetData.map(item => item.tire_size).filter(Boolean))];

  if (tireSizes.length === 0) return [];

  // Find products that match the tire sizes
  const recommendedProducts: Product[] = [];

  tireSizes.forEach(tireSize => {
    if (!tireSize) return;

    // Normalize the tire size for comparison
    const normalizedTireSize = tireSize.replace(/\s+/g, '').toUpperCase();

    // Find matching products
    const matchingProducts = products.filter(product => {
      const description = product.materialDescription.replace(/\s+/g, '').toUpperCase();
      return description.includes(normalizedTireSize);
    });

    recommendedProducts.push(...matchingProducts);
  });

  // Remove duplicates
  const uniqueProducts = recommendedProducts.filter((product, index, self) =>
    index === self.findIndex(p => p.id === product.id)
  );

  return uniqueProducts;
}

/**
 * Analyze customer data using AI
 */
export async function analyzeCustomerData(
  customerData: IntegratedCustomerData
): Promise<CustomerAnalysisResult> {
  console.log('=== START ANALYZE CUSTOMER DATA ===');
  console.log('Input customer data:', JSON.stringify(customerData, null, 2));
  
  try {
    if (!customerData || !customerData.customerName) {
      console.error('Invalid customer data:', customerData);
      throw new Error('Data pelanggan tidak valid');
    }

    console.log('Memulai analisis data pelanggan:', customerData.customerName);

    // Buat summary ringkas
    const summary = {
      totalRevenue: customerData.salesData.reduce((sum, item) => sum + (item.revenueInDocCurr || 0), 0),
      totalQty: customerData.salesData.reduce((sum, item) => sum + (item.qty || 0), 0),
      trend: (() => {
        if (customerData.salesData.length > 1) {
          const monthly: { [key: string]: number } = {};
          customerData.salesData.forEach(item => {
            const billingDate = item.billingDate || '';
            if (billingDate) {
              const month = billingDate.slice(0, 7);
              if (!monthly[month]) monthly[month] = 0;
              monthly[month] += item.revenueInDocCurr || 0;
            }
          });
          const months = Object.keys(monthly).sort();
          if (months.length > 1) {
            const first = monthly[months[0]];
            const last = monthly[months[months.length - 1]];
            if (last > first) return 'Meningkat';
            else if (last < first) return 'Menurun';
          }
        }
        return 'Stabil';
      })(),
      topProducts: (() => {
        const productMap: { [key: string]: { name: string; revenue: number; qty: number; lastPurchaseDate: string } } = {};
        customerData.salesData.forEach(item => {
          const key = item.materialDescription || 'Unknown';
          if (!productMap[key]) productMap[key] = { name: key, revenue: 0, qty: 0, lastPurchaseDate: '' };
          productMap[key].revenue += item.revenueInDocCurr || 0;
          productMap[key].qty += item.qty || 0;
          const billingDate = item.billingDate || '';
          if (!productMap[key].lastPurchaseDate || new Date(billingDate) > new Date(productMap[key].lastPurchaseDate)) {
            productMap[key].lastPurchaseDate = billingDate;
          }
        });
        return Object.values(productMap).sort((a, b) => b.revenue - a.revenue).slice(0, 10);
      })(),
      fleet: customerData.fleetData.map(item => ({
        model: item.model || '',
        unit_manufacture: item.unit_manufacture || '',
        tire_size: item.tire_size || '',
        unit_qty: item.unit_qty || '',
        totaltire: item.totaltire || ''
      })),
      availableProducts: customerData.recommendedProducts.map(p => ({
        id: p.id || '',
        materialDescription: p.materialDescription || '',
        price: p.price || 0
      }))
    };

    console.log('Summary data:', JSON.stringify(summary, null, 2));

    // Create system prompt for AI
    const systemPrompt = `Anda adalah asisten analisis pelanggan yang ahli untuk perusahaan ban. Tugas Anda adalah menganalisis data pelanggan dari berbagai sumber dan memberikan wawasan yang berharga tentang riwayat pembelian mereka, armada kendaraan mereka saat ini, dan produk apa yang mungkin mereka butuhkan di masa depan.

Berikan analisis dalam format JSON dengan struktur berikut:
1. Analisis riwayat pembelian (produk yang dibeli, kapan, jumlah)
2. Analisis armada saat ini (unit yang dimiliki, jumlah ban)
3. Rekomendasi produk berdasarkan armada dan riwayat pembelian
4. Teks analisis keseluruhan

Analisis Anda harus mencakup:
- Pola pembelian musiman (jika ada)
- Analisis kompetitif berdasarkan data yang tersedia
- Peluang upsell/cross-sell berdasarkan armada dan riwayat pembelian
- Rekomendasi produk yang spesifik dengan alasan yang jelas
- Potensi pendapatan dari rekomendasi produk
- Tren pembelian pelanggan (meningkat, menurun, atau stabil)

Berikan analisis yang mendalam dan wawasan yang dapat ditindaklanjuti. Gunakan data yang tersedia untuk membuat rekomendasi yang spesifik dan relevan. Pastikan analisis Anda komprehensif dan bermanfaat untuk tim penjualan.`;

    // Prompt baru untuk AI
    const userPrompt = `Berikut adalah ringkasan data pelanggan hasil filter dan agregasi:

Nama Pelanggan: ${customerData.customerName}
Variasi Nama: ${customerData.matchedNames.filter((n): n is string => typeof n === 'string' && Boolean(n)).map(name => String(name)).filter(Boolean).join(', ')}

SUMMARY DATA (JSON):
${JSON.stringify(summary, null, 2)}

Tugas Anda:
- Interpretasikan data revenue, tren, dan peluang upsell/cross-sell.
- Bandingkan revenue dengan jumlah unit, sebutkan potensi yang belum tergarap.
- Rekomendasikan produk yang relevan dari daftar produk tersedia.
- Berikan strategi/cara penawaran yang spesifik dan mudah dipahami.
- Tampilkan insight dalam format poin-poin (bullet point), dengan section terpisah:
  1. 💡 Insight Utama
  2. 📈 Peluang Upsell/Cross-sell
  3. 🛠️ Rekomendasi Produk
  4. 🤝 Strategi Penawaran

Gunakan bahasa Indonesia yang mudah dipahami, ringkas, dan actionable untuk tim sales/marketing. Jika data kurang, tetap berikan saran generik yang relevan.`;

    console.log('Preparing API request...');
    
    // Call OpenRouter API dengan timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      console.log('API request timeout triggered');
      controller.abort();
    }, API_TIMEOUT);

    try {
      console.log('Sending API request...');
      const response = await fetch(OPENROUTER_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'HTTP-Referer': 'https://chitraparatama.co.id',
          'X-Title': 'Chitra Marketing Tools - Customer Analysis'
        },
        body: JSON.stringify({
          model: MODEL,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          temperature: 0.3,
          max_tokens: 2000,
          response_format: { type: "json_object" }
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      console.log('API response received:', response.status);

      if (!response.ok) {
        console.error('API Error:', response.status, response.statusText);
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('API response data:', JSON.stringify(data, null, 2));
      
      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        console.error('Invalid API response format:', data);
        throw new Error('Format response API tidak valid');
      }

      // Extract the content from the response
      const analysisContent = data.choices[0].message.content;
      console.log('Analysis content:', analysisContent);

      // Parse the JSON response
      let analysisResult;
      try {
        analysisResult = JSON.parse(analysisContent);
        console.log('Parsed analysis result:', JSON.stringify(analysisResult, null, 2));
      } catch (error) {
        console.error('Error parsing analysis result:', error);
        throw new Error('Gagal memproses hasil analisis');
      }

      console.log('=== END ANALYZE CUSTOMER DATA ===');
      
      // Return the analysis result with customer ID, name, and raw data
      return {
        customerId: customerData.customerId,
        customerName: customerData.customerName,
        salesData: customerData.salesData,
        purchaseHistory: {
          totalPurchases: customerData.salesData.length,
          totalRevenue: customerData.salesData.reduce((sum, item) => sum + (item.revenueInDocCurr || 0), 0),
          firstPurchaseDate: customerData.salesData.length > 0 ? customerData.salesData[0].billingDate : '',
          lastPurchaseDate: customerData.salesData.length > 0 ? customerData.salesData[customerData.salesData.length - 1].billingDate : '',
          frequentlyPurchasedProducts: [],
          purchaseTrend: 'stable'
        },
        fleetAnalysis: {
          totalUnits: customerData.fleetData.reduce((sum, item) => sum + (parseInt(item.unit_qty || '0') || 0), 0),
          totalTires: customerData.fleetData.reduce((sum, item) => sum + (parseInt(item.totaltire || '0') || 0), 0),
          uniqueTireSizes: [...new Set(customerData.fleetData.map(item => item.tire_size || '').filter(Boolean))],
          fleetComposition: []
        },
        productRecommendations: customerData.recommendedProducts.slice(0, 3).map(product => ({
          productName: product.materialDescription || '',
          reason: 'Cocok dengan ukuran ban di armada pelanggan',
          confidence: 80,
          potentialRevenue: (product.price || 0) * 10
        })),
        analysisText: analysisResult.analysisText || (analysisResult.error || 'Tidak ada hasil analisis yang tersedia'),
        ...analysisResult
      };

    } catch (error) {
      clearTimeout(timeoutId);
      console.error('API call error:', error);
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Permintaan ke API timeout setelah 30 detik');
        }
        throw error;
      }
      throw new Error('Terjadi kesalahan saat memanggil API');
    }

  } catch (error) {
    console.error('Error analyzing customer data with AI:', error);
    
    // Return a fallback analysis result with error message
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error details:', errorMessage);
    
    console.log('=== END ANALYZE CUSTOMER DATA WITH ERROR ===');
    
    return {
      customerId: customerData.customerId,
      customerName: customerData.customerName,
      salesData: customerData.salesData,
      purchaseHistory: {
        totalPurchases: customerData.salesData.length,
        totalRevenue: customerData.salesData.reduce((sum, item) => sum + (item.revenueInDocCurr || 0), 0),
        firstPurchaseDate: customerData.salesData.length > 0 ? customerData.salesData[0].billingDate : '',
        lastPurchaseDate: customerData.salesData.length > 0 ? customerData.salesData[customerData.salesData.length - 1].billingDate : '',
        frequentlyPurchasedProducts: [],
        purchaseTrend: 'stable'
      },
      fleetAnalysis: {
        totalUnits: customerData.fleetData.reduce((sum, item) => sum + (parseInt(item.unit_qty || '0') || 0), 0),
        totalTires: customerData.fleetData.reduce((sum, item) => sum + (parseInt(item.totaltire || '0') || 0), 0),
        uniqueTireSizes: [...new Set(customerData.fleetData.map(item => item.tire_size || '').filter(Boolean))],
        fleetComposition: []
      },
      productRecommendations: customerData.recommendedProducts.slice(0, 3).map(product => ({
        productName: product.materialDescription || '',
        reason: 'Cocok dengan ukuran ban di armada pelanggan',
        confidence: 80,
        potentialRevenue: (product.price || 0) * 10
      })),
      analysisText: `Terjadi kesalahan saat menganalisis data: ${errorMessage}. Silakan coba lagi dalam beberapa saat.`
    };
  }
}
