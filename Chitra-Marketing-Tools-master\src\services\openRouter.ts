import axios from 'axios';

// Using the user's preferred API key for OpenRouter
const API_KEY = '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';

// Available models
export const MODELS = {
  GPT_3_5: 'openai/gpt-4.1-nano', // Replaced with GPT-4.1 Nano
  DEEPSEEK: 'openai/gpt-4.1-nano', // Replaced with GPT-4.1 Nano
  GEMINI_FLASH: 'openai/gpt-4.1-nano', // Replaced with GPT-4.1 Nano
  GEMINI_FLASH_LITE: 'openai/gpt-4.1-nano', // Replaced with GPT-4.1 Nano
  CLAUDE: 'openai/gpt-4.1-nano' // GPT-4.1 Nano for detailed analysis
};

export async function generateCopywriting(
  productDetails: string,
  price: number,
  specialFeatures: string,
  model = MODELS.GEMINI_FLASH // Default to Gemini 2.0 Flash
) {
  try {
    console.log(`Generating copywriting with model: ${model}`);

    // Create the appropriate request body based on the model
    let requestBody;

    if (model === MODELS.GEMINI_FLASH) {
      // Format for Gemini 2.0 Flash
      requestBody = {
        model: model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional marketing copywriter who creates compelling WhatsApp messages to promote product bundles. Create messages that are persuasive, concise, and highlight value.'
          },
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Create a compelling WhatsApp marketing message for this product bundle in the EXACT following format:

🚛 *PAKET HEMAT BAN [APPROPRIATE VEHICLE TYPE]!*
Dapatkan [NUMBER] produk premium:
- [PRODUCT 1 with BRAND NAME]
- [PRODUCT 2 with BRAND NAME]
- [PRODUCT 3 with BRAND NAME]
[ADD MORE PRODUCTS IF NEEDED]

💰 Hanya Rp ${price.toLocaleString()}
🔥 Stok terbatas, buruan order sekarang!

[OPTIONAL: Add ONE more sentence about value or special features if needed]

Products to include: ${productDetails}
Special features: ${specialFeatures}

The message must be in Indonesian, follow the exact format above with emojis, bolded title, and bullet points. Keep it concise.`
              }
            ]
          }
        ]
      };
    } else {
      // Standard format for other models
      requestBody = {
        model: model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional marketing copywriter who creates compelling WhatsApp messages to promote product bundles. Create messages that are persuasive, concise, and highlight value.'
          },
          {
            role: 'user',
            content: `Create a compelling WhatsApp marketing message for this product bundle in the EXACT following format:

🚛 *PAKET HEMAT BAN [APPROPRIATE VEHICLE TYPE]!*
Dapatkan [NUMBER] produk premium:
- [PRODUCT 1 with BRAND NAME]
- [PRODUCT 2 with BRAND NAME]
- [PRODUCT 3 with BRAND NAME]
[ADD MORE PRODUCTS IF NEEDED]

💰 Hanya Rp ${price.toLocaleString()}
🔥 Stok terbatas, buruan order sekarang!

[OPTIONAL: Add ONE more sentence about value or special features if needed]

Products to include: ${productDetails}
Special features: ${specialFeatures}

The message must be in Indonesian, follow the exact format above with emojis, bolded title, and bullet points. Keep it concise.`
          }
        ]
      };
    }

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chitraparatama.co.id', // Recommended by OpenRouter
          'X-Title': 'Chitra Marketing Tools' // Recommended by OpenRouter
        }
      }
    );

    console.log('OpenRouter API response:', JSON.stringify(response.data, null, 2));

    // Log the entire response structure to understand the format
    console.log('Response structure:', Object.keys(response.data));

    // Check for different possible response formats
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      console.log('Choices structure:', Object.keys(response.data.choices[0]));

      // Standard OpenAI/OpenRouter format
      if (response.data.choices[0].message && response.data.choices[0].message.content) {
        // Check if content is a string or an array
        const content = response.data.choices[0].message.content;
        if (typeof content === 'string') {
          return content;
        } else if (Array.isArray(content)) {
          // If content is an array, concatenate all text parts
          return content
            .filter((part: any) => part.type === 'text')
            .map((part: any) => part.text)
            .join('\n');
        }
      }

      // Alternative format that might be used
      if (response.data.choices[0].content) {
        if (typeof response.data.choices[0].content === 'string') {
          return response.data.choices[0].content;
        } else if (response.data.choices[0].content.parts) {
          return response.data.choices[0].content.parts
            .filter((part: any) => part.text)
            .map((part: any) => part.text)
            .join('\n');
        }
      }

      // Another possible format
      if (response.data.choices[0].text) {
        return response.data.choices[0].text;
      }
    }

    // Check for Gemini-specific format
    if (response.data && response.data.candidates && response.data.candidates.length > 0) {
      console.log('Candidates structure:', Object.keys(response.data.candidates[0]));

      if (response.data.candidates[0].content && response.data.candidates[0].content.parts) {
        return response.data.candidates[0].content.parts
          .filter((part: any) => part.text)
          .map((part: any) => part.text)
          .join('\n');
      }
    }

    // If we get here, we couldn't find the expected response format
    console.error('Unexpected response format:', response.data);
    return 'Failed to generate copywriting. Unexpected response format.';
  } catch (error: any) {
    console.error('Error generating copywriting with OpenRouter:', error);

    // More detailed error logging
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
      return `Failed to generate copywriting. Server error: ${error.response.status} - ${JSON.stringify(error.response.data)}`;
    } else if (error.request) {
      // The request was made but no response was received
      console.error('Error request:', error.request);
      return 'Failed to generate copywriting. No response received from server.';
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message || 'Unknown error');
      return `Failed to generate copywriting. Error: ${error.message || 'Unknown error'}`;
    }
  }
}
