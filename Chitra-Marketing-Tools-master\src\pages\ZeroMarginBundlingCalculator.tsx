import React, { useState, useEffect } from 'react';
import {
  Calculator, Truck, Sparkles, Loader2, Search, ArrowUpDown, DollarSign,
  BarChart, RefreshCw, AlertTriangle, CheckCircle, ArrowDown, ArrowUp,
  Minus, FileText, Save, AlertCircle, PlusCircle, MinusCircle, Gift
} from 'lucide-react';
import ProductSelectorSingle from '../components/ProductSelectorSingle';
import { BundleItem, Product } from '../types';
import { getProductByMaterialNo, refreshProductCache } from '../services/productPriceService';
import { formatCurrency } from '../utils/pricing';
import { analyzeBundlePrice } from '../services/priceAnalysisService';

interface CalculationResult {
  mainProductPrice: number;
  secondaryProductPrice: number;
  secondaryProductMargin: number;
  secondaryProductQty: number;
  totalCost: number;
  maxSecondaryQty: number;
  mainProductTotal: number;
  secondaryProductTotal: number;
  sellingPrice: number;
  pricePerMainUnit: number;
  recommendedMainQty?: number;
  aiReasoning?: string;
  optimalSecondaryQty?: number;
}

// Predefined main product details
const MAIN_PRODUCT_CODE = '200-047262';
const MAIN_PRODUCT_NAME = '27.00 R 49 XD GRIP B E4T TL **';
const DEFAULT_PRICE = 188100409; // Default price if product not found
const COMPETITOR_PRICE = 195000000; // Competitor price for reference (195 million)

export default function ZeroMarginBundlingCalculator() {
  const [mainProduct, setMainProduct] = useState<Product | null>(null);
  const [secondaryProduct, setSecondaryProduct] = useState<Product | null>(null);
  const [secondaryProductMargin, setSecondaryProductMargin] = useState<number>(20);
  const [calculationResult, setCalculationResult] = useState<CalculationResult | null>(null);
  const [secondaryProductQty, setSecondaryProductQty] = useState<number>(1);
  const [mainProductQty, setMainProductQty] = useState<number>(10);
  const [isCalculating, setIsCalculating] = useState<boolean>(false);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [aiRecommendation, setAiRecommendation] = useState<string | null>(null);

  // Load products on component mount
  useEffect(() => {
    loadMainProduct();

    // Add event listener for product data updates
    window.addEventListener('productDataUpdated', handleProductDataUpdated);

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('productDataUpdated', handleProductDataUpdated);
    };
  }, []);

  // Handle product data updates from other components
  const handleProductDataUpdated = () => {
    console.log('Product data updated, refreshing main product...');
    loadMainProduct();
  };

  // Manually refresh product data
  const handleRefreshProductData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Force refresh the product cache
      await refreshProductCache();
      // Then load the main product with fresh data
      await loadMainProduct();
      setError('Data produk berhasil diperbarui dari Product Management.');
    } catch (err) {
      console.error('Error refreshing product data:', err);
      setError('Gagal memperbarui data produk. Silakan coba lagi.');
    } finally {
      setLoading(false);
    }
  };

  // Load the main product from Product Management
  const loadMainProduct = async () => {
    try {
      setLoading(true);

      // Create a default product to use as fallback
      const defaultProduct: Product = {
        id: 'ban-27-00-r-49-default',
        oldMaterialNo: MAIN_PRODUCT_CODE,
        materialDescription: MAIN_PRODUCT_NAME,
        description: 'Michelin 27.00 R 49 XD GRIP',
        price: DEFAULT_PRICE
      };

      // Use our productPriceService to get the latest product data
      const product = await getProductByMaterialNo(
        MAIN_PRODUCT_CODE,
        '27.00 R 49 XD GRIP',
        defaultProduct
      );

      if (product && product.id !== 'ban-27-00-r-49-default') {
        console.log('Found 27.00 R 49 XD GRIP product:', product);
        setMainProduct(product);
      } else {
        console.warn('27.00 R 49 XD GRIP product not found in Product Management, using default values');
        setError('Produk 27.00 R 49 XD GRIP tidak ditemukan di Product Management. Menggunakan nilai default.');
        setMainProduct(defaultProduct);
      }
    } catch (err) {
      console.error('Error loading main product:', err);
      setError('Failed to load products. Please try again later.');

      // Use default values on error
      setMainProduct({
        id: 'ban-27-00-r-49-default',
        oldMaterialNo: MAIN_PRODUCT_CODE,
        materialDescription: MAIN_PRODUCT_NAME,
        description: 'Michelin 27.00 R 49 XD GRIP',
        price: DEFAULT_PRICE
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle secondary product selection
  const handleSecondaryProductSelect = (product: Product) => {
    setSecondaryProduct(product);
  };

  // Handle secondary product margin change
  const handleSecondaryMarginChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    setSecondaryProductMargin(Math.max(0, value));
  };

  // Handle secondary product quantity change
  const handleSecondaryQtyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    setSecondaryProductQty(Math.max(1, value));
  };

  // Handle main product quantity change
  const handleMainQtyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    setMainProductQty(Math.max(1, value));
  };

  // Calculate the minimum logical quantity of main product based on secondary product quantity
  const calculateRequiredMainQty = async () => {
    if (!mainProduct || !secondaryProduct) return;

    setIsCalculating(true);
    setError(null);

    try {
      // Calculate secondary product price with margin
      const secondaryProductPriceWithMargin = secondaryProduct.price * (1 + (secondaryProductMargin / 100));

      // Calculate total cost of secondary products
      const secondaryProductTotal = secondaryProductQty * secondaryProductPriceWithMargin;

      // Calculate minimum logical quantity of main product using marketing psychology
      // Strategy: Create a bundling offer that appears more valuable than competitor's offer
      // while still maintaining profitability through secondary product margin

      // Calculate the value of the secondary product with margin
      const secondaryProductValue = secondaryProductTotal;

      // Calculate how many main products we would need at competitor's price
      const competitorQty = Math.floor(COMPETITOR_PRICE / mainProduct.price);

      // Base calculation: Start with a quantity that makes our total price competitive
      // We want our bundle to be perceived as better value than competitor's offer
      let baseQty = competitorQty;

      // Calculate the value ratio of secondary product to main product
      const valueRatio = secondaryProductValue / mainProduct.price;

      // Apply marketing psychology:
      // 1. For high-value secondary products, increase main product quantity more aggressively
      //    to create a perception of exceptional value
      // 2. For lower-value secondary products, increase quantity more conservatively
      //    to maintain profitability

      // Calculate additional quantity based on secondary product value
      let additionalQty = 0;

      if (valueRatio >= 3) {
        // High-value secondary product - offer significantly more main products
        // This creates a "wow factor" for customers
        additionalQty = Math.ceil(valueRatio * 0.8);
      } else if (valueRatio >= 1) {
        // Medium-value secondary product - offer moderately more main products
        additionalQty = Math.ceil(valueRatio * 0.6);
      } else {
        // Lower-value secondary product - offer slightly more main products
        additionalQty = Math.ceil(valueRatio * 0.4);
      }

      // Calculate final quantity
      let minLogicalQty = baseQty + additionalQty;

      // Ensure we have at least 1 main product
      minLogicalQty = Math.max(1, minLogicalQty);

      // Marketing psychology: Use "charm pricing" - quantities ending in 9, 7, or 5 are perceived as better deals
      // Round up to nearest number ending in 9, 7, or 5 for psychological impact
      if (minLogicalQty % 10 !== 9 && minLogicalQty % 10 !== 7 && minLogicalQty % 10 !== 5) {
        const remainder = minLogicalQty % 10;
        if (remainder < 5) {
          minLogicalQty = minLogicalQty - remainder + 5; // Round up to nearest 5
        } else if (remainder < 7) {
          minLogicalQty = minLogicalQty - remainder + 7; // Round up to nearest 7
        } else {
          minLogicalQty = minLogicalQty - remainder + 9; // Round up to nearest 9
        }
      }

      // Set the calculated main product quantity
      setMainProductQty(minLogicalQty);

      // Calculate total cost
      const mainProductTotal = minLogicalQty * mainProduct.price;
      const totalCost = mainProductTotal + secondaryProductTotal;

      // Calculate price per main product unit
      const pricePerMainUnit = totalCost / minLogicalQty;

      // Set calculation result
      const result: CalculationResult = {
        mainProductPrice: mainProduct.price,
        secondaryProductPrice: secondaryProduct.price,
        secondaryProductMargin: secondaryProductMargin,
        secondaryProductQty: secondaryProductQty,
        totalCost: totalCost,
        maxSecondaryQty: secondaryProductQty, // We're using the user-specified quantity
        mainProductTotal: mainProductTotal,
        secondaryProductTotal: secondaryProductTotal,
        sellingPrice: totalCost,
        pricePerMainUnit: pricePerMainUnit,
        recommendedMainQty: minLogicalQty
      };

      setCalculationResult(result);

      // Now perform AI analysis to provide insights
      setIsAnalyzing(true);
      try {
        const aiAnalysisResult = await analyzeBundlePrice({
          mainProductName: mainProduct.materialDescription,
          mainProductPrice: mainProduct.price,
          mainProductMargin: 0, // 0% margin for main product
          secondaryProductName: secondaryProduct.materialDescription,
          secondaryProductPrice: secondaryProduct.price,
          secondaryProductMargin: secondaryProductMargin,
          secondaryProductQty: secondaryProductQty,
          minimumMainProductQty: minLogicalQty,
          minimumSellPrice: 0, // Not applicable for this calculator
          maximumSellPrice: 0, // No maximum price constraint
          targetMargin: 0, // 0% margin for main product
          totalCost: totalCost,
          competitorData: null, // No competitor data for this calculator
          fixedSecondaryQty: true, // Signal to AI that secondary quantity is fixed
          findMinimumLogicalQty: true // Signal to AI that we want to find minimum logical quantity
        });

        if (aiAnalysisResult && aiAnalysisResult.recommendedMainQty) {
          // If AI recommends a different quantity, use it
          const aiRecommendedQty = aiAnalysisResult.recommendedMainQty;

          // Recalculate with AI recommended quantity
          const aiMainProductTotal = aiRecommendedQty * mainProduct.price;
          const aiTotalCost = aiMainProductTotal + secondaryProductTotal;
          const aiPricePerMainUnit = aiTotalCost / aiRecommendedQty;

          // Update the result with AI recommendation
          const updatedResult = {
            ...result,
            recommendedMainQty: aiRecommendedQty,
            mainProductTotal: aiMainProductTotal,
            totalCost: aiTotalCost,
            pricePerMainUnit: aiPricePerMainUnit,
            aiReasoning: aiAnalysisResult.reasoning ||
              `AI telah menentukan jumlah ban utama yang optimal untuk menarik minat customer. Dibandingkan dengan kompetitor yang menawarkan ${Math.floor(COMPETITOR_PRICE / mainProduct.price)} unit di harga ${formatCurrency(COMPETITOR_PRICE)}, bundling ini menawarkan ${minLogicalQty} unit (${minLogicalQty - Math.floor(COMPETITOR_PRICE / mainProduct.price)} unit lebih banyak) dengan tambahan ${secondaryProductQty} unit produk bonus. Strategi ini memberikan nilai yang jauh lebih baik bagi customer sambil tetap menguntungkan perusahaan melalui margin pada produk bonus.`
          };

          setCalculationResult(updatedResult);
          setMainProductQty(aiRecommendedQty);
          setAiRecommendation(aiAnalysisResult.reasoning || null);
        } else {
          // Use our calculation but add AI reasoning if available
          const updatedResult = {
            ...result,
            aiReasoning: aiAnalysisResult?.reasoning ||
              `Jumlah ban utama yang optimal telah dihitung untuk menarik minat customer. Dibandingkan dengan kompetitor yang menawarkan ${Math.floor(COMPETITOR_PRICE / mainProduct.price)} unit di harga ${formatCurrency(COMPETITOR_PRICE)}, bundling ini menawarkan ${minLogicalQty} unit (${minLogicalQty - Math.floor(COMPETITOR_PRICE / mainProduct.price)} unit lebih banyak) dengan tambahan ${secondaryProductQty} unit produk bonus. Strategi ini memberikan nilai yang jauh lebih baik bagi customer sambil tetap menguntungkan perusahaan melalui margin pada produk bonus.`
          };

          setCalculationResult(updatedResult);
          setAiRecommendation(aiAnalysisResult?.reasoning || null);
        }
      } catch (aiError) {
        console.error('Error performing AI analysis:', aiError);
        // Don't set an error, just log it - the basic calculation still works
      } finally {
        setIsAnalyzing(false);
      }
    } catch (err) {
      console.error('Error calculating bundling:', err);
      setError('Gagal menghitung bundling. Silakan periksa input Anda dan coba lagi.');
    } finally {
      setIsCalculating(false);
    }
  };

  // Format currency for display
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Gift className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Ban 27.00R49 XD GRIP 0 Margin + Bonus</h1>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Product Section */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium">Ban Utama (0% Margin)</h2>
              <button
                onClick={handleRefreshProductData}
                className="flex items-center text-sm text-green-600 hover:text-green-800 px-2 py-1 border border-green-200 rounded"
                disabled={loading}
                title="Refresh product data from Product Management"
              >
                {loading ? (
                  <Loader2 size={16} className="mr-1 animate-spin" />
                ) : (
                  <RefreshCw size={16} className="mr-1" />
                )}
                {loading ? 'Updating...' : 'Update Price'}
              </button>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
                <span className="ml-2 text-gray-600">Loading products...</span>
              </div>
            ) : (
              <>
                {mainProduct && (
                  <div className="space-y-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="font-medium text-blue-800">{mainProduct.materialDescription}</div>
                      <div className="text-sm text-blue-600">Code: {mainProduct.oldMaterialNo}</div>
                      <div className="text-lg font-bold text-blue-900 mt-1">{formatCurrency(mainProduct.price)}</div>
                      <div className="mt-2 text-xs text-blue-700">
                        <span className="font-medium">Margin:</span> 0%
                      </div>
                      <div className="mt-2 text-xs text-red-600 font-medium">
                        Harga per unit tidak boleh di bawah harga pokok
                      </div>
                    </div>

                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-800 font-medium">
                        Jumlah ban utama akan dihitung secara otomatis
                      </p>
                      <p className="text-xs text-yellow-700 mt-1">
                        Jumlah minimum ban 27.00R49 yang logis akan dihitung berdasarkan jumlah produk bonus
                      </p>
                      {calculationResult && (
                        <div className="mt-2 bg-white p-2 rounded border border-yellow-200">
                          <p className="text-sm font-medium text-yellow-900">
                            Jumlah Ban: {calculationResult.recommendedMainQty || mainProductQty} unit
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Secondary Product Section */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-medium mb-4">Produk Bonus</h2>

            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
              </div>
            ) : (
              <div className="space-y-4">
                <ProductSelectorSingle
                  onProductSelect={handleSecondaryProductSelect}
                  selectedProduct={secondaryProduct}
                  label="Pilih Produk Bonus"
                />

                {secondaryProduct && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Margin Produk Bonus (%)
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={secondaryProductMargin}
                        onChange={handleSecondaryMarginChange}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Jumlah Produk Bonus
                      </label>
                      <input
                        type="number"
                        min="1"
                        value={secondaryProductQty}
                        onChange={handleSecondaryQtyChange}
                        className="w-full p-2 border rounded-md"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Masukkan jumlah produk bonus yang diinginkan
                      </p>
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Calculation Button and Results */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h2 className="text-lg font-medium mb-4">Hitung Bundling</h2>

            <div className="space-y-4">
              <button
                onClick={calculateRequiredMainQty}
                disabled={!mainProduct || !secondaryProduct || isCalculating || isAnalyzing}
                className={`w-full py-2 px-4 rounded-md text-white font-medium flex items-center justify-center ${
                  !mainProduct || !secondaryProduct || isCalculating || isAnalyzing
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {isCalculating || isAnalyzing ? (
                  <>
                    <Loader2 className="animate-spin mr-2 h-4 w-4" />
                    {isCalculating ? "Menghitung..." : "Menganalisis dengan AI..."}
                  </>
                ) : (
                  <>
                    <Calculator className="mr-2 h-4 w-4" />
                    Hitung Jumlah Ban Minimum
                  </>
                )}
              </button>

              <div className="text-sm text-gray-600 italic">
                Menghitung jumlah ban 27.00R49 yang menarik bagi customer dibandingkan harga kompetitor
              </div>

              <div className="text-xs text-blue-600 mt-2">
                Harga kompetitor: {formatCurrency(COMPETITOR_PRICE)} • Semakin besar nilai produk bonus, semakin banyak ban utama
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Calculation Results */}
      {calculationResult && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-medium mb-4">Hasil Perhitungan</h2>

          <div className="space-y-6">
            {/* AI Recommendation Section */}
            {calculationResult.aiReasoning && (
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-6">
                <div className="flex items-center mb-2">
                  <Sparkles className="h-5 w-5 text-blue-600 mr-2" />
                  <h3 className="text-md font-medium text-blue-800">Rekomendasi AI</h3>
                </div>
                <p className="text-sm text-blue-700 mb-3">
                  {calculationResult.aiReasoning}
                </p>

                {calculationResult.recommendedMainQty && calculationResult.recommendedMainQty !== mainProductQty && (
                  <div className="bg-white p-3 rounded border border-blue-200 text-sm">
                    <p className="font-medium text-blue-800">Rekomendasi Jumlah Ban Utama: {calculationResult.recommendedMainQty} unit</p>
                    <p className="text-xs text-blue-600 mt-1">
                      Jumlah ini telah diperbarui dalam perhitungan
                    </p>
                  </div>
                )}
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-amber-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-amber-800">Jumlah Ban 27.00R49</h3>
                <p className="text-2xl font-bold text-amber-900">
                  {calculationResult.recommendedMainQty || mainProductQty} unit
                </p>
                <p className="text-xs text-amber-700 mt-1">
                  {mainProduct?.materialDescription}
                </p>
                <div className="mt-2 text-xs bg-amber-100 text-amber-800 p-1 rounded">
                  Total nilai: {formatCurrency(calculationResult.mainProductTotal)} (0% margin)
                </div>
                <div className="mt-1 text-xs bg-green-100 text-green-800 p-1 rounded">
                  Kompetitor: {Math.floor(COMPETITOR_PRICE / (mainProduct?.price || 1))} unit di harga {formatCurrency(COMPETITOR_PRICE)}
                </div>
                <div className="mt-1 text-xs bg-blue-100 text-blue-800 p-1 rounded font-medium">
                  Keuntungan: +{(calculationResult.recommendedMainQty || mainProductQty) - Math.floor(COMPETITOR_PRICE / (mainProduct?.price || 1))} unit lebih banyak!
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-blue-800">Total Harga Bundling</h3>
                <p className="text-2xl font-bold text-blue-900">
                  {formatCurrency(calculationResult.totalCost)}
                </p>
                <div className="flex justify-between text-xs text-blue-700 mt-1">
                  <span>Ban Utama: {formatCurrency(calculationResult.mainProductTotal)}</span>
                  <span>Bonus: {formatCurrency(calculationResult.secondaryProductTotal)}</span>
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-purple-800">Harga Per Ban Utama</h3>
                <p className="text-2xl font-bold text-purple-900">
                  {formatCurrency(calculationResult.pricePerMainUnit)}
                </p>
                <p className="text-xs text-purple-700 mt-1">
                  Termasuk {calculationResult.secondaryProductQty} unit produk bonus
                </p>
                {calculationResult.pricePerMainUnit <= mainProduct.price && (
                  <div className="mt-2 text-xs text-red-600 bg-red-50 p-1 rounded">
                    Harga per unit lebih rendah dari harga pokok!
                  </div>
                )}
              </div>
            </div>

            <div className="border-t pt-4">
              <h3 className="font-medium text-gray-800 mb-3">Rincian Perhitungan</h3>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-gray-700">Ban Utama</h4>
                    <div className="mt-2 space-y-1">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Produk:</span>
                        <span className="text-sm font-medium">{mainProduct?.materialDescription}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Harga Satuan:</span>
                        <span className="text-sm font-medium">{formatCurrency(mainProduct?.price || 0)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Jumlah:</span>
                        <span className="text-sm font-medium">{calculationResult.recommendedMainQty || mainProductQty} unit</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Margin:</span>
                        <span className="text-sm font-medium">0%</span>
                      </div>
                      <div className="flex justify-between border-t pt-1 mt-1">
                        <span className="text-sm font-medium text-gray-700">Total:</span>
                        <span className="text-sm font-bold">{formatCurrency(calculationResult.mainProductTotal)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-gray-700">Produk Bonus</h4>
                    <div className="mt-2 space-y-1">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Produk:</span>
                        <span className="text-sm font-medium">{secondaryProduct?.materialDescription}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Harga Dasar:</span>
                        <span className="text-sm font-medium">{formatCurrency(secondaryProduct?.price || 0)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Margin:</span>
                        <span className="text-sm font-medium">{secondaryProductMargin}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Harga dengan Margin:</span>
                        <span className="text-sm font-medium">
                          {formatCurrency((secondaryProduct?.price || 0) * (1 + secondaryProductMargin / 100))}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Jumlah Maksimum:</span>
                        <span className="text-sm font-medium">{calculationResult.maxSecondaryQty} unit</span>
                      </div>
                      <div className="flex justify-between border-t pt-1 mt-1">
                        <span className="text-sm font-medium text-gray-700">Total:</span>
                        <span className="text-sm font-bold">{formatCurrency(calculationResult.secondaryProductTotal)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
