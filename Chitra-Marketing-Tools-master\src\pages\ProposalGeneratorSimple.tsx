import React, { useState } from 'react';
import { FileText, Upload, Download, Loader2 } from 'lucide-react';

// Define simple types
type ProposalType = 'consignment' | 'bundling' | 'trade-in' | string;

interface ProposalTemplate {
  name: string;
  file: File | null;
}

const ProposalGeneratorSimple: React.FC = () => {
  // State
  const [selectedType, setSelectedType] = useState<ProposalType>('consignment');
  const [template, setTemplate] = useState<ProposalTemplate | null>(null);
  const [formData, setFormData] = useState<Record<string, string>>({
    customerName: '',
    customerAddress: '',
    proposalDate: new Date().toISOString().split('T')[0],
    validUntil: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split('T')[0],
    productName: '',
    productDescription: '',
    productPrice: '',
    productQuantity: '',
    termsAndConditions: 'Payment Terms: 30 days after Date Invoice\nStock: Balikpapan\nDDP: Site\nExclude Tax',
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedFileUrl, setGeneratedFileUrl] = useState<string | null>(null);

  // Handle proposal type selection
  const handleTypeChange = (type: ProposalType) => {
    setSelectedType(type);
  };

  // Handle template upload
  const handleTemplateUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    setTemplate({
      name: file.name,
      file: file
    });
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle generate proposal
  const handleGenerateProposal = async () => {
    try {
      setIsGenerating(true);
      
      // Simulate proposal generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, we would generate the proposal here
      // For now, just set a dummy URL
      setGeneratedFileUrl('#');
      
      alert('Proposal berhasil dibuat! (Simulasi)');
    } catch (error) {
      console.error('Error generating proposal:', error);
      alert('Gagal membuat proposal. Silakan coba lagi.');
    } finally {
      setIsGenerating(false);
    }
  };

  // Proposal type options
  const proposalTypes = [
    { id: 'consignment', name: 'Proposal Konsinyasi', description: 'Proposal untuk penawaran produk dengan sistem konsinyasi' },
    { id: 'bundling', name: 'Proposal Bundling', description: 'Proposal untuk penawaran produk dengan sistem bundling' },
    { id: 'trade-in', name: 'Proposal Trade-In', description: 'Proposal untuk penawaran produk dengan sistem trade-in' }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold flex items-center">
          <FileText className="mr-2 text-blue-600" />
          Proposal Generator (Simple)
        </h1>
      </div>

      {/* Proposal Type Selection */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold mb-4">Pilih Jenis Proposal</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {proposalTypes.map(type => (
            <div
              key={type.id}
              className={`border rounded-lg p-4 cursor-pointer transition-all ${
                selectedType === type.id
                  ? 'border-blue-500 bg-blue-50 shadow-sm'
                  : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
              }`}
              onClick={() => handleTypeChange(type.id)}
            >
              <h3 className="font-medium text-gray-900">{type.name}</h3>
              <p className="text-sm text-gray-500 mt-1">{type.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Template Upload */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold mb-4">Upload Template</h2>
        <p className="text-gray-600 mb-4">
          Unggah file DOCX untuk digunakan sebagai template. Template harus berisi placeholder dalam format {{NamaField}}.
        </p>

        {!template ? (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              type="file"
              id="template-upload"
              onChange={handleTemplateUpload}
              className="hidden"
              accept=".docx"
            />

            <button
              type="button"
              onClick={() => document.getElementById('template-upload')?.click()}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <Upload size={16} className="mr-2" />
              Unggah Template DOCX
            </button>

            <p className="mt-2 text-sm text-gray-500">
              Unggah file DOCX dengan placeholder dalam format {{NamaField}}
            </p>
          </div>
        ) : (
          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <FileText size={24} className="text-blue-500 mr-2" />
                <div>
                  <h3 className="font-medium text-gray-900">{template.name}</h3>
                </div>
              </div>

              <button
                type="button"
                onClick={() => setTemplate(null)}
                className="p-1 text-gray-500 hover:text-red-500"
                title="Hapus template"
              >
                ×
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Form Data */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold mb-4">Data Proposal</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-1">
              Nama Customer <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="customerName"
              name="customerName"
              value={formData.customerName}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="customerAddress" className="block text-sm font-medium text-gray-700 mb-1">
              Alamat Customer
            </label>
            <input
              type="text"
              id="customerAddress"
              name="customerAddress"
              value={formData.customerAddress}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label htmlFor="proposalDate" className="block text-sm font-medium text-gray-700 mb-1">
              Tanggal Proposal <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              id="proposalDate"
              name="proposalDate"
              value={formData.proposalDate}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="validUntil" className="block text-sm font-medium text-gray-700 mb-1">
              Berlaku Hingga <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              id="validUntil"
              name="validUntil"
              value={formData.validUntil}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="productName" className="block text-sm font-medium text-gray-700 mb-1">
              Nama Produk <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="productName"
              name="productName"
              value={formData.productName}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="productDescription" className="block text-sm font-medium text-gray-700 mb-1">
              Deskripsi Produk
            </label>
            <input
              type="text"
              id="productDescription"
              name="productDescription"
              value={formData.productDescription}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label htmlFor="productPrice" className="block text-sm font-medium text-gray-700 mb-1">
              Harga Produk <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="productPrice"
              name="productPrice"
              value={formData.productPrice}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="productQuantity" className="block text-sm font-medium text-gray-700 mb-1">
              Jumlah <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="productQuantity"
              name="productQuantity"
              value={formData.productQuantity}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div className="md:col-span-2">
            <label htmlFor="termsAndConditions" className="block text-sm font-medium text-gray-700 mb-1">
              Syarat dan Ketentuan
            </label>
            <textarea
              id="termsAndConditions"
              name="termsAndConditions"
              value={formData.termsAndConditions}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Generate Button */}
      <div className="flex justify-end">
        <button
          type="button"
          onClick={handleGenerateProposal}
          disabled={isGenerating || !template || !formData.customerName || !formData.productName}
          className={`inline-flex items-center px-4 py-2 rounded-md ${
            isGenerating || !template || !formData.customerName || !formData.productName
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isGenerating ? (
            <>
              <Loader2 size={16} className="mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Download size={16} className="mr-2" />
              Generate Proposal
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ProposalGeneratorSimple;
