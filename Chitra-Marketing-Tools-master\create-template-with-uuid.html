<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Template with Specific UUID</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2 {
            color: #333;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .container {
            margin-top: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        input, select {
            padding: 8px;
            width: 100%;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        label {
            display: block;
            margin-top: 10px;
            font-weight: bold;
        }
        .form-group {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>Create Template with Specific UUID</h1>
    
    <div class="container">
        <p>This tool allows you to create a template with a specific UUID in localStorage.</p>
        
        <div class="form-group">
            <label for="uuid">UUID:</label>
            <input type="text" id="uuid" value="2a08939d-dfb6-4b63-b0da-4ea510947b7b">
        </div>
        
        <div class="form-group">
            <label for="name">Template Name:</label>
            <input type="text" id="name" placeholder="Enter template name">
        </div>
        
        <div class="form-group">
            <label for="type">Template Type:</label>
            <select id="type">
                <option value="bundling">Bundling</option>
                <option value="consignment">Konsinyasi</option>
                <option value="trade-in">Trade-In</option>
                <option value="performance-guarantee">Performance Guarantee</option>
                <option value="performance-warranty">Performance Warranty</option>
                <option value="first-michelin">First Michelin</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="variables">Variables (comma-separated):</label>
            <input type="text" id="variables" placeholder="e.g., customerName,proposalTitle,validUntil">
        </div>
        
        <button onclick="createTemplate()">Create Template</button>
        
        <h2>Results:</h2>
        <pre id="results">Fill in the form and click 'Create Template'.</pre>
    </div>

    <script>
        const TEMPLATES_STORAGE_KEY = 'chitra_templates';

        // Get templates from localStorage
        function getAllTemplates() {
            const templatesJson = localStorage.getItem(TEMPLATES_STORAGE_KEY);
            if (!templatesJson) {
                return [];
            }

            try {
                return JSON.parse(templatesJson);
            } catch (error) {
                console.error('Error parsing templates from localStorage:', error);
                return [];
            }
        }

        // Create a template with specific UUID
        function createTemplate() {
            const uuid = document.getElementById('uuid').value.trim();
            const name = document.getElementById('name').value.trim();
            const type = document.getElementById('type').value;
            const variablesInput = document.getElementById('variables').value.trim();
            
            const resultsElement = document.getElementById('results');
            
            if (!uuid || !name) {
                resultsElement.textContent = "UUID and name are required!";
                return;
            }
            
            // Parse variables
            const detectedVariables = variablesInput ? 
                variablesInput.split(',').map(v => v.trim()).filter(v => v) : 
                [];
            
            // Get existing templates
            const templates = getAllTemplates();
            
            // Check if template with this UUID already exists
            const existingIndex = templates.findIndex(t => t.id === uuid);
            
            // Create new template object
            const newTemplate = {
                id: uuid,
                name: name,
                type: type,
                file: null,
                detectedVariables: detectedVariables,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            // Either update existing or add new
            if (existingIndex >= 0) {
                templates[existingIndex] = newTemplate;
                resultsElement.textContent = `Updated existing template with UUID: ${uuid}`;
            } else {
                templates.push(newTemplate);
                resultsElement.textContent = `Created new template with UUID: ${uuid}`;
            }
            
            // Save to localStorage
            localStorage.setItem(TEMPLATES_STORAGE_KEY, JSON.stringify(templates));
            
            // Show template details
            resultsElement.textContent += `\n\nTemplate details:\n${JSON.stringify(newTemplate, null, 2)}`;
        }
    </script>
</body>
</html>
