import React, { useState } from 'react';
import KnowledgeBaseManager from '../components/KnowledgeBaseManager';
import CompanyDataForm from '../components/CompanyDataForm';
import { Database, Building, BookOpen } from 'lucide-react';

export default function KnowledgeBasePage() {
  const [activeTab, setActiveTab] = useState<'knowledge' | 'company'>('knowledge');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold flex items-center">
          <Database className="mr-2 text-blue-600" />
          Knowledge Base
        </h1>
        <p className="text-gray-600 mt-1">
          Kelola basis pengetahuan untuk meningkatkan kualitas negosiasi dan komunikasi dengan pelanggan.
        </p>
      </div>

      {/* Tabs */}
      <div className="flex border-b mb-6">
        <button
          className={`flex items-center px-4 py-2 font-medium ${
            activeTab === 'knowledge'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-600 hover:text-blue-600'
          }`}
          onClick={() => setActiveTab('knowledge')}
        >
          <BookOpen size={16} className="mr-2" />
          Knowledge Base
        </button>

        <button
          className={`flex items-center px-4 py-2 font-medium ${
            activeTab === 'company'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-600 hover:text-blue-600'
          }`}
          onClick={() => setActiveTab('company')}
        >
          <Building size={16} className="mr-2" />
          Data Perusahaan Umum
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'knowledge' ? (
        <KnowledgeBaseManager />
      ) : (
        <CompanyDataForm />
      )}
    </div>
  );
}
