import React, { useState, useRef } from 'react';
import { FileText, Upload, Download, Check } from 'lucide-react';

// Tipe data untuk jenis proposal
type ProposalType = 'bundling' | 'consignment' | 'trade-in';

// Interface untuk data form
interface FormData {
  customerName: string;
  customerAddress: string;
  contactPerson: string;
  phoneNumber: string;
  email: string;
  date: string;
  validUntil: string;
  // Fields khusus untuk jenis proposal tertentu
  productName?: string;
  productDescription?: string;
  productPrice?: number;
  productQuantity?: number;
  secondaryProductName?: string;
  secondaryProductQuantity?: number;
  tradeInProduct?: string;
  tradeInValue?: number;
  notes?: string;
}

const ProposalPage: React.FC = () => {
  // State untuk jenis proposal yang dipilih
  const [proposalType, setProposalType] = useState<ProposalType>('bundling');

  // State untuk file template
  const [templateFile, setTemplateFile] = useState<File | null>(null);

  // State untuk data form
  const [formData, setFormData] = useState<FormData>({
    customerName: '',
    customerAddress: '',
    contactPerson: '',
    phoneNumber: '',
    email: '',
    date: new Date().toISOString().split('T')[0],
    validUntil: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split('T')[0],
    productName: '',
    productDescription: '',
    productPrice: 0,
    productQuantity: 1,
    secondaryProductName: '',
    secondaryProductQuantity: 1,
    tradeInProduct: '',
    tradeInValue: 0,
    notes: ''
  });

  // State untuk status generasi proposal
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);

  // Ref untuk input file
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handler untuk perubahan jenis proposal
  const handleProposalTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setProposalType(e.target.value as ProposalType);
  };

  // Handler untuk upload file template
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setTemplateFile(e.target.files[0]);
    }
  };

  // Handler untuk perubahan data form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handler untuk submit form
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsGenerating(true);

    // Simulasi proses generasi proposal
    setTimeout(() => {
      setIsGenerating(false);
      setIsSuccess(true);

      // Reset status sukses setelah 3 detik
      setTimeout(() => {
        setIsSuccess(false);
      }, 3000);
    }, 1500);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold flex items-center">
          <FileText className="mr-2 text-blue-600" />
          Proposal Generator
        </h1>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold mb-4">Pilih Jenis Proposal</h2>
        <select
          value={proposalType}
          onChange={handleProposalTypeChange}
          className="w-full md:w-1/3 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="bundling">Proposal Bundling</option>
          <option value="consignment">Proposal Konsinyasi</option>
          <option value="trade-in">Proposal Trade-In</option>
        </select>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold mb-4">Upload Template</h2>
        <p className="text-gray-600 mb-4">
          Upload file DOCX yang berisi tag seperti {{CustomerName}}, {{ProductName}}, dsb.
        </p>

        <div className="flex items-center space-x-3">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileUpload}
            accept=".docx"
            className="hidden"
          />

          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 flex items-center"
          >
            <Upload className="mr-2 h-4 w-4" />
            Pilih File
          </button>

          {templateFile && (
            <span className="text-sm text-gray-600">
              {templateFile.name}
            </span>
          )}
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h2 className="text-lg font-semibold mb-4">Data Pelanggan</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-1">
                Nama Pelanggan <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="customerName"
                name="customerName"
                value={formData.customerName}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            <div>
              <label htmlFor="customerAddress" className="block text-sm font-medium text-gray-700 mb-1">
                Alamat
              </label>
              <input
                type="text"
                id="customerAddress"
                name="customerAddress"
                value={formData.customerAddress}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="contactPerson" className="block text-sm font-medium text-gray-700 mb-1">
                Contact Person
              </label>
              <input
                type="text"
                id="contactPerson"
                name="contactPerson"
                value={formData.contactPerson}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                Nomor Telepon
              </label>
              <input
                type="text"
                id="phoneNumber"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                Tanggal <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>

            <div>
              <label htmlFor="validUntil" className="block text-sm font-medium text-gray-700 mb-1">
                Berlaku Hingga <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="validUntil"
                name="validUntil"
                value={formData.validUntil}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
          </div>
        </div>

        {/* Form khusus untuk jenis proposal Bundling */}
        {proposalType === 'bundling' && (
          <div className="bg-white p-6 rounded-lg shadow-sm border mt-4">
            <h2 className="text-lg font-semibold mb-4">Data Produk Bundling</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="productName" className="block text-sm font-medium text-gray-700 mb-1">
                  Nama Produk Utama <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="productName"
                  name="productName"
                  value={formData.productName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="productDescription" className="block text-sm font-medium text-gray-700 mb-1">
                  Deskripsi Produk
                </label>
                <input
                  type="text"
                  id="productDescription"
                  name="productDescription"
                  value={formData.productDescription}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label htmlFor="productPrice" className="block text-sm font-medium text-gray-700 mb-1">
                  Harga Produk <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  id="productPrice"
                  name="productPrice"
                  value={formData.productPrice}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="productQuantity" className="block text-sm font-medium text-gray-700 mb-1">
                  Jumlah <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  id="productQuantity"
                  name="productQuantity"
                  value={formData.productQuantity}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                  min="1"
                />
              </div>

              <div>
                <label htmlFor="secondaryProductName" className="block text-sm font-medium text-gray-700 mb-1">
                  Nama Produk Sekunder
                </label>
                <input
                  type="text"
                  id="secondaryProductName"
                  name="secondaryProductName"
                  value={formData.secondaryProductName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label htmlFor="secondaryProductQuantity" className="block text-sm font-medium text-gray-700 mb-1">
                  Jumlah Produk Sekunder
                </label>
                <input
                  type="number"
                  id="secondaryProductQuantity"
                  name="secondaryProductQuantity"
                  value={formData.secondaryProductQuantity}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  min="1"
                />
              </div>
            </div>
          </div>
        )}

        {/* Form khusus untuk jenis proposal Konsinyasi */}
        {proposalType === 'consignment' && (
          <div className="bg-white p-6 rounded-lg shadow-sm border mt-4">
            <h2 className="text-lg font-semibold mb-4">Data Konsinyasi</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="productName" className="block text-sm font-medium text-gray-700 mb-1">
                  Nama Produk <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="productName"
                  name="productName"
                  value={formData.productName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="productDescription" className="block text-sm font-medium text-gray-700 mb-1">
                  Deskripsi Produk
                </label>
                <input
                  type="text"
                  id="productDescription"
                  name="productDescription"
                  value={formData.productDescription}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label htmlFor="productPrice" className="block text-sm font-medium text-gray-700 mb-1">
                  Harga Produk <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  id="productPrice"
                  name="productPrice"
                  value={formData.productPrice}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="productQuantity" className="block text-sm font-medium text-gray-700 mb-1">
                  Jumlah <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  id="productQuantity"
                  name="productQuantity"
                  value={formData.productQuantity}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                  min="1"
                />
              </div>
            </div>
          </div>
        )}

        {/* Form khusus untuk jenis proposal Trade-In */}
        {proposalType === 'trade-in' && (
          <div className="bg-white p-6 rounded-lg shadow-sm border mt-4">
            <h2 className="text-lg font-semibold mb-4">Data Trade-In</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="productName" className="block text-sm font-medium text-gray-700 mb-1">
                  Nama Produk Baru <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="productName"
                  name="productName"
                  value={formData.productName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="productPrice" className="block text-sm font-medium text-gray-700 mb-1">
                  Harga Produk Baru <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  id="productPrice"
                  name="productPrice"
                  value={formData.productPrice}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="tradeInProduct" className="block text-sm font-medium text-gray-700 mb-1">
                  Produk Trade-In <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="tradeInProduct"
                  name="tradeInProduct"
                  value={formData.tradeInProduct}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="tradeInValue" className="block text-sm font-medium text-gray-700 mb-1">
                  Nilai Trade-In <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  id="tradeInValue"
                  name="tradeInValue"
                  value={formData.tradeInValue}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>
          </div>
        )}

        <div className="bg-white p-6 rounded-lg shadow-sm border mt-4">
          <h2 className="text-lg font-semibold mb-4">Catatan Tambahan</h2>

          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Catatan
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <button
            type="submit"
            disabled={isGenerating || !templateFile || !formData.customerName}
            className={`px-6 py-3 rounded-md flex items-center ${
              isGenerating || !templateFile || !formData.customerName
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-white mr-2" />
                Generating...
              </>
            ) : isSuccess ? (
              <>
                <Check className="mr-2 h-4 w-4" />
                Berhasil!
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Generate Proposal
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProposalPage;
