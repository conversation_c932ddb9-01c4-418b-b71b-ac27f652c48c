import axios from 'axios';

// OpenRouter API key - using the same key as in the Bundle Pricing feature
const OPENROUTER_API_KEY = '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';

// Available models
const MODELS = {
  GPT_3_5: 'openai/gpt-4.1-nano',
  DEEPSEEK: 'openai/gpt-4.1-nano',
  GEMINI_FLASH_LITE: 'openai/gpt-4.1-nano'
};

/**
 * Generate an email offer for a promotion
 */
export async function generatePromoEmailOffer(
  promoName: string,
  promoType: string,
  productDetails: string[],
  discount: string,
  normalPrice: string,
  promoPrice: string,
  region: string,
  startDate: string,
  endDate: string,
  customerName?: string,
  customerCompany?: string,
  modelType: string = 'gemini-flash-lite'
) {
  switch (modelType) {
    case 'deepseek':
      return generateDeepSeekEmailOffer(
        promoName, promoType, productDetails, discount, normalPrice, promoPrice,
        region, startDate, endDate, customerName, customerCompany
      );
    case 'gemini-flash-lite':
      return generateGeminiFlashLiteEmailOffer(
        promoName, promoType, productDetails, discount, normalPrice, promoPrice,
        region, startDate, endDate, customerName, customerCompany
      );
    default:
      return generateOpenRouterEmailOffer(
        promoName, promoType, productDetails, discount, normalPrice, promoPrice,
        region, startDate, endDate, customerName, customerCompany
      );
  }
}

/**
 * Generate an email offer using DeepSeek model
 */
async function generateDeepSeekEmailOffer(
  promoName: string,
  promoType: string,
  productDetails: string[],
  discount: string,
  normalPrice: string,
  promoPrice: string,
  region: string,
  startDate: string,
  endDate: string,
  customerName?: string,
  customerCompany?: string
) {
  try {
    console.log('Generating email offer with DeepSeek Chat API');

    const requestBody = {
      model: MODELS.DEEPSEEK,
      messages: [
        {
          role: 'system',
          content: 'You are a professional sales representative creating formal email offers for potential clients. Your writing is professional, persuasive, and well-structured.'
        },
        {
          role: 'user',
          content: `Create a PROFESSIONAL and FORMAL email offer in Indonesian for this promotion:

Promo Name: ${promoName}
Promo Type: ${promoType}
Products: ${productDetails.join(', ')}
Discount: ${discount}
Normal Price: ${normalPrice}
Promo Price: ${promoPrice}
Region: ${region || 'Nasional'}
Period: ${new Date(startDate).toLocaleDateString('id-ID')} - ${new Date(endDate).toLocaleDateString('id-ID')}
${customerName ? 'Customer Name: ' + customerName : ''}
${customerCompany ? 'Company: ' + customerCompany : ''}

The email should be formal but warm, professional but not cold, and persuasive without being pushy. Use proper business Indonesian language throughout.`
        }
      ]
    };

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chitraparatama.co.id',
          'X-Title': 'Chitra Marketing Tools'
        }
      }
    );

    console.log('DeepSeek API response:', JSON.stringify(response.data, null, 2));

    if (response.data && response.data.choices && response.data.choices.length > 0) {
      if (response.data.choices[0].message && response.data.choices[0].message.content) {
        return response.data.choices[0].message.content;
      }

      // Alternative formats
      if (response.data.choices[0].content) {
        if (typeof response.data.choices[0].content === 'string') {
          return response.data.choices[0].content;
        } else if (response.data.choices[0].content.parts) {
          return response.data.choices[0].content.parts
            .filter((part: any) => part.text)
            .map((part: any) => part.text)
            .join('\n');
        }
      }

      if (response.data.choices[0].text) {
        return response.data.choices[0].text;
      }
    }

    console.error('Unexpected response format:', response.data);
    return getFallbackEmail(promoName, promoType, productDetails, discount, normalPrice, promoPrice, customerName);
  } catch (error) {
    console.error('Error generating email offer with DeepSeek API:', error);
    return getFallbackEmail(promoName, promoType, productDetails, discount, normalPrice, promoPrice, customerName);
  }
}

/**
 * Generate an email offer using Gemini Flash Lite model
 */
async function generateGeminiFlashLiteEmailOffer(
  promoName: string,
  promoType: string,
  productDetails: string[],
  discount: string,
  normalPrice: string,
  promoPrice: string,
  region: string,
  startDate: string,
  endDate: string,
  customerName?: string,
  customerCompany?: string
) {
  try {
    console.log('Generating concise email offer with Gemini 2.0 Flash Lite');

    const requestBody = {
      model: MODELS.GEMINI_FLASH_LITE,
      messages: [
        {
          role: 'system',
          content: 'You are a professional sales representative creating concise, impactful email offers. Your writing is professional yet impressive, brief but persuasive.'
        },
        {
          role: 'user',
          content: `Create a CONCISE and PROFESSIONAL email offer in Indonesian for this promotion:

Promo Name: ${promoName}
Promo Type: ${promoType}
Products: ${productDetails.join(', ')}
Discount: ${discount}
Normal Price: ${normalPrice}
Promo Price: ${promoPrice}
Region: ${region || 'Nasional'}
Period: ${new Date(startDate).toLocaleDateString('id-ID')} - ${new Date(endDate).toLocaleDateString('id-ID')}
${customerName ? 'Customer Name: ' + customerName : ''}
${customerCompany ? 'Company: ' + customerCompany : ''}

The email should be professional but modern, concise but compelling. Use proper business Indonesian language throughout. Make it impressive without being verbose.`
        }
      ]
    };

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chitraparatama.co.id',
          'X-Title': 'Chitra Marketing Tools'
        }
      }
    );

    console.log('Gemini Flash Lite API response:', JSON.stringify(response.data, null, 2));

    if (response.data && response.data.choices && response.data.choices.length > 0) {
      if (response.data.choices[0].message && response.data.choices[0].message.content) {
        return response.data.choices[0].message.content;
      }

      // Alternative formats
      if (response.data.choices[0].content) {
        if (typeof response.data.choices[0].content === 'string') {
          return response.data.choices[0].content;
        } else if (response.data.choices[0].content.parts) {
          return response.data.choices[0].content.parts
            .filter((part: any) => part.text)
            .map((part: any) => part.text)
            .join('\n');
        }
      }

      if (response.data.choices[0].text) {
        return response.data.choices[0].text;
      }
    }

    console.error('Unexpected response format:', response.data);
    return getFallbackEmail(promoName, promoType, productDetails, discount, normalPrice, promoPrice, customerName);
  } catch (error) {
    console.error('Error generating email offer with Gemini Flash Lite:', error);
    return getFallbackEmail(promoName, promoType, productDetails, discount, normalPrice, promoPrice, customerName);
  }
}

/**
 * Generate an email offer using OpenRouter with GPT-3.5
 */
async function generateOpenRouterEmailOffer(
  promoName: string,
  promoType: string,
  productDetails: string[],
  discount: string,
  normalPrice: string,
  promoPrice: string,
  region: string,
  startDate: string,
  endDate: string,
  customerName?: string,
  customerCompany?: string
) {
  try {
    console.log('Generating email offer with OpenRouter API');

    const requestBody = {
      model: MODELS.GPT_3_5,
      messages: [
        {
          role: 'system',
          content: 'You are a professional sales representative creating formal email offers for potential clients. Your writing is professional, persuasive, and well-structured.'
        },
        {
          role: 'user',
          content: `Create a PROFESSIONAL and FORMAL email offer in Indonesian for this promotion:

Promo Name: ${promoName}
Promo Type: ${promoType}
Products: ${productDetails.join(', ')}
Discount: ${discount}
Normal Price: ${normalPrice}
Promo Price: ${promoPrice}
Region: ${region || 'Nasional'}
Period: ${new Date(startDate).toLocaleDateString('id-ID')} - ${new Date(endDate).toLocaleDateString('id-ID')}
${customerName ? 'Customer Name: ' + customerName : ''}
${customerCompany ? 'Company: ' + customerCompany : ''}

The email should be formal but warm, professional but not cold, and persuasive without being pushy. Use proper business Indonesian language throughout.`
        }
      ]
    };

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chitraparatama.co.id',
          'X-Title': 'Chitra Marketing Tools'
        }
      }
    );

    console.log('OpenRouter API response:', JSON.stringify(response.data, null, 2));

    if (response.data && response.data.choices && response.data.choices.length > 0) {
      if (response.data.choices[0].message && response.data.choices[0].message.content) {
        return response.data.choices[0].message.content;
      }

      // Alternative formats
      if (response.data.choices[0].content) {
        if (typeof response.data.choices[0].content === 'string') {
          return response.data.choices[0].content;
        } else if (response.data.choices[0].content.parts) {
          return response.data.choices[0].content.parts
            .filter((part: any) => part.text)
            .map((part: any) => part.text)
            .join('\n');
        }
      }

      if (response.data.choices[0].text) {
        return response.data.choices[0].text;
      }
    }

    console.error('Unexpected response format:', response.data);
    return getFallbackEmail(promoName, promoType, productDetails, discount, normalPrice, promoPrice, customerName);
  } catch (error) {
    console.error('Error generating email offer with OpenRouter:', error);
    return getFallbackEmail(promoName, promoType, productDetails, discount, normalPrice, promoPrice, customerName);
  }
}

/**
 * Get a fallback email when API calls fail
 */
function getFallbackEmail(
  promoName: string,
  promoType: string,
  productDetails: string[],
  discount: string,
  normalPrice: string,
  promoPrice: string,
  customerName?: string
) {
  const greeting = customerName ? `Yth. Bapak/Ibu ${customerName},` : 'Yth. Bapak/Ibu,';
  const productList = productDetails.map(product => `<li>${product}</li>`).join('');

  return `<h2>Penawaran ${promoName}</h2>
<p>${greeting}</p>
<p>Kami dengan senang hati ingin menawarkan <strong>${promoName}</strong> untuk produk ban premium kami.</p>
<p>Dalam program ${promoType} ini, Anda berkesempatan mendapatkan:</p>
<ul>
${productList}
</ul>
<p>Dengan <strong>${discount}</strong> dari harga normal ${normalPrice} menjadi hanya ${promoPrice}!</p>
<p>Jangan lewatkan kesempatan ini untuk mendapatkan ban berkualitas tinggi dengan penawaran spesial.</p>
<p>Silakan hubungi tim sales kami untuk informasi lebih lanjut.</p>
<p>Hormat kami,<br>Tim Marketing<br>PT Chitra Paratama</p>`;
}
