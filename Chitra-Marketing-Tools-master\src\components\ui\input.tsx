import React from "react";
import { cn } from "../../lib/utils";

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: 'default' | 'filled' | 'outline' | 'flushed';
  size?: 'default' | 'sm' | 'lg';
  leftElement?: React.ReactNode;
  rightElement?: React.ReactNode;
  error?: boolean;
  errorMessage?: string;
}

/**
 * Input component for text entry
 *
 * @example
 * <Input placeholder="Enter text" />
 * <Input variant="filled" placeholder="Filled input" />
 * <Input leftElement={<Icon />} placeholder="With icon" />
 * <Input error errorMessage="This field is required" />
 */
const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    className = "",
    type = "text",
    variant = "default",
    size = "default",
    leftElement,
    rightElement,
    error = false,
    errorMessage,
    ...props
  }, ref) => {
    // Variant styles
    const variantStyles = {
      default: "border border-gray-300 bg-white focus:border-primary-500 dark:border-gray-600 dark:bg-gray-900 dark:text-gray-100",
      filled: "border-0 bg-gray-100 focus:bg-gray-50 dark:bg-gray-800 dark:focus:bg-gray-700 dark:text-gray-100",
      outline: "border-2 border-gray-300 bg-transparent focus:border-primary-500 dark:border-gray-600 dark:text-gray-100",
      flushed: "border-0 border-b-2 border-gray-300 rounded-none px-0 focus:border-primary-500 dark:border-gray-600 dark:bg-transparent dark:text-gray-100"
    };

    // Size styles
    const sizeStyles = {
      default: "h-10 px-3 py-2 text-sm",
      sm: "h-8 px-2 py-1 text-xs",
      lg: "h-12 px-4 py-3 text-base"
    };

    // Error styles
    const errorStyles = error
      ? "border-red-500 focus:border-red-500 focus:ring-red-500"
      : "focus:ring-primary-500";

    // Container styles for left/right elements
    const containerStyles = cn(
      "relative",
      className
    );

    // Input styles
    const inputStyles = cn(
      "flex w-full rounded-md bg-transparent text-gray-900 placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50",
      variantStyles[variant],
      sizeStyles[size],
      errorStyles,
      leftElement && "pl-10",
      rightElement && "pr-10"
    );

    return (
      <div className={containerStyles}>
        {leftElement && (
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
            {leftElement}
          </div>
        )}

        <input
          type={type}
          className={inputStyles}
          ref={ref}
          aria-invalid={error}
          aria-errormessage={errorMessage ? `error-${props.id || ''}` : undefined}
          {...props}
        />

        {rightElement && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-500">
            {rightElement}
          </div>
        )}

        {error && errorMessage && (
          <p
            id={`error-${props.id || ''}`}
            className="mt-1 text-xs text-red-500"
          >
            {errorMessage}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export { Input };
