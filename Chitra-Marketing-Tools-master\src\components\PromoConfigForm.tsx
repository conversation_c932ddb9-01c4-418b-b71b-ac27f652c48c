import React, { useState, useEffect } from 'react';
import { PromoConfig, PromoType } from '../types/promotion';
import { Customer } from '../types';
import { getAllCustomers } from '../services/customerService';
import { generatePromoName } from '../services/openRouterService';
import { Search, User, X, Sparkles, Loader2 } from 'lucide-react';

interface PromoConfigFormProps {
  config: PromoConfig;
  onConfigChange: (config: PromoConfig) => void;
  productDescriptions?: string[]; // Optional array of product descriptions for AI name generation
}

const PromoConfigForm: React.FC<PromoConfigFormProps> = ({ config, onConfigChange, productDescriptions = [] }) => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isGeneratingName, setIsGeneratingName] = useState(false);

  // Load customers on component mount
  useEffect(() => {
    const loadedCustomers = getAllCustomers();
    setCustomers(loadedCustomers);

    // If there's a customerId in the config, find and set the selected customer
    if (config.customerId) {
      const customer = loadedCustomers.find(c => c.id === config.customerId);
      if (customer) {
        setSelectedCustomer(customer);
      }
    }
  }, [config.customerId]);

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name.startsWith('additionalCosts.')) {
      const costField = name.split('.')[1];
      onConfigChange({
        ...config,
        additionalCosts: {
          ...config.additionalCosts,
          [costField]: costField === 'description' ? value : Number(value),
        },
      });
    } else {
      onConfigChange({
        ...config,
        [name]: value,
      });
    }
  };

  // Handle customer search
  const handleCustomerSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setIsDropdownOpen(true);
  };

  // Filter customers based on search query
  const filteredCustomers = searchQuery
    ? customers.filter(customer =>
        customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (customer.company && customer.company.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : [];

  // Handle customer selection
  const handleSelectCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setSearchQuery('');
    setIsDropdownOpen(false);
    onConfigChange({
      ...config,
      customerId: customer.id,
    });
  };

  // Clear selected customer
  const handleClearCustomer = () => {
    setSelectedCustomer(null);
    onConfigChange({
      ...config,
      customerId: undefined,
    });
  };

  // Generate promo name using AI
  const handleGeneratePromoName = async () => {
    try {
      setIsGeneratingName(true);

      // Use product descriptions from props or fallback to generic descriptions
      const products = productDescriptions.length > 0
        ? productDescriptions
        : ['Ban Premium', 'Ban Mining'];

      const generatedName = await generatePromoName(
        getPromoTypeLabel(config.type),
        products,
        config.region,
        config.startDate,
        config.endDate
      );

      onConfigChange({
        ...config,
        name: generatedName,
      });
    } catch (error) {
      console.error('Error generating promo name:', error);
      alert('Gagal membuat nama promo. Silakan coba lagi nanti.');
    } finally {
      setIsGeneratingName(false);
    }
  };

  // Get human-readable label for promo type
  const getPromoTypeLabel = (type: PromoType): string => {
    switch (type) {
      case PromoType.DISCOUNT_PERCENTAGE:
        return 'Diskon Persentase';
      case PromoType.DISCOUNT_FIXED:
        return 'Diskon Nominal Tetap';
      case PromoType.CASHBACK:
        return 'Cashback';
      case PromoType.BUY_GET:
        return 'Buy X Get Y';
      case PromoType.BUNDLING:
        return 'Bundling Produk/Jasa';
      case PromoType.LOYALTY:
        return 'Program Loyalitas';
      case PromoType.SEASONAL:
        return 'Promo Musiman';
      case PromoType.CLEARANCE:
        return 'Clearance Sale';
      case PromoType.TRADE_IN:
        return 'Program Trade-in';
      case PromoType.VOLUME:
        return 'Diskon Volume';
      default:
        return 'Promo';
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-semibold">Informasi Umum Promo</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Nama Promo */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Nama Promo
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              id="name"
              name="name"
              value={config.name}
              onChange={handleInputChange}
              placeholder="Contoh: Promo Bundling April 2025"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
            <button
              type="button"
              onClick={handleGeneratePromoName}
              disabled={isGeneratingName}
              className="px-3 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 flex items-center"
              title="Generate nama promo dengan AI"
            >
              {isGeneratingName ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-1" />
                  <span>AI</span>
                </>
              )}
            </button>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Klik tombol AI untuk membuat nama promo otomatis
          </p>
        </div>

        {/* Tipe Promo */}
        <div>
          <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
            Tipe Promo
          </label>
          <select
            id="type"
            name="type"
            value={config.type}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value={PromoType.DISCOUNT_PERCENTAGE}>Diskon %</option>
            <option value={PromoType.DISCOUNT_FIXED}>Diskon Nominal Tetap</option>
            <option value={PromoType.CASHBACK}>Cashback</option>
            <option value={PromoType.BUY_GET}>Buy X Get Y</option>
            <option value={PromoType.BUNDLING}>Bundling Produk/Jasa</option>
            <option value={PromoType.LOYALTY}>Program Loyalitas</option>
            <option value={PromoType.SEASONAL}>Promo Musiman</option>
            <option value={PromoType.CLEARANCE}>Clearance Sale</option>
            <option value={PromoType.TRADE_IN}>Program Trade-in</option>
            <option value={PromoType.VOLUME}>Diskon Volume</option>
          </select>
        </div>

        {/* Tanggal Mulai */}
        <div>
          <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
            Tanggal Mulai
          </label>
          <input
            type="date"
            id="startDate"
            name="startDate"
            value={config.startDate}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Tanggal Akhir */}
        <div>
          <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
            Tanggal Akhir
          </label>
          <input
            type="date"
            id="endDate"
            name="endDate"
            value={config.endDate}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Wilayah/Segmentasi */}
        <div>
          <label htmlFor="region" className="block text-sm font-medium text-gray-700 mb-1">
            Wilayah/Segmentasi
          </label>
          <input
            type="text"
            id="region"
            name="region"
            value={config.region}
            onChange={handleInputChange}
            placeholder="Contoh: Kalimantan Timur"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Customer Selector */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Customer
          </label>

          {selectedCustomer ? (
            // Display selected customer
            <div className="flex items-center justify-between p-2 border rounded-md bg-blue-50 border-blue-200">
              <div className="flex items-center">
                <User className="h-5 w-5 text-blue-500 mr-2" />
                <div>
                  <div className="font-medium">{selectedCustomer.name}</div>
                  <div className="text-sm text-gray-500">{selectedCustomer.company || selectedCustomer.email}</div>
                </div>
              </div>
              <button
                onClick={handleClearCustomer}
                className="text-gray-400 hover:text-gray-600"
                title="Clear selected customer"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          ) : (
            // Customer selector
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Cari customer..."
                value={searchQuery}
                onChange={handleCustomerSearch}
                onFocus={() => setIsDropdownOpen(true)}
                onBlur={() => setTimeout(() => setIsDropdownOpen(false), 200)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />

              {isDropdownOpen && filteredCustomers.length > 0 && (
                <div className="absolute z-10 mt-1 w-full bg-white border rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {filteredCustomers.map(customer => (
                    <div
                      key={customer.id}
                      className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleSelectCustomer(customer)}
                    >
                      <div className="font-medium">{customer.name}</div>
                      <div className="text-sm text-gray-600">{customer.company || customer.email}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <h3 className="text-md font-medium mt-4">Biaya Tambahan</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Biaya Marketing */}
        <div>
          <label htmlFor="additionalCosts.marketing" className="block text-sm font-medium text-gray-700 mb-1">
            Biaya Marketing (Rp)
          </label>
          <input
            type="number"
            id="additionalCosts.marketing"
            name="additionalCosts.marketing"
            value={config.additionalCosts.marketing}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Biaya Pengiriman */}
        <div>
          <label htmlFor="additionalCosts.shipping" className="block text-sm font-medium text-gray-700 mb-1">
            Biaya Pengiriman (Rp)
          </label>
          <input
            type="number"
            id="additionalCosts.shipping"
            name="additionalCosts.shipping"
            value={config.additionalCosts.shipping}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Biaya Lainnya */}
        <div>
          <label htmlFor="additionalCosts.other" className="block text-sm font-medium text-gray-700 mb-1">
            Biaya Lainnya (Rp)
          </label>
          <input
            type="number"
            id="additionalCosts.other"
            name="additionalCosts.other"
            value={config.additionalCosts.other}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Deskripsi Biaya Tambahan */}
      <div>
        <label htmlFor="additionalCosts.description" className="block text-sm font-medium text-gray-700 mb-1">
          Deskripsi Biaya Tambahan
        </label>
        <textarea
          id="additionalCosts.description"
          name="additionalCosts.description"
          value={config.additionalCosts.description}
          onChange={handleInputChange}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          placeholder="Jelaskan detail biaya tambahan yang diperlukan untuk promo ini"
        />
      </div>
    </div>
  );
};

export default PromoConfigForm;
