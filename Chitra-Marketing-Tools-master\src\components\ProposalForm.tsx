import React, { useState, useEffect } from 'react';
import {
  ProposalType,
  ProposalFormData,
  Product as ProposalProduct,
  ProposalDraft,
  TradeInDetails,
  PerformanceGuaranteeDetails,
  PerformanceWarrantyDetails,
  FirstMichelinDetails
} from '../types/proposal';
import { Product } from '../types';
import { Template } from '../types/template';
import CustomerSelector from './CustomerSelector';
import ProposalTypeSelector from './ProposalTypeSelector';
import { Plus, Minus, Upload, Search, X, FileText, ExternalLink } from 'lucide-react';
import { fetchProducts } from '../services/productService';
import { getTemplatesByType } from '../services/templateService';

// Helper function to convert from API Product to ProposalProduct
const convertToProposalProduct = (product: Product): ProposalProduct => {
  return {
    name: product.materialDescription,
    quantity: 1,
    unitPrice: product.price,
    total: product.price,
    bonus: ''
  };
};

interface ProposalFormProps {
  onSubmit: (data: ProposalFormData) => void;
}

const DRAFT_STORAGE_KEY = 'proposal_form_draft';

const initialTradeInDetails: TradeInDetails = {
  productName: '',
  tradeInValue: 0,
  newProduct: {
    name: '',
    quantity: 1,
    unitPrice: 0,
    total: 0
  }
};

const initialPerformanceGuaranteeDetails: PerformanceGuaranteeDetails = {
  products: [],
  guaranteePeriod: 12, // Default 12 months
  guaranteeTerms: '',
  performanceMetrics: '',
  compensationTerms: ''
};

const initialPerformanceWarrantyDetails: PerformanceWarrantyDetails = {
  products: [],
  warrantyPeriod: 12, // Default 12 months
  warrantyTerms: '',
  coverageDetails: '',
  claimProcedure: ''
};

const initialFirstMichelinDetails: FirstMichelinDetails = {
  products: [],
  implementationPeriod: 6, // Default 6 months
  specialTerms: '',
  benefitsDescription: '',
  testimonials: ''
};

const ProposalForm: React.FC<ProposalFormProps> = ({ onSubmit }) => {
  const [formData, setFormData] = useState<ProposalFormData>({
    // Basic fields
    customerName: '',
    proposalType: 'bundling',
    proposalDate: new Date().toISOString().split('T')[0],
    validUntil: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split('T')[0],

    // General Info
    proposalTitle: '',
    promoTitle: '',
    productName: '',
    offerType: 'Bundling',

    // Contact & Company
    contactPhone: '+62 21 2997 6661',
    website: 'http://www.chitraparatama.co.id',
    salesName: '',
    salesPhone: '',

    // Signature
    signatoryName: '',
    signatoryTitle: '',
    signatureDate: new Date().toISOString().split('T')[0],

    // Type-specific details
    bundlingDetails: {
      products: []
    }
  });

  const [template, setTemplate] = useState<File | null>(null);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>();
  const [detectedTags, setDetectedTags] = useState<string[]>([]);
  const [availableTemplates, setAvailableTemplates] = useState<Template[]>([]);
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');

  // Product search and selection
  const [products, setProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);

  // Load products on component mount
  useEffect(() => {
    const loadProducts = async () => {
      setIsLoadingProducts(true);
      try {
        const loadedProducts = await fetchProducts();
        setProducts(loadedProducts);
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    loadProducts();
  }, []);

  // Load templates when proposal type changes
  useEffect(() => {
    const loadTemplates = async () => {
      setIsLoadingTemplates(true);
      try {
        const templates = getTemplatesByType(formData.proposalType);
        setAvailableTemplates(templates);

        // Reset selected template if none match the current type
        if (selectedTemplateId && !templates.some(t => t.id === selectedTemplateId)) {
          setSelectedTemplateId('');
          setTemplate(null);
          setDetectedTags([]);
        }
      } catch (error) {
        console.error('Error loading templates:', error);
      } finally {
        setIsLoadingTemplates(false);
      }
    };

    loadTemplates();
  }, [formData.proposalType, selectedTemplateId]);

  // Load draft from localStorage
  useEffect(() => {
    const savedDraft = localStorage.getItem(DRAFT_STORAGE_KEY);
    if (savedDraft) {
      const draft: ProposalDraft = JSON.parse(savedDraft);
      setFormData(prev => ({
        ...prev,
        ...draft
      }));
    }
  }, []);

  // Save draft to localStorage
  useEffect(() => {
    const draft: ProposalDraft = {
      ...formData,
      templateName: template?.name,
      lastUpdated: new Date().toISOString()
    };
    localStorage.setItem(DRAFT_STORAGE_KEY, JSON.stringify(draft));
  }, [formData, template]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleTemplateUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      setTemplate(file);

      try {
        // Read the file content
        const text = await file.text();

        // Extract variables using regex
        const variableRegex = /\{\{([^}]+)\}\}/g;
        const variables: string[] = [];
        let match;

        while ((match = variableRegex.exec(text)) !== null) {
          variables.push(match[1]);
        }

        // Remove duplicates
        const uniqueVariables = [...new Set(variables)];

        if (uniqueVariables.length > 0) {
          setDetectedTags(uniqueVariables);
          console.log('Detected variables in template:', uniqueVariables);
        } else {
          setDetectedTags([]);
          console.log('No variables detected in template');
        }
      } catch (error) {
        console.error('Error parsing template:', error);
        setDetectedTags([]);
      }
    } else {
      alert('Please upload a valid .docx file');
    }
  };

  // Handle product search with error prevention
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      e.preventDefault();
      e.stopPropagation();
      setSearchQuery(e.target.value);
      setIsDropdownOpen(true);
    } catch (err) {
      console.error('Error in search input change:', err);
    }
  };

  // Filter products based on search query with error handling
  const filteredProducts = React.useMemo(() => {
    try {
      if (!searchQuery || !searchQuery.trim()) {
        return [];
      }

      const lowerSearchTerm = searchQuery.toLowerCase();
      return products.filter(product => {
        try {
          return (
            ((product.materialDescription || '').toLowerCase()).includes(lowerSearchTerm) ||
            ((product.oldMaterialNo || '').toLowerCase()).includes(lowerSearchTerm)
          );
        } catch (err) {
          console.error('Error filtering product:', product, err);
          return false;
        }
      });
    } catch (err) {
      console.error('Error in filtering products:', err);
      return []; // Return empty array if there's an error
    }
  }, [searchQuery, products]);

  // Handle product selection from search
  const handleSelectProduct = (product: Product) => {
    const proposalProduct = convertToProposalProduct(product);

    if (formData.proposalType === 'bundling') {
      setFormData(prev => ({
        ...prev,
        bundlingDetails: {
          products: [...(prev.bundlingDetails?.products || []), proposalProduct]
        }
      }));
    } else if (formData.proposalType === 'consignment') {
      setFormData(prev => ({
        ...prev,
        consignmentDetails: {
          ...(prev.consignmentDetails || { projectLocation: '', evaluationPeriod: 1 }),
          products: [...(prev.consignmentDetails?.products || []), proposalProduct]
        }
      }));
    } else if (formData.proposalType === 'performance-guarantee') {
      setFormData(prev => ({
        ...prev,
        performanceGuaranteeDetails: {
          ...(prev.performanceGuaranteeDetails || initialPerformanceGuaranteeDetails),
          products: [...(prev.performanceGuaranteeDetails?.products || []), proposalProduct]
        }
      }));
    } else if (formData.proposalType === 'performance-warranty') {
      setFormData(prev => ({
        ...prev,
        performanceWarrantyDetails: {
          ...(prev.performanceWarrantyDetails || initialPerformanceWarrantyDetails),
          products: [...(prev.performanceWarrantyDetails?.products || []), proposalProduct]
        }
      }));
    } else if (formData.proposalType === 'first-michelin') {
      setFormData(prev => ({
        ...prev,
        firstMichelinDetails: {
          ...(prev.firstMichelinDetails || initialFirstMichelinDetails),
          products: [...(prev.firstMichelinDetails?.products || []), proposalProduct]
        }
      }));
    }

    setSearchQuery('');
    setIsDropdownOpen(false);
  };

  // Add empty product manually
  const handleAddProduct = () => {
    const newProduct: ProposalProduct = {
      name: '',
      quantity: 1,
      unitPrice: 0,
      total: 0,
      bonus: ''
    };

    if (formData.proposalType === 'bundling') {
      setFormData(prev => ({
        ...prev,
        bundlingDetails: {
          products: [...(prev.bundlingDetails?.products || []), newProduct]
        }
      }));
    } else if (formData.proposalType === 'consignment') {
      setFormData(prev => ({
        ...prev,
        consignmentDetails: {
          ...(prev.consignmentDetails || { projectLocation: '', evaluationPeriod: 1 }),
          products: [...(prev.consignmentDetails?.products || []), newProduct]
        }
      }));
    } else if (formData.proposalType === 'performance-guarantee') {
      setFormData(prev => ({
        ...prev,
        performanceGuaranteeDetails: {
          ...(prev.performanceGuaranteeDetails || initialPerformanceGuaranteeDetails),
          products: [...(prev.performanceGuaranteeDetails?.products || []), newProduct]
        }
      }));
    } else if (formData.proposalType === 'performance-warranty') {
      setFormData(prev => ({
        ...prev,
        performanceWarrantyDetails: {
          ...(prev.performanceWarrantyDetails || initialPerformanceWarrantyDetails),
          products: [...(prev.performanceWarrantyDetails?.products || []), newProduct]
        }
      }));
    } else if (formData.proposalType === 'first-michelin') {
      setFormData(prev => ({
        ...prev,
        firstMichelinDetails: {
          ...(prev.firstMichelinDetails || initialFirstMichelinDetails),
          products: [...(prev.firstMichelinDetails?.products || []), newProduct]
        }
      }));
    }
  };

  // Remove product
  const handleRemoveProduct = (index: number) => {
    if (formData.proposalType === 'bundling') {
      setFormData(prev => ({
        ...prev,
        bundlingDetails: {
          products: prev.bundlingDetails?.products.filter((_, i) => i !== index) || []
        }
      }));
    } else if (formData.proposalType === 'consignment') {
      setFormData(prev => ({
        ...prev,
        consignmentDetails: {
          ...(prev.consignmentDetails || { projectLocation: '', evaluationPeriod: 1 }),
          products: prev.consignmentDetails?.products.filter((_, i) => i !== index) || []
        }
      }));
    } else if (formData.proposalType === 'performance-guarantee') {
      setFormData(prev => ({
        ...prev,
        performanceGuaranteeDetails: {
          ...(prev.performanceGuaranteeDetails || initialPerformanceGuaranteeDetails),
          products: prev.performanceGuaranteeDetails?.products.filter((_, i) => i !== index) || []
        }
      }));
    } else if (formData.proposalType === 'performance-warranty') {
      setFormData(prev => ({
        ...prev,
        performanceWarrantyDetails: {
          ...(prev.performanceWarrantyDetails || initialPerformanceWarrantyDetails),
          products: prev.performanceWarrantyDetails?.products.filter((_, i) => i !== index) || []
        }
      }));
    } else if (formData.proposalType === 'first-michelin') {
      setFormData(prev => ({
        ...prev,
        firstMichelinDetails: {
          ...(prev.firstMichelinDetails || initialFirstMichelinDetails),
          products: prev.firstMichelinDetails?.products.filter((_, i) => i !== index) || []
        }
      }));
    }
  };

  // Update product field
  const handleProductChange = (index: number, field: keyof ProposalProduct, value: string | number) => {
    const updateProducts = (products: ProposalProduct[]) => {
      return products.map((product, i) => {
        if (i === index) {
          const updatedProduct = { ...product, [field]: value };
          if (field === 'quantity' || field === 'unitPrice') {
            const quantity = field === 'quantity' ? Number(value) : product.quantity;
            const unitPrice = field === 'unitPrice' ? Number(value) : product.unitPrice;
            updatedProduct.total = quantity * unitPrice;
          }
          return updatedProduct;
        }
        return product;
      });
    };

    if (formData.proposalType === 'bundling') {
      setFormData(prev => ({
        ...prev,
        bundlingDetails: {
          products: updateProducts(prev.bundlingDetails?.products || [])
        }
      }));
    } else if (formData.proposalType === 'consignment') {
      setFormData(prev => ({
        ...prev,
        consignmentDetails: {
          ...(prev.consignmentDetails || { projectLocation: '', evaluationPeriod: 1 }),
          products: updateProducts(prev.consignmentDetails?.products || [])
        }
      }));
    } else if (formData.proposalType === 'performance-guarantee') {
      setFormData(prev => ({
        ...prev,
        performanceGuaranteeDetails: {
          ...(prev.performanceGuaranteeDetails || initialPerformanceGuaranteeDetails),
          products: updateProducts(prev.performanceGuaranteeDetails?.products || [])
        }
      }));
    } else if (formData.proposalType === 'performance-warranty') {
      setFormData(prev => ({
        ...prev,
        performanceWarrantyDetails: {
          ...(prev.performanceWarrantyDetails || initialPerformanceWarrantyDetails),
          products: updateProducts(prev.performanceWarrantyDetails?.products || [])
        }
      }));
    } else if (formData.proposalType === 'first-michelin') {
      setFormData(prev => ({
        ...prev,
        firstMichelinDetails: {
          ...(prev.firstMichelinDetails || initialFirstMichelinDetails),
          products: updateProducts(prev.firstMichelinDetails?.products || [])
        }
      }));
    }
  };

  const getProductsForCurrentType = (): ProposalProduct[] => {
    switch (formData.proposalType) {
      case 'bundling':
        return formData.bundlingDetails?.products || [];
      case 'consignment':
        return formData.consignmentDetails?.products || [];
      case 'performance-guarantee':
        return formData.performanceGuaranteeDetails?.products || [];
      case 'performance-warranty':
        return formData.performanceWarrantyDetails?.products || [];
      case 'first-michelin':
        return formData.firstMichelinDetails?.products || [];
      default:
        return [];
    }
  };

  const renderProductFields = () => {
    const products = getProductsForCurrentType();

    return (
      <div className="space-y-4">
        {/* Product Search */}
        <form
          className="relative"
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            return false;
          }}
        >
          <div className="flex items-center border rounded-lg overflow-hidden">
            <div className="px-3 text-gray-400">
              <Search size={18} />
            </div>
            <input
              type="text"
              placeholder="Cari produk ban..."
              value={searchQuery}
              onChange={handleSearchChange}
              onFocus={(e) => {
                e.preventDefault();
                setIsDropdownOpen(true);
              }}
              onBlur={(e) => {
                e.preventDefault();
                setTimeout(() => setIsDropdownOpen(false), 200);
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}
              className="flex-1 py-2 px-2 outline-none no-navigation"
            />
            {searchQuery && (
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSearchQuery('');
                }}
                className="px-3 text-gray-400 hover:text-gray-600"
              >
                <X size={16} />
              </button>
            )}
          </div>

          {isDropdownOpen && filteredProducts.length > 0 && (
            <div className="absolute z-10 mt-1 w-full bg-white border rounded-lg shadow-lg max-h-60 overflow-y-auto">
              {filteredProducts.map(product => (
                <div
                  key={product.id}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleSelectProduct(product);
                  }}
                >
                  <div className="font-medium">{product.materialDescription || 'No Name'}</div>
                  <div className="text-sm text-gray-600">{product.description || 'No Description'}</div>
                  <div className="text-sm text-gray-500">
                    Code: {product.oldMaterialNo || 'N/A'} •
                    Price: IDR {(product.price || 0).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </form>

        {/* Selected Products */}
        <div className="mt-4">
          <h3 className="text-md font-medium mb-2">Produk Terpilih</h3>

          {products.length === 0 ? (
            <div className="text-gray-500 italic">Belum ada produk yang dipilih</div>
          ) : (
            <div className="space-y-3">
              {products.map((product, index) => (
                <div key={index} className="flex flex-col p-4 bg-gray-50 rounded-md mb-2">
                  <div className="flex items-start space-x-4 mb-2">
                    <div className="flex-1">
                      <label className="text-xs font-medium text-gray-700 mb-1 block">
                        Nama Produk <span className="text-xs text-blue-600">{`{{Products[${index}].Name}}`}</span>
                      </label>
                      <input
                        type="text"
                        placeholder="Nama Produk"
                        value={product.name}
                        onChange={(e) => handleProductChange(index, 'name', e.target.value)}
                        className="w-full p-2 border rounded"
                        title={`Template variable: {{Products[${index}].Name}}`}
                      />
                    </div>
                    <div className="w-24">
                      <label className="text-xs font-medium text-gray-700 mb-1 block">
                        Qty <span className="text-xs text-blue-600">{`{{Products[${index}].Qty}}`}</span>
                      </label>
                      <input
                        type="number"
                        placeholder="Qty"
                        value={product.quantity}
                        onChange={(e) => handleProductChange(index, 'quantity', parseInt(e.target.value))}
                        className="w-full p-2 border rounded"
                        title={`Template variable: {{Products[${index}].Qty}}`}
                      />
                    </div>
                    <div className="w-32">
                      <label className="text-xs font-medium text-gray-700 mb-1 block">
                        Harga <span className="text-xs text-blue-600">{`{{Products[${index}].UnitPrice}}`}</span>
                      </label>
                      <input
                        type="number"
                        placeholder="Harga"
                        value={product.unitPrice}
                        onChange={(e) => handleProductChange(index, 'unitPrice', parseFloat(e.target.value))}
                        className="w-full p-2 border rounded"
                        title={`Template variable: {{Products[${index}].UnitPrice}}`}
                      />
                    </div>
                    <div className="w-32">
                      <label className="text-xs font-medium text-gray-700 mb-1 block">
                        Total <span className="text-xs text-blue-600">{`{{Products[${index}].Total}}`}</span>
                      </label>
                      <div className="p-2 text-right border rounded bg-gray-50">
                        {new Intl.NumberFormat('id-ID').format(product.total)}
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleRemoveProduct(index)}
                      className="p-2 text-red-600 hover:text-red-800 mt-6"
                    >
                      <Minus size={20} />
                    </button>
                  </div>
                  <div className="flex items-center">
                    <label className="text-sm font-medium text-gray-700 mr-2">
                      Bonus <span className="text-xs text-blue-600">{`{{Products[${index}].Bonus}}`}</span>:
                    </label>
                    <input
                      type="text"
                      placeholder="Bonus (opsional)"
                      value={product.bonus || ''}
                      onChange={(e) => handleProductChange(index, 'bonus', e.target.value)}
                      className="flex-1 p-2 border rounded text-sm"
                      title={`Template variable: {{Products[${index}].Bonus}}`}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}

          <button
            type="button"
            onClick={handleAddProduct}
            className="flex items-center px-4 py-2 mt-3 text-sm text-blue-600 hover:text-blue-800"
          >
            <Plus size={20} className="mr-1" />
            Tambah Produk Manual
          </button>
        </div>
      </div>
    );
  };

  const renderConsignmentFields = () => (
    <div className="space-y-4">
      <div>
        <label htmlFor="projectLocation" className="block text-sm font-medium text-gray-700">
          Lokasi Proyek
        </label>
        <input
          type="text"
          id="projectLocation"
          value={formData.consignmentDetails?.projectLocation || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            consignmentDetails: {
              ...(prev.consignmentDetails || { products: [], evaluationPeriod: 1 }),
              projectLocation: e.target.value
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
      <div>
        <label htmlFor="evaluationPeriod" className="block text-sm font-medium text-gray-700">
          Masa Evaluasi (bulan)
        </label>
        <input
          type="number"
          id="evaluationPeriod"
          value={formData.consignmentDetails?.evaluationPeriod || 1}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            consignmentDetails: {
              ...(prev.consignmentDetails || { products: [], projectLocation: '' }),
              evaluationPeriod: parseInt(e.target.value)
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
      {renderProductFields()}
    </div>
  );

  const renderTradeInFields = () => (
    <div className="space-y-4">
      <div>
        <label htmlFor="tradeInProduct" className="block text-sm font-medium text-gray-700">
          Produk Trade-In
        </label>
        <input
          type="text"
          id="tradeInProduct"
          value={formData.tradeInDetails?.productName || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            tradeInDetails: {
              ...(prev.tradeInDetails || initialTradeInDetails),
              productName: e.target.value
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
      <div>
        <label htmlFor="tradeInValue" className="block text-sm font-medium text-gray-700">
          Nilai Trade-In
        </label>
        <input
          type="number"
          id="tradeInValue"
          value={formData.tradeInDetails?.tradeInValue || 0}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            tradeInDetails: {
              ...(prev.tradeInDetails || initialTradeInDetails),
              tradeInValue: parseFloat(e.target.value)
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-2">Produk Baru</h4>
        <div className="space-y-2">
          <input
            type="text"
            placeholder="Nama Produk"
            value={formData.tradeInDetails?.newProduct.name || ''}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              tradeInDetails: {
                ...(prev.tradeInDetails || initialTradeInDetails),
                newProduct: {
                  ...(prev.tradeInDetails?.newProduct || { quantity: 1, unitPrice: 0, total: 0 }),
                  name: e.target.value
                }
              }
            }))}
            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
          <div className="grid grid-cols-2 gap-2">
            <input
              type="number"
              placeholder="Harga"
              value={formData.tradeInDetails?.newProduct.unitPrice || 0}
              onChange={(e) => {
                const price = parseFloat(e.target.value);
                setFormData(prev => ({
                  ...prev,
                  tradeInDetails: {
                    ...(prev.tradeInDetails || initialTradeInDetails),
                    newProduct: {
                      ...(prev.tradeInDetails?.newProduct || { name: '', quantity: 1 }),
                      unitPrice: price,
                      total: price * (prev.tradeInDetails?.newProduct.quantity || 1)
                    }
                  }
                }));
              }}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            <input
              type="number"
              placeholder="Quantity"
              value={formData.tradeInDetails?.newProduct.quantity || 1}
              onChange={(e) => {
                const qty = parseInt(e.target.value);
                setFormData(prev => ({
                  ...prev,
                  tradeInDetails: {
                    ...(prev.tradeInDetails || initialTradeInDetails),
                    newProduct: {
                      ...(prev.tradeInDetails?.newProduct || { name: '', unitPrice: 0 }),
                      quantity: qty,
                      total: qty * (prev.tradeInDetails?.newProduct.unitPrice || 0)
                    }
                  }
                }));
              }}
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderPerformanceGuaranteeFields = () => (
    <div className="space-y-4">
      {renderProductFields()}

      <div>
        <label htmlFor="guaranteePeriod" className="block text-sm font-medium text-gray-700">
          Periode Garansi (bulan)
        </label>
        <input
          type="number"
          id="guaranteePeriod"
          value={formData.performanceGuaranteeDetails?.guaranteePeriod || 12}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            performanceGuaranteeDetails: {
              ...(prev.performanceGuaranteeDetails || initialPerformanceGuaranteeDetails),
              guaranteePeriod: parseInt(e.target.value)
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="guaranteeTerms" className="block text-sm font-medium text-gray-700">
          Syarat dan Ketentuan Garansi
        </label>
        <textarea
          id="guaranteeTerms"
          rows={3}
          value={formData.performanceGuaranteeDetails?.guaranteeTerms || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            performanceGuaranteeDetails: {
              ...(prev.performanceGuaranteeDetails || initialPerformanceGuaranteeDetails),
              guaranteeTerms: e.target.value
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="performanceMetrics" className="block text-sm font-medium text-gray-700">
          Metrik Performa
        </label>
        <textarea
          id="performanceMetrics"
          rows={3}
          value={formData.performanceGuaranteeDetails?.performanceMetrics || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            performanceGuaranteeDetails: {
              ...(prev.performanceGuaranteeDetails || initialPerformanceGuaranteeDetails),
              performanceMetrics: e.target.value
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="compensationTerms" className="block text-sm font-medium text-gray-700">
          Ketentuan Kompensasi
        </label>
        <textarea
          id="compensationTerms"
          rows={3}
          value={formData.performanceGuaranteeDetails?.compensationTerms || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            performanceGuaranteeDetails: {
              ...(prev.performanceGuaranteeDetails || initialPerformanceGuaranteeDetails),
              compensationTerms: e.target.value
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
    </div>
  );

  const renderPerformanceWarrantyFields = () => (
    <div className="space-y-4">
      {renderProductFields()}

      <div>
        <label htmlFor="warrantyPeriod" className="block text-sm font-medium text-gray-700">
          Periode Garansi (bulan)
        </label>
        <input
          type="number"
          id="warrantyPeriod"
          value={formData.performanceWarrantyDetails?.warrantyPeriod || 12}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            performanceWarrantyDetails: {
              ...(prev.performanceWarrantyDetails || initialPerformanceWarrantyDetails),
              warrantyPeriod: parseInt(e.target.value)
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="warrantyTerms" className="block text-sm font-medium text-gray-700">
          Syarat dan Ketentuan Garansi
        </label>
        <textarea
          id="warrantyTerms"
          rows={3}
          value={formData.performanceWarrantyDetails?.warrantyTerms || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            performanceWarrantyDetails: {
              ...(prev.performanceWarrantyDetails || initialPerformanceWarrantyDetails),
              warrantyTerms: e.target.value
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="coverageDetails" className="block text-sm font-medium text-gray-700">
          Detail Cakupan Garansi
        </label>
        <textarea
          id="coverageDetails"
          rows={3}
          value={formData.performanceWarrantyDetails?.coverageDetails || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            performanceWarrantyDetails: {
              ...(prev.performanceWarrantyDetails || initialPerformanceWarrantyDetails),
              coverageDetails: e.target.value
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="claimProcedure" className="block text-sm font-medium text-gray-700">
          Prosedur Klaim
        </label>
        <textarea
          id="claimProcedure"
          rows={3}
          value={formData.performanceWarrantyDetails?.claimProcedure || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            performanceWarrantyDetails: {
              ...(prev.performanceWarrantyDetails || initialPerformanceWarrantyDetails),
              claimProcedure: e.target.value
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
    </div>
  );

  const renderFirstMichelinFields = () => (
    <div className="space-y-4">
      {renderProductFields()}

      <div>
        <label htmlFor="implementationPeriod" className="block text-sm font-medium text-gray-700">
          Periode Implementasi (bulan)
        </label>
        <input
          type="number"
          id="implementationPeriod"
          value={formData.firstMichelinDetails?.implementationPeriod || 6}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            firstMichelinDetails: {
              ...(prev.firstMichelinDetails || initialFirstMichelinDetails),
              implementationPeriod: parseInt(e.target.value)
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="specialTerms" className="block text-sm font-medium text-gray-700">
          Ketentuan Khusus
        </label>
        <textarea
          id="specialTerms"
          rows={3}
          value={formData.firstMichelinDetails?.specialTerms || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            firstMichelinDetails: {
              ...(prev.firstMichelinDetails || initialFirstMichelinDetails),
              specialTerms: e.target.value
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="benefitsDescription" className="block text-sm font-medium text-gray-700">
          Deskripsi Manfaat
        </label>
        <textarea
          id="benefitsDescription"
          rows={3}
          value={formData.firstMichelinDetails?.benefitsDescription || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            firstMichelinDetails: {
              ...(prev.firstMichelinDetails || initialFirstMichelinDetails),
              benefitsDescription: e.target.value
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>

      <div>
        <label htmlFor="testimonials" className="block text-sm font-medium text-gray-700">
          Testimoni (opsional)
        </label>
        <textarea
          id="testimonials"
          rows={3}
          value={formData.firstMichelinDetails?.testimonials || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            firstMichelinDetails: {
              ...(prev.firstMichelinDetails || initialFirstMichelinDetails),
              testimonials: e.target.value
            }
          }))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
    </div>
  );

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // No validation required - all fields are optional now

    // Create a copy of the form data with the template file and detected variables
    const formDataWithTemplate = {
      ...formData,
      template: template || undefined,
      detectedVariables: detectedTags
    };

    console.log('Submitting proposal form with data:', formDataWithTemplate);
    onSubmit(formDataWithTemplate as ProposalFormData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <CustomerSelector
        selectedCustomerId={selectedCustomerId}
        onCustomerSelect={(customerId) => {
          setSelectedCustomerId(customerId);
          setFormData(prev => ({ ...prev, customerName: customerId || '' }));
        }}
      />

      <ProposalTypeSelector
        value={formData.proposalType}
        onChange={(type) => {
          setFormData(prev => ({
            ...prev,
            proposalType: type,
            // Reset type-specific details when changing types
            bundlingDetails: type === 'bundling' ? { products: [] } : undefined,
            consignmentDetails: type === 'consignment' ? { projectLocation: '', evaluationPeriod: 1, products: [] } : undefined,
            tradeInDetails: type === 'trade-in' ? initialTradeInDetails : undefined,
            performanceGuaranteeDetails: type === 'performance-guarantee' ? initialPerformanceGuaranteeDetails : undefined,
            performanceWarrantyDetails: type === 'performance-warranty' ? initialPerformanceWarrantyDetails : undefined,
            firstMichelinDetails: type === 'first-michelin' ? initialFirstMichelinDetails : undefined
          }));
        }}
      />

      {/* General Info Section */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 mb-4">📄 Informasi Umum</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="proposalTitle" className="block text-sm font-medium text-gray-700">
              Judul Proposal <span className="text-xs text-blue-600">{`{{ProposalTitle}}`}</span>
            </label>
            <input
              type="text"
              id="proposalTitle"
              name="proposalTitle"
              value={formData.proposalTitle}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Masukkan judul proposal"
              title="Template variable: {{ProposalTitle}}"
            />
          </div>

          <div>
            <label htmlFor="promoTitle" className="block text-sm font-medium text-gray-700">
              Judul Promo <span className="text-xs text-blue-600">{`{{PromoTitle}}`}</span>
            </label>
            <input
              type="text"
              id="promoTitle"
              name="promoTitle"
              value={formData.promoTitle}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Contoh: Free CTS Consignment"
              title="Template variable: {{PromoTitle}}"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="proposalDate" className="block text-sm font-medium text-gray-700">
              Tanggal Proposal <span className="text-xs text-blue-600">{`{{ProposalDate}}`}</span>
            </label>
            <input
              type="date"
              id="proposalDate"
              name="proposalDate"
              value={formData.proposalDate}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              title="Template variable: {{ProposalDate}}"
            />
          </div>
          <div>
            <label htmlFor="validUntil" className="block text-sm font-medium text-gray-700">
              Berlaku Sampai <span className="text-xs text-blue-600">{`{{ValidUntil}}`}</span>
            </label>
            <input
              type="date"
              id="validUntil"
              name="validUntil"
              value={formData.validUntil}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              title="Template variable: {{ValidUntil}}"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="productName" className="block text-sm font-medium text-gray-700">
              Nama Produk <span className="text-xs text-blue-600">{`{{ProductName}}`}</span>
            </label>
            <input
              type="text"
              id="productName"
              name="productName"
              value={formData.productName}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Nama produk utama"
              title="Template variable: {{ProductName}}"
            />
          </div>

          <div>
            <label htmlFor="offerType" className="block text-sm font-medium text-gray-700">
              Tipe Penawaran <span className="text-xs text-blue-600">{`{{OfferType}}`}</span>
            </label>
            <select
              id="offerType"
              name="offerType"
              value={formData.offerType}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              title="Template variable: {{OfferType}}"
            >
              <option value="Bundling">Bundling</option>
              <option value="Konsinyasi">Konsinyasi</option>
              <option value="Trade-In">Trade-In</option>
            </select>
          </div>
        </div>
      </div>

      {/* Contact & Company Section */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 mb-4">📍 Kontak & Perusahaan</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="contactPhone" className="block text-sm font-medium text-gray-700">
              Telepon Kontak <span className="text-xs text-blue-600">{`{{ContactPhone}}`}</span>
            </label>
            <input
              type="text"
              id="contactPhone"
              name="contactPhone"
              value={formData.contactPhone}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="+62 21 2997 6661"
              title="Template variable: {{ContactPhone}}"
            />
          </div>

          <div>
            <label htmlFor="website" className="block text-sm font-medium text-gray-700">
              Website <span className="text-xs text-blue-600">{`{{Website}}`}</span>
            </label>
            <input
              type="url"
              id="website"
              name="website"
              value={formData.website}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="http://www.chitraparatama.co.id"
              title="Template variable: {{Website}}"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="salesName" className="block text-sm font-medium text-gray-700">
              Nama Sales <span className="text-xs text-blue-600">{`{{SalesName}}`}</span>
            </label>
            <input
              type="text"
              id="salesName"
              name="salesName"
              value={formData.salesName}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Nama sales"
              title="Template variable: {{SalesName}}"
            />
          </div>

          <div>
            <label htmlFor="salesPhone" className="block text-sm font-medium text-gray-700">
              Telepon Sales <span className="text-xs text-blue-600">{`{{SalesPhone}}`}</span>
            </label>
            <input
              type="tel"
              id="salesPhone"
              name="salesPhone"
              value={formData.salesPhone}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Nomor telepon sales"
              title="Template variable: {{SalesPhone}}"
            />
          </div>
        </div>
      </div>

      {/* Signature Section */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 mb-4">🖋️ Tanda Tangan</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="signatoryName" className="block text-sm font-medium text-gray-700">
              Nama Penandatangan <span className="text-xs text-blue-600">{`{{SignatoryName}}`}</span>
            </label>
            <input
              type="text"
              id="signatoryName"
              name="signatoryName"
              value={formData.signatoryName}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Nama penandatangan"
              title="Template variable: {{SignatoryName}}"
            />
          </div>

          <div>
            <label htmlFor="signatoryTitle" className="block text-sm font-medium text-gray-700">
              Jabatan Penandatangan <span className="text-xs text-blue-600">{`{{SignatoryTitle}}`}</span>
            </label>
            <input
              type="text"
              id="signatoryTitle"
              name="signatoryTitle"
              value={formData.signatoryTitle}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Jabatan penandatangan"
              title="Template variable: {{SignatoryTitle}}"
            />
          </div>

          <div>
            <label htmlFor="signatureDate" className="block text-sm font-medium text-gray-700">
              Tanggal Tanda Tangan <span className="text-xs text-blue-600">{`{{SignatureDate}}`}</span>
            </label>
            <input
              type="date"
              id="signatureDate"
              name="signatureDate"
              value={formData.signatureDate}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              title="Template variable: {{SignatureDate}}"
            />
          </div>
        </div>
      </div>

      {/* Dynamic fields based on proposal type */}
      {formData.proposalType === 'bundling' && renderProductFields()}
      {formData.proposalType === 'consignment' && renderConsignmentFields()}
      {formData.proposalType === 'trade-in' && renderTradeInFields()}
      {formData.proposalType === 'performance-guarantee' && renderPerformanceGuaranteeFields()}
      {formData.proposalType === 'performance-warranty' && renderPerformanceWarrantyFields()}
      {formData.proposalType === 'first-michelin' && renderFirstMichelinFields()}

      {/* Template Selection */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 mb-4">📄 Template Proposal</h3>

        {availableTemplates.length > 0 ? (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pilih Template
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {availableTemplates.map(tmpl => (
                  <div
                    key={tmpl.id}
                    className={`border rounded-md p-3 cursor-pointer transition-colors ${
                      selectedTemplateId === tmpl.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300 hover:border-blue-300'
                    }`}
                    onClick={async () => {
                      setSelectedTemplateId(tmpl.id);
                      setDetectedTags(tmpl.detectedVariables);

                      try {
                        // Create a dummy File object since we don't have the actual file stored
                        // This is a placeholder that will be recognized as a template being selected
                        const dummyContent = new Uint8Array([80, 75, 3, 4]); // PK header for ZIP/DOCX
                        const dummyFile = new File([dummyContent], tmpl.name, { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
                        setTemplate(dummyFile);
                        console.log('Template selected:', tmpl.name);
                      } catch (error) {
                        console.error('Error creating dummy template file:', error);
                      }
                    }}
                  >
                    <div className="flex items-center">
                      <FileText size={18} className="text-blue-500 mr-2" />
                      <div>
                        <div className="font-medium">{tmpl.name}</div>
                        <div className="text-xs text-gray-500">
                          {tmpl.detectedVariables.length} variabel terdeteksi
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {selectedTemplateId && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Variabel Terdeteksi:</h4>
                <div className="flex flex-wrap gap-2 p-3 bg-white rounded-md border border-gray-200">
                  {availableTemplates
                    .find(t => t.id === selectedTemplateId)?.detectedVariables
                    .map((variable, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {`{{${variable}}}`}
                      </span>
                    ))
                  }
                </div>
              </div>
            )}

            <div className="flex items-center mt-4">
              <div className="flex-1 border-t border-gray-300"></div>
              <span className="px-3 text-sm text-gray-500">atau</span>
              <div className="flex-1 border-t border-gray-300"></div>
            </div>
          </div>
        ) : (
          <div className="text-center p-4 bg-yellow-50 border border-yellow-200 rounded-md mb-4">
            <p className="text-sm text-yellow-700">
              Tidak ada template tersedia untuk jenis proposal ini.
              Silakan upload template baru atau buat template di menu Template Management.
            </p>
            <a
              href="#/template-management"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center mt-2 text-sm text-blue-600 hover:text-blue-800"
            >
              <ExternalLink size={14} className="mr-1" />
              Buka Template Management
            </a>
          </div>
        )}

        {/* Manual Template Upload */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Template Manual (.docx)
          </label>
          <div className="flex items-center">
            <input
              type="file"
              accept=".docx"
              onChange={handleTemplateUpload}
              className="sr-only"
              id="template-upload"
            />
            <label
              htmlFor="template-upload"
              className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <Upload size={16} className="mr-2" />
              Upload Template
            </label>
            {template && (
              <span className="ml-3 text-sm text-gray-500">
                {template.name}
              </span>
            )}
          </div>

          {detectedTags.length > 0 && !selectedTemplateId && (
            <div className="mt-2">
              <p className="text-sm text-gray-600 mb-1">Tag terdeteksi:</p>
              <div className="flex flex-wrap gap-2 p-3 bg-white rounded-md border border-gray-200">
                {detectedTags.map((tag, index) => (
                  <span key={index} className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                    {`{{${tag}}}`}
                  </span>
                ))}
              </div>
            </div>
          )}

          <p className="mt-2 text-sm text-gray-500">
            Upload file .docx yang berisi tag seperti {`{{CustomerName}}`}, {`{{ProductName}}`}, dsb.
          </p>
        </div>
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Generate Proposal
        </button>
      </div>
    </form>
  );
};

export default ProposalForm;
