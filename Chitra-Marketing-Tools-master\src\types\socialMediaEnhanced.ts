/**
 * Types for enhanced Social Media Marketing features
 */
import { SocialMediaPlatform, ContentType, PostStatus } from './socialMedia';

/**
 * Media Asset for Content Library
 */
export interface MediaAsset {
  id: string;
  name: string;
  type: 'image' | 'video' | 'document';
  mimeType: string;
  size: number;
  dataUrl: string;
  thumbnailUrl?: string;
  uploadDate: Date;
  tags: string[];
  description?: string;
  dimensions?: {
    width: number;
    height: number;
  };
  duration?: number; // For videos, in seconds
  platform?: SocialMediaPlatform;
  contentType?: ContentType;
  createdBy?: string;
}

/**
 * Media Asset Create Request
 */
export interface MediaAssetCreateRequest {
  file: File;
  name?: string;
  tags?: string[];
  description?: string;
  platform?: SocialMediaPlatform;
  contentType?: ContentType;
}

/**
 * Media Asset Update Request
 */
export interface MediaAssetUpdateRequest {
  id: string;
  name?: string;
  tags?: string[];
  description?: string;
  platform?: SocialMediaPlatform;
  contentType?: ContentType;
}

/**
 * Media Asset Search Request
 */
export interface MediaAssetSearchRequest {
  query?: string;
  tags?: string[];
  type?: 'image' | 'video' | 'document';
  platform?: SocialMediaPlatform;
  contentType?: ContentType;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

/**
 * Media Asset Search Response
 */
export interface MediaAssetSearchResponse {
  assets: MediaAsset[];
  total: number;
}

/**
 * Social Media Post Analytics
 */
export interface PostAnalytics {
  postId: string;
  platform: SocialMediaPlatform;
  impressions: number;
  reach: number;
  engagement: number;
  likes: number;
  comments: number;
  shares: number;
  saves: number;
  clicks: number;
  profileVisits: number;
  followersGained: number;
  date: Date;
  demographicData?: {
    ageRanges: {
      range: string;
      percentage: number;
    }[];
    genderDistribution: {
      gender: string;
      percentage: number;
    }[];
    topLocations: {
      location: string;
      percentage: number;
    }[];
  };
}

/**
 * Social Media Account
 */
export interface SocialMediaAccount {
  id: string;
  platform: SocialMediaPlatform;
  username: string;
  profileName: string;
  profilePictureUrl?: string;
  isConnected: boolean;
  accessToken?: string;
  refreshToken?: string;
  tokenExpiry?: Date;
  lastSyncDate?: Date;
  followerCount?: number;
  followingCount?: number;
  postCount?: number;
  businessAccount?: boolean;
}

/**
 * Social Media Account Create Request
 */
export interface SocialMediaAccountCreateRequest {
  platform: SocialMediaPlatform;
  username: string;
  profileName: string;
  accessToken?: string;
  refreshToken?: string;
  tokenExpiry?: Date;
}

/**
 * Direct Post Request
 */
export interface DirectPostRequest {
  accountId: string;
  platform: SocialMediaPlatform;
  contentType: ContentType;
  caption: string;
  hashtags: string[];
  mediaAssetIds: string[];
  scheduledDate?: Date;
  locationName?: string;
  locationId?: string;
  userTags?: {
    username: string;
    x: number;
    y: number;
  }[];
  firstComment?: string;
}

/**
 * Direct Post Response
 */
export interface DirectPostResponse {
  success: boolean;
  postId?: string;
  postUrl?: string;
  scheduledDate?: Date;
  error?: string;
}

/**
 * Analytics Dashboard Data
 */
export interface AnalyticsDashboardData {
  period: 'day' | 'week' | 'month' | '3months' | '6months' | 'year';
  startDate: Date;
  endDate: Date;
  platforms: SocialMediaPlatform[];
  summary: {
    totalPosts: number;
    totalImpressions: number;
    totalReach: number;
    totalEngagement: number;
    totalLikes: number;
    totalComments: number;
    totalShares: number;
    totalSaves: number;
    totalClicks: number;
    totalProfileVisits: number;
    totalFollowersGained: number;
    engagementRate: number;
  };
  trends: {
    date: Date;
    impressions: number;
    reach: number;
    engagement: number;
    followers: number;
  }[];
  topPosts: {
    postId: string;
    platform: SocialMediaPlatform;
    thumbnailUrl: string;
    engagement: number;
    likes: number;
    comments: number;
    shares: number;
    saves: number;
  }[];
  audienceGrowth: {
    date: Date;
    followers: number;
    followersGained: number;
    followersLost: number;
  }[];
  contentPerformance: {
    contentType: ContentType;
    count: number;
    avgEngagement: number;
    avgReach: number;
    avgImpressions: number;
  }[];
}

/**
 * Analytics Request
 */
export interface AnalyticsRequest {
  period: 'day' | 'week' | 'month' | '3months' | '6months' | 'year';
  startDate?: Date;
  endDate?: Date;
  platforms?: SocialMediaPlatform[];
  postIds?: string[];
}

/**
 * Instagram API Error
 */
export interface InstagramAPIError {
  code: number;
  message: string;
  type: string;
  fbtrace_id: string;
}

/**
 * Instagram API Response
 */
export interface InstagramAPIResponse<T> {
  data?: T;
  error?: InstagramAPIError;
}

/**
 * Instagram Media Container
 */
export interface InstagramMediaContainer {
  id: string;
  media_type: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM';
  media_url: string;
  permalink: string;
  thumbnail_url?: string;
  timestamp: string;
  caption?: string;
  username: string;
  children?: {
    data: {
      id: string;
      media_type: 'IMAGE' | 'VIDEO';
      media_url: string;
      thumbnail_url?: string;
    }[];
  };
}
