import {
  MediaAsset,
  MediaAssetCreateRequest,
  MediaAssetUpdateRequest,
  MediaAssetSearchRequest,
  MediaAssetSearchResponse
} from '../types/socialMediaEnhanced';
import { SocialMediaPlatform, ContentType } from '../types/socialMedia';

// Local storage key for media assets
const MEDIA_ASSETS_STORAGE_KEY = 'chitraMarketingTools_mediaAssets';

// Maximum file size in bytes (10 MB)
export const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Allowed file types
export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp'
];

export const ALLOWED_VIDEO_TYPES = [
  'video/mp4',
  'video/webm',
  'video/quicktime'
];

export const ALLOWED_DOCUMENT_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
];

export const ALLOWED_FILE_TYPES = [
  ...ALLOWED_IMAGE_TYPES,
  ...ALLOWED_VIDEO_TYPES,
  ...ALLOWED_DOCUMENT_TYPES
];

/**
 * Load media assets from localStorage
 */
const loadMediaAssets = (): MediaAsset[] => {
  try {
    const storedData = localStorage.getItem(MEDIA_ASSETS_STORAGE_KEY);
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      // Convert date strings to Date objects
      return parsedData.map((asset: any) => ({
        ...asset,
        uploadDate: new Date(asset.uploadDate)
      }));
    }
    return [];
  } catch (error) {
    console.error('Error loading media assets:', error);
    return [];
  }
};

/**
 * Save media assets to localStorage
 */
const saveMediaAssets = (assets: MediaAsset[]): void => {
  try {
    localStorage.setItem(MEDIA_ASSETS_STORAGE_KEY, JSON.stringify(assets));
  } catch (error) {
    console.error('Error saving media assets:', error);
  }
};

/**
 * Get all media assets
 */
export const getAllMediaAssets = async (): Promise<MediaAsset[]> => {
  return loadMediaAssets();
};

/**
 * Get media asset by ID
 */
export const getMediaAssetById = async (id: string): Promise<MediaAsset | null> => {
  const assets = loadMediaAssets();
  return assets.find(asset => asset.id === id) || null;
};

/**
 * Create a new media asset
 */
export const createMediaAsset = async (request: MediaAssetCreateRequest): Promise<MediaAsset> => {
  return new Promise((resolve, reject) => {
    const { file, name, tags, description, platform, contentType } = request;
    
    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      reject(new Error(`File size exceeds the maximum allowed size of ${MAX_FILE_SIZE / (1024 * 1024)} MB`));
      return;
    }
    
    // Validate file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      reject(new Error('File type not allowed. Please upload an image, video, or document.'));
      return;
    }
    
    // Determine asset type
    let assetType: 'image' | 'video' | 'document';
    if (ALLOWED_IMAGE_TYPES.includes(file.type)) {
      assetType = 'image';
    } else if (ALLOWED_VIDEO_TYPES.includes(file.type)) {
      assetType = 'video';
    } else {
      assetType = 'document';
    }
    
    // Read file as data URL
    const reader = new FileReader();
    
    reader.onload = async (event) => {
      try {
        const dataUrl = event.target?.result as string;
        
        // Generate thumbnail for images and videos
        let thumbnailUrl: string | undefined;
        let dimensions: { width: number; height: number } | undefined;
        let duration: number | undefined;
        
        if (assetType === 'image') {
          // For images, create a thumbnail and get dimensions
          const img = new Image();
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
            img.src = dataUrl;
          });
          
          dimensions = {
            width: img.width,
            height: img.height
          };
          
          // Create thumbnail
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const MAX_THUMB_SIZE = 200;
          
          let thumbWidth = img.width;
          let thumbHeight = img.height;
          
          if (thumbWidth > thumbHeight) {
            if (thumbWidth > MAX_THUMB_SIZE) {
              thumbHeight = Math.round(thumbHeight * (MAX_THUMB_SIZE / thumbWidth));
              thumbWidth = MAX_THUMB_SIZE;
            }
          } else {
            if (thumbHeight > MAX_THUMB_SIZE) {
              thumbWidth = Math.round(thumbWidth * (MAX_THUMB_SIZE / thumbHeight));
              thumbHeight = MAX_THUMB_SIZE;
            }
          }
          
          canvas.width = thumbWidth;
          canvas.height = thumbHeight;
          
          ctx?.drawImage(img, 0, 0, thumbWidth, thumbHeight);
          thumbnailUrl = canvas.toDataURL('image/jpeg', 0.7);
        } else if (assetType === 'video') {
          // For videos, we would normally create a thumbnail from the video
          // But this requires more complex processing, so we'll use a placeholder
          thumbnailUrl = '/assets/video-thumbnail-placeholder.png';
        }
        
        // Create new media asset
        const newAsset: MediaAsset = {
          id: `asset-${Date.now()}`,
          name: name || file.name,
          type: assetType,
          mimeType: file.type,
          size: file.size,
          dataUrl,
          thumbnailUrl,
          uploadDate: new Date(),
          tags: tags || [],
          description,
          dimensions,
          duration,
          platform,
          contentType
        };
        
        // Get existing assets
        const assets = loadMediaAssets();
        
        // Add new asset
        assets.push(newAsset);
        
        // Save to localStorage
        saveMediaAssets(assets);
        
        resolve(newAsset);
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
    
    reader.readAsDataURL(file);
  });
};

/**
 * Update a media asset
 */
export const updateMediaAsset = async (request: MediaAssetUpdateRequest): Promise<MediaAsset | null> => {
  const assets = loadMediaAssets();
  const assetIndex = assets.findIndex(asset => asset.id === request.id);
  
  if (assetIndex === -1) {
    return null;
  }
  
  const updatedAsset = {
    ...assets[assetIndex],
    ...request
  };
  
  assets[assetIndex] = updatedAsset;
  saveMediaAssets(assets);
  
  return updatedAsset;
};

/**
 * Delete a media asset
 */
export const deleteMediaAsset = async (id: string): Promise<boolean> => {
  const assets = loadMediaAssets();
  const updatedAssets = assets.filter(asset => asset.id !== id);
  
  if (updatedAssets.length === assets.length) {
    return false;
  }
  
  saveMediaAssets(updatedAssets);
  return true;
};

/**
 * Search media assets
 */
export const searchMediaAssets = async (request: MediaAssetSearchRequest): Promise<MediaAssetSearchResponse> => {
  const assets = loadMediaAssets();
  
  let filteredAssets = assets;
  
  // Filter by type
  if (request.type) {
    filteredAssets = filteredAssets.filter(asset => asset.type === request.type);
  }
  
  // Filter by platform
  if (request.platform) {
    filteredAssets = filteredAssets.filter(asset => asset.platform === request.platform);
  }
  
  // Filter by content type
  if (request.contentType) {
    filteredAssets = filteredAssets.filter(asset => asset.contentType === request.contentType);
  }
  
  // Filter by tags
  if (request.tags && request.tags.length > 0) {
    filteredAssets = filteredAssets.filter(asset => 
      request.tags!.some(tag => asset.tags.includes(tag))
    );
  }
  
  // Filter by date range
  if (request.dateRange) {
    filteredAssets = filteredAssets.filter(asset => {
      const assetDate = asset.uploadDate;
      return assetDate >= request.dateRange!.start && assetDate <= request.dateRange!.end;
    });
  }
  
  // Filter by search query
  if (request.query) {
    const query = request.query.toLowerCase();
    filteredAssets = filteredAssets.filter(asset => 
      asset.name.toLowerCase().includes(query) || 
      (asset.description && asset.description.toLowerCase().includes(query)) ||
      asset.tags.some(tag => tag.toLowerCase().includes(query))
    );
  }
  
  return {
    assets: filteredAssets,
    total: filteredAssets.length
  };
};
