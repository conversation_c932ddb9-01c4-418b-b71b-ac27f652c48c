import React from "react";
import { cn } from "../../lib/utils";

interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "primary" | "secondary" | "accent" | "outline" | "destructive" | "success" | "warning" | "info";
  size?: "default" | "sm" | "lg";
}

/**
 * Badge component for displaying status or labels
 *
 * @example
 * <Badge>Default</Badge>
 * <Badge variant="primary">Primary</Badge>
 * <Badge variant="outline">Outline</Badge>
 * <Badge variant="success" size="lg">Success</Badge>
 */
const Badge: React.FC<BadgeProps> = ({
  className = "",
  variant = "default",
  size = "default",
  ...props
}) => {
  // Variant styles
  const variantStyles = {
    default: "bg-gray-900 text-white hover:bg-gray-800 dark:bg-gray-700 dark:text-gray-100",
    primary: "bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700",
    secondary: "bg-secondary-500 text-white hover:bg-secondary-600 dark:bg-secondary-600 dark:hover:bg-secondary-700",
    accent: "bg-accent-500 text-white hover:bg-accent-600 dark:bg-accent-600 dark:hover:bg-accent-700",
    outline: "bg-transparent border border-gray-300 text-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800",
    destructive: "bg-red-500 text-white hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700",
    success: "bg-success-500 text-white hover:bg-success-600 dark:bg-success-600 dark:hover:bg-success-700",
    warning: "bg-warning-500 text-white hover:bg-warning-600 dark:bg-warning-600 dark:hover:bg-warning-700",
    info: "bg-info-500 text-white hover:bg-info-600 dark:bg-info-600 dark:hover:bg-info-700"
  };

  // Size styles
  const sizeStyles = {
    default: "px-2.5 py-0.5 text-xs",
    sm: "px-2 py-0.5 text-[0.625rem]",
    lg: "px-3 py-1 text-sm"
  };

  return (
    <div
      className={cn(
        "inline-flex items-center justify-center rounded-full font-semibold transition-colors",
        variantStyles[variant],
        sizeStyles[size],
        className
      )}
      {...props}
    />
  );
};

export { Badge };
