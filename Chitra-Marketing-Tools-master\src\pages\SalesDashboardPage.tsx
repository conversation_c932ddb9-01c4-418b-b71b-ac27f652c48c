import React from 'react';
import { BarChart3 } from 'lucide-react';

export default function SalesDashboardPage() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <BarChart3 className="h-6 w-6 text-blue-600 mr-2" />
          <h1 className="text-2xl font-semibold">Sales Dashboard</h1>
        </div>
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="iframe-container">
          <iframe 
            width="100%" 
            height="2000" 
            src="https://lookerstudio.google.com/embed/reporting/7d2e0f57-a983-42d0-977d-878b3bfeb662/page/p_p1nucldxpd" 
            frameBorder="0" 
            style={{ border: 0 }} 
            allowFullScreen 
            sandbox="allow-storage-access-by-user-activation allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox"
          ></iframe>
        </div>
      </div>
    </div>
  );
}
