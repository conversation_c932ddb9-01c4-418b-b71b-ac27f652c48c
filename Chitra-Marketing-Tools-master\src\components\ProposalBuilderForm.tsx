import React, { useState, useEffect } from 'react';
import {
  Plus,
  Minus,
  Upload,
  Search,
  X,
  FileText,
  Loader2,
  Info
} from 'lucide-react';
import {
  ProposalBuilderFormData,
  ProposalBuilderType,
  ProposalProduct,
  CompanyInfo,
  OfferDetails
} from '../types/proposalBuilder';
import { Product } from '../types';
import { fetchProducts } from '../services/productService';
import {
  Select,
  SelectItem
} from './ui/select';
import { MultiSelectDropdown, MultiSelectOption } from './ui/multi-select-dropdown';

interface ProposalBuilderFormProps {
  onSubmit: (data: ProposalBuilderFormData) => void;
  isSubmitting?: boolean;
}

// Local storage key for saving draft
const DRAFT_STORAGE_KEY = 'proposal_builder_draft';

// Initial company info
const initialCompanyInfo: CompanyInfo = {
  companyName: '',
  contactPerson: '',
  contactTitle: '',
  contactEmail: '',
  contactPhone: '',
  companyAddress: '',
  industry: ''
};

// Initial offer details
const initialOfferDetails: OfferDetails = {
  offerTitle: '',
  validUntil: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split('T')[0],
  specialTerms: '',
  paymentTerms: 'Net 30 days',
  deliveryTerms: 'FOB Destination'
};

const ProposalBuilderForm: React.FC<ProposalBuilderFormProps> = ({
  onSubmit,
  isSubmitting = false
}) => {
  // Form data state
  const [formData, setFormData] = useState<ProposalBuilderFormData>({
    // Basic information
    proposalType: 'bundling',
    proposalTitle: '',

    // Company information
    companyInfo: initialCompanyInfo,

    // Products
    mainProducts: [],
    bundledProducts: [],

    // Offer details
    offerDetails: initialOfferDetails,

    // Additional information
    customerPain: '',
    competitorInfo: '',
    previousDeals: '',
    additionalNotes: '',

    // Custom proposal fields
    customProposalType: '',
    customProposalRequirements: ''
  });

  // Products state
  const [availableProducts, setAvailableProducts] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState<boolean>(false);
  const [productSearchTerm, setProductSearchTerm] = useState<string>('');

  // File upload state
  const [supportingFile, setSupportingFile] = useState<File | null>(null);

  // Load products on component mount
  useEffect(() => {
    const loadProducts = async () => {
      setIsLoadingProducts(true);
      try {
        const products = await fetchProducts();
        setAvailableProducts(products);
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    loadProducts();
  }, []);

  // Load draft from local storage on component mount
  useEffect(() => {
    const draft = localStorage.getItem(DRAFT_STORAGE_KEY);
    if (draft) {
      try {
        const parsedDraft = JSON.parse(draft);
        setFormData(parsedDraft);
      } catch (error) {
        console.error('Error parsing draft:', error);
      }
    }
  }, []);

  // Save draft to local storage when form data changes
  useEffect(() => {
    localStorage.setItem(DRAFT_STORAGE_KEY, JSON.stringify(formData));
  }, [formData]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Handle nested properties
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof ProposalBuilderFormData],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle proposal type change
  const handleProposalTypeChange = (type: string) => {
    setFormData(prev => ({
      ...prev,
      proposalType: type as ProposalBuilderType
    }));
  };

  // Handle product selection
  const handleProductSelection = (productIds: string[], isMainProduct: boolean) => {
    const selectedProducts: ProposalProduct[] = productIds.map(id => {
      const product = availableProducts.find(p => p.id === id);
      return {
        product: product!,
        quantity: 1,
        discount: 0
      };
    });

    if (isMainProduct) {
      setFormData(prev => ({
        ...prev,
        mainProducts: selectedProducts
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        bundledProducts: selectedProducts
      }));
    }
  };

  // Handle product quantity change
  const handleProductQuantityChange = (productId: string, quantity: number, isMainProduct: boolean) => {
    if (isMainProduct) {
      setFormData(prev => ({
        ...prev,
        mainProducts: prev.mainProducts.map(p =>
          p.product.id === productId ? { ...p, quantity } : p
        )
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        bundledProducts: prev.bundledProducts?.map(p =>
          p.product.id === productId ? { ...p, quantity } : p
        ) || []
      }));
    }
  };

  // Handle product discount change
  const handleProductDiscountChange = (productId: string, discount: number, isMainProduct: boolean) => {
    if (isMainProduct) {
      setFormData(prev => ({
        ...prev,
        mainProducts: prev.mainProducts.map(p =>
          p.product.id === productId ? { ...p, discount } : p
        )
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        bundledProducts: prev.bundledProducts?.map(p =>
          p.product.id === productId ? { ...p, discount } : p
        ) || []
      }));
    }
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSupportingFile(e.target.files[0]);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Submit the form data without any validation
    onSubmit(formData);
  };

  // Convert products to multi-select options
  const productOptions: MultiSelectOption[] = availableProducts
    .filter(p => productSearchTerm === '' ||
      p.materialDescription.toLowerCase().includes(productSearchTerm.toLowerCase()))
    .map(p => ({
      value: p.id,
      label: `${p.materialDescription} (${p.materialNumber})`
    }));

  // Get selected product IDs
  const selectedMainProductIds = formData.mainProducts.map(p => p.product.id);
  const selectedBundledProductIds = formData.bundledProducts?.map(p => p.product.id) || [];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Proposal Type */}
      <div className="space-y-2">
        <label htmlFor="proposalType" className="block text-sm font-medium text-gray-700">
          Tipe Proposal
        </label>
        <Select
          value={formData.proposalType}
          onValueChange={handleProposalTypeChange}
        >
          <SelectItem value="bundling">Proposal Bundling Produk (Min. 5 halaman)</SelectItem>
          <SelectItem value="michelin-first">Proposal Michelin First (Min. 5 halaman)</SelectItem>
          <SelectItem value="product-warranty">Proposal Garansi Produk (Min. 5 halaman)</SelectItem>
          <SelectItem value="custom">Proposal Custom (Min. 5 halaman)</SelectItem>
        </Select>

        {/* Proposal Type Description */}
        <div className="mt-2 p-3 bg-blue-50 border border-blue-100 rounded-md text-sm text-blue-800">
          {formData.proposalType === 'bundling' && (
            <>
              <p className="font-medium mb-1">Proposal Bundling Produk</p>
              <p>Proposal ini ditujukan untuk tim pengadaan dengan fokus pada penawaran bundling produk yang memberikan nilai tambah dan efisiensi biaya. Mencakup analisis kebutuhan pelanggan, solusi bundling, perbandingan dengan kompetitor, dan manfaat jangka panjang.</p>
            </>
          )}
          {formData.proposalType === 'michelin-first' && (
            <>
              <p className="font-medium mb-1">Proposal Michelin First</p>
              <p>Proposal ini fokus pada pengenalan program Michelin First kepada pelanggan baru, menekankan nilai unik Michelin, kualitas, manfaat kinerja, dan keunggulan biaya jangka panjang. Mencakup analisis kebutuhan, solusi Michelin First, studi kasus, dan skema komersial.</p>
            </>
          )}
          {formData.proposalType === 'product-warranty' && (
            <>
              <p className="font-medium mb-1">Proposal Garansi Produk</p>
              <p>Proposal ini merinci penawaran garansi produk, mencakup persyaratan cakupan, durasi, proses klaim, dan nilai yang diberikan dalam hal pengurangan risiko. Menekankan komitmen perusahaan terhadap kualitas dan layanan purna jual.</p>
            </>
          )}
          {formData.proposalType === 'custom' && (
            <>
              <p className="font-medium mb-1">Proposal Custom</p>
              <p>Proposal kustom yang dapat disesuaikan dengan kebutuhan spesifik Anda. Mencakup pendahuluan masalah, analisa kebutuhan, solusi yang diajukan, skema penawaran, pembeda dari kompetitor, garansi & dukungan, serta penutup & arah tindak lanjut.</p>
            </>
          )}
        </div>
      </div>

      {/* Custom Proposal Type (only if proposalType is 'custom') */}
      {formData.proposalType === 'custom' && (
        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="customProposalType" className="block text-sm font-medium text-gray-700">
              Jenis Proposal Custom
            </label>
            <input
              type="text"
              id="customProposalType"
              name="customProposalType"
              value={formData.customProposalType}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Contoh: Proposal Konsinyasi, Proposal Trade-In, dll."
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="customProposalRequirements" className="block text-sm font-medium text-gray-700">
              Kebutuhan Khusus Proposal
            </label>
            <textarea
              id="customProposalRequirements"
              name="customProposalRequirements"
              value={formData.customProposalRequirements}
              onChange={handleInputChange}
              rows={4}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Jelaskan kebutuhan khusus untuk proposal ini, seperti: fokus pada efisiensi biaya, penekanan pada keunggulan teknologi, dll."
            />
          </div>
        </div>
      )}

      {/* Proposal Title */}
      <div className="space-y-2">
        <label htmlFor="proposalTitle" className="block text-sm font-medium text-gray-700">
          Judul Proposal
        </label>
        <input
          type="text"
          id="proposalTitle"
          name="proposalTitle"
          value={formData.proposalTitle}
          onChange={handleInputChange}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
          placeholder="Contoh: Proposal Bundling Ban Michelin XZL 2023"
        />
      </div>

      {/* Company Information */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Informasi Perusahaan Klien</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="companyInfo.companyName" className="block text-sm font-medium text-gray-700">
              Nama Perusahaan
            </label>
            <input
              type="text"
              id="companyInfo.companyName"
              name="companyInfo.companyName"
              value={formData.companyInfo.companyName}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="companyInfo.industry" className="block text-sm font-medium text-gray-700">
              Industri
            </label>
            <input
              type="text"
              id="companyInfo.industry"
              name="companyInfo.industry"
              value={formData.companyInfo.industry}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Contoh: Pertambangan, Konstruksi, dll."
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="companyInfo.contactPerson" className="block text-sm font-medium text-gray-700">
              Nama Kontak
            </label>
            <input
              type="text"
              id="companyInfo.contactPerson"
              name="companyInfo.contactPerson"
              value={formData.companyInfo.contactPerson}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="companyInfo.contactTitle" className="block text-sm font-medium text-gray-700">
              Jabatan
            </label>
            <input
              type="text"
              id="companyInfo.contactTitle"
              name="companyInfo.contactTitle"
              value={formData.companyInfo.contactTitle}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="companyInfo.contactEmail" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              type="email"
              id="companyInfo.contactEmail"
              name="companyInfo.contactEmail"
              value={formData.companyInfo.contactEmail}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="companyInfo.contactPhone" className="block text-sm font-medium text-gray-700">
              Telepon
            </label>
            <input
              type="text"
              id="companyInfo.contactPhone"
              name="companyInfo.contactPhone"
              value={formData.companyInfo.contactPhone}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div className="space-y-2 md:col-span-2">
            <label htmlFor="companyInfo.companyAddress" className="block text-sm font-medium text-gray-700">
              Alamat
            </label>
            <textarea
              id="companyInfo.companyAddress"
              name="companyInfo.companyAddress"
              value={formData.companyInfo.companyAddress}
              onChange={handleInputChange}
              rows={2}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Products Section */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Produk</h3>

        {/* Product Search */}
        <div className="mb-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Cari produk..."
              value={productSearchTerm}
              onChange={(e) => setProductSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full"
            />
            <Search className="absolute left-3 top-2.5 text-gray-400" size={16} />
            {productSearchTerm && (
              <button
                type="button"
                onClick={() => setProductSearchTerm('')}
                className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
              >
                <X size={16} />
              </button>
            )}
          </div>
        </div>

        {/* Main Products */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Produk Utama / Produk yang Dibundling
          </label>

          <MultiSelectDropdown
            options={productOptions}
            selectedValues={selectedMainProductIds}
            onChange={(values) => handleProductSelection(values, true)}
            placeholder="Pilih produk utama..."
          />

          {formData.mainProducts.length > 0 && (
            <div className="mt-3 space-y-2">
              {formData.mainProducts.map((product) => (
                <div key={product.product.id} className="flex items-center space-x-2 p-2 bg-white rounded border">
                  <div className="flex-grow">
                    <div className="font-medium text-sm">{product.product.materialDescription}</div>
                    <div className="text-xs text-gray-500">SKU: {product.product.materialNumber}</div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="number"
                      min="1"
                      value={product.quantity}
                      onChange={(e) => handleProductQuantityChange(product.product.id, parseInt(e.target.value) || 1, true)}
                      className="w-16 text-sm border border-gray-300 rounded p-1"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        setFormData(prev => ({
                          ...prev,
                          mainProducts: prev.mainProducts.filter(p => p.product.id !== product.product.id)
                        }));
                      }}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Bundled Products (only for bundling proposal type) */}
        {formData.proposalType === 'bundling' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Produk Tambahan (Bundling)
            </label>

            <MultiSelectDropdown
              options={productOptions}
              selectedValues={selectedBundledProductIds}
              onChange={(values) => handleProductSelection(values, false)}
              placeholder="Pilih produk tambahan..."
            />

            {formData.bundledProducts && formData.bundledProducts.length > 0 && (
              <div className="mt-3 space-y-2">
                {formData.bundledProducts.map((product) => (
                  <div key={product.product.id} className="flex items-center space-x-2 p-2 bg-white rounded border">
                    <div className="flex-grow">
                      <div className="font-medium text-sm">{product.product.materialDescription}</div>
                      <div className="text-xs text-gray-500">SKU: {product.product.materialNumber}</div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="number"
                        min="1"
                        value={product.quantity}
                        onChange={(e) => handleProductQuantityChange(product.product.id, parseInt(e.target.value) || 1, false)}
                        className="w-16 text-sm border border-gray-300 rounded p-1"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setFormData(prev => ({
                            ...prev,
                            bundledProducts: prev.bundledProducts?.filter(p => p.product.id !== product.product.id) || []
                          }));
                        }}
                        className="text-red-500 hover:text-red-700"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Offer Details */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Detail Penawaran</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="offerDetails.offerTitle" className="block text-sm font-medium text-gray-700">
              Judul Penawaran
            </label>
            <input
              type="text"
              id="offerDetails.offerTitle"
              name="offerDetails.offerTitle"
              value={formData.offerDetails.offerTitle}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Contoh: Penawaran Spesial Bundling Ban Michelin"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="offerDetails.validUntil" className="block text-sm font-medium text-gray-700">
              Berlaku Hingga
            </label>
            <input
              type="date"
              id="offerDetails.validUntil"
              name="offerDetails.validUntil"
              value={formData.offerDetails.validUntil}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="offerDetails.paymentTerms" className="block text-sm font-medium text-gray-700">
              Syarat Pembayaran
            </label>
            <input
              type="text"
              id="offerDetails.paymentTerms"
              name="offerDetails.paymentTerms"
              value={formData.offerDetails.paymentTerms}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="offerDetails.deliveryTerms" className="block text-sm font-medium text-gray-700">
              Syarat Pengiriman
            </label>
            <input
              type="text"
              id="offerDetails.deliveryTerms"
              name="offerDetails.deliveryTerms"
              value={formData.offerDetails.deliveryTerms}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
            />
          </div>

          <div className="space-y-2 md:col-span-2">
            <label htmlFor="offerDetails.specialTerms" className="block text-sm font-medium text-gray-700">
              Syarat & Ketentuan Khusus
            </label>
            <textarea
              id="offerDetails.specialTerms"
              name="offerDetails.specialTerms"
              value={formData.offerDetails.specialTerms}
              onChange={handleInputChange}
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Masukkan syarat dan ketentuan khusus untuk penawaran ini..."
            />
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Informasi Tambahan</h3>

        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="customerPain" className="block text-sm font-medium text-gray-700">
              Pain Points Pelanggan
            </label>
            <textarea
              id="customerPain"
              name="customerPain"
              value={formData.customerPain}
              onChange={handleInputChange}
              rows={2}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Masalah atau kebutuhan yang dihadapi pelanggan..."
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="competitorInfo" className="block text-sm font-medium text-gray-700">
              Informasi Kompetitor
            </label>
            <textarea
              id="competitorInfo"
              name="competitorInfo"
              value={formData.competitorInfo}
              onChange={handleInputChange}
              rows={2}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Informasi tentang penawaran kompetitor..."
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="previousDeals" className="block text-sm font-medium text-gray-700">
              Transaksi Sebelumnya
            </label>
            <textarea
              id="previousDeals"
              name="previousDeals"
              value={formData.previousDeals}
              onChange={handleInputChange}
              rows={2}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Riwayat transaksi dengan pelanggan ini..."
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="additionalNotes" className="block text-sm font-medium text-gray-700">
              Catatan Tambahan
            </label>
            <textarea
              id="additionalNotes"
              name="additionalNotes"
              value={formData.additionalNotes}
              onChange={handleInputChange}
              rows={2}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              placeholder="Informasi lain yang perlu dimasukkan dalam proposal..."
            />
          </div>
        </div>
      </div>

      {/* File Upload */}
      <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
        <h3 className="text-lg font-medium text-gray-800 mb-4">File Pendukung (Opsional)</h3>

        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <input
            type="file"
            id="supportingFile"
            onChange={handleFileUpload}
            className="hidden"
          />

          <button
            type="button"
            onClick={() => document.getElementById('supportingFile')?.click()}
            className="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Upload size={16} className="mr-2" />
            Pilih File
          </button>

          <p className="mt-2 text-sm text-gray-500">
            Unggah file pendukung seperti spesifikasi produk, brosur, dll.
          </p>

          {supportingFile && (
            <div className="mt-4 flex items-center justify-center p-2 bg-blue-50 border border-blue-200 rounded-md">
              <FileText size={16} className="text-blue-500 mr-2" />
              <span className="text-sm text-blue-700">{supportingFile.name}</span>
              <button
                type="button"
                onClick={() => setSupportingFile(null)}
                className="ml-2 text-blue-500 hover:text-blue-700"
              >
                <X size={14} />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isSubmitting}
          className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${
            isSubmitting ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          }`}
        >
          {isSubmitting ? (
            <>
              <Loader2 size={16} className="mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <FileText size={16} className="mr-2" />
              Generate Proposal
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default ProposalBuilderForm;
