import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import { BundleItem, PricingResult, Customer } from '../types';
import { formatCurrency } from '../utils/pricing';

// Add autotable to jsPDF
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

export function generateEnhancedQuotationPDF(
  bundleItems: BundleItem[],
  pricingResult: PricingResult,
  additionalInfo: string,
  customer?: Customer | null
): void {
  try {
    console.log('Generating enhanced PDF quotation...');
    
    // Create a new PDF document
    const doc = new jsPDF();
    
    // Add header with logo and quotation title
    addHeader(doc);
    
    // Add company and customer information
    addCompanyAndCustomerInfo(doc, customer);
    
    // Add quotation details
    addQuotationDetails(doc);
    
    // Add product table
    addProductTable(doc, bundleItems);
    
    // Add totals
    addTotals(doc, pricingResult);
    
    // Add notes and terms
    addNotesAndTerms(doc, additionalInfo);
    
    // Add footer
    addFooter(doc);
    
    // Save the PDF with customer name if available
    const filename = customer 
      ? `BundleBoost-Quotation-${customer.name.replace(/\\s+/g, '_')}.pdf`
      : 'BundleBoost-Quotation.pdf';
    
    doc.save(filename);
    console.log(`Enhanced PDF saved successfully as ${filename}`);
    
    // Show success message with customer name if available
    const successMessage = customer
      ? `Quotation for ${customer.name} generated successfully!`
      : 'PDF quotation generated successfully!';
    
    alert(successMessage);
  } catch (error) {
    console.error('Error generating enhanced PDF:', error);
    alert('Failed to generate enhanced PDF. Please check the console for details.');
  }
}

function addHeader(doc: jsPDF): void {
  // Add company logo (blue circle with company name)
  doc.setDrawColor(0, 120, 200); // Blue color for circle
  doc.setFillColor(0, 120, 200);
  doc.circle(35, 25, 10, 'F');
  
  // Add inner circle pattern (simplified)
  doc.setDrawColor(255, 255, 255);
  doc.setFillColor(255, 255, 255);
  doc.circle(35, 25, 7, 'S');
  doc.circle(35, 25, 5, 'S');
  doc.circle(35, 25, 3, 'S');
  
  // Add company name
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 120, 200); // Blue color
  doc.setFontSize(22);
  doc.text('BundleBoost', 50, 22);
  
  // Add company tagline
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(10);
  doc.setTextColor(100, 150, 0); // Green color
  doc.text('Your Trusted Partner', 50, 28);
  
  // Add QUOTATION title on the right
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(80, 80, 200); // Purple-blue color
  doc.setFontSize(18);
  doc.text('QUOTATION', 200, 22, { align: 'right' });
  
  // Add quotation number
  const today = new Date();
  const quotationNumber = `QUO-BP-${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;
  doc.setFontSize(10);
  doc.text(`# ${quotationNumber}`, 200, 28, { align: 'right' });
  
  // Add horizontal line
  doc.setDrawColor(220, 220, 220);
  doc.setLineWidth(0.5);
  doc.line(20, 35, 190, 35);
}

function addCompanyAndCustomerInfo(doc: jsPDF, customer?: Customer | null): void {
  // Company information (left side)
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(11);
  doc.text('PT BundleBoost Indonesia', 25, 45);
  
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  doc.text('Alamat: Jl. Industri No.123 Kawasan Industri', 25, 50);
  doc.text('Jakarta Utara | Kota Jakarta DKI Jakarta 14350', 25, 55);
  
  // Customer information (right side) with "To" label
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(0, 0, 0);
  doc.setFontSize(11);
  doc.text('To', 140, 45);
  
  if (customer) {
    doc.setFont('helvetica', 'bold');
    doc.text(customer.company || customer.name, 140, 50);
    
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(9);
    doc.text(`${customer.address}`, 140, 55);
    doc.text(`${customer.email} | ${customer.phone}`, 140, 60);
  } else {
    doc.setFont('helvetica', 'normal');
    doc.text('Customer details not provided', 140, 50);
  }
  
  // Add "Ship to" information
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('Ship to', 140, 70);
  
  doc.setFont('helvetica', 'normal');
  if (customer) {
    doc.text(customer.address, 140, 75);
  } else {
    doc.text('Same as billing address', 140, 75);
  }
}

function addQuotationDetails(doc: jsPDF): void {
  // Quotation details (left side)
  const today = new Date();
  const futureDate = new Date();
  futureDate.setDate(today.getDate() + 30); // Valid for 30 days
  
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('Quo Date:', 25, 70);
  doc.text('Validity Quote:', 25, 75);
  doc.text('From:', 25, 80);
  
  doc.setFont('helvetica', 'normal');
  doc.text(today.toLocaleDateString('en-GB'), 60, 70);
  doc.text(futureDate.toLocaleDateString('en-GB'), 60, 75);
  doc.text('Sales Team BundleBoost', 60, 80);
}

function addProductTable(doc: jsPDF, bundleItems: BundleItem[]): void {
  try {
    // Prepare table data
    const tableColumn = ['#', 'Item', 'Qty', 'Price', 'Amount'];
    const tableRows = bundleItems.map((item, index) => [
      (index + 1).toString(),
      `${item.product.materialDescription}\nPart Number: ${item.product.oldMaterialNo}`,
      item.quantity.toString(),
      formatCurrency(item.product.price).replace('Rp ', ''),
      formatCurrency(item.product.price * item.quantity).replace('Rp ', '')
    ]);
    
    // Add the table
    (doc as any).autoTable({
      head: [tableColumn],
      body: tableRows,
      startY: 90,
      theme: 'grid',
      styles: {
        fontSize: 9,
        cellPadding: 4,
        lineColor: [200, 200, 200],
        lineWidth: 0.1,
      },
      headStyles: {
        fillColor: [65, 105, 225], // Royal blue
        textColor: [255, 255, 255],
        fontStyle: 'bold',
        halign: 'center',
      },
      columnStyles: {
        0: { cellWidth: 10, halign: 'center' }, // #
        1: { cellWidth: 'auto' }, // Item
        2: { cellWidth: 15, halign: 'center' }, // Qty
        3: { cellWidth: 30, halign: 'right' }, // Price
        4: { cellWidth: 30, halign: 'right' }, // Amount
      },
      alternateRowStyles: {
        fillColor: [245, 245, 255],
      },
      didDrawCell: (data: any) => {
        // Add part number in smaller text if in the item column
        if (data.column.index === 1 && data.cell.section === 'body') {
          // This is handled by the newline in the data
        }
      }
    });
    
    console.log('Table added successfully');
  } catch (error) {
    console.error('Error adding product table:', error);
    throw error;
  }
}

function addTotals(doc: jsPDF, pricingResult: PricingResult): void {
  // Get the final Y position after the table
  const finalY = (doc as any).lastAutoTable.finalY + 5;
  
  // Add Sub Total and Total
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(9);
  doc.text('Sub Total', 150, finalY + 5);
  doc.text('Total', 150, finalY + 12);
  
  // Add the amounts
  doc.setFont('helvetica', 'bold');
  doc.text(`Rp${pricingResult.recommendedPrice.toLocaleString()}`, 190, finalY + 5, { align: 'right' });
  doc.text(`Rp${pricingResult.recommendedPrice.toLocaleString()}`, 190, finalY + 12, { align: 'right' });
  
  // Add a light gray background for the totals section
  doc.setFillColor(245, 245, 245);
  doc.rect(140, finalY, 50, 15, 'F');
  
  // Re-add the text on top of the background
  doc.setFont('helvetica', 'normal');
  doc.setTextColor(0, 0, 0);
  doc.text('Sub Total', 150, finalY + 5);
  doc.text('Total', 150, finalY + 12);
  
  doc.setFont('helvetica', 'bold');
  doc.text(`Rp${pricingResult.recommendedPrice.toLocaleString()}`, 190, finalY + 5, { align: 'right' });
  doc.text(`Rp${pricingResult.recommendedPrice.toLocaleString()}`, 190, finalY + 12, { align: 'right' });
}

function addNotesAndTerms(doc: jsPDF, additionalInfo: string): void {
  // Get the final Y position after the totals
  const finalY = (doc as any).lastAutoTable.finalY + 20;
  
  // Add Notes
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('Note:', 25, finalY);
  
  doc.setFont('helvetica', 'normal');
  doc.text('Ready 1 Pcs', 25, finalY + 5);
  
  if (additionalInfo && additionalInfo.trim() !== '') {
    const lines = doc.splitTextToSize(additionalInfo, 150);
    doc.text(lines, 25, finalY + 12);
  }
  
  // Add Terms & Conditions
  const termsY = finalY + (additionalInfo ? 25 : 15);
  
  doc.setFont('helvetica', 'bold');
  doc.text('Terms & Conditions:', 25, termsY);
  
  doc.setFont('helvetica', 'normal');
  doc.text('Payment Terms : 30 days after Date Invoice', 25, termsY + 5);
  doc.text('Stock : Balikpapan', 25, termsY + 10);
  doc.text('DDP : Site', 25, termsY + 15);
  doc.text('Exclude Tax', 25, termsY + 20);
  
  // Add horizontal line
  const lineY = termsY + 25;
  doc.setDrawColor(100, 100, 100);
  doc.setLineWidth(0.5);
  doc.line(25, lineY, 190, lineY);
}

function addFooter(doc: jsPDF): void {
  // Get the final Y position after the terms
  const finalY = (doc as any).lastAutoTable.finalY + 70; // Approximate position after terms
  
  // Add company details in footer
  doc.setFont('helvetica', 'bold');
  doc.setFontSize(9);
  doc.text('PT. BUNDLEBOOST INDONESIA', 25, finalY);
  doc.text('BANK MANDIRI', 25, finalY + 5);
  
  doc.setFont('helvetica', 'normal');
  doc.text('Branch Cilandak, Jakarta Selatan 12560', 25, finalY + 10);
  doc.text('IDR A/C NO:127 - 000 - 00 - 17416', 25, finalY + 15);
  
  // Add horizontal line
  doc.setDrawColor(100, 100, 100);
  doc.setLineWidth(0.5);
  doc.line(25, finalY + 20, 190, finalY + 20);
}
