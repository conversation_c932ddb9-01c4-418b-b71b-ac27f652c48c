/**
 * Interface for customer-specific pricing
 */
export interface CustomerPricing {
  id: string;
  customerId: string;
  productId: string;
  specialPrice: number;
  validFrom: string;
  validTo: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for customer pricing form data
 */
export interface CustomerPricingFormData {
  customerId: string;
  productId: string;
  specialPrice: number;
  validFrom: string;
  validTo: string;
  notes?: string;
}
