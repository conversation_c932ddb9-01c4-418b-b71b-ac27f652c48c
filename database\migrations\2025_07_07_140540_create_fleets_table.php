<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('fleets', function (Blueprint $table) {
            $table->id();
            $table->string('id_fleet_list')->nullable()->unique();
            $table->string('customer_name')->nullable();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->string('site')->nullable();
            $table->string('status')->nullable();
            $table->string('location')->nullable();
            $table->string('kabupaten')->nullable();
            $table->string('kecamatan')->nullable();
            $table->string('unit_manufacture')->nullable();
            $table->string('model')->nullable();
            $table->string('tire_size')->nullable();
            $table->integer('tire_quantity')->nullable();
            $table->integer('unit_qty')->nullable();
            $table->integer('totaltire')->nullable();
            $table->string('annual')->nullable();
            $table->string('forecast')->nullable();
            $table->timestamps();

            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('fleets');
    }
};