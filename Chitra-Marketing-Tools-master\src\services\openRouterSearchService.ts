/**
 * Service for performing real-time internet searches using OpenRouter API
 * This service allows searching the web and summarizing results with proper source attribution
 */

// OpenRouter API key
const OPENROUTER_API_KEY = '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';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Models - using cost-effective models as requested
export const MODELS = {
  SEARCH_MODEL: 'openai/gpt-4.1-nano', // Cost-effective model for search
};

// Default model for web search
const DEFAULT_SEARCH_MODEL = MODELS.SEARCH_MODEL;

// Cache settings
const SEARCH_CACHE_KEY_PREFIX = 'web_search_cache_';
const CACHE_EXPIRY = 6 * 60 * 60 * 1000; // 6 hours

// Interface for search request
export interface WebSearchRequest {
  query: string;
  maxResults?: number;
  includeImages?: boolean;
  model?: string;
}

// Interface for search result
export interface WebSearchResult {
  summary: string;
  sources: {
    title: string;
    url: string;
    snippet?: string;
    publisher?: string;
  }[];
  timestamp: number;
}

/**
 * Perform a web search and summarize the results
 */
export const performWebSearch = async (request: WebSearchRequest): Promise<WebSearchResult> => {
  try {
    const { query, maxResults = 5, includeImages = false, model = DEFAULT_SEARCH_MODEL } = request;

    // Check cache first
    const cacheKey = `${SEARCH_CACHE_KEY_PREFIX}${query.toLowerCase().trim()}`;
    const cachedResult = getCachedResult(cacheKey);

    if (cachedResult) {
      console.log('Using cached search result for:', query);
      return cachedResult;
    }

    console.log('Performing web search for:', query);

    // Create system prompt for web search
    const systemPrompt = `You are a web search assistant that can search the internet in real-time to find information. Your task is to:

1. Search for information about: "${query}"
2. Summarize the most relevant and recent information
3. Provide a comprehensive summary with factual information
4. Include proper source attribution with ONLY REAL, WORKING URLs from reputable news sites, industry publications, or official company websites
5. Focus on credible sources and recent information (preferably from the last 2 years)
6. Respond in Bahasa Indonesia with clear, concise language
7. VERY IMPORTANT: Only include URLs that actually exist and can be visited. Do NOT make up or hallucinate URLs.
8. VERY IMPORTANT: Verify each URL is from a legitimate source like CNN Indonesia, Detik, Kompas, Bisnis.com, industry publications, or official company websites.
9. VERY IMPORTANT: Include the full URL including https:// prefix and ensure it points to a specific article, not just a homepage.

Your response must be in JSON format with the following structure:
{
  "summary": "A comprehensive summary of the search results in Bahasa Indonesia",
  "sources": [
    {
      "title": "Title of the source (use the actual article title)",
      "url": "Full URL to the source (must be a real, working URL with https://)",
      "snippet": "Brief relevant excerpt from the source",
      "publisher": "Name of the publishing website or organization"
    },
    ...more sources (up to ${maxResults} sources)
  ]
}`;

    // Create user prompt
    const userPrompt = `Tolong cari informasi terbaru tentang: "${query}"

Saya membutuhkan:
1. Ringkasan komprehensif dalam Bahasa Indonesia
2. Minimal ${maxResults} sumber dengan URL lengkap yang BENAR-BENAR ADA dan DAPAT DIAKSES
3. Informasi faktual dan terkini (2023-2024 jika memungkinkan)
4. Hanya gunakan sumber terpercaya seperti:
   - Portal berita nasional (Kompas, Detik, CNN Indonesia, Bisnis.com, dll)
   - Situs resmi perusahaan atau organisasi
   - Publikasi industri terkemuka
   - Jurnal atau laporan resmi
5. Pastikan setiap URL:
   - Dimulai dengan https://
   - Mengarah ke artikel spesifik (bukan halaman utama)
   - Telah Anda verifikasi keberadaannya
   - Berisi konten yang relevan dengan topik
${includeImages ? '6. Referensi ke gambar relevan jika ada' : ''}

SANGAT PENTING: Jangan pernah membuat URL palsu atau yang tidak ada. Lebih baik memberikan lebih sedikit sumber yang valid daripada banyak sumber yang tidak valid.

Berikan hasil dalam format JSON sesuai instruksi.`;

    // Make the API call to OpenRouter
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Web Search'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content;

    // Parse the JSON response
    const parsedContent = JSON.parse(content);

    // Validate and filter sources
    const validatedSources = (parsedContent.sources || []).filter(source => {
      // Check if URL is valid
      try {
        // Must have a URL
        if (!source.url) return false;

        // Must be a valid URL format
        const url = new URL(source.url);

        // Must use https protocol
        if (url.protocol !== 'https:') return false;

        // Must not be just a domain (should have a path)
        if (url.pathname === '/' || url.pathname === '') return false;

        // Must have a title
        if (!source.title || source.title.trim() === '') return false;

        // Blacklist certain domains known to be problematic or non-existent
        const blacklistedDomains = [
          'example.com',
          'domain.com',
          'website.com',
          'yourwebsite.com',
          'site.com',
          'url.com'
        ];

        if (blacklistedDomains.some(domain => url.hostname.includes(domain))) {
          return false;
        }

        return true;
      } catch (e) {
        // Invalid URL format
        return false;
      }
    });

    console.log(`Filtered sources: ${validatedSources.length} valid out of ${(parsedContent.sources || []).length} total`);

    // Create the result object with validated sources
    const result: WebSearchResult = {
      summary: parsedContent.summary,
      sources: validatedSources,
      timestamp: Date.now()
    };

    // Cache the result
    cacheResult(cacheKey, result);

    return result;
  } catch (error) {
    console.error('Error performing web search:', error);

    // Return a fallback result
    return {
      summary: `Maaf, terjadi kesalahan saat mencari informasi tentang "${request.query}". Silakan coba lagi nanti.`,
      sources: [
        {
          title: "Sumber Informasi Industri Ban",
          url: "https://www.tireindustry.org/tire-industry-resources",
          snippet: "Sumber informasi resmi dari Tire Industry Association",
          publisher: "Tire Industry Association"
        },
        {
          title: "Portal Berita Industri Otomotif",
          url: "https://www.cnnindonesia.com/otomotif",
          snippet: "Berita terkini seputar industri otomotif dan ban",
          publisher: "CNN Indonesia"
        }
      ],
      timestamp: Date.now()
    };
  }
};

/**
 * Get cached search result
 */
const getCachedResult = (cacheKey: string): WebSearchResult | null => {
  try {
    const cachedData = localStorage.getItem(cacheKey);

    if (!cachedData) {
      return null;
    }

    const parsedData = JSON.parse(cachedData);

    // Check if cache has expired
    if (Date.now() - parsedData.timestamp > CACHE_EXPIRY) {
      localStorage.removeItem(cacheKey);
      return null;
    }

    return parsedData;
  } catch (error) {
    console.error('Error getting cached search result:', error);
    return null;
  }
};

/**
 * Cache search result
 */
const cacheResult = (cacheKey: string, result: WebSearchResult): void => {
  try {
    localStorage.setItem(cacheKey, JSON.stringify(result));
  } catch (error) {
    console.error('Error caching search result:', error);
  }
};
