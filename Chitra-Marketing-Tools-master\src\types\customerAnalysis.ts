/**
 * Types for the Customer Analysis feature
 */

import { Product } from './index';
import { Customer } from './customer';
import { FleetlistItem } from '../services/fleetlistService';
import { SalesRevenueItem } from '../services/salesRevenue2025Service';

/**
 * Integrated customer data from all sources
 */
export interface IntegratedCustomerData {
  customerId: string;
  customerName: string;
  matchedNames: string[]; // All variations of the customer name found in different data sources
  fleetData: FleetlistItem[];
  salesData: SalesRevenueItem[];
  recommendedProducts: Product[];
}

/**
 * Customer analysis result from AI
 */
export interface CustomerAnalysisResult {
  customerId: string;
  customerName: string;

  // Raw data
  salesData: SalesRevenueItem[];

  // Purchase history analysis
  purchaseHistory: {
    totalPurchases: number;
    totalRevenue: number;
    firstPurchaseDate: string;
    lastPurchaseDate: string;
    frequentlyPurchasedProducts: {
      productName: string;
      quantity: number;
      lastPurchaseDate: string;
    }[];
    purchaseTrend: 'increasing' | 'decreasing' | 'stable';
  };

  // Fleet analysis
  fleetAnalysis: {
    totalUnits: number;
    totalTires: number;
    uniqueTireSizes: string[];
    fleetComposition: {
      tireSize: string;
      quantity: number;
      percentage: number;
    }[];
  };

  // Product recommendations
  productRecommendations: {
    productName: string;
    reason: string;
    confidence: number; // 0-100
    potentialRevenue: number;
  }[];

  // Overall analysis text
  analysisText: string;
}

/**
 * Request for AI analysis
 */
export interface CustomerAnalysisRequest {
  customerName: string;
  fleetData: FleetlistItem[];
  salesData: SalesRevenueItem[];
  availableProducts: Product[];
}

/**
 * Customer name matching result
 */
export interface CustomerNameMatch {
  originalName: string;
  matchedNames: {
    name: string;
    source: 'customer' | 'fleet' | 'sales';
    confidence: number; // 0-100
  }[];
}

/**
 * Customer filter options
 */
export interface CustomerFilterOptions {
  searchQuery: string;
  hasPurchaseHistory: boolean;
  hasFleetData: boolean;
  minPurchaseAmount?: number;
  maxPurchaseAmount?: number;
  purchaseDateFrom?: string;
  purchaseDateTo?: string;
  productCategory?: string;
}
