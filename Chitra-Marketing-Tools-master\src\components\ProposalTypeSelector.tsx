import React from 'react';
import { ProposalType } from '../types/proposal';

interface ProposalTypeSelectorProps {
  value: ProposalType;
  onChange: (type: ProposalType) => void;
}

const ProposalTypeSelector: React.FC<ProposalTypeSelectorProps> = ({ value, onChange }) => {
  return (
    <div className="space-y-2">
      <label htmlFor="proposalType" className="block text-sm font-medium text-gray-700">
        Jenis Proposal
      </label>
      <select
        id="proposalType"
        value={value}
        onChange={(e) => onChange(e.target.value as ProposalType)}
        className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
      >
        <option value="bundling">Proposal Bundling</option>
        <option value="consignment">Proposal Konsinyasi</option>
        <option value="trade-in">Proposal Trade-In</option>
        <option value="performance-guarantee">Performance Guarantee</option>
        <option value="performance-warranty">Performance Warranty</option>
        <option value="first-michelin">First Michelin</option>
      </select>
    </div>
  );
};

export default ProposalTypeSelector;
