import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  X, 
  Save, 
  Tag, 
  Package, 
  DollarSign, 
  Users, 
  AlertCircle,
  Check
} from 'lucide-react';
import { 
  ScheduledPromotion,
  SeasonalInsight
} from '../types/seasonalMarketing';
import { saveScheduledPromotion } from '../services/seasonalMarketingService';
import { PromoType } from '../types/promotion';

interface PromoSchedulerProps {
  initialDate?: string;
  insight?: SeasonalInsight;
  onClose: () => void;
  onSave: (promotion: ScheduledPromotion) => void;
}

export default function PromoScheduler({ 
  initialDate, 
  insight,
  onClose, 
  onSave 
}: PromoSchedulerProps) {
  // State for form data
  const [formData, setFormData] = useState<Partial<ScheduledPromotion>>({
    id: '',
    name: '',
    description: '',
    startDate: initialDate || new Date().toISOString().split('T')[0],
    endDate: initialDate ? 
      new Date(new Date(initialDate).setDate(new Date(initialDate).getDate() + 30)).toISOString().split('T')[0] : 
      new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split('T')[0],
    promoType: '',
    products: [],
    status: 'planned',
    budget: 0,
    targetAudience: '',
    expectedImpact: 0,
    notes: '',
    color: 'blue'
  });

  // State for validation
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);

  // Populate form data from insight if provided
  useEffect(() => {
    if (insight) {
      setFormData(prev => ({
        ...prev,
        name: insight.title,
        description: insight.description,
        startDate: insight.startDate,
        endDate: insight.endDate,
        promoType: insight.recommendedPromoTypes[0] || '',
        products: insight.recommendedProducts,
        expectedImpact: insight.potentialImpact,
        notes: `Berdasarkan analisis musiman dengan skor rekomendasi ${insight.potentialImpact}/100 dan tingkat keyakinan ${insight.confidence}%.`
      }));
    }
  }, [insight]);

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle product change
  const handleProductChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const products = e.target.value.split('\n').filter(p => p.trim() !== '');
    setFormData(prev => ({ ...prev, products }));
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name) {
      newErrors.name = 'Nama promo harus diisi';
    }
    
    if (!formData.startDate) {
      newErrors.startDate = 'Tanggal mulai harus diisi';
    }
    
    if (!formData.endDate) {
      newErrors.endDate = 'Tanggal selesai harus diisi';
    } else if (formData.startDate && new Date(formData.endDate) <= new Date(formData.startDate)) {
      newErrors.endDate = 'Tanggal selesai harus setelah tanggal mulai';
    }
    
    if (!formData.promoType) {
      newErrors.promoType = 'Tipe promo harus dipilih';
    }
    
    if (!formData.products || formData.products.length === 0) {
      newErrors.products = 'Minimal satu produk harus diisi';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSaving(true);
    
    try {
      // Create a new promotion object
      const newPromotion: ScheduledPromotion = {
        id: formData.id || `promo-${Date.now()}`,
        name: formData.name || 'Promo Baru',
        description: formData.description || '',
        startDate: formData.startDate || new Date().toISOString().split('T')[0],
        endDate: formData.endDate || new Date(new Date().setDate(new Date().getDate() + 30)).toISOString().split('T')[0],
        promoType: formData.promoType || 'DISCOUNT_PERCENTAGE',
        products: formData.products || [],
        status: 'planned',
        budget: formData.budget || 0,
        targetAudience: formData.targetAudience || '',
        expectedImpact: formData.expectedImpact || 0,
        notes: formData.notes || '',
        color: formData.color || 'blue'
      };
      
      // Save the promotion
      const savedPromotion = saveScheduledPromotion(newPromotion);
      
      // Show success message
      setSaveSuccess(true);
      
      // Notify parent component
      onSave(savedPromotion);
      
      // Close the form after a delay
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (error) {
      console.error('Error saving promotion:', error);
      setErrors(prev => ({ ...prev, submit: 'Gagal menyimpan promo. Silakan coba lagi.' }));
    } finally {
      setIsSaving(false);
    }
  };

  // Available promo types
  const promoTypes = [
    { value: 'DISCOUNT_PERCENTAGE', label: 'Diskon Persentase' },
    { value: 'DISCOUNT_FIXED', label: 'Diskon Nominal Tetap' },
    { value: 'CASHBACK', label: 'Cashback' },
    { value: 'BUY_GET', label: 'Buy X Get Y' },
    { value: 'BUNDLING', label: 'Bundling Produk' },
    { value: 'LOYALTY', label: 'Program Loyalitas' },
    { value: 'SEASONAL', label: 'Promo Musiman' },
    { value: 'CLEARANCE', label: 'Clearance Sale' },
    { value: 'TRADE_IN', label: 'Program Trade-in' },
    { value: 'VOLUME', label: 'Diskon Volume' }
  ];

  // Available colors
  const colors = [
    { value: 'blue', label: 'Biru', class: 'bg-blue-500' },
    { value: 'green', label: 'Hijau', class: 'bg-green-500' },
    { value: 'purple', label: 'Ungu', class: 'bg-purple-500' },
    { value: 'orange', label: 'Oranye', class: 'bg-orange-500' },
    { value: 'red', label: 'Merah', class: 'bg-red-500' },
    { value: 'indigo', label: 'Indigo', class: 'bg-indigo-500' },
    { value: 'pink', label: 'Pink', class: 'bg-pink-500' },
    { value: 'teal', label: 'Teal', class: 'bg-teal-500' }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b">
          <div className="flex items-center">
            <Calendar className="h-5 w-5 text-blue-600 mr-2" />
            <h2 className="text-xl font-bold text-gray-900">
              {insight ? 'Jadwalkan Promo dari Insight' : 'Jadwalkan Promo Baru'}
            </h2>
          </div>
          <button
            className="p-1 rounded-full hover:bg-gray-100"
            onClick={onClose}
          >
            <X className="h-5 w-5 text-gray-600" />
          </button>
        </div>
        
        {/* Form */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* Success message */}
          {saveSuccess && (
            <div className="bg-green-50 border border-green-200 rounded-md p-3 flex items-center text-green-800">
              <Check className="h-5 w-5 text-green-600 mr-2" />
              <span>Promo berhasil dijadwalkan!</span>
            </div>
          )}
          
          {/* Error message */}
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-center text-red-800">
              <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
              <span>{errors.submit}</span>
            </div>
          )}
          
          {/* Basic info */}
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Nama Promo <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name || ''}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
                placeholder="Masukkan nama promo"
              />
              {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
            </div>
            
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Deskripsi
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description || ''}
                onChange={handleChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Deskripsi singkat tentang promo"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Tanggal Mulai <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  value={formData.startDate || ''}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md ${errors.startDate ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors.startDate && <p className="mt-1 text-sm text-red-500">{errors.startDate}</p>}
              </div>
              
              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Tanggal Selesai <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  value={formData.endDate || ''}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md ${errors.endDate ? 'border-red-500' : 'border-gray-300'}`}
                />
                {errors.endDate && <p className="mt-1 text-sm text-red-500">{errors.endDate}</p>}
              </div>
            </div>
          </div>
          
          {/* Promo details */}
          <div className="space-y-4">
            <div>
              <label htmlFor="promoType" className="block text-sm font-medium text-gray-700 mb-1">
                Tipe Promo <span className="text-red-500">*</span>
              </label>
              <select
                id="promoType"
                name="promoType"
                value={formData.promoType || ''}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${errors.promoType ? 'border-red-500' : 'border-gray-300'}`}
              >
                <option value="">Pilih tipe promo</option>
                {promoTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
              {errors.promoType && <p className="mt-1 text-sm text-red-500">{errors.promoType}</p>}
            </div>
            
            <div>
              <label htmlFor="products" className="block text-sm font-medium text-gray-700 mb-1">
                Produk <span className="text-red-500">*</span>
              </label>
              <textarea
                id="products"
                name="products"
                value={(formData.products || []).join('\n')}
                onChange={handleProductChange}
                rows={3}
                className={`w-full px-3 py-2 border rounded-md ${errors.products ? 'border-red-500' : 'border-gray-300'}`}
                placeholder="Masukkan produk (satu per baris)"
              />
              {errors.products && <p className="mt-1 text-sm text-red-500">{errors.products}</p>}
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-1">
                  Budget
                </label>
                <input
                  type="number"
                  id="budget"
                  name="budget"
                  value={formData.budget || ''}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Budget dalam Rupiah"
                />
              </div>
              
              <div>
                <label htmlFor="targetAudience" className="block text-sm font-medium text-gray-700 mb-1">
                  Target Audience
                </label>
                <input
                  type="text"
                  id="targetAudience"
                  name="targetAudience"
                  value={formData.targetAudience || ''}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Target audience"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="expectedImpact" className="block text-sm font-medium text-gray-700 mb-1">
                  Ekspektasi Dampak (0-100)
                </label>
                <input
                  type="number"
                  id="expectedImpact"
                  name="expectedImpact"
                  min="0"
                  max="100"
                  value={formData.expectedImpact || ''}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Skor dampak (0-100)"
                />
              </div>
              
              <div>
                <label htmlFor="color" className="block text-sm font-medium text-gray-700 mb-1">
                  Warna Label
                </label>
                <select
                  id="color"
                  name="color"
                  value={formData.color || 'blue'}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  {colors.map(color => (
                    <option key={color.value} value={color.value}>{color.label}</option>
                  ))}
                </select>
                <div className="mt-2 flex space-x-2">
                  {colors.map(color => (
                    <div 
                      key={color.value}
                      className={`w-6 h-6 rounded-full cursor-pointer ${color.class} ${formData.color === color.value ? 'ring-2 ring-offset-2 ring-gray-400' : ''}`}
                      onClick={() => setFormData(prev => ({ ...prev, color: color.value }))}
                    />
                  ))}
                </div>
              </div>
            </div>
            
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                Catatan
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes || ''}
                onChange={handleChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Catatan tambahan"
              />
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              onClick={onClose}
              disabled={isSaving}
            >
              Batal
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors flex items-center"
              disabled={isSaving || saveSuccess}
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  <span>Menyimpan...</span>
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  <span>Simpan</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
