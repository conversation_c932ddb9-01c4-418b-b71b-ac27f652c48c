import { generateImages } from './src/services/runwareService';
import { ImageGenerationRequest } from './src/types/imageGenerator';
import { v4 as uuidv4 } from 'uuid';

async function testImageGeneration() {
  const request: ImageGenerationRequest = {
    positivePrompt: 'A beautiful sunset over the mountains',
    negativePrompt: '',
    width: 512,
    height: 512,
    model: 'rundiffusion:130@100',
    numberResults: 1
  };

  try {
    const images = await generateImages(request);
    console.log('Generated images:', images);
  } catch (error) {
    console.error('Image generation failed:', error);
  }
}

testImageGeneration();
