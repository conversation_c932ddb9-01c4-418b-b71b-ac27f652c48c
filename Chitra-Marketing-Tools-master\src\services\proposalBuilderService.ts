/**
 * Service for generating marketing proposals using AI
 */
import { v4 as uuidv4 } from 'uuid';
import {
  ProposalBuilderFormData,
  ProposalBuilderResult,
  ProposalBuilderAIRequest,
  ProposalBuilderAIResponse
} from '../types/proposalBuilder';
import { formatCurrency } from '../utils/pricing';
import { saveAs } from 'file-saver';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, BorderStyle } from 'docx';
import { getAllKnowledgeEntries, searchKnowledgeEntries } from './knowledgeBaseService';

// OpenRouter API configuration
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const OPENROUTER_API_KEY = '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';
const MODEL = 'google/gemini-2.0-flash-001';

// Local storage key for saving generated proposals
const PROPOSALS_STORAGE_KEY = 'proposal_builder_history';

/**
 * Generate a marketing proposal using AI
 */
export const generateProposal = async (
  formData: ProposalBuilderFormData
): Promise<ProposalBuilderResult> => {
  try {
    console.log('Generating marketing proposal with AI:', formData);

    // Generate proposal content using AI
    const aiResponse = await callAI({ formData });

    // Generate DOCX document
    const documentUrl = await generateDocxDocument(formData, aiResponse);

    // Create result object
    const result: ProposalBuilderResult = {
      id: uuidv4(),
      proposalType: formData.proposalType,
      proposalTitle: formData.proposalTitle,
      generatedDate: new Date().toISOString(),
      documentUrl,
      previewHtml: convertToHtml(aiResponse)
    };

    // Save to history
    saveProposalToHistory(result);

    return result;
  } catch (error) {
    console.error('Error generating marketing proposal:', error);
    throw new Error('Failed to generate marketing proposal: ' +
      (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Get relevant knowledge base entries for the proposal
 */
const getRelevantKnowledgeEntries = async (formData: ProposalBuilderFormData): Promise<string> => {
  try {
    // Build search terms based on proposal type and content
    let searchTerms: string[] = [];

    // Add terms based on proposal type
    switch (formData.proposalType) {
      case 'bundling':
        searchTerms.push('bundling', 'paket', 'efisiensi biaya');
        break;
      case 'michelin-first':
        searchTerms.push('michelin', 'ban', 'kualitas', 'performa');
        break;
      case 'product-warranty':
        searchTerms.push('garansi', 'jaminan', 'kualitas', 'layanan purna jual');
        break;
      case 'custom':
        // For custom, use the custom type and requirements
        if (formData.customProposalType) {
          searchTerms.push(...formData.customProposalType.split(' '));
        }
        if (formData.customProposalRequirements) {
          searchTerms.push(...formData.customProposalRequirements.split(' ').filter(term => term.length > 4));
        }
        break;
    }

    // Add terms from company info and products
    if (formData.companyInfo.industry) {
      searchTerms.push(formData.companyInfo.industry);
    }

    // Add product terms
    formData.mainProducts.forEach(product => {
      const terms = product.product.materialDescription.split(' ').filter(term => term.length > 4);
      searchTerms.push(...terms);
    });

    // Add customer pain points if available
    if (formData.customerPain) {
      const painTerms = formData.customerPain.split(' ').filter(term => term.length > 4);
      searchTerms.push(...painTerms);
    }

    // Add competitor info if available
    if (formData.competitorInfo) {
      const competitorTerms = formData.competitorInfo.split(' ').filter(term => term.length > 4);
      searchTerms.push(...competitorTerms);
    }

    // Remove duplicates and common words
    const commonWords = ['yang', 'dengan', 'untuk', 'dari', 'dalam', 'pada', 'adalah', 'akan', 'tidak', 'juga', 'atau', 'ini', 'itu'];
    searchTerms = [...new Set(searchTerms)].filter(term =>
      !commonWords.includes(term.toLowerCase()) && term.length > 3
    );

    console.log('Search terms for knowledge base:', searchTerms);

    // Get all knowledge entries
    const allEntries = await getAllKnowledgeEntries();

    // Filter entries based on search terms
    const relevantEntries = allEntries.filter(entry => {
      // Check if any search term is in the title, content, or tags
      return searchTerms.some(term =>
        entry.title.toLowerCase().includes(term.toLowerCase()) ||
        entry.content.toLowerCase().includes(term.toLowerCase()) ||
        entry.tags.some(tag => tag.toLowerCase().includes(term.toLowerCase()))
      );
    });

    console.log(`Found ${relevantEntries.length} relevant knowledge base entries`);

    // Format the entries for the prompt
    if (relevantEntries.length === 0) {
      return '';
    }

    // Limit to 5 most relevant entries to avoid token limits
    const limitedEntries = relevantEntries.slice(0, 5);

    const formattedEntries = limitedEntries.map(entry =>
      `JUDUL: ${entry.title}\nKATEGORI: ${entry.category}\nKONTEN: ${entry.content}\n`
    ).join('\n---\n');

    return `INFORMASI DARI KNOWLEDGE BASE:\n${formattedEntries}`;
  } catch (error) {
    console.error('Error getting relevant knowledge base entries:', error);
    return '';
  }
};

/**
 * Call the AI service to generate proposal content
 */
const callAI = async (
  request: ProposalBuilderAIRequest
): Promise<ProposalBuilderAIResponse> => {
  try {
    const { formData } = request;

    // Format products for the prompt
    const mainProductsText = formData.mainProducts.map(item =>
      `- ${item.product.materialDescription} (${item.quantity} units): ${formatCurrency(item.product.price)} per unit`
    ).join('\n');

    const bundledProductsText = formData.bundledProducts?.map(item =>
      `- ${item.product.materialDescription} (${item.quantity} units): ${formatCurrency(item.product.price)} per unit`
    ).join('\n') || 'Tidak ada produk bundling';

    // Get relevant knowledge base entries
    const knowledgeBaseInfo = await getRelevantKnowledgeEntries(formData);

    // Create system prompt based on proposal type
    let systemPrompt = `Anda adalah seorang ahli marketing profesional dengan pengalaman luas dalam membuat proposal marketing yang meyakinkan dan efektif. Tugas Anda adalah membuat proposal marketing yang berkualitas tinggi berdasarkan informasi yang diberikan.

Proposal harus ditulis dalam bahasa Indonesia yang profesional, meyakinkan, dan mudah dipahami. Gunakan gaya bahasa marketing yang kuat namun tetap formal dan profesional. Hindari penggunaan bahasa yang terdengar seperti bot atau AI.

Proposal harus memiliki struktur yang jelas dengan bagian-bagian berikut:
1. Judul dan Pendahuluan
2. Gambaran Perusahaan (Chitra Paratama)
3. Deskripsi Produk / Bundling
4. Nilai Tambah dan Manfaat
5. Penawaran Harga
6. Garansi dan Syarat & Ketentuan
7. Penutup

Pastikan proposal memiliki kualitas tinggi dengan:
- Bahasa yang persuasif dan meyakinkan
- Fokus pada manfaat dan nilai bagi pelanggan
- Penjelasan yang jelas tentang produk dan penawaran
- Struktur yang rapi dan mudah dibaca
- Tone yang profesional dan berwibawa

Proposal harus memenuhi standar kualitas minimal 90/100 jika dinilai oleh sistem evaluasi proposal profesional.`;

    // Add specific instructions based on proposal type
    switch (formData.proposalType) {
      case 'bundling':
        systemPrompt += `\n\nProposal ini harus berfokus pada bundling produk, menekankan nilai dan manfaat biaya dari pembelian produk secara paket dibandingkan secara terpisah. Tekankan sinergi antar produk dan penghematan biaya secara keseluruhan. Jelaskan bagaimana kombinasi produk ini memberikan solusi lengkap untuk kebutuhan pelanggan.

Buat proposal bundling produk yang lengkap, profesional, dan ditujukan untuk tim pengadaan di perusahaan klien dengan struktur formal yang mencakup 6 bagian utama berikut:

1. Pendahuluan
   - Latar belakang perusahaan
   - Gambaran umum masalah pelanggan
   - Tujuan utama dari proposal bundling ini

2. Analisis Kebutuhan Pelanggan
   - Masalah yang dihadapi pelanggan saat ini
   - Dampaknya terhadap biaya operasional / downtime
   - Solusi ideal menurut kami

3. Solusi & Penawaran Bundling
   - Penjelasan setiap produk dalam bundling
   - Cara kerja bundling (misalnya 1 ban utama + 1 support + layanan teknis)
   - Skema harga, diskon, dan ketentuan

4. Perbandingan dengan Kompetitor
   - Benchmarking produk dan layanan
   - Kenapa solusi kami lebih unggul

5. Manfaat Jangka Panjang
   - Efisiensi biaya total (TCO)
   - Dukungan teknis / pelatihan
   - Risiko minimal (garansi, purna jual)

6. Penutup & Call to Action
   - Rangkuman
   - Ajakan tindak lanjut / meeting
   - Kontak tim penjualan

Tambahkan visualisasi bila perlu (misalnya tabel bundling, simulasi penghematan, dsb).
Gunakan bahasa Indonesia formal, profesional, dan persuasif.
Pastikan proposal memiliki nilai skor ≥90 di Proposal Analyzer. Jika belum, refine otomatis.
Proposal harus minimal 5 halaman.`;
        break;
      case 'michelin-first':
        systemPrompt += `\n\nProposal ini harus berfokus pada pengenalan produk Michelin kepada pelanggan untuk pertama kalinya, menekankan proposisi nilai unik Michelin, kualitas, manfaat kinerja, dan keunggulan biaya jangka panjang dibandingkan dengan kompetitor. Jelaskan sejarah dan reputasi Michelin sebagai pemimpin industri.

Buatkan proposal lengkap untuk penawaran program Michelin First kepada perusahaan klien dengan struktur sebagai berikut:

1. Pendahuluan Program Michelin First
   - Apa itu Michelin First?
   - Visi program untuk pelanggan besar

2. Analisa Kebutuhan Pelanggan
   - Kebutuhan ban (durabilitas, safety, efisiensi)
   - Tantangan saat ini (cost, maintenance, dsb)

3. Solusi Michelin First
   - Komponen layanan (produk, monitoring, garansi, support)
   - Penyesuaian dengan kebutuhan klien
   - Penjelasan format TCO & value

4. Studi Kasus / Testimoni Pelanggan Lain
   - Cerita keberhasilan (misalnya dari PT Vale, Adaro, dsb)

5. Skema Komersial
   - Model kontrak
   - Estimasi biaya
   - Benefit dalam angka

6. Penutup & Ajakan Kerja Sama

Gunakan gaya persuasif, dorong value dibanding hanya harga.
Sertakan kalkulasi penghematan dan simulasi.
Skor minimum: 90 Analyzer. Buat otomatis revisi jika belum cukup.
Proposal harus minimal 5 halaman.`;
        break;
      case 'product-warranty':
        systemPrompt += `\n\nProposal ini harus berfokus pada penawaran garansi produk, merinci persyaratan cakupan, durasi, proses klaim, dan nilai yang diberikan kepada pelanggan dalam hal pengurangan risiko dan ketenangan pikiran. Tekankan komitmen perusahaan terhadap kualitas dan layanan purna jual.

Buat proposal garansi produk lengkap dan mendalam untuk penawaran dengan struktur sebagai berikut:

1. Pendahuluan
   - Tujuan proposal garansi
   - Gambaran umum produk

2. Keunggulan Produk
   - Fitur unggulan
   - Pembuktian performa di lapangan

3. Rincian Garansi
   - Apa saja yang dicover
   - Proses klaim
   - Jangka waktu & pengecualian

4. Komparasi dengan Garansi Kompetitor
   - Highlight nilai lebih kami

5. Studi Kasus Penggunaan Produk
   - Rekam jejak reliability

6. Penutup
   - Ajakan diskusi lanjut
   - Kontak sales atau aftersales

Gunakan gaya teknis tetapi mudah dimengerti decision maker.
Proposal harus menghasilkan skor >90 di analyzer.
Proposal harus minimal 5 halaman.`;
        break;
      case 'custom':
        systemPrompt += `\n\nIni adalah proposal kustom dengan persyaratan khusus: ${formData.customProposalRequirements}. Sesuaikan proposal untuk memenuhi kebutuhan spesifik ini sambil mempertahankan pendekatan marketing yang profesional.

Berdasarkan input kustom dari user (produk, tujuan, pain point, keunikan case), buat proposal pemasaran profesional yang mencakup:

1. Pendahuluan Masalah
2. Analisa Kebutuhan
3. Solusi yang Diajukan
4. Skema Penawaran dan Harga
5. Pembeda dari Kompetitor
6. Garansi & Dukungan
7. Penutup & Arah Tindak Lanjut

Buat naratif yang kuat, profesional, dan disesuaikan dengan target audiens.
Minimal 1500 kata, dengan struktur logis dan persuasif.
Harus menghasilkan skor Analyzer >90.
Proposal harus minimal 5 halaman.`;
        break;
    }

    // Add formatting instructions
    systemPrompt += `\n\nProposal harus terstruktur dengan baik dengan bagian-bagian yang jelas, bahasa profesional, dan konten marketing yang persuasif. Gunakan bahasa bisnis Indonesia yang formal. Format dokumen secara profesional dengan judul, poin-poin, dan penekanan yang sesuai.

Hindari penggunaan tanda bintang (**) untuk pemformatan. Gunakan spasi dan jeda baris yang tepat untuk keterbacaan. Tampilkan tabel secara terpisah dan jelas. Gunakan bahasa yang tidak terdengar seperti AI atau bot.`;

    // Create user prompt with all the details
    const userPrompt = `Buatkan proposal pemasaran lengkap dengan detail berikut:

JENIS PROPOSAL: ${getProposalTypeName(formData.proposalType)}
${formData.proposalType === 'custom' ? `TIPE KUSTOM: ${formData.customProposalType}` : ''}

JUDUL PROPOSAL: ${formData.proposalTitle}

INFORMASI PERUSAHAAN KLIEN:
- Nama Perusahaan: ${formData.companyInfo.companyName}
- Kontak: ${formData.companyInfo.contactPerson} (${formData.companyInfo.contactTitle})
- Email: ${formData.companyInfo.contactEmail}
- Telepon: ${formData.companyInfo.contactPhone}
- Alamat: ${formData.companyInfo.companyAddress}
${formData.companyInfo.industry ? `- Industri: ${formData.companyInfo.industry}` : ''}

PRODUK UTAMA:
${mainProductsText}

${formData.bundledProducts && formData.bundledProducts.length > 0 ? `PRODUK BUNDLING:
${bundledProductsText}` : ''}

DETAIL PENAWARAN:
- Judul Penawaran: ${formData.offerDetails.offerTitle}
- Berlaku Hingga: ${formData.offerDetails.validUntil}
- Syarat Khusus: ${formData.offerDetails.specialTerms}
- Syarat Pembayaran: ${formData.offerDetails.paymentTerms}
- Syarat Pengiriman: ${formData.offerDetails.deliveryTerms}

${formData.customerPain ? `PAIN POINT PELANGGAN:
${formData.customerPain}` : ''}

${formData.competitorInfo ? `INFORMASI KOMPETITOR:
${formData.competitorInfo}` : ''}

${formData.previousDeals ? `TRANSAKSI SEBELUMNYA:
${formData.previousDeals}` : ''}

${formData.additionalNotes ? `CATATAN TAMBAHAN:
${formData.additionalNotes}` : ''}

${knowledgeBaseInfo ? `\n${knowledgeBaseInfo}\n` : ''}

Hasilkan proposal lengkap dengan struktur berikut:
1. Halaman Judul/Cover
2. Pendahuluan
3. Profil Perusahaan (Chitra Paratama)
4. Detail Produk
5. Proposisi Nilai
6. Detail Penawaran
7. Harga dan Paket
8. Syarat dan Ketentuan
9. Kesimpulan dan Call to Action

Berikan proposal dalam format yang siap untuk dikonversi menjadi dokumen DOCX.

PENTING: Gunakan informasi dari Knowledge Base yang diberikan untuk memperkaya konten proposal, terutama untuk bagian yang relevan seperti detail produk, proposisi nilai, dan strategi penawaran.`;

    console.log('Calling AI with prompt:', userPrompt);

    // Call the AI model via OpenRouter
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Proposal Builder'
      },
      body: JSON.stringify({
        model: MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 4000
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('AI response:', data);

    // Extract the content from the response
    const content = data.choices[0].message.content;

    // Parse the content into sections
    const sections = parseContentIntoSections(content);

    return {
      content,
      sections
    };
  } catch (error) {
    console.error('Error calling AI:', error);
    throw error;
  }
};

/**
 * Parse the AI-generated content into sections
 */
const parseContentIntoSections = (content: string): ProposalBuilderAIResponse['sections'] => {
  // Default empty sections
  const sections: ProposalBuilderAIResponse['sections'] = {
    introduction: '',
    companyOverview: '',
    productDetails: '',
    valueProposition: '',
    offerDetails: '',
    pricing: '',
    terms: '',
    conclusion: ''
  };

  try {
    // Simple parsing based on headings
    if (content.includes('# Pendahuluan') || content.includes('## Pendahuluan')) {
      const introMatch = content.match(/(?:# |## )Pendahuluan\s+([\s\S]*?)(?=(?:# |## )|$)/);
      if (introMatch && introMatch[1]) sections.introduction = introMatch[1].trim();
    }

    if (content.includes('# Profil Perusahaan') || content.includes('## Profil Perusahaan')) {
      const companyMatch = content.match(/(?:# |## )Profil Perusahaan\s+([\s\S]*?)(?=(?:# |## )|$)/);
      if (companyMatch && companyMatch[1]) sections.companyOverview = companyMatch[1].trim();
    }

    if (content.includes('# Detail Produk') || content.includes('## Detail Produk')) {
      const productMatch = content.match(/(?:# |## )Detail Produk\s+([\s\S]*?)(?=(?:# |## )|$)/);
      if (productMatch && productMatch[1]) sections.productDetails = productMatch[1].trim();
    }

    if (content.includes('# Proposisi Nilai') || content.includes('## Proposisi Nilai')) {
      const valueMatch = content.match(/(?:# |## )Proposisi Nilai\s+([\s\S]*?)(?=(?:# |## )|$)/);
      if (valueMatch && valueMatch[1]) sections.valueProposition = valueMatch[1].trim();
    }

    if (content.includes('# Detail Penawaran') || content.includes('## Detail Penawaran')) {
      const offerMatch = content.match(/(?:# |## )Detail Penawaran\s+([\s\S]*?)(?=(?:# |## )|$)/);
      if (offerMatch && offerMatch[1]) sections.offerDetails = offerMatch[1].trim();
    }

    if (content.includes('# Harga dan Paket') || content.includes('## Harga dan Paket')) {
      const pricingMatch = content.match(/(?:# |## )Harga dan Paket\s+([\s\S]*?)(?=(?:# |## )|$)/);
      if (pricingMatch && pricingMatch[1]) sections.pricing = pricingMatch[1].trim();
    }

    if (content.includes('# Syarat dan Ketentuan') || content.includes('## Syarat dan Ketentuan')) {
      const termsMatch = content.match(/(?:# |## )Syarat dan Ketentuan\s+([\s\S]*?)(?=(?:# |## )|$)/);
      if (termsMatch && termsMatch[1]) sections.terms = termsMatch[1].trim();
    }

    if (content.includes('# Kesimpulan') || content.includes('## Kesimpulan')) {
      const conclusionMatch = content.match(/(?:# |## )Kesimpulan\s+([\s\S]*?)(?=(?:# |## )|$)/);
      if (conclusionMatch && conclusionMatch[1]) sections.conclusion = conclusionMatch[1].trim();
    }

    return sections;
  } catch (error) {
    console.error('Error parsing content into sections:', error);
    return sections;
  }
};

/**
 * Generate a DOCX document from the AI response
 */
const generateDocxDocument = async (
  formData: ProposalBuilderFormData,
  aiResponse: ProposalBuilderAIResponse
): Promise<string> => {
  try {
    // Since we're having issues with docx.js in the browser, let's use a simpler approach
    // We'll create a rich text document with proper formatting that can be opened in Word

    // Create a HTML version of the document with proper styling
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${formData.proposalTitle}</title>
        <style>
          body {
            font-family: Calibri, Arial, sans-serif;
            margin: 1in;
            line-height: 1.5;
          }
          .title {
            font-size: 24pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 12pt;
            color: #2E74B5;
          }
          .subtitle {
            font-size: 18pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 24pt;
            color: #2E74B5;
          }
          .company {
            font-size: 12pt;
            text-align: center;
            margin-bottom: 6pt;
          }
          .date {
            font-size: 12pt;
            text-align: center;
            margin-bottom: 24pt;
          }
          h1 {
            font-size: 16pt;
            font-weight: bold;
            margin-top: 24pt;
            margin-bottom: 12pt;
            color: #2E74B5;
          }
          h2 {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 18pt;
            margin-bottom: 9pt;
            color: #2E74B5;
          }
          p {
            font-size: 12pt;
            margin-bottom: 9pt;
          }
          ul {
            margin-bottom: 9pt;
          }
          li {
            font-size: 12pt;
            margin-bottom: 3pt;
          }
          .page-break {
            page-break-before: always;
          }
        </style>
      </head>
      <body>
        <div class="title">${formData.proposalTitle}</div>
        <div class="subtitle">${getProposalTypeName(formData.proposalType)}</div>
        <div class="company">Dipersiapkan untuk: ${formData.companyInfo.companyName}</div>
        <div class="date">Tanggal: ${new Date().toLocaleDateString('id-ID')}</div>

        <div class="page-break"></div>

        ${convertMarkdownToHtml(aiResponse.content)}
      </body>
      </html>
    `;

    // Convert HTML to a Blob with correct MIME type
    // Use 'application/msword' instead of 'application/vnd.ms-word'
    const blob = new Blob([htmlContent], { type: 'application/msword' });

    // Create a URL for the blob
    const url = URL.createObjectURL(blob);

    try {
      // Use saveAs from file-saver for more reliable download
      saveAs(blob, `${formData.proposalTitle}.doc`);
      console.log('Document saved successfully');
    } catch (saveError) {
      console.error('Error using saveAs:', saveError);

      // Fallback method if saveAs fails
      try {
        const link = document.createElement('a');
        link.href = url;
        link.download = `${formData.proposalTitle}.doc`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        console.log('Document saved using fallback method');
      } catch (fallbackError) {
        console.error('Fallback download method failed:', fallbackError);
      }
    }

    return url;
  } catch (error) {
    console.error('Error generating DOCX document:', error);
    throw error;
  }
};

/**
 * Convert markdown content to HTML
 */
const convertMarkdownToHtml = (markdown: string): string => {
  let html = '';
  const lines = markdown.split('\n');

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    if (line.startsWith('# ')) {
      // Heading 1
      html += `<h1>${line.substring(2).trim()}</h1>\n`;
    } else if (line.startsWith('## ')) {
      // Heading 2
      html += `<h2>${line.substring(3).trim()}</h2>\n`;
    } else if (line.startsWith('- ')) {
      // Start a bullet list
      html += '<ul>\n';

      // Add the first bullet point
      html += `<li>${line.substring(2).trim()}</li>\n`;

      // Look ahead for more bullet points
      let j = i + 1;
      while (j < lines.length && lines[j].startsWith('- ')) {
        html += `<li>${lines[j].substring(2).trim()}</li>\n`;
        i = j; // Skip this line in the outer loop
        j++;
      }

      // Close the bullet list
      html += '</ul>\n';
    } else if (line.trim() !== '') {
      // Regular paragraph
      html += `<p>${line.trim()}</p>\n`;
    }
  }

  return html;
};

/**
 * Convert AI response to HTML for preview
 */
const convertToHtml = (aiResponse: ProposalBuilderAIResponse): string => {
  try {
    // Convert markdown to HTML
    let html = aiResponse.content
      .replace(/# (.*?)\n/g, '<h1>$1</h1>')
      .replace(/## (.*?)\n/g, '<h2>$1</h2>')
      .replace(/### (.*?)\n/g, '<h3>$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n- (.*)/g, '<ul><li>$1</li></ul>')
      .replace(/<\/ul><ul>/g, '');

    return `<div class="proposal-preview">${html}</div>`;
  } catch (error) {
    console.error('Error converting to HTML:', error);
    return `<div class="error">Error generating preview: ${error}</div>`;
  }
};

/**
 * Save a proposal to history in localStorage
 */
const saveProposalToHistory = (proposal: ProposalBuilderResult): void => {
  try {
    const history = getProposalHistory();
    history.unshift(proposal);

    // Limit history to 20 items
    if (history.length > 20) {
      history.pop();
    }

    localStorage.setItem(PROPOSALS_STORAGE_KEY, JSON.stringify(history));
  } catch (error) {
    console.error('Error saving proposal to history:', error);
  }
};

/**
 * Get proposal history from localStorage
 */
export const getProposalHistory = (): ProposalBuilderResult[] => {
  try {
    const history = localStorage.getItem(PROPOSALS_STORAGE_KEY);
    return history ? JSON.parse(history) : [];
  } catch (error) {
    console.error('Error getting proposal history:', error);
    return [];
  }
};

/**
 * Get a human-readable name for a proposal type
 */
export const getProposalTypeName = (type: string): string => {
  switch (type) {
    case 'bundling':
      return 'Proposal Bundling Produk';
    case 'michelin-first':
      return 'Proposal Michelin First';
    case 'product-warranty':
      return 'Proposal Garansi Produk';
    case 'custom':
      return 'Proposal Kustom';
    default:
      return 'Proposal';
  }
};
