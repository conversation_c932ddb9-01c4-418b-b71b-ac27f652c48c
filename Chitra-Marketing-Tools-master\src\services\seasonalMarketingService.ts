import {
  CalendarDay,
  CalendarMonth,
  RecommendationLevel,
  SeasonalFactor,
  SeasonalFactorType,
  ScheduledPromotion,
  SeasonalInsight,
  CalendarSettings
} from '../types/seasonalMarketing';

// Local storage keys
const SEASONAL_FACTORS_KEY = 'seasonalFactors';
const SCHEDULED_PROMOTIONS_KEY = 'scheduledPromotions';
const SEASONAL_INSIGHTS_KEY = 'seasonalInsights';
const CALENDAR_SETTINGS_KEY = 'calendarSettings';

/**
 * Default calendar settings
 */
const DEFAULT_CALENDAR_SETTINGS: CalendarSettings = {
  regions: ['All', 'Java', 'Sumatra', 'Kalimantan', 'Sulawesi', 'Papua'],
  productCategories: ['Earthmover', 'OTR', 'Truck', 'Light Truck', 'Passenger'],
  factorWeights: {
    [SeasonalFactorType.WEATHER]: 60,
    [SeasonalFactorType.MINING_OPERATIONS]: 90,
    [SeasonalFactorType.ECONOMIC]: 70,
    [SeasonalFactorType.COMPETITOR]: 80,
    [SeasonalFactorType.HISTORICAL]: 100,
    [SeasonalFactorType.INDUSTRY_EVENT]: 50,
    [SeasonalFactorType.BUDGET_CYCLE]: 85,
    [SeasonalFactorType.MAINTENANCE]: 75
  },
  displayOptions: {
    showWeekends: true,
    showCompetitors: true,
    showHistorical: true,
    colorCoding: 'recommendation'
  }
};

/**
 * Predefined seasonal factors that affect tire demand
 */
const PREDEFINED_SEASONAL_FACTORS: SeasonalFactor[] = [
  {
    id: 'sf-1',
    type: SeasonalFactorType.WEATHER,
    name: 'Musim Hujan',
    description: 'Musim hujan di Indonesia yang dapat mempengaruhi operasi pertambangan dan kebutuhan ban',
    impact: -7,
    startDate: '2024-10-01',
    endDate: '2025-03-31',
    region: 'Kalimantan',
    source: 'BMKG'
  },
  {
    id: 'sf-2',
    type: SeasonalFactorType.MINING_OPERATIONS,
    name: 'Peningkatan Produksi Q4',
    description: 'Peningkatan produksi pertambangan pada kuartal 4 untuk memenuhi target tahunan',
    impact: 8,
    startDate: '2024-10-01',
    endDate: '2024-12-31',
    region: 'All',
    source: 'Industry Analysis'
  },
  {
    id: 'sf-3',
    type: SeasonalFactorType.ECONOMIC,
    name: 'Kenaikan Harga Batu Bara',
    description: 'Kenaikan harga batu bara global yang meningkatkan aktivitas pertambangan',
    impact: 6,
    startDate: '2024-08-01',
    endDate: '2025-01-31',
    source: 'Economic Reports'
  },
  {
    id: 'sf-4',
    type: SeasonalFactorType.COMPETITOR,
    name: 'Promo Akhir Tahun Bridgestone',
    description: 'Promosi akhir tahun dari kompetitor utama Bridgestone',
    impact: -5,
    startDate: '2024-11-15',
    endDate: '2024-12-31',
    region: 'All',
    source: 'Market Intelligence'
  },
  {
    id: 'sf-5',
    type: SeasonalFactorType.HISTORICAL,
    name: 'Puncak Penjualan Q1',
    description: 'Berdasarkan data historis, Q1 adalah periode puncak penjualan ban pertambangan',
    impact: 9,
    startDate: '2025-01-01',
    endDate: '2025-03-31',
    region: 'All',
    source: 'Historical Sales Data'
  },
  {
    id: 'sf-6',
    type: SeasonalFactorType.INDUSTRY_EVENT,
    name: 'Mining Indonesia Expo',
    description: 'Pameran pertambangan terbesar di Indonesia',
    impact: 7,
    startDate: '2024-09-15',
    endDate: '2024-09-18',
    region: 'Jakarta',
    source: 'Event Calendar'
  },
  {
    id: 'sf-7',
    type: SeasonalFactorType.BUDGET_CYCLE,
    name: 'Alokasi Budget Awal Tahun',
    description: 'Periode alokasi anggaran baru untuk perusahaan pertambangan',
    impact: 10,
    startDate: '2025-01-01',
    endDate: '2025-02-28',
    region: 'All',
    source: 'Industry Analysis'
  },
  {
    id: 'sf-8',
    type: SeasonalFactorType.MAINTENANCE,
    name: 'Periode Maintenance Rutin',
    description: 'Periode pemeliharaan rutin untuk alat berat pertambangan',
    impact: 8,
    startDate: '2024-07-01',
    endDate: '2024-08-31',
    region: 'Kalimantan',
    source: 'Industry Reports'
  }
];

/**
 * Get all seasonal factors
 */
export const getSeasonalFactors = (): SeasonalFactor[] => {
  try {
    const storedFactors = localStorage.getItem(SEASONAL_FACTORS_KEY);
    if (storedFactors) {
      return JSON.parse(storedFactors);
    }
    // If no stored factors, use predefined ones and save them
    localStorage.setItem(SEASONAL_FACTORS_KEY, JSON.stringify(PREDEFINED_SEASONAL_FACTORS));
    return PREDEFINED_SEASONAL_FACTORS;
  } catch (error) {
    console.error('Error getting seasonal factors:', error);
    return PREDEFINED_SEASONAL_FACTORS;
  }
};

/**
 * Save a seasonal factor
 */
export const saveSeasonalFactor = (factor: SeasonalFactor): SeasonalFactor => {
  try {
    const factors = getSeasonalFactors();

    // Check if this is an update or a new factor
    const index = factors.findIndex(f => f.id === factor.id);

    if (index >= 0) {
      // Update existing factor
      factors[index] = factor;
    } else {
      // Add new factor with generated ID if not provided
      if (!factor.id) {
        factor.id = `sf-${Date.now()}`;
      }
      factors.push(factor);
    }

    // Save to local storage
    localStorage.setItem(SEASONAL_FACTORS_KEY, JSON.stringify(factors));
    return factor;
  } catch (error) {
    console.error('Error saving seasonal factor:', error);
    throw new Error('Failed to save seasonal factor');
  }
};

/**
 * Delete a seasonal factor
 */
export const deleteSeasonalFactor = (id: string): boolean => {
  try {
    const factors = getSeasonalFactors();
    const updatedFactors = factors.filter(f => f.id !== id);

    if (updatedFactors.length === factors.length) {
      return false; // No factor was deleted
    }

    localStorage.setItem(SEASONAL_FACTORS_KEY, JSON.stringify(updatedFactors));
    return true;
  } catch (error) {
    console.error('Error deleting seasonal factor:', error);
    return false;
  }
};

/**
 * Get all scheduled promotions
 */
export const getScheduledPromotions = (): ScheduledPromotion[] => {
  try {
    const storedPromotions = localStorage.getItem(SCHEDULED_PROMOTIONS_KEY);
    if (storedPromotions) {
      return JSON.parse(storedPromotions);
    }
    return [];
  } catch (error) {
    console.error('Error getting scheduled promotions:', error);
    return [];
  }
};

/**
 * Save a scheduled promotion
 */
export const saveScheduledPromotion = (promotion: ScheduledPromotion): ScheduledPromotion => {
  try {
    const promotions = getScheduledPromotions();

    // Check if this is an update or a new promotion
    const index = promotions.findIndex(p => p.id === promotion.id);

    if (index >= 0) {
      // Update existing promotion
      promotions[index] = promotion;
    } else {
      // Add new promotion with generated ID if not provided
      if (!promotion.id) {
        promotion.id = `promo-${Date.now()}`;
      }
      promotions.push(promotion);
    }

    // Save to local storage
    localStorage.setItem(SCHEDULED_PROMOTIONS_KEY, JSON.stringify(promotions));
    return promotion;
  } catch (error) {
    console.error('Error saving scheduled promotion:', error);
    throw new Error('Failed to save scheduled promotion');
  }
};

/**
 * Delete a scheduled promotion
 */
export const deleteScheduledPromotion = (id: string): boolean => {
  try {
    const promotions = getScheduledPromotions();
    const updatedPromotions = promotions.filter(p => p.id !== id);

    if (updatedPromotions.length === promotions.length) {
      return false; // No promotion was deleted
    }

    localStorage.setItem(SCHEDULED_PROMOTIONS_KEY, JSON.stringify(updatedPromotions));
    return true;
  } catch (error) {
    console.error('Error deleting scheduled promotion:', error);
    return false;
  }
};

/**
 * Get calendar settings
 */
export const getCalendarSettings = (): CalendarSettings => {
  try {
    const storedSettings = localStorage.getItem(CALENDAR_SETTINGS_KEY);
    if (storedSettings) {
      return JSON.parse(storedSettings);
    }
    return DEFAULT_CALENDAR_SETTINGS;
  } catch (error) {
    console.error('Error getting calendar settings:', error);
    return DEFAULT_CALENDAR_SETTINGS;
  }
};

/**
 * Save calendar settings
 */
export const saveCalendarSettings = (settings: CalendarSettings): CalendarSettings => {
  try {
    localStorage.setItem(CALENDAR_SETTINGS_KEY, JSON.stringify(settings));
    return settings;
  } catch (error) {
    console.error('Error saving calendar settings:', error);
    throw new Error('Failed to save calendar settings');
  }
};

/**
 * Calculate recommendation level based on score
 */
export const calculateRecommendationLevel = (score: number): RecommendationLevel => {
  if (score >= 80) return RecommendationLevel.EXCELLENT;
  if (score >= 60) return RecommendationLevel.GOOD;
  if (score >= 40) return RecommendationLevel.NEUTRAL;
  if (score >= 20) return RecommendationLevel.POOR;
  return RecommendationLevel.AVOID;
};

/**
 * Sample seasonal insights for the dashboard
 */
const SAMPLE_SEASONAL_INSIGHTS: SeasonalInsight[] = [
  {
    id: 'insight-1',
    title: 'Peluang Promosi Awal Tahun',
    description: 'Berdasarkan analisis faktor musiman, awal tahun 2025 adalah waktu yang sangat baik untuk meluncurkan promosi ban pertambangan. Alokasi anggaran baru dan peningkatan aktivitas pertambangan menciptakan peluang penjualan yang signifikan.',
    startDate: '2025-01-01',
    endDate: '2025-02-28',
    recommendationLevel: RecommendationLevel.EXCELLENT,
    factors: [
      getSeasonalFactors().find(f => f.id === 'sf-5') || {
        id: 'sf-5',
        type: SeasonalFactorType.HISTORICAL,
        name: 'Puncak Penjualan Q1',
        description: 'Berdasarkan data historis, Q1 adalah periode puncak penjualan ban pertambangan',
        impact: 9
      },
      getSeasonalFactors().find(f => f.id === 'sf-7') || {
        id: 'sf-7',
        type: SeasonalFactorType.BUDGET_CYCLE,
        name: 'Alokasi Budget Awal Tahun',
        description: 'Periode alokasi anggaran baru untuk perusahaan pertambangan',
        impact: 10
      }
    ],
    recommendedPromoTypes: ['Bundling', 'Performance Guarantee', 'Trade-In'],
    recommendedProducts: ['XDR3', 'XDR2', 'XTRA LOAD'],
    potentialImpact: 85,
    confidence: 92,
    aiGenerated: true
  },
  {
    id: 'insight-2',
    title: 'Antisipasi Musim Hujan Kalimantan',
    description: 'Musim hujan di Kalimantan akan berdampak negatif pada operasi pertambangan dan kebutuhan ban. Persiapkan strategi untuk mempertahankan penjualan selama periode ini dengan fokus pada nilai tambah dan layanan.',
    startDate: '2024-10-01',
    endDate: '2025-03-31',
    recommendationLevel: RecommendationLevel.POOR,
    factors: [
      getSeasonalFactors().find(f => f.id === 'sf-1') || {
        id: 'sf-1',
        type: SeasonalFactorType.WEATHER,
        name: 'Musim Hujan',
        description: 'Musim hujan di Indonesia yang dapat mempengaruhi operasi pertambangan dan kebutuhan ban',
        impact: -7
      }
    ],
    recommendedPromoTypes: ['Service Package', 'Extended Warranty'],
    recommendedProducts: ['XTXL', 'X-TRACTION'],
    potentialImpact: 65,
    confidence: 88,
    aiGenerated: true
  },
  {
    id: 'insight-3',
    title: 'Peluang Mining Indonesia Expo',
    description: 'Mining Indonesia Expo adalah kesempatan besar untuk memamerkan produk dan menjangkau pelanggan potensial baru. Persiapkan strategi pameran yang komprehensif untuk memaksimalkan ROI dari acara ini.',
    startDate: '2024-09-15',
    endDate: '2024-09-18',
    recommendationLevel: RecommendationLevel.GOOD,
    factors: [
      getSeasonalFactors().find(f => f.id === 'sf-6') || {
        id: 'sf-6',
        type: SeasonalFactorType.INDUSTRY_EVENT,
        name: 'Mining Indonesia Expo',
        description: 'Pameran pertambangan terbesar di Indonesia',
        impact: 7
      }
    ],
    recommendedPromoTypes: ['Event Special', 'Demo Program'],
    recommendedProducts: ['XDR3', 'XMINE', 'XTXL'],
    potentialImpact: 75,
    confidence: 85,
    aiGenerated: false
  },
  {
    id: 'insight-4',
    title: 'Antisipasi Promosi Kompetitor',
    description: 'Bridgestone diperkirakan akan meluncurkan promosi akhir tahun yang agresif. Persiapkan strategi untuk menghadapi persaingan ini dengan menekankan keunggulan produk Michelin dan nilai jangka panjang.',
    startDate: '2024-11-15',
    endDate: '2024-12-31',
    recommendationLevel: RecommendationLevel.NEUTRAL,
    factors: [
      getSeasonalFactors().find(f => f.id === 'sf-4') || {
        id: 'sf-4',
        type: SeasonalFactorType.COMPETITOR,
        name: 'Promo Akhir Tahun Bridgestone',
        description: 'Promosi akhir tahun dari kompetitor utama Bridgestone',
        impact: -5
      }
    ],
    recommendedPromoTypes: ['Value Package', 'TCO Analysis'],
    recommendedProducts: ['XDR3', 'XMINE', 'X-SUPER TERRAIN+'],
    potentialImpact: 60,
    confidence: 75,
    aiGenerated: true
  }
];

/**
 * Get all seasonal insights
 */
export const getSeasonalInsights = (): SeasonalInsight[] => {
  try {
    const storedInsights = localStorage.getItem(SEASONAL_INSIGHTS_KEY);
    if (storedInsights) {
      return JSON.parse(storedInsights);
    }
    // If no stored insights, use sample ones and save them
    localStorage.setItem(SEASONAL_INSIGHTS_KEY, JSON.stringify(SAMPLE_SEASONAL_INSIGHTS));
    return SAMPLE_SEASONAL_INSIGHTS;
  } catch (error) {
    console.error('Error getting seasonal insights:', error);
    return SAMPLE_SEASONAL_INSIGHTS;
  }
};

/**
 * Generate calendar data for a specific month
 */
export const generateCalendarMonth = async (year: number, month: number): Promise<CalendarMonth> => {
  const factors = getSeasonalFactors();
  const settings = getCalendarSettings();

  // Create date for the first day of the month
  const startDate = new Date(year, month - 1, 1);

  // Get the number of days in the month
  const daysInMonth = new Date(year, month, 0).getDate();

  // Get the last day of the month for date range
  const endDate = new Date(year, month - 1, daysInMonth);

  // Format dates for API calls
  const startDateString = startDate.toISOString().split('T')[0];
  const endDateString = endDate.toISOString().split('T')[0];

  // Import the holiday service
  const { getHolidaysByDateRange } = await import('../services/holidayService');

  // Fetch holidays for this month
  let holidays = [];
  try {
    holidays = await getHolidaysByDateRange(startDateString, endDateString);
    console.log(`Loaded ${holidays.length} holidays for ${year}-${month}`);
  } catch (error) {
    console.error('Error loading holidays:', error);
  }

  // Generate calendar days
  const days: CalendarDay[] = [];
  let monthTotalScore = 0;

  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month - 1, day);
    const dateString = date.toISOString().split('T')[0];

    // Find factors that apply to this date
    const applicableFactors = factors.filter(factor => {
      if (!factor.startDate || !factor.endDate) return true;

      const factorStart = new Date(factor.startDate);
      const factorEnd = new Date(factor.endDate);

      return date >= factorStart && date <= factorEnd;
    });

    // Find holidays for this date
    const dayHolidays = holidays.filter(holiday => holiday.date === dateString);

    // Add holidays as factors if they exist
    if (dayHolidays.length > 0) {
      dayHolidays.forEach(holiday => {
        // Create a seasonal factor from the holiday
        const holidayFactor: SeasonalFactor = {
          id: holiday.id,
          type: SeasonalFactorType.INDUSTRY_EVENT, // Use INDUSTRY_EVENT as the type for holidays
          name: holiday.name,
          description: holiday.description,
          impact: holiday.type === 'Nasional' ? 10 : 5, // National holidays have higher impact
          startDate: holiday.date,
          endDate: holiday.date,
          region: 'Indonesia',
          source: 'API Hari Libur',
          isHoliday: true, // Mark as holiday for UI display
          holidayType: holiday.type
        };

        // Add to applicable factors
        applicableFactors.push(holidayFactor);
      });
    }

    // Calculate score based on applicable factors and their weights
    let score = 50; // Start with neutral score

    applicableFactors.forEach(factor => {
      const weight = settings.factorWeights[factor.type] / 100;
      score += factor.impact * weight;
    });

    // Ensure score is within 0-100 range
    score = Math.max(0, Math.min(100, score));
    monthTotalScore += score;

    days.push({
      date: dateString,
      recommendationLevel: calculateRecommendationLevel(score),
      factors: applicableFactors,
      score,
      notes: '',
      holidays: dayHolidays // Add holidays to the day data
    });
  }

  // Calculate average score for the month
  const averageScore = days.length > 0 ? monthTotalScore / days.length : 0;

  // Find top factors for the month
  const factorCounts: { [key: string]: { factor: SeasonalFactor, count: number } } = {};

  days.forEach(day => {
    day.factors.forEach(factor => {
      if (!factorCounts[factor.id]) {
        factorCounts[factor.id] = { factor, count: 0 };
      }
      factorCounts[factor.id].count++;
    });
  });

  const topFactors = Object.values(factorCounts)
    .sort((a, b) => b.count - a.count)
    .slice(0, 3)
    .map(item => item.factor);

  return {
    year,
    month,
    days,
    averageScore,
    topFactors,
    holidays // Add holidays to the month data
  };
};
