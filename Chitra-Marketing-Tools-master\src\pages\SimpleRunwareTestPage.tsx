import React, { useState } from 'react';
import { generateImages } from '../services/runwareServiceNew';
import { ImageGenerationRequest, ImageModel } from '../types/imageGenerator';

const SimpleRunwareTestPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  // Add log message
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // Test image generation
  const testImageGeneration = async () => {
    setIsLoading(true);
    setError(null);
    setImageUrl(null);
    setLogs([]);

    addLog('Starting image generation test...');

    try {
      // Create a simple request
      const request: ImageGenerationRequest = {
        positivePrompt: 'a cat',
        negativePrompt: '',
        width: 512,
        height: 512,
        model: ImageModel.ANIME, // Using anime model which is faster
        numberResults: 1
      };

      addLog(`Request created: ${JSON.stringify(request)}`);

      // Generate the image
      addLog('Calling generateImages function...');
      const images = await generateImages(request);
      addLog(`Received ${images.length} images from API`);

      if (images && images.length > 0) {
        setImageUrl(images[0].imageURL);
        addLog(`Image URL: ${images[0].imageURL}`);
      } else {
        throw new Error("No images were generated");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error occurred";
      setError(errorMessage);
      addLog(`ERROR: ${errorMessage}`);
    } finally {
      setIsLoading(false);
      addLog('Test completed');
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">Simple Runware API Test</h1>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <button
          onClick={testImageGeneration}
          disabled={isLoading}
          className="w-full py-2 px-4 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Generating Image...' : 'Generate Test Image'}
        </button>
        
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
            <h3 className="font-medium">Error:</h3>
            <p className="mt-1">{error}</p>
          </div>
        )}
        
        {imageUrl && (
          <div className="mt-6">
            <h3 className="text-lg font-medium mb-2">Generated Image:</h3>
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <img
                src={imageUrl}
                alt="Generated test image"
                className="w-full h-auto"
                loading="lazy"
              />
            </div>
          </div>
        )}
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium mb-2">Logs:</h3>
        <div className="bg-gray-100 p-4 rounded-md h-64 overflow-y-auto font-mono text-sm">
          {logs.length === 0 ? (
            <p className="text-gray-500">No logs yet. Click the button to start the test.</p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="mb-1">{log}</div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default SimpleRunwareTestPage;
