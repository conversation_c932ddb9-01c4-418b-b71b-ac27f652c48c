import React, { useState, useEffect } from 'react';
import { PromoSimulation, PromoStatus } from '../types/promotion';
import { 
  getPromoSimulations, 
  getPromoTemplates,
  deletePromoSimulation,
  deletePromoTemplate
} from '../services/promoLibraryService';
import { 
  BookOpen, 
  Copy, 
  Trash2, 
  Search, 
  Calendar, 
  Tag, 
  ClipboardList, 
  Clock, 
  CheckCircle, 
  Send,
  Filter
} from 'lucide-react';

interface PromoLibraryProps {
  onUseTemplate: (simulation: PromoSimulation) => void;
  onSaveAsTemplate: () => void;
}

const PromoLibrary: React.FC<PromoLibraryProps> = ({
  onUseTemplate,
  onSaveAsTemplate
}) => {
  const [simulations, setSimulations] = useState<PromoSimulation[]>([]);
  const [templates, setTemplates] = useState<PromoSimulation[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'simulations' | 'templates'>('simulations');
  const [statusFilter, setStatusFilter] = useState<PromoStatus | 'ALL'>('ALL');

  // Load simulations and templates
  useEffect(() => {
    loadData();
  }, []);

  // Load data
  const loadData = () => {
    const loadedSimulations = getPromoSimulations();
    const loadedTemplates = getPromoTemplates();
    
    setSimulations(loadedSimulations);
    setTemplates(loadedTemplates);
  };

  // Handle delete simulation
  const handleDeleteSimulation = (id: string) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus simulasi ini?')) {
      const success = deletePromoSimulation(id);
      
      if (success) {
        loadData();
      } else {
        alert('Gagal menghapus simulasi.');
      }
    }
  };

  // Handle delete template
  const handleDeleteTemplate = (id: string) => {
    if (window.confirm('Apakah Anda yakin ingin menghapus template ini?')) {
      const success = deletePromoTemplate(id);
      
      if (success) {
        loadData();
      } else {
        alert('Gagal menghapus template.');
      }
    }
  };

  // Format date
  const formatDate = (date?: Date): string => {
    if (!date) return 'N/A';
    
    return new Date(date).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status icon
  const getStatusIcon = (status?: PromoStatus) => {
    switch (status) {
      case PromoStatus.DRAFT:
        return <ClipboardList size={16} className="text-gray-500" />;
      case PromoStatus.PENDING_APPROVAL:
        return <Clock size={16} className="text-yellow-500" />;
      case PromoStatus.APPROVED:
        return <CheckCircle size={16} className="text-green-500" />;
      case PromoStatus.SENT_TO_CUSTOMER:
        return <Send size={16} className="text-blue-500" />;
      default:
        return <ClipboardList size={16} className="text-gray-500" />;
    }
  };

  // Get status label
  const getStatusLabel = (status?: PromoStatus): string => {
    switch (status) {
      case PromoStatus.DRAFT:
        return 'Draft';
      case PromoStatus.PENDING_APPROVAL:
        return 'Menunggu Approval';
      case PromoStatus.APPROVED:
        return 'Disetujui';
      case PromoStatus.SENT_TO_CUSTOMER:
        return 'Dikirim ke Customer';
      default:
        return 'Draft';
    }
  };

  // Get status color
  const getStatusColor = (status?: PromoStatus): string => {
    switch (status) {
      case PromoStatus.DRAFT:
        return 'bg-gray-100 text-gray-800';
      case PromoStatus.PENDING_APPROVAL:
        return 'bg-yellow-100 text-yellow-800';
      case PromoStatus.APPROVED:
        return 'bg-green-100 text-green-800';
      case PromoStatus.SENT_TO_CUSTOMER:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Filter simulations by search query and status
  const filteredSimulations = simulations.filter(simulation => {
    const matchesSearch = 
      simulation.config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (simulation.name && simulation.name.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesStatus = 
      statusFilter === 'ALL' || 
      simulation.config.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // Filter templates by search query
  const filteredTemplates = templates.filter(template => 
    template.config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (template.name && template.name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-semibold">Library Promo</h2>
        <button
          onClick={onSaveAsTemplate}
          className="flex items-center px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200"
        >
          <BookOpen size={16} className="mr-1" />
          Simpan Sebagai Template
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <div className="flex">
          <div
            className={`py-2 px-4 cursor-pointer ${
              activeTab === 'simulations'
                ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('simulations')}
          >
            Simulasi Tersimpan
          </div>
          <div
            className={`py-2 px-4 cursor-pointer ${
              activeTab === 'templates'
                ? 'border-b-2 border-blue-500 text-blue-600 font-medium'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('templates')}
          >
            Template
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search size={16} className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Cari promo..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {activeTab === 'simulations' && (
          <div className="flex items-center space-x-2">
            <Filter size={16} className="text-gray-500" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as PromoStatus | 'ALL')}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="ALL">Semua Status</option>
              <option value={PromoStatus.DRAFT}>Draft</option>
              <option value={PromoStatus.PENDING_APPROVAL}>Menunggu Approval</option>
              <option value={PromoStatus.APPROVED}>Disetujui</option>
              <option value={PromoStatus.SENT_TO_CUSTOMER}>Dikirim ke Customer</option>
            </select>
          </div>
        )}
      </div>

      {/* Content */}
      <div>
        {activeTab === 'simulations' && (
          <>
            {filteredSimulations.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {searchQuery || statusFilter !== 'ALL' ? 
                  'Tidak ada simulasi yang sesuai dengan filter.' : 
                  'Belum ada simulasi tersimpan.'}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredSimulations.map(simulation => (
                  <div 
                    key={simulation.id} 
                    className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                  >
                    <div className="p-4">
                      <div className="flex justify-between items-start">
                        <h3 className="text-md font-medium text-gray-900 truncate">
                          {simulation.config.name}
                        </h3>
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(simulation.config.status)}`}>
                          <div className="flex items-center space-x-1">
                            {getStatusIcon(simulation.config.status)}
                            <span>{getStatusLabel(simulation.config.status)}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="mt-2 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Tag size={14} />
                          <span>{simulation.config.type}</span>
                        </div>
                        <div className="flex items-center space-x-1 mt-1">
                          <Calendar size={14} />
                          <span>
                            {new Date(simulation.config.startDate).toLocaleDateString('id-ID')} - {new Date(simulation.config.endDate).toLocaleDateString('id-ID')}
                          </span>
                        </div>
                      </div>
                      
                      <div className="mt-3 text-sm text-gray-700">
                        <div className="flex justify-between">
                          <span>Produk:</span>
                          <span className="font-medium">{simulation.items.length} item</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Dibuat:</span>
                          <span>{formatDate(simulation.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 px-4 py-3 border-t border-gray-200 flex justify-between">
                      <button
                        onClick={() => onUseTemplate(simulation)}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
                      >
                        <Copy size={14} className="mr-1" />
                        Gunakan Ulang
                      </button>
                      <button
                        onClick={() => handleDeleteSimulation(simulation.id!)}
                        className="text-red-600 hover:text-red-800 text-sm font-medium flex items-center"
                      >
                        <Trash2 size={14} className="mr-1" />
                        Hapus
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}

        {activeTab === 'templates' && (
          <>
            {filteredTemplates.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {searchQuery ? 
                  'Tidak ada template yang sesuai dengan pencarian.' : 
                  'Belum ada template tersimpan.'}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredTemplates.map(template => (
                  <div 
                    key={template.id} 
                    className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                  >
                    <div className="p-4">
                      <div className="flex justify-between items-start">
                        <h3 className="text-md font-medium text-gray-900 truncate">
                          {template.name || template.config.name}
                        </h3>
                        <div className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium">
                          Template
                        </div>
                      </div>
                      
                      <div className="mt-2 text-sm text-gray-500">
                        <div className="flex items-center space-x-1">
                          <Tag size={14} />
                          <span>{template.config.type}</span>
                        </div>
                      </div>
                      
                      <div className="mt-3 text-sm text-gray-700">
                        <div className="flex justify-between">
                          <span>Produk:</span>
                          <span className="font-medium">{template.items.length} item</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Dibuat:</span>
                          <span>{formatDate(template.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 px-4 py-3 border-t border-gray-200 flex justify-between">
                      <button
                        onClick={() => onUseTemplate(template)}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
                      >
                        <Copy size={14} className="mr-1" />
                        Gunakan Template
                      </button>
                      <button
                        onClick={() => handleDeleteTemplate(template.id!)}
                        className="text-red-600 hover:text-red-800 text-sm font-medium flex items-center"
                      >
                        <Trash2 size={14} className="mr-1" />
                        Hapus
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default PromoLibrary;
