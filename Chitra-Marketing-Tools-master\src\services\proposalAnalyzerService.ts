/**
 * Service for analyzing proposals using the Gemini model
 */
import { v4 as uuidv4 } from 'uuid';
import { saveAs } from 'file-saver';
import {
  ProposalAnalysisRequest,
  ProposalAnalysisResponse,
  ProposalAnalysisResult,
  ProposalImprovementRequest,
  ProposalImprovementResponse,
  ProposalImprovementResult,
  ProposalImprovementSection
} from '../types/proposalAnalyzer';

// Local storage keys
const ANALYSIS_HISTORY_KEY = 'proposal_analysis_history';
const IMPROVEMENT_HISTORY_KEY = 'proposal_improvement_history';

// Gemini model configuration
const GEMINI_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const OPENROUTER_API_KEY = '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';
const MODEL = 'google/gemini-2.0-flash-001';

/**
 * Analyze a proposal PDF using the Gemini model
 */
export const analyzeProposal = async (
  request: ProposalAnalysisRequest
): Promise<ProposalAnalysisResult> => {
  try {
    console.log(`Analyzing proposal: ${request.fileName} (${request.fileSize} bytes)`);

    // Create system prompt for the Gemini model
    const systemPrompt = `You are an expert sales negotiator and proposal analyst with extensive experience in creating successful sales and marketing agreements. Your task is to analyze the provided proposal PDF and provide detailed feedback to help improve it.

Focus on:
1. Overall persuasiveness and clarity
2. Value proposition and unique selling points
3. Pricing strategy and justification
4. Competitive advantages
5. Negotiation leverage points
6. Areas that need improvement or clarification
7. Specific recommendations to increase the proposal's effectiveness

Provide your analysis in Indonesian language with a professional, constructive tone. Be specific and actionable in your recommendations.`;

    // Create user prompt with the PDF content
    const userPrompt = `Berikut adalah proposal yang perlu dianalisis. Berikan analisis mendetail tentang kekuatan dan kelemahan proposal ini, serta saran untuk meningkatkan efektivitasnya dalam mencapai kesepakatan penjualan yang menguntungkan.

Nama file: ${request.fileName}
Ukuran file: ${(request.fileSize / 1024).toFixed(2)} KB

Isi proposal (teks yang diekstrak dari PDF):
${request.pdfContent}

Berikan analisis yang komprehensif dengan format berikut:
1. Skor keseluruhan (1-10)
2. Kekuatan utama proposal
3. Kelemahan yang perlu diperbaiki
4. Saran peningkatan spesifik
5. Tips negosiasi untuk proposal ini
6. Analisis detail tentang:
   - Proposisi nilai
   - Strategi harga
   - Timeline dan deliverables
   - Keunggulan kompetitif
7. Skor terperinci (1-10) untuk:
   - Daya persuasi
   - Kejelasan
   - Proposisi nilai
   - Strategi harga

Berikan analisis dalam format JSON dengan struktur berikut:
{
  "overallScore": 7,
  "strengths": ["Kekuatan 1", "Kekuatan 2"],
  "weaknesses": ["Kelemahan 1", "Kelemahan 2"],
  "improvementSuggestions": ["Saran 1", "Saran 2"],
  "negotiationTips": ["Tip 1", "Tip 2"],
  "detailedAnalysis": "Analisis mendetail tentang proposal...",
  "keyPoints": {
    "value": "Analisis proposisi nilai",
    "pricing": "Analisis strategi harga",
    "timeline": "Analisis timeline",
    "deliverables": "Analisis deliverables"
  },
  "competitiveAdvantage": "Analisis keunggulan kompetitif",
  "persuasionScore": 7,
  "clarityScore": 8,
  "valuePropositionScore": 6,
  "pricingStrategyScore": 7
}`;

    // Call the Gemini model via OpenRouter
    const response = await fetch(GEMINI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Proposal Analyzer'
      },
      body: JSON.stringify({
        model: MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Gemini API response:', data);

    // Parse the response content as JSON
    const analysisContent = data.choices[0].message.content;
    const analysisData: ProposalAnalysisResponse = JSON.parse(analysisContent);

    // Create the analysis result with metadata
    const result: ProposalAnalysisResult = {
      ...analysisData,
      id: uuidv4(),
      fileName: request.fileName,
      fileSize: request.fileSize,
      analysisDate: new Date(),
      model: MODEL
    };

    // Save to analysis history
    saveAnalysisToHistory(result);

    return result;
  } catch (error) {
    console.error('Error analyzing proposal:', error);

    // Return error result
    return {
      id: uuidv4(),
      fileName: request.fileName,
      fileSize: request.fileSize,
      analysisDate: new Date(),
      model: MODEL,
      overallScore: 0,
      strengths: [],
      weaknesses: [],
      improvementSuggestions: [],
      negotiationTips: [],
      detailedAnalysis: '',
      keyPoints: {
        value: '',
        pricing: '',
        timeline: '',
        deliverables: ''
      },
      competitiveAdvantage: '',
      persuasionScore: 0,
      clarityScore: 0,
      valuePropositionScore: 0,
      pricingStrategyScore: 0,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
};

/**
 * Save analysis result to history
 */
const saveAnalysisToHistory = (result: ProposalAnalysisResult): void => {
  try {
    // Get existing history
    const historyJson = localStorage.getItem(ANALYSIS_HISTORY_KEY);
    const history: ProposalAnalysisResult[] = historyJson ? JSON.parse(historyJson) : [];

    // Add new result to history (limit to 20 entries)
    history.unshift(result);
    if (history.length > 20) {
      history.pop();
    }

    // Save updated history
    localStorage.setItem(ANALYSIS_HISTORY_KEY, JSON.stringify(history));
  } catch (error) {
    console.error('Error saving analysis to history:', error);
  }
};

/**
 * Get analysis history
 */
export const getAnalysisHistory = (): ProposalAnalysisResult[] => {
  try {
    const historyJson = localStorage.getItem(ANALYSIS_HISTORY_KEY);
    return historyJson ? JSON.parse(historyJson) : [];
  } catch (error) {
    console.error('Error getting analysis history:', error);
    return [];
  }
};

/**
 * Get analysis by ID
 */
export const getAnalysisById = (id: string): ProposalAnalysisResult | null => {
  try {
    const history = getAnalysisHistory();
    return history.find(item => item.id === id) || null;
  } catch (error) {
    console.error('Error getting analysis by ID:', error);
    return null;
  }
};

/**
 * Delete analysis by ID
 */
export const deleteAnalysisById = (id: string): boolean => {
  try {
    const history = getAnalysisHistory();
    const updatedHistory = history.filter(item => item.id !== id);
    localStorage.setItem(ANALYSIS_HISTORY_KEY, JSON.stringify(updatedHistory));
    return true;
  } catch (error) {
    console.error('Error deleting analysis:', error);
    return false;
  }
};

/**
 * Generate improvements for a proposal based on analysis
 */
export const generateProposalImprovements = async (
  request: ProposalImprovementRequest
): Promise<ProposalImprovementResult> => {
  try {
    console.log(`Generating improvements for proposal: ${request.analysisResult.fileName}`);

    // Create system prompt for the Gemini model
    const systemPrompt = `You are an expert proposal writer and sales negotiator with extensive experience in creating successful sales and marketing agreements. Your task is to improve the provided proposal based on the analysis that has been done.

Focus on:
1. Enhancing the value proposition
2. Strengthening the pricing strategy and justification
3. Clarifying deliverables and timeline
4. Highlighting competitive advantages
5. Improving persuasiveness and clarity
6. Addressing the weaknesses identified in the analysis

Important formatting instructions:
- Write in a natural, conversational Indonesian language that sounds like it was written by a human expert, not AI
- DO NOT use asterisks (**) or any other markdown formatting in your text
- Use proper paragraph spacing with line breaks between paragraphs for better readability
- If presenting information in a table format, clearly separate it from the regular text
- Use professional but warm language that flows naturally

Provide your improvements in Indonesian language with a professional, persuasive tone. Be specific and actionable in your suggestions.`;

    // Create user prompt with the analysis result and PDF content
    const userPrompt = `Berikut adalah proposal yang perlu diperbaiki berdasarkan analisis yang telah dilakukan.

Nama file: ${request.analysisResult.fileName}

Isi proposal (teks yang diekstrak dari PDF):
${request.pdfText || 'Konten proposal tidak tersedia'}

Hasil analisis:
- Skor keseluruhan: ${request.analysisResult.overallScore}/10
- Kekuatan: ${request.analysisResult.strengths.join(', ')}
- Kelemahan: ${request.analysisResult.weaknesses.join(', ')}
- Saran peningkatan: ${request.analysisResult.improvementSuggestions.join(', ')}
- Analisis proposisi nilai: ${request.analysisResult.keyPoints.value}
- Analisis strategi harga: ${request.analysisResult.keyPoints.pricing}
- Analisis timeline dan deliverables: ${request.analysisResult.keyPoints.timeline}, ${request.analysisResult.keyPoints.deliverables}
- Analisis keunggulan kompetitif: ${request.analysisResult.competitiveAdvantage}
- Skor daya persuasi: ${request.analysisResult.persuasionScore}/10
- Skor kejelasan: ${request.analysisResult.clarityScore}/10
- Skor proposisi nilai: ${request.analysisResult.valuePropositionScore}/10
- Skor strategi harga: ${request.analysisResult.pricingStrategyScore}/10

Berdasarkan analisis di atas, buatkan perbaikan untuk proposal ini dengan format berikut:
1. Ringkasan perbaikan secara keseluruhan
2. Bagian-bagian yang perlu diperbaiki, untuk setiap bagian berikan:
   - Judul bagian
   - Konten asli
   - Konten yang sudah diperbaiki
   - Penjelasan perbaikan yang dilakukan
3. Perbaikan keseluruhan yang direkomendasikan

PENTING - Panduan format dan gaya bahasa:
- Gunakan bahasa Indonesia yang alami dan mengalir, seperti ditulis oleh konsultan profesional manusia
- Hindari bahasa yang terdengar kaku atau seperti terjemahan mesin
- JANGAN gunakan tanda bintang (**) atau format markdown lainnya untuk penekanan
- Gunakan paragraf dengan jarak yang cukup untuk memudahkan pembacaan
- Jika ada informasi dalam bentuk tabel, pisahkan dengan jelas dari teks biasa
- Gunakan bahasa yang profesional namun hangat dan persuasif

Berikan hasil perbaikan dalam format JSON dengan struktur berikut:
{
  "summary": "Ringkasan perbaikan secara keseluruhan",
  "sections": [
    {
      "title": "Judul Bagian 1",
      "originalContent": "Konten asli bagian 1",
      "improvedContent": "Konten yang sudah diperbaiki untuk bagian 1 (dengan paragraf yang dipisahkan dengan baris kosong untuk keterbacaan)",
      "explanation": "Penjelasan perbaikan yang dilakukan untuk bagian 1 (dengan paragraf yang dipisahkan dengan baris kosong untuk keterbacaan)"
    },
    {
      "title": "Judul Bagian 2",
      "originalContent": "Konten asli bagian 2",
      "improvedContent": "Konten yang sudah diperbaiki untuk bagian 2 (dengan paragraf yang dipisahkan dengan baris kosong untuk keterbacaan)",
      "explanation": "Penjelasan perbaikan yang dilakukan untuk bagian 2 (dengan paragraf yang dipisahkan dengan baris kosong untuk keterbacaan)"
    }
  ],
  "overallImprovements": "Deskripsi perbaikan keseluruhan yang direkomendasikan (dengan paragraf yang dipisahkan dengan baris kosong untuk keterbacaan)"
}`;

    // Call the Gemini model via OpenRouter
    const response = await fetch(GEMINI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Proposal Improvements'
      },
      body: JSON.stringify({
        model: MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.4,
        max_tokens: 3000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('Gemini API response for improvements:', data);

    // Parse the response content as JSON
    try {
      // Validate that the response has the expected structure
      if (!data.choices || !data.choices[0] || !data.choices[0].message || !data.choices[0].message.content) {
        console.error('Invalid API response structure:', data);
        throw new Error('Invalid API response structure. Response does not contain expected fields.');
      }

      const improvementContent = data.choices[0].message.content;
      console.log('Raw improvement content:', improvementContent);

      // Validate that the content is valid JSON
      let improvementData: ProposalImprovementResponse;
      try {
        improvementData = JSON.parse(improvementContent);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        console.error('Content that failed to parse:', improvementContent);
        throw new Error('Failed to parse API response as JSON. The response may not be in the expected format.');
      }

      // Validate that the parsed data has the required fields
      if (!improvementData.summary || !improvementData.sections || !improvementData.overallImprovements) {
        console.error('Incomplete improvement data:', improvementData);
        throw new Error('API response is missing required fields (summary, sections, or overallImprovements).');
      }

      // Ensure each section has proper content
      const pdfText = request.pdfText || 'Konten proposal tidak tersedia';

      // Process sections to ensure they have proper content
      const processedSections = improvementData.sections.map(section => {
        // If originalContent is empty or missing, try to extract it from the PDF text
        if (!section.originalContent || section.originalContent === 'Konten proposal tidak tersedia') {
          // Try to find a section in the PDF text that matches the title
          const titlePattern = new RegExp(`${section.title.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}[\\s\\S]*?(?=\\d+\\.\\s|$)`, 'i');
          const match = pdfText.match(titlePattern);

          if (match) {
            section.originalContent = match[0].trim();
          } else {
            // If no match found, use a relevant portion of the PDF text
            const lines = pdfText.split('\n');
            const relevantLines = lines.slice(0, Math.min(lines.length, 10)).join('\n');
            section.originalContent = relevantLines || 'Konten asli tidak dapat ditemukan';
          }
        }

        return section;
      });

      // Create the improvement result with metadata
      const result: ProposalImprovementResult = {
        ...improvementData,
        sections: processedSections,
        id: uuidv4(),
        analysisId: request.analysisResult.id,
        fileName: request.analysisResult.fileName,
        improvementDate: new Date(),
        model: MODEL
      };

      // Save to improvement history
      saveImprovementToHistory(result);

      return result;
    } catch (parseError) {
      console.error('Error processing API response:', parseError);
      throw new Error(`Error processing API response: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
    }
  } catch (error) {
    console.error('Error generating proposal improvements:', error);

    // Get a more detailed error message
    let errorMessage = 'Unknown error occurred';
    let errorDetails = '';

    if (error instanceof Error) {
      errorMessage = error.message;
      errorDetails = error.stack || '';
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && typeof error === 'object') {
      errorMessage = JSON.stringify(error);
    }

    console.error('Error details:', errorMessage);
    if (errorDetails) console.error('Error stack:', errorDetails);

    // Return error result with more detailed information
    return {
      id: uuidv4(),
      analysisId: request.analysisResult.id,
      fileName: request.analysisResult.fileName,
      improvementDate: new Date(),
      model: MODEL,
      summary: `Terjadi kesalahan saat menghasilkan perbaikan: ${errorMessage}`,
      sections: [{
        title: 'Informasi Kesalahan',
        originalContent: request.pdfText || 'Tidak dapat memproses konten asli karena terjadi kesalahan.',
        improvedContent: 'Silakan coba lagi atau hubungi administrator jika masalah berlanjut.',
        explanation: `Detail kesalahan: ${errorMessage}`
      }],
      overallImprovements: 'Tidak dapat menghasilkan perbaikan karena terjadi kesalahan. Silakan coba lagi nanti.',
      error: errorMessage
    };
  }
};

/**
 * Save improvement result to history
 */
const saveImprovementToHistory = (result: ProposalImprovementResult): void => {
  try {
    // Get existing history
    const historyJson = localStorage.getItem(IMPROVEMENT_HISTORY_KEY);
    const history: ProposalImprovementResult[] = historyJson ? JSON.parse(historyJson) : [];

    // Add new result to history (limit to 20 entries)
    history.unshift(result);
    if (history.length > 20) {
      history.pop();
    }

    // Save updated history
    localStorage.setItem(IMPROVEMENT_HISTORY_KEY, JSON.stringify(history));
  } catch (error) {
    console.error('Error saving improvement to history:', error);
  }
};

/**
 * Get improvement history
 */
export const getImprovementHistory = (): ProposalImprovementResult[] => {
  try {
    const historyJson = localStorage.getItem(IMPROVEMENT_HISTORY_KEY);
    return historyJson ? JSON.parse(historyJson) : [];
  } catch (error) {
    console.error('Error getting improvement history:', error);
    return [];
  }
};

/**
 * Get improvement by analysis ID
 */
export const getImprovementByAnalysisId = (analysisId: string): ProposalImprovementResult | null => {
  try {
    const history = getImprovementHistory();
    return history.find(item => item.analysisId === analysisId) || null;
  } catch (error) {
    console.error('Error getting improvement by analysis ID:', error);
    return null;
  }
};

/**
 * Delete improvement by ID
 */
export const deleteImprovementById = (id: string): boolean => {
  try {
    const history = getImprovementHistory();
    const updatedHistory = history.filter(item => item.id !== id);
    localStorage.setItem(IMPROVEMENT_HISTORY_KEY, JSON.stringify(updatedHistory));
    return true;
  } catch (error) {
    console.error('Error deleting improvement:', error);
    return false;
  }
};

/**
 * Generate a DOC file from analysis result
 */
export const generateAnalysisDoc = (analysis: ProposalAnalysisResult): void => {
  try {
    // Create HTML content for the document
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Analisis Proposal - ${analysis.fileName}</title>
        <style>
          body {
            font-family: Calibri, Arial, sans-serif;
            margin: 1in;
            line-height: 1.5;
          }
          .title {
            font-size: 24pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 12pt;
            color: #4F3CC9;
          }
          .subtitle {
            font-size: 18pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 24pt;
            color: #4F3CC9;
          }
          .info {
            font-size: 12pt;
            text-align: center;
            margin-bottom: 6pt;
          }
          .date {
            font-size: 12pt;
            text-align: center;
            margin-bottom: 24pt;
          }
          h1 {
            font-size: 16pt;
            font-weight: bold;
            margin-top: 24pt;
            margin-bottom: 12pt;
            color: #4F3CC9;
          }
          h2 {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 18pt;
            margin-bottom: 9pt;
            color: #4F3CC9;
          }
          p {
            font-size: 12pt;
            margin-bottom: 9pt;
          }
          ul {
            margin-bottom: 9pt;
          }
          li {
            font-size: 12pt;
            margin-bottom: 3pt;
          }
          .score-box {
            border: 1px solid #4F3CC9;
            padding: 10px;
            margin: 10px 0;
            background-color: #F5F3FF;
          }
          .score-title {
            font-weight: bold;
            color: #4F3CC9;
          }
          .score-value {
            font-size: 16pt;
            font-weight: bold;
            color: #4F3CC9;
          }
          .section {
            margin-top: 20px;
            border: 1px solid #E5E7EB;
            padding: 15px;
            background-color: #F9FAFB;
          }
          .section-title {
            font-weight: bold;
            color: #4F3CC9;
            margin-bottom: 10px;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10pt;
            color: #6B7280;
          }
          .page-break {
            page-break-before: always;
          }
        </style>
      </head>
      <body>
        <div class="title">Analisis Proposal</div>
        <div class="subtitle">${analysis.fileName}</div>
        <div class="info">Ukuran File: ${(analysis.fileSize / 1024).toFixed(2)} KB</div>
        <div class="date">Tanggal Analisis: ${new Date(analysis.analysisDate).toLocaleString('id-ID')}</div>

        <!-- Overall Score -->
        <div class="score-box">
          <div class="score-title">Skor Keseluruhan</div>
          <div class="score-value">${analysis.overallScore.toFixed(1)}/10</div>
        </div>

        <!-- Detailed Scores -->
        <h1>Skor Terperinci</h1>
        <ul>
          <li><span class="score-title">Daya Persuasi:</span> ${analysis.persuasionScore.toFixed(1)}/10</li>
          <li><span class="score-title">Kejelasan:</span> ${analysis.clarityScore.toFixed(1)}/10</li>
          <li><span class="score-title">Proposisi Nilai:</span> ${analysis.valuePropositionScore.toFixed(1)}/10</li>
          <li><span class="score-title">Strategi Harga:</span> ${analysis.pricingStrategyScore.toFixed(1)}/10</li>
        </ul>

        <!-- Strengths -->
        <h1>Kekuatan Utama</h1>
        <ul>
          ${analysis.strengths.map(strength => `<li>${strength}</li>`).join('')}
        </ul>

        <!-- Weaknesses -->
        <h1>Kelemahan</h1>
        <ul>
          ${analysis.weaknesses.map(weakness => `<li>${weakness}</li>`).join('')}
        </ul>

        <!-- Improvement Suggestions -->
        <h1>Saran Peningkatan</h1>
        <ul>
          ${analysis.improvementSuggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
        </ul>

        <!-- Negotiation Tips -->
        <h1>Tips Negosiasi</h1>
        <ul>
          ${analysis.negotiationTips.map(tip => `<li>${tip}</li>`).join('')}
        </ul>

        <div class="page-break"></div>

        <!-- Key Points -->
        <h1>Poin-Poin Kunci</h1>

        <div class="section">
          <div class="section-title">Proposisi Nilai</div>
          <p>${analysis.keyPoints.value}</p>
        </div>

        <div class="section">
          <div class="section-title">Strategi Harga</div>
          <p>${analysis.keyPoints.pricing}</p>
        </div>

        <div class="section">
          <div class="section-title">Timeline</div>
          <p>${analysis.keyPoints.timeline}</p>
        </div>

        <div class="section">
          <div class="section-title">Deliverables</div>
          <p>${analysis.keyPoints.deliverables}</p>
        </div>

        <!-- Competitive Advantage -->
        <h1>Keunggulan Kompetitif</h1>
        <p>${analysis.competitiveAdvantage}</p>

        <!-- Detailed Analysis -->
        <h1>Analisis Mendetail</h1>
        <p>${analysis.detailedAnalysis}</p>

        <div class="footer">
          Dibuat dengan Chitra Marketing Tools - Proposal Analyzer<br>
          Model AI: ${analysis.model}<br>
          Tanggal: ${new Date().toLocaleDateString('id-ID')}
        </div>
      </body>
      </html>
    `;

    // Convert HTML to a Blob
    const blob = new Blob([htmlContent], { type: 'application/vnd.ms-word' });

    // Save the blob as a DOC file
    saveAs(blob, `Analisis_Proposal_${analysis.fileName.replace(/\.[^/.]+$/, '')}.doc`);
  } catch (error) {
    console.error('Error generating analysis DOC:', error);
    alert('Gagal membuat dokumen analisis. Silakan coba lagi.');
  }
};

/**
 * Generate a DOC file from improvement result
 */
export const generateImprovementDoc = (improvement: ProposalImprovementResult, analysis: ProposalAnalysisResult): void => {
  try {
    // Create HTML content for the document
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Perbaikan Proposal - ${improvement.fileName}</title>
        <style>
          body {
            font-family: Calibri, Arial, sans-serif;
            margin: 1in;
            line-height: 1.5;
          }
          .title {
            font-size: 24pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 12pt;
            color: #047857;
          }
          .subtitle {
            font-size: 18pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 24pt;
            color: #047857;
          }
          .info {
            font-size: 12pt;
            text-align: center;
            margin-bottom: 6pt;
          }
          .date {
            font-size: 12pt;
            text-align: center;
            margin-bottom: 24pt;
          }
          h1 {
            font-size: 16pt;
            font-weight: bold;
            margin-top: 24pt;
            margin-bottom: 12pt;
            color: #047857;
          }
          h2 {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 18pt;
            margin-bottom: 9pt;
            color: #047857;
          }
          p {
            font-size: 12pt;
            margin-bottom: 9pt;
          }
          ul {
            margin-bottom: 9pt;
          }
          li {
            font-size: 12pt;
            margin-bottom: 3pt;
          }
          .section {
            margin-top: 20px;
            border: 1px solid #E5E7EB;
            padding: 15px;
            background-color: #F9FAFB;
          }
          .section-title {
            font-weight: bold;
            color: #047857;
            margin-bottom: 10px;
          }
          .original {
            background-color: #FEF3F2;
            padding: 10px;
            border-left: 3px solid #F87171;
            margin-bottom: 10px;
          }
          .improved {
            background-color: #ECFDF5;
            padding: 10px;
            border-left: 3px solid #34D399;
            margin-bottom: 10px;
          }
          .explanation {
            background-color: #F3F4F6;
            padding: 10px;
            font-style: italic;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10pt;
            color: #6B7280;
          }
          .page-break {
            page-break-before: always;
          }
        </style>
      </head>
      <body>
        <div class="title">Perbaikan Proposal</div>
        <div class="subtitle">${improvement.fileName}</div>
        <div class="info">Berdasarkan analisis dengan skor: ${analysis.overallScore.toFixed(1)}/10</div>
        <div class="date">Tanggal Perbaikan: ${new Date(improvement.improvementDate).toLocaleString('id-ID')}</div>

        <!-- Summary -->
        <h1>Ringkasan Perbaikan</h1>
        <p>${improvement.summary}</p>

        <div class="page-break"></div>

        <!-- Sections -->
        <h1>Bagian-Bagian yang Diperbaiki</h1>

        ${improvement.sections.map((section, index) => `
          <div class="section">
            <div class="section-title">${section.title}</div>

            <h2>Konten Asli:</h2>
            <div class="original">
              <p>${section.originalContent}</p>
            </div>

            <h2>Konten yang Diperbaiki:</h2>
            <div class="improved">
              <p>${section.improvedContent}</p>
            </div>

            <h2>Penjelasan Perbaikan:</h2>
            <div class="explanation">
              <p>${section.explanation}</p>
            </div>
          </div>
          ${index < improvement.sections.length - 1 ? '<div class="page-break"></div>' : ''}
        `).join('')}

        <div class="page-break"></div>

        <!-- Overall Improvements -->
        <h1>Rekomendasi Perbaikan Keseluruhan</h1>
        <p>${improvement.overallImprovements}</p>

        <div class="footer">
          Dibuat dengan Chitra Marketing Tools - Proposal Analyzer<br>
          Model AI: ${improvement.model}<br>
          Tanggal: ${new Date().toLocaleDateString('id-ID')}
        </div>
      </body>
      </html>
    `;

    // Convert HTML to a Blob
    const blob = new Blob([htmlContent], { type: 'application/vnd.ms-word' });

    // Save the blob as a DOC file
    saveAs(blob, `Perbaikan_Proposal_${improvement.fileName.replace(/\.[^/.]+$/, '')}.doc`);
  } catch (error) {
    console.error('Error generating improvement DOC:', error);
    alert('Gagal membuat dokumen perbaikan. Silakan coba lagi.');
  }
};

/**
 * Generate a TXT file from analysis result
 */
export const generateAnalysisTxt = (analysis: ProposalAnalysisResult): void => {
  try {
    // Create text content
    const textContent = `
ANALISIS PROPOSAL
=================
${analysis.fileName}
Ukuran File: ${(analysis.fileSize / 1024).toFixed(2)} KB
Tanggal Analisis: ${new Date(analysis.analysisDate).toLocaleString('id-ID')}

SKOR KESELURUHAN: ${analysis.overallScore.toFixed(1)}/10

SKOR TERPERINCI:
- Daya Persuasi: ${analysis.persuasionScore.toFixed(1)}/10
- Kejelasan: ${analysis.clarityScore.toFixed(1)}/10
- Proposisi Nilai: ${analysis.valuePropositionScore.toFixed(1)}/10
- Strategi Harga: ${analysis.pricingStrategyScore.toFixed(1)}/10

KEKUATAN UTAMA:
${analysis.strengths.map(strength => `- ${strength}`).join('\n')}

KELEMAHAN:
${analysis.weaknesses.map(weakness => `- ${weakness}`).join('\n')}

SARAN PENINGKATAN:
${analysis.improvementSuggestions.map(suggestion => `- ${suggestion}`).join('\n')}

TIPS NEGOSIASI:
${analysis.negotiationTips.map(tip => `- ${tip}`).join('\n')}

POIN-POIN KUNCI:
---------------

Proposisi Nilai:
${analysis.keyPoints.value}

Strategi Harga:
${analysis.keyPoints.pricing}

Timeline:
${analysis.keyPoints.timeline}

Deliverables:
${analysis.keyPoints.deliverables}

KEUNGGULAN KOMPETITIF:
${analysis.competitiveAdvantage}

ANALISIS MENDETAIL:
${analysis.detailedAnalysis}

=================
Dibuat dengan Chitra Marketing Tools - Proposal Analyzer
Model AI: ${analysis.model}
Tanggal: ${new Date().toLocaleDateString('id-ID')}
`;

    // Convert text to a Blob
    const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8' });

    // Save the blob as a TXT file
    saveAs(blob, `Analisis_Proposal_${analysis.fileName.replace(/\.[^/.]+$/, '')}.txt`);
  } catch (error) {
    console.error('Error generating analysis TXT:', error);
    alert('Gagal membuat dokumen analisis. Silakan coba lagi.');
  }
};
