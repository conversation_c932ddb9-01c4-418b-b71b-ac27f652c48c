/**
 * Runware.ai Image Generation Service
 *
 * This service provides functions for generating images using the Runware.ai API.
 */

import { ImageGenerationRequest, GeneratedImage, SavedImage } from '../types/imageGenerator';
import { v4 as uuidv4 } from 'uuid';

// Runware API key loaded from environment variable for security
const RUNWARE_API_KEY = process.env.RUNWARE_API_KEY || 'AYKdFJicrfah3pHCIJFbskrBndRmlhCM';

// Runware API endpoint
const RUNWARE_API_URL = 'https://api.runware.ai/v1';

// Local storage key for image history
const IMAGE_HISTORY_KEY = 'runware_image_history';

/**
 * Save generated images to local storage history
 * @param images Array of generated images
 */
export const saveImagesToHistory = (images: GeneratedImage[]): void => {
  try {
    // Get existing history from local storage
    const existingHistoryJson = localStorage.getItem(IMAGE_HISTORY_KEY);
    const existingHistory = existingHistoryJson ? JSON.parse(existingHistoryJson) : { images: [] };

    // Add new images to history with unique IDs
    const newImages: SavedImage[] = images.map(image => ({
      ...image,
      id: uuidv4(),
      tags: []
    }));

    // Update history with new images (add to beginning)
    const updatedHistory = {
      images: [...newImages, ...existingHistory.images]
    };

    // Save updated history to local storage
    localStorage.setItem(IMAGE_HISTORY_KEY, JSON.stringify(updatedHistory));
  } catch (error) {
    console.error('Error saving images to history:', error);
  }
};

/**
 * Get image generation history from local storage
 * @returns Array of saved images
 */
export const getImageHistory = (): SavedImage[] => {
  try {
    const historyJson = localStorage.getItem(IMAGE_HISTORY_KEY);
    if (!historyJson) {
      return [];
    }

    const history = JSON.parse(historyJson);
    return history.images || [];
  } catch (error) {
    console.error('Error getting image history:', error);
    return [];
  }
};

/**
 * Clear image generation history
 */
export const clearImageHistory = (): void => {
  try {
    localStorage.removeItem(IMAGE_HISTORY_KEY);
  } catch (error) {
    console.error('Error clearing image history:', error);
  }
};

/**
 * Delete a specific image from history
 * @param imageId ID of the image to delete
 */
export const deleteImageFromHistory = (imageId: string): void => {
  try {
    const history = getImageHistory();
    const updatedHistory = history.filter(image => image.id !== imageId);

    localStorage.setItem(IMAGE_HISTORY_KEY, JSON.stringify({ images: updatedHistory }));
  } catch (error) {
    console.error('Error deleting image from history:', error);
  }
};

/**
 * Generate images using Runware.ai API
 * @param request Image generation request
 * @returns Promise with array of generated images
 *
 * Example usage:
 *   import { generateImages } from './runwareService';
 *   const request = {
 *     positivePrompt: 'A beautiful sunset over the mountains',
 *     negativePrompt: '',
 *     width: 512,
 *     height: 512,
 *     model: 'civitai:102438@133677',
 *     numberResults: 1
 *   };
 *   generateImages(request).then(images => {
 *     console.log('Generated images:', images);
 *   }).catch(error => {
 *     console.error('Image generation error:', error);
 *   });
 */
export const generateImages = async (request: ImageGenerationRequest): Promise<GeneratedImage[]> => {
  try {
    console.log('Generating images with Runware.ai:', request);

    const { positivePrompt, negativePrompt, width, height, model, numberResults } = request;

    // Generate a unique task UUID
    const taskUUID = uuidv4();

    // Prepare the API request body - SIMPLIFIED based on working test
    const requestBody: any[] = [
      {
        taskType: 'imageInference',
        taskUUID,
        positivePrompt,
        width,
        height,
        model,
        numberResults
      }
    ];

    // Add negative prompt only if it's provided
    if (negativePrompt && negativePrompt.trim() !== '') {
      requestBody[0].negativePrompt = negativePrompt;
    }

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    // Create an AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

    try {
      // Call the Runware API
      const response = await fetch(RUNWARE_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${RUNWARE_API_KEY}`
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      // Clear the timeout since the request completed
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error response:', errorText);
        throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const responseData = await response.json();
      console.log('Runware API response:', JSON.stringify(responseData, null, 2));

      // Extract the generated images from the response
      const images: GeneratedImage[] = [];

      // Check if the response has the expected format with a data array
      if (responseData && responseData.data && Array.isArray(responseData.data)) {
        // Process each item in the data array
        for (const item of responseData.data) {
          // Check if this is an imageInference response with the required fields
          if (
            item.taskType === 'imageInference' &&
            item.imageUUID &&
            item.imageURL
          ) {
            images.push({
              imageUUID: item.imageUUID,
              imageURL: item.imageURL,
              prompt: positivePrompt,
              model,
              width,
              height,
              generatedAt: new Date()
            });
          }
        }
      }

      if (images.length === 0) {
        console.warn('No images found in the response:', responseData);
        throw new Error('No images were generated. Please try again with a different prompt or model.');
      }

      // Save the generated images to history
      saveImagesToHistory(images);

      return images;
    } catch (error) {
      // Make sure to clear the timeout if there's an error
      clearTimeout(timeoutId);
      throw error;
    }
  } catch (error) {
    console.error('Error generating images with Runware.ai:', error);
    throw error;
  }
};
