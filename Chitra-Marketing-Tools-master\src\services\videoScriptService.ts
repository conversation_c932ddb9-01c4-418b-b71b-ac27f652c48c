/**
 * Service for generating video scripts using OpenRouter API
 */
import {
  VideoScriptRequest,
  VideoScriptResponse,
  VideoType,
  VideoPurpose,
  VideoTargetAudience,
  VideoPlatform
} from '../types/videoScript';

// OpenRouter API key
const OPENROUTER_API_KEY = '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';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Models
export const MODELS = {
  GPT4O_NANO: 'openai/gpt-4.1-nano', // Using GPT-4o nano as requested
};

// Cache key for API responses
const API_CACHE_KEY_PREFIX = 'video_script_cache_';
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

/**
 * Generate a video script using OpenRouter API with caching
 */
export const generateVideoScript = async (request: VideoScriptRequest): Promise<VideoScriptResponse> => {
  try {
    // Create a cache key based on the request
    const cacheKey = API_CACHE_KEY_PREFIX + JSON.stringify({
      videoType: request.videoType,
      purpose: request.purpose,
      targetAudience: request.targetAudience,
      platform: request.platform,
      productName: request.productName,
      duration: request.duration
    });

    // Check if we have a cached response
    const cachedData = localStorage.getItem(cacheKey);
    if (cachedData) {
      try {
        const { data, timestamp } = JSON.parse(cachedData);
        // Use cache if it's not expired
        if (Date.now() - timestamp < CACHE_EXPIRY) {
          console.log('Using cached video script');
          return data;
        }
      } catch (error) {
        console.error('Error parsing cache:', error);
      }
    }

    console.log('Generating video script with OpenRouter API');

    // Prepare system prompt
    const systemPrompt = `Bertindaklah seperti ahli strategi konten viral yang telah menciptakan kanpanya dengan lebih dari 100 juta views di tiktok instagram dan youtube Distributor ban tambang, Tire Repair, Tire mangement, dan teknologi Tire di Indonesia. Misinya adalah membuat strategi konten viral untuk produk yang saya masukan kedalam knowladgebase / form, dengan target audiensi sesuai form.
Script yang Anda buat harus mencakup timeline detik per detik, dialog/narasi yang tepat, rekomendasi musik, dan visualisasi yang jelas.
Anda memahami teknik sinematografi, editing, dan storytelling untuk membuat video yang efektif, viral, dan menarik.`;

    // Get video type name in Indonesian
    const videoTypeNames: Record<VideoType, string> = {
      [VideoType.MARKETING_PRODUCT]: 'Marketing Produk',
      [VideoType.DOCUMENTATION]: 'Dokumentasi',
      [VideoType.REELS]: 'Reels',
      [VideoType.JOKES]: 'Jokes/Humor',
      [VideoType.REVIEW]: 'Review',
      [VideoType.TESTIMONIAL]: 'Testimoni',
      [VideoType.EDUCATIONAL]: 'Edukasi'
    };

    // Get purpose name in Indonesian
    const purposeNames: Record<VideoPurpose, string> = {
      [VideoPurpose.PROMOTION]: 'Promosi',
      [VideoPurpose.EDUCATION]: 'Edukasi',
      [VideoPurpose.VIRAL]: 'Viral',
      [VideoPurpose.DOCUMENTATION]: 'Dokumentasi'
    };

    // Get target audience name in Indonesian
    const targetAudienceNames: Record<VideoTargetAudience, string> = {
      [VideoTargetAudience.CUSTOMER]: 'Customer',
      [VideoTargetAudience.INTERNAL]: 'Internal',
      [VideoTargetAudience.GENERAL]: 'Umum'
    };

    // Get platform name
    const platformNames: Record<VideoPlatform, string> = {
      [VideoPlatform.INSTAGRAM]: 'Instagram',
      [VideoPlatform.TIKTOK]: 'TikTok',
      [VideoPlatform.YOUTUBE]: 'YouTube',
      [VideoPlatform.WHATSAPP]: 'WhatsApp'
    };

    // Prepare user prompt
    const userPrompt = `Buatkan script video yang SANGAT DETAIL dengan informasi berikut:

Jenis Video: ${videoTypeNames[request.videoType]}
Tujuan: ${purposeNames[request.purpose]}
Target Audiens: ${targetAudienceNames[request.targetAudience]}
Platform: ${platformNames[request.platform]}
${request.productName ? `Nama Produk: ${request.productName}` : ''}
${request.duration ? `Durasi: ${request.duration} detik` : ''}
${request.additionalInfo ? `Informasi Tambahan: ${request.additionalInfo}` : ''}

Berikan respons dalam format JSON berikut:
{
  "title": "Judul Video",
  "sections": [
    {
      "name": "Opening",
      "content": "Isi script untuk bagian pembuka",
      "duration": "Estimasi durasi dalam detik",
      "timeline": [
        {
          "startTime": "0:00",
          "endTime": "0:05",
          "shotType": "wide_shot/close_up/etc",
          "dialogue": {
            "speaker": "Narrator/Presenter/etc",
            "text": "Teks dialog atau narasi yang tepat",
            "tone": "Nada suara (enthusiastic/serious/etc)"
          },
          "visual": {
            "description": "Deskripsi visual yang detail",
            "transition": "Jenis transisi (cut/fade/etc)",
            "effects": "Efek visual jika ada"
          },
          "music": {
            "type": "Jenis musik (upbeat/dramatic/etc)",
            "description": "Deskripsi musik",
            "volume": "Volume musik (high/medium/low)"
          },
          "location": "Nama lokasi",
          "visualObjects": ["Objek 1", "Objek 2"],
          "notes": "Catatan tambahan"
        }
      ]
    },
    {
      "name": "Masalah",
      "content": "Isi script untuk bagian masalah",
      "duration": "Estimasi durasi dalam detik",
      "timeline": [...]
    },
    {
      "name": "Solusi",
      "content": "Isi script untuk bagian solusi",
      "duration": "Estimasi durasi dalam detik",
      "timeline": [...]
    },
    {
      "name": "CTA",
      "content": "Isi script untuk bagian call to action",
      "duration": "Estimasi durasi dalam detik",
      "timeline": [...]
    }
  ],
  "shotRecommendations": [
    {
      "shotType": "close_up/medium_shot/wide_shot/b_roll/testimonial/product_showcase/timelapse/drone/pan/tilt/tracking/dolly/zoom/handheld/static",
      "description": "Deskripsi shot yang detail",
      "duration": "Estimasi durasi dalam detik",
      "cameraMovement": "Pergerakan kamera jika ada",
      "angle": "Sudut pengambilan gambar",
      "lighting": "Pencahayaan yang direkomendasikan"
    }
  ],
  "locationRecommendations": [
    {
      "name": "Nama lokasi",
      "description": "Deskripsi lokasi yang detail",
      "lighting": "Pencahayaan yang direkomendasikan",
      "timeOfDay": "Waktu pengambilan gambar",
      "weatherCondition": "Kondisi cuaca yang direkomendasikan"
    }
  ],
  "visualObjectRecommendations": [
    {
      "name": "Nama objek visual",
      "purpose": "Tujuan penggunaan objek",
      "placement": "Penempatan objek (foreground/background)",
      "interaction": "Cara interaksi dengan objek"
    }
  ],
  "musicRecommendations": [
    {
      "type": "upbeat/dramatic/corporate/inspirational/energetic/calm/suspenseful/playful/emotional/ambient",
      "description": "Deskripsi musik yang detail",
      "startTime": "Waktu mulai",
      "endTime": "Waktu selesai",
      "mood": "Mood musik",
      "tempo": "Tempo musik (fast/medium/slow)",
      "volume": "Volume musik (high/medium/low)"
    }
  ],
  "timeline": [
    {
      "startTime": "0:00",
      "endTime": "0:05",
      "shotType": "wide_shot/close_up/etc",
      "dialogue": {
        "speaker": "Narrator/Presenter/etc",
        "text": "Teks dialog atau narasi yang tepat",
        "tone": "Nada suara (enthusiastic/serious/etc)"
      },
      "visual": {
        "description": "Deskripsi visual yang detail",
        "transition": "Jenis transisi (cut/fade/etc)",
        "effects": "Efek visual jika ada"
      },
      "music": {
        "type": "Jenis musik (upbeat/dramatic/etc)",
        "description": "Deskripsi musik",
        "volume": "Volume musik (high/medium/low)"
      },
      "location": "Nama lokasi",
      "visualObjects": ["Objek 1", "Objek 2"],
      "notes": "Catatan tambahan"
    }
  ],
  "totalDuration": "Total durasi dalam detik",
  "additionalNotes": "Catatan tambahan jika ada"
}

Pastikan script sesuai dengan jenis video, tujuan, target audiens, dan platform yang diminta.

SANGAT PENTING:
1. Berikan timeline yang SANGAT DETAIL dengan waktu yang tepat (detik per detik)
2. Berikan dialog/narasi yang LENGKAP dan TEPAT, bukan hanya deskripsi
3. Berikan rekomendasi musik yang SPESIFIK untuk setiap bagian
4. Berikan visualisasi yang JELAS dan DETAIL
5. Pastikan semua elemen (shot, dialog, musik, visual) SINKRON dalam timeline

Untuk industri ban dan otomotif, pastikan konten mencakup:
- Keunggulan produk ban (daya tahan, grip, efisiensi bahan bakar, dll)
- Teknologi yang digunakan dalam produk
- Manfaat untuk pengguna (keamanan, kenyamanan, penghematan, dll)
- Testimoni atau bukti kualitas jika relevan`;

    // Call OpenRouter API with GPT-4o nano model
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - Video Script Generator'
      },
      body: JSON.stringify({
        model: MODELS.GPT4O_NANO,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 4000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    console.log('OpenRouter API response:', data);

    if (data.choices && data.choices[0] && data.choices[0].message && data.choices[0].message.content) {
      try {
        const result = JSON.parse(data.choices[0].message.content);

        // Cache the result
        try {
          localStorage.setItem(cacheKey, JSON.stringify({
            data: result,
            timestamp: Date.now()
          }));
          console.log('Cached video script response');
        } catch (cacheError) {
          console.error('Error caching response:', cacheError);
          // Continue even if caching fails
        }

        return result;
      } catch (error) {
        console.error('Error parsing JSON response:', error);
        throw new Error('Failed to parse API response');
      }
    }

    throw new Error('Invalid API response format');
  } catch (error) {
    console.error('Error generating video script:', error);

    // Return a fallback response
    return {
      title: 'Script Video (Error Generating)',
      sections: [
        {
          name: 'Opening',
          content: 'Selamat datang di video kami tentang produk ban terbaru.',
          duration: '10 detik'
        },
        {
          name: 'Masalah',
          content: 'Banyak pengemudi mengalami masalah dengan ban yang cepat aus dan tidak tahan lama.',
          duration: '15 detik'
        },
        {
          name: 'Solusi',
          content: 'Produk ban kami dirancang dengan teknologi terbaru untuk ketahanan maksimal dan performa optimal.',
          duration: '20 detik'
        },
        {
          name: 'CTA',
          content: 'Kunjungi dealer terdekat atau hubungi kami untuk informasi lebih lanjut.',
          duration: '10 detik'
        }
      ],
      shotRecommendations: [
        {
          shotType: 'product_showcase',
          description: 'Close-up produk ban dengan pencahayaan yang baik',
          duration: '5-10 detik'
        }
      ],
      locationRecommendations: [
        {
          name: 'Showroom',
          description: 'Ruang pamer dengan pencahayaan yang baik'
        }
      ],
      visualObjectRecommendations: [
        {
          name: 'Ban',
          purpose: 'Menunjukkan produk utama'
        }
      ],
      totalDuration: '55 detik',
      additionalNotes: 'Terjadi kesalahan saat menghasilkan script. Ini adalah script default.'
    };
  }
};
