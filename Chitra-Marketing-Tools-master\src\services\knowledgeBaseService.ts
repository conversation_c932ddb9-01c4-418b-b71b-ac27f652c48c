import {
  KnowledgeEntry,
  KnowledgeEntryCreateRequest,
  KnowledgeEntryUpdateRequest,
  KnowledgeSearchRequest,
  KnowledgeSearchResponse,
  KnowledgeCategory
} from '../types/knowledgeBase';

// Local storage key for knowledge base entries
const KNOWLEDGE_BASE_STORAGE_KEY = 'chitraMarketingTools_knowledgeBase';

// Sample knowledge base entries for initial data
const SAMPLE_KNOWLEDGE_ENTRIES: KnowledgeEntry[] = [
  {
    id: 'kb-1',
    title: 'Prinsip Psikologi dalam Negosiasi: Reciprocity',
    content: 'Prinsip reciprocity (timbal balik) adalah salah satu prinsip persuasi yang paling kuat. Ketika seseorang memberikan sesuatu kepada kita, kita merasa terdorong untuk membalasnya. Dalam negosiasi, berikan sesuatu terlebih dahulu (informasi, konsesi kecil, atau nilai tambah) untuk menciptakan rasa kewajiban pada pelanggan untuk membalas. Contoh: "Kami bisa memberikan pelatihan gratis untuk tim Anda sebagai bagian dari pembelian ini."',
    category: KnowledgeCategory.PSYCHOLOGICAL_PRINCIPLES,
    tags: ['psikologi', 'persuasi', 'negosiasi', 'reciprocity'],
    createdAt: new Date('2023-01-15'),
    updatedAt: new Date('2023-01-15'),
    isActive: true
  },
  {
    id: 'kb-2',
    title: 'Menangani Keberatan Harga dengan Teknik Feel-Felt-Found',
    content: 'Teknik Feel-Felt-Found adalah cara efektif untuk menangani keberatan dengan empati. Struktur: "Saya mengerti Anda MERASA harga ini terlalu tinggi. Pelanggan lain juga pernah MERASA seperti itu. Namun, mereka MENEMUKAN bahwa nilai jangka panjang dan penghematan biaya operasional membuat investasi ini sangat berharga." Teknik ini menunjukkan empati, memberikan social proof, dan menawarkan solusi.',
    category: KnowledgeCategory.OBJECTION_HANDLING,
    tags: ['keberatan', 'harga', 'empati', 'teknik komunikasi'],
    createdAt: new Date('2023-02-20'),
    updatedAt: new Date('2023-02-20'),
    isActive: true
  },
  {
    id: 'kb-3',
    title: 'Perbandingan Harga Ban 27.00R49 dengan Kompetitor',
    content: 'Ban 27.00R49 kami memiliki harga yang kompetitif dibandingkan dengan merek lain di pasar. Bridgestone: Rp230-250 juta, Michelin: Rp240-260 juta, Goodyear: Rp220-240 juta. Keunggulan ban kami adalah umur pakai yang lebih panjang (10-15% lebih lama) dan ketahanan terhadap panas yang lebih baik, yang mengurangi risiko kerusakan pada kondisi tambang yang berat.',
    category: KnowledgeCategory.COMPETITOR_INFO,
    tags: ['27.00R49', 'perbandingan harga', 'kompetitor', 'ban tambang'],
    createdAt: new Date('2023-03-10'),
    updatedAt: new Date('2023-03-10'),
    isActive: true
  },
  {
    id: 'kb-4',
    title: 'Strategi Bundling untuk Ban Tambang',
    content: 'Bundling ban tambang dengan sensor pemantauan tekanan (TPMS) dapat meningkatkan nilai penawaran tanpa mengurangi margin secara signifikan. Sensor TPMS memiliki margin 30-40%, sementara ban memiliki margin 15-20%. Dengan bundling, kita dapat menawarkan diskon 5-10% pada sensor sambil mempertahankan harga ban, sehingga menciptakan persepsi nilai yang lebih tinggi bagi pelanggan sambil mempertahankan profitabilitas keseluruhan.',
    category: KnowledgeCategory.PRICING_STRATEGY,
    tags: ['bundling', 'TPMS', 'sensor', 'strategi harga', 'nilai tambah'],
    createdAt: new Date('2023-04-05'),
    updatedAt: new Date('2023-04-05'),
    isActive: true
  },
  {
    id: 'kb-5',
    title: 'Karakteristik Pembeli dari Perusahaan Tambang Besar',
    content: 'Pembeli dari perusahaan tambang besar seperti Adaro, Berau Coal, dan Kaltim Prima Coal biasanya memiliki proses pengambilan keputusan yang kompleks dengan multiple stakeholders. Mereka sangat fokus pada Total Cost of Ownership (TCO), bukan hanya harga awal. Keandalan pasokan dan dukungan after-sales sangat penting bagi mereka karena downtime sangat mahal. Mereka juga sering meminta referensi dari pengguna lain dan bukti kinerja di lokasi serupa.',
    category: KnowledgeCategory.CUSTOMER_INSIGHTS,
    tags: ['tambang besar', 'TCO', 'stakeholders', 'proses keputusan'],
    createdAt: new Date('2023-05-12'),
    updatedAt: new Date('2023-05-12'),
    isActive: true
  }
];

/**
 * Load knowledge base entries from localStorage
 */
const loadKnowledgeEntries = (): KnowledgeEntry[] => {
  try {
    const storedEntries = localStorage.getItem(KNOWLEDGE_BASE_STORAGE_KEY);
    if (storedEntries) {
      const entries = JSON.parse(storedEntries) as KnowledgeEntry[];
      
      // Convert string dates to Date objects
      return entries.map(entry => ({
        ...entry,
        createdAt: new Date(entry.createdAt),
        updatedAt: new Date(entry.updatedAt)
      }));
    }
  } catch (error) {
    console.error('Error loading knowledge base entries:', error);
  }
  
  // Return sample entries if none in storage
  return SAMPLE_KNOWLEDGE_ENTRIES;
};

/**
 * Save knowledge base entries to localStorage
 */
const saveKnowledgeEntries = (entries: KnowledgeEntry[]): void => {
  try {
    localStorage.setItem(KNOWLEDGE_BASE_STORAGE_KEY, JSON.stringify(entries));
  } catch (error) {
    console.error('Error saving knowledge base entries:', error);
  }
};

/**
 * Get all knowledge base entries
 */
export const getAllKnowledgeEntries = async (): Promise<KnowledgeEntry[]> => {
  return loadKnowledgeEntries();
};

/**
 * Get knowledge base entry by ID
 */
export const getKnowledgeEntryById = async (id: string): Promise<KnowledgeEntry | null> => {
  const entries = loadKnowledgeEntries();
  return entries.find(entry => entry.id === id) || null;
};

/**
 * Create a new knowledge base entry
 */
export const createKnowledgeEntry = async (request: KnowledgeEntryCreateRequest): Promise<KnowledgeEntry> => {
  const entries = loadKnowledgeEntries();
  
  const newEntry: KnowledgeEntry = {
    id: `kb-${Date.now()}`,
    ...request,
    createdAt: new Date(),
    updatedAt: new Date(),
    isActive: true
  };
  
  const updatedEntries = [...entries, newEntry];
  saveKnowledgeEntries(updatedEntries);
  
  return newEntry;
};

/**
 * Update a knowledge base entry
 */
export const updateKnowledgeEntry = async (request: KnowledgeEntryUpdateRequest): Promise<KnowledgeEntry | null> => {
  const entries = loadKnowledgeEntries();
  const entryIndex = entries.findIndex(entry => entry.id === request.id);
  
  if (entryIndex === -1) {
    return null;
  }
  
  const updatedEntry: KnowledgeEntry = {
    ...entries[entryIndex],
    ...request,
    updatedAt: new Date()
  };
  
  entries[entryIndex] = updatedEntry;
  saveKnowledgeEntries(entries);
  
  return updatedEntry;
};

/**
 * Delete a knowledge base entry
 */
export const deleteKnowledgeEntry = async (id: string): Promise<boolean> => {
  const entries = loadKnowledgeEntries();
  const filteredEntries = entries.filter(entry => entry.id !== id);
  
  if (filteredEntries.length === entries.length) {
    return false;
  }
  
  saveKnowledgeEntries(filteredEntries);
  return true;
};

/**
 * Search knowledge base entries
 */
export const searchKnowledgeEntries = async (request: KnowledgeSearchRequest): Promise<KnowledgeSearchResponse> => {
  const entries = loadKnowledgeEntries();
  
  let filteredEntries = entries;
  
  // Filter by active status if specified
  if (request.isActive !== undefined) {
    filteredEntries = filteredEntries.filter(entry => entry.isActive === request.isActive);
  }
  
  // Filter by category if specified
  if (request.category) {
    filteredEntries = filteredEntries.filter(entry => entry.category === request.category);
  }
  
  // Filter by tags if specified
  if (request.tags && request.tags.length > 0) {
    filteredEntries = filteredEntries.filter(entry => 
      request.tags!.some(tag => entry.tags.includes(tag))
    );
  }
  
  // Filter by search query if specified
  if (request.query) {
    const query = request.query.toLowerCase();
    filteredEntries = filteredEntries.filter(entry => 
      entry.title.toLowerCase().includes(query) || 
      entry.content.toLowerCase().includes(query) ||
      entry.tags.some(tag => tag.toLowerCase().includes(query))
    );
  }
  
  return {
    entries: filteredEntries,
    total: filteredEntries.length
  };
};
