@echo off
color 0A
title Chitra Marketing Tools Launcher
cls

:MENU
echo ===================================================
echo         CHITRA MARKETING TOOLS LAUNCHER
echo ===================================================
echo.
echo Pilih mode untuk menjalankan aplikasi:
echo.
echo [1] Mode Pengembangan (Development)
echo [2] Mode dengan Proxy
echo [3] Mode Lengkap (dengan Proxy dan Server Negosiasi)
echo [4] Build Aplikasi Portable
echo [5] Install Dependencies
echo [6] Keluar
echo.
set /p pilihan="Masukkan pilihan (1-6): "

if "%pilihan%"=="1" (
    cls
    echo ===================================================
    echo    MENJALANKAN CHITRA MARKETING TOOLS (DEV MODE)
    echo ===================================================
    echo.
    echo Memulai aplikasi dalam mode pengembangan...
    echo.
    npm run electron:dev
    echo.
    echo Aplikasi telah ditutup. Kembali ke menu utama...
    timeout /t 3 >nul
    goto MENU
) else if "%pilihan%"=="2" (
    cls
    echo ===================================================
    echo    MENJALANKAN CHITRA MARKETING TOOLS DENGAN PROXY
    echo ===================================================
    echo.
    echo Memulai aplikasi dengan server proxy...
    echo.
    npm run electron:dev:with-proxy
    echo.
    echo Aplikasi telah ditutup. Kembali ke menu utama...
    timeout /t 3 >nul
    goto MENU
) else if "%pilihan%"=="3" (
    cls
    echo ===================================================
    echo    MENJALANKAN CHITRA MARKETING TOOLS (FULL MODE)
    echo ===================================================
    echo.
    echo Memulai aplikasi dengan proxy dan server negosiasi...
    echo.
    npm run electron:dev:full
    echo.
    echo Aplikasi telah ditutup. Kembali ke menu utama...
    timeout /t 3 >nul
    goto MENU
) else if "%pilihan%"=="4" (
    cls
    echo ===================================================
    echo    MEMBUAT APLIKASI PORTABLE CHITRA MARKETING TOOLS
    echo ===================================================
    echo.
    echo Proses build aplikasi portable sedang berjalan...
    echo Harap tunggu, proses ini mungkin memerlukan waktu beberapa menit.
    echo.
    npm run electron:portable
    echo.
    echo Jika build berhasil, file portable dapat ditemukan di folder "Portable Chitra Marketing Tools No Watermark"
    echo.
    echo Tekan tombol apa saja untuk kembali ke menu utama...
    pause >nul
    goto MENU
) else if "%pilihan%"=="5" (
    cls
    echo ===================================================
    echo    INSTALASI DEPENDENCIES CHITRA MARKETING TOOLS
    echo ===================================================
    echo.
    echo Menginstal semua dependencies yang diperlukan...
    echo Proses ini mungkin memerlukan waktu beberapa menit.
    echo.
    npm install
    echo.
    echo Instalasi selesai!
    echo.
    echo Tekan tombol apa saja untuk kembali ke menu utama...
    pause >nul
    goto MENU
) else if "%pilihan%"=="6" (
    cls
    echo ===================================================
    echo         TERIMA KASIH TELAH MENGGUNAKAN
    echo           CHITRA MARKETING TOOLS
    echo ===================================================
    echo.
    echo Menutup aplikasi...
    timeout /t 2 >nul
    exit
) else (
    cls
    echo.
    echo Pilihan tidak valid. Silakan pilih angka 1-6.
    echo.
    timeout /t 2 >nul
    goto MENU
)
