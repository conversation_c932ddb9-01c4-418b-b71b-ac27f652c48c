/**
 * Types for Video Script Generator feature
 */

/**
 * Video types
 */
export enum VideoType {
  MARKETING_PRODUCT = 'marketing_product',
  DOCUMENTATION = 'documentation',
  REELS = 'reels',
  JOKES = 'jokes',
  REVIEW = 'review',
  TESTIMONIAL = 'testimonial',
  EDUCATIONAL = 'educational'
}

/**
 * Video purpose
 */
export enum VideoPurpose {
  PROMOTION = 'promotion',
  EDUCATION = 'education',
  VIRAL = 'viral',
  DOCUMENTATION = 'documentation'
}

/**
 * Target audience
 */
export enum VideoTargetAudience {
  CUSTOMER = 'customer',
  INTERNAL = 'internal',
  GENERAL = 'general'
}

/**
 * Video platform
 */
export enum VideoPlatform {
  INSTAGRAM = 'instagram',
  TIKTOK = 'tiktok',
  YOUTUBE = 'youtube',
  WHATSAPP = 'whatsapp'
}

/**
 * Shot type
 */
export enum ShotType {
  CLOSE_UP = 'close_up',
  MEDIUM_SHOT = 'medium_shot',
  WIDE_SHOT = 'wide_shot',
  B_ROLL = 'b_roll',
  TESTIMONIAL = 'testimonial',
  PRODUCT_SHOWCASE = 'product_showcase',
  TIMELAPSE = 'timelapse',
  DRONE = 'drone',
  PAN = 'pan',
  TILT = 'tilt',
  TRACKING = 'tracking',
  DOLLY = 'dolly',
  ZOOM = 'zoom',
  HANDHELD = 'handheld',
  STATIC = 'static'
}

/**
 * Music type
 */
export enum MusicType {
  UPBEAT = 'upbeat',
  DRAMATIC = 'dramatic',
  CORPORATE = 'corporate',
  INSPIRATIONAL = 'inspirational',
  ENERGETIC = 'energetic',
  CALM = 'calm',
  SUSPENSEFUL = 'suspenseful',
  PLAYFUL = 'playful',
  EMOTIONAL = 'emotional',
  AMBIENT = 'ambient'
}

/**
 * Video script request
 */
export interface VideoScriptRequest {
  videoType: VideoType;
  purpose: VideoPurpose;
  targetAudience: VideoTargetAudience;
  platform: VideoPlatform;
  additionalInfo?: string;
  productName?: string;
  duration?: number; // in seconds
}

/**
 * Music recommendation
 */
export interface MusicRecommendation {
  type: MusicType;
  description: string;
  startTime?: string; // e.g., "0:00"
  endTime?: string; // e.g., "0:30"
  mood?: string;
  tempo?: string; // e.g., "fast", "medium", "slow"
  volume?: string; // e.g., "high", "medium", "low"
}

/**
 * Video script shot recommendation
 */
export interface ShotRecommendation {
  shotType: ShotType;
  description: string;
  duration?: string; // e.g., "5-10 seconds"
  cameraMovement?: string;
  angle?: string;
  lighting?: string;
}

/**
 * Video script location recommendation
 */
export interface LocationRecommendation {
  name: string;
  description: string;
  lighting?: string; // e.g., "natural light", "studio lighting"
  timeOfDay?: string; // e.g., "morning", "afternoon", "evening"
  weatherCondition?: string; // e.g., "sunny", "cloudy", "rainy"
}

/**
 * Video script visual object recommendation
 */
export interface VisualObjectRecommendation {
  name: string;
  purpose: string;
  placement?: string; // e.g., "foreground", "background"
  interaction?: string; // how to interact with the object
}

/**
 * Video script dialogue/narration
 */
export interface ScriptDialogue {
  speaker: string; // e.g., "Narrator", "Presenter", "Customer"
  text: string;
  tone?: string; // e.g., "enthusiastic", "serious", "conversational"
  notes?: string; // additional notes for delivery
}

/**
 * Video script visual element
 */
export interface ScriptVisual {
  description: string;
  duration?: string;
  transition?: string; // e.g., "cut", "fade", "dissolve"
  effects?: string; // e.g., "slow motion", "color grading"
}

/**
 * Timeline segment
 */
export interface TimelineSegment {
  startTime: string; // e.g., "0:00"
  endTime: string; // e.g., "0:15"
  shotType: ShotType;
  dialogue?: ScriptDialogue;
  visual: ScriptVisual;
  music?: MusicRecommendation;
  location?: string;
  visualObjects?: string[];
  notes?: string;
}

/**
 * Video script section
 */
export interface VideoScriptSection {
  name: string; // e.g., "Opening", "Problem", "Solution", "CTA"
  content: string;
  duration?: string; // e.g., "10-15 seconds"
  timeline?: TimelineSegment[]; // Detailed timeline for this section
}

/**
 * Video script response
 */
export interface VideoScriptResponse {
  title: string;
  sections: VideoScriptSection[];
  shotRecommendations: ShotRecommendation[];
  locationRecommendations: LocationRecommendation[];
  visualObjectRecommendations: VisualObjectRecommendation[];
  musicRecommendations?: MusicRecommendation[];
  timeline?: TimelineSegment[]; // Overall detailed timeline
  totalDuration?: string;
  additionalNotes?: string;
}

/**
 * Saved video script
 */
export interface SavedVideoScript extends VideoScriptResponse {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  videoType: VideoType;
  purpose: VideoPurpose;
  targetAudience: VideoTargetAudience;
  platform: VideoPlatform;
  productName?: string;
  durationInSeconds?: number;
  tags?: string[];
  status?: 'draft' | 'final';
  relatedContentId?: string; // ID of related content in Monthly Content Plan
}
