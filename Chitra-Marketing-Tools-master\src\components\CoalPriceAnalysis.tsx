import React, { useState, useEffect } from 'react';
import { DollarSign, TrendingUp, TrendingDown, AlertTriangle, BarChart, Loader } from 'lucide-react';
import {
  analyzeCoalPriceTrend,
  CoalPriceTrend,
  CoalPrice,
  CoalPriceForecast,
  loadESDMDataToLocalStorage
} from '../services/coalPriceService';

// Interface for coal price data with month name
interface CoalPriceData {
  month: string;
  price: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
}

// Convert date string to month name
const getMonthName = (dateStr: string): string => {
  const date = new Date(dateStr);
  const monthNames = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ];
  return `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
};

// Convert CoalPrice array to CoalPriceData array
const convertToCoalPriceData = (prices: CoalPrice[]): CoalPriceData[] => {
  if (prices.length < 2) {
    return [];
  }

  const result: CoalPriceData[] = [];

  // Sort by date (newest first)
  const sortedPrices = [...prices].sort((a, b) =>
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  for (let i = 0; i < sortedPrices.length; i++) {
    const current = sortedPrices[i];
    const previous = i < sortedPrices.length - 1 ? sortedPrices[i + 1] : null;

    let change = 0;
    let trend: 'up' | 'down' | 'stable' = 'stable';

    if (previous) {
      change = ((current.price - previous.price) / previous.price) * 100;
      if (change > 1) {
        trend = 'up';
      } else if (change < -1) {
        trend = 'down';
      }
    }

    result.push({
      month: getMonthName(current.date),
      price: current.price,
      change,
      trend
    });
  }

  return result;
};

// Calculate the impact on mining operations
const calculateMiningImpact = (price: number, trend: 'up' | 'down' | 'stable'): string => {
  if (price > 115) {
    return 'Sangat positif - Aktivitas pertambangan tinggi, permintaan ban meningkat';
  } else if (price > 110) {
    return 'Positif - Aktivitas pertambangan baik, permintaan ban stabil';
  } else if (price > 105) {
    return 'Netral - Aktivitas pertambangan normal';
  } else if (price > 100) {
    return 'Negatif - Aktivitas pertambangan menurun, permintaan ban berkurang';
  } else {
    return 'Sangat negatif - Aktivitas pertambangan rendah, permintaan ban sangat berkurang';
  }
};

// Get the trend icon
const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
  switch (trend) {
    case 'up':
      return <TrendingUp className="h-5 w-5 text-green-600" />;
    case 'down':
      return <TrendingDown className="h-5 w-5 text-red-600" />;
    default:
      return null;
  }
};

// Get the trend color
const getTrendColor = (trend: 'up' | 'down' | 'stable'): string => {
  switch (trend) {
    case 'up':
      return 'text-green-600';
    case 'down':
      return 'text-red-600';
    default:
      return 'text-gray-600';
  }
};

// Get the price impact color
const getPriceImpactColor = (price: number): string => {
  if (price > 115) {
    return 'text-green-600 bg-green-50 border-green-200';
  } else if (price > 110) {
    return 'text-green-600 bg-green-50 border-green-200';
  } else if (price > 105) {
    return 'text-yellow-600 bg-yellow-50 border-yellow-200';
  } else if (price > 100) {
    return 'text-red-600 bg-red-50 border-red-200';
  } else {
    return 'text-red-600 bg-red-50 border-red-200';
  }
};

interface CoalPriceAnalysisProps {
  csvData?: string;
}

const CoalPriceAnalysis: React.FC<CoalPriceAnalysisProps> = ({ csvData }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [priceTrend, setPriceTrend] = useState<CoalPriceTrend | null>(null);
  const [priceData, setPriceData] = useState<CoalPriceData[]>([]);

  // Load ESDM data and analyze coal price trend
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load ESDM CSV data to localStorage if provided
        if (csvData) {
          await loadESDMDataToLocalStorage(csvData);
        }

        // Analyze coal price trend
        const trend = await analyzeCoalPriceTrend();
        setPriceTrend(trend);

        // Convert to price data format for display
        const data = convertToCoalPriceData([
          { date: new Date().toISOString().split('T')[0], price: trend.currentPrice, currency: 'USD' },
          { date: new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0], price: trend.previousPrice, currency: 'USD' }
        ]);

        setPriceData(data);
      } catch (err) {
        console.error('Error loading coal price data:', err);
        setError('Gagal memuat data harga batu bara. Silakan coba lagi.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [csvData]);

  // Show loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Loader className="h-8 w-8 animate-spin text-blue-600 mb-4" />
        <p className="text-gray-700">Memuat data harga batu bara...</p>
      </div>
    );
  }

  // Show error state
  if (error || !priceTrend) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-start">
          <AlertTriangle className="h-5 w-5 text-red-600 mr-2 mt-0.5" />
          <div>
            <h4 className="font-medium text-red-800 mb-1">Error:</h4>
            <p className="text-sm text-red-700">
              {error || 'Gagal memuat data harga batu bara. Silakan coba lagi.'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Get current month data
  const currentMonthData: CoalPriceData = {
    month: getMonthName(new Date().toISOString().split('T')[0]),
    price: priceTrend.currentPrice,
    change: priceTrend.percentChange,
    trend: priceTrend.trend
  };

  // Get previous month data
  const previousMonthData: CoalPriceData = {
    month: getMonthName(new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().split('T')[0]),
    price: priceTrend.previousPrice,
    change: 0,
    trend: 'stable'
  };

  // Get forecast data
  const forecastData: CoalPriceData[] = priceTrend.forecasts ?
    priceTrend.forecasts.map(forecast => ({
      month: forecast.month,
      price: forecast.price,
      change: forecast.change,
      trend: forecast.trend
    })) : [];

  return (
    <div className="space-y-6">
      {/* Current price summary */}
      <div className={`p-4 rounded-lg border ${getPriceImpactColor(currentMonthData.price)}`}>
        <h3 className="text-lg font-bold mb-2 flex items-center">
          <DollarSign className="h-5 w-5 mr-2" />
          <span>Harga Batu Bara Saat Ini: {currentMonthData.month}</span>
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium mb-1">Harga Acuan:</h4>
            <div className="flex items-center mb-2">
              <span className="text-2xl font-bold">${currentMonthData.price.toFixed(2)}</span>
              <span className="ml-2 text-sm">USD/ton</span>
              <div className={`ml-3 flex items-center ${getTrendColor(currentMonthData.trend)}`}>
                {getTrendIcon(currentMonthData.trend)}
                <span className="ml-1 font-medium">
                  {currentMonthData.change > 0 ? '+' : ''}{currentMonthData.change.toFixed(1)}%
                </span>
              </div>
            </div>
            <div className="text-sm">
              <span>Bulan lalu: </span>
              <span className="font-medium">${previousMonthData.price.toFixed(2)} USD/ton</span>
            </div>
          </div>

          <div>
            <h4 className="font-medium mb-1">Dampak:</h4>
            <div className="space-y-1">
              <div className="text-sm">
                <span className="font-medium">Operasi Pertambangan:</span>{' '}
                <span>{calculateMiningImpact(currentMonthData.price, currentMonthData.trend)}</span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Tren:</span>{' '}
                <span className={getTrendColor(currentMonthData.trend)}>
                  {currentMonthData.trend === 'up' ? 'Naik' : currentMonthData.trend === 'down' ? 'Turun' : 'Stabil'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Price forecast */}
      <div>
        <h3 className="text-lg font-bold mb-3 flex items-center">
          <BarChart className="h-5 w-5 mr-2 text-blue-600" />
          <span>Prakiraan Harga 3 Bulan Ke Depan (AI Forecast)</span>
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {forecastData.length > 0 ? (
            forecastData.map((data, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border ${getPriceImpactColor(data.price)}`}
              >
                <h4 className="font-medium mb-2">{data.month}</h4>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center">
                    <span className="text-xl font-bold">${data.price.toFixed(2)}</span>
                    <span className="ml-2">USD/ton</span>
                  </div>
                  <div className={`flex items-center ${getTrendColor(data.trend)}`}>
                    {getTrendIcon(data.trend)}
                    <span className="ml-1">
                      {data.change > 0 ? '+' : ''}{data.change.toFixed(1)}%
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Dampak:</span>{' '}
                    <span>{calculateMiningImpact(data.price, data.trend).split(' - ')[0]}</span>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-3 p-4 bg-gray-50 border border-gray-200 rounded-lg text-center">
              <p className="text-gray-700">Tidak ada data prakiraan yang tersedia.</p>
            </div>
          )}
        </div>
      </div>

      {/* Monthly price table */}
      <div>
        <h3 className="text-lg font-bold mb-3">Data Harga Batu Bara dari ESDM</h3>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bulan
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Harga (USD/ton)
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Perubahan
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tren
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Dampak
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {priceData.length > 0 ? (
                priceData.map((data, index) => (
                  <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                      {data.month}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      ${data.price.toFixed(2)}
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm">
                      <span className={data.change > 0 ? 'text-green-600' : data.change < 0 ? 'text-red-600' : 'text-gray-500'}>
                        {data.change > 0 ? '+' : ''}{data.change.toFixed(1)}%
                      </span>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm">
                      <div className="flex items-center">
                        {getTrendIcon(data.trend)}
                        <span className={`ml-1 ${getTrendColor(data.trend)}`}>
                          {data.trend === 'up' ? 'Naik' : data.trend === 'down' ? 'Turun' : 'Stabil'}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                      {calculateMiningImpact(data.price, data.trend).split(' - ')[0]}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-4 py-4 text-center text-sm text-gray-500">
                    Tidak ada data harga yang tersedia.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start">
          <AlertTriangle className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-800 mb-1">Catatan Penting:</h4>
            <p className="text-sm text-blue-700">
              Data harga batu bara di atas diambil dari file CSV ESDM yang diupload.
              Prakiraan harga 3 bulan ke depan dihasilkan oleh AI berdasarkan data historis.
              Harga batu bara memiliki korelasi langsung dengan aktivitas pertambangan dan permintaan ban.
              Harga yang lebih tinggi umumnya mendorong peningkatan aktivitas pertambangan, yang meningkatkan permintaan ban.
              Sumber data resmi: Kementerian ESDM (https://www.minerba.esdm.go.id/harga_acuan).
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CoalPriceAnalysis;
