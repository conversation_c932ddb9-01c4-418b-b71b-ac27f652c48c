import React, { useState, useEffect, useRef } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../components/ui/table';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogFooter,
  DialogTrigger
} from '../components/ui/dialog';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../components/ui/alert-dialog';
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '../components/ui/form';
import { useForm } from 'react-hook-form';
import { 
  Plus, 
  Pencil, 
  Trash2, 
  Upload, 
  Download, 
  Search, 
  Calendar, 
  DollarSign,
  ArrowUpDown,
  Info
} from 'lucide-react';
import Papa from 'papaparse';
import * as XLSX from 'xlsx';
import { CoalPrice } from '../services/coalPriceService';

interface CoalPriceManagementProps {
  onDataChange?: (data: CoalPrice[]) => void;
}

const CoalPriceManagement: React.FC<CoalPriceManagementProps> = ({ onDataChange }) => {
  // State for coal price data
  const [coalPrices, setCoalPrices] = useState<CoalPrice[]>([]);
  const [filteredPrices, setFilteredPrices] = useState<CoalPrice[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortConfig, setSortConfig] = useState<{ key: keyof CoalPrice; direction: 'asc' | 'desc' }>({ 
    key: 'date', 
    direction: 'desc' 
  });
  
  // State for dialogs
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedPrice, setSelectedPrice] = useState<CoalPrice | null>(null);
  
  // File input ref
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Form for adding/editing coal prices
  const form = useForm<CoalPrice>({
    defaultValues: {
      date: new Date().toISOString().split('T')[0],
      price: 0,
      currency: 'USD'
    }
  });
  
  // Load coal prices from localStorage on component mount
  useEffect(() => {
    const loadCoalPrices = () => {
      const storedPrices = localStorage.getItem('coalPrices');
      if (storedPrices) {
        const prices = JSON.parse(storedPrices) as CoalPrice[];
        setCoalPrices(prices);
        setFilteredPrices(prices);
      }
    };
    
    loadCoalPrices();
  }, []);
  
  // Filter and sort coal prices when search query or sort config changes
  useEffect(() => {
    let filtered = [...coalPrices];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(price => 
        price.date.toLowerCase().includes(query) || 
        price.price.toString().includes(query) ||
        price.currency.toLowerCase().includes(query)
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      if (sortConfig.key === 'date') {
        return sortConfig.direction === 'asc' 
          ? new Date(a.date).getTime() - new Date(b.date).getTime()
          : new Date(b.date).getTime() - new Date(a.date).getTime();
      } else if (sortConfig.key === 'price') {
        return sortConfig.direction === 'asc' 
          ? a.price - b.price
          : b.price - a.price;
      } else {
        return sortConfig.direction === 'asc'
          ? a[sortConfig.key].localeCompare(b[sortConfig.key])
          : b[sortConfig.key].localeCompare(a[sortConfig.key]);
      }
    });
    
    setFilteredPrices(filtered);
    
    // Notify parent component of data change
    if (onDataChange) {
      onDataChange(coalPrices);
    }
  }, [coalPrices, searchQuery, sortConfig, onDataChange]);
  
  // Save coal prices to localStorage
  const saveCoalPrices = (prices: CoalPrice[]) => {
    localStorage.setItem('coalPrices', JSON.stringify(prices));
    localStorage.setItem('scrapedESDMData', JSON.stringify(prices));
    setCoalPrices(prices);
  };
  
  // Handle sort
  const handleSort = (key: keyof CoalPrice) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };
  
  // Handle add coal price
  const handleAddPrice = (data: CoalPrice) => {
    // Format date to YYYY-MM-DD
    const formattedDate = new Date(data.date).toISOString().split('T')[0];
    
    // Check if date already exists
    const existingIndex = coalPrices.findIndex(price => price.date === formattedDate);
    
    if (existingIndex >= 0) {
      // Update existing price
      const updatedPrices = [...coalPrices];
      updatedPrices[existingIndex] = { ...data, date: formattedDate };
      saveCoalPrices(updatedPrices);
    } else {
      // Add new price
      const newPrices = [...coalPrices, { ...data, date: formattedDate }];
      saveCoalPrices(newPrices);
    }
    
    setAddDialogOpen(false);
    form.reset({
      date: new Date().toISOString().split('T')[0],
      price: 0,
      currency: 'USD'
    });
  };
  
  // Handle edit coal price
  const handleEditPrice = (data: CoalPrice) => {
    if (!selectedPrice) return;
    
    // Format date to YYYY-MM-DD
    const formattedDate = new Date(data.date).toISOString().split('T')[0];
    
    // Update price
    const updatedPrices = coalPrices.map(price => 
      price.date === selectedPrice.date ? { ...data, date: formattedDate } : price
    );
    
    saveCoalPrices(updatedPrices);
    setEditDialogOpen(false);
    setSelectedPrice(null);
  };
  
  // Handle delete coal price
  const handleDeletePrice = () => {
    if (!selectedPrice) return;
    
    // Delete price
    const updatedPrices = coalPrices.filter(price => price.date !== selectedPrice.date);
    saveCoalPrices(updatedPrices);
    setDeleteDialogOpen(false);
    setSelectedPrice(null);
  };
  
  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    
    if (fileExtension === 'csv') {
      // Parse CSV
      Papa.parse(file, {
        header: true,
        complete: (results) => {
          const parsedData = results.data as any[];
          const validData = parsedData
            .filter(row => row.date && row.price)
            .map(row => ({
              date: new Date(row.date).toISOString().split('T')[0],
              price: parseFloat(row.price),
              currency: row.currency || 'USD'
            }));
          
          if (validData.length > 0) {
            saveCoalPrices(validData);
          }
        },
        error: (error) => {
          console.error('Error parsing CSV:', error);
          alert('Error parsing CSV file. Please check the format.');
        }
      });
    } else if (fileExtension === 'xlsx' || fileExtension === 'xls') {
      // Parse Excel
      const reader = new FileReader();
      reader.onload = (e) => {
        const data = e.target?.result;
        if (!data) return;
        
        try {
          const workbook = XLSX.read(data, { type: 'binary' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const parsedData = XLSX.utils.sheet_to_json(worksheet) as any[];
          
          const validData = parsedData
            .filter(row => row.date && row.price)
            .map(row => ({
              date: new Date(row.date).toISOString().split('T')[0],
              price: parseFloat(row.price),
              currency: row.currency || 'USD'
            }));
          
          if (validData.length > 0) {
            saveCoalPrices(validData);
          }
        } catch (error) {
          console.error('Error parsing Excel:', error);
          alert('Error parsing Excel file. Please check the format.');
        }
      };
      reader.readAsBinaryString(file);
    } else {
      alert('Unsupported file format. Please upload a CSV or Excel file.');
    }
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  // Handle export to CSV
  const handleExportCSV = () => {
    const csv = Papa.unparse(coalPrices);
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'coal_prices.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Handle export to Excel
  const handleExportExcel = () => {
    const worksheet = XLSX.utils.json_to_sheet(coalPrices);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Coal Prices');
    XLSX.writeFile(workbook, 'coal_prices.xlsx');
  };
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Manajemen Data Harga Batu Bara</h2>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => fileInputRef.current?.click()}>
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileUpload}
            accept=".csv,.xlsx,.xls"
            className="hidden"
          />
          <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Data
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Tambah Data Harga Batu Bara</DialogTitle>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(handleAddPrice)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tanggal</FormLabel>
                        <FormControl>
                          <div className="flex">
                            <Calendar className="h-4 w-4 mr-2 mt-3" />
                            <Input type="date" {...field} />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Harga</FormLabel>
                        <FormControl>
                          <div className="flex">
                            <DollarSign className="h-4 w-4 mr-2 mt-3" />
                            <Input type="number" step="0.01" {...field} />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Mata Uang</FormLabel>
                        <FormControl>
                          <Input {...field} defaultValue="USD" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit">Simpan</Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      <div className="flex justify-between items-center">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Cari data..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExportCSV}>
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button variant="outline" onClick={handleExportExcel}>
            <Download className="h-4 w-4 mr-2" />
            Export Excel
          </Button>
        </div>
      </div>
      
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px] cursor-pointer" onClick={() => handleSort('date')}>
                <div className="flex items-center">
                  Tanggal
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort('price')}>
                <div className="flex items-center">
                  Harga
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead className="cursor-pointer" onClick={() => handleSort('currency')}>
                <div className="flex items-center">
                  Mata Uang
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead className="text-right">Aksi</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPrices.length > 0 ? (
              filteredPrices.map((price) => (
                <TableRow key={price.date}>
                  <TableCell className="font-medium">
                    {new Date(price.date).toLocaleDateString('id-ID', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </TableCell>
                  <TableCell>{price.price.toFixed(2)}</TableCell>
                  <TableCell>{price.currency}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Dialog open={editDialogOpen && selectedPrice?.date === price.date} onOpenChange={(open) => {
                        setEditDialogOpen(open);
                        if (!open) setSelectedPrice(null);
                      }}>
                        <DialogTrigger asChild>
                          <Button variant="ghost" size="icon" onClick={() => {
                            setSelectedPrice(price);
                            form.reset({
                              date: price.date,
                              price: price.price,
                              currency: price.currency
                            });
                          }}>
                            <Pencil className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Edit Data Harga Batu Bara</DialogTitle>
                          </DialogHeader>
                          <Form {...form}>
                            <form onSubmit={form.handleSubmit(handleEditPrice)} className="space-y-4">
                              <FormField
                                control={form.control}
                                name="date"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Tanggal</FormLabel>
                                    <FormControl>
                                      <div className="flex">
                                        <Calendar className="h-4 w-4 mr-2 mt-3" />
                                        <Input type="date" {...field} />
                                      </div>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name="price"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Harga</FormLabel>
                                    <FormControl>
                                      <div className="flex">
                                        <DollarSign className="h-4 w-4 mr-2 mt-3" />
                                        <Input type="number" step="0.01" {...field} />
                                      </div>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name="currency"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Mata Uang</FormLabel>
                                    <FormControl>
                                      <Input {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <DialogFooter>
                                <Button type="submit">Simpan</Button>
                              </DialogFooter>
                            </form>
                          </Form>
                        </DialogContent>
                      </Dialog>
                      <AlertDialog open={deleteDialogOpen && selectedPrice?.date === price.date} onOpenChange={(open) => {
                        setDeleteDialogOpen(open);
                        if (!open) setSelectedPrice(null);
                      }}>
                        <Button variant="ghost" size="icon" onClick={() => {
                          setSelectedPrice(price);
                          setDeleteDialogOpen(true);
                        }}>
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Hapus Data Harga Batu Bara</AlertDialogTitle>
                            <AlertDialogDescription>
                              Apakah Anda yakin ingin menghapus data harga batu bara untuk tanggal{' '}
                              {selectedPrice && new Date(selectedPrice.date).toLocaleDateString('id-ID', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                              })}?
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Batal</AlertDialogCancel>
                            <AlertDialogAction onClick={handleDeletePrice} className="bg-red-500 hover:bg-red-600">
                              Hapus
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-4">
                  Tidak ada data harga batu bara. Silakan tambahkan data baru atau import dari file.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 flex items-start">
        <Info className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
        <div>
          <h3 className="font-medium text-blue-800">Format File Import</h3>
          <p className="text-sm text-blue-700 mt-1">
            File CSV atau Excel harus memiliki kolom berikut: <code>date</code> (format: YYYY-MM-DD), <code>price</code> (angka), dan <code>currency</code> (opsional, default: USD).
          </p>
        </div>
      </div>
    </div>
  );
};

export default CoalPriceManagement;
