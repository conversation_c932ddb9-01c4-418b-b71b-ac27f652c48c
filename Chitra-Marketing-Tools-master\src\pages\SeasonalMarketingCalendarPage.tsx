import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Search,
  Filter,
  Sparkles,
  Settings,
  Plus,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  Brain,
  Zap,
  AlertCircle,
  Info,
  CloudRain,
  DollarSign,
  List,
  MessageSquare
} from 'lucide-react';
import MarketingCalendar from '../components/MarketingCalendar';
import SeasonalInsightCard from '../components/SeasonalInsightCard';
import PromoScheduler from '../components/PromoScheduler';
import SeasonalFactorsManager from '../components/SeasonalFactorsManager';
import MonthlyRainfallData from '../components/MonthlyRainfallData';
import CoalPriceAnalysis from '../components/CoalPriceAnalysis';
import CalendarContentGenerator from '../components/CalendarContentGenerator';
import { Button } from '../components/ui/button';
import {
  CalendarDay,
  ScheduledPromotion,
  SeasonalInsight,
  SeasonalAnalysisRequest,
  SeasonalAnalysisResponse,
  RecommendationLevel
} from '../types/seasonalMarketing';
import {
  getSeasonalFactors,
  getScheduledPromotions,
  saveScheduledPromotion,
  getCalendarSettings,
  saveCalendarSettings
} from '../services/seasonalMarketingService';
import {
  getSeasonalInsights,
  saveSeasonalInsight,
  analyzeSeasonalData
} from '../services/seasonalAnalysisService';

export default function SeasonalMarketingCalendarPage() {
  // State for calendar and insights
  const [selectedDay, setSelectedDay] = useState<CalendarDay | null>(null);
  const [insights, setInsights] = useState<SeasonalInsight[]>([]);
  const [filteredInsights, setFilteredInsights] = useState<SeasonalInsight[]>([]);
  const [promotions, setPromotions] = useState<ScheduledPromotion[]>([]);
  const [selectedPromotion, setSelectedPromotion] = useState<ScheduledPromotion | null>(null);
  const [selectedInsight, setSelectedInsight] = useState<SeasonalInsight | undefined>(undefined);

  // State for UI controls
  const [showScheduler, setShowScheduler] = useState(false);
  const [schedulerDate, setSchedulerDate] = useState<string>('');
  const [schedulerInsight, setSchedulerInsight] = useState<SeasonalInsight | undefined>(undefined);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [showInsightsPanel, setShowInsightsPanel] = useState(true);

  // State for tabs
  const [activeTab, setActiveTab] = useState<'calendar' | 'factors' | 'weather' | 'coal' | 'content'>('calendar');

  // State for ESDM CSV data
  const [esdmCsvData, setEsdmCsvData] = useState<string | null>(null);

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      // Load insights
      const allInsights = getSeasonalInsights();
      setInsights(allInsights);
      setFilteredInsights(allInsights);

      // Load promotions
      const allPromotions = getScheduledPromotions();
      setPromotions(allPromotions);

      // Load ESDM CSV data
      try {
        const response = await fetch('Data ESDM mei.csv');
        const data = await response.text();
        setEsdmCsvData(data);
      } catch (error) {
        console.error('Error loading ESDM CSV data:', error);
      }
    };

    loadData();
  }, []);

  // Filter insights when search query changes
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredInsights(insights);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = insights.filter(insight =>
      insight.title.toLowerCase().includes(query) ||
      insight.description.toLowerCase().includes(query) ||
      insight.recommendedPromoTypes.some(type => type.toLowerCase().includes(query)) ||
      insight.recommendedProducts.some(product => product.toLowerCase().includes(query))
    );

    setFilteredInsights(filtered);
  }, [searchQuery, insights]);

  // Handle day click on calendar
  const handleDayClick = (day: CalendarDay) => {
    setSelectedDay(day);

    // Filter insights for this day
    const dayInsights = insights.filter(insight => {
      const insightStart = new Date(insight.startDate);
      const insightEnd = new Date(insight.endDate);
      const selectedDate = new Date(day.date);

      return selectedDate >= insightStart && selectedDate <= insightEnd;
    });

    setFilteredInsights(dayInsights);
  };

  // Handle promotion click on calendar
  const handlePromotionClick = (promotion: ScheduledPromotion) => {
    setSelectedPromotion(promotion);
  };

  // Handle add promotion button click
  const handleAddPromotion = (date: string) => {
    setSchedulerDate(date);
    setSchedulerInsight(undefined);
    setShowScheduler(true);
  };

  // Handle schedule from insight
  const handleScheduleFromInsight = (insight: SeasonalInsight) => {
    setSchedulerInsight(insight);
    setSchedulerDate(insight.startDate);
    setShowScheduler(true);
  };

  // Handle save promotion
  const handleSavePromotion = (promotion: ScheduledPromotion) => {
    // Update promotions list
    setPromotions(prev => {
      const index = prev.findIndex(p => p.id === promotion.id);
      if (index >= 0) {
        // Update existing promotion
        const updated = [...prev];
        updated[index] = promotion;
        return updated;
      } else {
        // Add new promotion
        return [...prev, promotion];
      }
    });
  };

  // Handle AI analysis
  const handleAnalyzeData = async () => {
    setIsAnalyzing(true);
    setAnalysisError(null);

    try {
      // Prepare analysis request
      const request: SeasonalAnalysisRequest = {
        timeframe: {
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date(new Date().setMonth(new Date().getMonth() + 6)).toISOString().split('T')[0]
        },
        productCategories: ['Earthmover', 'OTR', 'Truck'],
        includeWeatherData: true,
        includeCoalPriceData: true
      };

      // Call analysis service
      const response = await analyzeSeasonalData(request);

      // Update insights
      setInsights(getSeasonalInsights());
      setFilteredInsights(getSeasonalInsights());

      // Show success message
      alert('Analisis berhasil! ' + response.insights.length + ' insight baru telah ditambahkan.');
    } catch (error) {
      console.error('Error analyzing data:', error);
      setAnalysisError('Gagal melakukan analisis. Silakan coba lagi.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Reset filters
  const handleResetFilters = () => {
    setSearchQuery('');
    setSelectedDay(null);
    setSelectedInsight(undefined);
    setFilteredInsights(insights);
  };

  // Toggle insights panel
  const toggleInsightsPanel = () => {
    setShowInsightsPanel(!showInsightsPanel);
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Calendar className="h-6 w-6 text-blue-600" />
          <h1 className="text-2xl font-bold text-gray-900">
            Kalender Pemasaran Musiman
          </h1>
        </div>

        <div className="flex items-center space-x-2">
          <button
            className="px-3 py-1.5 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 flex items-center"
            onClick={handleResetFilters}
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            <span>Reset Filter</span>
          </button>

          <button
            className="px-3 py-1.5 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors flex items-center"
            onClick={handleAnalyzeData}
            disabled={isAnalyzing}
          >
            {isAnalyzing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                <span>Menganalisis...</span>
              </>
            ) : (
              <>
                <Brain className="h-4 w-4 mr-1" />
                <span>Analisis AI</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Analysis error */}
      {analysisError && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-center text-red-800">
          <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
          <span>{analysisError}</span>
        </div>
      )}

      {/* Tab navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex -mb-px">
          <button
            className={`py-2 px-4 border-b-2 font-medium text-sm ${
              activeTab === 'calendar'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('calendar')}
          >
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              <span>Kalender</span>
            </div>
          </button>

          <button
            className={`py-2 px-4 border-b-2 font-medium text-sm ${
              activeTab === 'factors'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('factors')}
          >
            <div className="flex items-center">
              <List className="h-4 w-4 mr-2" />
              <span>Faktor Musiman</span>
            </div>
          </button>

          <button
            className={`py-2 px-4 border-b-2 font-medium text-sm ${
              activeTab === 'weather'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('weather')}
          >
            <div className="flex items-center">
              <CloudRain className="h-4 w-4 mr-2" />
              <span>Cuaca</span>
            </div>
          </button>

          <button
            className={`py-2 px-4 border-b-2 font-medium text-sm ${
              activeTab === 'coal'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('coal')}
          >
            <div className="flex items-center">
              <DollarSign className="h-4 w-4 mr-2" />
              <span>Harga Batu Bara</span>
            </div>
          </button>

          <button
            className={`py-2 px-4 border-b-2 font-medium text-sm ${
              activeTab === 'content'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('content')}
          >
            <div className="flex items-center">
              <MessageSquare className="h-4 w-4 mr-2" />
              <span>Generator Konten</span>
            </div>
          </button>
        </nav>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main content area (2/3 width on large screens) */}
        <div className="lg:col-span-2">
          {activeTab === 'calendar' && (
            <MarketingCalendar
              onDayClick={handleDayClick}
              onPromotionClick={handlePromotionClick}
              onAddPromotion={handleAddPromotion}
            />
          )}

          {activeTab === 'factors' && (
            <SeasonalFactorsManager
              onFactorsChange={() => {
                // Refresh calendar data when factors change
                setSelectedDay(null);
                handleResetFilters();
              }}
            />
          )}

          {activeTab === 'weather' && (
            <div className="bg-white rounded-lg border shadow-sm p-4">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <CloudRain className="h-5 w-5 mr-2 text-blue-600" />
                <span>Analisis Cuaca dan Dampak pada Operasi Pertambangan</span>
              </h2>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg mb-4">
                <p className="text-gray-700">
                  Cuaca memiliki dampak signifikan pada operasi pertambangan dan permintaan ban. Musim hujan di Indonesia (November-Maret) mengurangi aktivitas pertambangan karena kondisi jalan yang buruk dan keselamatan pekerja.
                </p>
                <p className="text-gray-700 mt-2">
                  Analisis ini menampilkan data curah hujan bulanan rata-rata di wilayah pertambangan utama di Kalimantan dan Sumatra, serta dampaknya pada operasi pertambangan dan permintaan ban.
                </p>
              </div>

              <MonthlyRainfallData />
            </div>
          )}

          {activeTab === 'coal' && (
            <div className="bg-white rounded-lg border shadow-sm p-4">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <DollarSign className="h-5 w-5 mr-2 text-blue-600" />
                <span>Analisis Harga Batu Bara dan Dampak pada Permintaan Ban</span>
              </h2>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg mb-4">
                <p className="text-gray-700">
                  Harga batu bara memiliki korelasi langsung dengan aktivitas pertambangan dan permintaan ban. Harga yang lebih tinggi umumnya mendorong peningkatan aktivitas pertambangan, yang meningkatkan permintaan ban.
                </p>
                <p className="text-gray-700 mt-2">
                  Analisis ini menampilkan data harga batu bara bulanan dan dampaknya pada operasi pertambangan dan permintaan ban. Sumber data: Kementerian ESDM (https://www.minerba.esdm.go.id/harga_acuan).
                </p>
              </div>

              <div className="space-y-6">
                <CoalPriceAnalysis csvData={esdmCsvData || undefined} />

                <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h3 className="text-lg font-bold mb-2 flex items-center">
                    <Info className="h-5 w-5 mr-2 text-blue-600" />
                    <span>Integrasi dengan Analisis Musiman</span>
                  </h3>
                  <p className="text-sm text-blue-700 mb-4">
                    Data harga batu bara yang dikelola di sini akan digunakan dalam analisis musiman untuk menentukan waktu optimal kampanye pemasaran.
                    Harga batu bara yang lebih tinggi umumnya menunjukkan aktivitas pertambangan yang lebih tinggi, yang meningkatkan permintaan ban.
                  </p>
                  <div className="flex space-x-3">
                    <Button
                      onClick={() => setActiveTab('calendar')}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      Kembali ke Kalender
                    </Button>
                    <Button
                      onClick={() => window.location.href = '#/coal-price-data'}
                      variant="outline"
                      className="border-blue-600 text-blue-600 hover:bg-blue-50"
                    >
                      Kelola Data Harga Batu Bara
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'content' && (
            <div className="bg-white rounded-lg border shadow-sm p-4">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <MessageSquare className="h-5 w-5 mr-2 text-blue-600" />
                <span>Generator Konten dari Kalender</span>
              </h2>

              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg mb-4">
                <p className="text-gray-700">
                  Generator konten ini menggunakan data dari kalender pemasaran musiman untuk membuat konten yang relevan dengan kondisi pasar saat ini.
                </p>
                <p className="text-gray-700 mt-2">
                  Pilih tanggal di kalender atau gunakan insight yang tersedia untuk menghasilkan konten yang sesuai dengan kondisi musiman.
                </p>
              </div>

              {selectedDay || selectedInsight ? (
                <CalendarContentGenerator
                  selectedDay={selectedDay}
                  selectedInsight={selectedInsight || (filteredInsights.length > 0 ? filteredInsights[0] : undefined)}
                  onContentSaved={() => {
                    // Refresh calendar data when content is saved
                    alert('Konten berhasil disimpan ke kalender!');
                  }}
                />
              ) : (
                <div className="p-8 text-center bg-gray-50 border border-gray-200 rounded-lg">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-600 font-medium">Pilih tanggal di kalender terlebih dahulu</p>
                  <p className="text-gray-500 text-sm mt-2">Klik pada tanggal di kalender untuk menghasilkan konten yang relevan dengan kondisi pada tanggal tersebut.</p>
                  <Button
                    onClick={() => setActiveTab('calendar')}
                    className="mt-4 bg-blue-600 hover:bg-blue-700"
                  >
                    Kembali ke Kalender
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Selected day info */}
          {selectedDay && (
            <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    {new Date(selectedDay.date).toLocaleDateString('id-ID', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' })}
                  </h3>
                  <p className="text-sm text-gray-600">
                    Skor Rekomendasi: {selectedDay.score.toFixed(1)}/100 ({selectedDay.recommendationLevel})
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors flex items-center"
                    onClick={() => handleAddPromotion(selectedDay.date)}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    <span>Tambah Promo</span>
                  </button>
                  <button
                    className="px-3 py-1 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors flex items-center"
                    onClick={() => setActiveTab('content')}
                  >
                    <MessageSquare className="h-4 w-4 mr-1" />
                    <span>Buat Konten</span>
                  </button>
                </div>
              </div>

              {/* Holidays */}
              {selectedDay.holidays && selectedDay.holidays.length > 0 && (
                <div className="mt-3">
                  <h4 className="text-sm font-medium text-red-600">Hari Libur:</h4>
                  <ul className="mt-1 space-y-1">
                    {selectedDay.holidays.map(holiday => (
                      <li key={holiday.id} className="text-sm flex items-center">
                        <span className={`inline-block text-center ${
                          holiday.type === 'Nasional' ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {holiday.type === 'Nasional' ? '🇮🇩' : '🏮'}
                        </span>
                        <span className="ml-2">{holiday.name}</span>
                        <span className="ml-2 text-xs text-gray-500">({holiday.type})</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Factors */}
              {selectedDay.factors.length > 0 && (
                <div className="mt-3">
                  <h4 className="text-sm font-medium text-gray-900">Faktor yang Mempengaruhi:</h4>
                  <ul className="mt-1 space-y-1">
                    {selectedDay.factors
                      .filter(factor => !factor.isHoliday) // Filter out holiday factors as they're shown above
                      .map(factor => (
                        <li key={factor.id} className="text-sm flex items-center">
                          <span className={`inline-block w-6 text-center ${factor.impact > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {factor.impact > 0 ? '+' : ''}{factor.impact}
                          </span>
                          <span className="ml-2">{factor.name}</span>
                        </li>
                      ))
                    }
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Insights panel (1/3 width on large screens) */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border">
            {/* Insights header */}
            <div className="p-4 border-b flex justify-between items-center">
              <div className="flex items-center">
                <Sparkles className="h-5 w-5 text-blue-600 mr-2" />
                <h2 className="text-lg font-medium text-gray-900">
                  Insight Pemasaran
                </h2>
              </div>
              <button
                className="p-1 rounded-full hover:bg-gray-100"
                onClick={toggleInsightsPanel}
              >
                {showInsightsPanel ? (
                  <ChevronUp className="h-5 w-5 text-gray-600" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-600" />
                )}
              </button>
            </div>

            {showInsightsPanel && (
              <>
                {/* Search and filters */}
                <div className="p-4 border-b">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Cari insight..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md"
                    />
                    <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                  </div>

                  {selectedDay && (
                    <div className="mt-2 text-sm text-blue-600 flex items-center">
                      <Info className="h-4 w-4 mr-1" />
                      <span>Menampilkan insight untuk {new Date(selectedDay.date).toLocaleDateString('id-ID', { day: 'numeric', month: 'long' })}</span>
                      <button
                        className="ml-2 text-gray-500 hover:text-gray-700"
                        onClick={handleResetFilters}
                      >
                        (Reset)
                      </button>
                    </div>
                  )}
                </div>

                {/* Insights list */}
                <div className="p-4 space-y-4 max-h-[600px] overflow-y-auto">
                  {filteredInsights.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Zap className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                      <p>Tidak ada insight yang ditemukan.</p>
                      <p className="text-sm mt-1">Coba reset filter atau gunakan Analisis AI untuk mendapatkan insight baru.</p>
                    </div>
                  ) : (
                    filteredInsights.map(insight => (
                      <SeasonalInsightCard
                        key={insight.id}
                        insight={insight}
                        onSchedule={() => handleScheduleFromInsight(insight)}
                        onGenerateContent={() => {
                          setSelectedInsight(insight);
                          setActiveTab('content');
                        }}
                      />
                    ))
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Promo scheduler modal */}
      {showScheduler && (
        <PromoScheduler
          initialDate={schedulerDate}
          insight={schedulerInsight}
          onClose={() => setShowScheduler(false)}
          onSave={handleSavePromotion}
        />
      )}
    </div>
  );
}
