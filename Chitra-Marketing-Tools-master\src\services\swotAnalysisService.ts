/**
 * Service for SWOT Analysis AI feature
 */

import { v4 as uuidv4 } from 'uuid';
import * as XLSX from 'xlsx';
import {
  SwotAnalysisRequest,
  SwotAnalysisResult,
  ManualSwotItem,
  SwotCategory,
  SwotInsightCategory,
  SwotDataSource,
  IntegratedSwotData
} from '../types/swotAnalysis';
import { Product } from '../types';
import { FleetlistItem } from './fleetlistService';
import { SalesRevenueItem } from './salesRevenue2025Service';
import { KnowledgeEntry, KnowledgeCategory } from '../types/knowledgeBase';
import { fetchProducts } from './productService';
import { fetchFleetlist } from './fleetlistService';
import { loadSalesRevenueData } from './salesRevenue2025Service';
import { getAllKnowledgeEntries, searchKnowledgeEntries } from './knowledgeBaseService';

// OpenRouter API configuration
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const OPENROUTER_API_KEY = '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';
const MODEL = 'openai/gpt-4.1-nano';

// Local storage key for saving SWOT analyses
const SWOT_ANALYSES_STORAGE_KEY = 'chitraMarketingTools_swotAnalyses';

/**
 * Perform SWOT analysis
 */
export const performSwotAnalysis = async (request: SwotAnalysisRequest): Promise<SwotAnalysisResult> => {
  try {
    console.log('Performing SWOT analysis with request:', request);

    // 1. Gather data from different sources
    const integratedData = await integrateData(request);

    // 2. Generate SWOT analysis using OpenRouter
    const swotAnalysis = await generateSwotAnalysisWithAI(request, integratedData);

    // 3. Save the analysis to local storage
    saveSwotAnalysis(swotAnalysis);

    return swotAnalysis;
  } catch (error) {
    console.error('Error performing SWOT analysis:', error);
    throw new Error('Failed to perform SWOT analysis');
  }
};

/**
 * Integrate data from different sources
 */
const integrateData = async (request: SwotAnalysisRequest): Promise<IntegratedSwotData> => {
  const integratedData: IntegratedSwotData = {
    companyData: {
      products: [],
      fleetData: [],
      salesData: []
    },
    knowledgeBaseData: [],
    manualInput: {
      strengths: request.manualStrengths || [],
      weaknesses: request.manualWeaknesses || [],
      opportunities: request.manualOpportunities || [],
      threats: request.manualThreats || []
    }
  };

  // Fetch product data if requested
  if (request.includeProductData) {
    integratedData.companyData.products = await fetchProducts();
  }

  // Fetch fleet data if requested
  if (request.includeFleetData) {
    integratedData.companyData.fleetData = await fetchFleetlist();
  }

  // Fetch sales data if requested
  if (request.includeSalesData) {
    integratedData.companyData.salesData = await loadSalesRevenueData();
  }

  // Fetch knowledge base data if requested
  if (request.includeKnowledgeBase) {
    // Get company general data from knowledge base
    const companyData = await searchKnowledgeEntries({
      category: KnowledgeCategory.COMPANY_GENERAL_DATA
    });

    // Get all knowledge entries that might be relevant
    const allEntries = await getAllKnowledgeEntries();

    // Combine the data
    integratedData.knowledgeBaseData = [
      ...companyData.entries,
      ...allEntries.filter(entry =>
        entry.category !== KnowledgeCategory.COMPANY_GENERAL_DATA &&
        (entry.category === KnowledgeCategory.PRODUCT_KNOWLEDGE ||
         entry.category === KnowledgeCategory.CUSTOMER_INSIGHTS ||
         entry.category === KnowledgeCategory.PRICING_STRATEGY)
      )
    ];
  }

  return integratedData;
};

/**
 * Generate SWOT analysis using OpenRouter AI
 */
const generateSwotAnalysisWithAI = async (
  request: SwotAnalysisRequest,
  integratedData: IntegratedSwotData
): Promise<SwotAnalysisResult> => {
  try {
    console.log('Generating SWOT analysis with OpenRouter AI');

    // Check if there's manual input
    const hasManualInput =
      (request.manualStrengths && request.manualStrengths.length > 0) ||
      (request.manualWeaknesses && request.manualWeaknesses.length > 0) ||
      (request.manualOpportunities && request.manualOpportunities.length > 0) ||
      (request.manualThreats && request.manualThreats.length > 0);

    // Prepare system prompt
    const systemPrompt = `Anda adalah konsultan strategi bisnis yang ahli dalam analisis SWOT (Strengths, Weaknesses, Opportunities, Threats) untuk industri ban dan otomotif. Anda akan menganalisis data perusahaan untuk menghasilkan analisis SWOT yang komprehensif dan memberikan insight serta rekomendasi strategis.

${hasManualInput ? 'Pengguna telah memberikan beberapa elemen SWOT secara manual. Anda harus menyempurnakan elemen-elemen tersebut dan menambahkan elemen baru berdasarkan data yang tersedia.' : 'Anda perlu mengidentifikasi elemen-elemen SWOT berdasarkan data yang tersedia.'}`;

    // Prepare user prompt with data
    const userPrompt = `Lakukan analisis SWOT untuk ${request.companyName}.

${hasManualInput ? `
Pengguna telah memberikan elemen SWOT berikut secara manual:

STRENGTHS:
${request.manualStrengths.map(s => `- ${s.title}: ${s.description}`).join('\n')}

WEAKNESSES:
${request.manualWeaknesses.map(w => `- ${w.title}: ${w.description}`).join('\n')}

OPPORTUNITIES:
${request.manualOpportunities.map(o => `- ${o.title}: ${o.description}`).join('\n')}

THREATS:
${request.manualThreats.map(t => `- ${t.title}: ${t.description}`).join('\n')}

Sempurnakan elemen-elemen di atas dan tambahkan elemen baru berdasarkan data yang tersedia.
` : 'Identifikasi elemen-elemen SWOT berdasarkan data yang tersedia.'}

Data yang tersedia:
${JSON.stringify(integratedData, null, 2)}

${request.customPrompt || ''}

Berikan hasil dalam format JSON dengan struktur berikut:
{
  "strengths": [
    {
      "title": "Judul kekuatan",
      "description": "Deskripsi detail",
      "impactScore": 1-10,
      "category": "strength",
      "dataSource": "sumber data"
    }
  ],
  "weaknesses": [...],
  "opportunities": [...],
  "threats": [...],
  "insights": [
    {
      "title": "Judul insight",
      "description": "Deskripsi detail",
      "category": "kategori insight",
      "relatedSwotItems": ["judul item SWOT terkait"]
    }
  ],
  "recommendations": [
    {
      "title": "Judul rekomendasi",
      "description": "Deskripsi detail",
      "priority": "high/medium/low",
      "implementationTimeframe": "short-term/medium-term/long-term",
      "relatedInsights": ["judul insight terkait"],
      "expectedOutcome": "hasil yang diharapkan"
    }
  ]
}`;

    // Make the API call to OpenRouter
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': window.location.origin || 'https://chitraparatama.co.id',
        'X-Title': 'Chitra Marketing Tools - SWOT Analysis'
      },
      body: JSON.stringify({
        model: MODEL,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 4000,
        response_format: { type: "json_object" }
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const aiResponse = JSON.parse(data.choices[0].message.content);

    // Format the AI response into our SwotAnalysisResult structure
    const result: SwotAnalysisResult = {
      id: uuidv4(),
      timestamp: Date.now(),
      companyName: request.companyName,
      strengths: aiResponse.strengths.map((item: any) => ({
        ...item,
        id: uuidv4()
      })),
      weaknesses: aiResponse.weaknesses.map((item: any) => ({
        ...item,
        id: uuidv4()
      })),
      opportunities: aiResponse.opportunities.map((item: any) => ({
        ...item,
        id: uuidv4()
      })),
      threats: aiResponse.threats.map((item: any) => ({
        ...item,
        id: uuidv4()
      })),
      insights: aiResponse.insights.map((item: any) => ({
        ...item,
        id: uuidv4()
      })),
      recommendations: aiResponse.recommendations.map((item: any) => ({
        ...item,
        id: uuidv4()
      })),
      dataSourcesUsed: {
        productData: request.includeProductData,
        fleetData: request.includeFleetData,
        salesData: request.includeSalesData,
        knowledgeBase: request.includeKnowledgeBase
      },
      manualInputUsed: hasManualInput
    };

    return result;
  } catch (error) {
    console.error('Error generating SWOT analysis with AI:', error);

    // Return a basic fallback analysis
    return createFallbackSwotAnalysis(request);
  }
};

/**
 * Create a fallback SWOT analysis when AI fails
 */
const createFallbackSwotAnalysis = (request: SwotAnalysisRequest): SwotAnalysisResult => {
  // Check if there's manual input
  const hasManualInput =
    (request.manualStrengths && request.manualStrengths.length > 0) ||
    (request.manualWeaknesses && request.manualWeaknesses.length > 0) ||
    (request.manualOpportunities && request.manualOpportunities.length > 0) ||
    (request.manualThreats && request.manualThreats.length > 0);

  // Use manual input if available
  const strengths = request.manualStrengths && request.manualStrengths.length > 0
    ? request.manualStrengths.map(item => ({
        id: uuidv4(),
        title: item.title,
        description: item.description,
        impactScore: 8,
        category: SwotCategory.STRENGTH,
        dataSource: SwotDataSource.MANUAL_INPUT
      }))
    : [{
        id: uuidv4(),
        title: "Kualitas Produk Superior",
        description: "Produk memiliki daya tahan dan performa yang lebih baik",
        impactScore: 8,
        category: SwotCategory.STRENGTH,
        dataSource: SwotDataSource.PRODUCT_DATA
      }];

  const weaknesses = request.manualWeaknesses && request.manualWeaknesses.length > 0
    ? request.manualWeaknesses.map(item => ({
        id: uuidv4(),
        title: item.title,
        description: item.description,
        impactScore: 7,
        category: SwotCategory.WEAKNESS,
        dataSource: SwotDataSource.MANUAL_INPUT
      }))
    : [{
        id: uuidv4(),
        title: "Distribusi Terbatas",
        description: "Jaringan distribusi belum mencakup semua wilayah potensial",
        impactScore: 7,
        category: SwotCategory.WEAKNESS,
        dataSource: SwotDataSource.AI_ANALYSIS
      }];

  const opportunities = request.manualOpportunities && request.manualOpportunities.length > 0
    ? request.manualOpportunities.map(item => ({
        id: uuidv4(),
        title: item.title,
        description: item.description,
        impactScore: 9,
        category: SwotCategory.OPPORTUNITY,
        dataSource: SwotDataSource.MANUAL_INPUT
      }))
    : [{
        id: uuidv4(),
        title: "Ekspansi Pasar Baru",
        description: "Potensi untuk memasuki pasar baru di wilayah timur Indonesia",
        impactScore: 9,
        category: SwotCategory.OPPORTUNITY,
        dataSource: SwotDataSource.AI_ANALYSIS
      }];

  const threats = request.manualThreats && request.manualThreats.length > 0
    ? request.manualThreats.map(item => ({
        id: uuidv4(),
        title: item.title,
        description: item.description,
        impactScore: 8,
        category: SwotCategory.THREAT,
        dataSource: SwotDataSource.MANUAL_INPUT
      }))
    : [{
        id: uuidv4(),
        title: "Perubahan Regulasi",
        description: "Perubahan regulasi pemerintah yang dapat mempengaruhi operasional",
        impactScore: 8,
        category: SwotCategory.THREAT,
        dataSource: SwotDataSource.AI_ANALYSIS
      }];

  return {
    id: uuidv4(),
    timestamp: Date.now(),
    companyName: request.companyName,
    strengths,
    weaknesses,
    opportunities,
    threats,
    insights: [
      {
        id: uuidv4(),
        title: "Fokus pada Kualitas sebagai Diferensiasi",
        description: "Kualitas produk yang superior dapat menjadi kunci diferensiasi di pasar",
        category: SwotInsightCategory.COMPETITIVE_ADVANTAGE,
        relatedSwotItems: ["Kualitas Produk Superior"]
      }
    ],
    recommendations: [
      {
        id: uuidv4(),
        title: "Perluas Jaringan Distribusi",
        description: "Tambahkan channel distribusi di wilayah timur Indonesia",
        priority: "high",
        implementationTimeframe: "medium-term",
        relatedInsights: ["Fokus pada Kualitas sebagai Diferensiasi"],
        expectedOutcome: "Peningkatan penjualan sebesar 15-20% dalam 1 tahun"
      }
    ],
    dataSourcesUsed: {
      productData: request.includeProductData,
      fleetData: request.includeFleetData,
      salesData: request.includeSalesData,
      knowledgeBase: request.includeKnowledgeBase
    },
    manualInputUsed: hasManualInput
  };
};

/**
 * Save SWOT analysis to local storage
 */
const saveSwotAnalysis = (analysis: SwotAnalysisResult): void => {
  try {
    // Get existing analyses
    const existingAnalysesJson = localStorage.getItem(SWOT_ANALYSES_STORAGE_KEY);
    const existingAnalyses: SwotAnalysisResult[] = existingAnalysesJson
      ? JSON.parse(existingAnalysesJson)
      : [];

    // Add new analysis
    existingAnalyses.push(analysis);

    // Save back to local storage
    localStorage.setItem(SWOT_ANALYSES_STORAGE_KEY, JSON.stringify(existingAnalyses));

    console.log('SWOT analysis saved to local storage');
  } catch (error) {
    console.error('Error saving SWOT analysis to local storage:', error);
  }
};

/**
 * Get all saved SWOT analyses
 */
export const getSwotAnalyses = (): SwotAnalysisResult[] => {
  try {
    const analysesJson = localStorage.getItem(SWOT_ANALYSES_STORAGE_KEY);
    return analysesJson ? JSON.parse(analysesJson) : [];
  } catch (error) {
    console.error('Error getting SWOT analyses from local storage:', error);
    return [];
  }
};

/**
 * Get a specific SWOT analysis by ID
 */
export const getSwotAnalysisById = (id: string): SwotAnalysisResult | null => {
  try {
    const analyses = getSwotAnalyses();
    return analyses.find(analysis => analysis.id === id) || null;
  } catch (error) {
    console.error('Error getting SWOT analysis by ID:', error);
    return null;
  }
};

/**
 * Delete a SWOT analysis by ID
 */
export const deleteSwotAnalysis = (id: string): boolean => {
  try {
    const analyses = getSwotAnalyses();
    const filteredAnalyses = analyses.filter(analysis => analysis.id !== id);
    localStorage.setItem(SWOT_ANALYSES_STORAGE_KEY, JSON.stringify(filteredAnalyses));
    return true;
  } catch (error) {
    console.error('Error deleting SWOT analysis:', error);
    return false;
  }
};



/**
 * Export SWOT analysis to Excel
 */
export const exportSwotAnalysisToExcel = (analysis: SwotAnalysisResult): void => {
  try {
    // Create workbook
    const wb = XLSX.utils.book_new();

    // Create SWOT worksheet
    const swotData = [
      ['SWOT Analysis for ' + analysis.companyName, '', '', ''],
      ['Date: ' + new Date(analysis.timestamp).toLocaleDateString(), '', '', ''],
      ['', '', '', ''],
      ['Strengths', 'Weaknesses', '', ''],
      ...analysis.strengths.map((s, i) => [
        `${s.title} (${s.impactScore}/10)`,
        i < analysis.weaknesses.length ? `${analysis.weaknesses[i].title} (${analysis.weaknesses[i].impactScore}/10)` : '',
        '',
        ''
      ]),
      ['', '', '', ''],
      ['Opportunities', 'Threats', '', ''],
      ...analysis.opportunities.map((o, i) => [
        `${o.title} (${o.impactScore}/10)`,
        i < analysis.threats.length ? `${analysis.threats[i].title} (${analysis.threats[i].impactScore}/10)` : '',
        '',
        ''
      ]),
      ['', '', '', ''],
      ['Insights', '', '', ''],
      ...analysis.insights.map(i => [i.title, i.description, i.category, '']),
      ['', '', '', ''],
      ['Recommendations', '', '', ''],
      ...analysis.recommendations.map(r => [r.title, r.description, `Priority: ${r.priority}`, `Timeframe: ${r.implementationTimeframe}`])
    ];

    const swotWs = XLSX.utils.aoa_to_sheet(swotData);
    XLSX.utils.book_append_sheet(wb, swotWs, 'SWOT Analysis');

    // Create Details worksheet with more information
    const detailsData = [
      ['Detailed SWOT Analysis', '', '', '', ''],
      ['', '', '', '', ''],
      ['Strengths', '', '', '', ''],
      ['Title', 'Description', 'Impact Score', 'Related To', 'Data Source'],
      ...analysis.strengths.map(s => [s.title, s.description, s.impactScore, s.relatedTo?.join(', ') || '', s.dataSource || '']),
      ['', '', '', '', ''],
      ['Weaknesses', '', '', '', ''],
      ['Title', 'Description', 'Impact Score', 'Related To', 'Data Source'],
      ...analysis.weaknesses.map(w => [w.title, w.description, w.impactScore, w.relatedTo?.join(', ') || '', w.dataSource || '']),
      ['', '', '', '', ''],
      ['Opportunities', '', '', '', ''],
      ['Title', 'Description', 'Impact Score', 'Related To', 'Data Source'],
      ...analysis.opportunities.map(o => [o.title, o.description, o.impactScore, o.relatedTo?.join(', ') || '', o.dataSource || '']),
      ['', '', '', '', ''],
      ['Threats', '', '', '', ''],
      ['Title', 'Description', 'Impact Score', 'Related To', 'Data Source'],
      ...analysis.threats.map(t => [t.title, t.description, t.impactScore, t.relatedTo?.join(', ') || '', t.dataSource || ''])
    ];

    const detailsWs = XLSX.utils.aoa_to_sheet(detailsData);
    XLSX.utils.book_append_sheet(wb, detailsWs, 'Details');

    // Create Insights & Recommendations worksheet
    const insightsRecsData = [
      ['Insights & Recommendations', '', '', '', ''],
      ['', '', '', '', ''],
      ['Insights', '', '', '', ''],
      ['Title', 'Description', 'Category', 'Related SWOT Items', ''],
      ...analysis.insights.map(i => [i.title, i.description, i.category, i.relatedSwotItems?.join(', ') || '', '']),
      ['', '', '', '', ''],
      ['Recommendations', '', '', '', ''],
      ['Title', 'Description', 'Priority', 'Timeframe', 'Expected Outcome'],
      ...analysis.recommendations.map(r => [r.title, r.description, r.priority, r.implementationTimeframe, r.expectedOutcome])
    ];

    const insightsRecsWs = XLSX.utils.aoa_to_sheet(insightsRecsData);
    XLSX.utils.book_append_sheet(wb, insightsRecsWs, 'Insights & Recommendations');

    // Export the workbook
    XLSX.writeFile(wb, `SWOT_Analysis_${analysis.companyName}_${new Date().toISOString().split('T')[0]}.xlsx`);
  } catch (error) {
    console.error('Error exporting SWOT analysis to Excel:', error);
    throw new Error('Failed to export SWOT analysis to Excel');
  }
};
