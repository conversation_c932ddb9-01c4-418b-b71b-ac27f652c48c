import axios from 'axios';
import { MODELS } from './openRouter';

// Using the same API key as the other AI services
const API_KEY = '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';

// Define a specialized model for data analysis
const DATA_ANALYSIS_MODEL = MODELS.DEEPSEEK; // Using GPT-4.1-nano for analytical tasks

interface CompetitorData {
  avgPrice: number;
  minPrice: number;
  maxPrice: number;
  premiumAvgPrice: number;
  economyAvgPrice: number;
  topBrands: { brand: string; price: number }[];
}

interface PriceAnalysisInput {
  mainProductName: string;
  mainProductPrice: number;
  secondaryProductName: string;
  secondaryProductPrice: number;
  secondaryProductQty: number;
  minimumMainProductQty: number;
  minimumSellPrice: number;
  maximumSellPrice: number;
  targetMargin: number;
  totalCost: number;
  competitorData?: CompetitorData;
}

interface PriceAnalysisResult {
  optimalPrice: number;
  optimalMargin: number;
  reasoning: string;
}

export async function analyzeBundlePrice(input: PriceAnalysisInput): Promise<PriceAnalysisResult> {
  try {
    console.log('Analyzing optimal bundle price with AI model:', DATA_ANALYSIS_MODEL);

    const requestBody = {
      model: DATA_ANALYSIS_MODEL,
      messages: [
        {
          role: 'system',
          content: 'You are an expert pricing analyst specializing in heavy equipment parts and tires. You analyze market data and provide optimal pricing recommendations based on cost, margin targets, and market positioning. Your analysis should be data-driven and consider psychological pricing factors.'
        },
        {
          role: 'user',
          content: `Analyze the following bundling scenario and determine the optimal selling price:

Main Product: ${input.mainProductName}
Main Product Cost: ${input.mainProductPrice.toLocaleString()} IDR
Secondary Product: ${input.secondaryProductName}
Secondary Product Cost: ${input.secondaryProductPrice.toLocaleString()} IDR
Secondary Product Quantity: ${input.secondaryProductQty} units
Minimum Main Product Quantity Needed: ${input.minimumMainProductQty} units
Minimum Selling Price Constraint: ${input.minimumSellPrice.toLocaleString()} IDR
Maximum Selling Price Constraint: ${input.maximumSellPrice.toLocaleString()} IDR
Target Margin: ${input.targetMargin}%
Total Bundle Cost: ${input.totalCost.toLocaleString()} IDR
${input.competitorData ? `
Competitor Price Data for 27.00R49 Tires:
- Average Market Price: ${input.competitorData.avgPrice.toLocaleString()} IDR
- Premium Brands Average Price: ${input.competitorData.premiumAvgPrice.toLocaleString()} IDR
- Economy Brands Average Price: ${input.competitorData.economyAvgPrice.toLocaleString()} IDR
- Price Range: ${input.competitorData.minPrice.toLocaleString()} - ${input.competitorData.maxPrice.toLocaleString()} IDR
- Top Brands by Price:
  ${input.competitorData.topBrands.map(b => `* ${b.brand}: ${b.price.toLocaleString()} IDR`).join('\n  ')}
` : ''}

Based on your analysis, determine:
1. The optimal selling price (a specific price, not a range) between the minimum and maximum constraints
2. The resulting profit margin at this price
3. A brief explanation of your reasoning that includes comparison with competitor prices

Consider factors like:
- Psychological pricing (e.g., prices ending in 9s)
- Competitive positioning in the heavy equipment market (compare with competitor data)
- Value perception for bundled products
- Balancing margin with sales volume potential
- Market positioning relative to premium and economy brands

Return your analysis in JSON format with these fields:
- optimalPrice: (number, the recommended price in IDR)
- optimalMargin: (number, the percentage margin at this price)
- reasoning: (string, brief explanation of your recommendation)

Your response should ONLY contain valid JSON that can be parsed.`
        }
      ]
    };

    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://bundleboost.app',
          'X-Title': 'Chitra Marketing Tools'
        }
      }
    );

    console.log('Price analysis API response:', JSON.stringify(response.data, null, 2));

    // Extract the content from the response
    let content = '';
    if (response.data && response.data.choices && response.data.choices.length > 0) {
      content = response.data.choices[0].message.content;
    } else if (response.data && response.data.candidates && response.data.candidates.length > 0) {
      content = response.data.candidates[0].content.parts[0].text;
    }

    // Try to parse the JSON response
    try {
      // Extract JSON from the response (in case there's any extra text)
      const jsonMatch = content.match(/\\{.*\\}/s) || content.match(/\\{[\\s\\S]*\\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : content;

      // Clean up the string to ensure it's valid JSON
      const cleanedJson = jsonString
        .replace(/^```json/, '')
        .replace(/```$/, '')
        .trim();

      const result = JSON.parse(cleanedJson);

      // Validate the result has the expected properties
      if (!result.optimalPrice || !result.optimalMargin || !result.reasoning) {
        throw new Error('Invalid response format');
      }

      return {
        optimalPrice: Number(result.optimalPrice),
        optimalMargin: Number(result.optimalMargin),
        reasoning: result.reasoning
      };
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);

      // Fallback to a calculated price if parsing fails
      const range = input.maximumSellPrice - input.minimumSellPrice;
      const marketFactor = 0.65; // Default market factor
      let optimalPrice = input.minimumSellPrice + (range * marketFactor);

      // Apply psychological pricing (ending in 999,000)
      optimalPrice = Math.floor(optimalPrice / 1000000) * 1000000 + 999000;

      // Ensure optimal price is within range
      optimalPrice = Math.max(input.minimumSellPrice, Math.min(input.maximumSellPrice, optimalPrice));

      // Calculate margin
      const optimalMargin = ((optimalPrice - input.totalCost) / input.totalCost) * 100;

      return {
        optimalPrice,
        optimalMargin,
        reasoning: "Harga ini dihitung berdasarkan algoritma optimasi dengan mempertimbangkan faktor psikologis harga dan posisi kompetitif di pasar. Harga diatur untuk memaksimalkan margin sambil tetap menarik bagi pelanggan."
      };
    }
  } catch (error) {
    console.error('Error analyzing bundle price:', error);

    // Fallback calculation if the API call fails
    const range = input.maximumSellPrice - input.minimumSellPrice;
    const marketFactor = 0.65; // Default market factor
    let optimalPrice = input.minimumSellPrice + (range * marketFactor);

    // Apply psychological pricing (ending in 999,000)
    optimalPrice = Math.floor(optimalPrice / 1000000) * 1000000 + 999000;

    // Ensure optimal price is within range
    optimalPrice = Math.max(input.minimumSellPrice, Math.min(input.maximumSellPrice, optimalPrice));

    // Calculate margin
    const optimalMargin = ((optimalPrice - input.totalCost) / input.totalCost) * 100;

    return {
      optimalPrice,
      optimalMargin,
      reasoning: "Harga ini dihitung menggunakan algoritma fallback karena layanan AI tidak tersedia. Perhitungan mempertimbangkan rentang harga yang ditentukan dan faktor psikologis harga untuk memaksimalkan daya tarik penawaran."
    };
  }
}
