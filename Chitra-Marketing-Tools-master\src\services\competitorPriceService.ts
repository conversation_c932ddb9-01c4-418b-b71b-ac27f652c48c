import axios from 'axios';
import <PERSON> from 'papapar<PERSON>';

// URL for the competitor price dataset
const COMPETITOR_PRICE_URL = 'https://docs.google.com/spreadsheets/d/e/2PACX-1vTFCYrDPugIyxFQMQaUS2e11OY8NIGSOqd-jz5jznHSMGORjl0SSFEFNA2p0Iw_r8FHz3PGJ78IncXk/pub?gid=**********&single=true&output=csv';

// Add CORS proxy if needed
const useCorsProxy = true;
const corsProxy = 'https://corsproxy.io/?';

// Get the actual URL with CORS proxy if needed
const getCompetitorPriceUrl = () => useCorsProxy ? `${corsProxy}${encodeURIComponent(COMPETITOR_PRICE_URL)}` : COMPETITOR_PRICE_URL;

// Interface for competitor price data
export interface CompetitorPrice {
  id: string;
  customer: string;
  tireSize: string;
  brand: string;
  category: string;
  supplier: string;
  currency: string;
  price: number;
  remark: string;
  date: string;
  businessConsultant: string;
  formattedPrice: string;
}

/**
 * Fetches and processes competitor price data for 27.00R49 tires
 */
export const fetchCompetitorPrices = async (): Promise<CompetitorPrice[]> => {
  try {
    const url = getCompetitorPriceUrl();
    console.log('Fetching competitor price data from:', url);
    const response = await axios.get(url, {
      headers: {
        'Accept': 'text/csv; charset=utf-8',
      }
    });

    // Log the first part of the response to debug
    console.log('Competitor price data response preview:', response.data.substring(0, 200) + '...');

    return new Promise((resolve, reject) => {
      Papa.parse(response.data, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          if (results.data && Array.isArray(results.data)) {
            console.log('Competitor price raw data sample:', results.data.slice(0, 2));
            console.log('Competitor price columns:', results.meta.fields);

            // Transform data to match our interface and filter for 27.00R49 tires
            const competitorPrices = results.data
              .filter((item: any) => {
                // Check for different possible formats of 27.00R49
                const sizeValue = item['Size Tire'] || '';
                return sizeValue === '27.00R49' ||
                       sizeValue === '27.00 R 49' ||
                       sizeValue === '27.00-R49' ||
                       sizeValue === '27R49' ||
                       sizeValue === '27.00 R49' ||
                       sizeValue === '27.0R49' ||
                       sizeValue === '27R49' ||
                       sizeValue === '27.00-49';
              })
              .map((item: any, index: number) => {
                // Parse price value
                let priceValue = 0;
                try {
                  if (item.Currency === 'USD') {
                    // Convert USD to IDR (assuming 1 USD = 16,400 IDR)
                    // Handle different price formats
                    const priceStr = item.Price || '0';
                    const usdValue = parseFloat(priceStr.replace(/[^\d.-]/g, ''));
                    priceValue = usdValue * 16400;
                  } else {
                    // Parse IDR value - handle different formats
                    const priceStr = item.Price || '0';
                    // Remove non-numeric characters except decimal point
                    const cleanPrice = priceStr.replace(/[^\d.-]/g, '');
                    priceValue = parseFloat(cleanPrice);

                    // If price is still NaN, try to parse from PRICE column
                    if (isNaN(priceValue) && item['PRICE']) {
                      const formattedPrice = item['PRICE'].replace(/[^\d.-]/g, '');
                      priceValue = parseFloat(formattedPrice);
                    }
                  }
                } catch (error) {
                  console.error('Error parsing price:', item.Price, error);
                  priceValue = 0;
                }

                // If price is still NaN, set to 0
                if (isNaN(priceValue)) {
                  priceValue = 0;
                }

                return {
                  id: `cp-${index + 1}`,
                  customer: item['Nama Customer'] || '',
                  tireSize: item['Size Tire'] || '',
                  brand: item.Brand || '',
                  category: item['Category Tire'] || '',
                  supplier: item.Supplier || '',
                  currency: item.Currency || 'IDR',
                  price: priceValue,
                  remark: item['Remark / Delivery Drop Point'] || '',
                  date: item['Tanggal Informasi'] || '',
                  businessConsultant: item['Business Consultant'] || '',
                  formattedPrice: item['PRICE'] || ''
                };
              });

            console.log(`Successfully parsed ${competitorPrices.length} competitor price records for 27.00R49 tires`);
            if (competitorPrices.length > 0) {
              console.log('Sample transformed competitor price data:', competitorPrices[0]);
            }

            resolve(competitorPrices);
          } else {
            console.error('Invalid competitor price data structure:', results);
            reject(new Error('Failed to parse competitor price CSV data'));
          }
        },
        error: (error) => {
          console.error('Error parsing competitor price CSV:', error);
          reject(error);
        }
      });
    });
  } catch (error) {
    console.error('Error fetching competitor price data:', error);
    return FALLBACK_COMPETITOR_PRICES;
  }
};

/**
 * Calculate average price by brand
 */
export const calculateAveragePriceByBrand = (data: CompetitorPrice[]): { brand: string, avgPrice: number, count: number }[] => {
  const brandPrices: { [key: string]: { total: number, count: number } } = {};

  // Calculate total price and count for each brand
  data.forEach(item => {
    if (!brandPrices[item.brand]) {
      brandPrices[item.brand] = { total: 0, count: 0 };
    }
    brandPrices[item.brand].total += item.price;
    brandPrices[item.brand].count += 1;
  });

  // Calculate average price for each brand
  return Object.entries(brandPrices)
    .map(([brand, { total, count }]) => ({
      brand,
      avgPrice: total / count,
      count
    }))
    .sort((a, b) => b.avgPrice - a.avgPrice);
};

/**
 * Get price statistics for 27.00R49 tires
 */
export const getPriceStatistics = (data: CompetitorPrice[]): {
  minPrice: number,
  maxPrice: number,
  avgPrice: number,
  medianPrice: number,
  premiumAvgPrice: number,
  economyAvgPrice: number
} => {
  if (data.length === 0) {
    return {
      minPrice: 0,
      maxPrice: 0,
      avgPrice: 0,
      medianPrice: 0,
      premiumAvgPrice: 0,
      economyAvgPrice: 0
    };
  }

  // Sort prices for calculations
  const prices = data.map(item => item.price).sort((a, b) => a - b);

  // Calculate min, max, avg
  const minPrice = prices[0];
  const maxPrice = prices[prices.length - 1];
  const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;

  // Calculate median
  const mid = Math.floor(prices.length / 2);
  const medianPrice = prices.length % 2 === 0
    ? (prices[mid - 1] + prices[mid]) / 2
    : prices[mid];

  // Categorize brands as premium or economy
  const premiumBrands = ['BRIDGESTONE', 'GOODYEAR', 'MICHELIN', 'GOOD YEAR'];
  const premiumPrices = data
    .filter(item => premiumBrands.includes(item.brand.toUpperCase()))
    .map(item => item.price);

  const economyPrices = data
    .filter(item => !premiumBrands.includes(item.brand.toUpperCase()))
    .map(item => item.price);

  // Calculate average prices for premium and economy brands
  const premiumAvgPrice = premiumPrices.length > 0
    ? premiumPrices.reduce((sum, price) => sum + price, 0) / premiumPrices.length
    : 0;

  const economyAvgPrice = economyPrices.length > 0
    ? economyPrices.reduce((sum, price) => sum + price, 0) / economyPrices.length
    : 0;

  return {
    minPrice,
    maxPrice,
    avgPrice,
    medianPrice,
    premiumAvgPrice,
    economyAvgPrice
  };
};

// Fallback data in case the API fails
export const FALLBACK_COMPETITOR_PRICES: CompetitorPrice[] = [
  {
    id: "cp-1",
    customer: "Graha Prima Energy",
    tireSize: "27.00R49",
    brand: "GOODYEAR",
    category: "Earthmover",
    supplier: "Centra Qualita",
    currency: "IDR",
    price: 179000000,
    remark: "Based on TTC Database",
    date: "01/02/2025",
    businessConsultant: "Ikbal Laisa",
    formattedPrice: "Rp179.000.000"
  },
  {
    id: "cp-2",
    customer: "WAHANA BANDAWA KENCANA",
    tireSize: "27.00R49",
    brand: "GOODYEAR",
    category: "Earthmover",
    supplier: "Centra Qualita",
    currency: "IDR",
    price: 191000000,
    remark: "",
    date: "02/02/2025",
    businessConsultant: "Buri Antoni",
    formattedPrice: "Rp191.000.000"
  },
  {
    id: "cp-3",
    customer: "Harmoni Panca Utama",
    tireSize: "27.00R49",
    brand: "MAXAM",
    category: "Earthmover",
    supplier: "Lima Pilar Abadi",
    currency: "IDR",
    price: 120000000,
    remark: "Set & Exclude PPN + Franco Site",
    date: "12/02/2025",
    businessConsultant: "Ketut Wisnu K",
    formattedPrice: "Rp120.000.000"
  },
  {
    id: "cp-4",
    customer: "Geopersada Mulia Abadi",
    tireSize: "27.00R49",
    brand: "BRIDGESTONE",
    category: "Earthmover",
    supplier: "Linda Hanta Wijaya",
    currency: "IDR",
    price: 185000000,
    remark: "Set & Exclude PPN + Franco Site",
    date: "14/02/2025",
    businessConsultant: "Agung Ari P",
    formattedPrice: "Rp185.000.000"
  },
  {
    id: "cp-5",
    customer: "Petrosea",
    tireSize: "27.00R49",
    brand: "BRIDGESTONE",
    category: "Earthmover",
    supplier: "Linda Hanta Wijaya",
    currency: "IDR",
    price: 215000000,
    remark: "Set & Exclude PPN + Franco Site",
    date: "14/02/2025",
    businessConsultant: "Agung Ari P",
    formattedPrice: "Rp215.000.000"
  },
  {
    id: "cp-6",
    customer: "PT HASNUR RIUNG SINERGI",
    tireSize: "27.00R49",
    brand: "GOOD YEAR",
    category: "Earthmover",
    supplier: "Centra Qualita",
    currency: "IDR",
    price: 185000000,
    remark: "SITE KALSEL",
    date: "10/03/2025",
    businessConsultant: "Zulfikar Ramli",
    formattedPrice: "Rp185.000.000"
  },
  {
    id: "cp-7",
    customer: "KPC",
    tireSize: "27.00R49",
    brand: "GOODYEAR",
    category: "Earthmover",
    supplier: "GOODYEAR",
    currency: "USD",
    price: 123984000,
    remark: "Based on TTC Database",
    date: "01/02/2025",
    businessConsultant: "Ikbal Laisa",
    formattedPrice: "Rp123.984.000"
  },
  {
    id: "cp-8",
    customer: "Abadi Jaya Laxmindo",
    tireSize: "27.00R49",
    brand: "BRIDGESTONE",
    category: "Earthmover",
    supplier: "Linda Hanta Wijaya",
    currency: "IDR",
    price: 190000000,
    remark: "Job Site",
    date: "06/03/2025",
    businessConsultant: "Ocky hegar",
    formattedPrice: "Rp190.000.000"
  },
  {
    id: "cp-9",
    customer: "Unggul Dinamika Utama",
    tireSize: "27.00R49",
    brand: "GOODYEAR",
    category: "Earthmover",
    supplier: "Linda Hanta Wijaya",
    currency: "IDR",
    price: 179000000,
    remark: "",
    date: "01/02/2025",
    businessConsultant: "Ikbal Laisa",
    formattedPrice: "Rp179.000.000"
  },
  {
    id: "cp-10",
    customer: "Thiess Indonesia",
    tireSize: "27.00R49",
    brand: "LUAN",
    category: "Earthmover",
    supplier: "Luan TIRE INDONESIA",
    currency: "IDR",
    price: 140000000,
    remark: "Set & Exclude PPN",
    date: "14/02/2025",
    businessConsultant: "Rochim Pradana",
    formattedPrice: "Rp140.000.000"
  },
  {
    id: "cp-11",
    customer: "Arkananta Aa Pratista",
    tireSize: "27.00R49",
    brand: "LUAN",
    category: "Earthmover",
    supplier: "Luan TIRE INDONESIA",
    currency: "IDR",
    price: 140000000,
    remark: "Set & Exclude PPN + Franco Site",
    date: "12/02/2025",
    businessConsultant: "Ketut Wisnu K",
    formattedPrice: "Rp140.000.000"
  },
  {
    id: "cp-12",
    customer: "HASNUR RIUNG SINERGI",
    tireSize: "27.00R49",
    brand: "LUAN",
    category: "Earthmover",
    supplier: "Luan TIRE INDONESIA",
    currency: "IDR",
    price: 140000000,
    remark: "MS401",
    date: "01/02/2025",
    businessConsultant: "Zulfikar Ramli",
    formattedPrice: "Rp140.000.000"
  },
  {
    id: "cp-13",
    customer: "Mandiri Karya Prima",
    tireSize: "27.00R49",
    brand: "LUAN",
    category: "Earthmover",
    supplier: "Luan TIRE INDONESIA",
    currency: "IDR",
    price: 140000000,
    remark: "Set & Exclude PPN + Franco Site",
    date: "12/02/2025",
    businessConsultant: "Ketut Wisnu K",
    formattedPrice: "Rp140.000.000"
  },
  {
    id: "cp-14",
    customer: "Petrosea",
    tireSize: "27.00R49",
    brand: "LUAN",
    category: "Earthmover",
    supplier: "Luan TIRE INDONESIA",
    currency: "IDR",
    price: 126000000,
    remark: "Set & Exclude PPN + Franco Site",
    date: "14/02/2025",
    businessConsultant: "Agung Ari P",
    formattedPrice: "Rp126.000.000"
  },
  {
    id: "cp-15",
    customer: "THRIEVENI",
    tireSize: "27.00R49",
    brand: "LUAN",
    category: "Earthmover",
    supplier: "Luan TIRE INDONESIA",
    currency: "IDR",
    price: 161000000,
    remark: "",
    date: "14/02/2025",
    businessConsultant: "Gregorius DR",
    formattedPrice: "Rp161.000.000"
  }
];
