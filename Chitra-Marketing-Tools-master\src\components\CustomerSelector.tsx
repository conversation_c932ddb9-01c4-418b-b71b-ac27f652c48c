import React, { useState, useEffect } from 'react';
import { Customer } from '../types';
import { getAllCustomers } from '../services/customerService';
import { Search, User, X } from 'lucide-react';

interface CustomerSelectorProps {
  selectedCustomerId: string | undefined;
  onCustomerSelect: (customerId: string | undefined) => void;
}

export default function CustomerSelector({ selectedCustomerId, onCustomerSelect }: CustomerSelectorProps) {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  
  // Load customers on component mount
  useEffect(() => {
    const allCustomers = getAllCustomers();
    setCustomers(allCustomers);
    setFilteredCustomers(allCustomers);
    
    // If there's a selected customer ID, find the customer
    if (selectedCustomerId) {
      const customer = allCustomers.find(c => c.id === selectedCustomerId);
      if (customer) {
        setSelectedCustomer(customer);
      }
    }
  }, [selectedCustomerId]);
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    
    if (query.trim() === '') {
      setFilteredCustomers(customers);
    } else {
      const filtered = customers.filter(customer => 
        customer.name.toLowerCase().includes(query.toLowerCase()) ||
        customer.company?.toLowerCase().includes(query.toLowerCase()) ||
        customer.email.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredCustomers(filtered);
    }
  };
  
  // Handle customer selection
  const handleSelectCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    onCustomerSelect(customer.id);
    setIsDropdownOpen(false);
    setSearchQuery('');
  };
  
  // Clear selected customer
  const handleClearCustomer = () => {
    setSelectedCustomer(null);
    onCustomerSelect(undefined);
  };
  
  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        Customer
      </label>
      
      {selectedCustomer ? (
        // Display selected customer
        <div className="flex items-center justify-between p-2 border rounded-md bg-blue-50 border-blue-200">
          <div className="flex items-center">
            <User className="h-5 w-5 text-blue-500 mr-2" />
            <div>
              <div className="font-medium">{selectedCustomer.name}</div>
              <div className="text-sm text-gray-500">{selectedCustomer.email}</div>
            </div>
          </div>
          <button
            onClick={handleClearCustomer}
            className="text-gray-400 hover:text-gray-600"
            title="Clear selected customer"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
      ) : (
        // Customer selector
        <div className="relative">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search for a customer..."
              value={searchQuery}
              onChange={handleSearchChange}
              onFocus={() => setIsDropdownOpen(true)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          
          {isDropdownOpen && (
            <div className="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-60 overflow-y-auto">
              {filteredCustomers.length === 0 ? (
                <div className="p-4 text-sm text-gray-500 text-center">
                  No customers found
                </div>
              ) : (
                <ul className="py-1">
                  {filteredCustomers.map(customer => (
                    <li
                      key={customer.id}
                      onClick={() => handleSelectCustomer(customer)}
                      className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    >
                      <div className="font-medium">{customer.name}</div>
                      <div className="text-sm text-gray-500">
                        {customer.company && `${customer.company} • `}
                        {customer.email}
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}
        </div>
      )}
      
      <p className="text-sm text-gray-500">
        Select a customer for this bundle to include their details in quotes and messages
      </p>
    </div>
  );
}
