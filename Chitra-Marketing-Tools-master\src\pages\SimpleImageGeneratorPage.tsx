import React, { useState } from 'react';
import { ImageModel } from '../types/imageGenerator';
import { generateImages } from '../services/simpleRunwareService';

const SimpleImageGeneratorPage: React.FC = () => {
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!prompt.trim()) {
      alert('Please enter a prompt');
      return;
    }

    setIsGenerating(true);

    try {
      const images = await generateImages({
        positivePrompt: prompt,
        width: 512,
        height: 512,
        model: ImageModel.JUGGERNAUT_PRO,
        numberResults: 1
      });

      if (images && images.length > 0) {
        setImageUrl(images[0].imageURL);
        alert('Image generated successfully!');
      } else {
        alert('No images were generated');
      }
    } catch (error) {
      console.error('Error generating images:', error);
      alert('Failed to generate images: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1 style={{ marginBottom: '20px' }}>Simple Image Generator</h1>

      <form onSubmit={handleSubmit} style={{ marginBottom: '20px' }}>
        <div style={{ marginBottom: '10px' }}>
          <label htmlFor="prompt" style={{ display: 'block', marginBottom: '5px' }}>
            Prompt
          </label>
          <textarea
            id="prompt"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="Describe the image you want to generate..."
            style={{ width: '100%', padding: '8px', minHeight: '100px' }}
            required
          />
        </div>

        <button
          type="submit"
          disabled={isGenerating || !prompt.trim()}
          style={{
            padding: '10px 15px',
            backgroundColor: isGenerating ? '#ccc' : '#4299e1',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isGenerating ? 'not-allowed' : 'pointer'
          }}
        >
          {isGenerating ? 'Generating...' : 'Generate Image'}
        </button>
      </form>

      {imageUrl && (
        <div>
          <h2 style={{ marginBottom: '10px' }}>Generated Image:</h2>
          <div style={{ border: '1px solid #ddd', borderRadius: '4px', overflow: 'hidden' }}>
            <img
              src={imageUrl}
              alt="Generated image"
              style={{ width: '100%', height: 'auto' }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleImageGeneratorPage;
