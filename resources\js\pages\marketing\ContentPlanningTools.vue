<template>
    <AppLayout>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Content Planning Tools</h1>
                    <p class="mt-2 text-gray-600">Tools untuk perencanaan konten marketing dan social media</p>
                </div>
                <div class="flex space-x-3">
                    <button
                        @click="exportPlan"
                        class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center"
                    >
                        <Download class="h-4 w-4 mr-2" />
                        Export Plan
                    </button>
                    <button
                        @click="generateMonthlyPlan"
                        :disabled="isGenerating"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
                    >
                        <Loader2 v-if="isGenerating" class="h-4 w-4 mr-2 animate-spin" />
                        <Sparkles v-else class="h-4 w-4 mr-2" />
                        Generate Monthly Plan
                    </button>
                </div>
            </div>

            <!-- Tab Navigation -->
            <div class="bg-white rounded-lg shadow">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6">
                        <button
                            v-for="tab in tabs"
                            :key="tab.id"
                            @click="activeTab = tab.id"
                            :class="[
                                'py-4 px-1 border-b-2 font-medium text-sm',
                                activeTab === tab.id
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            ]"
                        >
                            <component :is="tab.icon" class="h-4 w-4 mr-2 inline" />
                            {{ tab.name }}
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="p-6">
                    <!-- Monthly Plan Tab -->
                    <div v-if="activeTab === 'monthly-plan'" class="space-y-6">
                        <!-- Plan Configuration -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Konfigurasi Rencana Bulanan</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Bulan</label>
                                    <select 
                                        v-model="planConfig.month" 
                                        class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option v-for="(month, index) in months" :key="index" :value="index">
                                            {{ month }}
                                        </option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Tahun</label>
                                    <select 
                                        v-model="planConfig.year" 
                                        class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option v-for="year in years" :key="year" :value="year">
                                            {{ year }}
                                        </option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Post per Minggu</label>
                                    <input 
                                        v-model.number="planConfig.postsPerWeek" 
                                        type="number" 
                                        min="1" 
                                        max="7"
                                        class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    />
                                </div>
                            </div>

                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Kategori Konten</label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                    <label v-for="category in contentCategories" :key="category.id" class="flex items-center">
                                        <input 
                                            type="checkbox" 
                                            :value="category.id"
                                            v-model="planConfig.selectedCategories"
                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        />
                                        <span class="ml-2 text-sm text-gray-700">{{ category.name }}</span>
                                    </label>
                                </div>
                            </div>

                            <div class="mt-4 flex items-center">
                                <input 
                                    type="checkbox" 
                                    v-model="planConfig.includeHolidays"
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                                <span class="ml-2 text-sm text-gray-700">Sertakan konten hari libur dan perayaan</span>
                            </div>
                        </div>

                        <!-- Generated Plan -->
                        <div v-if="monthlyPlan" class="bg-white border rounded-lg">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">
                                    Rencana Konten {{ months[planConfig.month] }} {{ planConfig.year }}
                                </h3>
                                <p class="text-sm text-gray-600">{{ monthlyPlan.posts.length }} konten dijadwalkan</p>
                            </div>

                            <div class="p-6">
                                <div class="space-y-4">
                                    <div 
                                        v-for="post in monthlyPlan.posts" 
                                        :key="post.id"
                                        class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                                    >
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-3 mb-2">
                                                    <span class="text-sm font-medium text-blue-600">
                                                        {{ formatDate(post.date) }}
                                                    </span>
                                                    <span :class="[
                                                        'px-2 py-1 text-xs font-medium rounded-full',
                                                        getCategoryColor(post.category)
                                                    ]">
                                                        {{ post.category }}
                                                    </span>
                                                </div>
                                                <h4 class="font-medium text-gray-900 mb-2">{{ post.title }}</h4>
                                                <p class="text-sm text-gray-600 mb-3">{{ post.description }}</p>
                                                
                                                <!-- Hashtags -->
                                                <div v-if="post.hashtags && post.hashtags.length > 0" class="mb-3">
                                                    <div class="flex flex-wrap gap-1">
                                                        <span 
                                                            v-for="hashtag in post.hashtags.slice(0, 5)" 
                                                            :key="hashtag"
                                                            class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded"
                                                        >
                                                            #{{ hashtag }}
                                                        </span>
                                                        <span v-if="post.hashtags.length > 5" class="text-xs text-gray-500">
                                                            +{{ post.hashtags.length - 5 }} more
                                                        </span>
                                                    </div>
                                                </div>

                                                <!-- Image Description -->
                                                <div v-if="post.imageDescription" class="text-xs text-gray-500 italic">
                                                    📸 {{ post.imageDescription }}
                                                </div>
                                            </div>

                                            <div class="flex space-x-2 ml-4">
                                                <button
                                                    @click="editPost(post)"
                                                    class="p-2 text-gray-400 hover:text-blue-600"
                                                    title="Edit"
                                                >
                                                    <Edit class="h-4 w-4" />
                                                </button>
                                                <button
                                                    @click="savePost(post)"
                                                    class="p-2 text-gray-400 hover:text-green-600"
                                                    title="Save to Calendar"
                                                >
                                                    <Save class="h-4 w-4" />
                                                </button>
                                                <button
                                                    @click="deletePost(post.id)"
                                                    class="p-2 text-gray-400 hover:text-red-600"
                                                    title="Delete"
                                                >
                                                    <Trash2 class="h-4 w-4" />
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div v-else class="text-center py-12">
                            <Calendar class="h-12 w-12 mx-auto text-gray-300 mb-4" />
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Rencana Konten</h3>
                            <p class="text-gray-600 mb-4">Klik "Generate Monthly Plan" untuk membuat rencana konten bulanan</p>
                        </div>
                    </div>

                    <!-- Content Ideas Tab -->
                    <div v-if="activeTab === 'content-ideas'" class="space-y-6">
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Generator Ide Konten</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Topik/Tema</label>
                                    <input 
                                        v-model="ideaGenerator.topic" 
                                        type="text" 
                                        placeholder="Contoh: Ban untuk musim hujan"
                                        class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    />
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Target Audience</label>
                                    <select 
                                        v-model="ideaGenerator.audience" 
                                        class="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="mining">Industri Pertambangan</option>
                                        <option value="construction">Konstruksi</option>
                                        <option value="logistics">Logistik & Transportasi</option>
                                        <option value="general">Umum</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mt-4">
                                <button
                                    @click="generateContentIdeas"
                                    :disabled="isGeneratingIdeas"
                                    class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 flex items-center"
                                >
                                    <Loader2 v-if="isGeneratingIdeas" class="h-4 w-4 mr-2 animate-spin" />
                                    <Lightbulb v-else class="h-4 w-4 mr-2" />
                                    Generate Ideas
                                </button>
                            </div>
                        </div>

                        <!-- Generated Ideas -->
                        <div v-if="contentIdeas.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div 
                                v-for="idea in contentIdeas" 
                                :key="idea.id"
                                class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                            >
                                <div class="flex items-start justify-between mb-3">
                                    <h4 class="font-medium text-gray-900">{{ idea.title }}</h4>
                                    <span :class="[
                                        'px-2 py-1 text-xs font-medium rounded-full',
                                        getIdeaTypeColor(idea.type)
                                    ]">
                                        {{ idea.type }}
                                    </span>
                                </div>
                                <p class="text-sm text-gray-600 mb-3">{{ idea.description }}</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-500">{{ idea.platform }}</span>
                                    <button
                                        @click="useIdea(idea)"
                                        class="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
                                    >
                                        Use Idea
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Templates Tab -->
                    <div v-if="activeTab === 'templates'" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div 
                                v-for="template in contentTemplates" 
                                :key="template.id"
                                class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                                @click="useTemplate(template)"
                            >
                                <div class="flex items-center mb-3">
                                    <component :is="template.icon" class="h-6 w-6 text-blue-600 mr-3" />
                                    <h4 class="font-medium text-gray-900">{{ template.name }}</h4>
                                </div>
                                <p class="text-sm text-gray-600 mb-3">{{ template.description }}</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-gray-500">{{ template.category }}</span>
                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                                        {{ template.usage }} uses
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import {
    Download,
    Sparkles,
    Loader2,
    Calendar,
    Edit,
    Save,
    Trash2,
    Lightbulb,
    FileText,
    Image,
    Video,
    MessageSquare,
    BarChart3
} from 'lucide-vue-next';
import { ref, computed, onMounted } from 'vue';

// Types
interface ContentPost {
    id: string;
    date: string;
    title: string;
    description: string;
    category: string;
    hashtags?: string[];
    imageDescription?: string;
}

interface MonthlyPlan {
    year: number;
    month: number;
    posts: ContentPost[];
}

interface ContentIdea {
    id: string;
    title: string;
    description: string;
    type: string;
    platform: string;
}

interface ContentTemplate {
    id: string;
    name: string;
    description: string;
    category: string;
    icon: any;
    usage: number;
    template: string;
}

interface PlanConfig {
    month: number;
    year: number;
    postsPerWeek: number;
    selectedCategories: string[];
    includeHolidays: boolean;
}

interface IdeaGenerator {
    topic: string;
    audience: string;
}

// Reactive state
const activeTab = ref('monthly-plan');
const isGenerating = ref(false);
const isGeneratingIdeas = ref(false);
const monthlyPlan = ref<MonthlyPlan | null>(null);
const contentIdeas = ref<ContentIdea[]>([]);

// Configuration
const planConfig = ref<PlanConfig>({
    month: new Date().getMonth(),
    year: new Date().getFullYear(),
    postsPerWeek: 3,
    selectedCategories: ['product', 'educational', 'promotional'],
    includeHolidays: true
});

const ideaGenerator = ref<IdeaGenerator>({
    topic: '',
    audience: 'mining'
});

// Constants
const tabs = [
    { id: 'monthly-plan', name: 'Monthly Plan', icon: Calendar },
    { id: 'content-ideas', name: 'Content Ideas', icon: Lightbulb },
    { id: 'templates', name: 'Templates', icon: FileText }
];

const months = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
];

const years = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() + i);

const contentCategories = [
    { id: 'product', name: 'Product Showcase' },
    { id: 'educational', name: 'Educational' },
    { id: 'promotional', name: 'Promotional' },
    { id: 'industry', name: 'Industry News' },
    { id: 'testimonial', name: 'Testimonial' },
    { id: 'behind_scenes', name: 'Behind the Scenes' },
    { id: 'seasonal', name: 'Seasonal' },
    { id: 'tips', name: 'Tips & Tricks' }
];

const contentTemplates = ref<ContentTemplate[]>([
    {
        id: 'product-showcase',
        name: 'Product Showcase',
        description: 'Template untuk memamerkan produk ban dengan spesifikasi dan keunggulan',
        category: 'Product',
        icon: Image,
        usage: 45,
        template: 'Introducing our latest {product_name}! Perfect for {use_case} with {key_features}. #ChitraTires #QualityTires'
    },
    {
        id: 'educational-post',
        name: 'Educational Post',
        description: 'Template untuk konten edukatif tentang perawatan ban dan tips berkendara',
        category: 'Educational',
        icon: FileText,
        usage: 32,
        template: 'Did you know? {educational_fact} Here are {number} tips to {benefit}: {tips_list} #TireEducation #SafetyFirst'
    },
    {
        id: 'promotional-offer',
        name: 'Promotional Offer',
        description: 'Template untuk promosi dan penawaran khusus',
        category: 'Promotional',
        icon: BarChart3,
        usage: 28,
        template: '🔥 SPECIAL OFFER! Get {discount}% off on {product_category}! Valid until {end_date}. Terms and conditions apply. #SpecialOffer #ChitraDeals'
    },
    {
        id: 'testimonial',
        name: 'Customer Testimonial',
        description: 'Template untuk menampilkan testimoni dan review pelanggan',
        category: 'Social Proof',
        icon: MessageSquare,
        usage: 19,
        template: '⭐ Customer Review: "{testimonial_text}" - {customer_name}, {customer_company}. Thank you for trusting Chitra Tires! #CustomerReview #Testimonial'
    },
    {
        id: 'industry-news',
        name: 'Industry News',
        description: 'Template untuk berbagi berita dan update industri pertambangan',
        category: 'Industry',
        icon: FileText,
        usage: 15,
        template: '📰 Industry Update: {news_headline}. {brief_summary} What does this mean for {target_audience}? {analysis} #IndustryNews #Mining'
    },
    {
        id: 'video-content',
        name: 'Video Content',
        description: 'Template untuk konten video dan multimedia',
        category: 'Video',
        icon: Video,
        usage: 12,
        template: '🎥 Watch our latest video: {video_title}! Learn about {video_topic} in just {duration} minutes. Link in bio! #VideoContent #ChitraEducation'
    }
]);

// Utility functions
const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('id-ID', {
        weekday: 'long',
        day: 'numeric',
        month: 'long'
    });
};

const getCategoryColor = (category: string): string => {
    const colors: Record<string, string> = {
        'product': 'bg-blue-100 text-blue-800',
        'educational': 'bg-green-100 text-green-800',
        'promotional': 'bg-red-100 text-red-800',
        'industry': 'bg-purple-100 text-purple-800',
        'testimonial': 'bg-yellow-100 text-yellow-800',
        'behind_scenes': 'bg-indigo-100 text-indigo-800',
        'seasonal': 'bg-orange-100 text-orange-800',
        'tips': 'bg-teal-100 text-teal-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
};

const getIdeaTypeColor = (type: string): string => {
    const colors: Record<string, string> = {
        'Image Post': 'bg-blue-100 text-blue-800',
        'Video Content': 'bg-red-100 text-red-800',
        'Carousel': 'bg-green-100 text-green-800',
        'Story': 'bg-purple-100 text-purple-800',
        'Reel': 'bg-pink-100 text-pink-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
};

// Main functions
const generateMonthlyPlan = async () => {
    isGenerating.value = true;

    try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        const { year, month, postsPerWeek, selectedCategories, includeHolidays } = planConfig.value;
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        const totalPosts = Math.floor((daysInMonth / 7) * postsPerWeek);

        const posts: ContentPost[] = [];

        // Generate posts for the month
        for (let i = 0; i < totalPosts; i++) {
            const dayOfMonth = Math.floor((i / totalPosts) * daysInMonth) + 1;
            const date = new Date(year, month, dayOfMonth);
            const category = selectedCategories[i % selectedCategories.length];

            const post = generateContentPost(date, category, i);
            posts.push(post);
        }

        // Sort posts by date
        posts.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

        monthlyPlan.value = {
            year,
            month,
            posts
        };

    } catch (error) {
        console.error('Error generating monthly plan:', error);
        alert('Gagal membuat rencana bulanan. Silakan coba lagi.');
    } finally {
        isGenerating.value = false;
    }
};

const generateContentPost = (date: Date, category: string, index: number): ContentPost => {
    const contentTemplates: Record<string, any> = {
        product: {
            titles: [
                'Introducing Our Latest Heavy-Duty Tires',
                'Premium Quality Tires for Mining Operations',
                'Durable Tires Built for Extreme Conditions',
                'Advanced Tire Technology for Maximum Performance'
            ],
            descriptions: [
                'Discover our new line of heavy-duty tires designed specifically for mining and construction vehicles. Built to withstand the toughest conditions.',
                'Experience superior performance with our premium tire collection. Engineered for durability and reliability in demanding environments.',
                'Our latest tire innovation combines cutting-edge technology with proven durability for unmatched performance.',
                'Upgrade your fleet with our professional-grade tires designed for maximum efficiency and safety.'
            ]
        },
        educational: {
            titles: [
                'Tire Maintenance Tips for Heavy Equipment',
                'How to Extend Your Tire Lifespan',
                'Understanding Tire Pressure for Optimal Performance',
                'Seasonal Tire Care Guidelines'
            ],
            descriptions: [
                'Learn essential tire maintenance practices that can significantly extend the life of your heavy equipment tires and improve operational efficiency.',
                'Discover proven strategies to maximize your tire investment through proper care, regular inspections, and preventive maintenance.',
                'Understanding the importance of proper tire pressure and how it affects fuel efficiency, tire wear, and overall vehicle performance.',
                'Comprehensive guide to tire care across different seasons, ensuring optimal performance year-round.'
            ]
        },
        promotional: {
            titles: [
                'Limited Time: 20% Off All Mining Tires',
                'Special Fleet Discount Available Now',
                'End of Season Tire Sale',
                'Bulk Purchase Incentives'
            ],
            descriptions: [
                'Take advantage of our limited-time offer on premium mining tires. Perfect opportunity to upgrade your fleet at unbeatable prices.',
                'Exclusive discounts for fleet operators. Contact our sales team to learn about volume pricing and special terms.',
                'Clear out inventory with significant savings on select tire models. While supplies last.',
                'Special pricing for bulk orders. The more you buy, the more you save. Contact us for custom quotes.'
            ]
        },
        industry: {
            titles: [
                'Mining Industry Trends and Insights',
                'New Regulations Affecting Fleet Operations',
                'Technology Advances in Heavy Equipment',
                'Market Analysis: Mining Sector Outlook'
            ],
            descriptions: [
                'Stay informed about the latest trends shaping the mining industry and how they impact equipment and tire requirements.',
                'Important updates on new regulations that may affect your fleet operations and compliance requirements.',
                'Explore the latest technological advances in heavy equipment and their implications for tire performance and selection.',
                'Comprehensive analysis of current market conditions and future outlook for the mining sector.'
            ]
        }
    };

    const template = contentTemplates[category] || contentTemplates.product;
    const titleIndex = index % template.titles.length;
    const descIndex = index % template.descriptions.length;

    return {
        id: `post-${date.getTime()}-${index}`,
        date: date.toISOString().split('T')[0],
        title: template.titles[titleIndex],
        description: template.descriptions[descIndex],
        category,
        hashtags: generateHashtags(category),
        imageDescription: generateImageDescription(category)
    };
};

const generateHashtags = (category: string): string[] => {
    const baseHashtags = ['ChitraTires', 'QualityTires', 'HeavyEquipment'];
    const categoryHashtags: Record<string, string[]> = {
        product: ['ProductShowcase', 'NewProduct', 'TireInnovation'],
        educational: ['TireEducation', 'MaintenanceTips', 'SafetyFirst'],
        promotional: ['SpecialOffer', 'LimitedTime', 'FleetDiscount'],
        industry: ['IndustryNews', 'Mining', 'TechUpdate']
    };

    return [...baseHashtags, ...(categoryHashtags[category] || [])];
};

const generateImageDescription = (category: string): string => {
    const descriptions: Record<string, string[]> = {
        product: [
            'High-quality tire product shot with technical specifications overlay',
            'Heavy equipment tire in action at mining site',
            'Close-up of tire tread pattern showing durability features'
        ],
        educational: [
            'Infographic showing tire maintenance steps',
            'Before and after comparison of proper tire care',
            'Technical diagram explaining tire components'
        ],
        promotional: [
            'Eye-catching promotional banner with discount information',
            'Fleet of vehicles showcasing tire products',
            'Limited time offer graphic with pricing details'
        ],
        industry: [
            'Mining site overview showing equipment in operation',
            'Industry statistics and trend charts',
            'Professional meeting or conference setting'
        ]
    };

    const categoryDescriptions = descriptions[category] || descriptions.product;
    return categoryDescriptions[Math.floor(Math.random() * categoryDescriptions.length)];
};

const generateContentIdeas = async () => {
    isGeneratingIdeas.value = true;

    try {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));

        const { topic, audience } = ideaGenerator.value;

        // Generate ideas based on topic and audience
        const ideas: ContentIdea[] = [
            {
                id: 'idea-1',
                title: `${topic} - Product Comparison`,
                description: `Compare different tire options for ${topic} scenarios, highlighting key features and benefits for ${audience} sector.`,
                type: 'Carousel',
                platform: 'Instagram'
            },
            {
                id: 'idea-2',
                title: `Expert Tips: ${topic}`,
                description: `Share professional insights and best practices related to ${topic} from industry experts.`,
                type: 'Video Content',
                platform: 'YouTube'
            },
            {
                id: 'idea-3',
                title: `Case Study: ${topic} Success Story`,
                description: `Showcase a real customer success story featuring ${topic} and its impact on their operations.`,
                type: 'Image Post',
                platform: 'LinkedIn'
            },
            {
                id: 'idea-4',
                title: `Quick Facts: ${topic}`,
                description: `Share interesting statistics and facts about ${topic} that ${audience} professionals should know.`,
                type: 'Story',
                platform: 'Instagram'
            },
            {
                id: 'idea-5',
                title: `Behind the Scenes: ${topic} Testing`,
                description: `Show the rigorous testing process for products related to ${topic}, building trust and credibility.`,
                type: 'Reel',
                platform: 'Instagram'
            }
        ];

        contentIdeas.value = ideas;

    } catch (error) {
        console.error('Error generating content ideas:', error);
        alert('Gagal membuat ide konten. Silakan coba lagi.');
    } finally {
        isGeneratingIdeas.value = false;
    }
};

// Event handlers
const editPost = (post: ContentPost) => {
    alert(`Edit post: ${post.title} (belum diimplementasi)`);
};

const savePost = (post: ContentPost) => {
    alert(`Save post to calendar: ${post.title} (belum diimplementasi)`);
};

const deletePost = (postId: string) => {
    if (monthlyPlan.value) {
        monthlyPlan.value.posts = monthlyPlan.value.posts.filter(p => p.id !== postId);
    }
};

const useIdea = (idea: ContentIdea) => {
    alert(`Using idea: ${idea.title} (belum diimplementasi)`);
};

const useTemplate = (template: ContentTemplate) => {
    alert(`Using template: ${template.name} (belum diimplementasi)`);
};

const exportPlan = () => {
    if (!monthlyPlan.value) {
        alert('Tidak ada rencana untuk diekspor. Silakan buat rencana terlebih dahulu.');
        return;
    }

    try {
        const csvContent = [
            // Header
            ['Date', 'Title', 'Description', 'Category', 'Hashtags', 'Image Description'].join(','),
            // Data
            ...monthlyPlan.value.posts.map(post => [
                post.date,
                `"${post.title}"`,
                `"${post.description}"`,
                post.category,
                `"${post.hashtags?.join(' ') || ''}"`,
                `"${post.imageDescription || ''}"`
            ].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `content_plan_${months[planConfig.value.month]}_${planConfig.value.year}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Rencana konten berhasil diekspor ke CSV.');

    } catch (error) {
        console.error('Error exporting plan:', error);
        alert('Gagal mengekspor rencana konten.');
    }
};

// Initialize on mount
onMounted(() => {
    // Load any saved data or initialize defaults
});
</script>
