import React, { useState, useEffect } from 'react';
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Info,
  AlertTriangle,
  CheckCircle,
  Clock,
  ThumbsUp,
  ThumbsDown,
  Filter,
  Settings,
  Plus,
  Calendar as CalendarIcon
} from 'lucide-react';
import {
  CalendarMonth,
  CalendarDay,
  RecommendationLevel,
  ScheduledPromotion,
  CalendarSettings
} from '../types/seasonalMarketing';
import {
  generateCalendarMonth,
  getScheduledPromotions,
  getCalendarSettings
} from '../services/seasonalMarketingService';

interface MarketingCalendarProps {
  onDayClick?: (day: CalendarDay) => void;
  onPromotionClick?: (promotion: ScheduledPromotion) => void;
  onAddPromotion?: (date: string) => void;
}

export default function MarketingCalendar({
  onDayClick,
  onPromotionClick,
  onAddPromotion
}: MarketingCalendarProps) {
  // State for current date
  const [currentDate, setCurrentDate] = useState(new Date());
  const [calendarData, setCalendarData] = useState<CalendarMonth | null>(null);
  const [promotions, setPromotions] = useState<ScheduledPromotion[]>([]);
  const [settings, setSettings] = useState<CalendarSettings | null>(null);
  const [loading, setLoading] = useState(true);

  // Load calendar data for the current month
  useEffect(() => {
    const loadCalendarData = async () => {
      setLoading(true);
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1; // JavaScript months are 0-indexed

      try {
        // Generate calendar data (now async)
        const data = await generateCalendarMonth(year, month);
        setCalendarData(data);

        // Load scheduled promotions
        const allPromotions = getScheduledPromotions();
        setPromotions(allPromotions);

        // Load settings
        const calendarSettings = getCalendarSettings();
        setSettings(calendarSettings);
      } catch (error) {
        console.error('Error loading calendar data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCalendarData();
  }, [currentDate]);

  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  // Navigate to current month
  const goToCurrentMonth = () => {
    setCurrentDate(new Date());
  };

  // Get color for recommendation level
  const getRecommendationColor = (level: RecommendationLevel): string => {
    switch (level) {
      case RecommendationLevel.EXCELLENT:
        return 'bg-green-100 border-green-500 text-green-800';
      case RecommendationLevel.GOOD:
        return 'bg-blue-100 border-blue-500 text-blue-800';
      case RecommendationLevel.NEUTRAL:
        return 'bg-gray-100 border-gray-500 text-gray-800';
      case RecommendationLevel.POOR:
        return 'bg-yellow-100 border-yellow-500 text-yellow-800';
      case RecommendationLevel.AVOID:
        return 'bg-red-100 border-red-500 text-red-800';
      default:
        return 'bg-gray-100 border-gray-500 text-gray-800';
    }
  };

  // Get icon for recommendation level
  const getRecommendationIcon = (level: RecommendationLevel) => {
    switch (level) {
      case RecommendationLevel.EXCELLENT:
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case RecommendationLevel.GOOD:
        return <ThumbsUp className="h-4 w-4 text-blue-600" />;
      case RecommendationLevel.NEUTRAL:
        return <Clock className="h-4 w-4 text-gray-600" />;
      case RecommendationLevel.POOR:
        return <ThumbsDown className="h-4 w-4 text-yellow-600" />;
      case RecommendationLevel.AVOID:
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Info className="h-4 w-4 text-gray-600" />;
    }
  };

  // Check if a day has scheduled promotions
  const getDayPromotions = (dateString: string): ScheduledPromotion[] => {
    return promotions.filter(promo => {
      const promoStart = new Date(promo.startDate);
      const promoEnd = new Date(promo.endDate);
      const day = new Date(dateString);

      return day >= promoStart && day <= promoEnd;
    });
  };

  // Render day cell
  const renderDay = (day: CalendarDay) => {
    const dayPromotions = getDayPromotions(day.date);
    const dayDate = new Date(day.date);
    const isWeekend = dayDate.getDay() === 0 || dayDate.getDay() === 6;
    const isToday = new Date().toISOString().split('T')[0] === day.date;
    const hasHolidays = day.holidays && day.holidays.length > 0;

    // Find holiday factors
    const holidayFactors = day.factors.filter(factor => factor.isHoliday);

    return (
      <div
        key={day.date}
        className={`relative p-2 border-t ${
          getRecommendationColor(day.recommendationLevel)
        } ${isWeekend ? 'opacity-80' : ''} ${
          isToday ? 'ring-2 ring-blue-500' : ''
        } ${hasHolidays ? 'border-l-4 border-l-red-500' : ''}
        min-h-[80px] cursor-pointer transition-all hover:shadow-md`}
        onClick={() => onDayClick && onDayClick(day)}
      >
        <div className="flex justify-between items-start">
          <span className={`text-sm font-medium ${isToday ? 'text-blue-700' : ''} ${hasHolidays ? 'text-red-700 font-bold' : ''}`}>
            {dayDate.getDate()}
          </span>
          <div className="flex items-center">
            {getRecommendationIcon(day.recommendationLevel)}
            <span className="ml-1 text-xs">{day.score.toFixed(0)}</span>
          </div>
        </div>

        {/* Holidays */}
        {hasHolidays && (
          <div className="mt-1 space-y-1">
            {day.holidays.slice(0, 1).map(holiday => (
              <div
                key={holiday.id}
                className="text-xs p-1 rounded truncate bg-red-200 text-red-800 border border-red-300"
                title={holiday.description}
              >
                {holiday.name}
              </div>
            ))}
            {day.holidays.length > 1 && (
              <div className="text-xs text-red-600 font-medium">
                +{day.holidays.length - 1} hari libur lainnya
              </div>
            )}
          </div>
        )}

        {/* Promotions */}
        <div className={`${hasHolidays ? 'mt-1' : 'mt-2'} space-y-1`}>
          {dayPromotions.slice(0, hasHolidays ? 1 : 2).map(promo => (
            <div
              key={promo.id}
              className={`text-xs p-1 rounded truncate ${
                promo.color ? `bg-${promo.color}-200 text-${promo.color}-800` : 'bg-purple-200 text-purple-800'
              }`}
              onClick={(e) => {
                e.stopPropagation();
                onPromotionClick && onPromotionClick(promo);
              }}
            >
              {promo.name}
            </div>
          ))}
          {dayPromotions.length > (hasHolidays ? 1 : 2) && (
            <div className="text-xs text-gray-600">
              +{dayPromotions.length - (hasHolidays ? 1 : 2)} more
            </div>
          )}
        </div>

        {/* Add promotion button */}
        <button
          className="absolute bottom-1 right-1 p-1 text-gray-500 hover:text-blue-600 opacity-0 hover:opacity-100 transition-opacity"
          onClick={(e) => {
            e.stopPropagation();
            onAddPromotion && onAddPromotion(day.date);
          }}
        >
          <Plus className="h-4 w-4" />
        </button>
      </div>
    );
  };

  // Render weekday headers
  const renderWeekdays = () => {
    const weekdays = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

    return (
      <div className="grid grid-cols-7 text-center bg-gray-100">
        {weekdays.map((day, index) => (
          <div key={index} className="py-2 text-sm font-medium text-gray-700">
            {day}
          </div>
        ))}
      </div>
    );
  };

  // Render calendar grid
  const renderCalendarGrid = () => {
    if (!calendarData) return null;

    const { year, month, days } = calendarData;

    // Get the day of the week for the first day of the month (0 = Sunday, 1 = Monday, etc.)
    const firstDayOfMonth = new Date(year, month - 1, 1).getDay();

    // Create an array with empty cells for days before the first day of the month
    const emptyCells = Array(firstDayOfMonth).fill(null);

    return (
      <div className="grid grid-cols-7 border-l border-r border-b">
        {/* Empty cells for days before the first day of the month */}
        {emptyCells.map((_, index) => (
          <div key={`empty-${index}`} className="p-2 border-t bg-gray-50 min-h-[80px]"></div>
        ))}

        {/* Calendar days */}
        {days.map(day => renderDay(day))}
      </div>
    );
  };

  // Render month summary
  const renderMonthSummary = () => {
    if (!calendarData) return null;

    const { averageScore, topFactors, holidays } = calendarData;
    const recommendationLevel = averageScore >= 80 ? 'Sangat Baik' :
                               averageScore >= 60 ? 'Baik' :
                               averageScore >= 40 ? 'Netral' :
                               averageScore >= 20 ? 'Kurang Baik' :
                               'Hindari';

    // Count national holidays
    const nationalHolidays = holidays?.filter(h => h.type === 'Nasional') || [];
    const religiousHolidays = holidays?.filter(h => h.type === 'Keagamaan') || [];
    const totalHolidays = holidays?.length || 0;

    return (
      <div className="mt-4 bg-white p-4 rounded-lg shadow-sm border">
        <h3 className="text-lg font-medium text-gray-900">Ringkasan Bulan</h3>
        <div className="mt-2 grid grid-cols-3 gap-4">
          <div>
            <p className="text-sm text-gray-500">Skor Rata-rata</p>
            <p className="text-2xl font-bold">{averageScore.toFixed(1)}</p>
            <p className="text-sm font-medium text-blue-600">{recommendationLevel}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Faktor Utama</p>
            <ul className="mt-1 text-sm">
              {topFactors.map(factor => (
                <li key={factor.id} className="flex items-center">
                  <span className={factor.impact > 0 ? 'text-green-600' : 'text-red-600'}>
                    {factor.impact > 0 ? '+' : ''}{factor.impact}
                  </span>
                  <span className="ml-2">{factor.name}</span>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <p className="text-sm text-gray-500">Hari Libur</p>
            <p className="text-2xl font-bold">{totalHolidays}</p>
            <div className="text-sm">
              <p><span className="font-medium text-red-600">{nationalHolidays.length}</span> Libur Nasional</p>
              <p><span className="font-medium text-green-600">{religiousHolidays.length}</span> Libur Keagamaan</p>
              <p><span className="font-medium text-blue-600">{totalHolidays - nationalHolidays.length - religiousHolidays.length}</span> Lainnya</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Calendar header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Calendar className="h-5 w-5 text-blue-600" />
          <h2 className="text-xl font-bold text-gray-900">
            Kalender Pemasaran Musiman
          </h2>
        </div>

        <div className="flex items-center space-x-2">
          <button
            className="p-2 rounded-full hover:bg-gray-100"
            onClick={goToPreviousMonth}
          >
            <ChevronLeft className="h-5 w-5 text-gray-600" />
          </button>

          <button
            className="px-3 py-1 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded"
            onClick={goToCurrentMonth}
          >
            Bulan Ini
          </button>

          <span className="text-lg font-medium text-gray-900">
            {currentDate.toLocaleDateString('id-ID', { month: 'long', year: 'numeric' })}
          </span>

          <button
            className="p-2 rounded-full hover:bg-gray-100"
            onClick={goToNextMonth}
          >
            <ChevronRight className="h-5 w-5 text-gray-600" />
          </button>

          <button className="p-2 rounded-full hover:bg-gray-100">
            <Filter className="h-5 w-5 text-gray-600" />
          </button>

          <button className="p-2 rounded-full hover:bg-gray-100">
            <Settings className="h-5 w-5 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Legend */}
      <div className="flex flex-wrap items-center gap-4 text-sm">
        <div className="flex items-center">
          <div className="w-3 h-3 bg-green-100 border border-green-500 rounded-sm mr-1"></div>
          <span>Sangat Baik</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-blue-100 border border-blue-500 rounded-sm mr-1"></div>
          <span>Baik</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-gray-100 border border-gray-500 rounded-sm mr-1"></div>
          <span>Netral</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-yellow-100 border border-yellow-500 rounded-sm mr-1"></div>
          <span>Kurang Baik</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 bg-red-100 border border-red-500 rounded-sm mr-1"></div>
          <span>Hindari</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 border-l-4 border-l-red-500 rounded-sm mr-1"></div>
          <span>Hari Libur</span>
        </div>
      </div>

      {/* Calendar */}
      <div className="bg-white rounded-lg shadow-sm border">
        {loading ? (
          <div className="flex justify-center items-center h-96">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {renderWeekdays()}
            {renderCalendarGrid()}
          </>
        )}
      </div>

      {/* Month summary */}
      {!loading && renderMonthSummary()}
    </div>
  );
}
