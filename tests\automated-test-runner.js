/**
 * Automated Test Runner for Chitra Marketing Tools
 * This script performs comprehensive testing of all features
 */

class TestRunner {
    constructor() {
        this.results = {
            passed: 0,
            failed: 0,
            tests: []
        };
        this.baseUrl = 'http://localhost:8000';
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🚀 Starting Comprehensive Test Suite...\n');
        
        try {
            await this.testNavigation();
            await this.testCalculators();
            await this.testDataManagement();
            await this.testPerformance();
            await this.testAccessibility();
            
            this.printResults();
        } catch (error) {
            console.error('❌ Test suite failed:', error);
        }
    }

    /**
     * Test navigation and routing
     */
    async testNavigation() {
        console.log('📍 Testing Navigation & Routing...');
        
        const routes = [
            '/',
            '/ban27-bundling',
            '/bundling-qty',
            '/zero-margin-bundling',
            '/promo-simulation',
            '/analytics',
            '/product-management',
            '/customer-management'
        ];

        for (const route of routes) {
            await this.testRoute(route);
        }
    }

    /**
     * Test individual route
     */
    async testRoute(route) {
        try {
            const response = await fetch(`${this.baseUrl}${route}`);
            const success = response.status === 200;
            
            this.recordTest(`Route ${route}`, success, 
                success ? 'Route accessible' : `HTTP ${response.status}`);
        } catch (error) {
            this.recordTest(`Route ${route}`, false, error.message);
        }
    }

    /**
     * Test calculator functionality
     */
    async testCalculators() {
        console.log('🧮 Testing Calculator Suite...');
        
        // Test calculator pages load
        const calculators = [
            'ban27-bundling',
            'bundling-qty', 
            'zero-margin-bundling',
            'promo-simulation'
        ];

        for (const calc of calculators) {
            await this.testCalculatorPage(calc);
        }
    }

    /**
     * Test individual calculator page
     */
    async testCalculatorPage(calculator) {
        try {
            const response = await fetch(`${this.baseUrl}/${calculator}`);
            const html = await response.text();
            
            // Check for essential elements
            const hasForm = html.includes('form') || html.includes('input');
            const hasCalculateButton = html.includes('Calculate') || html.includes('Hitung');
            const hasResults = html.includes('result') || html.includes('Result');
            
            const success = response.status === 200 && hasForm;
            
            this.recordTest(`Calculator ${calculator}`, success,
                success ? 'Calculator page loaded with form elements' : 'Missing essential elements');
        } catch (error) {
            this.recordTest(`Calculator ${calculator}`, false, error.message);
        }
    }

    /**
     * Test data management functionality
     */
    async testDataManagement() {
        console.log('📊 Testing Data Management...');
        
        const dataPages = [
            'product-management',
            'customer-management'
        ];

        for (const page of dataPages) {
            await this.testDataPage(page);
        }
    }

    /**
     * Test individual data management page
     */
    async testDataPage(page) {
        try {
            const response = await fetch(`${this.baseUrl}/${page}`);
            const html = await response.text();
            
            // Check for CRUD elements
            const hasTable = html.includes('table') || html.includes('tbody');
            const hasAddButton = html.includes('Tambah') || html.includes('Add');
            const hasSearch = html.includes('search') || html.includes('Search');
            
            const success = response.status === 200 && hasTable && hasAddButton;
            
            this.recordTest(`Data Management ${page}`, success,
                success ? 'Data management page loaded with CRUD elements' : 'Missing CRUD elements');
        } catch (error) {
            this.recordTest(`Data Management ${page}`, false, error.message);
        }
    }

    /**
     * Test performance metrics
     */
    async testPerformance() {
        console.log('⚡ Testing Performance...');
        
        const testUrls = [
            '/',
            '/analytics',
            '/product-management'
        ];

        for (const url of testUrls) {
            await this.testPagePerformance(url);
        }
    }

    /**
     * Test individual page performance
     */
    async testPagePerformance(url) {
        try {
            const startTime = Date.now();
            const response = await fetch(`${this.baseUrl}${url}`);
            const endTime = Date.now();
            
            const loadTime = endTime - startTime;
            const success = response.status === 200 && loadTime < 3000; // 3 second threshold
            
            this.recordTest(`Performance ${url}`, success,
                `Load time: ${loadTime}ms ${success ? '(Good)' : '(Slow)'}`);
        } catch (error) {
            this.recordTest(`Performance ${url}`, false, error.message);
        }
    }

    /**
     * Test accessibility features
     */
    async testAccessibility() {
        console.log('♿ Testing Accessibility...');
        
        try {
            const response = await fetch(`${this.baseUrl}/`);
            const html = await response.text();
            
            // Basic accessibility checks
            const hasLang = html.includes('lang=');
            const hasTitle = html.includes('<title>');
            const hasMetaViewport = html.includes('viewport');
            const hasSkipLink = html.includes('skip') || html.includes('Skip');
            
            this.recordTest('Accessibility - Language', hasLang, 
                hasLang ? 'Language attribute found' : 'Missing lang attribute');
            
            this.recordTest('Accessibility - Title', hasTitle,
                hasTitle ? 'Page title found' : 'Missing page title');
            
            this.recordTest('Accessibility - Viewport', hasMetaViewport,
                hasMetaViewport ? 'Viewport meta tag found' : 'Missing viewport meta tag');
                
        } catch (error) {
            this.recordTest('Accessibility Tests', false, error.message);
        }
    }

    /**
     * Record test result
     */
    recordTest(testName, passed, message) {
        const result = {
            name: testName,
            passed,
            message,
            timestamp: new Date().toISOString()
        };
        
        this.results.tests.push(result);
        
        if (passed) {
            this.results.passed++;
            console.log(`  ✅ ${testName}: ${message}`);
        } else {
            this.results.failed++;
            console.log(`  ❌ ${testName}: ${message}`);
        }
    }

    /**
     * Print final test results
     */
    printResults() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 TEST RESULTS SUMMARY');
        console.log('='.repeat(60));
        console.log(`Total Tests: ${this.results.tests.length}`);
        console.log(`✅ Passed: ${this.results.passed}`);
        console.log(`❌ Failed: ${this.results.failed}`);
        console.log(`Success Rate: ${((this.results.passed / this.results.tests.length) * 100).toFixed(1)}%`);
        
        if (this.results.failed > 0) {
            console.log('\n❌ FAILED TESTS:');
            this.results.tests
                .filter(test => !test.passed)
                .forEach(test => {
                    console.log(`  • ${test.name}: ${test.message}`);
                });
        }
        
        console.log('\n🎉 Test suite completed!');
        
        // Generate test report
        this.generateReport();
    }

    /**
     * Generate detailed test report
     */
    generateReport() {
        const report = {
            summary: {
                totalTests: this.results.tests.length,
                passed: this.results.passed,
                failed: this.results.failed,
                successRate: ((this.results.passed / this.results.tests.length) * 100).toFixed(1),
                timestamp: new Date().toISOString()
            },
            tests: this.results.tests,
            recommendations: this.generateRecommendations()
        };

        console.log('\n📄 Detailed report generated');
        console.log('Report data:', JSON.stringify(report, null, 2));
    }

    /**
     * Generate recommendations based on test results
     */
    generateRecommendations() {
        const recommendations = [];
        
        const failedTests = this.results.tests.filter(test => !test.passed);
        
        if (failedTests.some(test => test.name.includes('Performance'))) {
            recommendations.push('Consider implementing lazy loading for better performance');
            recommendations.push('Optimize bundle sizes and enable compression');
        }
        
        if (failedTests.some(test => test.name.includes('Accessibility'))) {
            recommendations.push('Improve accessibility by adding missing ARIA attributes');
            recommendations.push('Ensure proper heading hierarchy and alt text for images');
        }
        
        if (failedTests.some(test => test.name.includes('Route'))) {
            recommendations.push('Check server configuration and route definitions');
            recommendations.push('Ensure all routes are properly registered');
        }
        
        return recommendations;
    }
}

// Run tests if this script is executed directly
if (typeof window === 'undefined') {
    const testRunner = new TestRunner();
    testRunner.runAllTests();
}

// Export for use in browser
if (typeof window !== 'undefined') {
    window.TestRunner = TestRunner;
}
