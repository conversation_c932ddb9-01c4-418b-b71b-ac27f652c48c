# Chitra Marketing Tools

Marketing tools for PT Chitra Paratama.

## Getting Started

To run the development server:
```
npm install
npm run dev
```

To run with proxy server:
```
npm run dev:with-proxy
```

To run with negotiation server:
```
npm run dev:with-negotiation
```

To run with both proxy and negotiation servers:
```
npm run dev:full
```

## Electron App

To run the Electron app in development mode:
```
npm run electron:dev
```

To build a portable Electron app:
```
npm run electron:portable
```

## Context7 MCP Integration

This project is integrated with [Context7 MCP](https://github.com/upstash/context7), a powerful tool that provides up-to-date code documentation for LLMs and AI code editors.

### What is Context7?

Context7 MCP pulls up-to-date, version-specific documentation and code examples straight from the source and places them directly into your prompt. This helps solve common issues with LLMs:

- ✅ Get up-to-date code examples instead of outdated training data
- ✅ Access accurate APIs that actually exist
- ✅ Receive version-specific answers for your packages

### How to Use Context7

Simply add `use context7` to your prompt when asking for code-related assistance. For example:

```
Create a React component that fetches data from an API and displays it in a table. use context7
```

```
Help me implement a PostgreSQL query to find duplicate records. use context7
```

### Testing Context7

To test if Context7 is working correctly:
```
npm run context7:test
```

### Requirements

- Node.js v18.0.0 or higher is required to use Context7
