import React, { useState, useEffect } from 'react';
import { <PERSON>, Co<PERSON>, <PERSON>rkles, FileText, Mail } from 'lucide-react';
import { BundleItem, PricingResult, Customer } from '../types';
import { formatCurrency } from '../utils/pricing';
import { generateCopywriting as generateGeminiCopywriting } from '../services/aiService';
import { generateCopywriting as generateOpenRouterCopywriting, MODELS } from '../services/openRouter';
import { generateQuotationPDF } from '../services/pdfService';
import { generateSimpleQuotationPDF } from '../services/simplePdfService';
import { generateProfessionalQuotationPDF } from '../services/professionalPdfService';
import { generateEmailOffer } from '../services/emailService';
import { getCustomerById } from '../services/customerService';

interface PricingResultsProps {
  bundleItems: BundleItem[];
  pricingResult: PricingResult;
  additionalInfo: string;
  customerId?: string;
  config?: any; // We'll use this to pass all the config data
}

export default function PricingResults({ bundleItems, pricingResult, additionalInfo, customerId, config }: PricingResultsProps) {
  const [copywriting, setCopywriting] = useState<string>('');
  const [emailOffer, setEmailOffer] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingEmail, setIsGeneratingEmail] = useState(false);
  const [copied, setCopied] = useState(false);
  const [emailCopied, setEmailCopied] = useState(false);
  const [selectedModel, setSelectedModel] = useState<'deepseek' | 'openrouter-gemini'>('openrouter-gemini');
  const [selectedEmailModel, setSelectedEmailModel] = useState<'gemini-flash-lite' | 'deepseek' | 'openrouter-gemini'>('gemini-flash-lite');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [emailErrorMessage, setEmailErrorMessage] = useState<string | null>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  // Load customer data when customerId changes
  useEffect(() => {
    if (customerId) {
      const customer = getCustomerById(customerId);
      setSelectedCustomer(customer || null);
    } else {
      setSelectedCustomer(null);
    }
  }, [customerId]);

  const handleGenerateCopywriting = async () => {
    setIsGenerating(true);

    const productDetails = bundleItems
      .map(item => `${item.quantity}x ${item.product.materialDescription}`)
      .join(', ');

    // Add customer information if available
    const customerInfo = selectedCustomer
      ? `Customer: ${selectedCustomer.name} (${selectedCustomer.company || 'No Company'})`
      : '';

    let result = '';
    let errorOccurred = false;

    // Clear any previous error message
    setErrorMessage(null);

    try {
      if (selectedModel === 'deepseek') {
        // Use DeepSeek model via OpenRouter
        console.log('Using DeepSeek Chat API');
        result = await generateOpenRouterCopywriting(
          productDetails,
          pricingResult.recommendedPrice,
          `${additionalInfo}${customerInfo ? '\n' + customerInfo : ''}`,
          MODELS.DEEPSEEK
        );

        // Check if there was an error
        if (result.includes('Failed to generate copywriting')) {
          errorOccurred = true;
          setErrorMessage('DeepSeek API failed. Please try again or try the GPT-3.5 model.');
        }
      } else {
        // Use OpenRouter with GPT-3.5
        console.log('Using OpenRouter with GPT-3.5');
        result = await generateOpenRouterCopywriting(
          productDetails,
          pricingResult.recommendedPrice,
          `${additionalInfo}${customerInfo ? '\n' + customerInfo : ''}`,
          MODELS.GPT_3_5
        );

        // If we get an error message back, try the fallback
        if (result.includes('Failed to generate copywriting')) {
          console.log('OpenRouter failed, falling back to DeepSeek API');
          errorOccurred = true;

          // Set an error message but still try the fallback
          setErrorMessage('GPT-3.5 failed. Trying DeepSeek as fallback...');

          // Try the fallback with DeepSeek
          const fallbackResult = await generateOpenRouterCopywriting(
            productDetails,
            pricingResult.recommendedPrice,
            `${additionalInfo}${customerInfo ? '\n' + customerInfo : ''}`,
            MODELS.DEEPSEEK
          );

          // If fallback succeeded, use that result and switch the model
          if (!fallbackResult.includes('Failed to generate copywriting')) {
            result = fallbackResult;
            setSelectedModel('deepseek');
            setErrorMessage('Switched to DeepSeek because GPT-3.5 was unavailable.');
          } else {
            // Both models failed
            setErrorMessage('Both AI models failed. Please try again later.');
          }
        }
      }
    } catch (error) {
      console.error('Error in handleGenerateCopywriting:', error);
      result = 'Failed to generate copywriting. Please try again or select a different model.';
      errorOccurred = true;
      setErrorMessage('An unexpected error occurred. Please try again.');
    }

    setCopywriting(result);
    setIsGenerating(false);
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(copywriting);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleCopyEmailToClipboard = () => {
    navigator.clipboard.writeText(emailOffer);
    setEmailCopied(true);
    setTimeout(() => setEmailCopied(false), 2000);
  };

  const handleGenerateEmailOffer = async () => {
    setIsGeneratingEmail(true);
    setEmailErrorMessage(null);

    const productDetails = bundleItems
      .map(item => `${item.quantity}x ${item.product.materialDescription}`)
      .join(', ');

    // Add customer information if available
    const customerInfo = selectedCustomer
      ? `Customer: ${selectedCustomer.name}, Company: ${selectedCustomer.company || 'No Company'}, Email: ${selectedCustomer.email}, Phone: ${selectedCustomer.phone}`
      : '';

    let result = '';

    try {
      // Use the appropriate API based on the selected model
      result = await generateEmailOffer(
        productDetails,
        pricingResult.recommendedPrice,
        additionalInfo,
        customerInfo,
        selectedEmailModel // Pass the selected model directly
      );

      // Check if there was an error
      if (result.includes('Failed to generate email offer')) {
        setEmailErrorMessage('Failed to generate email offer. Trying fallback model...');

        // Determine fallback model
        let fallbackModel = 'openrouter-gemini';
        if (selectedEmailModel === 'openrouter-gemini') {
          fallbackModel = 'deepseek';
        } else if (selectedEmailModel === 'deepseek') {
          fallbackModel = 'gemini-flash-lite';
        }

        // Try the fallback model
        const fallbackResult = await generateEmailOffer(
          productDetails,
          pricingResult.recommendedPrice,
          additionalInfo,
          customerInfo,
          fallbackModel
        );

        if (!fallbackResult.includes('Failed to generate email offer')) {
          result = fallbackResult;
          setSelectedEmailModel(fallbackModel as any);
          setEmailErrorMessage(`Switched to ${fallbackModel} because the primary model was unavailable.`);
        } else {
          setEmailErrorMessage('All AI models failed. Please try again later.');
        }
      }
    } catch (error) {
      console.error('Error in handleGenerateEmailOffer:', error);
      result = 'Failed to generate email offer. Please try again or select a different model.';
      setEmailErrorMessage('An unexpected error occurred. Please try again.');
    }

    setEmailOffer(result);
    setIsGeneratingEmail(false);
  };

  const handleGeneratePDF = () => {
    if (bundleItems.length > 0 && pricingResult) {
      try {
        console.log('Generating PDF...');

        // Create additional info with customer details if available
        const pdfAdditionalInfo = selectedCustomer
          ? `${additionalInfo}\n\nCustomer: ${selectedCustomer.name}\nCompany: ${selectedCustomer.company || ''}\nEmail: ${selectedCustomer.email}\nPhone: ${selectedCustomer.phone}`
          : additionalInfo;

        // Use the professional PDF with reliable layout
        generateProfessionalQuotationPDF(
          bundleItems,
          pricingResult,
          config?.note || additionalInfo, // Use note field if available, otherwise use additionalInfo
          selectedCustomer,
          {
            quoDate: config?.quoDate || new Date().toISOString().split('T')[0],
            validityQuote: config?.validityQuote || '',
            from: config?.from || 'Sales Team',
            termsAndCondition: config?.termsAndCondition || ''
          }
        );

        // If you want to try the other PDF versions, uncomment these lines
        // generateSimpleQuotationPDF(bundleItems, pricingResult, pdfAdditionalInfo, selectedCustomer);
        // generateQuotationPDF(bundleItems, pricingResult, pdfAdditionalInfo, selectedCustomer);
      } catch (error) {
        console.error('Error in handleGeneratePDF:', error);
        alert('Failed to generate PDF. Please check the console for details.');
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Pricing Results</h2>

          <button
            onClick={handleGeneratePDF}
            disabled={bundleItems.length === 0}
            className={`flex items-center px-4 py-2 rounded-md text-white ${
              bundleItems.length === 0
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-green-600 hover:bg-green-700'
            }`}
            title="Generate PDF Quotation"
          >
            <FileText size={16} className="mr-2" />
            Generate Professional Quotation
          </button>
        </div>

        {bundleItems.some((item) => (item as any).editedPrice !== undefined && (item as any).editedPrice !== item.product.price) && (
          <div className="mb-4 p-2 bg-yellow-50 border border-yellow-200 rounded-md text-sm text-yellow-800">
            Note: Some product prices have been customized for this bundle.
          </div>
        )}

        {selectedCustomer && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <h3 className="text-sm font-medium text-blue-800 mb-1">Customer Information</h3>
            <div className="text-sm text-blue-700">
              <p><span className="font-medium">Name:</span> {selectedCustomer.name}</p>
              {selectedCustomer.company && <p><span className="font-medium">Company:</span> {selectedCustomer.company}</p>}
              <p><span className="font-medium">Contact:</span> {selectedCustomer.email} | {selectedCustomer.phone}</p>
            </div>
          </div>
        )}

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Total Cost</h3>
              <p className="text-lg font-semibold">Rp {pricingResult.totalCost.toLocaleString()}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500">Profit</h3>
              <p className="text-lg font-semibold">Rp {pricingResult.profit.toLocaleString()}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500">Profit Margin</h3>
              <p className="text-lg font-semibold">{pricingResult.profitMargin.toFixed(2)}%</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500">Recommended Price</h3>
              <p className="text-xl font-bold text-green-600">Rp {pricingResult.recommendedPrice.toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">WhatsApp Copywriting</h2>

          <button
            onClick={handleGenerateCopywriting}
            disabled={isGenerating || bundleItems.length === 0}
            className={`px-4 py-2 rounded-md text-white ${
              isGenerating || bundleItems.length === 0
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {isGenerating ? 'Generating...' : 'Generate Copy'}
          </button>
        </div>

        <div className="mb-4">
          <div className="flex items-center space-x-4">
            <div className="text-sm font-medium text-gray-700">Select AI Model:</div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setSelectedModel('deepseek')}
                className={`flex items-center px-3 py-1.5 text-sm rounded-md ${
                  selectedModel === 'deepseek'
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                }`}
              >
                DeepSeek Chat
              </button>

              <button
                onClick={() => setSelectedModel('openrouter-gemini')}
                className={`flex items-center px-3 py-1.5 text-sm rounded-md ${
                  selectedModel === 'openrouter-gemini'
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                }`}
              >
                <Sparkles size={14} className="mr-1" />
                GPT-3.5 Turbo
              </button>
            </div>
          </div>

          {errorMessage && (
            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md text-sm text-yellow-800">
              {errorMessage}
            </div>
          )}
        </div>

        {copywriting ? (
          <div className="relative mt-4">
            <div className="bg-gray-50 p-4 rounded-md whitespace-pre-line text-sm font-medium">
              {copywriting}
            </div>

            <div className="flex justify-between items-center mt-2">
              <div className="text-xs text-gray-500 flex items-center">
                {selectedModel === 'openrouter-gemini' ? (
                  <>
                    <Sparkles size={12} className="mr-1 text-blue-500" />
                    Generated with GPT-3.5 Turbo
                  </>
                ) : (
                  <>Generated with DeepSeek Chat</>
                )}
              </div>
            </div>

            <button
              onClick={handleCopyToClipboard}
              className="absolute top-2 right-2 p-2 bg-white rounded-full shadow-sm hover:bg-gray-100"
              title="Copy to clipboard"
            >
              {copied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
            </button>
          </div>
        ) : (
          <div className="bg-gray-50 p-4 rounded-md text-gray-500 italic">
            {isGenerating
              ? 'Generating copywriting...'
              : 'Click "Generate Copy" to create WhatsApp marketing text for this bundle'}
          </div>
        )}
      </div>

      {/* Professional Email Offer Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Professional Email Offer</h2>

          <button
            onClick={handleGenerateEmailOffer}
            disabled={isGeneratingEmail || bundleItems.length === 0}
            className={`flex items-center px-4 py-2 rounded-md text-white ${
              isGeneratingEmail || bundleItems.length === 0
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-indigo-600 hover:bg-indigo-700'
            }`}
          >
            <Mail size={16} className="mr-2" />
            {isGeneratingEmail ? 'Generating...' : 'Generate Email'}
          </button>
        </div>

        <div className="mb-4">
          <div className="flex items-center space-x-4">
            <div className="text-sm font-medium text-gray-700">Select AI Model:</div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => setSelectedEmailModel('gemini-flash-lite')}
                className={`flex items-center px-3 py-1.5 text-sm rounded-md ${
                  selectedEmailModel === 'gemini-flash-lite'
                    ? 'bg-indigo-100 text-indigo-700 border border-indigo-300'
                    : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                }`}
              >
                <Sparkles size={14} className="mr-1 text-purple-500" />
                Gemini 2.0 Flash Lite
              </button>

              <button
                onClick={() => setSelectedEmailModel('deepseek')}
                className={`flex items-center px-3 py-1.5 text-sm rounded-md ${
                  selectedEmailModel === 'deepseek'
                    ? 'bg-indigo-100 text-indigo-700 border border-indigo-300'
                    : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                }`}
              >
                DeepSeek Chat
              </button>

              <button
                onClick={() => setSelectedEmailModel('openrouter-gemini')}
                className={`flex items-center px-3 py-1.5 text-sm rounded-md ${
                  selectedEmailModel === 'openrouter-gemini'
                    ? 'bg-indigo-100 text-indigo-700 border border-indigo-300'
                    : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-200'
                }`}
              >
                <Sparkles size={14} className="mr-1" />
                GPT-3.5 Turbo
              </button>
            </div>
          </div>

          {emailErrorMessage && (
            <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md text-sm text-yellow-800">
              {emailErrorMessage}
            </div>
          )}
        </div>

        {emailOffer ? (
          <div className="relative mt-4">
            <div className="bg-gray-50 p-4 rounded-md whitespace-pre-line text-sm font-medium">
              {emailOffer}
            </div>

            <div className="flex justify-between items-center mt-2">
              <div className="text-xs text-gray-500 flex items-center">
                {selectedEmailModel === 'gemini-flash-lite' ? (
                  <>
                    <Sparkles size={12} className="mr-1 text-purple-500" />
                    Generated with Gemini 2.0 Flash Lite
                  </>
                ) : selectedEmailModel === 'openrouter-gemini' ? (
                  <>
                    <Sparkles size={12} className="mr-1 text-indigo-500" />
                    Generated with GPT-3.5 Turbo
                  </>
                ) : (
                  <>Generated with DeepSeek Chat</>
                )}
              </div>
            </div>

            <button
              onClick={handleCopyEmailToClipboard}
              className="absolute top-2 right-2 p-2 bg-white rounded-full shadow-sm hover:bg-gray-100"
              title="Copy to clipboard"
            >
              {emailCopied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
            </button>
          </div>
        ) : (
          <div className="bg-gray-50 p-4 rounded-md text-gray-500 italic">
            {isGeneratingEmail
              ? 'Generating professional email offer...'
              : 'Click "Generate Email" to create a professional email offer for this bundle'}
          </div>
        )}
      </div>
    </div>
  );
}
