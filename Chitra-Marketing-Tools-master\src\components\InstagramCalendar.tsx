import React, { useState, useEffect } from 'react';
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Plus,
  Image,
  Video,
  Layers,
  Clock,
  CheckCircle,
  AlertCircle,
  Info,
  Instagram,
  Search,
  Filter,
  Sparkles,
  HardHat,
  ToggleLeft,
  ToggleRight,
  RefreshCw
} from 'lucide-react';
import {
  InstagramCalendarMonth,
  InstagramCalendarDay,
  SocialMediaPost,
  ContentType,
  PostStatus
} from '../types/socialMedia';
import {
  generateInstagramCalendarMonth
} from '../services/socialMediaService';
import { convertInstagramCalendarDayToContentRequest } from '../services/calendarContentService';
import { clearHolidayCache } from '../services/holidayService';

interface InstagramCalendarProps {
  onDayClick?: (day: InstagramCalendarDay) => void;
  onPostClick?: (post: SocialMediaPost) => void;
  onAddPost?: (date: string) => void;
  onGenerateContent?: (day: InstagramCalendarDay) => void;
}

export default function InstagramCalendar({
  onDayClick,
  onPostClick,
  onAddPost,
  onGenerateContent
}: InstagramCalendarProps) {
  // State for current date
  const [currentDate, setCurrentDate] = useState(new Date());
  const [calendarData, setCalendarData] = useState<InstagramCalendarMonth | null>(null);
  const [loading, setLoading] = useState(true);

  // State for filters
  const [showMiningEvents, setShowMiningEvents] = useState(true);

  // State for refresh
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Load calendar data for the current month
  useEffect(() => {
    const loadCalendarData = async () => {
      setLoading(true);
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1; // JavaScript months are 0-indexed

      try {
        // Generate calendar data
        const data = await generateInstagramCalendarMonth(year, month);
        setCalendarData(data);
      } catch (error) {
        console.error('Error loading calendar data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCalendarData();
  }, [currentDate]);

  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() - 1);
      return newDate;
    });
  };

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() + 1);
      return newDate;
    });
  };

  // Refresh calendar data by clearing cache and reloading
  const refreshCalendarData = async () => {
    try {
      setIsRefreshing(true);

      // Clear holiday cache
      clearHolidayCache();

      // Reload calendar data
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth() + 1;

      // Generate calendar data
      const data = await generateInstagramCalendarMonth(year, month);
      setCalendarData(data);

      // Show success message
      alert('Data kalender berhasil diperbarui dengan hari libur terbaru');
    } catch (error) {
      console.error('Error refreshing calendar data:', error);
      alert('Gagal memperbarui data kalender');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Format month name
  const formatMonthName = (date: Date): string => {
    return date.toLocaleDateString('id-ID', { month: 'long', year: 'numeric' });
  };

  // Get content type icon
  const getContentTypeIcon = (contentType: ContentType) => {
    switch (contentType) {
      case ContentType.IMAGE:
        return <Image size={16} className="text-blue-500" />;
      case ContentType.VIDEO:
        return <Video size={16} className="text-red-500" />;
      case ContentType.CAROUSEL:
        return <Layers size={16} className="text-purple-500" />;
      case ContentType.STORY:
        return <Clock size={16} className="text-orange-500" />;
      case ContentType.REEL:
        return <Video size={16} className="text-pink-500" />;
      default:
        return <Image size={16} />;
    }
  };

  // Get post status icon
  const getPostStatusIcon = (status: PostStatus) => {
    switch (status) {
      case PostStatus.PUBLISHED:
        return <CheckCircle size={16} className="text-green-500" />;
      case PostStatus.SCHEDULED:
        return <Clock size={16} className="text-blue-500" />;
      case PostStatus.DRAFT:
        return <AlertCircle size={16} className="text-yellow-500" />;
      case PostStatus.ARCHIVED:
        return <AlertCircle size={16} className="text-gray-500" />;
      default:
        return null;
    }
  };

  // Get recommendation color based on score
  const getRecommendationColor = (score: number | undefined): string => {
    if (!score) return 'bg-gray-100';

    if (score >= 80) return 'bg-green-100 border-green-300';
    if (score >= 60) return 'bg-yellow-100 border-yellow-300';
    return 'bg-red-100 border-red-300';
  };

  // Render calendar grid
  const renderCalendarGrid = () => {
    if (!calendarData) return null;

    const { days } = calendarData;

    // Get day names for header
    const dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
    const dayNamesShort = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

    // Calculate the first day of the month (0 = Sunday, 1 = Monday, etc.)
    const firstDayOfMonth = new Date(calendarData.year, calendarData.month - 1, 1).getDay();

    // Create calendar grid with empty cells for days before the first day of the month
    const calendarGrid = [];

    // Add empty cells for days before the first day of the month
    let cells = [];
    for (let i = 0; i < firstDayOfMonth; i++) {
      cells.push(
        <div key={`empty-${i}`} className="p-1 border border-gray-200 rounded bg-gray-50 h-24"></div>
      );
    }

    // Add cells for each day of the month
    days.forEach((day, index) => {
      const dayNumber = index + 1;
      const isToday = new Date().toISOString().split('T')[0] === day.date;
      const posts = day.posts || [];

      cells.push(
        <div
          key={`day-${dayNumber}`}
          className={`p-1 border ${isToday ? 'border-blue-500' : 'border-gray-200'} ${
            day.isHoliday
              ? day.holidayName && day.holidayName.toLowerCase().includes('cuti bersama')
                ? 'border-l-4 border-l-orange-500'
                : 'border-l-4 border-l-red-500'
              : ''
          } rounded ${getRecommendationColor(day.recommendationScore)} h-24 overflow-hidden relative`}
          onClick={() => onDayClick && onDayClick(day)}
        >
          <div className="flex justify-between items-start">
            <span className={`text-sm font-medium ${isToday ? 'text-blue-600' : ''} ${day.isHoliday ? 'text-red-600' : ''}`}>
              {dayNumber}
            </span>
            {day.recommendationScore !== undefined && (
              <span className="text-xs bg-white px-1 rounded-full border">
                {day.recommendationScore}
              </span>
            )}
          </div>

          {/* Holiday name if it's a holiday */}
          {day.isHoliday && day.holidayName && (
            <div className={`mt-1 text-xs font-medium ${
              day.holidayName.toLowerCase().includes('cuti bersama')
                ? 'text-orange-600 bg-orange-50 border-orange-200'
                : 'text-red-600 bg-red-50 border-red-200'
            } px-1 py-0.5 rounded border`}>
              {day.holidayName}
            </div>
          )}

          {/* Mining event indicator - only show if showMiningEvents is true */}
          {showMiningEvents && day.isHoliday && day.holidayName && (
            day.holidayName.toLowerCase().includes('tambang') ||
            day.holidayName.toLowerCase().includes('pertambangan') ||
            day.holidayName.toLowerCase().includes('mining') ||
            day.holidayName.toLowerCase().includes('batubara') ||
            day.holidayName.toLowerCase().includes('maintenance')
          ) ? (
            <div className="mt-1 text-xs font-medium text-amber-700 bg-amber-50 px-1 py-0.5 rounded border border-amber-200 flex items-center">
              <HardHat size={12} className="mr-1 text-amber-600" />
              {day.holidayName}
            </div>
          ) : null}

          {/* Posts for this day */}
          <div className="mt-1 space-y-1 max-h-[calc(100%-24px)] overflow-hidden">
            {posts.slice(0, 2).map((post, postIndex) => (
              <div
                key={`post-${post.id}`}
                className="flex items-center text-xs p-1 bg-white rounded border border-gray-200 cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  onPostClick && onPostClick(post);
                }}
              >
                {getContentTypeIcon(post.contentType)}
                <span className="ml-1 truncate">{post.title}</span>
                <span className="ml-auto">{getPostStatusIcon(post.status)}</span>
              </div>
            ))}
            {posts.length > 2 && (
              <div className="text-xs text-center text-gray-500">
                +{posts.length - 2} more
              </div>
            )}
          </div>

          {/* Action buttons */}
          <div className="absolute bottom-1 right-1 flex space-x-1">
            {/* Generate content button */}
            <button
              className="bg-purple-500 text-white rounded-full p-1 hover:bg-purple-600 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                onGenerateContent && onGenerateContent(day);
              }}
              title="Hasilkan konten"
            >
              <Sparkles size={14} />
            </button>

            {/* Add post button */}
            <button
              className="bg-blue-500 text-white rounded-full p-1 hover:bg-blue-600 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                onAddPost && onAddPost(day.date);
              }}
              title="Tambah post"
            >
              <Plus size={14} />
            </button>
          </div>
        </div>
      );
    });

    // Add empty cells for days after the last day of the month to complete the grid
    const totalCells = cells.length;
    const rowsNeeded = Math.ceil(totalCells / 7);
    const cellsNeeded = rowsNeeded * 7;

    for (let i = totalCells; i < cellsNeeded; i++) {
      cells.push(
        <div key={`empty-end-${i}`} className="p-1 border border-gray-200 rounded bg-gray-50 h-24"></div>
      );
    }

    // Group cells into rows
    const rows = [];
    for (let i = 0; i < cells.length; i += 7) {
      rows.push(
        <div key={`row-${i}`} className="grid grid-cols-7 gap-1 mb-1">
          {cells.slice(i, i + 7)}
        </div>
      );
    }

    return (
      <div className="space-y-1">
        {/* Day names header */}
        <div className="grid grid-cols-7 gap-1 mb-1">
          {dayNames.map((day, index) => (
            <div
              key={`header-${index}`}
              className={`text-center text-sm font-medium py-2 ${index === 0 || index === 6 ? 'bg-red-50 text-red-700' : 'bg-blue-50 text-blue-700'} rounded border ${index === 0 || index === 6 ? 'border-red-200' : 'border-blue-200'}`}
            >
              <span className="hidden md:inline">{day}</span>
              <span className="md:hidden">{dayNamesShort[index]}</span>
            </div>
          ))}
        </div>

        {/* Calendar days */}
        {rows}
      </div>
    );
  };

  // Render calendar summary
  const renderCalendarSummary = () => {
    if (!calendarData) return null;

    const { days } = calendarData;

    // Calculate total posts
    const totalPosts = days.reduce((sum, day) => sum + (day.posts?.length || 0), 0);

    // Calculate posts by status
    const publishedPosts = days.reduce((sum, day) => {
      return sum + (day.posts?.filter(post => post.status === PostStatus.PUBLISHED).length || 0);
    }, 0);

    const scheduledPosts = days.reduce((sum, day) => {
      return sum + (day.posts?.filter(post => post.status === PostStatus.SCHEDULED).length || 0);
    }, 0);

    const draftPosts = days.reduce((sum, day) => {
      return sum + (day.posts?.filter(post => post.status === PostStatus.DRAFT).length || 0);
    }, 0);

    // Calculate posts by content type
    const imageCount = days.reduce((sum, day) => {
      return sum + (day.posts?.filter(post => post.contentType === ContentType.IMAGE).length || 0);
    }, 0);

    const videoCount = days.reduce((sum, day) => {
      return sum + (day.posts?.filter(post => post.contentType === ContentType.VIDEO).length || 0);
    }, 0);

    const carouselCount = days.reduce((sum, day) => {
      return sum + (day.posts?.filter(post => post.contentType === ContentType.CAROUSEL).length || 0);
    }, 0);

    const reelCount = days.reduce((sum, day) => {
      return sum + (day.posts?.filter(post => post.contentType === ContentType.REEL).length || 0);
    }, 0);

    return (
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-medium mb-3">Ringkasan Bulan Ini</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div>
            <p className="text-sm text-gray-500">Total Konten</p>
            <p className="text-2xl font-bold">{totalPosts}</p>
            <div className="text-sm">
              <p><span className="font-medium text-green-600">{publishedPosts}</span> Published</p>
              <p><span className="font-medium text-blue-600">{scheduledPosts}</span> Scheduled</p>
              <p><span className="font-medium text-yellow-600">{draftPosts}</span> Draft</p>
            </div>
          </div>
          <div>
            <p className="text-sm text-gray-500">Tipe Konten</p>
            <div className="text-sm mt-2">
              <p><span className="font-medium text-blue-600">{imageCount}</span> Image</p>
              <p><span className="font-medium text-red-600">{videoCount}</span> Video</p>
              <p><span className="font-medium text-purple-600">{carouselCount}</span> Carousel</p>
              <p><span className="font-medium text-pink-600">{reelCount}</span> Reel</p>
            </div>
          </div>
          <div className="col-span-2">
            <p className="text-sm text-gray-500">Tips Posting</p>
            <div className="text-sm mt-2 bg-blue-50 p-2 rounded border border-blue-200">
              <p className="font-medium text-blue-800 mb-1">Rekomendasi untuk {formatMonthName(currentDate)}</p>
              <ul className="list-disc list-inside text-blue-700 space-y-1">
                <li>Post 2-3x seminggu untuk konsistensi</li>
                <li>Gunakan Reels untuk meningkatkan jangkauan</li>
                <li>Posting di jam 10-11 pagi atau 7-8 malam</li>
                <li>Gunakan 20-30 hashtag relevan per post</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* Calendar Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Instagram size={20} className="text-pink-500 mr-2" />
          <h2 className="text-xl font-semibold">Kalender Instagram</h2>
        </div>
        <div className="flex items-center space-x-4">
          {/* Mining events toggle */}
          <button
            onClick={() => setShowMiningEvents(!showMiningEvents)}
            className="flex items-center text-sm px-3 py-1.5 rounded-full border border-gray-300 hover:bg-gray-50"
            title={showMiningEvents ? "Sembunyikan acara pertambangan" : "Tampilkan acara pertambangan"}
          >
            <HardHat size={16} className={`mr-1.5 ${showMiningEvents ? 'text-amber-600' : 'text-gray-400'}`} />
            <span>Acara Tambang</span>
            {showMiningEvents ? (
              <ToggleRight size={16} className="ml-1.5 text-green-500" />
            ) : (
              <ToggleLeft size={16} className="ml-1.5 text-gray-400" />
            )}
          </button>

          {/* Refresh button */}
          <button
            onClick={refreshCalendarData}
            disabled={isRefreshing}
            className={`flex items-center text-sm px-3 py-1.5 rounded-full border border-blue-300 hover:bg-blue-50 ${isRefreshing ? 'opacity-50 cursor-not-allowed' : ''}`}
            title="Perbarui data hari libur"
          >
            <RefreshCw size={16} className={`mr-1.5 text-blue-600 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span>{isRefreshing ? 'Memperbarui...' : 'Perbarui Hari Libur'}</span>
          </button>

          {/* Month navigation */}
          <div className="flex items-center space-x-2">
            <button
              onClick={goToPreviousMonth}
              className="p-1 rounded-full hover:bg-gray-100"
              aria-label="Previous month"
            >
              <ChevronLeft size={20} />
            </button>
            <span className="text-lg font-medium">{formatMonthName(currentDate)}</span>
            <button
              onClick={goToNextMonth}
              className="p-1 rounded-full hover:bg-gray-100"
              aria-label="Next month"
            >
              <ChevronRight size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Calendar Summary */}
      {renderCalendarSummary()}

      {/* Calendar Grid */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          renderCalendarGrid()
        )}
      </div>

      {/* Legend */}
      <div className="bg-white p-3 rounded-lg shadow-sm border border-gray-200">
        <div className="flex flex-wrap gap-3">
          <div className="flex items-center">
            <div className="w-4 h-4 rounded bg-green-100 border border-green-300 mr-1"></div>
            <span className="text-xs">Rekomendasi Tinggi</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 rounded bg-yellow-100 border border-yellow-300 mr-1"></div>
            <span className="text-xs">Rekomendasi Sedang</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 rounded bg-red-100 border border-red-300 mr-1"></div>
            <span className="text-xs">Rekomendasi Rendah</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 border-l-4 border-l-red-500 mr-1"></div>
            <span className="text-xs">Hari Libur Nasional</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 border-l-4 border-l-orange-500 mr-1"></div>
            <span className="text-xs">Cuti Bersama</span>
          </div>
          <div className="flex items-center">
            <HardHat size={14} className="text-amber-600 mr-1" />
            <span className="text-xs">Acara Tambang</span>
          </div>
          <div className="flex items-center ml-4">
            <Image size={14} className="text-blue-500 mr-1" />
            <span className="text-xs">Image</span>
          </div>
          <div className="flex items-center">
            <Video size={14} className="text-red-500 mr-1" />
            <span className="text-xs">Video</span>
          </div>
          <div className="flex items-center">
            <Layers size={14} className="text-purple-500 mr-1" />
            <span className="text-xs">Carousel</span>
          </div>
          <div className="flex items-center">
            <Video size={14} className="text-pink-500 mr-1" />
            <span className="text-xs">Reel</span>
          </div>
        </div>
      </div>
    </div>
  );
}
