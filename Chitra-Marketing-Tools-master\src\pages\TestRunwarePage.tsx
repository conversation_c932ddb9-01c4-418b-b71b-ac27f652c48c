import React, { useState } from 'react';
import { generateImages } from '../services/runwareServiceNew';
import { ImageGenerationRequest } from '../types/imageGenerator';
import { ImageIcon, Sparkles, Loader2 } from 'lucide-react';
// Mock useToast function
const useToast = () => {
  return {
    toast: (options: any) => {
      console.log('Toast:', options);
      alert(options.title + ': ' + options.description);
    }
  };
};

const TestRunwarePage: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const testImageGeneration = async () => {
    setIsGenerating(true);
    setError(null);
    setImageUrl(null);

    try {
      const request: ImageGenerationRequest = {
        positivePrompt: 'a cat',
        negativePrompt: '',
        width: 512,
        height: 512,
        model: 'rundiffusion:130@100',
        numberResults: 1
      };

      const images = await generateImages(request);
      console.log('Generated images:', images);

      if (images && images.length > 0) {
        setImageUrl(images[0].imageURL);
        toast({
          title: "Success",
          description: "Image generated successfully!",
        });
      } else {
        setError("No images were generated");
      }
    } catch (err) {
      console.error('Image generation failed:', err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to generate image",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center mb-6">
        <ImageIcon size={24} className="text-blue-500 mr-2" />
        <h1 className="text-2xl font-bold">Test Runware API</h1>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6 p-6">
        <button
          onClick={testImageGeneration}
          disabled={isGenerating}
          className="w-full flex justify-center items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-blue-300 disabled:cursor-not-allowed"
        >
          {isGenerating ? (
            <>
              <Loader2 size={16} className="mr-2 animate-spin" />
              Generating Test Image...
            </>
          ) : (
            <>
              <Sparkles size={16} className="mr-2" />
              Generate Test Image
            </>
          )}
        </button>

        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md text-red-700">
            <h3 className="font-medium">Error:</h3>
            <p className="mt-1">{error}</p>
          </div>
        )}

        {imageUrl && (
          <div className="mt-6">
            <h3 className="text-lg font-medium mb-2">Generated Image:</h3>
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <img
                src={imageUrl}
                alt="Generated test image"
                className="w-full h-auto"
                loading="lazy"
              />
            </div>
            <p className="mt-2 text-sm text-gray-500">Image URL: {imageUrl}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TestRunwarePage;
