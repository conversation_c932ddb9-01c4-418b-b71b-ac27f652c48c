import { Template, TemplateFormData } from '../types/template';
import { v4 as uuidv4 } from 'uuid';
// JSZip is dynamically imported in extractTextFromDocx function

const TEMPLATES_STORAGE_KEY = 'chitra_templates';

// Helper function to extract variables from a docx file
export const extractVariablesFromDocx = async (file: File): Promise<string[]> => {
  try {
    console.log('Starting to extract variables from docx file:', file.name);

    // First try the binary approach with docx library
    try {
      const arrayBuffer = await file.arrayBuffer();
      const content = await extractTextFromDocx(arrayBuffer);
      console.log('Extracted text content from docx:', content.substring(0, 200) + '...');

      // Look for {{variable}} patterns
      const variableRegex = /\{\{([^}]+)\}\}/g;
      const matches = content.match(variableRegex) || [];
      console.log('Found variable matches:', matches);

      // Extract just the variable names without the braces
      const variables = matches.map(match => match.replace(/\{\{|\}\}/g, ''));
      console.log('Extracted variables:', variables);

      if (variables.length > 0) {
        return variables;
      }
    } catch (docxError) {
      console.error('Error using docx library to extract text:', docxError);
      // Fall back to text-based approach
    }

    // Fallback: Try to read as text directly
    console.log('Falling back to text-based approach');
    const text = await file.text();
    console.log('Read file as text, length:', text.length);

    // Check if we got binary content
    if (text.includes('PK') && text.includes('word/document.xml')) {
      console.log('File appears to be a binary DOCX file, but read as text');
    }

    const variableRegex = /\{\{([^}]+)\}\}/g;
    const matches = text.match(variableRegex) || [];
    console.log('Found variable matches (fallback):', matches);

    // Extract just the variable names without the braces
    return matches.map(match => match.replace(/\{\{|\}\}/g, ''));
  } catch (error) {
    console.error('Error extracting variables from docx:', error);
    return [];
  }
};

// Helper function to decode XML entities
const decodeXmlEntities = (text: string): string => {
  const entities: Record<string, string> = {
    '&lt;': '<',
    '&gt;': '>',
    '&amp;': '&',
    '&quot;': '"',
    '&apos;': "'",
    '&#123;': '{',
    '&#125;': '}',
  };

  return text.replace(/&[^;]+;/g, (entity) => {
    return entities[entity] || entity;
  });
};

// Helper function to extract text from docx using JSZip
const extractTextFromDocx = async (arrayBuffer: ArrayBuffer): Promise<string> => {
  try {
    // Use JSZip to extract the document.xml file from the docx package
    const JSZip = (await import('jszip')).default;
    const zip = new JSZip();

    // Load the docx file as a zip archive
    const zipContent = await zip.loadAsync(arrayBuffer);

    // Get the document.xml file
    const documentXml = await zipContent.file('word/document.xml')?.async('text');
    if (!documentXml) {
      console.error('Could not find word/document.xml in the docx file');
      return '';
    }

    console.log('Found document.xml, length:', documentXml.length);

    // Extract text from w:t tags (simplified approach)
    const textRegex = /<w:t[^>]*>(.*?)<\/w:t>/g;
    let textMatch;
    let result = '';

    while ((textMatch = textRegex.exec(documentXml)) !== null) {
      // Decode HTML entities that might be in the XML
      const decodedText = decodeXmlEntities(textMatch[1]);
      result += decodedText + ' ';
    }

    console.log('Extracted text from document.xml, length:', result.length);
    return result;
  } catch (error) {
    console.error('Error in extractTextFromDocx:', error);
    throw error;
  }
};

// Get all templates
export const getAllTemplates = (): Template[] => {
  const templatesJson = localStorage.getItem(TEMPLATES_STORAGE_KEY);
  if (!templatesJson) return [];

  try {
    return JSON.parse(templatesJson);
  } catch (error) {
    console.error('Error parsing templates from localStorage:', error);
    return [];
  }
};

// Get templates by type
export const getTemplatesByType = (type: string): Template[] => {
  const templates = getAllTemplates();
  return templates.filter(template => template.type === type);
};

// Get template by ID
export const getTemplateById = (id: string): Template | null => {
  const templates = getAllTemplates();
  const template = templates.find(t => t.id === id);
  return template || null;
};

// Create a new template
export const createTemplate = async (templateData: TemplateFormData): Promise<Template> => {
  const templates = getAllTemplates();

  // Extract variables from the file
  let detectedVariables: string[] = [];
  if (templateData.file) {
    detectedVariables = await extractVariablesFromDocx(templateData.file);
  }

  // Create a new template object
  const newTemplate: Template = {
    id: uuidv4(),
    name: templateData.name,
    type: templateData.type,
    file: null, // We don't store the actual File object in localStorage
    fileUrl: templateData.file ? URL.createObjectURL(templateData.file) : undefined,
    detectedVariables,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // Store the file in IndexedDB or another storage mechanism
  // For now, we'll just store the template metadata

  // Add to templates array and save
  templates.push(newTemplate);
  localStorage.setItem(TEMPLATES_STORAGE_KEY, JSON.stringify(templates));

  return newTemplate;
};

// Update an existing template
export const updateTemplate = async (id: string, templateData: Partial<TemplateFormData>): Promise<Template | null> => {
  const templates = getAllTemplates();
  const templateIndex = templates.findIndex(t => t.id === id);

  if (templateIndex === -1) return null;

  // Extract variables if a new file is provided
  let detectedVariables = templates[templateIndex].detectedVariables;
  if (templateData.file) {
    detectedVariables = await extractVariablesFromDocx(templateData.file);
  }

  // Update the template
  templates[templateIndex] = {
    ...templates[templateIndex],
    ...(templateData.name && { name: templateData.name }),
    ...(templateData.type && { type: templateData.type }),
    ...(templateData.file && {
      file: null, // We don't store the actual File object
      fileUrl: URL.createObjectURL(templateData.file),
      detectedVariables
    }),
    updatedAt: new Date().toISOString()
  };

  // Save updated templates
  localStorage.setItem(TEMPLATES_STORAGE_KEY, JSON.stringify(templates));

  return templates[templateIndex];
};

// Delete a template
export const deleteTemplate = (id: string): boolean => {
  const templates = getAllTemplates();
  const filteredTemplates = templates.filter(t => t.id !== id);

  if (filteredTemplates.length === templates.length) {
    return false; // No template was deleted
  }

  localStorage.setItem(TEMPLATES_STORAGE_KEY, JSON.stringify(filteredTemplates));
  return true;
};
