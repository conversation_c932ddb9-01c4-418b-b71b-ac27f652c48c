import axios from 'axios';
import { MODELS } from './openRouter';

// Using OpenRouter API with the provided key
const API_KEY = '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';
const MODEL = 'openai/gpt-4.1-nano';

export async function generateCopywriting(
  productDetails: string,
  price: number,
  specialFeatures: string
) {
  try {
    console.log('Generating copywriting with OpenRouter API using model:', MODEL);

    const requestBody = {
      model: MODEL,
      messages: [
        {
          role: 'system',
          content: 'You are an expert marketing copywriter specializing in creating persuasive, attention-grabbing WhatsApp messages for automotive product bundles. Your writing is enthusiastic, uses powerful language, and creates a sense of urgency and exclusivity.'
        },
        {
          role: 'user',
          content: `Create an ENGAGING and PERSUASIVE WhatsApp marketing message in Indonesian for this automotive product bundle:

Products: ${productDetails}
Price: Rp ${price.toLocaleString()}
Special features: ${specialFeatures}

Follow this format but make it EXCITING and COMPELLING:
1. Start with eye-catching emojis (🚛, 🔥, 💯, etc.)
2. Create a BOLD, ATTENTION-GRABBING title that mentions the vehicle type and uses words like "PROMO SPESIAL", "PAKET HEMAT", or "DISKON BESAR"
3. Use the phrase "Dapatkan [NUMBER] produk premium:" followed by a detailed bullet list of each product with brand names
4. Highlight the price with emojis like 💰 and emphasize the value/savings
5. Create a strong sense of urgency with phrases like "Stok terbatas!", "Promo hanya berlaku minggu ini!", etc.
6. Add 2-3 persuasive sentences about benefits, quality, and value
7. End with a clear call-to-action like "Hubungi kami sekarang untuk pesan!" or "Jangan lewatkan kesempatan ini!"
8. Use additional relevant emojis throughout (✅, ⭐, 🔧, etc.)

Make it longer and more detailed than a basic message, but still concise enough for WhatsApp (around 10-15 lines). Use formatting like *bold* and _italic_ to emphasize key points.`
        }
      ]
    };

    console.log('Request body:', JSON.stringify(requestBody, null, 2));

    const response = await axios.post(
      'https://openrouter.ai/api/v1/chat/completions',
      requestBody,
      {
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://chitraparatama.co.id',
          'X-Title': 'Chitra Marketing Tools'
        }
      }
    );

    console.log('OpenRouter API response:', JSON.stringify(response.data, null, 2));

    // Extract the content from the response
    if (response.data &&
        response.data.choices &&
        response.data.choices.length > 0 &&
        response.data.choices[0].message &&
        response.data.choices[0].message.content) {
      return response.data.choices[0].message.content;
    }

    console.error('Unexpected response format:', response.data);
    return 'Failed to generate copywriting. Unexpected response format.';
  } catch (error) {
    console.error('Error generating copywriting with OpenRouter API:', error);

    // More detailed error logging
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Error response data:', error.response.data);
      console.error('Error response status:', error.response.status);
      console.error('Error response headers:', error.response.headers);
      return `Failed to generate copywriting. Server error: ${error.response.status} - ${JSON.stringify(error.response.data)}`;
    } else if (error.request) {
      // The request was made but no response was received
      console.error('Error request:', error.request);
      return 'Failed to generate copywriting. No response received from server.';
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error message:', error.message);
      return `Failed to generate copywriting. Error: ${error.message}`;
    }
  }
}
