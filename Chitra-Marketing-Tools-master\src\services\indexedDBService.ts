/**
 * IndexedDB Service
 * 
 * This service provides a wrapper around IndexedDB for persistent storage
 * that works across different ports and browser sessions.
 */

// Database configuration
const DB_NAME = 'chitraMarketingToolsDB';
const DB_VERSION = 1;
const STORES = {
  SALES_REVENUE_2025: 'salesRevenue2025'
};

/**
 * Initialize the database
 */
export const initDB = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = (event) => {
      console.error('Error opening IndexedDB:', event);
      reject('Error opening IndexedDB');
    };

    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      console.log('IndexedDB opened successfully');
      resolve(db);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      
      // Create object stores if they don't exist
      if (!db.objectStoreNames.contains(STORES.SALES_REVENUE_2025)) {
        const store = db.createObjectStore(STORES.SALES_REVENUE_2025, { keyPath: 'id' });
        
        // Create indexes for faster queries
        store.createIndex('customerName', 'customerName', { unique: false });
        store.createIndex('salesman', 'salesman', { unique: false });
        store.createIndex('billingDate', 'billingDate', { unique: false });
        
        console.log('Object store created:', STORES.SALES_REVENUE_2025);
      }
    };
  });
};

/**
 * Get all items from a store
 */
export const getAllItems = async <T>(storeName: string): Promise<T[]> => {
  const db = await initDB();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readonly');
    const store = transaction.objectStore(storeName);
    const request = store.getAll();
    
    request.onerror = (event) => {
      console.error(`Error getting items from ${storeName}:`, event);
      reject(`Error getting items from ${storeName}`);
    };
    
    request.onsuccess = (event) => {
      const items = (event.target as IDBRequest).result;
      resolve(items);
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Add multiple items to a store
 */
export const addItems = async <T>(storeName: string, items: T[]): Promise<boolean> => {
  const db = await initDB();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    
    let successCount = 0;
    
    items.forEach((item) => {
      const request = store.add(item);
      
      request.onsuccess = () => {
        successCount++;
        if (successCount === items.length) {
          console.log(`Successfully added ${successCount} items to ${storeName}`);
        }
      };
      
      request.onerror = (event) => {
        console.error(`Error adding item to ${storeName}:`, event);
      };
    });
    
    transaction.oncomplete = () => {
      db.close();
      resolve(true);
    };
    
    transaction.onerror = (event) => {
      console.error(`Transaction error for ${storeName}:`, event);
      reject(`Transaction error for ${storeName}`);
    };
  });
};

/**
 * Update an item in a store
 */
export const updateItem = async <T>(storeName: string, item: T): Promise<boolean> => {
  const db = await initDB();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.put(item);
    
    request.onerror = (event) => {
      console.error(`Error updating item in ${storeName}:`, event);
      reject(`Error updating item in ${storeName}`);
    };
    
    request.onsuccess = () => {
      console.log(`Item updated successfully in ${storeName}`);
      resolve(true);
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Delete an item from a store
 */
export const deleteItem = async (storeName: string, id: string): Promise<boolean> => {
  const db = await initDB();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.delete(id);
    
    request.onerror = (event) => {
      console.error(`Error deleting item from ${storeName}:`, event);
      reject(`Error deleting item from ${storeName}`);
    };
    
    request.onsuccess = () => {
      console.log(`Item deleted successfully from ${storeName}`);
      resolve(true);
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Clear all items from a store
 */
export const clearStore = async (storeName: string): Promise<boolean> => {
  const db = await initDB();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(storeName, 'readwrite');
    const store = transaction.objectStore(storeName);
    const request = store.clear();
    
    request.onerror = (event) => {
      console.error(`Error clearing store ${storeName}:`, event);
      reject(`Error clearing store ${storeName}`);
    };
    
    request.onsuccess = () => {
      console.log(`Store ${storeName} cleared successfully`);
      resolve(true);
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
};

/**
 * Replace all items in a store
 */
export const replaceAllItems = async <T>(storeName: string, items: T[]): Promise<boolean> => {
  try {
    // Clear the store first
    await clearStore(storeName);
    
    // Then add all new items
    return await addItems(storeName, items);
  } catch (error) {
    console.error(`Error replacing items in ${storeName}:`, error);
    return false;
  }
};

export const STORE_NAMES = STORES;
