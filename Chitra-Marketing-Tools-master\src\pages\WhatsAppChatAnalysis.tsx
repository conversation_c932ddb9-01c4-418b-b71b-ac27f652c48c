import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  WhatsAppMessage,
  WhatsAppChatSession
} from '../types/whatsappChat';
import {
  parseWhatsAppChat,
  createWhatsAppChatSession,
  saveWhatsAppChatSession,
  analyzeWhatsAppChat
} from '../services/whatsappChatService';
import {
  MessageSquare,
  Upload,
  Send,
  User,
  BarChart3,
  Loader2,
  AlertTriangle,
  CheckCircle2,
  FileText,
  Clock,
  Brain,
  ExternalLink,
  History,
  Copy,
  CheckCheck,
  MessageCircle
} from 'lucide-react';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '../components/ui/tabs';
import { analyzeWhatsAppWebConversation, saveWhatsAppWebConversation } from '../services/negotiationService';

export default function WhatsAppChatAnalysis() {
  // State for WhatsApp Chat Analysis
  const [customerName, setCustomerName] = useState('');
  const [salesName, setSalesName] = useState('');
  const [chatText, setChatText] = useState('');
  const [chatSession, setChatSession] = useState<WhatsAppChatSession | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // State for WhatsApp Web AI Assistant
  const [activeTab, setActiveTab] = useState('chat-analysis');
  const [webAnalysis, setWebAnalysis] = useState('');
  const [isWebAnalyzing, setIsWebAnalyzing] = useState(false);
  const [webError, setWebError] = useState<string | null>(null);
  const [question, setQuestion] = useState('');
  const [recentQuestions, setRecentQuestions] = useState<string[]>([]);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  // Scroll to bottom of chat when messages change
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatSession?.messages]);

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Reset states
    setChatText('');
    setChatSession(null);
    setError(null);
    setSuccess(null);

    // Check file type (should be .txt)
    if (file.type !== 'text/plain') {
      setError('Hanya file teks (.txt) yang didukung. Silakan ekspor chat WhatsApp sebagai file teks.');
      return;
    }

    // Read file content
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setChatText(content);
    };
    reader.onerror = () => {
      setError('Gagal membaca file. Silakan coba lagi.');
    };
    reader.readAsText(file);
  };

  // Parse chat
  const parseChat = () => {
    if (!chatText) {
      setError('Silakan unggah file chat WhatsApp terlebih dahulu.');
      return;
    }

    if (!customerName) {
      setError('Silakan masukkan nama pelanggan.');
      return;
    }

    if (!salesName) {
      setError('Silakan masukkan nama sales.');
      return;
    }

    try {
      // Parse chat text
      const messages = parseWhatsAppChat(chatText, salesName, customerName);

      if (messages.length === 0) {
        setError('Tidak dapat menemukan pesan dalam format WhatsApp. Pastikan format chat sesuai.');
        return;
      }

      // Create chat session
      const session = createWhatsAppChatSession(customerName, salesName, messages);
      setChatSession(session);

      // Save session
      saveWhatsAppChatSession(session);

      setSuccess('Chat berhasil diimpor dan diurai.');
      setError(null);
    } catch (err) {
      console.error('Error parsing chat:', err);
      setError('Terjadi kesalahan saat mengurai chat. Pastikan format chat sesuai.');
    }
  };

  // Analyze chat
  const analyzeChat = async () => {
    if (!chatSession) {
      setError('Silakan impor dan urai chat terlebih dahulu.');
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      const analysisResponse = await analyzeWhatsAppChat(chatSession);

      if (analysisResponse.evaluation) {
        // Navigate to results page
        navigate('/whatsapp-chat-results', {
          state: {
            session: chatSession,
            evaluation: analysisResponse.evaluation
          }
        });
      } else {
        setError('Tidak dapat menganalisis chat. Silakan coba lagi.');
      }
    } catch (err) {
      console.error('Error analyzing chat:', err);
      setError('Terjadi kesalahan saat menganalisis chat. Silakan coba lagi.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Format timestamp
  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Function to open WhatsApp Web in external browser
  const openWhatsAppWeb = () => {
    window.open('https://web.whatsapp.com/', '_blank');
  };

  // Function to analyze the current conversation with AI
  const handleWebAnalyze = async () => {
    if (!question) {
      setWebError('Mohon masukkan pertanyaan yang ingin dianalisis.');
      return;
    }

    setIsWebAnalyzing(true);
    setWebError(null);

    try {
      // Save the question for future reference
      saveWhatsAppWebConversation(
        question,
        "WhatsApp Web User",
        "AI Assistant"
      );

      // Add to recent questions (max 5)
      setRecentQuestions(prev => {
        const newQuestions = [question, ...prev];
        return newQuestions.slice(0, 5);
      });

      // Analyze conversation with the question
      const result = await analyzeWhatsAppWebConversation(
        "Pertanyaan: " + question,
        "Customer", // Placeholder
        "Sales" // Placeholder
      );

      setWebAnalysis(result.message);
    } catch (err) {
      console.error('Error analyzing WhatsApp Web conversation:', err);
      setWebError('Terjadi kesalahan saat menganalisis percakapan. Silakan coba lagi.');
    } finally {
      setIsWebAnalyzing(false);
    }
  };

  // Function to use a recent question
  const useRecentQuestion = (question: string) => {
    setQuestion(question);
  };

  // Function to copy analysis to clipboard
  const copyToClipboard = (text: string, index: number) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-semibold mb-6 flex items-center">
        <MessageSquare className="mr-2" />
        Analisis Chat WhatsApp
      </h1>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="chat-analysis" className="flex items-center gap-1">
            <MessageCircle className="h-4 w-4" />
            Analisis Chat WhatsApp
          </TabsTrigger>
          <TabsTrigger value="web-assistant" className="flex items-center gap-1">
            <Brain className="h-4 w-4" />
            WhatsApp Web AI Assistant
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chat-analysis">
          {/* Error and Success Messages */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 border-l-4 border-red-500 text-red-700 flex items-start">
              <AlertTriangle className="mr-2 mt-0.5 flex-shrink-0" size={18} />
              <p>{error}</p>
            </div>
          )}

          {success && (
            <div className="mb-4 p-3 bg-green-50 border-l-4 border-green-500 text-green-700 flex items-start">
              <CheckCircle2 className="mr-2 mt-0.5 flex-shrink-0" size={18} />
              <p>{success}</p>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Import Section */}
            <div className="lg:col-span-1 space-y-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h2 className="text-lg font-medium mb-4 flex items-center">
                  <Upload className="mr-2 text-blue-500" size={20} />
                  Impor Chat WhatsApp
                </h2>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nama Pelanggan
                    </label>
                    <input
                      type="text"
                      value={customerName}
                      onChange={(e) => setCustomerName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Masukkan nama pelanggan"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nama Sales
                    </label>
                    <input
                      type="text"
                      value={salesName}
                      onChange={(e) => setSalesName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Masukkan nama sales"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      File Chat WhatsApp
                    </label>
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileUpload}
                      accept=".txt"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      Unggah file chat WhatsApp yang diekspor (.txt)
                    </p>
                  </div>

                  <button
                    onClick={parseChat}
                    disabled={!chatText || !customerName || !salesName}
                    className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    <FileText size={18} className="mr-2" />
                    Urai Chat
                  </button>
                </div>
              </div>

              {chatSession && (
                <div className="bg-white p-6 rounded-lg shadow-sm border">
                  <h2 className="text-lg font-medium mb-4 flex items-center">
                    <BarChart3 className="mr-2 text-purple-500" size={20} />
                    Analisis Chat
                  </h2>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center">
                        <User size={16} className="mr-1 text-gray-500" />
                        <span className="text-gray-700">Pelanggan: {chatSession.customerName}</span>
                      </div>
                      <div className="flex items-center">
                        <User size={16} className="mr-1 text-gray-500" />
                        <span className="text-gray-700">Sales: {chatSession.salesName}</span>
                      </div>
                    </div>

                    <div className="flex items-center text-sm">
                      <Clock size={16} className="mr-1 text-gray-500" />
                      <span className="text-gray-700">
                        {chatSession.messages.length} pesan
                      </span>
                    </div>

                    <button
                      onClick={analyzeChat}
                      disabled={isAnalyzing}
                      className="w-full py-2 px-4 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                      {isAnalyzing ? (
                        <>
                          <Loader2 size={18} className="mr-2 animate-spin" />
                          Menganalisis...
                        </>
                      ) : (
                        <>
                          <BarChart3 size={18} className="mr-2" />
                          Analisis Chat
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Chat Preview */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-sm border h-[600px] flex flex-col">
                <div className="p-4 border-b bg-green-50 flex items-center">
                  <MessageSquare className="mr-2 text-green-600" size={20} />
                  <h2 className="text-lg font-medium">Preview Chat WhatsApp</h2>
                </div>

                {chatSession ? (
                  <div
                    ref={chatContainerRef}
                    className="flex-1 overflow-y-auto p-4 space-y-3"
                  >
                    {chatSession.messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${
                          message.isSales ? 'justify-end' : 'justify-start'
                        }`}
                      >
                        <div
                          className={`max-w-[70%] rounded-lg p-3 ${
                            message.isSales
                              ? 'bg-blue-100 text-blue-900'
                              : 'bg-gray-100 text-gray-900'
                          }`}
                        >
                          <div className="flex items-center mb-1">
                            <span className="font-medium text-xs">
                              {message.sender}
                            </span>
                            <span className="ml-2 text-xs text-gray-500">
                              {formatTimestamp(message.timestamp)}
                            </span>
                          </div>
                          <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex-1 flex items-center justify-center p-4 text-gray-500">
                    <div className="text-center">
                      <MessageSquare size={48} className="mx-auto mb-4 opacity-20" />
                      <p>Impor chat WhatsApp untuk melihat preview</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="web-assistant">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Panel - AI Assistant */}
            <div className="lg:col-span-2">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-semibold flex items-center">
                    <Brain className="mr-2" size={20} /> AI Assistant untuk WhatsApp
                  </h2>
                  <button
                    onClick={openWhatsAppWeb}
                    className="flex items-center px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                  >
                    <ExternalLink size={16} className="mr-2" />
                    Buka WhatsApp Web
                  </button>
                </div>

                <div className="mb-6">
                  <p className="text-gray-700 mb-4">
                    AI Assistant ini akan membantu Anda dalam percakapan WhatsApp dengan pelanggan.
                    Ajukan pertanyaan tentang strategi negosiasi, cara merespons pelanggan, atau teknik penjualan.
                  </p>

                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-md mb-4">
                    <h3 className="font-medium text-blue-800 mb-2 flex items-center">
                      <MessageSquare size={16} className="mr-2" /> Contoh Pertanyaan:
                    </h3>
                    <ul className="text-blue-700 space-y-2 text-sm">
                      <li>• Bagaimana cara merespons pelanggan yang meminta diskon besar?</li>
                      <li>• Apa strategi terbaik untuk menawarkan bundling produk ban?</li>
                      <li>• Bagaimana cara menangani keberatan pelanggan tentang harga?</li>
                      <li>• Template pesan untuk follow-up pelanggan yang lama tidak merespons?</li>
                      <li>• Cara meyakinkan pelanggan tentang kualitas produk ban kita?</li>
                    </ul>
                  </div>
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tanyakan AI Assistant
                  </label>
                  <textarea
                    value={question}
                    onChange={(e) => setQuestion(e.target.value)}
                    className="w-full p-3 border rounded-md h-32 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Masukkan pertanyaan Anda tentang percakapan WhatsApp dengan pelanggan..."
                  ></textarea>
                </div>

                {recentQuestions.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                      <History size={14} className="mr-1" /> Pertanyaan Terakhir:
                    </h3>
                    <div className="space-y-2">
                      {recentQuestions.map((q, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
                          <button
                            onClick={() => useRecentQuestion(q)}
                            className="text-sm text-left text-gray-700 hover:text-blue-600 truncate flex-1"
                          >
                            {q}
                          </button>
                          <button
                            onClick={() => copyToClipboard(q, index)}
                            className="p-1 text-gray-500 hover:text-blue-600"
                            title="Salin pertanyaan"
                          >
                            {copiedIndex === index ? <CheckCheck size={14} className="text-green-500" /> : <Copy size={14} />}
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <button
                  onClick={handleWebAnalyze}
                  disabled={isWebAnalyzing}
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 flex items-center justify-center"
                >
                  {isWebAnalyzing ? (
                    <>
                      <Loader2 className="animate-spin mr-2" size={18} />
                      Menganalisis...
                    </>
                  ) : (
                    <>
                      <Brain className="mr-2" size={18} />
                      Dapatkan Jawaban AI
                    </>
                  )}
                </button>

                {webError && (
                  <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
                    {webError}
                  </div>
                )}

                {webAnalysis && (
                  <div className="mt-6 p-4 bg-gray-50 rounded-md">
                    <h3 className="font-medium text-gray-800 mb-2">Hasil Analisis:</h3>
                    <div className="prose prose-sm max-w-none">
                      {webAnalysis}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Right Panel - Analysis Results */}
            <div className="lg:col-span-1">
              <div className="bg-white p-6 rounded-lg shadow-md h-full">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold flex items-center">
                    <Brain className="mr-2" size={20} /> Jawaban AI
                  </h2>
                  {webAnalysis && (
                    <button
                      onClick={() => copyToClipboard(webAnalysis, -1)}
                      className="p-2 text-gray-600 hover:text-blue-600 rounded-full hover:bg-gray-100"
                      title="Salin jawaban"
                    >
                      {copiedIndex === -1 ? <CheckCheck size={16} className="text-green-500" /> : <Copy size={16} />}
                    </button>
                  )}
                </div>

                {webAnalysis ? (
                  <div className="whitespace-pre-wrap bg-gray-50 p-4 rounded-md border overflow-y-auto" style={{ maxHeight: '700px' }}>
                    {webAnalysis}
                  </div>
                ) : (
                  <div className="text-gray-500 italic flex flex-col items-center justify-center h-64">
                    <Brain size={48} className="text-gray-300 mb-4" />
                    <p className="text-center">
                      Jawaban AI akan muncul di sini setelah Anda mengajukan pertanyaan.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
