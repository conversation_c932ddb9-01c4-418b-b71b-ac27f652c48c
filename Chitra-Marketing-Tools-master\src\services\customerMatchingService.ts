/**
 * Service for matching customer names across different data sources
 * Uses fuzzy matching to identify the same customer with different name variations
 */

import { Customer } from '../types/customer';
import { FleetlistItem } from './fleetlistService';
import { SalesRevenueItem } from './salesRevenue2025Service';
import { CustomerNameMatch } from '../types/customerAnalysis';

/**
 * Normalize a customer name for comparison
 * - Convert to uppercase
 * - Remove common prefixes like PT, CV, etc.
 * - Remove punctuation and extra spaces
 */
export function normalizeCustomerName(name: string): string {
  if (!name) return '';
  
  // Convert to uppercase
  let normalized = name.toUpperCase();
  
  // Remove common prefixes
  normalized = normalized
    .replace(/^PT\.?\s+/, '')
    .replace(/^CV\.?\s+/, '')
    .replace(/^PD\.?\s+/, '')
    .replace(/^UD\.?\s+/, '')
    .replace(/^PERUSAHAAN\s+/, '')
    .replace(/^FIRMA\s+/, '')
    .replace(/^YAYASAN\s+/, '');
  
  // Remove punctuation and normalize spaces
  normalized = normalized
    .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '')
    .replace(/\s+/g, ' ')
    .trim();
  
  return normalized;
}

/**
 * Calculate similarity score between two strings (0-100)
 * Higher score means more similar
 */
export function calculateSimilarity(str1: string, str2: string): number {
  const s1 = normalizeCustomerName(str1);
  const s2 = normalizeCustomerName(str2);
  
  if (s1 === s2) return 100; // Exact match after normalization
  
  // Check if one is contained within the other
  if (s1.includes(s2) || s2.includes(s1)) {
    const longerLength = Math.max(s1.length, s2.length);
    const shorterLength = Math.min(s1.length, s2.length);
    return Math.round((shorterLength / longerLength) * 100);
  }
  
  // Calculate Levenshtein distance
  const distance = levenshteinDistance(s1, s2);
  const maxLength = Math.max(s1.length, s2.length);
  
  // Convert distance to similarity score (0-100)
  return Math.max(0, Math.round((1 - distance / maxLength) * 100));
}

/**
 * Calculate Levenshtein distance between two strings
 * Lower distance means more similar
 */
function levenshteinDistance(str1: string, str2: string): number {
  const m = str1.length;
  const n = str2.length;
  
  // Create a matrix of size (m+1) x (n+1)
  const dp: number[][] = Array(m + 1).fill(null).map(() => Array(n + 1).fill(0));
  
  // Initialize the matrix
  for (let i = 0; i <= m; i++) {
    dp[i][0] = i;
  }
  
  for (let j = 0; j <= n; j++) {
    dp[0][j] = j;
  }
  
  // Fill the matrix
  for (let i = 1; i <= m; i++) {
    for (let j = 1; j <= n; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      dp[i][j] = Math.min(
        dp[i - 1][j] + 1, // deletion
        dp[i][j - 1] + 1, // insertion
        dp[i - 1][j - 1] + cost // substitution
      );
    }
  }
  
  return dp[m][n];
}

/**
 * Find matches for a customer name across all data sources
 */
export function findCustomerMatches(
  customerName: string,
  customers: Customer[],
  fleetData: FleetlistItem[],
  salesData: SalesRevenueItem[],
  similarityThreshold: number = 70 // Default threshold for considering a match
): CustomerNameMatch {
  const result: CustomerNameMatch = {
    originalName: customerName,
    matchedNames: []
  };
  
  // Check customer database
  customers.forEach(customer => {
    const similarity = calculateSimilarity(customerName, customer.name);
    if (similarity >= similarityThreshold) {
      result.matchedNames.push({
        name: customer.name,
        source: 'customer',
        confidence: similarity
      });
    }
  });
  
  // Check fleet data
  const uniqueFleetCustomers = new Set<string>();
  fleetData.forEach(item => {
    if (item.customer && !uniqueFleetCustomers.has(item.customer)) {
      uniqueFleetCustomers.add(item.customer);
      const similarity = calculateSimilarity(customerName, item.customer);
      if (similarity >= similarityThreshold) {
        result.matchedNames.push({
          name: item.customer,
          source: 'fleet',
          confidence: similarity
        });
      }
    }
  });
  
  // Check sales data
  const uniqueSalesCustomers = new Set<string>();
  salesData.forEach(item => {
    if (item.customerName && !uniqueSalesCustomers.has(item.customerName)) {
      uniqueSalesCustomers.add(item.customerName);
      const similarity = calculateSimilarity(customerName, item.customerName);
      if (similarity >= similarityThreshold) {
        result.matchedNames.push({
          name: item.customerName,
          source: 'sales',
          confidence: similarity
        });
      }
    }
  });
  
  // Sort matches by confidence (highest first)
  result.matchedNames.sort((a, b) => b.confidence - a.confidence);
  
  return result;
}

/**
 * Get all possible customer names from all data sources
 */
export function getAllPossibleCustomerNames(
  customers: Customer[],
  fleetData: FleetlistItem[],
  salesData: SalesRevenueItem[]
): string[] {
  const uniqueNames = new Set<string>();
  
  // Add names from customer database
  customers.forEach(customer => {
    if (customer.name) uniqueNames.add(customer.name);
  });
  
  // Add names from fleet data
  fleetData.forEach(item => {
    if (item.customer) uniqueNames.add(item.customer);
  });
  
  // Add names from sales data
  salesData.forEach(item => {
    if (item.customerName) uniqueNames.add(item.customerName);
  });
  
  return Array.from(uniqueNames);
}
