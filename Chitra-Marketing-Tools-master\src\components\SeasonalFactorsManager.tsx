import React, { useState, useEffect } from 'react';
import {
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  CloudRain,
  Truck,
  DollarSign,
  Users,
  History,
  Calendar,
  Briefcase,
  Wrench,
  AlertTriangle
} from 'lucide-react';
import {
  SeasonalFactor,
  SeasonalFactorType
} from '../types/seasonalMarketing';
import {
  getSeasonalFactors,
  saveSeasonalFactor,
  deleteSeasonalFactor
} from '../services/seasonalMarketingService';

interface SeasonalFactorsManagerProps {
  onFactorsChange?: () => void;
}

export default function SeasonalFactorsManager({ onFactorsChange }: SeasonalFactorsManagerProps) {
  // State for factors list and editing
  const [factors, setFactors] = useState<SeasonalFactor[]>([]);
  const [editingFactor, setEditingFactor] = useState<SeasonalFactor | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<SeasonalFactorType | 'all'>('all');

  // Load factors on component mount
  useEffect(() => {
    loadFactors();
  }, []);

  // Load factors from service
  const loadFactors = () => {
    setIsLoading(true);
    setError(null);

    try {
      const allFactors = getSeasonalFactors();
      setFactors(allFactors);
    } catch (err) {
      console.error('Error loading seasonal factors:', err);
      setError('Gagal memuat faktor musiman. Silakan coba lagi.');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter factors based on search query and type filter
  const filteredFactors = factors.filter(factor => {
    const matchesSearch =
      factor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      factor.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (factor.region && factor.region.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (factor.source && factor.source.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesType = filterType === 'all' || factor.type === filterType;

    return matchesSearch && matchesType;
  });

  // Handle adding a new factor
  const handleAddFactor = () => {
    const newFactor: SeasonalFactor = {
      id: '',
      type: SeasonalFactorType.WEATHER,
      name: '',
      description: '',
      impact: 0,
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString().split('T')[0],
      region: 'All',
      source: 'Manual Input'
    };

    setEditingFactor(newFactor);
    setIsAdding(true);
  };

  // Handle editing a factor
  const handleEditFactor = (factor: SeasonalFactor) => {
    setEditingFactor({ ...factor });
    setIsAdding(false);
  };

  // Handle deleting a factor
  const handleDeleteFactor = async (id: string) => {
    if (!window.confirm('Apakah Anda yakin ingin menghapus faktor ini?')) {
      return;
    }

    try {
      const success = deleteSeasonalFactor(id);

      if (success) {
        loadFactors();
        if (onFactorsChange) onFactorsChange();
      } else {
        setError('Gagal menghapus faktor.');
      }
    } catch (err) {
      console.error('Error deleting factor:', err);
      setError('Gagal menghapus faktor. Silakan coba lagi.');
    }
  };

  // Handle saving a factor
  const handleSaveFactor = async () => {
    if (!editingFactor) return;

    // Validate required fields
    if (!editingFactor.name || !editingFactor.description) {
      setError('Nama dan deskripsi faktor harus diisi.');
      return;
    }

    try {
      const savedFactor = saveSeasonalFactor(editingFactor);

      if (savedFactor) {
        setEditingFactor(null);
        loadFactors();
        if (onFactorsChange) onFactorsChange();
      } else {
        setError('Gagal menyimpan faktor.');
      }
    } catch (err) {
      console.error('Error saving factor:', err);
      setError('Gagal menyimpan faktor. Silakan coba lagi.');
    }
  };

  // Handle canceling edit/add
  const handleCancelEdit = () => {
    setEditingFactor(null);
    setError(null);
  };

  // Get icon for factor type
  const getFactorTypeIcon = (type: SeasonalFactorType) => {
    switch (type) {
      case SeasonalFactorType.WEATHER:
        return <CloudRain className="h-4 w-4" />;
      case SeasonalFactorType.MINING_OPERATIONS:
        return <Truck className="h-4 w-4" />;
      case SeasonalFactorType.ECONOMIC:
        return <DollarSign className="h-4 w-4" />;
      case SeasonalFactorType.COMPETITOR:
        return <Users className="h-4 w-4" />;
      case SeasonalFactorType.HISTORICAL:
        return <History className="h-4 w-4" />;
      case SeasonalFactorType.INDUSTRY_EVENT:
        return <Calendar className="h-4 w-4" />;
      case SeasonalFactorType.BUDGET_CYCLE:
        return <Briefcase className="h-4 w-4" />;
      case SeasonalFactorType.MAINTENANCE:
        return <Wrench className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  // Get name for factor type
  const getFactorTypeName = (type: SeasonalFactorType): string => {
    switch (type) {
      case SeasonalFactorType.WEATHER:
        return 'Cuaca';
      case SeasonalFactorType.MINING_OPERATIONS:
        return 'Operasi Pertambangan';
      case SeasonalFactorType.ECONOMIC:
        return 'Ekonomi';
      case SeasonalFactorType.COMPETITOR:
        return 'Kompetitor';
      case SeasonalFactorType.HISTORICAL:
        return 'Historis';
      case SeasonalFactorType.INDUSTRY_EVENT:
        return 'Event Industri';
      case SeasonalFactorType.BUDGET_CYCLE:
        return 'Siklus Anggaran';
      case SeasonalFactorType.MAINTENANCE:
        return 'Pemeliharaan';
      default:
        return 'Lainnya';
    }
  };

  // Render factor editor
  const renderFactorEditor = () => {
    if (!editingFactor) return null;

    return (
      <div className="bg-white p-4 rounded-lg border shadow-sm mb-4">
        <h3 className="text-lg font-medium mb-4">
          {isAdding ? 'Tambah Faktor Baru' : 'Edit Faktor'}
        </h3>

        {error && (
          <div className="mb-4 p-2 bg-red-50 text-red-700 rounded border border-red-200 flex items-center">
            <AlertTriangle className="h-4 w-4 mr-2" />
            <span>{error}</span>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nama Faktor
            </label>
            <input
              type="text"
              value={editingFactor.name}
              onChange={(e) => setEditingFactor({ ...editingFactor, name: e.target.value })}
              className="w-full p-2 border rounded"
              placeholder="Nama faktor"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tipe Faktor
            </label>
            <select
              value={editingFactor.type}
              onChange={(e) => setEditingFactor({ ...editingFactor, type: e.target.value as SeasonalFactorType })}
              className="w-full p-2 border rounded"
            >
              {Object.values(SeasonalFactorType).map(type => (
                <option key={type} value={type}>
                  {getFactorTypeName(type)}
                </option>
              ))}
            </select>
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Deskripsi
            </label>
            <textarea
              value={editingFactor.description}
              onChange={(e) => setEditingFactor({ ...editingFactor, description: e.target.value })}
              className="w-full p-2 border rounded resize-y"
              rows={3}
              placeholder="Deskripsi faktor"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Dampak (-10 hingga +10)
            </label>
            <input
              type="number"
              min="-10"
              max="10"
              value={editingFactor.impact}
              onChange={(e) => setEditingFactor({ ...editingFactor, impact: parseInt(e.target.value) })}
              className="w-full p-2 border rounded"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Wilayah
            </label>
            <input
              type="text"
              value={editingFactor.region || ''}
              onChange={(e) => setEditingFactor({ ...editingFactor, region: e.target.value })}
              className="w-full p-2 border rounded"
              placeholder="Wilayah (opsional)"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tanggal Mulai
            </label>
            <input
              type="date"
              value={editingFactor.startDate || ''}
              onChange={(e) => setEditingFactor({ ...editingFactor, startDate: e.target.value })}
              className="w-full p-2 border rounded"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tanggal Selesai
            </label>
            <input
              type="date"
              value={editingFactor.endDate || ''}
              onChange={(e) => setEditingFactor({ ...editingFactor, endDate: e.target.value })}
              className="w-full p-2 border rounded"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sumber Data
            </label>
            <input
              type="text"
              value={editingFactor.source || ''}
              onChange={(e) => setEditingFactor({ ...editingFactor, source: e.target.value })}
              className="w-full p-2 border rounded"
              placeholder="Sumber data (opsional)"
            />
          </div>
        </div>

        <div className="mt-4 flex justify-end space-x-2">
          <button
            onClick={handleCancelEdit}
            className="px-4 py-2 border rounded text-gray-700 hover:bg-gray-50 flex items-center"
          >
            <X className="h-4 w-4 mr-1" />
            <span>Batal</span>
          </button>

          <button
            onClick={handleSaveFactor}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
          >
            <Save className="h-4 w-4 mr-1" />
            <span>Simpan</span>
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Manajemen Faktor Musiman</h2>

        <button
          onClick={handleAddFactor}
          className="px-3 py-1.5 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus className="h-4 w-4 mr-1" />
          <span>Tambah Faktor</span>
        </button>
      </div>

      {/* Editor */}
      {editingFactor && renderFactorEditor()}

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-2">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Cari faktor..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full p-2 border rounded"
          />
        </div>

        <div>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as SeasonalFactorType | 'all')}
            className="w-full p-2 border rounded"
          >
            <option value="all">Semua Tipe</option>
            {Object.values(SeasonalFactorType).map(type => (
              <option key={type} value={type}>
                {getFactorTypeName(type)}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Error message */}
      {error && !editingFactor && (
        <div className="p-2 bg-red-50 text-red-700 rounded border border-red-200 flex items-center">
          <AlertTriangle className="h-4 w-4 mr-2" />
          <span>{error}</span>
        </div>
      )}

      {/* Loading state */}
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        /* Factors list */
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          {filteredFactors.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              {searchQuery || filterType !== 'all' ?
                'Tidak ada faktor yang sesuai dengan filter.' :
                'Belum ada faktor musiman. Klik "Tambah Faktor" untuk membuat faktor baru.'}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">
                      Nama
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                      Tipe
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/12">
                      Dampak
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                      Periode
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/6">
                      Wilayah
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-1/12">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredFactors.map(factor => (
                    <tr key={factor.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <div className="font-medium text-gray-900 break-words">{factor.name}</div>
                        <div className="text-xs text-gray-500 break-words">{factor.description}</div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex items-center">
                          <span className="mr-1">{getFactorTypeIcon(factor.type)}</span>
                          <span className="break-words">{getFactorTypeName(factor.type)}</span>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          factor.impact > 0 ? 'bg-green-100 text-green-800' :
                          factor.impact < 0 ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {factor.impact > 0 ? '+' : ''}{factor.impact}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {factor.startDate && factor.endDate ? (
                          <div className="break-words">
                            {new Date(factor.startDate).toLocaleDateString('id-ID', { day: 'numeric', month: 'short' })} - {new Date(factor.endDate).toLocaleDateString('id-ID', { day: 'numeric', month: 'short', year: 'numeric' })}
                          </div>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        <div className="break-words">
                          {factor.region || <span className="text-gray-400">-</span>}
                        </div>
                      </td>
                      <td className="px-4 py-3 text-right text-sm font-medium">
                        <button
                          onClick={() => handleEditFactor(factor)}
                          className="text-blue-600 hover:text-blue-900 mr-3"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteFactor(factor.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
