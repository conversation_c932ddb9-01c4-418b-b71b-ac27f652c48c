const express = require('express');
const cors = require('cors');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();

// Enable CORS for all routes
app.use(cors());

// Proxy middleware options
const options = {
  target: 'https://gohse.id/crm/api',
  changeOrigin: true,
  pathRewrite: {
    '^/api': '', // remove /api prefix when forwarding to target
  },
  onProxyReq: (proxyReq, req, res) => {
    // Add the authorization header to the proxy request
    proxyReq.setHeader('Authorization', 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyIjoiQWZpIiwibmFtZSI6ImFmaSIsIkFQSV9USU1FIjoxNzQ2NTM1MzcxfQ.nQtXFvJJgcv5SgId_1EtD7vW3Qf0WCwggTX7vw1tDq4');
    
    // Log the request
    console.log(`Proxying ${req.method} request to ${proxyReq.path}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    // Log the response
    console.log(`Received response with status ${proxyRes.statusCode} for ${req.method} request to ${req.path}`);
  },
  onError: (err, req, res) => {
    console.error('Proxy error:', err);
    res.status(500).json({ error: 'Proxy error', message: err.message });
  }
};

// Create the proxy middleware
const apiProxy = createProxyMiddleware(options);

// Use the proxy for all requests to /api
app.use('/api', apiProxy);

// Add a test endpoint
app.get('/test', (req, res) => {
  res.json({ message: 'Proxy server is working!' });
});

// Start the server
const PORT = process.env.PORT || 3001;
app.listen(PORT, () => {
  console.log(`Proxy server running on port ${PORT}`);
  console.log(`Access the Perfex CRM API through http://localhost:${PORT}/api`);
});
