import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Textarea } from '../components/ui/textarea';
import { useToast } from '../components/ui/use-toast';
import { Instagram, Upload, Download, Sparkles } from 'lucide-react';

export default function InstagramAnalysisNew() {
  const [instagramUsername, setInstagramUsername] = useState('');
  const [instagramData, setInstagramData] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const { toast } = useToast();

  // Handle Instagram connection
  const handleConnectInstagram = async () => {
    if (!instagramUsername.trim()) {
      toast({
        title: "Username <PERSON>an",
        description: "Silakan masukkan username Instagram Anda",
        variant: "destructive"
      });
      return;
    }

    setIsConnecting(true);

    try {
      // Generate dummy data for now
      const dummyData = {
        username: instagramUsername,
        followers: Math.floor(Math.random() * 5000) + 1000,
        following: Math.floor(Math.random() * 1000) + 200,
        posts: Array.from({ length: 30 }, (_, i) => ({
          id: `post_${i}`,
          type: ['image', 'carousel', 'video', 'reels'][Math.floor(Math.random() * 4)],
          caption: `This is a sample post #${i+1} for ${instagramUsername}`,
          likes: Math.floor(Math.random() * 500) + 50,
          comments: Math.floor(Math.random() * 50) + 5,
          date: new Date(Date.now() - i * 86400000).toISOString().split('T')[0]
        }))
      };

      setInstagramData(JSON.stringify(dummyData, null, 2));
      
      toast({
        title: "Koneksi Berhasil",
        description: `Berhasil mengambil data dari akun Instagram: ${instagramUsername}`,
      });
    } catch (error) {
      console.error('Error connecting to Instagram:', error);
      toast({
        title: "Koneksi Gagal",
        description: "Gagal mengambil data Instagram. Silakan coba lagi.",
        variant: "destructive"
      });
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle analysis
  const handleAnalyze = async () => {
    if (!instagramData.trim()) {
      toast({
        title: "Data Diperlukan",
        description: "Silakan masukkan data Instagram untuk dianalisis",
        variant: "destructive"
      });
      return;
    }

    setIsAnalyzing(true);

    try {
      // Generate dummy analysis result
      const dummyAnalysis = {
        ringkasan: {
          totalPostingan: 30,
          tingkatEngagement: 3.8,
          rataRataLikes: 156,
          rataRataKomentar: 12,
          jenisKontenTerbaik: ['Carousel', 'Reels'],
          skorKinerjaKeseluruhan: 7.2,
          analisisSingkat: "Akun Instagram menunjukkan kinerja yang cukup baik dengan tingkat engagement 3.8%. Konten jenis carousel dan reels menunjukkan performa terbaik."
        }
      };

      setAnalysisResult(dummyAnalysis);
      
      toast({
        title: "Analisis Selesai",
        description: "Data Instagram berhasil dianalisis",
      });
    } catch (error) {
      console.error('Error analyzing Instagram data:', error);
      toast({
        title: "Analisis Gagal",
        description: "Gagal menganalisis data Instagram. Silakan coba lagi.",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Analisis Instagram (New)</h1>
        <p className="text-gray-600">Analisis performa Instagram Anda dan dapatkan wawasan untuk meningkatkan strategi</p>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Input Data Instagram</CardTitle>
            <CardDescription>
              Tempel data Instagram Anda atau hubungkan akun untuk dianalisis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="instagram-data">Data Instagram</Label>
                <Textarea
                  id="instagram-data"
                  placeholder="Tempel data Instagram Anda di sini..."
                  className="min-h-[200px]"
                  value={instagramData}
                  onChange={(e) => setInstagramData(e.target.value)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="instagram-username">Username:</Label>
                    <Input
                      id="instagram-username"
                      placeholder="@namapengguna"
                      className="w-48"
                      value={instagramUsername}
                      onChange={(e) => setInstagramUsername(e.target.value)}
                    />
                    <Button
                      variant="outline"
                      onClick={handleConnectInstagram}
                      disabled={isConnecting || !instagramUsername.trim()}
                    >
                      <Instagram className="h-4 w-4 mr-2" />
                      {isConnecting ? 'Menghubungkan...' : 'Hubungkan Instagram'}
                    </Button>
                  </div>
                </div>
                <Button
                  onClick={handleAnalyze}
                  disabled={isAnalyzing || !instagramData.trim()}
                >
                  <Sparkles className="h-4 w-4 mr-2" />
                  {isAnalyzing ? 'Menganalisis...' : 'Analisis'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {analysisResult && (
          <Card>
            <CardHeader>
              <CardTitle>Hasil Analisis</CardTitle>
              <CardDescription>
                Ringkasan performa Instagram Anda
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm font-medium text-gray-500">Total Postingan</div>
                    <div className="text-2xl font-bold">{analysisResult.ringkasan.totalPostingan}</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm font-medium text-gray-500">Tingkat Engagement</div>
                    <div className="text-2xl font-bold">{analysisResult.ringkasan.tingkatEngagement}%</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm font-medium text-gray-500">Rata-rata Likes</div>
                    <div className="text-2xl font-bold">{analysisResult.ringkasan.rataRataLikes}</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-sm font-medium text-gray-500">Rata-rata Komentar</div>
                    <div className="text-2xl font-bold">{analysisResult.ringkasan.rataRataKomentar}</div>
                  </div>
                </div>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-sm font-medium text-blue-700 mb-2">Analisis Singkat</div>
                  <p className="text-gray-700">{analysisResult.ringkasan.analisisSingkat}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
