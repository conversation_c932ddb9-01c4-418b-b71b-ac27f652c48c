<template>
  <AppLayout :breadcrumbs="breadcrumbs">
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900 flex items-center">
          <Truck class="h-6 w-6 mr-2" />
          <Settings class="h-5 w-5 mr-2" />
          Fleet Tire Analyzer
        </h1>
        <div class="flex space-x-3">
          <button
            @click="refreshData"
            :disabled="isLoading"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <RefreshCw :class="['h-4 w-4 mr-2', { 'animate-spin': isLoading }]" />
            Refresh Data
          </button>
          <button
            @click="forceClearAndReload"
            class="inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <Trash2 class="h-4 w-4 mr-2" />
            Clear Cache
          </button>
        </div>
      </div>

      <!-- Summary Cards -->
      <div v-if="!isLoading && !error && fleetData.length > 0" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <Truck class="h-8 w-8 text-blue-600" />
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Vehicles</p>
              <p class="text-2xl font-semibold text-gray-900">{{ totalVehicles }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <Settings class="h-8 w-8 text-green-600" />
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total Tires</p>
              <p class="text-2xl font-semibold text-gray-900">{{ totalTires }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <BarChart2 class="h-8 w-8 text-purple-600" />
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Tire Sizes</p>
              <p class="text-2xl font-semibold text-gray-900">{{ uniqueTireSizes.length }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <Users class="h-8 w-8 text-orange-600" />
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Customers</p>
              <p class="text-2xl font-semibold text-gray-900">{{ uniqueCustomers.length }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Search and Filters -->
      <div class="bg-white rounded-lg shadow-sm border p-4">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1 relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search by customer, model, tire size..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
          </div>
          
          <select
            v-model="selectedTireSize"
            class="block w-full sm:w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Tire Sizes</option>
            <option v-for="size in uniqueTireSizes" :key="size" :value="size">
              {{ size }}
            </option>
          </select>
          
          <select
            v-model="selectedCustomer"
            class="block w-full sm:w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Customers</option>
            <option v-for="customer in uniqueCustomers" :key="customer" :value="customer">
              {{ customer }}
            </option>
          </select>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="text-center">
          <Loader2 class="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p class="text-gray-600">Loading fleet tire data...</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
          <AlertTriangle class="h-5 w-5 text-red-400" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error Loading Data</h3>
            <p class="mt-1 text-sm text-red-700">{{ error }}</p>
          </div>
        </div>
      </div>

      <!-- Data Table -->
      <div v-if="!isLoading && !error && fleetData.length > 0" class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Fleet Tire Analysis</h3>
          
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tire Size</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tire Qty</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Qty</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Tires</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="item in paginatedData" :key="item.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {{ item.customer || 'N/A' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ item.model || 'N/A' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ item.location || 'N/A' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {{ item.tire_size || 'N/A' }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ item.tire_quantity || 'N/A' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ item.unit_qty || 'N/A' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {{ item.total_tire || 'N/A' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="getStatusClass(item.status)">
                      {{ item.status || 'Active' }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div v-if="totalPages > 1" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
              <button
                @click="previousPage"
                :disabled="currentPage === 1"
                class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                @click="nextPage"
                :disabled="currentPage === totalPages"
                class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Showing <span class="font-medium">{{ startIndex + 1 }}</span> to <span class="font-medium">{{ endIndex }}</span> of <span class="font-medium">{{ filteredData.length }}</span> results
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    @click="previousPage"
                    :disabled="currentPage === 1"
                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <ChevronLeft class="h-5 w-5" />
                  </button>
                  
                  <button
                    v-for="page in visiblePages"
                    :key="page"
                    @click="currentPage = page"
                    :class="[
                      'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                      page === currentPage
                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    ]"
                  >
                    {{ page }}
                  </button>
                  
                  <button
                    @click="nextPage"
                    :disabled="currentPage === totalPages"
                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    <ChevronRight class="h-5 w-5" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!isLoading && !error && fleetData.length === 0" class="text-center py-12">
        <Truck class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">No fleet data found</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by loading fleet data from the API.</p>
        <div class="mt-6">
          <button
            @click="refreshData"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <RefreshCw class="h-4 w-4 mr-2" />
            Load Data
          </button>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  Truck, Search, ChevronLeft, ChevronRight, RefreshCw,
  AlertTriangle, Loader2, Users, BarChart2, Settings,
  Trash2
} from 'lucide-vue-next'
import AppLayout from '../../layouts/AppLayout.vue'
import { clearAllCache } from '../../utils/clearCache.js'

// Types
interface FleetData {
  id: string
  customer?: string
  model?: string
  location?: string
  tire_size?: string
  tire_quantity?: string | number
  unit_qty?: string | number
  total_tire?: string | number
  status?: string
  [key: string]: any
}

// Reactive state
const fleetData = ref<FleetData[]>([])
const isLoading = ref(false)
const error = ref<string | null>(null)
const searchQuery = ref('')
const selectedTireSize = ref('')
const selectedCustomer = ref('')
const currentPage = ref(1)
const itemsPerPage = 10

// Breadcrumbs
const breadcrumbs = [
  { name: 'Analytics', href: '#' },
  { name: 'Fleet Tire Analyzer', href: '/fleet-tire-analyzer' }
]

// Computed properties
const filteredData = computed(() => {
  let filtered = fleetData.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item =>
      (item.customer?.toLowerCase().includes(query)) ||
      (item.model?.toLowerCase().includes(query)) ||
      (item.tire_size?.toLowerCase().includes(query)) ||
      (item.location?.toLowerCase().includes(query))
    )
  }

  if (selectedTireSize.value) {
    filtered = filtered.filter(item => item.tire_size === selectedTireSize.value)
  }

  if (selectedCustomer.value) {
    filtered = filtered.filter(item => item.customer === selectedCustomer.value)
  }

  return filtered
})

const totalVehicles = computed(() => fleetData.value.length)

const totalTires = computed(() => {
  return fleetData.value.reduce((sum, item) => {
    const tireCount = parseInt(String(item.total_tire || 0))
    return sum + (isNaN(tireCount) ? 0 : tireCount)
  }, 0)
})

const uniqueTireSizes = computed(() => {
  const sizes = fleetData.value
    .map(item => item.tire_size)
    .filter(size => size && size.trim() !== '')
  return [...new Set(sizes)].sort()
})

const uniqueCustomers = computed(() => {
  const customers = fleetData.value
    .map(item => item.customer)
    .filter(customer => customer && customer.trim() !== '')
  return [...new Set(customers)].sort()
})

const totalPages = computed(() => Math.ceil(filteredData.value.length / itemsPerPage))

const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage)
const endIndex = computed(() => Math.min(startIndex.value + itemsPerPage, filteredData.value.length))

const paginatedData = computed(() => {
  return filteredData.value.slice(startIndex.value, endIndex.value)
})

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, start + 4)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// Methods
const getStatusClass = (status: string | undefined) => {
  const statusLower = (status || 'active').toLowerCase()

  const classes = {
    'active': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800',
    'inactive': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800',
    'maintenance': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800',
    'pending': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'
  }

  return classes[statusLower] || classes['active']
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const forceClearAndReload = async () => {
  try {
    await clearAllCache()
    fleetData.value = []
    currentPage.value = 1
    searchQuery.value = ''
    selectedTireSize.value = ''
    selectedCustomer.value = ''
    await refreshData()
  } catch (err) {
    console.error('Error clearing cache:', err)
    error.value = 'Failed to clear cache'
  }
}

const refreshData = async () => {
  isLoading.value = true
  error.value = null

  try {
    console.log('🌐 Fetching fleet tire data from API...')
    const response = await fetch('https://chitraparatama.co.id/ICS/product/get_api.php?function=fleetlist', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const rawData = await response.json()
    console.log('📥 Raw API Response:', rawData)

    // Handle different response formats
    let dataArray: any[] = []

    if (Array.isArray(rawData)) {
      dataArray = rawData
    } else if (rawData && Array.isArray(rawData.data)) {
      dataArray = rawData.data
    } else if (rawData && rawData.success && Array.isArray(rawData.data)) {
      dataArray = rawData.data
    } else {
      throw new Error('Invalid data format received from API')
    }

    // Process and validate the data with tire-specific focus
    const processedData = dataArray.map((item: any, index: number) => ({
      id: item.id || item.id_fleet_list || `fleet-tire-${index + 1}`,
      customer: item.customer || item.Customer || item.nama_customer || '',
      model: item.model || item.Model || item.model_unit || item.unit_model || '',
      location: item.location || item.Location || item.lokasi || item.site || '',
      tire_size: item.tire_size || item.TireSize || item['Tire Size'] || item.ukuran_ban || '',
      tire_quantity: item.tire_quantity || item.TireQuantity || item['Tire Quantity'] || item.qty_ban || 0,
      unit_qty: item.unit_qty || item.UnitQty || item['Unit Qty'] || item.qty_unit || 0,
      total_tire: item.total_tire || item.TotalTire || item['Total Tire'] || item.total_ban || 0,
      status: item.status || item.Status || 'Active'
    }))

    fleetData.value = processedData
    console.log('✅ Fleet tire data loaded successfully:', processedData.length, 'records')

  } catch (err) {
    console.error('❌ Error fetching fleet tire data:', err)
    error.value = err instanceof Error ? err.message : 'Failed to load fleet tire data'
    fleetData.value = []
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  refreshData()
})
</script>
